// Copyright(C) 2022 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2022-06-22, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package main

import (
	"context"
	"encoding/json"
	"flag"
	"log"

	"icode.baidu.com/baidu/gdp/codec"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/net/ral"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/bootstrap"
)

var config *bootstrap.Config

var appConfig = flag.String("conf", "./conf/app.toml", "app config file")

func main() {
	flag.Parse()

	config = bootstrap.MustLoadAppConfig(*appConfig)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	bootstrap.MustInit(ctx)

	// 执行退出前的回调，如日志关闭落盘、其他资源清理工作
	defer bootstrap.InvokeBeforeShutdown()

	myTask()
}

type sumResult struct {
	ErrNo int
	Msg   string
	Data  struct {
		Sum int
	}
}

func (s *sumResult) String() string {
	bf, _ := json.Marshal(s)
	return string(bf)
}

func myTask() {
	req := &ghttp.RalRequest{
		Method: "GET",
		Path:   "/cal/sum?ids=123,456",
	}
	ret := &sumResult{
		ErrNo: -1,
	}

	resp := &ghttp.RalResponse{
		Data:    ret,
		Decoder: codec.JSONDecoder,
	}

	if err := ral.RAL(context.Background(), "gdp_site", req, resp); err != nil {
		log.Println("ral.err=", err)
	}
	log.Println("response:", ret)
}
