// Copyright(C) 2022 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2022-03-07, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package main

// 演示： 往当前 app 的 nshead server 发送请求

import (
	"flag"
	"fmt"
	"log"
	"net"
	"time"

	"icode.baidu.com/baidu/gdp/nshead"
)

var addr = flag.String("addr", "127.0.0.1:8081", "server addr")

func main() {
	flag.Parse()

	conn, err := net.DialTimeout("tcp", *addr, 1*time.Second)
	if err != nil {
		log.Fatalf("dial failed: %v", err)
	}
	defer conn.Close()

	rw := nshead.NewReadWriter(conn)

	for i := 0; i < 20; i++ {
		// send request
		msg := fmt.Sprintf("hello %d", i)
		_, e := rw.Write([]byte(msg))
		log.Println("send request:", i, msg, e)
		if e != nil {
			log.Fatalf("send failed: %v", e)
		}

		// read response
		_, resp, e2 := rw.ReadHeadBody()
		if e2 != nil {
			log.Fatalf("read resp failed: %v", e2)
		}

		log.Println("receive response: ", string(resp))
		fmt.Println()
	}
}
