#!/bin/bash

# supervise control

# AbsPath: absolute path of control script
# AppRoot: absolute path of app root
# AppName: same as the dir name
AbsPath=$(cd $(dirname "$BASH_SOURCE") && pwd)
AppRoot=$(dirname "$AbsPath")
AppName="go-client-forum"

# app
App="${AppRoot}/bin/${AppName}"
AppConf="${AppRoot}/conf/app.toml"
PortConf="${AppRoot}/conf/port.conf"

# start
# if app is running, nothing to do.
function ControlStart() {
    ${App} 1>/dev/null 2>/tmp/go-client-forum.err.log
    return 0
}

# stop
# /bin/ps only list matrix container process
function ControlStop() {
    /bin/ps -ef | grep "${App}" | grep -v grep | awk '{print $2}' | xargs kill -9 &>/dev/null
    sleep 0.5
}

# restart
function ControlRestart() {
    ControlStop
    ControlStart
}

# usage
function Usage() {
    echo "Usage: $(basename "$0") [start|stop|restart]"
}

# init
function Init() {
    if [[ ! -x ${App} ]]; then
        echo "app cmd not exist: ${App}"
        return 1
    fi

    if [[ ! -f "${PortConf}" ]]; then
        echo "port conf not exist: ${PortConf}"
        return 1
    fi
 
    # replace port template value
    port=$(grep "LISTEN_PORT" "${PortConf}" | awk '{print $NF}')
    sed -i "s/\$LISTEN_PORT/${port}/g" "$AppConf"

	if [[ -d "${AppRoot}/so" ]]; then
        echo "export LD_LIBRARY_PATH=${AppRoot}/so"
        export LD_LIBRARY_PATH=${AppRoot}/so:$LD_LIBRARY_PATH
    fi
    
    return 0
}

function main() {
    Init || exit 1

    action=$1
    case "X${action}" in
        Xstart)
            ControlStart || ( echo 'retry...' && ControlRestart )
            ;;
        Xstop)
            ControlStop
            ;;
        Xrestart)
            ControlRestart
            ;;
        *)
            Usage
            ;;
    esac
}

if [ $# != 1 ] ; then
    Usage
    exit 1
fi
 
cd "${AppRoot}" || exit 1
main "$@"
