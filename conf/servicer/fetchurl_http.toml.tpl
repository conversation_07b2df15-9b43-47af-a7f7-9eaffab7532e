# Service的名字，必选，需自定义修改
Name = "fetchurl_http"

# http://man.baidu.com/inf/orp/#%E6%9C%8D%E5%8A%A1%E4%BB%8B%E7%BB%8D_fetchurl%E6%9C%8D%E5%8A%A1

# 以下自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用了Manual，则ConnTimeOut、WriteTimeOut、ReadTimeOut、Retry 需要配置
# 使用其他方式，如BNS，上述则ConnTimeOut等参数默认会从 BNS config 中读取，
# 若配置了则会导致 BNS config 中的值不生效

# 可选，连接超时，默认5000
ConnTimeOut = 200
# 可选，写数据超时，默认5000
WriteTimeOut = 500
# 可选，读数据超时，默认5000
ReadTimeOut = 500
# 可选，请求失败后的重试次数：总请求次数 = Retry + 1,默认0
Retry = 1

# 由于 目前 BNS：group.fetchurl.orp.cn 没有配置 config ，所以在此单独配置上述超时时间

# 可选，Resource是否配置的代理服务器的地址
[Proxy]
# Protocol: 代理服务的协议类型，可选值：HTTP
Protocol = "HTTP"

[Strategy]
# 资源使用策略，非必选，默认使用 RoundRobin
# RoundRobin-依次轮询 Random-随机 LocalityAware-la加权轮询，需要策略配置，
Name = "RoundRobin"

# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
# 使用 BNS 来配置一个 proxy 集群
{{ if ne .BNSName "" }}
[Resource.BNS]
BNSName = "{{.BNSName}}"
#PortKey = "main"
{{ else if .ip }}
[Resource.Manual]
[[Resource.Manual.jx]]
Host = "{{.ip}}"
Port = {{.port}}
{{end}}