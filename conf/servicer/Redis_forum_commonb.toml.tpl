Name = "Redis_forum_commonb"
# 连接超时
ConnTimeOut = 50
# 写数据超时
WriteTimeOut = 200
# 读数据超时
ReadTimeOut = 200
# 累计重试次数
Retry = 0
# 是否使用连接池
Reuse = true

[Strategy]
# 资源使用策略，非必选，默认使用 RoundRobin
#RoundRobin-依次轮询
#Random-随机
#LocalityAware-la加权轮询，需要策略配置，
Name="RoundRobin"

Protocol = "nshead" # http / nshead

# 数据体格式
Converter = "mapack2"  # form / mcpack1 / mapack2 / string


{{ if ne .BNSName "" }}
[Resource.BNS]
BNSName = "{{.BNSName}}"
{{ else if .ip }}
[Resource.Manual]
[[Resource.Manual.jx]]
Host = "{{.ip}}"
Port = {{.port}}
{{end}}
