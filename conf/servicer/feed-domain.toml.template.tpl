# Service的名字，必选
Name = "feed-domain"

# 配置说明： http://gdp.baidu-int.com/gdp2/docs/examples/client/30_servicer/

# 使用 BNS 类型的时候，不需要配置 ConnTimeOut(读超时) 等内容
# 配置内容会从 BNS 读取到，配置的内容每 5 秒会从 BNS 系统查询并更新
# 若在当前文件也配置了 ConnTimeOut=100，会覆盖掉 BNS 配置的值

ReadTimeOut=1500

# 数据头协议
Protocol = "http" # http / nshead

# 数据体格式
Converter = "json"  # form / mcpack1 / mapack2 / string

[Strategy]
# 资源使用策略，非必选，默认使用 RoundRobin
#RoundRobin-依次轮询
#Random-随机
#LocalityAware-la加权轮询，需要策略配置，
Name="RoundRobin"

{{ if ne .BNSName "" }}
[Resource.BNS]
BNSName = "{{.BNSName}}"
{{ else if .ip }}
[Resource.Manual]
[[Resource.Manual.jx]]
Host = "{{.ip}}"
Port = {{.port}}
{{end}}


