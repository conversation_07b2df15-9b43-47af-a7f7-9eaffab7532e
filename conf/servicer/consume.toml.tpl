Name = "consume"


# 数据头协议
Protocol = "http" # http / nshead

# 数据体格式
Converter = "json"  # form / mcpack1 / mapack2 / string

[Strategy]
# 资源使用策略，非必选，默认使用 RoundRobin
#RoundRobin-依次轮询
#Random-随机
#LocalityAware-la加权轮询，需要策略配置，
Name="RoundRobin"


{{ if ne .BNSName "" }}
[Resource.BNS]
BNSName = "{{.BNSName}}"
{{ else if .ip }}
[Resource.Manual]
[[Resource.Manual.jx]]
Host = "{{.ip}}"
Port = {{.port}}
{{end}}
