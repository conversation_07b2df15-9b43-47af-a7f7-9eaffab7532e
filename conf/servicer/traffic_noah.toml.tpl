Name = "traffic_noah"
# 连接超时
ConnTimeOut = 5000
# 写数据超时
WriteTimeOut = 5000
# 读数据超时
ReadTimeOut = 5000
# 请求失败后的重试次数：总请求次数 = Retry + 1
Retry = 1

[Strategy]
# 资源使用策略
# random: 纯随机
# roundrobin: 依次轮询（未实现）
# weight-random: 带权重随机（未实现）
# hash: 使用hashid按范围访问（未实现）
Name = "Random" # random / roundrobin / weight-random / hash

{{ if ne .BNSName "" }}
[Resource.BNS]
BNSName = "{{.BNSName}}"
{{ else if .ip }}
[Resource.Manual]
[[Resource.Manual.default]]
Host = "{{.ip}}"
Port = {{.port}}
{{end}}
