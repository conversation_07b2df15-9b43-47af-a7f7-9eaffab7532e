package baidu.tieba;

import "google/protobuf/descriptor.proto";
extend google.protobuf.FieldOptions {
    optional string autosetter = 170010;
}
// 语言种类枚举，需要持续补充
enum LanguageType {
    ZN_CH = 0;   // 中文
    THAI = 1;    // 泰文
    // 以下补充标准列表
}

// 日志字符编码枚举
enum CodeType {
    CODE_TYPE_UNKNOWN = 0;   // 未知
    UTF8 = 1;      // utf8，建议选用此编码打印
    GBK = 2;       // gbk
    GB18030 = 3;   // gb18030
}

// 标识用户身份的id结构
message CookieUserid {
    optional string baiduid = 1;   // cookie中的baiduid字段或url中对应的参数
    optional string hao123id = 2;  // hao123业务特有的hao123id
    optional string cproid = 3;    // cpro业务特有的cproid
    optional string nmid = 4;      // 糯米id
}

// 标识用户设别的id结构
message DeviceID {
    optional string imei = 1;      // 移动设别的imei码
    optional string meid = 2;      // CDMA设备的身份设备码，相当于imei
    optional string mac_addr = 3;  // 设别的mac地址，适用的日志必打
    optional string cuid = 4;      // 百度统一的移动设备端应用的唯一标识信息，适用的日志必打
    optional string pccode = 5;    // windows的唯一设备标识码
    optional string open_udid = 6; // apple设备的唯一设备标识
    optional string udid = 7;      // apple设备的唯一设备标识，已禁用
    optional string idfa = 8;      // ios设备的广告id, 被关闭后无意义
}

message PassportID {
    // 为了下游QE的Java程序的兼容性，将字段类型从uint32改为int64
    optional int64 userid = 1[(autosetter) = "to_int64(odp::extract_param(#uid))"];
    optional string username = 2[(autosetter) = "odp::extract_param(#un)"];
}

message MobileLocation {
    optional int32 gps_x = 1;  // x轴坐标
    optional int32 gps_y = 2;  // y轴坐标
    optional int32 gps_r = 3;  // 精度范围
}

message MercatorLocation {
    optional int32 mer_x = 1;  // x轴坐标
    optional int32 mer_y = 2;  // y轴坐标
}

message HttpServiceInfo {
    optional string user_agent = 1;    // 客户端和useragent
    optional string request_url = 2;   // 请求的完整URL
    optional string http_method = 3;   // http方法，GET/POST
    optional string http_status = 4;   // http返回状态码
    optional string cookie = 5;        // 请求完整的cookie串
    optional string send_bytes = 6;    // 发送的数据量
    optional string response_time = 7; // 处理时间，单位为毫秒
    optional string http_version = 8;  // http版本
}

enum OSType {
    OS_TYPE_UNKNWON       = 0;    // 未知
    IOS           = 1;    // ios
    ANDROID       = 2;     // 安卓
    WINDOWSPHONE  = 3;    // windows
}

message TerminalInfo {
    optional OSType os = 1[default = OS_TYPE_UNKNWON]; // 设备操作系统
    optional string os_version = 2;             // 设备操作系统版本
    optional string manufacturer = 3;          // 终端设备制造商，如苹果、三星
    optional string terminal_type = 4;         // 终端机型，如I9300，iPhone5s
    optional int32 resolution_h = 5;          // 终端屏幕分辨率(高)
    optional int32 resolution_v = 6;         // 终端屏幕分辨率(宽)
    optional int32 ppi = 7;                   // 设备分辨率ppi
}

message AppInfo {
    optional string app_name    = 1;  // app名称，此字段适用于浏览器名称
    optional string app_version = 2;  // app版本
    optional string app_channel = 3;  // app渠道
}

message NetInfo {
    optional string net_type = 1;    // 联网方式，如wifi，3G
    optional string net_apn = 2;     // 接入点，如cmnet，3gnet
}

message ViewInfo {
    optional string page_url = 1;         // 页面url
    optional string referer = 2;          // 来源url
    optional int32 display_num = 3;      // 展现结果数量
    optional string page_title = 4;       // 页面标题
    optional int32 page_num = 5;         // 页码
    optional bool is_pv = 6;              // 说明是否为pv，true为计算为pv
}

enum ClientType {
    CLIENT_TYPE_UNKNOWN = 0;       // 未知端
    PC_WEB = 1;        // pc的web浏览器访问
    WAP_COMMON = 2;    // 非智能机的wap浏览器访问
    WAP_SMART = 3;     // 智能机的wap浏览器访问
    PAD_WEB = 4;       // pad上的web访问
    PAD_APP = 5;       // pad上的app访问
    PC_CLIENT = 6;     // pc上的应用程序访问
    MOBILE_APP = 7;    // 移动端的应用程序访问
    SDK = 8;           // 通过sdk方式提供
    API = 9;           // 通过api方式提供
}

message Common {

//--------------------以下描述日志基础信息--------------------

    // 日志所属产品线名称，此名称不会自动生成
    optional string product_name = 1[(autosetter) = "odp::extract_param(#pro)"];

    // 日志名称，此名称不会自动生成
    optional string module_name = 2[(autosetter) = "odp::extract_param(#mid)"];

    // 标识用户request的唯一id。logid主要用处将处理逻辑能够串联起来。我们强烈建议one action one logid。如果上游传入logid，本地将不要再重复生成，而是将上游的logid设置本字段中。
    required string logid = 3[(autosetter) = "odp::extract_param(#logid)"];

    // 此日志中使用的语言，默认为中文，不需要每行都打印，修改default来实现设置。
    optional LanguageType language = 4[default = ZN_CH];

    // 日志的字符编码，默认为未知，不需要每行都打印，修改default来实现设置。
    optional CodeType code_type = 5[default = CODE_TYPE_UNKNOWN];

//--------------------以下描述用户身份标识信息--------------------

    // 标识用户身份的id，为一个结构，选择可以填写的部分添加。
    optional CookieUserid cookie_userid = 6;

    // 标识用户设备的id，为一个结构，选择可以填写部分添加
    optional DeviceID device_id = 7;

    // 用户passport session交互返回的userid和username
    optional PassportID passport_id = 8;

    // 用户的电话号码
    optional string phone_num = 9;

//--------------------以下描述日志时间信息--------------------

    // 服务器端记录的日志时间戳，单位为毫秒，此字段必打
    required int64 timestamp = 10[(autosetter) = "timestamp()"];

    // 服务器端记录的日志时区，默认为东八区，赋值为8；如为西部时区，如西三区赋值为-3。
    optional int32 timezone = 11;

    // 用户端记录的日志时间戳，单位为毫秒
    optional int64 user_timestamp = 12;

    // 用户端记录的日志时区，默认为东八区，赋值为8；如为西部时区，如西三区赋值为-3。
    optional int32 user_timezone = 13;

//--------------------以下描述位置相关信息--------------------

    // 用户的IP信息，标准为ip v4，格式为无符号整数而不是点分十进制。转换方式使用小端法。
    optional fixed32 ip = 14;

    // 用户的IP信息，标准为ip v6。
    optional string ipv6 = 15;

    // 网络提供商信息，这里记录的是直接取到的值，而不是解析出的值
    optional string net_provider = 16;

    // 经纬度坐标下的移动坐标。为一个结构，包含x,y两个轴的信息以及精度
    optional MobileLocation mobile_location = 17;

    // 墨卡托坐标系下的移动坐标。
    optional MercatorLocation mercator_location = 18;

//--------------------以下描述不同应用形态关联的信息--------------------

    // httpservice打印的信息，如果不是httpservice类型的日志，不需要打印。
    optional HttpServiceInfo http_service_info = 19;

    // 设备的终端信息
    optional TerminalInfo terminal_info = 20;

    // 应用软件信息
    optional AppInfo app_info = 21;

    // 网络运营商信息
    optional NetInfo net_info = 22;

//--------------------以下字段为推荐使用，如果有类似含义，请使用相同字段名称--------------------

    // 动作名称，由产品线自定义
    optional string action_name = 23;

    // 流量渠道，遵守约定的名称，继续使用tn
    optional string tn  = 24;

    // 检索词信息，适用于检索类行为
    optional string query = 25;

    // 页面浏览信息
    optional ViewInfo view_info = 26;

    // 用户的访问端信息，用以区分用户的访问模式，适用于记录来自多个源的信息的日志。
    optional ClientType client_type = 27[default=CLIENT_TYPE_UNKNOWN];

}

enum LogCategory {
    EXPOSURE = 1;
    CLICK = 2;
    DURATION=3;
};

// 主message名称规则是：日志名的驼峰格式。例如：default_message（日志名） DefaultMessage（主message名字）。
message TiebaRecomUi {

    //----------------------------------------------------------------------------------------------------------------
    //  以下字段用于描述系统级传输信息，用户不需要关心。默认保留。
    //----------------------------------------------------------------------------------------------------------------

    //  传输配置字段，默认需要保留
    optional MetaData metadata                     = 25;
    optional string   log_tag                      = 26;


    // 统一公有字段
    required Common common = 41;


    //----------------------------------------------------------------------------------------------------------------
    //  以上id小于128的字段为预留字段，不能添加。用户自有字段id从129开始。
    //----------------------------------------------------------------------------------------------------------------
    required string unique_log_key = 129[(autosetter) = "odp::extract_param(#unique_log_key)"]; //单次请求唯一
    optional int64 unified_timestamp = 130[(autosetter) = "to_int64(odp::extract_param(#unified_timestamp))"]; // 可以打通所有拼接日志的时间戳,秒级，
    optional LogCategory log_type = 131[default=EXPOSURE];//已废弃，不再使用
    required int64 log_timestamp = 132[(autosetter) = "to_int64(odp::extract_param(#log_timestamp))"]; //日志时间，曝光时间或点击时间,秒级
    optional int64 tid = 133[(autosetter) = "to_int64(odp::extract_param(#tid))"]; //同ThreadInfo中的tid对应，可以唯一确定一个帖子对应的feature
    required bytes client_log = 134[(autosetter) = "odp::extract_param(#client_log)"]; //端上日志
    optional string new_log_type = 135[(autosetter) = "odp::extract_param(#new_log_type)"];//日志类型
}


// MetaData信息在提交pb配置后会自动补全minos所需信息
// 用户请不要进行改动，系统会在提交后自动修改

// MetaData信息请不要进行修改，保留原始状态
message MetaData {
    optional int32 log_module_id = 1 [default = 6943];
    optional string log_name = 2 [default = "tieba_recom_ui"];
    optional string product_name = 3 [default = "tieba"];
}