package baidu.tieba;

import "google/protobuf/descriptor.proto";
extend google.protobuf.FieldOptions {
            optional string autosetter = 170010;
}

// 语言种类枚举，需要持续补充
enum LanguageType {
    ZN_CH = 0;   // 中文
    THAI = 1;    // 泰文
    // 以下补充标准列表
}

// 日志字符编码枚举
enum CodeType {
    CODE_TYPE_UNKNOWN = 0;   // 未知
    UTF8 = 1;      // utf8，建议选用此编码打印
    GBK = 2;       // gbk
    GB18030 = 3;   // gb18030
}

// 标识用户身份的id结构
message CookieUserid {
    optional string baiduid = 1;   // cookie中的baiduid字段或url中对应的参数
    optional string hao123id = 2;  // hao123业务特有的hao123id
    optional string cproid = 3;    // cpro业务特有的cproid
    optional string nmid = 4;      // 糯米id
}

// 标识用户设别的id结构
message DeviceID {
    optional string imei = 1[(autosetter) = " odp::extract_param(#_phone_imei)"];      // 移动设别的imei码
    optional string meid = 2;      // CDMA设备的身份设备码，相当于imei
    optional string mac_addr = 3;  // 设别的mac地址，适用的日志必打
    optional string cuid = 4[(autosetter) = " odp::extract_param(#cuid)"];      // 百度统一的移动设备端应用的唯一标识信息，适用的日志必打
    optional string pccode = 5;    // windows的唯一设备标识码
    optional string open_udid = 6; // apple设备的唯一设备标识
    optional string udid = 7;      // apple设备的唯一设备标识，已禁用
    optional string idfa = 8[(autosetter) = " odp::extract_param(#idfa)"];      // ios设备的广告id, 被关闭后无意义
}

message PassportID {
    optional uint32 userid = 1;
    optional string username = 2[(autosetter) = " odp::extract_param(#un)"];
}

message MobileLocation {
    optional int32 gps_x = 1;  // x轴坐标
    optional int32 gps_y = 2;  // y轴坐标
    optional int32 gps_r = 3;  // 精度范围
}

message MercatorLocation {
    optional int32 mer_x = 1;  // x轴坐标
    optional int32 mer_y = 2;  // y轴坐标
}

message HttpServiceInfo {
    optional string user_agent = 1[(autosetter) = " odp::extract_param(#agent)"];    // 客户端和useragent
    optional string request_url = 2;   // 请求的完整URL
    optional string http_method = 3;   // http方法，GET/POST
    optional string http_status = 4;   // http返回状态码
    optional string cookie = 5;        // 请求完整的cookie串
    optional string send_bytes = 6;    // 发送的数据量
    optional string response_time = 7; // 处理时间，单位为毫秒
    optional string http_version = 8;  // http版本
}

enum OSType {
    OS_TYPE_UNKNWON       = 0;    // 未知
    IOS           = 1;    // ios
    ANDROID       = 2;     // 安卓
    WINDOWSPHONE  = 3;    // windows
}

message TerminalInfo {
    optional OSType os = 1; // 设备操作系统
    optional string os_version = 2;             // 设备操作系统版本
    optional string manufacturer = 3[(autosetter) = " odp::extract_param(#brand)"];          // 终端设备制造商，如苹果、三星
    optional string terminal_type = 4[(autosetter) = " odp::extract_param(#brand_type)"];         // 终端机型，如I9300，iPhone5s
    optional int32 resolution_h = 5[(autosetter) = "to_int32( odp::extract_param(#height))"];          // 终端屏幕分辨率(高)
    optional int32 resolution_v = 6[(autosetter) = "to_int32( odp::extract_param(#width))"];         // 终端屏幕分辨率(宽)
    optional int32 ppi = 7[(autosetter) = "to_int32( odp::extract_param(#ppi))"];  // 设备分辨率ppi
}

message AppInfo {
    optional string app_name    = 1;  // app名称，此字段适用于浏览器名称
    optional string app_version = 2[(autosetter) = " odp::extract_param(#_client_version)"];  // app版本
    optional string app_channel = 3[(autosetter) = " odp::extract_param(#from)"];  // app渠道
}

message NetInfo {
    optional string net_type = 1[(autosetter) = " odp::extract_param(#net_type)"];    // 联网方式，如wifi，3G
    optional string net_apn = 2[(autosetter) = " odp::extract_param(#net_apn)"];     // 接入点，如cmnet，3gnet。
}

message ViewInfo {
    optional string page_url = 1[(autosetter) = " odp::extract_param(#url)"];         // 页面url
    optional string referer = 2[(autosetter) = " odp::extract_param(#refer)"];          // 来源url
    optional int32 display_num = 3;      // 展现结果数量
    optional string page_title = 4;       // 页面标题
    optional int32 page_num = 5[(autosetter) = "to_int32(odp::extract_param(#pn))"];  ;         // 页码
    optional bool is_pv = 6;              // 说明是否为pv，true为计算为pv
}

enum ClientType {
    CLIENT_TYPE_UNKNOWN = 0;       // 未知端
    PC_WEB = 1;        // pc的web浏览器访问
    WAP_COMMON = 2;    // 非智能机的wap浏览器访问
    WAP_SMART = 3;     // 智能机的wap浏览器访问
    PAD_WEB = 4;       // pad上的web访问
    PAD_APP = 5;       // pad上的app访问
    PC_CLIENT = 6;     // pc上的应用程序访问
    MOBILE_APP = 7;    // 移动端的应用程序访问
    SDK = 8;           // 通过sdk方式提供
    API = 9;           // 通过api方式提供
}

message Common {

//--------------------以下描述日志基础信息--------------------

    // 日志所属产品线名称，此名称不会自动生成
    optional string product_name = 1[(autosetter) = " odp::extract_param(#pro)"];

    // 日志名称，此名称不会自动生成
    optional string module_name = 2[(autosetter) = " odp::extract_param(#mid)"];

    // 标识用户request的唯一id。logid主要用处将处理逻辑能够串联起来。我们强烈建议one action one logid。如果上游传入logid，本地将不要再重复生成，而是将上游的logid设置本字段中。
    required string logid = 3[(autosetter) = " odp::extract_param(#logid)"];

    // 此日志中使用的语言，默认为中文，不需要每行都打印，修改default来实现设置。
    optional LanguageType language = 4[default = ZN_CH];

    // 日志的字符编码，默认为未知，不需要每行都打印，修改default来实现设置。
    optional CodeType code_type = 5[default = UTF8];

//--------------------以下描述用户身份标识信息--------------------

    // 标识用户身份的id，为一个结构，选择可以填写的部分添加。
    optional CookieUserid cookie_userid = 6;

    // 标识用户设备的id，为一个结构，选择可以填写部分添加
    optional DeviceID device_id = 7;

    // 用户passport session交互返回的userid和username
    optional PassportID passport_id = 8;

    // 用户的电话号码
    optional string phone_num = 9[(autosetter) = "odp::extract_param(#mobilephone)"];

//--------------------以下描述日志时间信息--------------------

    // 服务器端记录的日志时间戳，单位为毫秒，此字段必打
	required int64 timestamp = 10[(autosetter) = "timestamp()"];

    // 服务器端记录的日志时区，默认为东八区，赋值为8；如为西部时区，如西三区赋值为-3。
    optional int32 timezone = 11;

    // 用户端记录的日志时间戳，单位为毫秒
    optional int64 user_timestamp = 12[(autosetter) = "to_int64(odp::extract_param(#t))"];

    // 用户端记录的日志时区，默认为东八区，赋值为8；如为西部时区，如西三区赋值为-3。
    optional int32 user_timezone = 13;

//--------------------以下描述位置相关信息--------------------

    // 用户的IP信息，标准为ip v4，格式为无符号整数而不是点分十进制。转换方式使用小端法。
    optional fixed32 ip = 14;

    // 用户的IP信息，标准为ip v6。
    optional string ipv6 = 15[(autosetter) = "odp::extract_param(#uip6)"];

    // 网络提供商信息，这里记录的是直接取到的值，而不是解析出的值
    optional string net_provider = 16;

    // 经纬度坐标下的移动坐标。为一个结构，包含x,y两个轴的信息以及精度
    optional MobileLocation mobile_location = 17;

    // 墨卡托坐标系下的移动坐标。
    optional MercatorLocation mercator_location = 18;

//--------------------以下描述不同应用形态关联的信息--------------------

    // httpservice打印的信息，如果不是httpservice类型的日志，不需要打印。
    optional HttpServiceInfo http_service_info = 19;

    // 设备的终端信息
    optional TerminalInfo terminal_info = 20;

    // 应用软件信息
    optional AppInfo app_info = 21;

    // 网络运营商信息
    optional NetInfo net_info = 22;

//--------------------以下字段为推荐使用，如果有类似含义，请使用相同字段名称--------------------

    // 动作名称，由产品线自定义
    optional string action_name = 23;

    // 流量渠道，遵守约定的名称，继续使用tn
    optional string tn  = 24;

    // 检索词信息，适用于检索类行为
    optional string query = 25;

    // 页面浏览信息
    optional ViewInfo view_info = 26;

    // 用户的访问端信息，用以区分用户的访问模式，适用于记录来自多个源的信息的日志。
    optional ClientType client_type = 27[default=MOBILE_APP];

}

message ObjectType{
    //对象id
    optional string obj_id = 1[(autosetter) = " odp::extract_param(#obj_id)"];
	//对象名称
	optional string obj_name = 2[(autosetter) = " odp::extract_param(#obj_name)"];
	//对象类型
	optional string obj_type = 3[(autosetter) = " odp::extract_param(#obj_type)"];
	//对象位置
	optional string obj_locate = 4[(autosetter) = " odp::extract_param(#obj_locate)"];
	//对象来源
	optional string obj_source = 5[(autosetter) = " odp::extract_param(#obj_source)"];
	//对象停留时长
	optional string obj_duration = 6[(autosetter) = " odp::extract_param(#obj_duration)"];
	//对象去向
	optional string obj_to = 7[(autosetter) = " odp::extract_param(#obj_to)"];
	//对象备用参数
	optional string obj_param1 = 8[(autosetter) = " odp::extract_param(#obj_param1)"];
	optional string obj_param2 = 9[(autosetter) = " odp::extract_param(#obj_param2)"];
	optional string obj_param3 = 10[(autosetter) = " odp::extract_param(#obj_param3)"];
}

// 主message名称需要修改成同日志名称一致。
message TiebaClientBatchUi {

    //----------------------------------------------------------------------------------------------------------------
    //  以下字段用于描述系统级传输信息，用户不需要关心。默认保留。
    //----------------------------------------------------------------------------------------------------------------

    //  传输配置字段，默认需要保留
    optional MetaData metadata                     = 25;
    optional string   log_tag                      = 26;


    // 统一公有字段
    required Common common = 41;


    //----------------------------------------------------------------------------------------------------------------
    //  以上id小于128的字段为预留字段，不能添加。用户自有字段id从129开始。
    //----------------------------------------------------------------------------------------------------------------
	//用户行为动作
	optional string urlkey = 129[(autosetter) = "odp::extract_param(#urlkey)"];
	//用户userid
	optional int64 userid = 130[(autosetter) = "to_int64( odp::extract_param(#uid))"];
	//吧id
	optional int64 fid = 131[(autosetter) = "to_int64( odp::extract_param(#fid))"];  
	//吧名称
	optional string fname = 132[(autosetter) = "odp::extract_param(#fname)"];  
	//主题贴id
	optional int64 tid = 133[(autosetter) = "to_int64( odp::extract_param(#tid))"]; 
    //回复贴id	
	optional int64 pid = 134[(autosetter) = "to_int64( odp::extract_param(#post_id))"];  
	//楼层数
	optional int64 floor = 135[(autosetter) = "to_int64( odp::extract_param(#floor_num))"];
   //客户单类型字符串表示,暂时不用
   optional string client_type_str = 136; 
	//图片数量
	optional int32 co_img_cnt = 137[(autosetter) = "to_int32( odp::extract_param(#co_img_cnt))"];
	//是否新用户
	optional int32 is_new_user = 138[(autosetter) = "to_int32( odp::extract_param(#is_new_user))"];
	
	//字段被使用，但是大表没有对应字段
	//楼中楼数 废弃
	optional int64 quota_id = 139;  
	//错误号
	optional int32 errno = 140[(autosetter) = "to_int32(odp::extract_param(#errno))"];
	
	//大表没有对应字段
	//是否是首页
	optional int32 isfp = 141[(autosetter) = "to_int32(odp::extract_param(#isfp))"];
	//当前渠道号
	optional string current_channel = 142[(autosetter) = "odp::extract_param(#cfrom)"];
	//是否主子版本新增
	optional int32 subapp_is_new_user = 143[(autosetter) = "to_int32(odp::extract_param(#subapp_is_new_user))"];
	//是都计入uv
	optional int32 is_uv = 144[(autosetter) = "to_int32(odp::extract_param(#isuv))"]; 
    optional int32 is_pv = 148[(autosetter) = "to_int32( odp::extract_param(#ispv))"];	
	
	optional ObjectType tieba_obj =145;
	
	//其他参数字段
	optional string tieba_params =146[(autosetter) = "odp::extract_param(#tieba_params)"];  //json格式
	optional int32 client_os = 147[(autosetter) = "to_int32(odp::extract_param(#_client_type))"]; // 设备操作系统
	optional int64 optime = 149[(autosetter) = "to_int64(odp::extract_param(#optime))"];
   optional int32 ip = 150;
   optional int64 uip = 151[(autosetter) = "to_int64(odp::extract_param(#uip))"];

	//楼中楼数
	optional int64 quote_id = 152[(autosetter) = "to_int64(odp::extract_param(#quote_id))"];  
	
}


// 以下信息用于Minos的传输配置，用户无需关心
// 如确定不使用Minos，也可以删除
message MetaData {
    optional int32 log_module_id   =  1 [default = 1575];
    optional string log_name        =  2 [default = "tieba_client_batch_ui"];
    optional string product_name    =  3 [default = "tieba"];
}
