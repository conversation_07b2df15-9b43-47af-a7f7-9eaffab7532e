[comlogsvr]

[comlogsvr.logswitch]
is_loc_open = 1
is_net_open = 0

[comlogsvr.localconf]
log_prefix = "stat-log"

[common]
global_read_only = 0
show_vote_power = 0

[fcrypt]
tieba_key = "Baidu.Vote.2007.04.12"
vote_pid = 1

[post]
video_admin_num = 10
video_member_num = 10
video_older_num = 1
video_login_num = 1
video_other_num = 1
img_admin_num   = 10
img_member_num  = 10
img_older_num   = 10
img_login_num   = 10
img_other_num   = 10
max_img_face_num = 10

[cookie]
domain = "tieba.baidu.com"
life_time = "2020-12-31 23:59:59"

[passgate]
user = "tieba"
pass = "tieba.baidu.com"

[env]
environment = 0
[env.orp_env]
subsys = "mo_client_frs"

[pblog]
  [pblog.tieba_client_ui]
  is_pblog_on = 1
  pb_proto_name = "tieba_client_ui"
  pb_msg_name = "TiebaClientUi"
  pb_tieba_param_filter = [
    "_phone_imei",
  "cuid",
  "un",
  "agent",
  "height",
  "width",
  "_client_version",
  "from",
  "url",
  "refer",
  "ispv",
  "pro",
  "mid",
  "logid",
  "mobilephone",
  "uip",
  "urlkey",
  "uid",
  "fid",
  "fname",
  "tid",
  "post_id",
  "floor_num",
  "co_img_cnt",
  "is_new_user",
  "quote_id",
  "errno",
  "_client_type",
  "BDUSS",
  "_bduss",
  "bduid",
  "BAIDUID",
  "cookieuid",
  "user_id",
  "_client_id",
  "phone_imei",
  "cuid_tmp",
  "_phone_imei_tmp",
  "ios_phone_imei",
  "_phone_newimei",
  "is_query_imei",
  "ios_imei_type",
  "reqkey",
  "client_type_str",
  "openid",
  "uipstr",
  "operrno",
  "no_un",
  "is_mobile",
  "_timestamp",
  "cgi_wait_time",
  "cgi_start_time",
  "php_start_time",
  "time_global_config_load_tieba",
  "time_server_time",
  "time_user_login",
  "time_total",
  "time_build_page",
  "time_pass_session",
  "time_tbapi_http_service_impusher_getClientsByCuids",
  "time_tbapi_http_service_modata_getIosOldImeiByNewImei",
  "time_tbapi_http_service_post_getPostsByThreadId",
  "time_config_load",
  "time_tbapi_call_cupid_query",
  "time_service_post_getPostsByThreadId",
  "time_execute_ext_logic",
  "time_main_call_multi",
  "time_main_get_strategy",
  "time_ext_call_multi",
  "time_main_build_var",
  "time_get_post_list",
  "time_main_get_cupid",
  "time_main_ext_all",
  "lcs_secure",
  "lcs_worker_id",
  "lcs_conn_time",
  "lcs_sign",
  "lcs_ip"
  ]

  [pblog.tieba_wap_ui]
  is_pblog_on = 1
  pb_proto_name = "tieba_wap_ui"
  pb_msg_name = "TiebaWapUi"
  pb_tieba_param_filter = [
      "req_data_BAIDUID",
    "cuid",
    "un",
    "agent",
    "mobile_OSVersion",
    "mobile_vendor",
    "mobile_model",
    "screen_height",
    "screen_width",
    "url",
    "refer",
    "pro",
    "mid",
    "logid",
    "mobilephone",
    "urlkey",
    "uid",
    "wiseuid",
    "uip",
    "is_new_user",
    "mobile_OSType",
    "mobile_bwservendor",
    "mobile_bwserver",
    "fid",
    "fname",
    "tid",
    "post_id",
    "floor",
    "addpic",
    "errno",
    "operrno",
    "ispv",
    "pubtype",
    "pagetype",
    "baiduid2",
    "cuid2",
    "req_data_BAIDU_WISE_UID",
    "req_data_IS_NEW_USER",
    "new_wiseuid",
    "new_cuid",
    "cuid_sub",
    "req_data_BIDUPSID",
    "uip_str",
    "cookie_user_jump",
    "req_data_TIEBAUID",
    "req_data_mo_originid",
    "cookieuid",
    "req_data_lp",
    "req_data_close_client_tip_bottom",
    "req_pb_isLivePost",
    "req_data_baiduid",
    "req_data_da_second_name",
    "req_data_PLUS",
    "pd",
    "req_data_da_obj_id",
    "req_data_da_first_name",
    "req_data_cuid",
    "total_time",
    "req_data_SET_PB_IMAGE_WIDTH",
    "req_data_pd_before_fix",
    "req_data_CLIENTHEIGHT",
    "req_data_plus_cv"
  ]

  [pblog.padweb]
    is_pblog_on = 1
    pb_proto_name = "tieba_padweb_ui"
    pb_msg_name = "TiebaPadwebUi"
    pb_tieba_param_filter = [
          "bduid",
      "un",
      "agent",
      "mobile_OSVersion",
      "mobile_vendor",
      "mobile_model",
      "screen_height",
      "screen_width",
      "url",
      "refer",
      "pro",
      "mid",
      "logid",
      "mobilephone",
      "urlkey",
      "uid",
      "uip",
      "is_new_user",
      "mobile_OSType",
      "mobile_bwservendor",
      "mobile_bwserver",
      "fid",
      "fname",
      "tid",
      "post_id",
      "errno",
      "ispv",
      "tbapi_apicall_cupid_query",
      "tbapi_http_service_forum_getBtxInfoByName",
      "tbapi_http_service_post_mgetThread",
      "tbapi_http_service_perm_getManagerAndMemberInfo",
      "tbapi_http_service_sign_getUserSignInfo",
      "tbapi_http_service_anti_antiTbmaskQuery",
      "tbapi_http_service_zan_batchIsLiked",
      "config_load",
      "tbapi_apicall_frsstrategy_cupid",
      "tbapi_http_service_post_getPostsByThreadId",
      "tbapi_http_service_user_mgetUserForumInfo",
      "strategy_hit",
      "tbapi_http_service_post_getGoodThreads",
      "tbapi_http_service_user_mgetUserData",
      "tbapi_http_service_perm_getBawuList",
      "tbapi_http_service_forum_getBtxInfo",
      "tbapi_apicall_userforum_sign_info",
      "tbapi_call_cupid_query"
    ]

     [pblog.feye_stat]
    is_pblog_on = 1
    pb_proto_name = "tieba_pcweb_ui"
    pb_msg_name = "TiebaPcwebUi"
    pb_tieba_param_filter = [
          "bduid",
      "un",
      "agent",
      "cookie_bduss",
      "url",
      "refer",
      "pro",
      "mid",
      "logid",
      "mobilephone",
      "urlkey",
      "uid",
      "uip",
      "is_new_user",
      "fid",
      "fname",
      "tid",
      "post_type",
      "post_id",
      "quote_type",
      "floor_num",
      "image_num",
      "errno",
      "ispv",
      "img_feature",
      "referer",
      "ref",
      "user_start_time",
      "cookieuid",
      "no_un",
      "dir_two",
      "serveice_type",
      "zhibo_type",
      "dir_one",
      "uri",
      "gradelevel",
      "is_like",
      "auto_jump",
      "with_pop",
      "ps_eqid",
      "locationInfo",
      "access_un",
      "server_ip",
      "forum_name",
      "second_dir",
      "BDUSS",
      "user_name"
    ]

         [pblog.wap]
    is_pblog_on = 1
    pb_proto_name = "tieba_client_batch_ui"
    pb_msg_name = "TiebaClientBatchUi"
    pb_tieba_param_filter = [
          "_phone_imei",
      "cuid",
      "un",
      "agent",
      "height",
      "width",
      "_client_version",
      "from",
      "url",
      "refer",
      "ispv",
      "pro",
      "mid",
      "logid",
      "mobilephone",
      "uip",
      "urlkey",
      "uid",
      "fid",
      "fname",
      "tid",
      "post_id",
      "floor_num",
      "co_img_cnt",
      "is_new_user",
      "quote_id",
      "errno",
      "_client_type",
      "BDUSS",
      "_bduss",
      "bduid",
      "BAIDUID",
      "cookieuid",
      "user_id",
      "_client_id",
      "phone_imei",
      "cuid_tmp",
      "_phone_imei_tmp",
      "ios_phone_imei",
      "_phone_newimei",
      "is_query_imei",
      "ios_imei_type",
      "reqkey",
      "client_type_str",
      "openid",
      "uipstr",
      "operrno",
      "no_un",
      "is_mobile",
      "_timestamp",
      "cgi_wait_time",
      "cgi_start_time",
      "php_start_time",
      "time_global_config_load_tieba",
      "time_server_time",
      "time_user_login",
      "time_total",
      "time_build_page",
      "time_pass_session",
      "time_tbapi_http_service_impusher_getClientsByCuids",
      "time_tbapi_http_service_modata_getIosOldImeiByNewImei",
      "time_tbapi_http_service_post_getPostsByThreadId",
      "time_config_load",
      "time_tbapi_call_cupid_query",
      "time_service_post_getPostsByThreadId",
      "time_execute_ext_logic",
      "time_main_call_multi",
      "time_main_get_strategy",
      "time_ext_call_multi",
      "time_main_build_var",
      "time_get_post_list",
      "time_main_get_cupid",
      "time_main_ext_all",
      "lcs_secure",
      "lcs_worker_id",
      "lcs_conn_time",
      "lcs_sign",
      "lcs_ip"
    ]

    [pblog.tieba_hatch_ui]
    is_pblog_on = 1
    pb_proto_name = "tieba_hatch_ui"
    pb_msg_name = "TiebaHatchUi"
    pb_tieba_param_filter = [
          "logid",
      "errno",
      "tieba_client_type",
      "pro",
      "mid",
      "urlkey",
      "urlkey_type",
      "ispv",
      "uid",
      "un",
      "imei",
      "cuid",
      "baiduid",
      "phone_num",
      "is_new_user",
      "uip",
      "ipv6",
      "gps_x",
      "gps_y",
      "gps_r",
      "net_type",
      "net_apn",
      "user_timestamp",
      "timestamp",
      "user_timezone",
      "timezone",
      "os",
      "os_version",
      "brand",
      "brand_type",
      "height",
      "width",
      "ppi",
      "app_name",
      "app_version",
      "app_channel",
      "url",
      "refer",
      "pn",
      "agent",
      "cookie",
      "obj_id",
      "obj_name",
      "obj_type",
      "obj_locate",
      "obj_source",
      "obj_duration",
      "obj_to",
      "obj_param1",
      "obj_param2",
      "obj_param3",
      "aid",
      "hid",
      "friend_uid",
      "pid"
    ]

    [pblog.tieba_recom_ui]
    is_pblog_on = 1
    pb_proto_name = "tieba_recom_ui"
    pb_msg_name = "TiebaRecomUi"
    pb_tieba_param_filter = [
          "unique_log_key",
      "unified_timestamp",
      "new_log_type",
      "log_timestamp",
      "tid",
      "client_log",
      "logid",
      "timestamp",
      "errno",
      "pro",
      "mid"
    ]