# client_db.conf
# pb 注意！ 安卓是奇数，IOS是偶数
pb_img_cdn = "iphone,9.1.0,INFINITY,6 | iphone,5.0.100,INFINITY,2 | android,*******,INFINITY,7 | android,*******,INFINITY,5 | android,5.0.100,INFINITY,3 | android,4.5.0,5.0.99,1 | ipad,4.0.0,INFINITY,4"

pb_show_user_icon = "iphone,5.0.100,INFINITY,4 | android,5.0.100,INFINITY,6 | ipad,4.0.0,INFINITY,4"

pb_parse_phone = "iphone,4.3.0,INFINITY | android,4.3.0,INFINITY"

pb_parse_bdhd = ""

pb_check_spam_url = "iphone,5.0.100,INFINITY | android,5.0.100,INFINITY | ipad,4.0.0,INFINITY"

pb_show_voice = "android,4.5.0,INFINITY | iphone,4.5.0,INFINITY | ipad,4.0.0,INFINITY"

pb_add_post_list = "android,5.7.0,INFINITY | iphone,5.7.0,INFINITY | ipad,4.1.0,INFINITY"

pb_activity = "android,0.0.0,INFINITY | iphone,0.0.0,INFINITY | ipad,4.0.0,INFINITY"

pb_activity_new = "android,6.9.6,6.9.6 | iphone,6.9.6,INFINITY | android,6.9.8,INFINITY"

pb_live_post = "android,5.0.100,INFINITY | iphone,5.0.100,INFINITY | ipad,4.0.0,INFINITY"

pb_filter_var = "iphone,4.2.0,INFINITY | ipad,4.0.0,INFINITY"

pb_female_icon = "iphone,4.3.0,INFINITY | ipad,4.0.0,INFINITY | android,6.3.0,INFINITY"

pb_new_smile = "iphone,5.5.0,INFINITY | android,5.4.0,INFINITY | ipad,4.0.0,INFINITY"

pb_need_zan = "iphone,5.7.0,INFINITY | android,5.7.0,INFINITY | ipad,5.7.0,INFINITY"

pb_graffiti_to_img = "iphone,0.0.0,7.6.999 | android,0.0.0,7.2.999"

pb_need_graffiti = "iphone,7.7.0,INFINITY | android,7.3.0,INFINITY"

pb_emoji = "iphone,0.0.0,INFINITY | ipad,0.0.0,INFINITY | ipad,4.0.0,INFINITY"

pb_disable_cdn = "iphone,5.1.3,5.1.3 | iphone,5.5.0,5.5.0 | android,5.4.2,5.4.2 | android,5.5.0,5.5.1"

pb_positioning_floor = "iphone,5.0.100,INFINITY | android,5.0.100,INFINITY | ipad,4.0.0,INFINITY"

pb_data_saving_ios = "iphone,6.1.0,INFINITY"

pb_data_saving_android = "android,6.1.0,INFINITY"

pb_user_list = "iphone,6.1.0,INFINITY | android,6.1.0,INFINITY"

pb_game_news = "android,6.6.0,INFINITY | iphone,6.7.0,INFINITY"

pb_iconinfo = "android,6.8.0,INFINITY | iphone,6.8.0,INFINITY"

# frs
frs_img_cdn = "iphone,7.3.0,INFINITY,8 | android,7.3.0,INFINITY,7 | iphone,5.7.0,7.2.99,6 | android,5.7.0,7.2.99,5 | iphone,5.0.100,5.6.99,2 | android,5.0.100,5.6.99,3 | android,4.5.0,5.0.99,1 | ipad,4.0.0,INFINITY,4"

frs_show_user_icon = "iphone,5.0.100,INFINITY,1 | android,5.0.100,INFINITY,5 | ipad,4.0.0,INFINITY,1"

frs_show_voice = "android,4.5.0,INFINITY | iphone,4.5.0,INFINITY | ipad,4.0.0,INFINITY"

frs_activity_thread = "android,0.0.0,INFINITY | iphone,0.0.0,INFINITY | ipad,4.0.0,INFINITY"

frs_live_post = "android,5.0.100,INFINITY | iphone,5.0.100,INFINITY | ipad,4.0.0,INFINITY"

frs_filter_var = "iphone,4.2.0,INFINITY, ipad,4.0.0,INFINITY"

frs_disable_cdn = "iphone,5.1.3,5.1.3 | iphone,5.5.0,5.5.0 | android,5.4.2,5.4.2 | android,5.5.0,5.5.1"

frs_show_forum_tag = "iphone,4.0.0,INFINITY | android,4.0.0,INFINITY | ipad,4.0.0,INFINITY"

frs_need_zan = "iphone,5.7.0,INFINITY | android,5.7.0,INFINITY | ipad,5.7.0,INFINITY"

frs_data_saving_ios = "iphone,6.1.0,INFINITY"

frs_data_saving_android = "android,6.1.0,INFINITY"

frs_user_list = "iphone,6.1.0,INFINITY | android,6.1.0,INFINITY"
frs_world_cup = "iphone,6.1.0,INFINITY | android,6.1.1,INFINITY"
frs_chunhua = "android,6.1.3,INFINITY"

frs_like_forum = "iphone,6.2.0,INFINITY | android,6.5.0,INFINITY"

frs_game_news = "iphone,6.3.0,INFINITY | android,6.4.0,INFINITY"

frs_iconinfo = "android,6.8.0,INFINITY | iphone,6.8.0,INFINITY"

frs_push_thread = "android,7.0.0,INFINITY | iphone,7.0.0,INFINITY"
#frs-ext
frs_activity_enter_homework = "iphone,6.8.0,INFINITY | android,6.8.0,INFINITY"
frs_banner_adense = "iphone,6.8.0,INFINITY | android,6.8.0,INFINITY"
# bawu_enter
bawu_enter_control = "iphone,7.0.0,INFINITY | android,7.0.0,INFINITY"
# star_schedule
star_schedule = "iphone,7.1.0,INFINITY | android,7.1.0,INFINITY"
#msign
msign_use_member_strage = "iphone,6.1.0,INFINITY | ipad,6.1.0,INFINITY | android,6.3.0,INFINITY"
msign_use_new_member_strage = "iphone,6.2.0,INFINITY | ipad,6.2.0,INFINITY | android,6.3.0,INFINITY"

##frs,pb show sign info
show_miss_sign_num = "iphone,6.2.0,INFINITY | ipad,6.2.0,INFINITY | android,6.3.0,INFINITY"

#new fangtan live
fangtan_live_new = "android,7.2.0,INFINITY | iphone,7.2.0,INFINITY"
#企业商业化7.3 版本功能
advanced_lottery = "android,7.3.0,INFINITY | iphone,7.3.0,INFINITY"

#企业商业化7.4 版本功能
convene_activity = "android,7.4.0,INFINITY | iphone,7.4.0,INFINITY"
#企业商业化7.5 版本功能
carrier_headline = "android,7.5.0,INFINITY | iphone,7.5.0,INFINITY"
#企业商业化7.7 版本功能
adsense_deeplink = "android,7.7.0,INFINITY | iphone,7.7.0,INFINITY"
homepage_deeplink = "android,7.8.0,INFINITY | iphone,7.8.0,INFINITY"
#吧主召集帖 7.3 版本控制
bazhu_task_version = "android,7.3.0,INFINITY | iphone,7.3.0,INFINITY"

#pay
pay_member_is_charge = "iphone,6.1.2,INFINITY | ipad,6.1.2,INFINITY"
pay_tmall_menu = "iphone,6.3.0,INFINITY | android,6.4.0,INFINITY"
andorid_buyprop_control =  "android,6.5.7,INFINITY"

#forum
forumrecommend_check_login = "iphone,6.1.0,6.6.100 | android,6.1.1,6.6.100"
forumrecommend_sep = "iphone,6.5.0,INFINITY | android,6.5.0,INFINITY"
forumrecommend_adsense_banner  = "iphone,6.8.0,INFINITY | android,6.8.0,INFINITY"

#frs,forum
banner_url_proto = "iphone,6.5.0,INFINITY | android,6.5.0,INFINITY"

#frs,pb
spread_ad = "iphone,6.2.0,INFINITY | android,6.1.1,INFINITY"
activity_control = "iphone,6.5.0,INFINITY | android,6.5.0,INFINITY"
pb_floor_top = "iphone,6.6.0,INFINITY | android,6.6.0,INFINITY"
starenter_control = "iphone,6.5.0,INFINITY | android,6.5.0,INFINITY"
frs_filtergcon_android = "android,6.0.0,INFINITY"
spread_ad_second_generation = "iphone,6.2.0,INFINITY | android,6.3.0,INFINITY"
spread_ad_new_style = "iphone,6.3.0,INFINITY | android,6.4.0,INFINITY"

#新年活动的彩蛋样式
frs_new_year_show_style = "iphone,6.6.0,INFINITY | android,6.5.5,INFINITY"

frs_gcon_pic_android = "android,6.2.0,INFINITY"
frs_gcon_pic_ios  = "iphone,6.2.0,INFINITY "
frs_live_list_show  = "android,6.4.5,INFINITY "
frs_activity_head = "iphone,6.9.0,INFINITY "
#client-user
user_follow_add_friend_list = "iphone,6.1.4,INFINITY | android,6.2.2,INFINITY"
user_profile = "iphone,6.3.0,INFINITY | android,6.4.0,INFINITY | iphone,6.1.7,INFINITY,true,kuang | iphone,6.8.0,INFINITY,true,tieba | iphone,6.8.0,INFINITY,true,ipad | android,6.3.0,INFINITY,true,kuang | android,*******,INFINITY,true,mini "
game_full_rank =  "iphone,6.3.0,INFINITY | android,6.4.0,INFINITY"
user_private_info  =  "iphone,6.8.0,INFINITY | android,6.8.0,INFINITY"
user_iconinfo = "android,6.8.0,INFINITY | iphone,6.8.0,INFINITY"
unaudited_video_post_list = "iphone,8.7.0,INFINITY | android,8.8.0,INFINITY"
max_unaudited_video_count = 100
# client_business
client_business_version_control = "android,6.9.6,6.9.6 | iphone,6.9.6,INFINITY | android,6.9.8,INFINITY"
user_feed_consent  =  "iphone,7.3.0,INFINITY | android,7.3.0,INFINITY"
#client.conf
#是否开启debug模式
debug = 1
#是否开启qa debug模式
qa_debug = 0
#是否开启qa debug response
qa_debug_resp = 0
#常去喜欢的吧分页结果数
favolike_rn = 100
#frs页的结果数
frs_rn = 50
#pb页的结果数
pb_rn = 30
pb_rn_zeus = 30
zeus_smile_count = 5
#推荐页的结果数
recommend_rn = 10
#pb test模式的结果数
pb_test_rn = 60
#pn test模式的用户ID白名单
pb_test_uid = "*********,*********,9005783"
#全局最大的结果数
global_default_rn = 500
#默认取图片id的数目
batch_photo_id_number = 360
#默认一次取图片的个数
batch_photo_number = 30
#是否必须是post方式
must_post = 1
#transcoder_server
transcoder_server= "http://mt1.baidu.com"
#passport sapi
#passport_sapi_server = "https://db-testing-psp01.db01.baidu.com"
#passport_sapi_server_port = 8443
passport_sapi_server = "https://passport.baidu.com"
passport_sapi_server_port = 80
passport_sapi_read_timeout = 3000
passport_sapi_connect_timeout = 500
passport_sapi_retry = 2

passport_sapi_tpl = "tb"
passport_sapi_appid = "1"
passport_sapi_key = "6e93e7659ae637845c7f83abee68a740"

#passport验证码图片host
passport_vcode_host = "http://wappass.baidu.com/cgi-bin/genimage?"

userinfo_encrypt_version = "qw"
userinfo_encrypt_qw_key = "1dLd45irnxXipwMek034fu-e"
userinfo_encrypt_qw_algorithm = "tripledes"
userinfo_encrypt_qw_mode= "ecb"

commit_open_id = "tbclient"
commit_secure_key = "tbclient20111111&#&#"

default_baiduid = "B41A54B331DDC9DA62D597CD2042CEDA:FG=1"
#展示顶的阈值
comment_threshold = 100

#图片上传后的地址
pic_host = "http://imgsrc.baidu.com/forum/pic/item/"

#头像服务器的URL
head_image_host = "**********"
head_image_port = 80

#PB页大图的缩略大小
pb_big_image_max_width = 640

#最多插入5个表情
max_smile_count = 10

#表情前缀
smile_host = "http://static.tieba.baidu.com/tb/editor/images/client/"
smile_host_new = "http://static.tieba.baidu.com/tb/editor/images/"
tieba_smile_host = "http://static.tieba.baidu.com/tb/editor/images/"
zhangbai_smile_host_imo = "http://imo.baidu.com/static/images/emotion/"
zhangbai_smile_host_t = "http://t.baidu.com/static/images/emotion/"
tieba_vcode_host = "http://tieba.baidu.com/cgi-bin/genimg?"
zeus_simle_host = "http://static.tieba.com/tb/editor/images/wise/zface/"

#回复/at我的动态页的结果数
feed_rn = 20
#回复/at我的动态页的最大结果数
max_feed_rn = 100
#多次获取回复/at我的动态页，每次获取的结果数
each_feed_rn = 50
#关注我的列表的结果数
follow_rn= 20
#我的粉丝列表的结果数
fans_rn= 20
#图片代理方式 1表示反向代理，2表示302代理
img_proxy_type = 1
img_proxy_read_timeout = 4000
img_proxy_connect_timeout = 1000
img_proxy_retry = 1
img_qulity = 80
img_zeus_qulity = 80
img_Thumbnail_size = 70
#image_proxy_host = "http://***********"
#image_proxy_port = 8088

log_file_path = "/home/<USER>/tieba-php/client_log/"

magic_sign = "123456tbclient654321"

open_sign_check = 1

#楼中楼每页显示楼数
floor_page_size = 10

inner_tieba_host = "inner-orp.tieba.baidu.com"
inner_tieba_port = 80
inner_tieba_connect_timeout = 100
inner_tieba_read_timeout = 1000

#是否开启第三方渠道激活统计（1表示开启，0表示关闭）
#已不和该渠道合作，重构代码时将这部分配置去掉
#do_sync_active = 1
#激活统计实际发送的百分比
#sync_active_percent = 100
#发送激活统计的渠道号
#sync_active_from = "aishide"


#短信
send_sms= 0
send_sms_host= "emp.baidu.com"
send_sms_port= 8080

#随手发相关
ssf_thread_type = 7
ssf_forum_name = "百度贴吧手机客户端微贴"
ssf_froum_id = "3525444"

#神来一句表情class
bde_smiley = "BDE_Smiley2"

#是否开启富文本电话号码识别
parse_phone = 1

#是否开启cdn url模式
is_cdn_url = 1
#cdn pic url flow
cdn_url_flow = 10

#在frs/page, pb/page, pb/floor中显示语音播放键或者语音文字， 0 不显示 1 显示语音图标 2 显示语音文字
voice_style = 1

# 一键签到开关
m_sign = 1
# 一键签到等级
m_sign_level = 7
# 一键签到等级(付费会员)
m_sign_member_level = 5
# 一键签到等级(高级会员)
m_sign_s_member_level = 1
# 时间阀值
sign_time_threshold = 1
# 签到个数阀值
max_sign_num = 50
# 高级会员签到数
max_member_sign_num = 200

#扒吧节穿越配置,top吧词表
across_forum_wordlist_top = "tb_wordlist_redis_baba_across_forum100"
#扒吧节穿越配置,其他吧词表
across_forum_wordlist_else = "tb_wordlist_redis_baba_across_forum_else"
#扒吧节穿越配置,活动开始时间
across_forum_start_time = "2016-12-03 00:00:00"
#扒吧节穿越配置,活动结束时间
across_forum_end_time = "2016-12-04 23:59:59"
#扒吧节穿越配置,活动标识
across_forum_date_prefix = "20161203"

#client_db
capi_mysql_host = "***********"
capi_mysql_port = 4408
capi_mysql_connect_timeout = 1000
capi_mysql_user = "forum"
capi_mysql_passwd = "2j38ej34js8d"
capi_mysql_db = "client"

#野表情
meme_sign_key = "7S6wbXjEKL9N"
meme_text = "自定义表情"
meme_to_text_conf = "iphone,0.0.0,8.5.999 | android,0.0.0,8.5.999"
max_meme_count = 10

#视频
video_host_cdn = "tb-video.bdstatic.com"
video_host_bos = "bos.nj.bpc.baidu.com"
video_host_ip = "cmcc=**************&cucc=*************&ctcc=*************"
