[[Operator]]
ImportPackage = "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
OPName = "user_agent_common_params"
StructName = "OperatorCommonParams"

[[Operator]]
ImportPackage = "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/useragent/privateparams"
OPName = "user_agent_private_params"
StructName = "OperatorPrivateParams"

[[Operator]]
ImportPackage = "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentuids"
OPName = "user_agent_agent_uids"
StructName = "OperatorAgentUIDs"
NeedAdapter = true

[[Operator]]
ImportPackage = "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentwhitelist"
OPName = "user_agent_whitelist"
StructName = "OperatorAgentWhitelist"
NeedAdapter = true

[[Operator]]
ImportPackage = "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agent"
OPName = "user_agent_agent"
StructName = "OperatorAgent"
NeedAdapter = true

[[Operator]]
ImportPackage = "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/useragent/buildoutput"
OPName = "user_agent_build_output"
StructName = "OperatorBuildOutput"
