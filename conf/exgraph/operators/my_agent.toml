[[Operator]]
ImportPackage = "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
OPName = "my_agent_common_params"
StructName = "OperatorCommonParams"

[[Operator]]
ImportPackage = "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/myagent/privateparams"
OPName = "my_agent_private_params"
StructName = "OperatorPrivateParams"

[[Operator]]
ImportPackage = "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentuids"
OPName = "my_agent_agent_uids"
StructName = "OperatorAgentUIDs"
NeedAdapter = true

[[Operator]]
ImportPackage = "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agent"
OPName = "my_agent_agent"
StructName = "OperatorAgent"
NeedAdapter = true

[[Operator]]
ImportPackage = "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentwhitelist"
OPName = "my_agent_whitelist"
StructName = "OperatorAgentWhitelist"
NeedAdapter = true

[[Operator]]
ImportPackage = "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/myagent/buildoutput"
OPName = "my_agent_build_output"
StructName = "OperatorBuildOutput"
