Name = "hottopic_topicdetail"
Exgraph = """
topic_detail_prepare->
[topic_detail_topic_info, word_list]->
[
    [
        (
            [thread_list, module_info, user_info]->
            [common_thread_filter, build_thread_list, hot_post, real_post, relate_forum, relate_topic, ai_game, ai_bot]->
            [deal_post, common_threaduser_filter, linggan_filter, build_meta]
        ){name="ext_stage1"}
    ],
    theme_color
]{transCanLost=\"true\"}
-> [build_topic_info, adjust_area]
-> search_exit
-> topic_detail_resp
"""