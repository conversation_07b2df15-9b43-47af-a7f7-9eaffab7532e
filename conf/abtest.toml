Name = "UbsAbtest"

[agree]
allfunctions                = "" # 对所有接口生效实验，配置为实验组id或name
format                      = "jsonrow"
ie                          = "utf-8"
httpMethod                  = "post"
ralServiceName              = "agree_pandora" # 这个参数可以指定实验的流量打到哪个ral service
getAgreeByUserIdAndPostIds  = "tieba_agree_php2go"
getAgreeByUserIdAndThreadId = "tieba_agree_php2go"
getTopAgreePostIds          = "tieba_agree_php2go"
getAgreeInfoForHomePage     = "tieba_agree_php2go"

[photo]
allfunctions                = "" # 对所有接口生效实验，配置为实验组id或name
format                      = "jsonrow"
ie                          = "utf-8"
httpMethod                  = "post"
ralServiceName              = "photo_go" # 这个参数可以指定实验的流量打到哪个ral service
getImageAbt                 = "AtAnyTime"
getPbPicList                = "AtAnyTime"
getAlbumInfoByThreadIds     = "AtAnyTime"
getPicInfoByPostIds         = "AtAnyTime"
getPbPicInfo                = "AtAnyTime"