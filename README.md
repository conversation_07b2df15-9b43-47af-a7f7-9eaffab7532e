# 项目名称

该项目基于GDP2框架开发：http://gdp.baidu-int.com/

最低Go版本为1.16

## 快速开始
如何构建、安装、运行

### 配置文件目录说明：
conf:    开发时的配置  
conf_online：线上配置文件   
conf_qa:  测试的配置

服务端口：
1. 端口区间： conf/port.conf  
2. 各 Server 监听的端口：hestia/conf/hestia.conf

### 运行：

```shell

> go run main.go  
> go run main.go -conf conf/app.toml    
> go run main.go -conf conf_qa/app.toml  
```

### 使用热重启功能：
若使用 baidu/gdp/hestia 提供的热重启功能，请先阅读文档：
http://wiki.baidu.com/pages/viewpage.action?pageId=1370968292 
并按照文档对 PaaS 环境做相应配置部署。

当前模块需要做如下修改：
1. conf_online/app.toml.template 的 'EnableHestia' 设置为 true
2. 删除 conf_online/pandora.ini 
3. mv conf_online/pandora.ini.hestia conf_online/pandora.ini


### 更新 GDP 的所有组件到最新：
```bash
go get icode.baidu.com/baidu/gdp/...
```

## 测试
```bash
go test -race -v -cover -gcflags="-N -l" ./...
```

## 如何贡献
贡献 patch 流程、质量要求：
* 代码编写要符合[百度 Go 代码规范](http://gdp.baidu-int.com/go_style_guide?from=icode.baidu.com/baidu/tieba-server-user-base/go-client-forum)
* 有对应的单测代码
* 代码是格式化好的

推荐代码格式化工具：
https://github.com/fsgo/go_fmt (会将注释、import 内容 按照规范要求格式化)

## 讨论
如流讨论群：XXXX
