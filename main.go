// Copyright(C) 2022 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2022-06-22, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package main

import (
	"context"
	"flag"
	"log"

	"github.com/json-iterator/go/extra"

	_ "icode.baidu.com/baidu/gdp/automaxprocs"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/bootstrap"
)

// 应该的主配置文件，可以通过修改此参数来切换运行环境
// 比如可以有多套环境 conf(开发环境配置)、conf_qa (测试环境配置)、conf_online（线上环境配置）
// 在编译的时候，会将 conf_online 打包为 conf，其他的配置不会打包
var appConfig = flag.String("conf", "./conf/app.toml", "app config file")

func main() {
	flag.Parse()

	config := bootstrap.MustLoadAppConfig(*appConfig)

	extra.RegisterFuzzyDecoders()

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	bootstrap.MustInit(ctx)

	// 若应用自身内存消耗很少，可以通过此压舱物的方式来对 GC 进行优化,降低 GC 频率
	ballast := make([]byte, 3<<30) // 分配 1G 内存,不会占用物理内存空间
	defer func() {
		log.Println("ballast len", len(ballast))
	}()

	log.Println("server exit:", bootstrap.StartServers(ctx, config))
}
