package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/score/getThreadScoreItemList"
)

type GetThreadScoreItemListData struct {
	Request *getThreadScoreItemList.DataReq
	BaseObj *uiclient.UIBaseAction

	UserID           uint64 // 当前登录用户uid
	ThreadID         uint64 // 帖子id
	Pn               uint32 // 页码
	Rn               uint32 // 每页数量
	SortType         uint32 // 排序方式
	NeedUserScore    uint32 // 是否需要用户分数
	NeedScoreUserNum uint32 // 是否需要用户数量
	ForumID          uint64 // 吧id
	ItemIds          []uint64// 帖子打分项id列表
}
