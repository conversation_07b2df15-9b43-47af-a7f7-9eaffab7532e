package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/hottopic"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
)

type CTopicDetailBaseData struct {
	Request *topicDetail.DataReq
	BaseObj *uiclient.UIBaseAction
	
	StaticField *TopicDetailStaticField
}

type TopicDetailStaticField struct {
	TopicID   int64
	TopicName string
	IsDeleted int32

	UserID   int64
	IsLogin  int32
	Cuid     string
	IsClient bool

	SampleIDs []string
	PageType  string // 页面类型，端内/端外（小程序/wise等）

	IntClientType    int
	StrClientVersion string

	TopicInfo *client.RecomTopicList // 话题信息

	RelateThreadIds []int64 // 话题帖子id

	RelateThreadInfo ThreadInfo // 话题帖子信息

	RelateThreadList []*client.ThreadInfo // 话题帖子信息

	Switch map[string]any // 各类开关

	WordList map[string]any // 词表信息

	User *client.User

	ContentAreaCardList []*client.AreaCardData

	ModuleInfo *ModuleInfo

	ThemeColor *client.ThemeColorInfo

	HasMore int32 // 是否还有

	Meta *MetaInfo // tdk
}

// ModuleInfo todo 补充信息
type ModuleInfo struct {
	// 普通贴
	CommonThread ModuleThread

	// 投票贴
	VoteThread ModuleThread

	// 打分贴
	ScoreThread ModuleThread

	// PK贴
	PKThread ModuleThread

	// 相关吧
	RelateForum ModuleForum

	// 相关话题
	RelateTopic ModuleTopic

	// ai游戏
	AiGame ModuleAiGame

	// 智能体
	AiBot ModuleAiBot

	// AI游戏 支持agent
	AiGameNew ModuleAiGameNew
}

type ModuleThread struct {
	ThreadInfo  ThreadInfo
	ThreadList  []*client.ThreadInfo
	ScoreIDsMap map[int64][]uint64
	ModuleOrder int32 // 模块顺序
}

type ThreadInfo struct {
	ThreadIds       []int64
	PostIds         map[int64][]int64
	RecomTagInfo    map[int64]*client.ThreadRecommendTag
	ShowPostID      []*ShowPostIDInfo
	FilterThreadIds []int64                  // 过滤帖子id
	HotPostInfo     map[int64][]*client.Post // 热议贴信息
	RealPostInfo    map[int64][]*client.Post // 实时回复信息
	FilterPostIds   []int64
}

type ShowPostIDInfo struct {
	ThreadID   uint64   `json:"thread_id"`
	PostIDList []uint64 `json:"post_id_list"`
}

type ModuleForum struct {
	ForumIds       []uint32
	RelateForumRaw map[uint32]*hottopic.RelateForum
	ForumInfoList  []*client.SimpleForum
	RecomTagInfo   []*client.ThreadRecommendTag
	ModuleOrder    int32 // 模块顺序
}

type ModuleTopic struct {
	TopicIds      []int64
	TopicInfoList []*client.RecomTopicList
	RecomTagInfo  []*client.ThreadRecommendTag
	ModuleOrder   int32 // 模块顺序
}

type ModuleAiGame struct {
	GameRoomIds    []int64
	AiGameInfoList []*client.AiInteractiveGamePlot
	RecomTagInfo   []*client.ThreadRecommendTag
	ModuleOrder    int32 // 模块顺序
}

type ModuleAiBot struct {
	AiBotUID    int64
	AiBotInfo   *client.AibBot
	ModuleOrder int32 // 模块顺序
}

type ModuleAiGameNew struct {
	AiGameUID   int64
	AiGameInfo  *client.AibBot
	ModuleOrder int32 // 模块顺序
}

type ModuleOrderInfo struct {
	ModuleOrder int32        // 模块顺序
	ModuleInfo  CardDataInfo // module_info
}

type CardDataInfo struct {
	ThreadList    []*client.ThreadInfo
	ForumInfoList []*client.SimpleForum
	TopicInfoList []*client.RecomTopicList
	AiGameList    []*client.AiInteractiveGamePlot
	AiBotInfo     *client.AibBot
	AiGameNew     *client.AibBot
}

type CollectInfo struct {
	CollectStatus  int32
	CollectMaskPid string
}

type MetaInfo struct {
	Title string
	Keywords string
	Description  string
}
