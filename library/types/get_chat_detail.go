package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getChatDetail"
)

const (
	PartialVisibleGuest  = 0 // 客态
	PartialVisibleMaster = 1 // 主态
)

type GetChatDetailBaseData struct {
	Request  *getChatDetail.GetChatDetailReq
	BaseObj  *uiclient.UIBaseAction
	UserID   int64
	RobotUID uint64
}

// HitKeyWords 单角色命中关键词的词表配置结构
type HitKeyWords struct {
	RobotUID int64    `json:"robot_uid"`
	KeyWords []string `json:"key_words"`
}
