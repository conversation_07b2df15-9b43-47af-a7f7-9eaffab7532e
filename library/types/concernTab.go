package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/video/concernTab"
)

type CConcernTabBaseData struct {
	Request *concernTab.ConcernTabReq
	BaseObj *uiclient.UIBaseAction

	StaticField *ConcernTabStaticField
}

type ConcernTabStaticField struct {
	IntFeedId             int
	IntLastFeedId         string
	IntHasMore            int
	Limit                 int
	LoadType              int
	UserId                int64
	IntClientType         int
	StrClientVersion      string
	IntScreenWidth        int32
	IntScreenHeight       int32
	RequestTimes          int32
	IntScreenDip          float64
	IntQType              int32
	StringShowWord        string
	ArrForumFriends       []*client.ThreadUser
	ThreadInfoList        []*client.ThreadInfo
	ArrThreadTids         []uint64
	IntBjhThreadType      int
	ConcernThreadInfoList []*client.ThreadInfo
	SourceType            int // 0：关注用户 1：吧友在看
	ArrFansNum            map[int64]uint32
}

func NewConcernTabStaticField() *ConcernTabStaticField {
	return &ConcernTabStaticField{
		Limit:            10,
		IntClientType:    1,
		StrClientVersion: "12.6.5",
		IntScreenWidth:   1280,
		IntScreenHeight:  1920,
		IntScreenDip:     3,
		IntBjhThreadType: 105,
		ArrFansNum:       make(map[int64]uint32),
		ThreadInfoList:   make([]*client.ThreadInfo, 0),
		ArrForumFriends:  make([]*client.ThreadUser, 0),
		ArrThreadTids:    make([]uint64, 0),
	}
}
