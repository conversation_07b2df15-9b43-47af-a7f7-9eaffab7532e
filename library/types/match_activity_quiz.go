package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/matchActivityQuiz"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/quiz"
)

type MatchActivityQuizBaseData struct {
	Request *matchActivityQuiz.DataReq
	BaseObj *uiclient.UIBaseAction
	UserID  int64
}

type GetQuizByIdsRes struct {
	ErrNo  int            `json:"errno"`
	ErrMsg string         `json:"errmsg"`
	Data   *quiz.QuizInfo `json:"data"`
}
