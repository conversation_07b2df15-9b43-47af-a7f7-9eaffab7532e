package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"
	clientForumGuide "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"
)

type CForumGuideBaseData struct {
	Request *forumGuide.ForumGuideReq
	BaseObj *uiclient.UIBaseAction

	StaticField *ForumGuideStaticField
}

// ForumEntryExperienceName 进吧tab重构对应的实验名称
const ForumEntryExperienceName = "12_56_forumentry_a"

const (
	FlistSortCustom     = 1 // 自定义排序
	FlistSortForumLevel = 2 // 吧等级排序
	FlistSortForumTop   = 3 // 置顶排序
	FlistSortForumTopSortScore = 4 // 置顶-自由排序-等级 顺序排序
	LikeForumPageSize = 200 // 我关注的吧列表长度

	HotSearchKey             = "tb_hot_search_fids_by_dt"
	BtxHotSearchCall         = 16
	HotSearchForumMultiKey   = "hot_search_forum_multi_key"
	HotSearchTitlePrefix     = "今日热吧No.%d %s吧"
	HotSearchDisplayNum      = 10
	MultiGetHotNumNum        = 20
	HotNumMultiKey           = "hot_num_multi_key"
	TBWordlistRedisVoiceroom = "tb_wordlist_redis_voiceroom"
)

// 外露展示数量相关
const (
	// WordlistForumLikeName 进吧tab业务使用的词表名
	WordlistForumLikeName = "tb_wordlist_redis_forum_enter"

	// FoldDisplayNumKey 外露展示数量配置key
	FoldDisplayNumKey = "forum_guide_fold_display_num"

	// DefaultFoldDisplayNum 默认的外露展示数量
	DefaultFoldDisplayNum = 6

	MaxTopForumNumKey     = "max_top_like_forum_num" // 置顶吧最大数量
	DefaultMaxTopForumNum = 60                       // 默认置顶吧最大数量

	LikeForumTabCallForm = 3 // call_form入参，3表示来自关注的吧页面

	ForumSquareIconListKey = "forum_square_icon_list"
)

type ForumGuideStaticField struct {
	UserID    int64
	Cuid      string
	SampleIDs []string

	IntClientType    int
	StrClientVersion string

	ForumCreateInfo *client.ForumCreateInfo // 获取私有化吧创建信息

	ArrHotSearch []*clientForumGuide.HotSearch // 获取热搜数据

	ArrLikeForumTmp []*perm.UserOutput // 获取关注吧数据

	ArrLikeForumList []*clientForumGuide.LikeForum // 获取关注吧数据

	ArrForumHotNumList map[int64]*common.MgetHotByForumId // 获取吧热度值数据

	ArrForumSignInfo map[int64]int32 // 吧签到信息

	ArrForumBaseInfoList map[int64]*forum.BtxInfo // 获取吧基础信息数据
	BtxForumIdx          map[int64]*forum.BtxInfo // 获取吧基础信息数据

	VoiceRoomList []*client.ThreadInfo
	SortType      int

	// 12.56.5 进吧tab重构相关字段
	// IsRefactor 是否是重构版本
	IsRefactor bool

	// FoldDisplayNum 12.56.5进吧tab重构，折叠状态时，外露展示的数量
	FoldDisplayNum int

	// BoolLikeForumHasMore 关注的吧是否存在更多
	BoolLikeForumHasMore bool

	ManagerForumIDs         []int64                            // 吧主身份的吧
	AssistForumIDs          []int64                            // 小吧主身份的吧
	ArrTopLikeForumTmp      []*perm.UserOutput                 // perm返回的置顶吧
	ArrTopLikeForumList     []*clientForumGuide.LikeForum      // 置顶的吧信息
	ArrTopForumHotNumList   map[int64]*common.MgetHotByForumId // 置顶吧热度值数据
	ArrTopForumSignInfo     map[int64]int32                    // 置顶吧签到信息
	ArrTopForumBaseInfoList map[int64]*forum.BtxInfo           // 置顶吧基础信息数据
	BtxTopForumIdx          map[int64]*forum.BtxInfo           // 置顶吧基础信息数据

}
