package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	forumProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	postProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	gfamProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbumMore"
)

type GetForumAlbumMoreBaseData struct {
	Request     *gfamProto.GetForumAlbumMoreReq
	BaseObj     *uiclient.UIBaseAction
	StaticField *GetForumAlbumMoreStaticField
}

type GetForumAlbumMoreStaticField struct {
	UserID     int64 // 用户ID
	ClientType int   // 客户端类型

	// 处理过的请求参数
	ThreadIDs      [][]uint64        // 待查询的贴ID，已去重，并按照设定的数量进行分组
	ForumIDs       [][]uint32        // 待查询的吧ID，已去重，并按照设定的数量进行分组
	ThreadForumMap map[uint64]uint32 // 贴ID、吧ID映射关系，key=tid，value=fid

	// 中间变量
	RawTheadInfo          map[uint64]*postProto.ThreadInfo // mgetThread的原始返回值，key=tid，value=thread_info
	RawForumInfo          map[uint32]*forumProto.BtxInfo   // mgetBtxInfoEx的原始返回值，key=fid，value=forum_info
	MaskThreadID          map[uint64]struct{}              // 需要屏蔽的贴ID列表，key=tid
	ForumTopVerticalField map[uint32]string                // 吧所在的topN垂类，key=fid，value=所在垂类名称
	ForumEnterNum         map[uint32]int64                 // 吧昨日的进吧量，key=fid，value=昨日进吧量
	ForumHotNum           map[uint32]int64                 // 吧的热度值，key=fid，value=热度值
}

func (sf *GetForumAlbumMoreStaticField) Init() {
	sf.ThreadForumMap = make(map[uint64]uint32)
	sf.RawTheadInfo = make(map[uint64]*postProto.ThreadInfo)
	sf.RawForumInfo = make(map[uint32]*forumProto.BtxInfo)
	sf.MaskThreadID = make(map[uint64]struct{})
	sf.ForumTopVerticalField = make(map[uint32]string)
	sf.ForumEnterNum = make(map[uint32]int64)
	sf.ForumHotNum = make(map[uint32]int64)
}
