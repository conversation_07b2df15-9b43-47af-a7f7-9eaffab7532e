package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/ala"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/userstate"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
)

type CHistoryForumBaseData struct {
	Request *getHistoryForum.GetHistoryForumReq
	BaseObj *uiclient.UIBaseAction

	StaticField *HistoryForumStaticField
}

const (
	LIVE_FORUM_ID = 25962739
)

type HistoryForumStaticField struct {
	ArrHistoryInfo      []map[string]interface{}
	ArrHistoryForumInfo []*client.HistoryForumInfo
	StrClientVersion    string
	IntClientType       int
	ForumIds            []uint32
	ForumInfos          interface{}
	UserWeekForums      map[int64]bool
	UserWeekForumInfos  []*client.HistoryForumInfo
	IntUserId           int64
	BoolLogin           bool
	UserData            []*user.UserInfo
	PermData            map[int64]*perm.PermOut
	BlockData           map[int64]*userstate.QueryBlockAndAppealInfo
	UserState           map[int64]*userstate.QueryUserStates
	BolIsCallForum      bool

	LiveForum []*common.GetLiveForum
	HotThread map[string][]*common.GetHotThread
	HotNum    map[int64]*common.MgetHotByForumId
	ForumInfo map[uint32]*forum.BtxInfo
	// ForumInfo        map[uint32]*forum.MgetBtxInfo
	ForumMember      map[uint32]*perm.ForumOutput
	LiveBarRecom     *ala.GetLiveBarRecommend
	LiveForumInfo    *forum.GetBtxInfoRes
	ForbiddenInfo    map[string]uint32
	ForumLevel       map[uint32]uint32
	ForumIsLikeForum map[uint32]uint32
	ForumTag         map[int64]*common.MqueryTagInfo
	OfficialForum    map[uint32]struct{}
}

func NewHistoryForumStaticField() *HistoryForumStaticField {
	return &HistoryForumStaticField{
		ArrHistoryForumInfo: make([]*client.HistoryForumInfo, 0),
		UserWeekForumInfos:  make([]*client.HistoryForumInfo, 0),
		PermData:            make(map[int64]*perm.PermOut),
		BlockData:           make(map[int64]*userstate.QueryBlockAndAppealInfo),
		UserState:           make(map[int64]*userstate.QueryUserStates),
	}
}
