package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/agree"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/celebrity"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	"icode.baidu.com/baidu/tieba-server-go/golib2/exgraph"
)

type CPicpageBaseData struct {
	Request *picpage.PicpageReqIdl
	BaseObj *uiclient.UIBaseAction

	StaticField *PicpageStaticField
	// dataSec 数据修改通道
	DataSec exgraph.DataSec
}

const (
	TagNameURL = "http://imgsa.baidu.com/forum/pic/item/58ee3d6d55fbb2fb9bb53ba2474a20a44723dce0.jpg"
	TagNameWh  = "70,40"

	HasAgree       = 1 // 点过顶或踩
	NotAgree       = 0 // 没点过顶或踩
	AgreeTypeAgree = 2 // 点顶
	AgreeTypeCai   = 5 // 点踩

	ReplyPrivateOnlyFans    = 5 // 只有粉丝才能评论我的贴子
	ReplyPrivateOnlyConcern = 6 // 只有我关注的人才能评论我的贴子
	ReplyPrivateOnlyMe      = 7 // 只有主贴作者自己可评论
	// sample_idredis缓存前缀

	ShowAdsense = 1

	RedisAdTaskPrefix = "ad_free_end_time_"
)

type PicpageStaticField struct {
	ScreenInfo       map[string]int32
	IsYYLive         bool
	IntClientType    int
	StrClientVersion string
	ArrPackageInfo   interface{}
	// 所有需要查询表情包的表情
	ArrMemes interface{}
	// 所有在cache中的表情包
	ArrPackageInCache interface{}
	// mandatory inputs
	StrForumName     string
	FilterFirstFloor int64
	IntTid           int64
	StrPicID         string
	PostID           int64
	CurrentPostID    int64
	IntNext          int
	IntPrev          int
	IntNotSeeLz      int
	IntIsNote        int
	AladdinSrcID     int
	IntIsAiPic       bool

	// optional inputs
	IntScreenWidth  int32
	IntScreenHeight int32
	IntScreenDIP    float64
	IntQType        int32

	// 配置
	IntIsImgCDN int

	// other parameters
	IntPicStrategyType int
	// 客户端增加楼层数,返回本图信息
	BigPic bool

	// variables
	ArrRecomLive  []*picpage.AlaLiveInfo
	ArrThreadInfo *post.ThreadInfo
	ArrForumInfo  *picpage.PicForumInfo

	AdsenseForumInfo *forum.GetBtxInfoByNameRes
	AdsenseUserInfo  []*user.UserInfo

	IntFid             int32
	IntNumOfPics       int32
	ArrPicList         []*picpage.PicList
	IntNumOfLzPics     int
	ArrAlbumList       []*client.AlbumElement
	ArrPicAlbum        []*picpage.PicList
	ArrCreateUserID    []int64
	UserID             int64
	Cuid               string
	NetType            int
	FidFromPicID       uint64 // 通过pic id解析出来的
	StrPicfrom         string
	ArrPostInfo        []*post.Post
	FormatPostInfo     map[uint64]*post.Post
	StrObjType         string // 标示从哪个页面进入大图页 首页:"index" frs:"frs" pb:"pb" 其他："other"
	IsTopAgree         int    // 是否是神评 1:是 2:否
	Source             int    // 是否是用户点击请求 1:是 2:否-滑动请求
	ArrPostUserAgree   map[int64]*agree.AgreeMap
	ArrThreadUserAgree []*agree.DataList
	BjhThreadtype      string
	IsBjh              int

	PicIndex        int
	NeedAd          bool
	NeedRecLive     bool
	NeedRecAuthor   bool
	ArrPostid       []int64
	ArrTpoint       []int64
	ArrPicid2Postid map[string]int64
	IntFirstPostID  uint64
	ArrCommentNum   map[int64]uint32 // 储存postid->commentNum的对应关系

	// 长图统计需求，1为有长图，0为无长图
	IntPicType int

	// 透传cuid给手百广告
	StrShoubaiCuid    string
	StrShoubaiCuidAnd string

	ArrAnti          *client.Anti // anti信息
	ReplyPrivateFlag int          // 楼主评论权限标识位 1：可以发贴；2：需关注后发贴；3：需被楼主关注才能发贴
	AuthorInfo       *picpage.UserInfo
	ArrUserInfo      *user.UserInfo
	ArrOutThread     *picpage.ThreadInfo
	ArrPerm          *perm.PermOut
	ArrUserInfos     map[int64]*user.UserInfo
	UserAdSet        *celebrity.GetUserAdSetRes

	// ABtest 实验信息
	StrSampleID string

	// 使用小熊广告
	NeedBearAd bool
	// 是否需要展示广告
	ShowAdsense     int
	BearInBlack     bool // 命中小熊流量且命中小熊黑名单
	RecomLiveList   []*picpage.RecomLive
	InsertFloorData []*picpage.InsertFloorData

	NeedChangePicExpTime bool // 是否需要修改图片过期时间
	NeedFrsPageInfo      bool // 是否需要下发右滑跳转frs页的信息
	NeedRecomLiveList    bool // 是否需要下发推荐直播信息
}

// NewPicpageStaticField 函数返回一个 PicpageStaticField 指针
func NewPicpageStaticField() *PicpageStaticField {
	staticField := new(PicpageStaticField)
	staticField.ReplyPrivateFlag = 1
	staticField.ArrPostid = make([]int64, 0)
	staticField.ArrPicList = make([]*picpage.PicList, 0)
	staticField.ArrCreateUserID = make([]int64, 0)
	staticField.ArrPicid2Postid = make(map[string]int64)
	staticField.ArrTpoint = make([]int64, 0)
	staticField.FormatPostInfo = make(map[uint64]*post.Post)
	staticField.ArrCommentNum = make(map[int64]uint32)
	staticField.ArrAlbumList = make([]*client.AlbumElement, 0)
	staticField.ArrRecomLive = make([]*picpage.AlaLiveInfo, 0)
	staticField.RecomLiveList = make([]*picpage.RecomLive, 0)
	staticField.InsertFloorData = make([]*picpage.InsertFloorData, 0)
	staticField.ScreenInfo = map[string]int32{
		"width":  1024,
		"height": 768,
	}

	return staticField
}
