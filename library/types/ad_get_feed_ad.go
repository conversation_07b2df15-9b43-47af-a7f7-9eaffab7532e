package types

import (
	"sync"

	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/cmap"
	client "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/ad/getFeedAd"
)

type FeedAdBaseData struct {
	Request     *getFeedAd.DataReq
	BaseObj     *uiclient.UIBaseAction
	StaticField *FeedAdStaticField
	TmpData     cmap.ConcurrentMap
}

type FeedAdStaticField struct {
	UserID        int64 // 当前登录用户uid
	Cuid          string
	ClientType    int
	ClientVersion string
	OsVersion     int
	IntIP         uint32
	IntNetType    int
	CuidGalaxy2   string
	Model         string
	BaidUID       string
	Caid          string
	AndroidID     string
	UserAgent     string
	Cmode         string
	Mac           string
	ScreenWidth   int
	ScreenHeight  int
	ScreenDip     float64
	Idfa          string
	ShouBaiCuid   string
	StrImei       string
	IntImei       int
	SampleID      string
	ArrSampleIds  []string
	AdNeedParam   map[string]map[string]int
	Brand         string

	AppTransmitData *client.AppTransmitData
	AdPlace         string

	Frs           []*client.App
	Recom         []*client.App
	PbComment     []*client.App
	PbBanner      []*client.App
	FrsLock       sync.Mutex
	RecomLock     sync.Mutex
	PbCommentLock sync.Mutex
	PbBannerLock  sync.Mutex

	AdSet bool
}

func (s *FeedAdStaticField) Init() {
	s.Frs = make([]*client.App, 0)
	s.Recom = make([]*client.App, 0)
	s.PbComment = make([]*client.App, 0)
	s.PbBanner = make([]*client.App, 0)
	s.AdNeedParam = map[string]map[string]int{}
	s.AdSet = true
}

// AppendFrs 将给定的应用列表追加到 baseData 的静态字段 Frs 中
//
// 参数:
//
//	s: *FeedAdStaticField 类型的指针，包含互斥锁 FrsLock 和 Frs
//	appList: []*client.App 类型的切片，包含要追加的应用列表
func (s *FeedAdStaticField) AppendFrs(appList []*client.App) {
	s.FrsLock.Lock()
	defer s.FrsLock.Unlock()
	s.Frs = append(s.Frs, appList...)
}

// AppendRecom 将给定的应用列表追加到 baseData 的静态字段 Recom 中
//
// 参数:
//
//	s: *FeedAdStaticField 类型的指针，包含互斥锁 RecomLock 和 Recom
//	appList: []*client.App 类型的切片，包含要追加的应用列表
func (s *FeedAdStaticField) AppendRecom(appList []*client.App) {
	s.RecomLock.Lock()
	defer s.RecomLock.Unlock()
	s.Recom = append(s.Recom, appList...)
}

// AppendPbComment 将给定的应用列表追加到 baseData 的静态字段 PbComment 中
//
// 参数:
//
//	s: *FeedAdStaticField 类型的指针，包含互斥锁 PbCommentLock 和 PbComment
//	appList: []*client.App 类型的切片，包含要追加的应用列表
func (s *FeedAdStaticField) AppendComment(appList []*client.App) {
	s.PbCommentLock.Lock()
	defer s.PbCommentLock.Unlock()
	s.PbComment = append(s.PbComment, appList...)
}

// AppendPbBanner 将给定的应用列表追加到 baseData 的静态字段 PbBanner 中
//
// 参数:
//
//	s: *FeedAdStaticField 类型的指针，包含互斥锁 PbBannerLock 和 PbBanner
//	appList: []*client.App 类型的切片，包含要追加的应用列表
func (s *FeedAdStaticField) AppendBanner(appList []*client.App) {
	s.PbBannerLock.Lock()
	defer s.PbBannerLock.Unlock()
	s.PbBanner = append(s.PbBanner, appList...)
}
