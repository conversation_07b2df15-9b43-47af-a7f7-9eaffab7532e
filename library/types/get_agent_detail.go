package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	p "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getAgentDetail"
)

const (
	BotSetAuditDoing = 0 // 审核中
	BotSetAuditPass  = 1 // 审核通过
	BotSetAuditDeny  = 2 // 审核失败
	BotSetInvalid    = 3 // 定时任务失效

	BotSetTypeBackground = 1 // 背景
	BotSetTypeName       = 2 // 备注
	BotSetTypeNickname   = 3 // 称呼我
	BotSetTypeSet        = 4 // 设定
	BotSetTypeCron       = 5 // 定时提醒
)

type GetAgentDetailBaseData struct {
	Request *p.GetAgentDetailReq
	BaseObj *uiclient.UIBaseAction
}

type AgentChatDetailConf struct {
	BotSourceText  string `json:"bot_source_text"`
	GameSourceText string `json:"game_source_text"`
}
