package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/question/getQuestionList"
)

type GetQuestionListBaseData struct {
	Request     *getQuestionList.GetQuestionListReq
	BaseObj     *uiclient.UIBaseAction
	StaticField GetQuestionListStaticField
}

type GetQuestionListStaticField struct {
	ClientVersion string
	ClientType    int
	UserID        uint64
	GaoKaoAuthFID int64
	GaoKaoAuthInfo *getQuestionList.User
}
