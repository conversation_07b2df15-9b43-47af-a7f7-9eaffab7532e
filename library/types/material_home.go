package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	materialProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/material"
)

// HomeBaseData 数据结构体
type HomeBaseData struct {
	Request *materialProto.HomeReq
	BaseObj *uiclient.UIBaseAction
}

// ConfData 配置数据结构体
type ConfData struct {
	ConfInfo         map[string]interface{}
	ConvertConfInfo  map[string]interface{}
	CheckInfo        map[string]interface{}
	ActivityInfo     map[string]*ActivityInfoConf
	PureModeConfInfo map[string]interface{}
	PureModeSwitch   bool
}

type ActivityInfoConf struct {
	NeedService int `json:"need_service"`
	NeedUserID  int `json:"need_user_id"`
	ModuleLevel int `json:"module_level"`
	ServiceConf struct {
		ServiceName    string `json:"serviceName"`
		Method         string `json:"method"`
		IE             string `json:"ie"`
		RalServiceName string `json:"ralServiceName"`
	} `json:"service_conf"`
	ReqInput       []string               `json:"req_input"`
	ReqInputConfig map[string]interface{} `json:"req_input_config"`
	ExtInfo        map[string]interface{} `json:"ext_info"`
}
