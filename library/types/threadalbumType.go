package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	threadalbumProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/threadalbum/getAlbumListForManager"
	threadalbumAddProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/threadalbum/getThreadListForAdd"
)

// ThreadalbumForAddBaseData 数据结构体
type ThreadalbumForAddBaseData struct {
	Request *threadalbumAddProto.GetThreadListForAddrReq
	BaseObj *uiclient.UIBaseAction
}

// ThreadalbumBaseData 数据结构体
type ThreadalbumBaseData struct {
	Request *threadalbumProto.GetAlbumListForManagerReq
	BaseObj *uiclient.UIBaseAction
}
