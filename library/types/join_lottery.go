package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/activity/joinLottery"
)

type JoinLotteryBaseData struct {
	Request *joinLottery.JoinLotteryReq
	BaseObj *uiclient.UIBaseAction
}

// GameTaskFinishData 游戏侧finish接口
type GameTaskFinishData struct {
	Errno  int32  `json:"errno"`
	Errmsg string `json:"errmsg"`
	Tipmsg string `json:"tipmsg"`
	Logid  string `json:"logid"`
	Cost   string `json:"cost"`
	Dip    string `json:"dip"`
	Data   struct {
		CashSum    int32  `json:"cash_sum"`
		ID         int32  `json:"id"`
		Kind       int32  `json:"kind"`
		Type       int32  `json:"type"`
		OriginType int32  `json:"origin_type"`
		Pos        int32  `json:"pos"`
		Name       string `json:"name"`
		Data       struct {
			Title       string `json:"title"`
			TitleColor  string `json:"title_color"`
			Desc        string `json:"desc"`
			DescColor   string `json:"desc_color"`
			ImgURL      string `json:"img_url"`
			ImgURLRoll  string `json:"img_url_roll"`
			ImgURLTitle string `json:"img_url_title"`
			Blocks      []struct {
				Img string `json:"img"`
			} `json:"blocks"`
			LimitTimesLeft int32       `json:"limit_times_left"`
			TodaySumTimes  int32       `json:"today_sum_times"`
			TodayRestTimes int32       `json:"today_rest_times"`
			HitIdx         int32       `json:"hit_idx"`
			EndTime        interface{} `json:"end_time"`
			StartTime      int32       `json:"start_time"`
		} `json:"data"`
		Status    int32 `json:"status"`
		StatusUID int32 `json:"status_uid"`
		DbuID     int32 `json:"dbu_id"`
		More      []struct {
			CashSum    int32  `json:"cash_sum"`
			ID         int32  `json:"id"`
			Kind       int32  `json:"kind"`
			Type       int32  `json:"type"`
			OriginType int32  `json:"origin_type"`
			Pos        int32  `json:"pos"`
			Name       string `json:"name"`
			Data       struct {
				Title           string      `json:"title"`
				TitleColor      string      `json:"title_color"`
				Desc            string      `json:"desc"`
				Tips            string      `json:"tips"`
				DescColor       string      `json:"desc_color"`
				TitlePopup      string      `json:"title_popup"`
				DescPopup       string      `json:"desc_popup"`
				ImgURL          string      `json:"img_url"`
				PopupImgURL     string      `json:"popup_img_url"`
				ValidStartTime  string      `json:"valid_start_time"`
				ValidEndTime    string      `json:"valid_end_time"`
				Number          int32       `json:"number"`
				CreateTime      int32       `json:"create_time"`
				VoucherMoney    string      `json:"voucher_money"`
				ProductIndex    int32       `json:"product_index"`
				LowMoney        int32       `json:"low_money"`
				DiscountPercent int32       `json:"discount_percent"`
				VoucherType     int32       `json:"voucher_type"`
				ValidDate       string      `json:"valid_date"`
				TimesMore       int32       `json:"times_more"`
				VipEndTime      int32       `json:"vip_end_time"`
				RewardSubType   int32       `json:"reward_sub_type"`
				BlessTipList    interface{} `json:"bless_tip_list"`
				BlessInfo       struct {
					BlessTitle    string      `json:"bless_title"`
					BlessImg      string      `json:"bless_img"`
					BlessDescList interface{} `json:"bless_desc_list"`
				} `json:"bless_info"`
				Amount      int32  `json:"amount"`
				Value       string `json:"value"`
				ShowPercent string `json:"show_percent"`
				Subtype     int32  `json:"subtype"`
			} `json:"data"`
			Status      int32       `json:"status"`
			StatusUID   int32       `json:"status_uid"`
			DbuID       int32       `json:"dbu_id"`
			More        interface{} `json:"more"`
			Amount      int32       `json:"amount"`
			SpecialType int32       `json:"special_type"`
			RefreshType int32       `json:"refresh_type"`
			AwardNum    int32       `json:"AwardNum"`
		} `json:"more"`
		Amount      int32 `json:"amount"`
		SpecialType int32 `json:"special_type"`
		RefreshType int32 `json:"refresh_type"`
		AwardNum    int32 `json:"AwardNum"`
	} `json:"data"`
}

type GameTaskOpenRes struct {
	Errno  int32            `json:"errno"`
	Errmsg string           `json:"errmsg"`
	Data   GameTaskOpenData `json:"data"`
}

type GameTaskOpenData struct {
	RewardList []GameTaskOpenRewardList `json:"reward_list"`
}

type GameTaskOpenRewardList struct {
	Value     string `json:"value"`
	ImgURL    string `json:"img_url"`
	Name      string `json:"name"`
	RewardID  int64  `json:"reward_id"`
	TaskID    int64  `json:"task_id"`
	RewardNum int64  `json:"reward_num"`
}
