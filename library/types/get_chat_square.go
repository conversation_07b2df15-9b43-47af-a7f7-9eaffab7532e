package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getChatSquare"
)

type GetChatSquareBaseData struct {
	Request  *getChatSquare.GetChatSquareReq
	BaseObj  *uiclient.UIBaseAction
	UserID   uint64          // 当前登录用户uid
	Pn       uint32          // 当前页码
	Rn       uint32          // 每页条数
	RoleType string          // 查询角色类型
	BotUIDs  []int64         // 机器人uid列表
	TopicMap map[int64]int64 // uid->topic_id
}
