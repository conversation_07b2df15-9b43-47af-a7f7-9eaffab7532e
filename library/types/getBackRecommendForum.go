package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/excellent/getBackRecommendForum"
)

type GetBackRecommendForumBaseData struct {
	Request *getBackRecommendForum.GetBackRecommendForumReq
	BaseObj *uiclient.UIBaseAction
}

// WLUbsAbtestConfigTab abtest config
const WLUbsAbtestConfigTab = "tb_wordlist_redis_ubs_abtest_config"

// PreKeySampleIdCache abtest redis prefix
const PreKeySampleIdCache = "user_sid_"
