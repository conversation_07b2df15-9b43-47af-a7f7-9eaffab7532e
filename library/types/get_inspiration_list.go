package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/inspiration/getInspirationList"
)

type GetInspirationListBaseData struct {
	Request       *getInspirationList.GetInspirationListReq
	BaseObj       *uiclient.UIBaseAction
	UserID        uint64 // 当前登录用户uid
	ForumID       int64  // 吧id
	Pn            uint32 // 当前页码
	Rn            uint32 // 每页条数
	ClientType    int
	ClientVersion string
}
