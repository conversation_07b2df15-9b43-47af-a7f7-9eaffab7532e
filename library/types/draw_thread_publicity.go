package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	dtProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/drawthread"
)

const DrawThreadRedisKey = "tb_wordlist_redis_thread_draw_config"

// PublicityBaseData 数据结构体
type DrawThreadPublicityBaseData struct {
	Request *dtProto.PublicityReq
	BaseObj *uiclient.UIBaseAction
}
