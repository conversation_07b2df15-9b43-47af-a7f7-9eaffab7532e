package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/bawu/getDelThreadInfo"
)

const (
	BawuCallFrom    = "bawu"
	PmcCallFrom     = "pmc"
	QueryTypeThread = 1 // 帖子
	QueryTypePost   = 2 // 回复
	QueryTypeLzl    = 3 // 楼中楼
)

type GetDelThreadInfoBaseData struct {
	Request *getDelThreadInfo.GetDelThreadInfoReq
	BaseObj *uiclient.UIBaseAction

	UserID  uint64 // 当前登录用户uid
	ForumID int64  // 查询的吧id

	AuthorID      uint64           // 帖子或者回复的作者uid
	AuthorAddress string           // 帖子或者回复的作者ip地址
	ThreadInfo    *post.ThreadInfo // mgetThread 返回的帖子信息
	PostInfo      *post.Post       // getPostInfo 返回的回复信息

	OutUserData        *client.User                      // 返回的用户信息
	OutPostData        *client.Post                      // 返回的回复信息
	OutSubPostData     *client.SubPostList               // 返回的楼中楼信息
	OutThreadData      *client.ThreadInfo                // 返回的帖子信息
	OutDrawThreadData  *getDelThreadInfo.DrawThreadInfo  // 返回的抽奖贴信息
	OutScoreThreadData *getDelThreadInfo.ThreadScoreInfo // 返回的打分贴信息
}

type ThreadDrawWordlistConfig struct {
	MarqueeIntroBefore string // 前面展示的文案，开奖前
	MarqueeIntroAfter  string // 前面展示的文案，开奖后
	MarqueeSuffix      string // 昵称后面拼接的文案

	CountdownIntroBefore1 string // 前面展示的文案，开奖前，未参与
	CountdownIntroBefore2 string // 前面展示的文案，开奖前，已参与
	CountdownIntroDuring  string // 前面展示的文案，开奖中
	CountdownIntroAfter   string // 前面展示的文案，开奖后
	CountdownIntroFinish  string // 前面展示的文案，抽奖结束

	CountdownBtnText string // 按钮展示的文案，开奖后
	CountdownBtnURL  string // 按钮的跳转链接，开奖后
}
