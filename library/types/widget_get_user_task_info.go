package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	userProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	getUserTaskInfoProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/widget/getUserTaskInfo"
)

// GetUserTaskInfoBaseData 数据结构体
type GetUserTaskInfoBaseData struct {
	Request *getUserTaskInfoProto.GetUserTaskInfoReq
	BaseObj *uiclient.UIBaseAction

	UserGrowthScore  int32   // 用户成长值分数
	UserTmoneyRemain float32 // 用户贴贝余额
	// UserPageSignInfo *commonProto.PageSignProgressData // 用户连续签到任务的签到状态
	UserDataInfo   *userProto.GetUserDataRes     // 用户基础信息
	UserTaskList   []*commonProto.UserGrowthTask // 用户未完成任务
	UserPushSwitch int                           // 用户端内官方推送开关，开启时值为1
}

type SortTask struct {
	Task      *commonProto.UserGrowthTask
	SortValue int
}

type TaskExt struct {
	WidgetExt TaskWidgetExt `json:"widget_ext,omitempty"`
	Scores    map[int]int   `json:"scores,omitempty"`
}
type TaskWidgetExt struct {
	WidgetShowName string `json:"widget_show_name,omitempty"`
	WidgetShowSort int    `json:"widget_show_sort,omitempty"`
}
