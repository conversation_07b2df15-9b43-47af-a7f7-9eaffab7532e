package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/bawu/getDeleteReason"
)

type GetDeleteReasonBaseData struct {
	Request *getDeleteReason.GetDeleteReasonReq
	BaseObj *uiclient.UIBaseAction

	UserID        uint64 // 当前登录用户uid
	ForumID       uint64 // 查询的吧id
	ClientVersion string
}
