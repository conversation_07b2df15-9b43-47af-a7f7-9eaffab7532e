package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/dialog/showDialog"
)

type ShowDialogBaseData struct {
	Request *showDialog.ShowDialogReq
	BaseObj *uiclient.UIBaseAction
}

type DialogConf struct {
	Type            int32       `json:"type"`
	BackgroundImage string      `json:"background_image"`
	ButtonText      string      `json:"button_text"`
	URL             string      `json:"url"`
	ForumName       string      `json:"forum_name"`
	ThreadID        int64       `json:"thread_id"`
	RoomID          int64       `json:"room_id"`
	Schema          string      `json:"schema"`
	ExtConf         ExtConfData `json:"ext_conf"`
	Advertiser      string      `json:"advertiser"`
}

type ExtConfData struct {
	SchemeAppURL   string       `json:"scheme_app_url"`
	SchemeH5URL    string       `json:"scheme_h5_url"`
	PvUpload       PvUploadData `json:"pv_upload"`
	PvTrackData    []string     `json:"pv_track_data"`
	ClickTrackData []string     `json:"click_track_data"`
}

func (e ExtConfData) Empty() bool {
	return e.SchemeAppURL == "" && e.SchemeH5URL == "" && e.PvUpload.Empty() && len(e.PvTrackData) == 0 && len(e.ClickTrackData) == 0
}

func (p PvUploadData) Empty() bool {
	return p.Token == "" && p.UniqIDDaily == "" && p.FrequencyRock == ""
}

type PvUploadData struct {
	Token         string `json:"token"`
	UniqID        string `json:"uniqId"`
	UniqIDDaily   string `json:"uniqId_daily"`
	FrequencyRock string `json:"frequency_rock"`
}

type CaidData struct {
	Caid        []CaidInfo `json:"caid"`
	FactorsData string     `json:"factors_data"`
	CaidValid   string     `json:"caid_valid"`
}

type CaidInfo struct {
	Vendor       string       `json:"vendor"`
	GenerateTime string       `json:"generate_time"`
	Caid         []CaidDetail `json:"caid"`
}
type CaidDetail struct {
	Version string `json:"version"`
	Caid    string `json:"caid"`
}

type FactorsInfo struct {
	FactorsVersion string             `json:"factorsVersion"`
	FactorsForCaid FactorsForCaidInfo `json:"factorsForCaid"`
}

type FactorsForCaidInfo struct {
	BootSecTime string `json:"bootSecTime"`
	SysFileTime string `json:"sysFileTime"`
}

type PageSignPopConfig struct {
	Title           string
	Subtitle        string
	ButtonText      string
	ButtonText2     string
	ButtonText3     string
	ExtendAwardInfo string
}

type CommercialUpdateInfo struct {
	BeforeAwardInfo UpdateAwardInfo `json:"before_award_info"`
	AfterAwardInfo  UpdateAwardInfo `json:"after_award_info"`
}

type UpdateAwardInfo struct {
	Icon string `json:"icon"`
	Num  int    `json:"num"`
	Text string `json:"text"`
}

const (
	DialogTypeCommon           = 1
	DialogTypePageSignPop      = 2 // 12.78 签到任务弹窗
	DialogTypePageSignAward    = 3 // 12.78 签到激励弹窗
	DialogTypePageAppUninstall = 4 // 12.88 卸载挽留弹窗

	DialogConfSuffix = "_conf_info"
	DialogConfTable  = "tb_wordlist_redis_dialog_show_conf"

	TiebaDialogSecurityKey = "Tieba_Dialog_pv_key_$^Tk20240618"
	CaidVenterReyun        = "0"
	CaidVenterXTY          = "1"
	CaidVenterAli          = "2"

	// UsergrowthTaskIdentifyKey 签到任务身份redis key
	UsergrowthTaskIdentifyKey = "usergrowth_task_identify_%d"

	UsergrowthTaskIdentifyNewUser        = 1 // 新用户
	UsergrowthTaskIdentifyExternal       = 2 // 调起用户
	UsergrowthTaskIdentifyCommercialPush = 3 // 商业化Push用户

	PageSignPopTokenSalt = "tieba_task_page_sign_pX6CorNP_B&)Q27sHn"
	// app卸载弹窗 salt
	AppUninstallPopTokenSalt = "tieba_app_uninstall_A89FoRbb_T.&>eqs3n"

	StatisticsURLParamNotReplaceOff       = "off"
	StatisticsURLParamNotReplaceAll       = "all"
	StatisticsURLParamNotReplaceWhitelist = "whitelist"
)

// SubappMap 过审版本key
var SubappMap = map[string]int{
	"tieba":  6,
	"mini":   5,
	"wangsu": 4,
}

type PageSignAwardDialogConf struct {
	Title string `json:"title"`
	Pic   string `json:"pic"`
	Type  int32  `json:"type"`
}

type DialogTraceURLParamReplaceConf struct {
	Mode         string   `json:"mode"`         // "off" / "whitelist" / "all"
	Whitelist    []string `json:"whitelist"`    // CUID 白名单，仅在 mode = "whitelist" 时生效
	ReplaceULink bool     `json:"replaceUlink"` //是否替换ulink，默认不替换..
}
