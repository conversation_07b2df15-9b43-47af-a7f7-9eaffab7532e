package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/question/guide"
)

type CGuideBaseData struct {
	Request *guide.GuideReq
	BaseObj *uiclient.UIBaseAction

	StaticField *GuideStaticField
}

type GuideStaticField struct{}

func NewGuideStaticField() *GuideStaticField {
	return &GuideStaticField{}
}
