package types

import (
	"sync"

	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	forumproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/yuelao2"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbum"
)

const (
	ForumAlbumConfTableName = "tb_wordlist_redis_forum_enter" // 进吧页吧单conf词表名
	RecomAlbumType          = 1                               // 推荐吧单
	HotAlbumType            = 2                               // 新热吧单
)

type GetForumAlbumBaseData struct {
	Request     *getForumAlbum.GetForumAlbumReq
	BaseObj     *uiclient.UIBaseAction
	StaticField *GetForumAlbumStaticField
}

type GetForumAlbumStaticField struct {
	Pn        int64  // 页码
	SessionID string // 推荐侧去重使用

	UserID           uint64 // 当前登录用户uid，如果为0则表示未登录
	IntClientType    int    // 客户端类型
	StrClientVersion string // 客户端版本
	Cuid             string //cuid

	HotForumAlbumList   []*common.HotForumAlbumInfo         // 新热吧单数据，service接口返回的原始数据
	RecomForumAlbumList []*yuelao2.RecommForumAlbumInfo     // 推荐返回的吧单数据
	OpCardMeta          []*common.OpCardInfo                // 运营卡片数据
	DataLock            *sync.RWMutex                       // 读写数据的锁
	ForumIDList         []int64                             // 需要获取吧信息的吧id列表
	ThreadIDList        []int64                             // 需要获取帖子信息的帖子id列表
	ForumAlbumIDList    []int64                             // 吧单id列表
	NextTheadIDMap      map[int64]int64                     // 记录推荐吧中第一个tid的下一个tid
	TID2AlbumIDMap      map[int64]int64                     // 帖子id -> 吧单id，简化版使用
	AlbumID2TidsMap     map[int64][]*getForumAlbum.TidFidSt // 吧单id -> 帖子id列表，简化版使用
	FidInfList          [][]interface{}                     // 分片后的吧id列表
	TidInfList          [][]interface{}                     // 分片后的帖子id列表

	ForumMetaInfoMap            map[int64]*forumproto.BtxInfo // mgetForumBtxInfo接口的返回值
	ThreadMetaInfoMap           map[int64]*post.ThreadInfo    // mgetForumThread接口的返回值
	ForumHotValueMap            map[int64]int64               // 吧热度
	ForumEnterNumMap            map[int64]int64               // 吧的进入次数
	UserLikeForumMap            map[int64]uint32              // 用户关注吧关系
	UserVerticalField           []string                      // 用户所属垂类
	AlbumShowInfoMap            map[int64]int64               // 吧单的曝光数量
	ForumIsTop5Map              map[int64]string              // fid -> top5垂类
	ExpandGuideWordHasMore      string                        // 展开吧单的引导语（还有更多吧时）
	ExpandGuideWordNoMore       string                        // 展开吧单的引导语（没有更多吧时）
	RecommendForumAlbumIcon     string                        // 推荐兴趣吧单的图标
	RecommendForumAlbumStyleExp string                        // 推荐吧样式实验
	RecommendFourmExpWhiteList  []string                      // 推荐吧样式实验白名单
	HotForumAlbumIcon           string                        // 新门吧单的图标
	ForumEnterNumThreshold      int64                         // 词表配置的进吧次数阈值
	ForumEntryIconForeDay       string                        // 进吧运营位图片，前面大图(明)
	ForumEntryIconForeNight     string                        // 进吧运营位图片，前面大图(暗)

	ResHotForumAlbumList         map[int64]*getForumAlbum.ForumAlbumInfo // 返回的新的热吧单数据
	ResRecomForumAlbumList       []*getForumAlbum.ForumAlbumInfo         // 返回的推荐吧单数据
	ShowStyle                    string                                  // 是否命中新版样式
	TopicNeedPaddingThreadNumMap map[int64]int                           // 需要补充的帖子数量
	ThreadNum                    int                                     // 需要展示的帖子数量
}

func (a *GetForumAlbumStaticField) InitStatic() {
	a.DataLock = &sync.RWMutex{}
	a.ForumMetaInfoMap = make(map[int64]*forumproto.BtxInfo)
	a.UserLikeForumMap = make(map[int64]uint32)
	a.NextTheadIDMap = make(map[int64]int64)
	a.ThreadMetaInfoMap = make(map[int64]*post.ThreadInfo)
	a.ForumHotValueMap = make(map[int64]int64)
	a.ForumEnterNumMap = make(map[int64]int64)
	a.AlbumShowInfoMap = make(map[int64]int64)
	a.ForumIsTop5Map = make(map[int64]string)
	a.ResHotForumAlbumList = make(map[int64]*getForumAlbum.ForumAlbumInfo)
	a.TID2AlbumIDMap = make(map[int64]int64)
	a.AlbumID2TidsMap = make(map[int64][]*getForumAlbum.TidFidSt)
}

type HotForumAlbumSortSt struct {
	AlbumInfo     *common.HotForumAlbumInfo
	VerticalField string
	HasSorted     bool
	Next          *HotForumAlbumSortSt
}
