package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/video/cardInfo"
)

type CCardInfoBaseData struct {
	Request *cardInfo.VideoCardInfoReq
	BaseObj *uiclient.UIBaseAction

	StaticField *CardInfoStaticField
}

type CardInfoStaticField struct {
}

func NewCardInfoStaticField() *CardInfoStaticField {
	return &CardInfoStaticField{}
}
