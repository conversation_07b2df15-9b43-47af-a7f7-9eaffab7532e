package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	commonproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/gaokao/warmup"
)

type GaokaoWarmupData struct {
	Request     *warmup.WarmupReq
	BaseObj     *uiclient.UIBaseAction
	StaticField *GaokaoWarmupStaticField
}

type GaokaoWarmupStaticField struct {
	UserID        uint64 // 当前登录用户uid
	ClientType    int
	ClientVersion string
	CallFrom      string // 入参中的call_from
	ForumName     string // 入参中的forum_name
	ForumID       int64  // 吧ID

	UserPortrait          string                   // 用户portrait
	UserShowName          string                   // 用户昵称
	UserShowText          string                   // 用户区-用户展示文案
	UserInfoBtn           string                   // 用户区-认证通过后按钮
	UserInfoBtnLink       string                   // 用户区-认证通过后按钮跳转链接
	UserAuthInfo          *commonproto.StuAuthInfo // 用户认证信息
	AIanswerRule          *warmup.WarmupRule       // 规则区-ai问答部分的数据
	ForumManagerRecomRule *warmup.WarmupRule       // 规则区-吧主推荐部分的数据
	OfficialAuditRule     *warmup.WarmupRule       // 规则区-官方推荐部分的数据
	HeadCardInfo          *warmup.WarmupHead       // 头图区
	AwardInfo             *warmup.WarmupAward      // 奖励区
	ConfigInfo            *warmup.WarmupConfig     // config区
}
