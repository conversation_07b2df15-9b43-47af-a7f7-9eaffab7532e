package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getRecommendForumData"
)

const REC_FROM_LABEL = "entry_forum" //推荐吧服务用来标识：页面来源
const REC_FORUM_NUM = 30             //单次请求获取推荐吧条数
const REC_RAL_SUCCESS_CODE = 0       //推荐吧成功状态码
const BTX_PER_CALL = 10              //单次ral请求吧条数
const RAL_MGET_THREAD_NUM = 10       //单次ral请求thread条数
const DISPLAY_MIN_THREAD_NUM = 3     //最小帖子数
const DISPLAY_MAX_THREAD_NUM = 6     //展示最大帖子数

const REC_FORUM_MULTI_KEY = "rec_forum_multi_key"

type CRecommendForumDataBaseData struct {
	Request *getRecommendForumData.GetRecommendForumDataReq
	BaseObj *uiclient.UIBaseAction

	StaticField *RecommendForumDataStaticField
}

type RecommendForumDataStaticField struct {
	ArrRecForumIds      []uint32
	ArrRecTids          map[uint32][]int64
	ArrRecFrom          map[uint32]string
	ArrRecForumInfoList []*getRecommendForumData.RecForumInfo
	BtxForumIdx         map[int64]*forum.BtxInfo
	DataCnt             int
	SourceType          int
	UserId              int64
	Page                int
	Login               bool
	ClientType          int
	ClientVersion       string
	ThreadList          map[uint32]map[int64]*getRecommendForumData.ThreadList
}

func NewRecommendForumDataStaticField() *RecommendForumDataStaticField {
	return &RecommendForumDataStaticField{
		ArrRecForumIds:      make([]uint32, 0),
		ArrRecTids:          make(map[uint32][]int64),
		ArrRecFrom:          make(map[uint32]string),
		BtxForumIdx:         make(map[int64]*forum.BtxInfo),
		ArrRecForumInfoList: make([]*getRecommendForumData.RecForumInfo, 0),
	}
}
