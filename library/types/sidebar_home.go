package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sidebar/home"
)

const (
	SideBarConfTableName = "tb_wordlist_redis_sidebar_conf" // 词表配置表名
)

type SidebarHomeBaseData struct {
	Request     *home.SidebarHomeReq
	BaseObj     *uiclient.UIBaseAction
	StaticField *SidebarHomeStaticField
}

type SidebarHomeStaticField struct {
	UserID        uint64 // 当前登录用户uid
	ClientType    int
	ClientVersion string
	PureMode      int

	UserAttrIcon         []*client.UserAttrIcon             // 用户icon列表
	ArrUserInfo          *user.UserInfo                     //getUserDataEx 的返回值
	IsWorkcreator        int                                // 是否是视频创作者
	ArrPendant           *client.Pendant                    // 头像挂件
	TbVip                *client.TbVipInfo                  // 用户大V认证
	Ecom                 *client.Ecom                       // 贴吧电商
	ArrNewGod            *client.NewGodInfo                 // 大神
	VipBannerInfo        *home.VipBanner                    // 会员banner
	LikeForumPermRes     []*perm.UserOutput                 // perm返回的关注吧信息
	LikeForumIds         []uint64                           // perm返回的关注吧信息id
	BoolLikeForumHasMore bool                               // perm返回的关注吧信息是否还有更多
	ArrForumBaseInfoList map[int64]*forum.BtxInfo           // 获取吧基础信息数据
	ArrForumHotNumList   map[int64]*common.MgetHotByForumId // 获取吧热度值数据
	ArrForumSignInfo     map[int64]int32                    // 吧签到信息

	AllToolsMap        map[string]*SidebarToolInfo   // 所有功能列表
	ToolClassMap       map[string][]*SidebarToolInfo // 工具列表，按照class分组
	YunyingToolNames   []string                      // 运营位工具名称
	ShowToolClass      []string                      // 展示的工具类别
	CommonToolNameList []string                      // 常用工具名称列表
	NeedTipsToolsList  []*SidebarToolInfo            // 需要展示右上角额外展示信息功能list
}

func (s *SidebarHomeStaticField) Init() {
	s.ArrForumBaseInfoList = make(map[int64]*forum.BtxInfo)
	s.ArrForumHotNumList = make(map[int64]*common.MgetHotByForumId)
	s.AllToolsMap = make(map[string]*SidebarToolInfo)
	s.ToolClassMap = make(map[string][]*SidebarToolInfo)
}

type SidebarToolInfo struct {
	// 标题
	Title string `json:"title,omitempty"`
	// 优先使用scheme，scheme不可用时使用兜底
	ActionURI string `json:"action_uri,omitempty"`
	// 1，schema跳转；2， h5链接；3，小程序, 4 下载
	ActionType string `json:"action_type,omitempty"`
	// icon链接
	Icon string `json:"icon,omitempty"`
	// 功能类别
	Class string `json:"class,omitempty"`
	// 右上角额外展示信息
	Tips string `json:"tips,omitempty"`
	// 是否需要展示右上角额外展示信息
	NeedExTips int32 `json:"need_ex_tips,omitempty"`
	// 是否需要过滤审核态
	NeedFilter int32 `json:"need_filter,omitempty"`
	// icon 日间模式
	IconDay string `json:"icon_day,omitempty"`
	// icon 夜间模式
	IconNight string `json:"icon_night,omitempty"`
	// 跳转scheme
	Scheme string `json:"scheme,omitempty"`
	// 下载时的额外参数
	ExtParams string `json:"ext_params,omitempty"`
}
