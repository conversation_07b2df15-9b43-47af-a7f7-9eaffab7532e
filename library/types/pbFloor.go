package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/contentforumsearch"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/adsense"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/agree"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/pb"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/userpost"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/common"
)

type CPbFloorBaseData struct {
	Request *floor.PbFloorReqIdl
	BaseObj *uiclient.UIBaseAction

	StaticField *PbFloorStaticField
}

const (
	CupidPhotoBlackList  = 225 // 是否在图片贴吧黑名单当中
	CupidAlbumPhoto      = 27  // 判断是否开启精
	CupidPhotoPbSwitch   = 230 // 是否可以从 看图pb页切换到 图片贴吧
	CupidBanPicTopic     = 226 // 判断是否禁止开启图片话题贴
	CupidDefaultPhotoFrs = 205 // 判断是否开启图片贴吧
	CupidBanPostPic      = 203 // 根据吧判定那些吧不能发图片
	CupidForeverBanPower = 216 // 永久封禁权限
	CupidBrandZone       = 201 // 查询是否含有广告贴吧列表
	CupidRulePaper       = 287 // 查询吧是否
	FloorPageSize        = 30

	// ReplyOnlyFans    = 5 // 只有粉丝才能评论我的贴子
	// ReplyOnlyConcern = 6 // 只有我关注的人才能评论我的贴子
	// ReplyOnlyMe      = 7 // 只有主贴作者自己可评论

	// memberVip by dingfenghu
	NativeMemberCenterUrl = "membercenter:"
	// NATIVE_MEMBER_CENTER_URL = "http://tieba.baidu.com/mo/q/tbmall/recallScore?from=icon"
	MemberVipSupportUrl = "http://tieba.baidu.com/mo/q/celebrity/support?forum_id="
	MemberVipVipUrl     = "http://tieba.baidu.com/mo/q/celebrity/vip?forum_id="
	MemberVipIconUrl    = "http://imgsrc.baidu.com/forum/pic/item/0d7de31190ef76c6aaa443ad9b16fdfaae516766.jpg"
	H5MemberPkURL       = "http://tieba.baidu.com/mo/q/member/pk"                       // 会员PKUrl
	NativeMemberBuyURL  = "http://tieba.baidu.com/mo/q/member/pk?jumptoforum=memberbuy" // 会员购买

	RepostBrowserStatusNotReport   = 0
	RepostBrowserStatusMultiForum  = 1
	RepostBrowserStatusSingleForum = 2

	// HasAgree = 1 // 点过顶或踩
	// NotAgree int // 没点过顶或踩
	//
	// AgreeTypeAgree = 2 // 点顶
	// AgreeTypeCai   = 5 // 点踩

	AbtestIdPbFoldSmallFlow = 5
)

type PbFloorStaticField struct {
	IntKz   int64
	IntZ    int
	IntPid  int64
	IntSpid int64
	IntPn   int

	// config variables
	IsPositioningFloor bool
	IsParseBdhd        bool
	GenderIcon         bool

	// variables used in methods
	UserId                int64
	ForumInfo             *forum.GetBtxInfoRes
	PostInfo              *post.Thread
	Comments              *pb.Thread
	PostList              *post.Post
	SubPostList           []*client.SubPostList
	Page                  *client.Page
	Cupid                 *client.SimpleForum
	Perm                  *perm.PermOut
	UserInfos             map[int64]*user.UserInfo
	VoiceList             map[int64]*common.Voice
	ThreadStore           *userpost.QueryThreadStoreTypeOutput
	TWZhiInfo             map[int64]*client.ZhiBoInfoTW
	ThreadInfo            *post.ThreadInfo
	PostInfo2             interface{}
	Voice                 []map[string]interface{}
	UserList              []int64
	IsCommReverse         int
	IntThreadForumId      uint32 // 贴子原始forum_id
	IntReplyPrivateFlag   int
	IsRepostThread        bool
	IntRepostThreadStatus int // 转贴贴子浏览状态 1-主态 2-客态

	NovelFansInfo     map[int64]*client.NovelFansInfo
	PanPostList       map[string]map[int]map[int]int
	ArrUid            map[int64]int64
	Bawulist          *perm.BawuOut
	ArrUserUK         map[int64]string
	StrSubappType     string
	IntPbUserIconSize int
	IntPbParsePhone   int
	IntPbImgCdn       int
	PbPicStrategyType int
	SubPostAgreeData  map[int64]*agree.AgreeMap
	PostAgreeData     *agree.GetAgreeByUserIdAndPostId
	Strategy          interface{}
	IntCdnErrTime     int64

	ABTestIds interface{}

	ShowSquared  bool
	ABTestConfig interface{}

	IsBJH                   int
	ErrGetThread            int
	BJHPostInfo             interface{}
	BJHUserIds              interface{}
	BJHThreadInfo           interface{}
	RebornVersion           bool
	IntClientType           int
	StrClientVersion        string
	IntSubContentReplayType int // 回复

	// 是否是12.4创作者拉黑的白名单uid
	IsBlackWhite bool

	// 2022世界杯开关
	WorldcupSwitch bool

	// 楼中楼主态
	CommentMaskInfoInput interface{}

	// 是否包含群聊链接
	HasChatroomId    bool
	ChatroomMutilNum int // 保存群聊信息请求的次数

	// 机器人不展示吧等级
	LevelBlackList map[int64]string
	ChatroomInfo   map[int64]*chat.GetRoomsDetailDataRes

	TiebaPlusPlugFloorTokens []string
	IntSmileGrade            int
	IntVoiceShow             int
	AtUID                    []int64
	AtUname                  []string
	TPointPost               *client.TPointPost
	SkinInfo                 *client.SkinInfo
	TiebaPlusPlugInfo        map[string]*adsense.DecodePluginToken

	BawuMap              map[int64]*BawuInfo
	UserIcons            map[int64][]*client.UserAttrIcon
	UserShowInfo         map[int64]*client.UserShowInfo
	ThreadAuthorShowInfo map[int64]*client.UserShowInfo

	HitWordMap             map[string]string
	QueryMatchStatus       map[string]bool
	PbSearchConfig         *contentforumsearch.AllShowConfigDataStruct
	PbSearchThemeColorInfo *client.ThemeColorInfo
	PbSearchWords          map[string]string
	PbSearchIconInfo       string
	ShopGoodsLink          []uint64
}

func NewPbFloorStaticField() *PbFloorStaticField {
	return &PbFloorStaticField{
		IntPn:                    1,
		IntSubContentReplayType:  4,
		IntPbUserIconSize:        4,
		IntReplyPrivateFlag:      1,
		ArrUid:                   make(map[int64]int64),
		Voice:                    make([]map[string]interface{}, 0),
		VoiceList:                make(map[int64]*common.Voice),
		ChatroomInfo:             make(map[int64]*chat.GetRoomsDetailDataRes),
		SubPostList:              make([]*client.SubPostList, 0),
		UserInfos:                make(map[int64]*user.UserInfo),
		NovelFansInfo:            make(map[int64]*client.NovelFansInfo),
		TiebaPlusPlugFloorTokens: make([]string, 0),
		IntSmileGrade:            3,
		IntVoiceShow:             1,
		AtUID:                    make([]int64, 0),
		AtUname:                  make([]string, 0),
		TiebaPlusPlugInfo:        make(map[string]*adsense.DecodePluginToken),
		LevelBlackList:           make(map[int64]string),
		BawuMap:                  make(map[int64]*BawuInfo),
		PanPostList:              make(map[string]map[int]map[int]int),
	}
}

type BawuInfo struct {
	IsBawu   int32
	BawuType string
}
