package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/metadata"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/video"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/video/redIcon"
)

type CRedIconBaseData struct {
	Request *redIcon.VideoRedIconReq
	BaseObj *uiclient.UIBaseAction

	StaticField *VideoRedIconStaticField
}

type VideoRedIconStaticField struct {
	IntUID          int64
	ThreadInfo      []*metadata.ThreadInfoOutWithThreadTypes
	VideoTabRedIcon *video.GetVideoTabRedIcon
}

func NewRedIconStaticField() *VideoRedIconStaticField {
	return &VideoRedIconStaticField{}
}
