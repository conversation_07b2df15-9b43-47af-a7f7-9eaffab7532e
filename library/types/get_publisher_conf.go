package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	publisherConfProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/publisher/publisherConf"
)

// @Description 数据结构体
type PublisherConfBaseData struct {
	Request *publisherConfProto.GetPublisherConfReq
	BaseObj *uiclient.UIBaseAction
}

const (
	MachineAuditFail    = 3 // 机审名称不通过
	PersonAuditFail     = 6 // 人审不通过
	PmAuditFail         = 9 // 运营审核不通过
	MachineAuditPicFail = 8 // 抽奖贴机机审图片不通过
)
