package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/metadata"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/itemPage"
)

type CItemPageBaseData struct {
	Request *itemPage.ItemPageReq
	BaseObj *uiclient.UIBaseAction

	StaticField *ItemPageStaticField
}

type ItemPageStaticField struct {
	IntUid               int64
	StrClientVersion     string
	IntClientType        int
	IntRecomForumNum     int
	IntRecomBindForumNum int
	IntThreadNum         int
	ItemId               uint32
	/*post::getMFrs请求res_num = n时，因为过滤部分帖子，返回少于n条；
	 * 故请求_intGetMFrsResNum条，截取其中_intThreadNum条返回；
	 */
	IntGetMFrsResNum int
	SortType         string
	IntScreenWidth   int32
	IntScreenHeight  int32
	IntScreenDip     float64
	IntQType         int32
	IntMinRecomForum int
	IntMinRecomItem  int

	Link            string
	AlbumList       []*client.AlbumElement
	ItemGameInfo    *client.ItemGameInfo
	ItemInfo        *client.ItemInfo
	HasTornado      int32
	RecommendItem   []*client.ItemInfo
	ThreadList      []*metadata.ThreadInfoOutWithThreadTypes
	DiscussionList  []*metadata.ThreadInfoOutWithThreadTypes
	DiscussId       int64
	ArrItemInfo     *common.ItemInfoList
	RecommmendForum []*client.RecommendForumInfo
	HotVideo        interface{}
	UnclaimedNum    int32
	GameCodeList    []*client.GameCodeList
	Details         []*client.ItemDetail
	Services        []*client.ItemService
	Intro           []interface{}
}

func NewItemPageStaticField() *ItemPageStaticField {
	return &ItemPageStaticField{
		IntRecomForumNum:     10,
		IntRecomBindForumNum: 2,
		IntThreadNum:         3,
		IntGetMFrsResNum:     20,
		SortType:             "score",
		IntMinRecomForum:     3,
		IntMinRecomItem:      3,
		ItemGameInfo:         new(client.ItemGameInfo),
		RecommendItem:        make([]*client.ItemInfo, 0),
		ThreadList:           make([]*metadata.ThreadInfoOutWithThreadTypes, 0),
		GameCodeList:         make([]*client.GameCodeList, 0),
		Details:              make([]*client.ItemDetail, 0),
		Services:             make([]*client.ItemService, 0),
		Intro:                make([]interface{}, 0),
	}
}
