package types

import (
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getShareInfo"
)

type GetShareInfoData struct {
	Request *getShareInfo.GetShareInfoReq
	BaseObj *uiclient.UIBaseAction

	UserID   uint64 // 当前登录用户uid
	ThreadID uint64 // 帖子id
	ForumID  uint64 // 吧id
	Scene    string //agent || score
	//HasHistory int32  // 是否需要查询历史消息  0 1
	BotUK string // agent相关标识
}
