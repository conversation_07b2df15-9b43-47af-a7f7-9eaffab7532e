// Copyright(C) 2022 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2022-06-22, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package resource

import (
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/mysql"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/tieba-server-go/go-cache/cache"
)

// LoggerService 业务访问日志(access_log)：log/service/service.log
var LoggerService logit.Logger

// MySQLClientUser mysql client 单例
var MySQLClientUser mysql.Client

var RedisSign redis.Client
var CacheSign cache.Cache

var RedisUserGrowth redis.Client
var CacheUserGrowth cache.Cache

var RedisMsglogic redis.Client
var CacheMsglogic cache.Cache

var RedisAdsense redis.Client

var RedisCommonb redis.Client

var CacheTwLive cache.Cache
var RedisTwLive redis.Client

var RedisPush redis.Client
var CachePush cache.Cache

var RedisMember redis.Client
var CacheMember cache.Cache
