package pb

import (
	"context"
	"google.golang.org/protobuf/proto"
	"html"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/format"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbrichtext"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/common/meta"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
)

const (
	GetUidByUnamesCount = 30
)

type ProcessData struct {
	PicList []*picpage.PicList
	StrType string
	// todo 还有一个content类型，可以再加一个字段
}

func Process(ctx context.Context, processData *ProcessData) {

	arrUname := make([]string, 0)
	for _, arrOne := range processData.PicList {
		if processData.StrType == "desc" {
			arrUname = append(arrUname, ProcessOnePic(ctx, arrOne)...)
		}
	}

	multi := tbservice.Multi()
	for i := 0; len(arrUname) > i*GetUidByUnamesCount; i++ {
		var unames []string
		if len(arrUname) > (i+1)*GetUidByUnamesCount {
			unames = arrUname[i*GetUidByUnamesCount : (i+1)*GetUidByUnamesCount]
		} else {
			unames = arrUname[i*GetUidByUnamesCount:]
		}

		arrParam := &tbservice.Parameter{
			Service: "user",
			Method:  "getUidByUnames",
			Input: map[string]interface{}{
				"user_name": unames,
			},
			Output: &user.GetUidByUnamesRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getUidByUnames"+strconv.Itoa(i), arrParam)
	}

	multi.Call(ctx)

	arrUid := make(map[string]int64)
	for i := 0; len(arrUname) > i*GetUidByUnamesCount; i++ {
		resInter, err := multi.GetResult(ctx, "getUidByUnames"+strconv.Itoa(i))
		if err != nil {
			tbcontext.WarningF(ctx, "call user::getUidByUnames fail: %v", err)
			continue
		}
		res := resInter.(*user.GetUidByUnamesRes)
		for _, userInfo := range res.GetOutput().GetUids() {
			arrUid[userInfo.GetUserName()] = userInfo.GetUserId()
		}
	}

	if len(arrUid) > 0 {
		for _, arrOne := range processData.PicList {
			for _, slot := range arrOne.GetDescr() {
				if slot.GetType() == tbrichtext.SLOT_TYPE_AT {
					un := slot.GetUn()
					slot.Uid = proto.Int64(arrUid[un])
				}
			}
		}
	}
}

func ProcessOnePic(ctx context.Context, pic *picpage.PicList) []string {
	title := html.UnescapeString(pic.GetTitle())
	pic.Title = proto.String(title)

	parserCondition := tbrichtext.GetDefaultParserCondition()
	parserCondition.NewLineCount = 1
	objParser := &tbrichtext.Parser{
		Ctx: ctx,
	}

	if strings.TrimSpace(pic.GetDescStr()) != "" {
		objRequest, err := objParser.Process(parserCondition, pic.GetDescStr())
		if err != nil {
			tbcontext.WarningF(ctx, "obj parser fail: %v, desc: %v", err, []byte(pic.GetDescStr()))
		}
		pic.Descr = objRequest.ArrSlotContent
	}

	newSize := 0
	arrNewContent := make([]*meta.PbContent, 0)
	arrUname := make([]string, 0)
	for _, arrSlot := range pic.Descr {
		switch arrSlot.GetType() {
		case tbrichtext.SLOT_TYPE_AT:
			arrUname = append(arrUname, arrSlot.GetUn())
			arrNewContent = append(arrNewContent, arrSlot)
			newSize++
		case tbrichtext.SLOT_TYPE_IMG:
			if newSize != 0 && arrNewContent[newSize-1].GetType() == tbrichtext.SLOT_TYPE_TEXT {
				arrNewContent[newSize-1].Text = proto.String(arrNewContent[newSize-1].GetText() + "[图片]")
			} else {
				arrSlot.Type = proto.Uint32(tbrichtext.SLOT_TYPE_TEXT)
				arrSlot.Src = nil
				arrSlot.Text = proto.String("[图片]")
				arrNewContent = append(arrNewContent, arrSlot)
				newSize++
			}
		case tbrichtext.SLOT_TYPE_TEXT:
			if newSize != 0 && arrNewContent[newSize-1].GetType() == tbrichtext.SLOT_TYPE_TEXT {
				arrNewContent[newSize-1].Text = proto.String(arrNewContent[newSize-1].GetText() + arrSlot.GetText())
			} else {
				arrNewContent = append(arrNewContent, arrSlot)
				newSize++
			}
		default:
			arrNewContent = append(arrNewContent, arrSlot)
			newSize++
		}
	}

	pic.Descr = arrNewContent
	if pic.GetUserId() != 0 {
		pic.GetAuthor().Id = proto.Int64(pic.GetUserId())
	}
	if pic.GetUserName() != "" {
		pic.GetAuthor().Name = proto.String(pic.GetUserName())
	}

	userInfo := &client.User{
		Id:       proto.Int64(pic.GetAuthor().GetId()),
		Name:     proto.String(pic.GetAuthor().GetName()),
		NameShow: proto.String(pic.GetAuthor().GetNameShow()),
		Portrait: proto.String(pic.GetAuthor().GetPortrait()),
		Type:     proto.Int32(pic.GetAuthor().GetType()),
		LevelId:  proto.Int32(pic.GetAuthor().GetLevelId()),
		IsLike:   proto.Int32(pic.GetAuthor().GetIsLike()),
	}
	format.FormatClientUser(userInfo)
	pic.Author = userInfo
	return arrUname
}
