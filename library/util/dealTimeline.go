package util

import (
	"context"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/tieba-server-go/golib2/php"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
)

type TimelineStruct struct {
	BeginTime int64 `json:"begin_time"`
	EndTime   int64 `json:"end_time"`
}

// 解析时间线
func ParseTimeline(ctx context.Context, timeline string) map[int]TimelineStruct {
	unserTimeline, err := php.Unserialize([]byte(timeline))
	if err != nil {
		tbcontext.WarningF(ctx, "unserialize timeline fail. timeline:%v err:%v", timeline, err)
		return nil
	}

	timelineSlice, ok := unserTimeline.([]any)
	if !ok {
		tbcontext.WarningF(ctx, "parse timeline fail. timeline:%v err:%v", timeline, "invalid timeline format, expected array")
		return nil
	}

	timelineMap := make(map[int]TimelineStruct)
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		tbcontext.WarningF(ctx, "load location fail. err:%v", err)
		return nil
	}

	for _, timelineItem := range timelineSlice {
		item, ok := timelineItem.(string)
		if !ok {
			tbcontext.WarningF(ctx, "parse timeline fail. timeline:%v err:invalid timeline item format, expected string", timeline)
			return nil
		}

		item = strings.TrimSpace(item)
		conf := strings.Split(item, "##")
		if len(conf) < 2 {
			tbcontext.WarningF(ctx, "parse timeline fail. timeline:%v err:invalid timeline format, missing '##' separato", timeline)
			return nil
		}

		status, err := strconv.Atoi(conf[0])
		if err != nil {
			tbcontext.WarningF(ctx, "parse status fail. timeline:%v err:%v", timeline, err)
			return nil
		}

		arrTime := strings.Split(conf[1], "@")
		if len(arrTime) < 2 {
			tbcontext.WarningF(ctx, "parse timeline fail. timeline:%v err:invalid time format, missing '@' separator", timeline)
			return nil
		}

		begin, err := time.ParseInLocation("2006-01-02 15:04:05", arrTime[0], loc)
		if err != nil {
			tbcontext.WarningF(ctx, "parse begin time fail. timeline:%v err:%v", timeline, err)
			return nil
		}

		end, err := time.ParseInLocation("2006-01-02 15:04:05", arrTime[1], loc)
		if err != nil {
			tbcontext.WarningF(ctx, "parse end time fail. timeline:%v err:%v", timeline, err)
			return nil
		}

		timelineMap[status] = TimelineStruct{
			BeginTime: begin.Unix(),
			EndTime:   end.Unix(),
		}
	}
	return timelineMap
}
