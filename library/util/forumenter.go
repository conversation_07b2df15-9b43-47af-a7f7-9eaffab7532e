package util

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/excache"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
)

const TopForumNum = 5                     // 每个分类的topN
var topNForumLocalCache *excache.LRUCache // 本地缓存，缓存垂类topN的吧

func init() {
	topNForumLocalCache = excache.NewLRUCache(10, 0, time.Hour) //设置一个最大容量为10条记录，，且仅生效一个小时的 LRUcache
}

var classTypeList = []string{"游戏", "教育", "动漫", "行业", "生活", "地区", "文学", "情感", "体育", "科技", "数码", "动物", "娱乐明星", "时尚",
	"健康养生", "影视综", "财经", "汽车", "文化", "美食", "植物", "音乐", "社会", "科学", "搞笑"} // 垂类列表

// 查每个垂类中的热门top5吧，有本地缓存逻辑
func GetTopNForum(ctx context.Context) (map[int64]string, error) {
	// 查本地缓存
	cacheRes, hit := topNForumLocalCache.Get("topNforum")
	if hit {
		res, ok := cacheRes.(map[int64]string)
		if ok {
			return res, nil
		}
	}

	// 未命中缓存，查redis
	keys := make([]string, 0)
	for _, class := range classTypeList {
		keys = append(keys, fmt.Sprintf("tb_tophotforum_by_dt_cate_%s", class))
	}
	res, err := resource.RedisSign.MGet(ctx, keys...).Result()
	if err != nil {
		tbcontext.WarningF(ctx, "call redis mget fail, keys=[%v], err=[%v]", keys, err)
		return nil, err
	}

	// 将每个分类里面的top5记录下来
	tempMap := make(map[int64]string, 0)
	for index, class := range classTypeList {
		if res[index] == nil {
			continue
		}
		value := common.Tvttt(res[index], common.TTT_STRING).(string)
		if value == "" {
			continue
		}
		forumList := []int64{}
		err := jsoniter.UnmarshalFromString(value, &forumList)
		if err != nil {
			tbcontext.WarningF(ctx, "call jsoniter unmarshal fail, value=[%v], err=[%v]", value, err)
			continue
		}
		for i, fid := range forumList {
			if i > TopForumNum-1 {
				break
			}
			tempMap[fid] = class
		}
	}

	// 更新本地缓存, 缓存1小时
	topNForumLocalCache.Set("topNforum", tempMap)
	return tempMap, nil
}

func GenPicURLForAlbum(picURL string) string {
	tempSt, err := url.Parse(picURL)
	if err != nil {
		return picURL
	}
	// 从传入的url中解析出product 和 pic_spec
	urlPath, err := url.PathUnescape(tempSt.Path)
	if err != nil {
		return picURL
	}
	tempArr := strings.Split(urlPath, "/")
	tempPicStrID := tempArr[4]

	matches := strings.Split(tempPicStrID, ".")
	if len(matches) != 2 {
		return picURL
	}

	picID, err := image.DecodePicUrlCrypt(matches[0])
	if err != nil {
		return picURL
	}

	input := []image.Pid2UrlInput{
		{
			PicId:       int64(picID),
			ProductName: "forum",
			PicSpec:     proto.String("w=580;q=80"),
		},
	}

	out, _ := image.BatPid2Url(input)
	return out[0]
}
