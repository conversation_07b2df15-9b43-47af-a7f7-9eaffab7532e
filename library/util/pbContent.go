package util

import (
	"context"
	"strings"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbrichtext"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/common/meta"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
)

func GetPostContentRichText(ctx context.Context, content interface{}) []*client.PbContent {
	richAbstract := make([]*client.PbContent, 0)
	// 富文本处理
	richText := make([]*client.PbContent, 0)
	structContent := []*meta.PostStructContent{}
	err := common.StructAToStructBCtx(ctx, content, &structContent)
	if err != nil {
		tbcontext.WarningF(ctx, "getPostContent err:%v", err)
	} else {
		richText, err = BuildRichAbstract(ctx, structContent)
		if err != nil {
			tbcontext.WarningF(ctx, "Fail to transfer err:%v", err)
		}
	}

	for _, value := range richText {
		content := &client.PbContent{}
		err := common.StructAToStructBCtx(ctx, value, &content)
		if err != nil {
			tbcontext.WarningF(ctx, "getPostContent err:%v", err)
		} else {
			richAbstract = append(richAbstract, content)
		}
	}
	return richAbstract
}

func BuildRichAbstract(ctx context.Context, structContent []*meta.PostStructContent) ([]*client.PbContent, error) {
	objCondition := tbrichtext.GetDefaultParserCondition()
	objCondition.NewLineCount = 1
	objCondition.BolParseBdhd = true
	objCondition.BolParsePhone = true
	objCondition.BolCheckSpamUrl = true
	// 涂鸦在低与7.3.0以下版本或ios端是否显示成图片
	objCondition.BolGraffitiToImg = true
	objParserStruct := &tbrichtext.ParserStructured{}
	objParserStruct.SetClientType(clientvers.CLIENT_TYPE_ANDROID)
	objParserStruct.SetClientVersion("12.76")
	// pb结构化后，解析content方式
	// 对content进行内容处理,包括参数的过滤和调整
	pspInput := &tbrichtext.ParserStructProcessInput{
		PObjCondition:  objCondition,
		BolEmoji:       true,
		ScreenWidth:    0,
		ScreenHeight:   0,
		ArrText:        structContent,
		BolIsAllOrigin: true,
		BolNeedTopic:   true,
	}
	parseRes, err := objParserStruct.Process(ctx, pspInput)
	pbContents := parseRes.ArrSlotContent
	if err != nil || len(pbContents) == 0 {
		tbcontext.WarningF(ctx, "parseRes err:%v", err)
		return nil, err
	}
	// 客户端和fe不一样，兼容
	for idx, item := range pbContents {
		// src没有鉴权串
		if len(item.GetOriginSrc()) > 0 {
			item.Src = item.OriginSrc
		}
		// 小表情，把表情链接补充上去
		if item.GetType() == 2 && len(structContent) > idx {
			cnt := structContent[idx]
			if cnt.GetClass() == "BDE_Smiley" && strings.Contains(cnt.GetSrc(), item.GetText()) {
				item.Src = proto.String(structContent[idx].GetSrc())
			}
		}
	}

	contents := make([]*client.PbContent, 0)
	err = common.StructAToStructBCtx(ctx, pbContents, &contents)
	if err != nil {
		tbcontext.WarningF(ctx, "StructAToStructB err=%v", err)
		return nil, err
	}

	return contents, nil
}

// 获取用户展示昵称
func GetUserNameShow(userInfo *user.UserInfo) string {
	if userInfo == nil {
		return ""
	}

	nickNameV2 := userInfo.GetUserNicknameV2()
	if nickNameV2 != "" {
		return nickNameV2
	}

	nickName := userInfo.GetUserNickname()
	if nickName != "" {
		return nickName
	}

	userName := userInfo.GetUserName()
	return userName
}
