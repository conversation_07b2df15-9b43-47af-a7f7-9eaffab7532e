/**
 * @Author: gongruiyang
 * @Description:
 * @File:  splitContent
 * @Date: 2024/07/16 下午9:25
 */

package util

import (
	"strings"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbrichtext"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// ContentReStruct 对纯文本进行匹配命中词，PbContent 从 SLOT_TYPE_TEXT 类型转为 SLOT_TYPE_QUERY_HIT_WORD 类型
func ContentReStruct(contentList []*client.PbContent, hitWordMap map[string]string, staticField *types.PbFloorStaticField) []*client.PbContent {
	icon := "https://tieba-ares.cdn.bcebos.com/mis/2023-11/1700620182255/378a4ca359a9.webp"
	if staticField.IntClientType == clientvers.CLIENT_TYPE_ANDROID && clientvers.CompareV2(staticField.StrClientVersion, ">=", "12.73") {
		icon = "https://tieba-ares.cdn.bcebos.com/mis/2024-11/1730789183739/864daf80ad4b.webp"
	}
	// 获取命中词
	keyWords := make([]string, 0)
	for k := range hitWordMap {
		keyWords = append(keyWords, k)
	}

	resContentList := make([]*client.PbContent, 0)
	for _, content := range contentList {
		if content == nil {
			continue
		}

		// 非文本内容跳过
		if content.GetType() != tbrichtext.SLOT_TYPE_TEXT {
			resContentList = append(resContentList, content)
			continue
		}

		// 过滤楼中楼回复中的固定文案【回复】
		if content.GetText() == "回复 " {
			resContentList = append(resContentList, content)
			continue
		}

		// 过滤楼中楼回复中的被回复人uname
		if content.GetUid() != 0 {
			resContentList = append(resContentList, content)
			continue
		}

		// 关键词为特殊结构，单词请求只处理第一处关键词高亮，后续出现当做普通文本处理
		splitContent := splitTextByKeywords(content.GetText(), keyWords)
		for _, splitItem := range splitContent {
			if gameName, ok := hitWordMap[splitItem]; ok && !staticField.QueryMatchStatus[splitItem] {
				newContent := &client.PbContent{
					Type:        proto.Uint32(tbrichtext.SLOT_TYPE_QUERY_HIT_WORD),
					QueryText:   proto.String(splitItem),
					QueryPrefix: proto.String(gameName),
					Icon:        proto.String(icon),
				}
				resContentList = append(resContentList, newContent)
				staticField.QueryMatchStatus[splitItem] = true
			} else {
				newContent := &client.PbContent{
					Type: proto.Uint32(tbrichtext.SLOT_TYPE_TEXT),
					Text: proto.String(splitItem),
				}
				resContentList = append(resContentList, newContent)
			}
		}
	}

	return resContentList
}

// splitTextByKeywords 拆分文本，按关键词打散为数组，大小写敏感
func splitTextByKeywords(text string, keywords []string) []string {
	var result []string

	start := 0
	for _, keyword := range keywords {
		pos := strings.Index(text[start:], keyword)
		if pos != -1 {
			pos += start // 调整 pos 相对于整个字符串的位置
			if pos > start {
				// 将关键词之前的部分加入结果数组
				result = append(result, text[start:pos])
			}
			// 将关键词加入结果数组
			result = append(result, keyword)
			// 更新起始位置
			start = pos + len(keyword)
		}
	}

	// 将剩余部分加入结果数组
	if start < len(text) {
		result = append(result, text[start:])
	}

	return result
}
