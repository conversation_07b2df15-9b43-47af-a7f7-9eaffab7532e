package constants

const (
	// db中的审核状态
	AuditStatusWaitOp         = 0  // 初始态，等待审核
	AuditStatusOpSuccess      = 1  // 运营审核通过
	AuditStatusOpDeny         = 2  // 运营审核拒绝
	AuditStatusUegSuccess     = 3  // ueg机审通过
	AuditStatusUegDeny        = 4  // ueg机审拒绝
	AuditStatusUegPerson      = 5  // ueg人审中
	AuditStatusUegPersonDeny  = 6  // ueg人审拒绝
	AuditStatusAgent          = 7  // agent审核中
	AuditStatusAgentDeny      = 8  // agent审核拒绝
	AuditStatusPlotMachine    = 9  // 剧情信息机审中
	AuditStatusPlotDeny       = 10 // 剧情信息机审拒绝
	AuditStatusPlotPerson     = 11 // 剧情信息人审中
	AuditStatusPlotPersonDeny = 12 // 剧情信息人审拒绝

	// ui返回的审核状态
	AuditStatusWait = 0 // 审核中
	AuditStatusPass = 1 // 通过
	AuditStatusDeny = 2 // 拒绝
)

// AuditStatusMap db中审核状态与ui返回的审核状态映射
var AuditStatusMap = map[int64]int64{
	AuditStatusWaitOp:         AuditStatusWait,
	AuditStatusOpSuccess:      AuditStatusPass,
	AuditStatusOpDeny:         AuditStatusDeny,
	AuditStatusUegSuccess:     AuditStatusWait,
	AuditStatusUegDeny:        AuditStatusDeny,
	AuditStatusUegPerson:      AuditStatusWait,
	AuditStatusUegPersonDeny:  AuditStatusDeny,
	AuditStatusAgent:          AuditStatusWait,
	AuditStatusAgentDeny:      AuditStatusDeny,
	AuditStatusPlotMachine:    AuditStatusWait,
	AuditStatusPlotDeny:       AuditStatusDeny,
	AuditStatusPlotPerson:     AuditStatusWait,
	AuditStatusPlotPersonDeny: AuditStatusDeny,
}

// BotAuditStatusMap db中审核状态与ui返回的审核状态映射
var BotAuditStatusMap = map[int64]int64{
	AuditStatusWaitOp:        AuditStatusWait,
	AuditStatusOpSuccess:     AuditStatusPass,
	AuditStatusOpDeny:        AuditStatusDeny,
	AuditStatusUegSuccess:    AuditStatusWait,
	AuditStatusUegDeny:       AuditStatusDeny,
	AuditStatusUegPerson:     AuditStatusWait,
	AuditStatusUegPersonDeny: AuditStatusDeny,
	AuditStatusAgent:         AuditStatusWait,
	AuditStatusAgentDeny:     AuditStatusDeny,
}
