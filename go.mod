module icode.baidu.com/baidu/tieba-server-user-base/go-client-forum

go 1.22

require (
	github.com/chzyer/readline v0.0.0-20180603132655-2972be24d48e
	github.com/goccy/go-json v0.9.11
	github.com/golang/protobuf v1.5.4
	github.com/json-iterator/go v1.1.12
	github.com/pkg/errors v0.9.1
	github.com/spf13/cast v1.6.0
	github.com/tidwall/sjson v1.2.5
	golang.org/x/sync v0.7.0
	google.golang.org/protobuf v1.33.0
	icode.baidu.com/baidu/gdp/automaxprocs v1.1.0
	icode.baidu.com/baidu/gdp/codec v1.25.0
	icode.baidu.com/baidu/gdp/conf v1.22.2
	icode.baidu.com/baidu/gdp/env v1.22.1
	icode.baidu.com/baidu/gdp/excache v0.4.0
	icode.baidu.com/baidu/gdp/extension v1.25.2
	icode.baidu.com/baidu/gdp/ghttp v1.29.0
	icode.baidu.com/baidu/gdp/hestia v0.3.6
	icode.baidu.com/baidu/gdp/logit v1.26.0
	icode.baidu.com/baidu/gdp/metrics v0.6.2
	icode.baidu.com/baidu/gdp/mysql v1.25.4
	icode.baidu.com/baidu/gdp/net v1.33.0
	icode.baidu.com/baidu/gdp/nshead v1.26.0
	icode.baidu.com/baidu/gdp/paas v0.1.0
	icode.baidu.com/baidu/gdp/redis v1.30.0
	icode.baidu.com/baidu/ps-se-go/exgraph v0.4.12
	icode.baidu.com/baidu/ps-se-go/exgraph-plug v0.3.12
	icode.baidu.com/baidu/tieba-go-user-base/ub-golib v1.2.49-0.20250729091058-2f2e897a1969
	icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl v0.0.22-0.20250806140755-afa371fc6881
	icode.baidu.com/baidu/tieba-qa/traffic-middleware/v2 v2.0.3
	icode.baidu.com/baidu/tieba-server-go/commonlib2 v0.0.32-0.20241205063733-12860ea659e8
	icode.baidu.com/baidu/tieba-server-go/go-cache v1.0.4
	icode.baidu.com/baidu/tieba-server-go/golib2 v1.1.50-0.20250102072424-1bc167427403
	icode.baidu.com/baidu/tieba-server-go/operator-common v0.1.1-0.20250724083007-2345b29a292d
	icode.baidu.com/baidu/tieba-server-user-base/render v0.0.0-20250710074314-c279d4c5b0b6
)

replace github.com/gomodule/redigo v2.0.0+incompatible => github.com/gomodule/redigo v1.8.5

replace icode.baidu.com/baidu/tieba-qa/traffic-middleware => icode.baidu.com/baidu/tieba-qa/traffic-middleware v1.0.0

// replace icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl => ../../tieba-go-user-base/ub-protobufidl

// replace icode.baidu.com/baidu/tieba-go-user-base/ub-golib => ../../tieba-go-user-base/ub-golib

require (
	github.com/BurntSushi/toml v1.4.0 // indirect
	github.com/DATA-DOG/go-sqlmock v1.5.0 // indirect
	github.com/alicebob/gopher-json v0.0.0-20200520072559-a9ecdc9d1d3a // indirect
	github.com/alicebob/miniredis/v2 v2.30.5 // indirect
	github.com/andybalholm/brotli v1.0.5 // indirect
	github.com/arl/statsviz v0.5.2 // indirect
	github.com/basgys/goxml2json v1.1.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/didi/gendry v1.8.1 // indirect
	github.com/envoyproxy/protoc-gen-validate v0.10.1 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/gin-gonic/gin v1.8.1 // indirect
	github.com/go-logr/logr v1.2.3 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-playground/locales v0.14.0 // indirect
	github.com/go-playground/universal-translator v0.18.0 // indirect
	github.com/go-playground/validator/v10 v10.11.1 // indirect
	github.com/go-redis/redis/v8 v8.11.5 // indirect
	github.com/go-sql-driver/mysql v1.7.1 // indirect
	github.com/gogo/protobuf v1.3.3-0.20220703181809-8892e00f9446 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/gomodule/redigo v2.0.0+incompatible // indirect
	github.com/gorilla/websocket v1.4.2 // indirect
	github.com/idoubi/goutils v1.0.4 // indirect
	github.com/idoubi/goz v1.2.2 // indirect
	github.com/imroc/req v0.3.2 // indirect
	github.com/klauspost/compress v1.16.7 // indirect
	github.com/leodido/go-urn v1.2.1 // indirect
	github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0 // indirect
	github.com/mattn/go-isatty v0.0.16 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mozillazg/go-pinyin v0.19.0 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/openzipkin/zipkin-go v0.4.0 // indirect
	github.com/orcaman/concurrent-map v0.0.0-20210106121528-16402b402231 // indirect
	github.com/panjf2000/ants/v2 v2.7.3 // indirect
	github.com/pelletier/go-toml/v2 v2.0.5 // indirect
	github.com/philhofer/fwd v1.1.1 // indirect
	github.com/pierrec/lz4/v4 v4.1.18 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/prometheus/client_golang v1.15.1 // indirect
	github.com/prometheus/client_model v0.4.0 // indirect
	github.com/prometheus/common v0.44.0 // indirect
	github.com/prometheus/procfs v0.11.0 // indirect
	github.com/shirou/gopsutil/v3 v3.23.5 // indirect
	github.com/shoenig/go-m1cpu v0.1.6 // indirect
	github.com/stretchr/testify v1.8.4 // indirect
	github.com/techoner/gophp v0.2.0 // indirect
	github.com/tidwall/gjson v1.18.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/tinylib/msgp v1.1.6 // indirect
	github.com/tklauser/go-sysconf v0.3.11 // indirect
	github.com/tklauser/numcpus v0.6.0 // indirect
	github.com/ugorji/go/codec v1.2.7 // indirect
	github.com/yoda-of-soda/map2xml v1.0.2 // indirect
	github.com/yuin/gopher-lua v1.1.0 // indirect
	github.com/yusufpapurcu/wmi v1.2.3 // indirect
	go.opentelemetry.io/contrib/propagators/b3 v1.14.0 // indirect
	go.opentelemetry.io/contrib/propagators/jaeger v1.14.0 // indirect
	go.opentelemetry.io/contrib/propagators/ot v1.4.0 // indirect
	go.opentelemetry.io/otel v1.14.0 // indirect
	go.opentelemetry.io/otel/exporters/jaeger v1.14.0 // indirect
	go.opentelemetry.io/otel/exporters/stdout/stdouttrace v1.4.1 // indirect
	go.opentelemetry.io/otel/exporters/zipkin v1.4.1 // indirect
	go.opentelemetry.io/otel/sdk v1.14.0 // indirect
	go.opentelemetry.io/otel/trace v1.14.0 // indirect
	golang.org/x/crypto v0.15.0 // indirect
	golang.org/x/mod v0.12.0 // indirect
	golang.org/x/net v0.18.0 // indirect
	golang.org/x/sys v0.14.0 // indirect
	golang.org/x/text v0.14.0 // indirect
	golang.org/x/time v0.3.0 // indirect
	google.golang.org/genproto v0.0.0-20230306155012-7f2fa6fef1f4 // indirect
	google.golang.org/grpc v1.55.0 // indirect
	gopkg.in/go-playground/validator.v9 v9.31.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	icode.baidu.com/baidu/gdp/acl v1.21.0 // indirect
	icode.baidu.com/baidu/gdp/apiserver v1.21.4 // indirect
	icode.baidu.com/baidu/gdp/bns v1.25.0 // indirect
	icode.baidu.com/baidu/gdp/crypto v1.1.0 // indirect
	icode.baidu.com/baidu/gdp/exjson v1.1.3 // indirect
	icode.baidu.com/baidu/gdp/gdp v1.5.12 // indirect
	icode.baidu.com/baidu/gdp/mcpack v1.24.0 // indirect
	icode.baidu.com/baidu/gdp/panel v0.5.1 // indirect
	icode.baidu.com/baidu/gdp/passport v1.21.3 // indirect
	icode.baidu.com/baidu/gdp/pbrpc v1.28.0 // indirect
	icode.baidu.com/baidu/gdp/xds v0.3.0 // indirect
	icode.baidu.com/baidu/jady/ug-bizlib v1.0.39 // indirect
	icode.baidu.com/baidu/jady/ug-prize-lib v1.8.1 // indirect
	icode.baidu.com/baidu/third-party/go-control-plane v0.8.7-0.20220531025328-39658c5e033e // indirect
	icode.baidu.com/baidu/tieba-server-go/post v0.0.67 // indirect
	icode.baidu.com/baidu/tieba-server-go/rpc-protosidl v0.0.0-20240327062207-101c330ff04b // indirect
	istio.io/gogo-genproto v0.0.0-20220107154653-792e62a35424 // indirect
)

// replace icode.baidu.com/baidu/tieba-server-go/operator-common => ../../tieba-server-go/operator-common

// replace icode.baidu.com/baidu/tieba-go-user-base/ub-golib => ../../tieba-go-user-base/ub-golib

// replace icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl => ../../tieba-go-user-base/ub-protobufidl

// replace icode.baidu.com/baidu/tieba-server-user-base/render => ../../tieba-server-user-base/render
