# init project path
HOMEDIR := $(shell pwd)
WORKROOT := $(HOMEDIR)/../../..
OUTDIR  := $(HOMEDIR)/output
APPNAME := go-client-forum

export CC :=/opt/compiler/gcc-8.2/bin/gcc

# init command params
CONFERENV := online
GO      := go
GOPATH  := $(shell $(GO) env GOPATH)
GOMOD   := $(GO) mod
ifeq ($(CONFERENV),offline)
	GOBUILD := $(GO) build -gcflags "all=-N -l"
else
	GOBUILD := $(GO) build
endif
GOTEST  := $(GO) test -gcflags="-N -l"
GOPKGS  := $$($(GO) list ./...| grep -vE "vendor")
export PATH:=$(PATH)

# test cover files
COVPROF := $(HOMEDIR)/covprof.out  # coverage profile
COVFUNC := $(HOMEDIR)/covfunc.txt  # coverage profile information for each function
COVHTML := $(HOMEDIR)/covhtml.html # HTML representation of coverage profile
GOTEST  := $(GO) test -gcflags="-N -l"
# make, make all
all: prepare compile package
# set proxy env
set-env:
	$(GO) env -w GO111MODULE=on
	$(GO) env -w GONOPROXY=\*\*.baidu.com\*\*
	$(GO) env -w GOPROXY=https://goproxy.baidu-int.com
	$(GO) env -w GONOSUMDB=\*
#make prepare, download dependencies
prepare: gomod confer gocov textprocess fcrypt
gomod: set-env
	$(GOMOD) download
confer:
	$(GO) install icode.baidu.com/baidu/tieba-server-go/confer/confer@latest
gocov:
	$(GO) install icode.baidu.com/baidu/ep-qa/go-cov@latest
# include pkg path
FCRYPT := $(WORKROOT)/baidu/tieba-server-c-util/fcrypt
TEXTPROCESS := $(WORKROOT)/baidu/tieba-server-c-util/textprocess

export PKG_CONFIG_PATH :=$(FCRYPT)/pkgconfig:$(TEXTPROCESS)/pkgconfig:$$PKG_CONFIG_PATH

# download dynamic lib, replace env value GOPATH
TOKEN  := "IREPO-TOKEN:d10d23e2-3d4f-4c01-b030-7ecf550ef04b"
TARGET := "https://irepo.baidu-int.com/rest/prod/v3/baidu/tieba-server-c-util/textprocess/releases/1.0.15.2/files"

textprocess:
	wget -O output.tar.gz --no-check-certificate --header $(TOKEN) $(TARGET)
	rm -rf $(TEXTPROCESS)
	mkdir -p $(TEXTPROCESS)
	tar zxvf output.tar.gz
	mv output/* $(TEXTPROCESS)
	find $(TEXTPROCESS) -name 'lib*.pc' | xargs sed -i "s?\$${WORKROOT}?${WORKROOT}?g"

# download fcrypt dynamic lib
FCRYPT_TOKEN := "IREPO-TOKEN:1896b950-da35-4c6b-badf-de0ab3914e5a"
FCRYPT_TARGET := "https://irepo.baidu-int.com/rest/prod/v3/baidu/tieba-server-c-util/fcrypt/releases/1.0.1.1/files"

fcrypt:
	wget -O output.tar.gz --no-check-certificate --header $(FCRYPT_TOKEN) $(FCRYPT_TARGET)
	rm -rf $(FCRYPT)
	mkdir -p $(FCRYPT)
	tar zxvf output.tar.gz
	mv output/* $(FCRYPT)
	find $(FCRYPT) -name 'lib*.pc' | xargs sed -i "s?\$${WORKROOT}?${WORKROOT}?g"

coverage:
	$(GOPATH)/bin/go-cov system --inst-only --no-temp --socket-path /tmp/$(APPNAME)_cov.sock .

# make test, test your code
test: prepare test-case
test-case:
	$(GOPATH)/bin/confer -s conf -e offline --exclude ./test/conf/app.toml
	/bin/cp -rf $(HOMEDIR)/conf/* $(HOMEDIR)/test/conf/
	export LD_LIBRARY_PATH=${FCRYPT}/so && $(GOTEST) -v -cover $(GOPKGS)
	$(GOTEST) -v -cover $(GOPKGS)

#make compile
compile: coverage build
build:
	$(GOBUILD) -o $(HOMEDIR)/go-client-forum
# generate for system test
	$(GOTEST) -c -covermode set -o go-client-forum.systest -coverpkg ./...
# generate conf
	$(GOPATH)/bin/confer -s conf/servicer -e $(CONFERENV)
# make test, test your code
test: prepare test-case
# make package
package: package-bin
package-bin:
	rm -rf $(OUTDIR)
	mkdir -p $(OUTDIR)
	cp -r bin $(OUTDIR)/
	mv $(APPNAME) $(OUTDIR)/bin/
	if [ -e $(APPNAME).systest  ]; then mv $(APPNAME).systest $(OUTDIR)/bin/; fi
	$(shell if [ -d "bin"  ]; then cp -r bin $(OUTDIR)/;chmod 755 $(OUTDIR)/bin/*;fi)
	cp -r $(TEXTPROCESS)/so $(OUTDIR)
	cp -r $(FCRYPT)/so $(OUTDIR)
	cp -r ./noahdes $(OUTDIR)
	cp -r ./conf $(OUTDIR)
	cp -r ./hestia $(OUTDIR)
#编译线下运行，使用make CONFERENV=offline，不删除配置文件，线上编译使用make，默认删除
ifeq ($(CONFERENV),online)
	rm -rf $(OUTDIR)/conf/app.toml
	rm -rf $(OUTDIR)/conf/port.conf
endif

# make clean
clean:
	$(GO) clean
	rm -rf $(OUTDIR)
	rm -rf $(HOMEDIR)/go-client-forum
	rm -rf $(GOPATH)/pkg/darwin_amd64
# avoid filename conflict and speed up build
.PHONY: all prepare compile test package clean build
