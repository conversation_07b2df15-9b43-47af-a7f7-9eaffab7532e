// Copyright(C) 2022 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2022-06-22, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package httpserver

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
)

// Config http server 的配置内容
type Config struct {
	ReadTimeout  int // 单位 ms
	WriteTimeout int // 单位 ms
	IdleTimeout  int
}

// NewServer 创建一个新的 http server
func NewServer(ctx context.Context, cfg Config) *ghttp.DefaultServer {
	ser := &ghttp.DefaultServer{
		Ctx:          ctx,
		ReadTimeout:  time.Millisecond * time.Duration(cfg.ReadTimeout),
		WriteTimeout: time.Millisecond * time.Duration(cfg.WriteTimeout),
		IdleTimeout:  time.Millisecond * time.Duration(cfg.IdleTimeout),
		MaxBodyBytes: 0,
		Logger:       nil,
	}

	ser.SetHandler(httpRouter(ser))
	return ser
}
