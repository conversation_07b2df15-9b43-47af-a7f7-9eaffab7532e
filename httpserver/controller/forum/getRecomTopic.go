package forum

import (
	"context"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getRecomTopic"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum"
)

func GetRecomTopic(ctx context.Context, req ghttp.Request) ghttp.Response {
	return GetRecomTopicActionInstance(ctx, req).UiFuncHandler()
}

type GetRecomTopicAction struct {
	*uiclient.UIBaseAction
	GetRecomTopicReq *getRecomTopic.GetRecomTopicReqIdl
	GetRecomTopicRes *getRecomTopic.GetRecomTopicResIdl
	global           *types.GetRecomTopicBaseData
}

func GetRecomTopicActionInstance(ctx context.Context, req ghttp.Request) *GetRecomTopicAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &GetRecomTopicAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		GetRecomTopicReq: &getRecomTopic.GetRecomTopicReqIdl{},
		GetRecomTopicRes: &getRecomTopic.GetRecomTopicResIdl{},
		global:           &types.GetRecomTopicBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.GetRecomTopicReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.GetRecomTopicRes.Data = new(getRecomTopic.GetRecomTopicRes)

	return obj
}

func (obj *GetRecomTopicAction) ParseParams() {

}

func (obj *GetRecomTopicAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetRecomTopicReq || nil == obj.GetRecomTopicReq.Data {
		return nil
	}
	obj.global.Request = obj.GetRecomTopicReq.GetData()
	fid := obj.GetRecomTopicReq.GetData().GetFid()
	arrPrivateInfo := map[string]any{
		"fid": fid,
	}
	return arrPrivateInfo
}

func (obj *GetRecomTopicAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetRecomTopicReq {
		return false
	}
	return true
}

func (obj *GetRecomTopicAction) Execute() bool {
	errNo := forum.GetRecomTopic(obj.Ctx, obj.global, obj.GetRecomTopicRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}
	return true
}

func (obj *GetRecomTopicAction) BuildOut() {
	if nil == obj || nil == obj.GetRecomTopicRes {
		return
	}
	if nil == obj.GetRecomTopicRes.Error {
		obj.GetRecomTopicRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.GetRecomTopicRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *GetRecomTopicAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.GetRecomTopicReq || nil == obj.GetRecomTopicReq.Data {
		return
	}
}
