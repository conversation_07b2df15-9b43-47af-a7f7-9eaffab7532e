package forum

import (
	"context"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumLevelDetail"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/getforumleveldetail/adapter"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/getforumleveldetail/gen"
)

type GetForumLevelDetailAction struct {
	*uiclient.UIBaseAction
	getForumLevelDetailReq *getForumLevelDetail.GetForumLevelDetailReqIdl
	getForumLevelDetailRes *getForumLevelDetail.GetForumLevelDetailResIdl
}

func GetForumLevelDetail(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getForumLevelDetailActionInstance(ctx, req).UiFuncHandler()
}

func getForumLevelDetailActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}

	obj := &GetForumLevelDetailAction{
		UIBaseAction:           &uiclient.UIBaseAction{},
		getForumLevelDetailReq: &getForumLevelDetail.GetForumLevelDetailReqIdl{},
		getForumLevelDetailRes: &getForumLevelDetail.GetForumLevelDetailResIdl{},
	}
	if !obj.Init(ctx, req, obj, obj.getForumLevelDetailReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	return obj
}

func (obj *GetForumLevelDetailAction) ParseParams() {
	return
}

func (obj *GetForumLevelDetailAction) GetPrivateInfo() map[string]any {
	return map[string]any{}
}

func (obj *GetForumLevelDetailAction) CheckPrivate() bool {
	return true
}

func (obj *GetForumLevelDetailAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getForumLevelDetailReq == nil ||
		obj.getForumLevelDetailReq.Data == nil || obj.getForumLevelDetailRes == nil {
		return false
	}

	ll := logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService())
	defer func() {
		logging.ReleaseLogitLogger(ll)
	}()
	engCtx := resource.GetForumLevelDetailEngine.NewContext(obj.Ctx, []engine.ContextOption{
		engine.WithLogger(ll.AsEngineLogger()),
	}...)
	defer func() {
		engine.ReleaseContext(engCtx)
	}()

	engCtx.MustRegisterInstance(&obj.UIBaseAction)
	err := resource.GetForumLevelDetailEngine.Run(engCtx)

	// 算子中设置过非0错误号，使用设置的错误号直接返回
	if errnoInter := tbcontext.GetErrNo(obj.Ctx); errnoInter != nil {
		if errno := cast.ToInt(errnoInter); errno != tiebaerror.ERR_SUCCESS {
			return false
		}
	}

	// 没设置过错误号，兜底返回错误
	if err != nil {
		tbcontext.WarningF(obj.Ctx, "GetForumLevelDetail exgraph engine run fail, err=%v", err)
		obj.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, tiebaerror.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return false
	}

	var output getForumLevelDetail.GetForumLevelDetailRes
	ok := engCtx.MutableInstance(&output)
	if !ok {
		tbcontext.WarningF(obj.Ctx, "get getForumLevelDetail output fail")
		return false
	}

	obj.getForumLevelDetailRes.Data = &output
	return true
}

func (obj *GetForumLevelDetailAction) BuildOut() {
	if obj == nil || obj.getForumLevelDetailRes == nil {
		return
	}
	if obj.getForumLevelDetailRes.Error == nil {
		obj.getForumLevelDetailRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.getForumLevelDetailRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

func (obj *GetForumLevelDetailAction) BuildLog() {
	if obj == nil || obj.getForumLevelDetailRes == nil ||
		obj.getForumLevelDetailRes.Data == nil {
		return
	}
	return
}
