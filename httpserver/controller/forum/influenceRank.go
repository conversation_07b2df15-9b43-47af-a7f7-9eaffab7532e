package forum

import (
	"context"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/stlog"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/influenceRank"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum"
)

func InfluenceRank(ctx context.Context, req ghttp.Request) ghttp.Response {
	return influenceRankActionInstance(ctx, req).UiFuncHandler()
}

type InfluenceRankAction struct {
	*uiclient.UIBaseAction
	InfluenceRankReq *influenceRank.InfluenceRankReqIdl
	InfluenceRankRes *influenceRank.InfluenceRankResIdl
	global           *types.InfluenceRankBaseData
}

func influenceRankActionInstance(ctx context.Context, req ghttp.Request) *InfluenceRankAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &InfluenceRankAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		InfluenceRankReq: &influenceRank.InfluenceRankReqIdl{},
		InfluenceRankRes: &influenceRank.InfluenceRankResIdl{},
		global:           &types.InfluenceRankBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.InfluenceRankReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.InfluenceRankRes.Data = new(influenceRank.InfluenceRankRes)

	return obj
}

func (obj *InfluenceRankAction) ParseParams() {

}

func (obj *InfluenceRankAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.InfluenceRankReq || nil == obj.InfluenceRankReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.InfluenceRankReq.GetData()

	forumId := obj.InfluenceRankReq.GetData().GetForumId()

	arrPrivateInfo := map[string]any{
		"forum_id": forumId,
	}

	return arrPrivateInfo
}

func (obj *InfluenceRankAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.InfluenceRankReq {
		return false
	}

	return true
}

func (obj *InfluenceRankAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.InfluenceRankReq || nil == obj.InfluenceRankReq.Data {
		return false
	}
	errNo := forum.InfluenceRank(obj.Ctx, obj.global, obj.InfluenceRankRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *InfluenceRankAction) BuildOut() {
	if nil == obj || nil == obj.InfluenceRankRes {
		return
	}
	if nil == obj.InfluenceRankRes.Error {
		// 默认赋值成功
		obj.InfluenceRankRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.InfluenceRankRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *InfluenceRankAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.InfluenceRankReq || nil == obj.InfluenceRankReq.Data {
		return
	}
	stlog.AddLog(obj.Ctx, "urlkey", "cforumapi/forum/influenceRank")
}
