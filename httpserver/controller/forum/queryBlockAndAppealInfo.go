package forum

import (
	"context"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/queryBlockAndAppealInfo"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func QueryBlockAndAppealInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getQueryBlockAndAppealInfoActionInstance(ctx, req).UiFuncHandler()
}

type queryBlockAndAppealInfoAction struct {
	*uiclient.UIBaseAction
	queryBlockAndAppealInfoReq *queryBlockAndAppealInfo.QueryBlockAndAppealInfoReqIdl
	queryBlockAndAppealInfoRes *queryBlockAndAppealInfo.QueryBlockAndAppealInfoResIdl
	global                     *types.CQueryBlockAndAppealInfoBaseData
}

func getQueryBlockAndAppealInfoActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &queryBlockAndAppealInfoAction{
		UIBaseAction:               &uiclient.UIBaseAction{},
		queryBlockAndAppealInfoReq: &queryBlockAndAppealInfo.QueryBlockAndAppealInfoReqIdl{},
		queryBlockAndAppealInfoRes: &queryBlockAndAppealInfo.QueryBlockAndAppealInfoResIdl{},
		global:                     &types.CQueryBlockAndAppealInfoBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.queryBlockAndAppealInfoReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.queryBlockAndAppealInfoRes.Data = new(queryBlockAndAppealInfo.QueryBlockAndAppealInfoRes)
	return obj
}

func (obj *queryBlockAndAppealInfoAction) ParseParams() {
	if nil == obj || nil == obj.queryBlockAndAppealInfoReq || nil == obj.queryBlockAndAppealInfoReq.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.queryBlockAndAppealInfoReq.GetData()
}

func (obj *queryBlockAndAppealInfoAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.queryBlockAndAppealInfoReq || nil == obj.queryBlockAndAppealInfoReq.Data {
		return nil
	}
	userId := int64(0)
	if userIdStr, ok := obj.Req.PostForm("user_id"); ok {
		userId = common.Tvttt(userIdStr, common.TTT_INT64).(int64)
	}

	arrPrivateInfo := map[string]interface{}{
		"user_id": userId,
	}

	return arrPrivateInfo
}

func (obj *queryBlockAndAppealInfoAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.queryBlockAndAppealInfoReq {
		return false
	}

	return true
}

func (obj *queryBlockAndAppealInfoAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.queryBlockAndAppealInfoReq || nil == obj.queryBlockAndAppealInfoReq.Data {
		return false
	}
	errNo := forum.QueryBlockAndAppealInfo(obj.Ctx, obj.global, obj.queryBlockAndAppealInfoRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *queryBlockAndAppealInfoAction) BuildOut() {
	if nil == obj || nil == obj.queryBlockAndAppealInfoRes {
		return
	}
	if nil == obj.queryBlockAndAppealInfoRes.Error {
		//默认赋值成功
		obj.queryBlockAndAppealInfoRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	obj.ObjResponse.SetStructData(obj.queryBlockAndAppealInfoRes)
}

func (obj *queryBlockAndAppealInfoAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.queryBlockAndAppealInfoReq || nil == obj.queryBlockAndAppealInfoReq.Data {
		return
	}
}
