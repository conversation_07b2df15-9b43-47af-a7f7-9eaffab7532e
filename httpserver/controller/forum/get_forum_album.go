package forum

import (
	"context"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbum"
	golibExgraph "icode.baidu.com/baidu/tieba-server-go/golib2/exgraph"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum/get_forum_album/core"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum/get_forum_album/ext"
)

const GetForumAlbumEngineName = "forum_getforumalbum"

var (
	getForumAlbumExgraph engine.Engine
)

func init() {
	var err error
	err = golibExgraph.InitExgraph()
	if err != nil {
		panic(err)
	}
	getForumAlbumExgraph, err = golibExgraph.GetEngineByName(GetForumAlbumEngineName)
	if err != nil {
		panic(err)
	}
}

func GetForumAlbum(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getForumAlbumActionInstance(ctx, req).UiFuncHandler()
}

type GetForumAlbumAction struct {
	*uiclient.UIBaseAction
	getForumAlbumReq *getForumAlbum.GetForumAlbumReqIdl
	getForumAlbumRes *getForumAlbum.GetForumAlbumResIdl
	global           *types.GetForumAlbumBaseData
}

func getForumAlbumActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}

	obj := &GetForumAlbumAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		getForumAlbumReq: &getForumAlbum.GetForumAlbumReqIdl{},
		getForumAlbumRes: &getForumAlbum.GetForumAlbumResIdl{},
		global:           &types.GetForumAlbumBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getForumAlbumReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getForumAlbumRes.Data = new(getForumAlbum.GetForumAlbumResData)
	return obj
}

func (obj *GetForumAlbumAction) ParseParams() {
	if nil == obj || nil == obj.getForumAlbumReq || nil == obj.getForumAlbumRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getForumAlbumReq.GetData()
}

func (obj *GetForumAlbumAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getForumAlbumReq || nil == obj.getForumAlbumReq.Data {
		return nil
	}
	pn := obj.getForumAlbumReq.GetData().GetPn()
	if pn == 0 {
		pn = 1
	}

	arrPrivateInfo := map[string]interface{}{
		"pn": common.Tvttt(pn, common.TTT_INT64),
	}

	return arrPrivateInfo
}

func (obj *GetForumAlbumAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getForumAlbumReq {
		return false
	}

	return true
}

func (obj *GetForumAlbumAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getForumAlbumReq || nil == obj.getForumAlbumReq.Data {
		return false
	}

	obj.global.StaticField = new(types.GetForumAlbumStaticField)
	staticField := obj.global.StaticField
	staticField.InitStatic()
	staticField.UserID = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	staticField.IntClientType = common.Tvttt(obj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	staticField.StrClientVersion = common.Tvttt(obj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	staticField.Cuid = common.Tvttt(obj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
	staticField.Pn = common.Tvttt(obj.ObjRequest.GetPrivateAttr("pn", 1), common.TTT_INT64).(int64)

	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}

	exctx := getForumAlbumExgraph.NewContext(obj.Ctx, opts...)

	exctx.MustRegisterInstance(&obj.global)
	exctx.MustRegisterInstance(&obj.getForumAlbumRes.Data)

	err := getForumAlbumExgraph.Run(exctx)
	if err != nil {
		obj.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, tiebaerror.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		tbcontext.FatalF(obj.Ctx, "exgraph engine run fail.err:%s", err.Error())
	}

	exctx.RangeOpCost(func(opName string, cost time.Duration) {
		obj.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	return true
}

func (obj *GetForumAlbumAction) BuildOut() {
	if nil == obj || nil == obj.getForumAlbumRes {
		return
	}
	if nil == obj.getForumAlbumRes.Error {
		//默认赋值成功
		obj.getForumAlbumRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	obj.ObjResponse.SetStructData(obj.getForumAlbumRes)
}

func (obj *GetForumAlbumAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getForumAlbumReq || nil == obj.getForumAlbumReq.Data {
		return
	}
	// 业务定制监控打点
	obj.buildCustomMonitorLog()
}

// 业务定制监控打点
func (obj *GetForumAlbumAction) buildCustomMonitorLog() {
	albumNums := 0
	if nil != obj.getForumAlbumRes {
		albumNums = len(obj.getForumAlbumRes.GetData().GetForumAlbum())
	}
	tbcontext.TraceF(obj.Ctx, "tieba_custom_metric_monitor module=go-client-forum item=/c/f/forum/getForumAlbum metric=album_num value1=%d value2=", albumNums)
}
