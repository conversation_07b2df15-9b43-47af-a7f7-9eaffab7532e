package forum

import (
	"context"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getRecommendForumData"
	golibExgraph "icode.baidu.com/baidu/tieba-server-go/golib2/exgraph"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum/getRecommendForumData/core"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum/getRecommendForumData/ext"
)

const RecommendForumDataEngineName = "forum_recommendforumdata"

var (
	recommendForumDataExgraph engine.Engine
)

func init() {
	var err error
	err = golibExgraph.InitExgraph()
	if err != nil {
		panic(err)
	}
	recommendForumDataExgraph, err = golibExgraph.GetEngineByName(RecommendForumDataEngineName)
	if err != nil {
		panic(err)
	}
}

func RecommendForumData(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getRecommendForumDataActionInstance(ctx, req).UiFuncHandler()
}

type RecommendForumDataAction struct {
	*uiclient.UIBaseAction
	RecommendForumDataReq *getRecommendForumData.GetRecommendForumDataReqIdl
	RecommendForumDataRes *getRecommendForumData.GetRecommendForumDataResIdl
	global                *types.CRecommendForumDataBaseData
}

func getRecommendForumDataActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &RecommendForumDataAction{
		UIBaseAction:          &uiclient.UIBaseAction{},
		RecommendForumDataReq: &getRecommendForumData.GetRecommendForumDataReqIdl{},
		RecommendForumDataRes: &getRecommendForumData.GetRecommendForumDataResIdl{},
		global:                &types.CRecommendForumDataBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.RecommendForumDataReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.global.StaticField = types.NewRecommendForumDataStaticField()
	obj.RecommendForumDataRes.Data = new(getRecommendForumData.GetRecommendForumDataRes)
	return obj
}

func (obj *RecommendForumDataAction) ParseParams() {
	if nil == obj || nil == obj.RecommendForumDataReq || nil == obj.RecommendForumDataReq.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.RecommendForumDataReq.GetData()
}

func (obj *RecommendForumDataAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.RecommendForumDataReq || nil == obj.RecommendForumDataReq.Data {
		return nil
	}

	arrPrivateInfo := map[string]interface{}{
		"page": obj.RecommendForumDataReq.GetData().GetPage(),
	}

	return arrPrivateInfo
}

func (obj *RecommendForumDataAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.RecommendForumDataReq {
		return false
	}
	return true
}

func (obj *RecommendForumDataAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.RecommendForumDataReq || nil == obj.RecommendForumDataReq.Data {
		return false
	}

	staticField := obj.global.StaticField
	staticField.UserId = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	staticField.Login = common.Tvttt(obj.ObjRequest.GetCommonAttr("login", false), common.TTT_BOOL).(bool)
	staticField.ClientType = common.Tvttt(obj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	staticField.ClientVersion = common.Tvttt(obj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	staticField.Page = common.Tvttt(obj.ObjRequest.GetPrivateAttr("page", 0), common.TTT_INT).(int)

	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}

	exctx := recommendForumDataExgraph.NewContext(obj.Ctx, opts...)

	exctx.MustRegisterInstance(&obj.global)
	exctx.MustRegisterInstance(&obj.RecommendForumDataRes.Data)

	err := recommendForumDataExgraph.Run(exctx)
	if err != nil {
		tbcontext.WarningF(obj.Ctx, "exgraph engine run fail.err:%s", err.Error())
	}

	exctx.RangeOpCost(func(opName string, cost time.Duration) {
		obj.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	return true
}

func (obj *RecommendForumDataAction) BuildOut() {
	if nil == obj || nil == obj.RecommendForumDataRes {
		return
	}
	if nil == obj.RecommendForumDataRes.Error {
		//默认赋值成功
		obj.RecommendForumDataRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	obj.ObjResponse.SetStructData(obj.RecommendForumDataRes)
}

func (obj *RecommendForumDataAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.RecommendForumDataReq || nil == obj.RecommendForumDataReq.Data {
		return
	}
}
