package forum

import (
	"context"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/stlog"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"
	golibExgraph "icode.baidu.com/baidu/tieba-server-go/golib2/exgraph"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum/forum_guide/core"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum/forum_guide/ext"
)

const ForumGuideEngineName = "forum_forumguide"

var (
	forumGuideExgraph engine.Engine
)

func init() {
	var err error
	err = golibExgraph.InitExgraph()
	if err != nil {
		panic(err)
	}
	forumGuideExgraph, err = golibExgraph.GetEngineByName(ForumGuideEngineName)
	if err != nil {
		panic(err)
	}
}

func ForumGuide(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getForumGuideActionInstance(ctx, req).UiFuncHandler()
}

type ForumGuideAction struct {
	*uiclient.UIBaseAction
	ForumGuideReq *forumGuide.ForumGuideReqIdl
	ForumGuideRes *forumGuide.ForumGuideResIdl
	global        *types.CForumGuideBaseData
}

func getForumGuideActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}

	// 打点JA3信息 add zengguowang 2023-07-27
	// HTTP_X_BFE_SSL_JA3_HASH 示例：dof5f23eed31e09c221b323a3ce64109
	// HTTP_X_BFE_SSL_JA3_RAW 示例：771,**************-49195-49196-52393-49199-49200-52392-49171-49172-156-157-47-53,0-23-65281-1,0-11-35-16-5-13-51-45-43-21,29-23-24,0
	ja3Hash := req.HeaderDefault("X-Bfe-Ssl-Ja3-Hash", "")
	ja3Raw := req.HeaderDefault("X-Bfe-Ssl-Ja3-Raw", "")
	stlog.AddLog(ctx, "http_x_bfe_ssl_ja3_hash", ja3Hash)
	stlog.AddLog(ctx, "http_x_bfe_ssl_ja3_raw", ja3Raw)

	obj := &ForumGuideAction{
		UIBaseAction:  &uiclient.UIBaseAction{},
		ForumGuideReq: &forumGuide.ForumGuideReqIdl{},
		ForumGuideRes: &forumGuide.ForumGuideResIdl{},
		global:        &types.CForumGuideBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.ForumGuideReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.global.StaticField = new(types.ForumGuideStaticField)
	obj.ForumGuideRes.Data = new(forumGuide.ForumGuideRes)
	return obj
}

func (obj *ForumGuideAction) ParseParams() {
	if nil == obj || nil == obj.ForumGuideReq || nil == obj.ForumGuideReq.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.ForumGuideReq.GetData()
}

func (obj *ForumGuideAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ForumGuideReq || nil == obj.ForumGuideReq.Data {
		return nil
	}
	page := strings.TrimSpace(obj.Req.PostFormDefault("page", ""))
	if page == "" {
		page = obj.Req.QueryDefault("page", "1")
	}
	if page == "" {
		page = "1"
	}

	arrPrivateInfo := map[string]interface{}{
		"page": common.Tvttt(page, common.TTT_INT),
	}

	return arrPrivateInfo
}

func (obj *ForumGuideAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ForumGuideReq {
		return false
	}
	obj.global.StaticField.SortType = int(obj.ForumGuideReq.GetData().GetSortType())
	obj.global.StaticField.VoiceRoomList = make([]*client.ThreadInfo, 0)
	obj.global.StaticField.ArrLikeForumList = make([]*forumGuide.LikeForum, 0)

	if obj.global.StaticField.SortType != types.FlistSortCustom &&
		obj.global.StaticField.SortType != types.FlistSortForumLevel &&
		obj.global.StaticField.SortType != types.FlistSortForumTop &&
		obj.global.StaticField.SortType != types.FlistSortForumTopSortScore {
		return false
	}

	return true
}

func (obj *ForumGuideAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ForumGuideReq || nil == obj.ForumGuideReq.Data {
		return false
	}

	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}

	exctx := forumGuideExgraph.NewContext(obj.Ctx, opts...)

	exctx.MustRegisterInstance(&obj.global)
	exctx.MustRegisterInstance(&obj.ForumGuideRes.Data)

	err := forumGuideExgraph.Run(exctx)
	if err != nil {
		tbcontext.WarningF(obj.Ctx, "exgraph engine run fail.err:%s", err.Error())
	}

	exctx.RangeOpCost(func(opName string, cost time.Duration) {
		obj.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	return true
}

func (obj *ForumGuideAction) BuildOut() {
	if nil == obj || nil == obj.ForumGuideRes {
		return
	}
	if nil == obj.ForumGuideRes.Error {
		// 默认赋值成功
		obj.ForumGuideRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	obj.ObjResponse.SetStructData(obj.ForumGuideRes)
}

func (obj *ForumGuideAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.ForumGuideReq || nil == obj.ForumGuideReq.Data {
		return
	}
}
