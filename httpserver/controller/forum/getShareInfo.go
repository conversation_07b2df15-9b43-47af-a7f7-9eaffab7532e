package forum

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getShareInfo"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum"
)

func GetShareInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getShareInfoActionInstance(ctx, req).UiFuncHandler()
}

type getShareInfoAction struct {
	*uiclient.UIBaseAction
	GetShareInfoReq *getShareInfo.GetShareInfoReqIdl
	GetShareInfoRes *getShareInfo.GetShareInfoResIdl
	global          *types.GetShareInfoData
}

func getShareInfoActionInstance(ctx context.Context, req ghttp.Request) *getShareInfoAction {
	if nil == ctx || nil == req {
		return nil
	}

	obj := &getShareInfoAction{
		UIBaseAction:    &uiclient.UIBaseAction{},
		GetShareInfoReq: &getShareInfo.GetShareInfoReqIdl{},
		GetShareInfoRes: &getShareInfo.GetShareInfoResIdl{},
		global:          &types.GetShareInfoData{},
	}

	if !obj.Init(ctx, req, obj, obj.GetShareInfoReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}

	obj.global.BaseObj = obj.UIBaseAction
	obj.GetShareInfoRes.Data = new(getShareInfo.GetShareInfo)
	tbcontext.NoticeF(ctx, "GetShareInfo start333, obj:%s", common.ToString(obj))
	return obj
}

func (obj *getShareInfoAction) ParseParams() {
	if nil == obj || nil == obj.GetShareInfoReq || nil == obj.GetShareInfoRes.Data || nil == obj.ObjRequest {
		tbcontext.WarningF(obj.Ctx, "ParseParams error")
		return
	}

	// 如果老的参数有任意一个被传入， 则进入打分贴参数判断逻辑
	if obj.GetShareInfoReq.GetData().GetThreadId() != 0 || obj.GetShareInfoReq.GetData().GetForumId() != 0 {
		if obj.GetShareInfoReq.GetData().GetThreadId() == 0 {
			tbcontext.NoticeF(obj.Ctx, "params[thread_id] error")
			return
		}
		if obj.GetShareInfoReq.GetData().GetForumId() == 0 {
			tbcontext.NoticeF(obj.Ctx, "params[forum_id] error")
			return
		}

	}

	// scene = agent需要传 bot_uk
	if obj.GetShareInfoReq.GetData().GetScene() == "agent" && obj.GetShareInfoReq.GetData().GetBotUk() == "" {
		tbcontext.NoticeF(obj.Ctx, "params[bot_uk] error")
		return
	}

	// scene = score 需要传 thread_id、forum_id
	if obj.GetShareInfoReq.GetData().GetScene() == "score" && (obj.GetShareInfoReq.GetData().GetThreadId() == 0 || obj.GetShareInfoReq.GetData().GetForumId() == 0) {
		tbcontext.NoticeF(obj.Ctx, "params[threadId or forumId] error")
		return
	}

	obj.global.Request = obj.GetShareInfoReq.GetData()
}

func (obj *getShareInfoAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetShareInfoReq || nil == obj.GetShareInfoReq.Data {
		tbcontext.WarningF(obj.Ctx, "GetPrivateInfo error")
		return nil
	}

	// privateInfo := map[string]interface{}{
	// 	"check_login": true,
	// 	"need_login":  true,
	// }
	privateInfo := map[string]interface{}{}
	return privateInfo
}

func (obj *getShareInfoAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetShareInfoReq {
		tbcontext.WarningF(obj.Ctx, "CheckPrivate error")
		return false
	}

	return true
}

func (obj *getShareInfoAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetShareInfoReq || nil == obj.GetShareInfoReq.Data ||
		obj.global == nil {
		tbcontext.WarningF(obj.Ctx, "Execute error")
		return false
	}

	// 处理参数
	obj.global.UserID = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", ""), common.TTT_UINT64).(uint64)
	// 这段代码过于拟人了，看上去很忙但是不知道在忙什么
	// if obj.GetShareInfoReq.GetData().GetScene() == "agent" {
	// 	obj.global.Scene = obj.GetShareInfoReq.GetData().GetScene()
	// 	//if obj.GetShareInfoReq.GetData().GetExtraParam().GetHasHistory() == 0 {
	// 	//	obj.global.HasHistory = 0
	// 	//} else {
	// 	//	obj.global.HasHistory = obj.GetShareInfoReq.GetData().GetExtraParam().GetHasHistory()
	// 	//}
	// 	obj.global.BotUK = obj.GetShareInfoReq.GetData().GetBotUk()
	// } else {
	// 	obj.global.Scene = "score"
	// 	obj.global.ThreadID = obj.GetShareInfoReq.GetData().GetThreadId()
	// 	obj.global.ForumID = obj.GetShareInfoReq.GetData().GetForumId()
	// }

	obj.global.Scene = obj.GetShareInfoReq.GetData().GetScene()
	obj.global.ThreadID = obj.GetShareInfoReq.GetData().GetThreadId()
	obj.global.ForumID = obj.GetShareInfoReq.GetData().GetForumId()
	obj.global.BotUK = obj.GetShareInfoReq.GetData().GetBotUk()

	if obj.global.Scene == "" {
		obj.global.Scene = "score"
	}

	// tbcontext.NoticeF(obj.Ctx, "obj.global.info:%s", common.ToString(obj.global))

	errNo := forum.GetShareInfo(obj.Ctx, obj.global, obj.GetShareInfoRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}
	return true
}

func (obj *getShareInfoAction) BuildOut() {
	if nil == obj || nil == obj.GetShareInfoRes {
		return
	}

	if nil == obj.GetShareInfoRes.Error {
		// 默认赋值成功
		obj.GetShareInfoRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.GetShareInfoRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getShareInfoAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.GetShareInfoReq || nil == obj.GetShareInfoRes.Data {
		return
	}
}
