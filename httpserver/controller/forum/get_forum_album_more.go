package forum

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"

	clientProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	gfamProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbumMore"

	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum"
)

type getForumAlbumMoreAction struct {
	*uiclient.UIBaseAction
	getForumAlbumMoreReq *gfamProto.GetForumAlbumMoreReqIdl
	getForumAlbumMoreRes *gfamProto.GetForumAlbumMoreResIdl
	global               *types.GetForumAlbumMoreBaseData
}

func GetForumAlbumMore(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getForumAlbumMoreActionInstance(ctx, req).UiFuncHandler()
}

func getForumAlbumMoreActionInstance(ctx context.Context, req ghttp.Request) *getForumAlbumMoreAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &getForumAlbumMoreAction{
		UIBaseAction:         &uiclient.UIBaseAction{},
		getForumAlbumMoreReq: &gfamProto.GetForumAlbumMoreReqIdl{},
		getForumAlbumMoreRes: &gfamProto.GetForumAlbumMoreResIdl{},
		global:               &types.GetForumAlbumMoreBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getForumAlbumMoreReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getForumAlbumMoreRes.Data = &gfamProto.GetForumAlbumMoreResData{}
	return obj
}

func (obj *getForumAlbumMoreAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.getForumAlbumMoreReq == nil || obj.getForumAlbumMoreReq.Data == nil {
		return
	}
	obj.global.Request = obj.getForumAlbumMoreReq.GetData()
}

func (obj *getForumAlbumMoreAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.getForumAlbumMoreReq == nil || obj.getForumAlbumMoreReq.Data == nil {
		return nil
	}
	queryData := obj.getForumAlbumMoreReq.GetData()
	privateInfo := map[string]any{
		"thread_ids": queryData.GetThreadIds(),
		"forum_ids":  queryData.GetForumIds(),
	}
	return privateInfo
}

func (obj *getForumAlbumMoreAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getForumAlbumMoreReq == nil {
		return false
	}
	return true
}

func (obj *getForumAlbumMoreAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getForumAlbumMoreReq == nil || obj.getForumAlbumMoreReq.Data == nil {
		return false
	}

	obj.global.StaticField = new(types.GetForumAlbumMoreStaticField)
	obj.global.StaticField.Init()
	obj.global.StaticField.UserID = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	obj.global.StaticField.ClientType = common.Tvttt(obj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)

	errno := forum.GetForumAlbumMore(obj.Ctx, obj.global, obj.getForumAlbumMoreRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}

func (obj *getForumAlbumMoreAction) BuildOut() {
	if obj == nil || obj.getForumAlbumMoreRes == nil {
		return
	}
	if obj.getForumAlbumMoreRes.Error == nil {
		obj.getForumAlbumMoreRes.Error = &clientProto.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}

	outData := map[string]any{
		"data": obj.getForumAlbumMoreRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getForumAlbumMoreAction) BuildLog() {
	if obj == nil || obj.Ctx == nil || obj.Req == nil || obj.getForumAlbumMoreReq == nil || obj.getForumAlbumMoreReq.Data == nil {
		return
	}
}
