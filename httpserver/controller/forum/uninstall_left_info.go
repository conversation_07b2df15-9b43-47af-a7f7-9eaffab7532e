package forum

import (
	"context"
	"errors"
	"strconv"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	uninstallLeftProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/uninstallLeftInfo"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/uninstallleft/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/uninstallleft/buildoutput"
)

// UninstallLeftInfo _
func UninstallLeftInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	return uninstallLeftInfoActionInstance(ctx, req).UiFuncHandler()
}

// uninstallLeftInfoAction _
type uninstallLeftInfoAction struct {
	*uiclient.UIBaseAction
	uninstallLeftInfoReq *uninstallLeftProto.UninstallLeftInfoReqIdl
	uninstallLeftInfoRes *uninstallLeftProto.UninstallLeftInfoResIdl
}

// uninstallLeftInfoActionInstance _
func uninstallLeftInfoActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	obj := &uninstallLeftInfoAction{
		UIBaseAction:         &uiclient.UIBaseAction{},
		uninstallLeftInfoReq: &uninstallLeftProto.UninstallLeftInfoReqIdl{},
		uninstallLeftInfoRes: &uninstallLeftProto.UninstallLeftInfoResIdl{
			Error: &client.Error{
				Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
				Errmsg:  proto.String(""),
				Usermsg: proto.String(""),
			},
			Data: &uninstallLeftProto.UninstallLeftInfo{},
		},
	}
	if !obj.Init(ctx, req, obj, obj.uninstallLeftInfoReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	return obj
}

// ParseParams 格式化参数，由private_params算子代替
func (u *uninstallLeftInfoAction) ParseParams() {
	return
}

// GetPrivateInfo 获取私有参数，由private_params算子代替
func (u *uninstallLeftInfoAction) GetPrivateInfo() map[string]any {
	return map[string]any{}
}

// CheckPrivate 校验私有参数，由private_params算子代替
func (u *uninstallLeftInfoAction) CheckPrivate() bool {
	return true
}

func (u *uninstallLeftInfoAction) Execute() bool {
	if u == nil || u.ObjRequest == nil || u.ObjResponse == nil || u.uninstallLeftInfoRes == nil {
		return false
	}

	// 初始化返回值
	u.ObjResponse.SetStructData(u.uninstallLeftInfoRes)

	ll := logging.WrapLogit(u.Ctx, tbcontext.GetLogidString(u.Ctx), loggerResource.GetLoggerService())
	defer func() {
		logging.ReleaseLogitLogger(ll)
	}()

	engCtx := resource.UninstallLeftInfoEngine.NewContext(u.Ctx, []engine.ContextOption{
		engine.WithLogger(ll.AsEngineLogger()),
	}...)
	defer func() {
		engine.ReleaseContext(engCtx)
	}()

	engCtx.MustRegisterInstance(&u.UIBaseAction)
	err := resource.UninstallLeftInfoEngine.Run(engCtx)

	// 记录算子耗时
	engCtx.RangeOpCost(func(opName string, cost time.Duration) {
		u.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	// 算子中设置过错误号，使用算子设置的错误号作为返回
	var state engine.State
	if errors.As(err, &state) {
		tbcontext.WarningF(u.Ctx, "exgraph execute fail, code=%d, errmsg=%s", state.Code(), state.Error())
		u.Error(state.Code(), state.Error(), false)
		return false
	}

	// 未设置过错误号，兜底返回错误信息
	if err != nil {
		tbcontext.WarningF(u.Ctx, "exgraph execute fail, err=%v", err)
		u.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, stcdefine.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return false
	}

	var buildOutputInterface buildoutput.BuildOutput
	ok := engCtx.MutableInstance(&buildOutputInterface)
	if !ok || buildOutputInterface == nil {
		tbcontext.WarningF(u.Ctx, "get uninstall_left_info buildOutput fail")
		return false
	}

	output := buildOutputInterface.GetOutput()
	if output == nil {
		tbcontext.WarningF(u.Ctx, "get uninstall_left_info output fail")
		return false
	}
	u.uninstallLeftInfoRes.Data = output
	return true
}

func (u *uninstallLeftInfoAction) BuildOut() {
	if u == nil || u.uninstallLeftInfoRes == nil {
		return
	}

	u.ObjResponse.SetStructData(u.uninstallLeftInfoRes)
	return
}

func (u *uninstallLeftInfoAction) BuildLog() {
	return
}
