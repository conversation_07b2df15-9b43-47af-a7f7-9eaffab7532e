package forum

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	golibExgraph "icode.baidu.com/baidu/tieba-server-go/golib2/exgraph"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum/get_history_forum/core"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum/get_history_forum/ext"
)

const HistoryForumEngineName = "forum_historyforum"

var (
	HistoryForumExgraph engine.Engine
)

func init() {
	var err error
	err = golibExgraph.InitExgraph()
	if err != nil {
		panic(err)
	}
	HistoryForumExgraph, err = golibExgraph.GetEngineByName(HistoryForumEngineName)
	if err != nil {
		panic(err)
	}
}

func HistoryForum(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getHistoryForumActionInstance(ctx, req).UiFuncHandler()
}

type HistoryForumAction struct {
	*uiclient.UIBaseAction
	HistoryForumReq *getHistoryForum.GetHistoryForumReqIdl
	HistoryForumRes *getHistoryForum.GetHistoryForumResIdl
	global          *types.CHistoryForumBaseData
}

func getHistoryForumActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &HistoryForumAction{
		UIBaseAction:    &uiclient.UIBaseAction{},
		HistoryForumReq: &getHistoryForum.GetHistoryForumReqIdl{},
		HistoryForumRes: &getHistoryForum.GetHistoryForumResIdl{},
		global:          &types.CHistoryForumBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.HistoryForumReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.global.StaticField = types.NewHistoryForumStaticField()
	obj.HistoryForumRes.Data = new(getHistoryForum.GetHistoryForumRes)
	return obj
}

func (obj *HistoryForumAction) ParseParams() {
	if nil == obj || nil == obj.HistoryForumReq || nil == obj.HistoryForumReq.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.HistoryForumReq.GetData()
}

func (obj *HistoryForumAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.HistoryForumReq || nil == obj.HistoryForumReq.Data {
		return nil
	}

	//obj.ObjRequest.AddStrategy("check_sign", false)
	historyInfo := make([]map[string]interface{}, 0)
	err := json.Unmarshal([]byte(obj.HistoryForumReq.GetData().GetHistory()), &historyInfo)
	if err != nil {
		tbcontext.WarningF(obj.Ctx, "get history info fail: %v", err)
	}
	arrPrivateInfo := map[string]interface{}{
		"history_info": historyInfo,
	}

	return arrPrivateInfo
}

func (obj *HistoryForumAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.HistoryForumReq {
		return false
	}
	historyInfo, _ := obj.ObjRequest.GetPrivateAttr("history_info", []map[string]interface{}{}).([]map[string]interface{})
	if historyInfo == nil || len(historyInfo) == 0 {
		tbcontext.WarningF(obj.Ctx, "get history info fail: %v", historyInfo)
		obj.Error(tiebaerror.ERR_SUCCESS, tiebaerror.GetErrMsg(tiebaerror.ERR_SUCCESS), false)
		return false
	}

	return true
}

func (obj *HistoryForumAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.HistoryForumReq || nil == obj.HistoryForumReq.Data {
		return false
	}
	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}

	exctx := HistoryForumExgraph.NewContext(obj.Ctx, opts...)

	exctx.MustRegisterInstance(&obj.global)
	exctx.MustRegisterInstance(&obj.HistoryForumRes.Data)

	err := HistoryForumExgraph.Run(exctx)
	if err != nil {
		tbcontext.WarningF(obj.Ctx, "exgraph engine run fail.err:%s", err.Error())
	}

	exctx.RangeOpCost(func(opName string, cost time.Duration) {
		obj.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	return true
}

func (obj *HistoryForumAction) BuildOut() {
	if nil == obj || nil == obj.HistoryForumRes {
		return
	}
	if nil == obj.HistoryForumRes.Error {
		//默认赋值成功
		obj.HistoryForumRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	obj.ObjResponse.SetStructData(obj.HistoryForumRes)
}

func (obj *HistoryForumAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.HistoryForumReq || nil == obj.HistoryForumReq.Data {
		return
	}
}
