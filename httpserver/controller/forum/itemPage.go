package forum

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/itemPage"
	golibExgraph "icode.baidu.com/baidu/tieba-server-go/golib2/exgraph"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum/item_page/core"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum/item_page/ext"
)

const ItemPageEngineName = "forum_itempage"

var (
	ItemPageExgraph engine.Engine
)

func init() {
	var err error
	err = golibExgraph.InitExgraph()
	if err != nil {
		panic(err)
	}
	ItemPageExgraph, err = golibExgraph.GetEngineByName(ItemPageEngineName)
	if err != nil {
		panic(err)
	}
}

func ItemPage(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getItemPageActionInstance(ctx, req).UiFuncHandler()
}

type ItemPageAction struct {
	*uiclient.UIBaseAction
	ItemPageReq *itemPage.ItemPageReqIdl
	ItemPageRes *itemPage.ItemPageResIdl
	global      *types.CItemPageBaseData
}

func getItemPageActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &ItemPageAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		ItemPageReq:  &itemPage.ItemPageReqIdl{},
		ItemPageRes:  &itemPage.ItemPageResIdl{},
		global:       &types.CItemPageBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.ItemPageReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.global.StaticField = types.NewItemPageStaticField()
	obj.ItemPageRes.Data = new(itemPage.ItemPageRes)
	return obj
}

func (obj *ItemPageAction) ParseParams() {
	if nil == obj || nil == obj.ItemPageReq || nil == obj.ItemPageReq.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.ItemPageReq.GetData()
}

func (obj *ItemPageAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ItemPageReq || nil == obj.ItemPageReq.Data {
		return nil
	}

	itemPageReq := obj.ItemPageReq.GetData()

	arrPrivateInfo := map[string]interface{}{
		"scr_w":   itemPageReq.GetScrW(),
		"scr_h":   itemPageReq.GetScrH(),
		"scr_dip": itemPageReq.GetScrDip(),
		"q_type":  itemPageReq.GetQType(),
		"item_id": itemPageReq.GetItemId(),
	}

	return arrPrivateInfo
}

func (obj *ItemPageAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ItemPageReq {
		return false
	}

	itemId := common.Tvttt(obj.ObjRequest.GetPrivateAttr("item_id", 0), common.TTT_UINT32).(uint32)
	if itemId <= 0 {
		tbcontext.WarningF(obj.Ctx, " item_id error: %v", itemId)
		obj.Error(tiebaerror.ERR_PARAM_ERROR, tiebaerror.GetErrMsg(tiebaerror.ERR_PARAM_ERROR), false)
		return false
	}

	return true
}

func (obj *ItemPageAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ItemPageReq || nil == obj.ItemPageReq.Data {
		return false
	}
	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}

	exctx := ItemPageExgraph.NewContext(obj.Ctx, opts...)

	exctx.MustRegisterInstance(&obj.global)
	exctx.MustRegisterInstance(&obj.ItemPageRes.Data)

	err := ItemPageExgraph.Run(exctx)
	if err != nil {
		tbcontext.WarningF(obj.Ctx, "exgraph engine run fail.err:%s", err.Error())
	}

	exctx.RangeOpCost(func(opName string, cost time.Duration) {
		obj.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	return true
}

func (obj *ItemPageAction) BuildOut() {
	if nil == obj || nil == obj.ItemPageRes {
		return
	}
	if nil == obj.ItemPageRes.Error {
		//默认赋值成功
		obj.ItemPageRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	// 如果返回的json，需要更改几个字段
	if obj.global.BaseObj.StrFormat != "" {
		obj.ObjResponse.SetStructData(obj.ItemPageRes)
	} else {
		res := make(map[string]interface{})
		res["error_code"] = obj.ItemPageRes.GetError().GetErrorno()
		res["error_msg"] = obj.ItemPageRes.GetError().GetErrmsg()
		data := common.StructToMap(obj.ItemPageRes.GetData())
		for k, v := range data {
			res[k] = v
		}

		if listMap, ok := res["item_info"].(map[string]interface{}); ok {
			if rankingMap, ok := listMap["ranking"].(map[string]interface{}); ok {
				rankingMap["rankingParam"] = rankingMap["ranking_param"]
				rankingMap["rank"] = rankingMap["rank_num"]
			}
			listMap["icon_size"] = common.Tvttt(listMap["icon_size"], common.TTT_STRING)
			listMap["intro"] = obj.global.StaticField.Intro
			listMap["detail"] = obj.global.StaticField.Details
			listMap["service"] = obj.global.StaticField.Services
			if itemOptions, ok := listMap["item_options"].([]interface{}); ok {
				listMap["item_options"] = handleItemOption(itemOptions)
			}
		}

		// 处理recomment_item里面的item_options
		if recommentItems, ok := res["recommend_item"].([]interface{}); ok {
			for _, recommendItem := range recommentItems {
				if recommendItemMap, ok := recommendItem.(map[string]interface{}); ok {
					recommendItemMap["icon_size"] = common.Tvttt(recommendItemMap["icon_size"], common.TTT_STRING)
					if itemOptions, ok := recommendItemMap["item_options"].([]interface{}); ok {
						recommendItemMap["item_options"] = handleItemOption(itemOptions)
					}
				}
			}
		}


		if discussionList, ok := res["discussion_list"].([]interface{}); ok {
			for _, discussion := range discussionList {
				if dMap,ok := discussion.(map[string]interface{}); ok {
					if dMap["media"] == nil {
						dMap["media"] = make([]interface{}, 0)
					}
				}
			}
		}

		if threadList, ok := res["thread_list"].([]interface{}); ok {
			for _, thread := range threadList {
				if tMap,ok := thread.(map[string]interface{}); ok {
					if tMap["media"] == nil {
						tMap["media"] = make([]interface{}, 0)
					}
				}
			}
		}

		obj.ObjResponse.SetOutData(res)
	}

}

func (obj *ItemPageAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.ItemPageReq || nil == obj.ItemPageReq.Data {
		return
	}
}

func handleItemOption(itemOptions []interface{}) []interface{} {
	for _, itemOption := range itemOptions {
		if itemOptionMap, ok := itemOption.(map[string]interface{}); ok {
			if _, ok = itemOptionMap["value"]; ok {
				tmp := make(map[string]interface{})
				err := json.Unmarshal([]byte(common.Tvttt(itemOptionMap["value"], common.TTT_STRING).(string)), &tmp)
				if err == nil {
					itemOptionMap["value"] = tmp
				}
			}
		}
	}

	return itemOptions
}
