package forum

import (
	"context"
	getUserSign "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getUserSign"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/forum"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func GetUserSign(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getGetUserSignActionInstance(ctx, req).UiFuncHandler()
}

type GetUserSignAction struct {
	*uiclient.UIBaseAction
	GetUserSignReq *getUserSign.GetUserSignReqIdl
	GetUserSignRes *getUserSign.GetUserSignResIdl
	global         *types.GetUserSignBaseData
}

func getGetUserSignActionInstance(ctx context.Context, req ghttp.Request) *GetUserSignAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &GetUserSignAction{
		UIBaseAction:   &uiclient.UIBaseAction{},
		GetUserSignReq: &getUserSign.GetUserSignReqIdl{},
		GetUserSignRes: &getUserSign.GetUserSignResIdl{},
		global:         &types.GetUserSignBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.GetUserSignReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.GetUserSignRes.Data = new(getUserSign.GetUserSignRes)

	return obj
}

func (obj *GetUserSignAction) ParseParams() {

}

func (obj *GetUserSignAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetUserSignReq || nil == obj.GetUserSignReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.GetUserSignReq.GetData()

	from := obj.GetUserSignReq.GetData().GetFrom()
	forumIds := obj.GetUserSignReq.GetData().GetForumIds()

	arrPrivateInfo := map[string]any{
		"from":      from,
		"forum_ids": forumIds,
	}

	return arrPrivateInfo
}

func (obj *GetUserSignAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetUserSignReq {
		return false
	}

	return true
}

func (obj *GetUserSignAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetUserSignReq || nil == obj.GetUserSignReq.Data {
		return false
	}
	errNo := forum.GetUserSign(obj.Ctx, obj.global, obj.GetUserSignRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *GetUserSignAction) BuildOut() {
	if nil == obj || nil == obj.GetUserSignRes {
		return
	}
	if nil == obj.GetUserSignRes.Error {
		// 默认赋值成功
		obj.GetUserSignRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.GetUserSignRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *GetUserSignAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.GetUserSignReq || nil == obj.GetUserSignReq.Data {
		return
	}
}
