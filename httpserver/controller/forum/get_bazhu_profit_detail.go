package forum

import (
	"context"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getBazhuProfitDetail"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/getbazhuprofitdetail/adapter"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/getbazhuprofitdetail/gen"
)

type GetBazhuProfitDetailAction struct {
	*uiclient.UIBaseAction
	getBazhuProfitDetailReq *getBazhuProfitDetail.GetBazhuProfitDetailReqIdl
	getBazhuProfitDetailRes *getBazhuProfitDetail.GetBazhuProfitDetailResIdl
}

func GetBazhuProfitDetail(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getBazhuProfitDetailActionInstance(ctx, req).UiFuncHandler()
}

func getBazhuProfitDetailActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}

	obj := &GetBazhuProfitDetailAction{
		UIBaseAction:            &uiclient.UIBaseAction{},
		getBazhuProfitDetailReq: &getBazhuProfitDetail.GetBazhuProfitDetailReqIdl{},
		getBazhuProfitDetailRes: &getBazhuProfitDetail.GetBazhuProfitDetailResIdl{
			Error: &client.Error{
				Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
				Errmsg:  proto.String(""),
				Usermsg: proto.String(""),
			},
			Data: &getBazhuProfitDetail.GetBazhuProfitDetailRes{},
		},
	}
	if !obj.Init(ctx, req, obj, obj.getBazhuProfitDetailReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	return obj
}

// ParseParams 格式化参数，由private_params算子代替
func (obj *GetBazhuProfitDetailAction) ParseParams() {
	return
}

// GetPrivateInfo 获取私有参数，由private_params算子代替
func (obj *GetBazhuProfitDetailAction) GetPrivateInfo() map[string]any {
	return map[string]any{}
}

// CheckPrivate 校验私有参数，由private_params算子代替
func (obj *GetBazhuProfitDetailAction) CheckPrivate() bool {
	return true
}

func (obj *GetBazhuProfitDetailAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getBazhuProfitDetailReq == nil ||
		obj.getBazhuProfitDetailReq.Data == nil || obj.getBazhuProfitDetailRes == nil {
		return false
	}

	ll := logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService())
	defer func() {
		logging.ReleaseLogitLogger(ll)
	}()

	engCtx := resource.GetBazhuProfitDetailEngine.NewContext(obj.Ctx, []engine.ContextOption{
		engine.WithLogger(ll.AsEngineLogger()),
	}...)
	defer func() {
		engine.ReleaseContext(engCtx)
	}()

	engCtx.MustRegisterInstance(&obj.UIBaseAction)
	err := resource.GetBazhuProfitDetailEngine.Run(engCtx)

	// 算子中设置过非0错误号，使用设置的错误号直接返回
	if errnoInter := tbcontext.GetErrNo(obj.Ctx); errnoInter != nil {
		if errno := cast.ToInt(errnoInter); errno != tiebaerror.ERR_SUCCESS {
			return false
		}
	}

	// 没设置过错误号，兜底返回错误
	if err != nil {
		tbcontext.WarningF(obj.Ctx, "GetBazhuProfitDetail exgraph engine run fail, err=%v", err)
		obj.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, tiebaerror.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return false
	}

	var output getBazhuProfitDetail.GetBazhuProfitDetailRes
	ok := engCtx.MutableInstance(&output)
	if !ok {
		tbcontext.WarningF(obj.Ctx, "get getBazhuProfitDetail output fail")
		return false
	}

	obj.getBazhuProfitDetailRes.Data = &output
	return true
}

func (obj *GetBazhuProfitDetailAction) BuildOut() {
	if obj == nil || obj.getBazhuProfitDetailRes == nil {
		return
	}
	if obj.getBazhuProfitDetailRes.Error == nil {
		obj.getBazhuProfitDetailRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.getBazhuProfitDetailRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

func (obj *GetBazhuProfitDetailAction) BuildLog() {
	if obj == nil || obj.getBazhuProfitDetailRes == nil ||
		obj.getBazhuProfitDetailRes.Data == nil {
		return
	}
	return
}
