package forum

import (
	"context"
	"errors"
	"strconv"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/searchForumByTag"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"

	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/searchForumByTag/adapter"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/searchForumByTag/gen"
)

func SearchForumByTag(ctx context.Context, req ghttp.Request) ghttp.Response {
	return SearchForumByTagActionInstance(ctx, req).UiFuncHandler()
}

type SearchForumByTagAction struct {
	*uiclient.UIBaseAction
	searchForumByTagReq *searchForumByTag.SearchForumByTagReqIdl
	searchForumByTagRes *searchForumByTag.SearchForumByTagResIdl
}

func SearchForumByTagActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}

	obj := &SearchForumByTagAction{
		UIBaseAction:        &uiclient.UIBaseAction{},
		searchForumByTagReq: &searchForumByTag.SearchForumByTagReqIdl{},
		searchForumByTagRes: &searchForumByTag.SearchForumByTagResIdl{},
	}
	if !obj.Init(ctx, req, obj, obj.searchForumByTagReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	return obj
}

// ParseParams 格式化参数，由private_params算子代替
func (s *SearchForumByTagAction) ParseParams() {
	return
}

// GetPrivateInfo 获取私有参数，由private_params算子代替
func (s *SearchForumByTagAction) GetPrivateInfo() map[string]any {
	return map[string]any{}
}

// CheckPrivate 校验私有参数，由private_params算子代替
func (s *SearchForumByTagAction) CheckPrivate() bool {
	return true
}

func (s *SearchForumByTagAction) Execute() bool {
	if s == nil || s.ObjRequest == nil || s.searchForumByTagReq == nil ||
		s.searchForumByTagReq.Data == nil || s.searchForumByTagRes == nil {
		return false
	}

	ll := logging.WrapLogit(s.Ctx, tbcontext.GetLogidString(s.Ctx), loggerResource.GetLoggerService())
	defer func() {
		logging.ReleaseLogitLogger(ll)
	}()

	engCtx := resource.SearchForumByTag.NewContext(s.Ctx, []engine.ContextOption{
		engine.WithLogger(ll.AsEngineLogger()),
	}...)
	defer func() {
		engine.ReleaseContext(engCtx)
	}()

	engCtx.MustRegisterInstance(&s.UIBaseAction)
	err := resource.SearchForumByTag.Run(engCtx)

	// 记录算子耗时
	engCtx.RangeOpCost(func(opName string, cost time.Duration) {
		s.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	// 算子中设置过错误号，使用算子设置的错误号作为返回
	var state engine.State
	if errors.As(err, &state) {
		tbcontext.WarningF(s.Ctx, "exgraph execute fail, code=%d, errmsg=%s", state.Code(), state.Error())
		s.Error(state.Code(), state.Error(), false)
		return false
	}

	// 未设置过错误号，兜底返回错误信息
	if err != nil {
		tbcontext.WarningF(s.Ctx, "exgraph execute fail, err=%v", err)
		s.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, stcdefine.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return false
	}

	var output searchForumByTag.SearchForumByTagRes
	ok := engCtx.MutableInstance(&output)
	if !ok {
		tbcontext.WarningF(s.Ctx, "get test_path output fail")
		return false
	}
	s.searchForumByTagRes.Data = &output
	return true
}

func (s *SearchForumByTagAction) BuildOut() {
	if s == nil || s.searchForumByTagRes == nil {
		return
	}
	if s.searchForumByTagRes.Error == nil {
		s.searchForumByTagRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	s.ObjResponse.SetOutData(
		map[string]any{
			"data": s.searchForumByTagRes.GetData(),
		},
	)
	return
}

func (s *SearchForumByTagAction) BuildLog() {
	return
}
