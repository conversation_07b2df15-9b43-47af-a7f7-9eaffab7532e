package material

import (
	"context"

	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	materialProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/material"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/material"
)

func Bang(ctx context.Context, req ghttp.Request) ghttp.Response {
	return bangActionInstance(ctx, req).UiFuncHandler()
}

type bangAction struct {
	*uiclient.UIBaseAction
	bangReq  *materialProto.ForumBangReqIdl
	bangRes  *materialProto.ForumBangResIdl
	baseData *types.ForumBangBaseData
}

func bangActionInstance(ctx context.Context, req ghttp.Request) *bangAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &bangAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		bangReq:      &materialProto.ForumBangReqIdl{},
		bangRes: &materialProto.ForumBangResIdl{
			Error: &client.Error{},
			Data:  &materialProto.ForumBangRes{},
		},
		baseData: &types.ForumBangBaseData{},
	}
	// 通过Init方法赋值
	if !obj.Init(ctx, req, obj, obj.bangReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

func (obj *bangAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.bangReq == nil || obj.bangReq.Data == nil {
		return
	}
	obj.baseData.Request = obj.bangReq.GetData()
}

func (obj *bangAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.bangReq == nil || obj.bangReq.Data == nil {
		return nil
	}
	privateInfo := map[string]any{
		"check_login": false,
		"need_login":  false,
	}
	return privateInfo
}

func (obj *bangAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.bangReq == nil {
		return false
	}
	return true
}

func (obj *bangAction) BuildOut() {
	if obj == nil || obj.bangRes == nil {
		return
	}
	if obj.bangRes.Error == nil {
		obj.bangRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.bangRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

func (obj *bangAction) BuildLog() {
	return
}

func (obj *bangAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.bangReq == nil || obj.bangReq.Data == nil {
		return false
	}
	errno := service.MaterialForumBang(obj.Ctx, obj.baseData, obj.bangRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}
