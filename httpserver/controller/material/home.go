package material

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	client "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	materialProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/material"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/material"
)

// Home 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func Home(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 通过homeActionInstance实例化homeAction类，并调用UiFuncHandler()方法给ghttp.Response赋值
	return homeActionInstance(ctx, req).UiFuncHandler()
}

// homeAction 服务相关结构体
type homeAction struct {
	// 组合UIBaseAction基础类，并实现基类方法
	*uiclient.UIBaseAction
	// 通过proto定义输入数据
	homeReq *materialProto.HomeReqIdl
	// 通过proto定义输出数据
	homeRes *materialProto.HomeResIdl
	// 通过types封装跟service层交互的数据
	baseData *types.HomeBaseData
}

// homeActionInstance 创建一个homeAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回homeAction实例
func homeActionInstance(ctx context.Context, req ghttp.Request) *homeAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &homeAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		homeReq: &materialProto.HomeReqIdl{
			Data: &materialProto.HomeReq{},
		},
		homeRes: &materialProto.HomeResIdl{
			Data:  &materialProto.HomeRes{},
			Error: &client.Error{},
		},
		baseData: &types.HomeBaseData{},
	}
	// 通过Init方法赋值
	if !obj.Init(ctx, req, obj, obj.homeReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

// ParseParams 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *homeAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.homeReq == nil || obj.homeReq.Data == nil {
		return
	}
	obj.baseData.Request = obj.homeReq.GetData()
}

// GetPrivateInfo 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *homeAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.homeReq == nil || obj.homeReq.Data == nil {
		return nil
	}
	privateInfo := map[string]any{
		//"check_login": false,
		//"need_login":  false,
	}
	return privateInfo
}

// CheckPrivate 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *homeAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.homeReq == nil {
		return false
	}
	return true
}

// BuildOut 构建输出数据的方法
// @Param：无
// @Return：无
func (obj *homeAction) BuildOut() {
	if obj == nil || obj.homeRes == nil {
		return
	}
	if obj.homeRes.Error == nil {
		obj.homeRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.homeRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

// BuildLog 创建日志
// @Param：无
// @Return：无
func (obj *homeAction) BuildLog() {
	return
}

// Execute 执行homeAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *homeAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.homeReq == nil || obj.homeReq.Data == nil {
		return false
	}
	errno := service.MaterialHome(obj.Ctx, obj.baseData, obj.homeRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}
