package material

import (
	"context"
	"google.golang.org/protobuf/proto"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/material"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	client "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	tbProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/material"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// ThreadBang 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func ThreadBang(ctx context.Context, req ghttp.Request) ghttp.Response {
	return threadBangActionInstance(ctx, req).UiFuncHandler()
}

// threadBangAction 服务相关结构体
type threadBangAction struct {
	*uiclient.UIBaseAction
	threadBangReq *tbProto.ThreadBangReqIdl
	threadBangRes *tbProto.ThreadBangResIdl
	baseData      *types.ThreadBangBaseData
}

// threadBangActionInstance 创建一个threadBangAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回threadBangAction实例
func threadBangActionInstance(ctx context.Context, req ghttp.Request) *threadBangAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &threadBangAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		threadBangReq: &tbProto.ThreadBangReqIdl{
			Data: &tbProto.ThreadBangReq{},
		},
		threadBangRes: &tbProto.ThreadBangResIdl{
			Data:  &tbProto.ThreadBangRes{},
			Error: &client.Error{},
		},
		baseData: &types.ThreadBangBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.threadBangReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

// ParseParams 获取数据并赋值给baseData.Request
func (obj *threadBangAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.threadBangReq == nil || obj.threadBangReq.Data == nil {
		return
	}
	obj.baseData.Request = obj.threadBangReq.GetData()
}

// GetPrivateInfo 设置私有信息
func (obj *threadBangAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.threadBangReq == nil || obj.threadBangReq.Data == nil {
		return nil
	}
	privateInfo := map[string]any{
		//"check_login": true,
		//"need_login":  false,
	}
	return privateInfo
}

// CheckPrivate 校验合法性
func (obj *threadBangAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.threadBangReq == nil {
		return false
	}
	return true
}

// BuildOut 构建输出数据的方法
func (obj *threadBangAction) BuildOut() {
	if obj == nil || obj.threadBangRes == nil {
		return
	}
	if obj.threadBangRes.Error == nil {
		obj.threadBangRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.threadBangRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

// BuildLog 创建日志
func (obj *threadBangAction) BuildLog() {
	return
}

// Execute 执行threadBangAction的逻辑
func (obj *threadBangAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.threadBangReq == nil || obj.threadBangReq.Data == nil {
		return false
	}
	errno := service.ThreadBang(obj.Ctx, obj.baseData, obj.threadBangRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}
