package threadalbum

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	client "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	threadalbumProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/threadalbum/getAlbumListForManager"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/threadalbum"
)

// GetAlbumListForManager 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func GetAlbumListForManager(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getAlbumListForManagerActionInstance(ctx, req).UiFuncHandler()
}

// getAlbumListForManagerAction 服务相关结构体
type getAlbumListForManagerAction struct {
	*uiclient.UIBaseAction
	req      *threadalbumProto.GetAlbumListForManagerReqIdl
	res      *threadalbumProto.AlbumListForManagerResIdl
	baseData *types.ThreadalbumBaseData
}

// getAlbumListForManagerActionInstance 创建一个getAlbumListForManagerAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回getAlbumListForManagerAction实例
func getAlbumListForManagerActionInstance(ctx context.Context, req ghttp.Request) *getAlbumListForManagerAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &getAlbumListForManagerAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		req:          &threadalbumProto.GetAlbumListForManagerReqIdl{},
		res: &threadalbumProto.AlbumListForManagerResIdl{
			Data:  &threadalbumProto.AlbumListForManagerRes{},
			Error: &client.Error{},
		},
		baseData: &types.ThreadalbumBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.req) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

// ParseParams 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *getAlbumListForManagerAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.req == nil || obj.req.Data == nil {
		return
	}
	obj.baseData.Request = obj.req.GetData()
}

// GetPrivateInfo 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *getAlbumListForManagerAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.req == nil || obj.req.Data == nil {
		return nil
	}
	privateInfo := map[string]any{
		"check_login": true,
		"need_login":  true,
	}
	return privateInfo
}

// CheckPrivate 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *getAlbumListForManagerAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.req == nil {
		return false
	}
	return true
}

// BuildOut 构建输出数据的方法
// @Param：无
// @Return：无
func (obj *getAlbumListForManagerAction) BuildOut() {
	if obj == nil || obj.res == nil {
		return
	}
	if obj.res.Error == nil {
		obj.res.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.res.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

// BuildLog 创建日志
// @Param：无
// @Return：无
func (obj *getAlbumListForManagerAction) BuildLog() {
	return
}

// Execute 执行getAlbumListForManagerAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *getAlbumListForManagerAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.req == nil || obj.req.Data == nil {
		return false
	}
	errno := service.GetAlbumListForManager(obj.Ctx, obj.baseData, obj.res)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}
