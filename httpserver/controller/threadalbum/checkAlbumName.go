package threadalbum

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/threadalbum/checkAlbumName"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/threadalbum"
)

func CheckAlbumName(ctx context.Context, req ghttp.Request) ghttp.Response {
	return checkAlbumNameActionInstance(ctx, req).UiFuncHandler()
}

type checkAlbumNameAction struct {
	*uiclient.UIBaseAction
	CheckAlbumNameReq *checkAlbumName.CheckAlbumNameReqIdl
	checkAlbumNameRes *checkAlbumName.CheckAlbumNameResIdl
	global            *types.CheckAlbumNameData
}

func checkAlbumNameActionInstance(ctx context.Context, req ghttp.Request) *checkAlbumNameAction {
	if nil == ctx || nil == req {
		return nil
	}

	obj := &checkAlbumNameAction{
		UIBaseAction:      &uiclient.UIBaseAction{},
		CheckAlbumNameReq: &checkAlbumName.CheckAlbumNameReqIdl{},
		checkAlbumNameRes: &checkAlbumName.CheckAlbumNameResIdl{},
		global:            &types.CheckAlbumNameData{},
	}

	if !obj.Init(ctx, req, obj, obj.CheckAlbumNameReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}

	obj.global.BaseObj = obj.UIBaseAction
	obj.checkAlbumNameRes.Data = new(checkAlbumName.DataRes)
	tbcontext.NoticeF(ctx, "GetThreadScoreItemList start333, obj:%s", common.ToString(obj))
	return obj
}

func (obj *checkAlbumNameAction) ParseParams() {
	if nil == obj || nil == obj.CheckAlbumNameReq || nil == obj.checkAlbumNameRes.Data || nil == obj.ObjRequest {
		tbcontext.WarningF(obj.Ctx, "ParseParams error")
		return
	}
	if obj.CheckAlbumNameReq.GetData().GetForumId() <= 0 || obj.CheckAlbumNameReq.GetData().GetAlbumTitle() == "" {
		tbcontext.WarningF(obj.Ctx, "ParseParams error")
	}

	obj.global.Request = obj.CheckAlbumNameReq.GetData()
}

func (obj *checkAlbumNameAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.CheckAlbumNameReq || nil == obj.checkAlbumNameRes.Data {
		tbcontext.WarningF(obj.Ctx, "GetPrivateInfo error")
		return nil
	}

	// privateInfo := map[string]interface{}{
	// 	"check_login": true,
	// 	"need_login":  true,
	// }
	privateInfo := map[string]interface{}{}
	return privateInfo
}

func (obj *checkAlbumNameAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.CheckAlbumNameReq {
		tbcontext.WarningF(obj.Ctx, "CheckPrivate error")
		return false
	}

	return true
}

func (obj *checkAlbumNameAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.CheckAlbumNameReq || nil == obj.CheckAlbumNameReq.Data {
		tbcontext.WarningF(obj.Ctx, "Execute error")
		return false
	}

	// 处理参数
	obj.global.AlbumTitle = obj.CheckAlbumNameReq.GetData().GetAlbumTitle()
	obj.global.ForumID = obj.CheckAlbumNameReq.GetData().GetForumId()
	// tbcontext.NoticeF(obj.Ctx, "obj.global.info:%s", common.ToString(obj.global))

	errNo := threadalbum.CheckAlbumName(obj.Ctx, obj.global, obj.checkAlbumNameRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *checkAlbumNameAction) BuildOut() {
	if nil == obj || nil == obj.checkAlbumNameRes {
		return
	}

	if nil == obj.checkAlbumNameRes.Error {
		// 默认赋值成功
		obj.checkAlbumNameRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.checkAlbumNameRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *checkAlbumNameAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.CheckAlbumNameReq || nil == obj.checkAlbumNameRes.Data {
		return
	}
}
