package threadalbum

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	client "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	threadalbumProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/threadalbum/getThreadListForAdd"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/threadalbum"
)

// GetThreadListForAdd 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func GetThreadListForAdd(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getThreadListForAddActionInstance(ctx, req).UiFuncHandler()
}

// getThreadListForAddAction 服务相关结构体
type getThreadListForAddAction struct {
	*uiclient.UIBaseAction
	req      *threadalbumProto.GetThreadListForAddrReqIdl
	res      *threadalbumProto.ThreadListForAddrResIdl
	baseData *types.ThreadalbumForAddBaseData
}

// getThreadListForAddActionInstance 创建一个getThreadListForAddAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回getThreadListForAddAction实例
func getThreadListForAddActionInstance(ctx context.Context, req ghttp.Request) *getThreadListForAddAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &getThreadListForAddAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		req:          &threadalbumProto.GetThreadListForAddrReqIdl{},
		res: &threadalbumProto.ThreadListForAddrResIdl{
			Data:  &threadalbumProto.ThreadListForAddrRes{},
			Error: &client.Error{},
		},
		baseData: &types.ThreadalbumForAddBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.req) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

// ParseParams 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *getThreadListForAddAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.req == nil || obj.req.Data == nil {
		return
	}
	obj.baseData.Request = obj.req.GetData()
}

// GetPrivateInfo 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *getThreadListForAddAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.req == nil || obj.req.Data == nil {
		return nil
	}
	privateInfo := map[string]any{
		"check_login": true,
		"need_login":  true,
	}
	return privateInfo
}

// CheckPrivate 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *getThreadListForAddAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.req == nil {
		return false
	}
	return true
}

// BuildOut 构建输出数据的方法
// @Param：无
// @Return：无
func (obj *getThreadListForAddAction) BuildOut() {
	if obj == nil || obj.res == nil {
		return
	}
	if obj.res.Error == nil {
		obj.res.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.res.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

// BuildLog 创建日志
// @Param：无
// @Return：无
func (obj *getThreadListForAddAction) BuildLog() {
	return
}

// Execute 执行getThreadListForAddAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *getThreadListForAddAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.req == nil || obj.req.Data == nil {
		return false
	}
	errno := service.GetThreadListForAdd(obj.Ctx, obj.baseData, obj.res)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}
