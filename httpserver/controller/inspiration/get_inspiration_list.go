package inspiration

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/inspiration/getInspirationList"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/inspiration"
)

func GetInspirationList(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getInspirationListActionInstance(ctx, req).UiFuncHandler()
}

type getInspirationListAction struct {
	*uiclient.UIBaseAction
	getInspirationListReq *getInspirationList.GetInspirationListReqIdl
	getInspirationListRes *getInspirationList.GetInspirationListResIdl
	global                *types.GetInspirationListBaseData
}

func getInspirationListActionInstance(ctx context.Context, req ghttp.Request) *getInspirationListAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &getInspirationListAction{
		UIBaseAction:          &uiclient.UIBaseAction{},
		getInspirationListReq: &getInspirationList.GetInspirationListReqIdl{},
		getInspirationListRes: &getInspirationList.GetInspirationListResIdl{},
		global:                &types.GetInspirationListBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getInspirationListReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getInspirationListRes.Data = new(getInspirationList.GetInspirationListRes)
	return obj
}

func (obj *getInspirationListAction) ParseParams() {
	if nil == obj || nil == obj.getInspirationListReq || nil == obj.getInspirationListRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getInspirationListReq.GetData()
}

func (obj *getInspirationListAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getInspirationListReq || nil == obj.getInspirationListReq.Data {
		return nil
	}

	forumId := obj.getInspirationListReq.GetData().GetForumId()
	pn := obj.getInspirationListReq.GetData().GetPn()
	rn := obj.getInspirationListReq.GetData().GetRn()
	if pn == 0 {
		pn = 1
	}
	if rn == 0 {
		rn = 10
	}

	arrPrivateInfo := map[string]any{
		"check_login": true, // 默认需要登录
		"forum_id":    forumId,
		"pn":          pn,
		"rn":          rn,
	}

	return arrPrivateInfo
}

func (obj *getInspirationListAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getInspirationListReq {
		return false
	}

	return true
}

func (obj *getInspirationListAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getInspirationListReq || nil == obj.getInspirationListReq.Data {
		return false
	}
	errNo := inspiration.DoGetInspirationList(obj.Ctx, obj.global, obj.getInspirationListRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *getInspirationListAction) BuildOut() {
	if nil == obj || nil == obj.getInspirationListRes {
		return
	}
	if nil == obj.getInspirationListRes.Error {
		// 默认赋值成功
		obj.getInspirationListRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getInspirationListRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getInspirationListAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getInspirationListReq || nil == obj.getInspirationListReq.Data {
		return
	}
}
