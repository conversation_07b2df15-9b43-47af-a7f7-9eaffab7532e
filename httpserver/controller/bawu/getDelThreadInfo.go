package bawu

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"

	clientProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	uiProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/bawu/getDelThreadInfo"

	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/bawu"
)

type getDelThreadInfoAction struct {
	*uiclient.UIBaseAction
	getDelThreadInfoReq *uiProto.GetDelThreadInfoReqIdl
	getDelThreadInfoRes *uiProto.GetDelThreadInfoResIdl
	global              *types.GetDelThreadInfoBaseData
}

func GetDelThreadInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getDelThreadInfoActionInstance(ctx, req).UiFuncHandler()
}

func getDelThreadInfoActionInstance(ctx context.Context, req ghttp.Request) *getDelThreadInfoAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &getDelThreadInfoAction{
		UIBaseAction:        &uiclient.UIBaseAction{},
		getDelThreadInfoReq: &uiProto.GetDelThreadInfoReqIdl{},
		getDelThreadInfoRes: &uiProto.GetDelThreadInfoResIdl{},
		global:              &types.GetDelThreadInfoBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getDelThreadInfoReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getDelThreadInfoRes.Data = &uiProto.GetDelThreadInfoData{}
	return obj
}

func (obj *getDelThreadInfoAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.getDelThreadInfoReq == nil || obj.getDelThreadInfoReq.Data == nil {
		return
	}
	obj.global.Request = obj.getDelThreadInfoReq.GetData()
}

func (obj *getDelThreadInfoAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.getDelThreadInfoReq == nil || obj.getDelThreadInfoReq.Data == nil {
		return nil
	}
	// obj.ObjRequest.AddStrategy("check_sign", false)
	privateInfo := map[string]any{
		"check_login": true,
		"need_login":  true,
	}
	return privateInfo
}

func (obj *getDelThreadInfoAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getDelThreadInfoReq == nil {
		return false
	}
	// 校验call_from
	if obj.getDelThreadInfoReq.GetData().GetCallFrom() != types.BawuCallFrom && obj.getDelThreadInfoReq.GetData().GetCallFrom() != types.PmcCallFrom {
		obj.Error(tiebaerror.ERR_PARAM_ERROR, stcdefine.GetErrMsg(tiebaerror.ERR_PARAM_ERROR), true)
		return false
	}
	// 校验query_type
	if obj.getDelThreadInfoReq.GetData().GetQueryType() != types.QueryTypeThread && obj.getDelThreadInfoReq.GetData().GetQueryType() != types.QueryTypePost &&
		obj.getDelThreadInfoReq.GetData().GetQueryType() != types.QueryTypeLzl {
		obj.Error(tiebaerror.ERR_PARAM_ERROR, stcdefine.GetErrMsg(tiebaerror.ERR_PARAM_ERROR), true)
		return false

	}
	// 校验帖子id或者回复id
	if (obj.getDelThreadInfoReq.GetData().GetQueryType() == types.QueryTypeThread && obj.getDelThreadInfoReq.GetData().GetThreadId() == 0) ||
		(obj.getDelThreadInfoReq.GetData().GetQueryType() == types.QueryTypePost && obj.getDelThreadInfoReq.GetData().GetPostId() == 0) ||
		(obj.getDelThreadInfoReq.GetData().GetQueryType() == types.QueryTypeLzl && obj.getDelThreadInfoReq.GetData().GetPostId() == 0) {
		obj.Error(tiebaerror.ERR_PARAM_ERROR, stcdefine.GetErrMsg(tiebaerror.ERR_PARAM_ERROR), true)
		return false
	}
	return true
}

func (obj *getDelThreadInfoAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getDelThreadInfoReq == nil || obj.getDelThreadInfoReq.Data == nil {
		return false
	}

	obj.global.UserID = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	errno := bawu.GetDelThreadInfo(obj.Ctx, obj.global, obj.getDelThreadInfoRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}

func (obj *getDelThreadInfoAction) BuildOut() {
	if obj == nil || obj.getDelThreadInfoRes == nil {
		return
	}
	if obj.getDelThreadInfoRes.Error == nil {
		obj.getDelThreadInfoRes.Error = &clientProto.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}

	outData := map[string]any{
		"data": obj.getDelThreadInfoRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getDelThreadInfoAction) BuildLog() {
	if obj == nil || obj.Ctx == nil || obj.Req == nil || obj.getDelThreadInfoReq == nil || obj.getDelThreadInfoReq.Data == nil {
		return
	}
}
