package bawu

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"

	clientProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	uiProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/bawu/getDeleteReason"

	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/bawu"
)

type getDeleteReasonAction struct {
	*uiclient.UIBaseAction
	getDeleteReasonReq *uiProto.GetDeleteReasonReqIdl
	getDeleteReasonRes *uiProto.GetDeleteReasonResIdl
	global             *types.GetDeleteReasonBaseData
}

func GetDeleteReason(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getDeleteReasonActionInstance(ctx, req).UiFuncHandler()
}

func getDeleteReasonActionInstance(ctx context.Context, req ghttp.Request) *getDeleteReasonAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &getDeleteReasonAction{
		UIBaseAction:       &uiclient.UIBaseAction{},
		getDeleteReasonReq: &uiProto.GetDeleteReasonReqIdl{},
		getDeleteReasonRes: &uiProto.GetDeleteReasonResIdl{},
		global:             &types.GetDeleteReasonBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getDeleteReasonReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getDeleteReasonRes.Data = &uiProto.GetDeleteReasonData{}
	return obj
}

func (obj *getDeleteReasonAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.getDeleteReasonReq == nil || obj.getDeleteReasonReq.Data == nil {
		return
	}
	obj.global.Request = obj.getDeleteReasonReq.GetData()
}

func (obj *getDeleteReasonAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.getDeleteReasonReq == nil || obj.getDeleteReasonReq.Data == nil {
		return nil
	}
	obj.ObjRequest.AddStrategy("check_sign", false)
	privateInfo := map[string]any{
		"check_login": true,
		"need_login":  true,
	}
	return privateInfo
}

func (obj *getDeleteReasonAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getDeleteReasonReq == nil {
		return false
	}
	return true
}

func (obj *getDeleteReasonAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getDeleteReasonReq == nil || obj.getDeleteReasonReq.Data == nil {
		return false
	}

	obj.global.UserID = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	errno := bawu.GetDeleteReason(obj.Ctx, obj.global, obj.getDeleteReasonRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}

func (obj *getDeleteReasonAction) BuildOut() {
	if obj == nil || obj.getDeleteReasonRes == nil {
		return
	}
	if obj.getDeleteReasonRes.Error == nil {
		obj.getDeleteReasonRes.Error = &clientProto.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}

	outData := map[string]any{
		"data": obj.getDeleteReasonRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getDeleteReasonAction) BuildLog() {
	if obj == nil || obj.Ctx == nil || obj.Req == nil || obj.getDeleteReasonReq == nil || obj.getDeleteReasonReq.Data == nil {
		return
	}
}
