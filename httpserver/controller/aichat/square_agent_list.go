package aichat

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/squareAgentList"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

func SquareAgentList(ctx context.Context, req ghttp.Request) ghttp.Response {
	return SquareAgentListActionInstance(ctx, req).UiFuncHandler()
}

type SquareAgentListAction struct {
	*uiclient.UIBaseAction
	SquareAgentListReq *squareAgentList.SquareAgentListReqIdl
	SquareAgentListRes *squareAgentList.SquareAgentListResIdl
	global             *types.SquareAgentListBaseData
}

func SquareAgentListActionInstance(ctx context.Context, req ghttp.Request) *SquareAgentListAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &SquareAgentListAction{
		UIBaseAction:       &uiclient.UIBaseAction{},
		SquareAgentListReq: &squareAgentList.SquareAgentListReqIdl{},
		SquareAgentListRes: &squareAgentList.SquareAgentListResIdl{},
		global:             &types.SquareAgentListBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.SquareAgentListReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.SquareAgentListRes.Data = new(squareAgentList.SquareAgentListRes)
	return obj
}

func (obj *SquareAgentListAction) ParseParams() {
	if nil == obj || nil == obj.SquareAgentListReq || nil == obj.SquareAgentListRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.SquareAgentListReq.GetData()
}

func (obj *SquareAgentListAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.SquareAgentListReq || nil == obj.SquareAgentListReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	pn := obj.SquareAgentListReq.GetData().GetPn()
	rn := obj.SquareAgentListReq.GetData().GetRn()
	source := obj.SquareAgentListReq.GetData().GetSource()

	arrPrivateInfo := map[string]any{
		"pn":     pn,
		"rn":     rn,
		"source": source,
	}

	return arrPrivateInfo
}

func (obj *SquareAgentListAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.SquareAgentListReq {
		return false
	}

	return true
}

func (obj *SquareAgentListAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.SquareAgentListReq || nil == obj.SquareAgentListReq.Data {
		return false
	}
	errNo := aichat.SquareAgentList(obj.Ctx, obj.global, obj.SquareAgentListRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *SquareAgentListAction) BuildOut() {
	if nil == obj || nil == obj.SquareAgentListRes {
		return
	}
	if nil == obj.SquareAgentListRes.Error {
		// 默认赋值成功
		obj.SquareAgentListRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.SquareAgentListRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *SquareAgentListAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.SquareAgentListReq || nil == obj.SquareAgentListReq.Data {
		return
	}
}
