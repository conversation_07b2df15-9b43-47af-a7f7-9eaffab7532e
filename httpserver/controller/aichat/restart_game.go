package aichat

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/restartGame"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

func RestartGame(ctx context.Context, req ghttp.Request) ghttp.Response {
	return restartGameActionInstance(ctx, req).UiFuncHandler()
}

type restartGameAction struct {
	*uiclient.UIBaseAction
	restartGameReq *restartGame.RestartGameReqIdl
	restartGameRes *restartGame.RestartGameResIdl
	global           *types.RestartGameBaseData
}

func restartGameActionInstance(ctx context.Context, req ghttp.Request) *restartGameAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &restartGameAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		restartGameReq: &restartGame.RestartGameReqIdl{},
		restartGameRes: &restartGame.RestartGameResIdl{},
		global:           &types.RestartGameBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.restartGameReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.restartGameRes.Data = new(restartGame.RestartGameRes)
	return obj
}

func (obj *restartGameAction) ParseParams() {
	if nil == obj || nil == obj.restartGameReq || nil == obj.restartGameRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.restartGameReq.GetData()
}
func (obj *restartGameAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.restartGameReq || nil == obj.restartGameReq.Data {
		return nil
	}
	// 从调用入参中获取需要的参数
	plotId := obj.restartGameReq.GetData().GetPlotId()

	arrPrivateInfo := map[string]any{
		"plot_id": plotId, //剧情id
	}

	return arrPrivateInfo
}

func (obj *restartGameAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.restartGameReq {
		return false
	}

	return true
}

func (obj *restartGameAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.restartGameReq || nil == obj.restartGameReq.Data {
		return false
	}
	//下游service内容
	errNo := aichat.DoRestartGame(obj.Ctx, obj.global, obj.restartGameRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *restartGameAction) BuildOut() {
	if nil == obj || nil == obj.restartGameRes {
		return
	}
	if nil == obj.restartGameRes.Error {
		// 默认赋值成功
		obj.restartGameRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.restartGameRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *restartGameAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.restartGameReq || nil == obj.restartGameReq.Data {
		return
	}
}