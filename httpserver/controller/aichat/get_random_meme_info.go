package aichat

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getRandomMemeInfo"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

func GetRandomMemeInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getRandomMemeInfoActionInstance(ctx, req).UiFuncHandler()
}

type sendRandomMemeMsgAction struct {
	*uiclient.UIBaseAction
	getRandomMemeInfoReq *getRandomMemeInfo.GetRandomMemeInfoReqIdl
	getRandomMemeInfoRes *getRandomMemeInfo.GetRandomMemeInfoResIdl
	global               *types.GetRandomMemeInfoBaseData
}

func getRandomMemeInfoActionInstance(ctx context.Context, req ghttp.Request) *sendRandomMemeMsgAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &sendRandomMemeMsgAction{
		UIBaseAction:         &uiclient.UIBaseAction{},
		getRandomMemeInfoReq: &getRandomMemeInfo.GetRandomMemeInfoReqIdl{},
		getRandomMemeInfoRes: &getRandomMemeInfo.GetRandomMemeInfoResIdl{},
		global:               &types.GetRandomMemeInfoBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getRandomMemeInfoReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getRandomMemeInfoRes.Data = new(getRandomMemeInfo.GetRandomMemeInfoRes)
	return obj
}

func (obj *sendRandomMemeMsgAction) ParseParams() {
	if nil == obj || nil == obj.getRandomMemeInfoReq || nil == obj.getRandomMemeInfoRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getRandomMemeInfoReq.GetData()
}

func (obj *sendRandomMemeMsgAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getRandomMemeInfoReq || nil == obj.getRandomMemeInfoReq.Data {
		return nil
	}

	// 临时关掉接口的签名校验
	// obj.ObjRequest.AddStrategy("check_sign", false)

	packageID := obj.getRandomMemeInfoReq.GetData().GetMemePackageId()
	arrPrivateInfo := map[string]any{
		//"check_login":     true,
		"meme_package_id": packageID,
	}

	return arrPrivateInfo
}

func (obj *sendRandomMemeMsgAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getRandomMemeInfoReq {
		return false
	}

	return true
}

func (obj *sendRandomMemeMsgAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getRandomMemeInfoReq || nil == obj.getRandomMemeInfoReq.Data {
		return false
	}
	errNo := aichat.DoGetRandomMeme(obj.Ctx, obj.global, obj.getRandomMemeInfoRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *sendRandomMemeMsgAction) BuildOut() {
	if nil == obj || nil == obj.getRandomMemeInfoRes {
		return
	}
	if nil == obj.getRandomMemeInfoRes.Error {
		// 默认赋值成功
		obj.getRandomMemeInfoRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getRandomMemeInfoRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *sendRandomMemeMsgAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getRandomMemeInfoReq || nil == obj.getRandomMemeInfoReq.Data {
		return
	}
}
