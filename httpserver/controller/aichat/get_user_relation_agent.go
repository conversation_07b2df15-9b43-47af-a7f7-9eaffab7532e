/**
 * @Author: gong<PERSON>iyang
 * @Description:
 * @File:  get_user_relation_agent
 * @Date: 2025/02/10 11:08
 */

package aichat

import (
	"context"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getUserRelationAgent"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/getuserrelationagent/adapter"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/getuserrelationagent/gen"
)

type GetUserRelationAgentAction struct {
	*uiclient.UIBaseAction
	getUserRelationAgentReq *getUserRelationAgent.GetUserRelationAgentReqIdl
	getUserRelationAgentRes *getUserRelationAgent.GetUserRelationAgentResIdl
}

func GetUserRelationAgent(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getUserRelationAgentActionInstance(ctx, req).UiFuncHandler()
}

func getUserRelationAgentActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}

	obj := &GetUserRelationAgentAction{
		UIBaseAction:            &uiclient.UIBaseAction{},
		getUserRelationAgentReq: &getUserRelationAgent.GetUserRelationAgentReqIdl{},
		getUserRelationAgentRes: &getUserRelationAgent.GetUserRelationAgentResIdl{},
	}
	if !obj.Init(ctx, req, obj, obj.getUserRelationAgentReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	return obj
}

func (obj *GetUserRelationAgentAction) ParseParams() {
	return
}

func (obj *GetUserRelationAgentAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.getUserRelationAgentReq == nil ||
		obj.getUserRelationAgentReq.Data == nil {
		return nil
	}
	reqData := obj.getUserRelationAgentReq.GetData()
	privateInfo := map[string]any{
		"pn": reqData.GetPn(),
		"rn": reqData.GetRn(),
	}
	return privateInfo
}

func (obj *GetUserRelationAgentAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getUserRelationAgentReq == nil ||
		obj.getUserRelationAgentReq.Data == nil {
		return false
	}
	return true
}

func (obj *GetUserRelationAgentAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getUserRelationAgentReq == nil ||
		obj.getUserRelationAgentReq.Data == nil {
		return false
	}

	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}
	engCtx := resource.GetUserRelationAgentEngine.NewContext(obj.Ctx, opts...)
	defer func() {
		engine.ReleaseContext(engCtx)
	}()

	engCtx.MustRegisterInstance(&obj.UIBaseAction)
	err := resource.GetUserRelationAgentEngine.Run(engCtx)

	// 算子中设置过非0错误号，使用设置的错误号直接返回
	if errnoInter := tbcontext.GetErrNo(obj.Ctx); errnoInter != nil {
		if errno := cast.ToInt(errnoInter); errno != tiebaerror.ERR_SUCCESS {
			return false
		}
	}

	// 没设置过错误号，兜底返回错误
	if err != nil {
		tbcontext.WarningF(obj.Ctx, "getUserRelationAgent exgraph engine run fail, err=%v", err)
		obj.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, tiebaerror.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return false
	}

	var output getUserRelationAgent.GetUserRelationAgentRes
	ok := engCtx.MutableInstance(&output)
	if !ok {
		tbcontext.WarningF(obj.Ctx, "get getUserRelationAgent output fail")
		return false
	}

	obj.getUserRelationAgentRes.Data = &output
	return true
}

func (obj *GetUserRelationAgentAction) BuildOut() {
	if obj == nil || obj.getUserRelationAgentRes == nil {
		return
	}
	if obj.getUserRelationAgentRes.Error == nil {
		obj.getUserRelationAgentRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.getUserRelationAgentRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

func (obj *GetUserRelationAgentAction) BuildLog() {
	if obj == nil || obj.getUserRelationAgentRes == nil ||
		obj.getUserRelationAgentRes.Data == nil {
		return
	}
	return
}
