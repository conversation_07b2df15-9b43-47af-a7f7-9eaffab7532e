package aichat

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getMyChatBots"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

func GetMyChatBots(ctx context.Context, req ghttp.Request) ghttp.Response {
	return geteMyChatBotsActionctionInstance(ctx, req).UiFuncHandler()
}

type geteMyChatBotsAction struct {
	*uiclient.UIBaseAction
	getMyChatBotsReq *getMyChatBots.GetMyChatBotsReqIdl
	getMyChatBotsRes *getMyChatBots.GetMyChatBotsResIdl
	global           *types.GetMyChatBotsBaseData
}

func geteMyChatBotsActionctionInstance(ctx context.Context, req ghttp.Request) *geteMyChatBotsAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &geteMyChatBotsAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		getMyChatBotsReq: &getMyChatBots.GetMyChatBotsReqIdl{},
		getMyChatBotsRes: &getMyChatBots.GetMyChatBotsResIdl{},
		global:           &types.GetMyChatBotsBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getMyChatBotsReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getMyChatBotsRes.Data = new(getMyChatBots.GetMyChatBotsRes)
	return obj
}

func (obj *geteMyChatBotsAction) ParseParams() {
	if nil == obj || nil == obj.getMyChatBotsReq || nil == obj.getMyChatBotsRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getMyChatBotsReq.GetData()
}

func (obj *geteMyChatBotsAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getMyChatBotsReq || nil == obj.getMyChatBotsRes.Data {
		return nil
	}

	// 临时关掉接口的签名校验
	// obj.ObjRequest.AddStrategy("check_sign", false)

	arrPrivateInfo := map[string]any{
		"check_login": true,
	}

	return arrPrivateInfo
}

func (obj *geteMyChatBotsAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getMyChatBotsReq {
		return false
	}

	return true
}

func (obj *geteMyChatBotsAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getMyChatBotsReq || nil == obj.getMyChatBotsReq.Data {
		return false
	}
	errNo := aichat.DoGetMyChatBots(obj.Ctx, obj.global, obj.getMyChatBotsRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *geteMyChatBotsAction) BuildOut() {
	if nil == obj || nil == obj.getMyChatBotsRes {
		return
	}
	if nil == obj.getMyChatBotsRes.Error {
		// 默认赋值成功
		obj.getMyChatBotsRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getMyChatBotsRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *geteMyChatBotsAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getMyChatBotsReq || nil == obj.getMyChatBotsReq.Data {
		return
	}
}
