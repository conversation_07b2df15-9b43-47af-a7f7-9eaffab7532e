package aichat

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getGameListDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

func GetGameListDetail(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getGameListDetailActionInstance(ctx, req).UiFuncHandler()
}

type getGameListDetailAction struct {
	*uiclient.UIBaseAction
	getGameListDetailReq *getGameListDetail.GetGameListDetailReqIdl
	getGameListDetailRes *getGameListDetail.GetGameListDetailResIdl
	global           *types.GetGameListDetailBaseData
}

func getGameListDetailActionInstance(ctx context.Context, req ghttp.Request) *getGameListDetailAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &getGameListDetailAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		getGameListDetailReq: &getGameListDetail.GetGameListDetailReqIdl{},
		getGameListDetailRes: &getGameListDetail.GetGameListDetailResIdl{},
		global:           &types.GetGameListDetailBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getGameListDetailReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getGameListDetailRes.Data = new(getGameListDetail.GetGameListDetailRes)
	return obj
}

func (obj *getGameListDetailAction) ParseParams() {
	if nil == obj || nil == obj.getGameListDetailReq || nil == obj.getGameListDetailRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getGameListDetailReq.GetData()
}
func (obj *getGameListDetailAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getGameListDetailReq || nil == obj.getGameListDetailReq.Data {
		return nil
	}
	// 从调用入参中获取需要的参数
	chatUk := obj.getGameListDetailReq.GetData().GetChatUk()

	arrPrivateInfo := map[string]any{
		"chat_uk": chatUk, //剧情id
	}

	return arrPrivateInfo
}

func (obj *getGameListDetailAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getGameListDetailReq {
		return false
	}

	return true
}

func (obj *getGameListDetailAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getGameListDetailReq || nil == obj.getGameListDetailReq.Data {
		return false
	}
	//下游service内容
	errNo := aichat.DoGetGameListDetail(obj.Ctx, obj.global, obj.getGameListDetailRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *getGameListDetailAction) BuildOut() {
	if nil == obj || nil == obj.getGameListDetailRes {
		return
	}
	if nil == obj.getGameListDetailRes.Error {
		// 默认赋值成功
		obj.getGameListDetailRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getGameListDetailRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getGameListDetailAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getGameListDetailReq || nil == obj.getGameListDetailReq.Data {
		return
	}
}