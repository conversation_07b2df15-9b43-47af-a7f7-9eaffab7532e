package aichat

import (
	"context"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getUserBotSet"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

func GetUserBotSet(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getUserBotSetActionInstance(ctx, req).UiFuncHandler()
}

type getUserBotSetAction struct {
	*uiclient.UIBaseAction
	getUserBotSetReq *getUserBotSet.GetUserBotSetReqIdl
	getUserBotSetRes *getUserBotSet.GetUserBotSetResIdl
	global           *types.GetUserBotSetBaseData
}

func getUserBotSetActionInstance(ctx context.Context, req ghttp.Request) *getUserBotSetAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &getUserBotSetAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		getUserBotSetReq: &getUserBotSet.GetUserBotSetReqIdl{},
		getUserBotSetRes: &getUserBotSet.GetUserBotSetResIdl{},
		global:           &types.GetUserBotSetBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getUserBotSetReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getUserBotSetRes.Data = new(getUserBotSet.GetUserBotSetData)
	return obj
}

func (obj *getUserBotSetAction) ParseParams() {
	if nil == obj || nil == obj.getUserBotSetReq || nil == obj.getUserBotSetReq.Data || nil == obj.ObjRequest {
		return
	}
	if obj.global != nil {
		obj.global.Request = obj.getUserBotSetReq.GetData()
	}
}

func (obj *getUserBotSetAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getUserBotSetReq || nil == obj.getUserBotSetReq.Data {
		return nil
	}

	arrPrivateInfo := map[string]any{
		"check_login": true,
		"need_login":  true,
	}

	return arrPrivateInfo
}

func (obj *getUserBotSetAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getUserBotSetReq {
		return false
	}

	return true
}

func (obj *getUserBotSetAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getUserBotSetReq || nil == obj.getUserBotSetReq.Data {
		return false
	}
	errNo := aichat.DoGetUserBotSet(obj.Ctx, obj.global, obj.getUserBotSetRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *getUserBotSetAction) BuildOut() {
	if nil == obj || nil == obj.getUserBotSetRes {
		return
	}
	if nil == obj.getUserBotSetRes.Error {
		// 默认赋值成功
		obj.getUserBotSetRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getUserBotSetRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}
