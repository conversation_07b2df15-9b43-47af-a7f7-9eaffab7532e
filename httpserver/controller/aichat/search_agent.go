package aichat

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/searchAgent"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

func SearchAgent(ctx context.Context, req ghttp.Request) ghttp.Response {
	return searchAgentActionInstance(ctx, req).UiFuncHandler()
}

type searchAgentAction struct {
	*uiclient.UIBaseAction
	searchAgentReq *searchAgent.SearchAgentReqIdl
	searchAgentRes *searchAgent.SearchAgentResIdl
	global         *types.SearchAgentBaseData
}

func searchAgentActionInstance(ctx context.Context, req ghttp.Request) *searchAgentAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &searchAgentAction{
		UIBaseAction:   &uiclient.UIBaseAction{},
		searchAgentReq: &searchAgent.SearchAgentReqIdl{},
		searchAgentRes: &searchAgent.SearchAgentResIdl{},
		global:         &types.SearchAgentBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.searchAgentReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.searchAgentRes.Data = new(searchAgent.SearchAgentRes)
	return obj
}

func (obj *searchAgentAction) ParseParams() {
	if nil == obj || nil == obj.searchAgentReq || nil == obj.searchAgentRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.searchAgentReq.GetData()
}

func (obj *searchAgentAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.searchAgentReq || nil == obj.searchAgentReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	pn := obj.searchAgentReq.GetData().GetPn()
	rn := obj.searchAgentReq.GetData().GetRn()
	key := obj.searchAgentReq.GetData().GetKey()

	arrPrivateInfo := map[string]any{
		"pn":  pn,
		"rn":  rn,
		"key": key,
	}

	return arrPrivateInfo
}

func (obj *searchAgentAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.searchAgentReq {
		return false
	}

	return true
}

func (obj *searchAgentAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.searchAgentReq || nil == obj.searchAgentReq.Data {
		return false
	}
	errNo := aichat.SearchAgent(obj.Ctx, obj.global, obj.searchAgentRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *searchAgentAction) BuildOut() {
	if nil == obj || nil == obj.searchAgentRes {
		return
	}
	if nil == obj.searchAgentRes.Error {
		// 默认赋值成功
		obj.searchAgentRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.searchAgentRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *searchAgentAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.searchAgentReq || nil == obj.searchAgentReq.Data {
		return
	}
}
