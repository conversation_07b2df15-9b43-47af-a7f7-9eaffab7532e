package aichat

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/gameOver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

func GameOver(ctx context.Context, req ghttp.Request) ghttp.Response {
	return gameOverActionInstance(ctx, req).UiFuncHandler()
}

type gameOverAction struct {
	*uiclient.UIBaseAction
	gameOverReq *gameOver.GameOverReqIdl
	gameOverRes *gameOver.GameOverResIdl
	global      *types.GameOverBaseData
}

func gameOverActionInstance(ctx context.Context, req ghttp.Request) *gameOverAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &gameOverAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		gameOverReq:  &gameOver.GameOverReqIdl{},
		gameOverRes:  &gameOver.GameOverResIdl{},
		global:       &types.GameOverBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.gameOverReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.gameOverRes.Data = new(gameOver.GameOverRes)
	return obj
}

func (obj *gameOverAction) ParseParams() {
	if nil == obj || nil == obj.gameOverReq || nil == obj.gameOverRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.gameOverReq.GetData()
}
func (obj *gameOverAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.gameOverReq || nil == obj.gameOverReq.Data {
		return nil
	}
	// 从调用入参中获取需要的参数
	plotId := obj.gameOverReq.GetData().GetPlotId()
	sourceFrom := obj.gameOverReq.GetData().GetSourceFrom()

	arrPrivateInfo := map[string]any{
		"plot_id":     plotId,     //剧情id
		"source_from": sourceFrom, //1-端内 2-小程序
	}

	return arrPrivateInfo
}

func (obj *gameOverAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.gameOverReq {
		return false
	}

	return true
}

func (obj *gameOverAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.gameOverReq || nil == obj.gameOverReq.Data {
		return false
	}
	//下游service内容
	errNo := aichat.DoGameOver(obj.Ctx, obj.global, obj.gameOverRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *gameOverAction) BuildOut() {
	if nil == obj || nil == obj.gameOverRes {
		return
	}
	if nil == obj.gameOverRes.Error {
		// 默认赋值成功
		obj.gameOverRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.gameOverRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *gameOverAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.gameOverReq || nil == obj.gameOverReq.Data {
		return
	}
}
