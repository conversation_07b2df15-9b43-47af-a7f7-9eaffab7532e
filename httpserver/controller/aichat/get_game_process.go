package aichat

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getGameProcess"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

func GetGameProcess(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getGameProcessActionInstance(ctx, req).UiFuncHandler()
}

type getGameProcessAction struct {
	*uiclient.UIBaseAction
	getGameProcessReq *getGameProcess.GetGameProcessReqIdl
	getGameProcessRes *getGameProcess.GetGameProcessResIdl
	global           *types.GetGameProcessBaseData
}

func getGameProcessActionInstance(ctx context.Context, req ghttp.Request) *getGameProcessAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &getGameProcessAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		getGameProcessReq: &getGameProcess.GetGameProcessReqIdl{},
		getGameProcessRes: &getGameProcess.GetGameProcessResIdl{},
		global:           &types.GetGameProcessBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getGameProcessReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getGameProcessRes.Data = new(getGameProcess.GetGameProcessRes)
	return obj
}

func (obj *getGameProcessAction) ParseParams() {
	if nil == obj || nil == obj.getGameProcessReq || nil == obj.getGameProcessRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getGameProcessReq.GetData()
}
func (obj *getGameProcessAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getGameProcessReq || nil == obj.getGameProcessReq.Data {
		return nil
	}
	// 从调用入参中获取需要的参数
	plotId := obj.getGameProcessReq.GetData().GetPlotId()

	arrPrivateInfo := map[string]any{
		"plot_id": plotId, //剧情id
	}

	return arrPrivateInfo
}

func (obj *getGameProcessAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getGameProcessReq {
		return false
	}

	return true
}

func (obj *getGameProcessAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getGameProcessReq || nil == obj.getGameProcessReq.Data {
		return false
	}
	//下游service内容
	errNo := aichat.DoGetGameProcess(obj.Ctx, obj.global, obj.getGameProcessRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *getGameProcessAction) BuildOut() {
	if nil == obj || nil == obj.getGameProcessRes {
		return
	}
	if nil == obj.getGameProcessRes.Error {
		// 默认赋值成功
		obj.getGameProcessRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getGameProcessRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getGameProcessAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getGameProcessReq || nil == obj.getGameProcessReq.Data {
		return
	}
}