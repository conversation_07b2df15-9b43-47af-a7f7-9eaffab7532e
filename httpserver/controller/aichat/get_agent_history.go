package aichat

import (
	"context"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	p "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getAgentHistory"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

// GetAgentHistory 获取代理详情函数
func GetAgentHistory(ctx context.Context, req ghttp.Request) ghttp.Response {
	return GetAgentHistoryActionInstance(ctx, req).UiFuncHandler()
}

type GetAgentHistoryAction struct {
	*uiclient.UIBaseAction
	GetAgentHistoryReq *p.GetAgentHistoryReqIdl
	GetAgentHistoryRes *p.GetAgentHistoryResIdl
	global             *types.GetAgentHistoryBaseData
}

// GetAgentHistoryActionInstance 获取AgentDetailAction实例，如果上下文或请求为nil则返回nil
func GetAgentHistoryActionInstance(ctx context.Context, req ghttp.Request) *GetAgentHistoryAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &GetAgentHistoryAction{
		UIBaseAction:       &uiclient.UIBaseAction{},
		GetAgentHistoryReq: &p.GetAgentHistoryReqIdl{},
		GetAgentHistoryRes: &p.GetAgentHistoryResIdl{},
		global:             &types.GetAgentHistoryBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.GetAgentHistoryReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.GetAgentHistoryRes.Data = new(p.GetAgentHistoryRes)
	return obj
}

// ParseParams 解析参数函数
func (obj *GetAgentHistoryAction) ParseParams() {
	if nil == obj || nil == obj.GetAgentHistoryReq || nil == obj.GetAgentHistoryRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.GetAgentHistoryReq.GetData()
}

// GetPrivateInfo 获取私有信息，包括机器人UID、会话ID和用户ID
func (obj *GetAgentHistoryAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetAgentHistoryReq || nil == obj.GetAgentHistoryReq.Data {
		return nil
	}
	agentId := obj.GetAgentHistoryReq.GetData().GetAgentId()
	conversationId := obj.GetAgentHistoryReq.GetData().GetConversationId()
	size := obj.GetAgentHistoryReq.GetData().GetSize()
	pageToken := obj.GetAgentHistoryReq.GetData().GetPageToken()
	plotId := obj.GetAgentHistoryReq.GetData().GetPlotId()
	arrPrivateInfo := map[string]any{
		"agent_id":        agentId,
		"conversation_id": conversationId,
		"size":            size,
		"page_token":      pageToken,
		"plot_id":         plotId,
	}
	return arrPrivateInfo
}

// CheckPrivate 判断是否为私有方法，如果不存在该方法则返回false，否则返回true
func (obj *GetAgentHistoryAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetAgentHistoryReq {
		return false
	}
	return true
}

// Execute Execute 执行方法
func (obj *GetAgentHistoryAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetAgentHistoryReq || nil == obj.GetAgentHistoryReq.Data {
		return false
	}
	errNo := aichat.DoGetAgentHistory(obj.Ctx, obj.global, obj.GetAgentHistoryRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

// BuildOut BuildOut 用于构建输出数据，包括错误信息和响应数据
func (obj *GetAgentHistoryAction) BuildOut() {
	if nil == obj || nil == obj.GetAgentHistoryRes {
		return
	}
	if nil == obj.GetAgentHistoryRes.Error {
		// 默认赋值成功
		obj.GetAgentHistoryRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.GetAgentHistoryRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

// BuildLog 构建日志，用于记录请求的相关信息和处理过程中遇到的异常信息
func (obj *GetAgentHistoryAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.GetAgentHistoryReq || nil == obj.GetAgentHistoryReq.Data {
		return
	}
}
