package aichat

import (
	"context"
	"errors"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/myAgent"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/myagent/adapter"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/myagent/gen"
)

func MyAgent(ctx context.Context, req ghttp.Request) ghttp.Response {
	return myAgentActionInstance(ctx, req).UiFuncHandler()
}

type myAgentAction struct {
	*uiclient.UIBaseAction
	myAgentReq *myAgent.MyAgentReqIdl
	myAgentRes *myAgent.MyAgentResIdl
}

func myAgentActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if ctx == nil || req == nil {
		return nil
	}
	ua := &myAgentAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		myAgentReq:   &myAgent.MyAgentReqIdl{},
		myAgentRes:   &myAgent.MyAgentResIdl{},
	}
	if !ua.Init(ctx, req, ua, ua.myAgentReq) {
		tbcontext.WarningF(ctx, "UIBase init fail")
	}
	return ua
}

func (u *myAgentAction) ParseParams() {
	return
}

func (u *myAgentAction) GetPrivateInfo() map[string]any {
	if u == nil || u.ObjRequest == nil || u.myAgentReq == nil || u.myAgentReq.Data == nil {
		return nil
	}
	data := u.myAgentReq.GetData()
	privateInfo := map[string]any{
		"pn":    data.GetPn(),
		"rn":    data.GetRn(),
		"scene": data.GetScene(),
		"bot_uk": data.GetBotUk(),
	}
	return privateInfo
}

func (u *myAgentAction) CheckPrivate() bool {
	if u == nil || u.ObjRequest == nil || u.myAgentReq == nil || u.myAgentReq.Data == nil {
		return false
	}
	return true
}

func (u *myAgentAction) Execute() bool {
	if u == nil || u.ObjRequest == nil || u.myAgentReq == nil || u.myAgentReq.Data == nil {
		return false
	}

	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(u.Ctx, tbcontext.GetLogidString(u.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}

	engCtx := resource.MyAgentEngine.NewContext(u.Ctx, opts...)
	defer func() {
		engine.ReleaseContext(engCtx)
	}()

	engCtx.MustRegisterInstance(&u.UIBaseAction)
	err := resource.MyAgentEngine.Run(engCtx)

	// 算子中设置过非0错误，使用设置的错误号直接返回
	var v engine.State
	if errors.As(err, &v) {
		tbcontext.WarningF(u.Ctx, "op err, code:[%d], errmsg:[%s]", v.Code(), err.Error())
		u.Error(v.Code(), err.Error(), false)
		return false
	}

	// 没设置过错误号，兜底返回错误
	if err != nil {
		tbcontext.WarningF(u.Ctx, "myAgent exgraph engine run fail, err=%v", err)
		u.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, tiebaerror.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return false
	}

	var output myAgent.MyAgentRes
	ok := engCtx.MutableInstance(&output)
	if !ok {
		tbcontext.WarningF(u.Ctx, "get myAgent output fail")
		return false
	}

	u.myAgentRes.Data = &output
	return true
}

func (u *myAgentAction) BuildOut() {
	if u == nil || u.myAgentRes == nil {
		return
	}
	if u.myAgentRes.Error == nil {
		u.myAgentRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": u.myAgentRes.GetData(),
	}
	u.ObjResponse.SetOutData(outData)
	return
}

func (u *myAgentAction) BuildLog() {
	if u == nil || u.myAgentReq == nil || u.myAgentReq.Data == nil {
		return
	}
	return
}
