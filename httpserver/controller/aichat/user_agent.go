package aichat

import (
	"context"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/userAgent"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/useragent/adapter"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/useragent/gen"
)

func UserAgent(ctx context.Context, req ghttp.Request) ghttp.Response {
	return userAgentActionInstance(ctx, req).UiFuncHandler()
}

type userAgentAction struct {
	*uiclient.UIBaseAction
	userAgentReq *userAgent.UserAgentReqIdl
	userAgentRes *userAgent.UserAgentResIdl
}

func userAgentActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if ctx == nil || req == nil {
		return nil
	}
	ua := &userAgentAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		userAgentReq: &userAgent.UserAgentReqIdl{},
		userAgentRes: &userAgent.UserAgentResIdl{},
	}
	if !ua.Init(ctx, req, ua, ua.userAgentReq) {
		tbcontext.WarningF(ctx, "UIBase init fail")
	}
	return ua
}

func (u *userAgentAction) ParseParams() {
	return
}

func (u *userAgentAction) GetPrivateInfo() map[string]any {
	if u == nil || u.ObjRequest == nil || u.userAgentReq == nil || u.userAgentReq.Data == nil {
		return nil
	}
	data := u.userAgentReq.GetData()
	privateInfo := map[string]any{
		"pn":        data.GetPn(),
		"rn":        data.GetRn(),
		"user_type": data.GetUserType(),
	}
	return privateInfo
}

func (u *userAgentAction) CheckPrivate() bool {
	if u == nil || u.ObjRequest == nil || u.userAgentReq == nil || u.userAgentReq.Data == nil {
		return false
	}
	return true
}

func (u *userAgentAction) Execute() bool {
	if u == nil || u.ObjRequest == nil || u.userAgentReq == nil || u.userAgentReq.Data == nil {
		return false
	}

	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(u.Ctx, tbcontext.GetLogidString(u.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}

	engCtx := resource.UserAgentEngine.NewContext(u.Ctx, opts...)
	defer func() {
		engine.ReleaseContext(engCtx)
	}()

	engCtx.MustRegisterInstance(&u.UIBaseAction)
	err := resource.UserAgentEngine.Run(engCtx)

	// 算子中设置过非0错误号，使用设置的错误号直接返回
	if errnoInter := tbcontext.GetErrNo(u.Ctx); errnoInter != nil {
		if errno := cast.ToInt(errnoInter); errno != tiebaerror.ERR_SUCCESS {
			return false
		}
	}

	// 没设置过错误号，兜底返回错误
	if err != nil {
		tbcontext.WarningF(u.Ctx, "userAgent exgraph engine run fail, err=%v", err)
		u.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, tiebaerror.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return false
	}

	var output userAgent.UserAgentRes
	ok := engCtx.MutableInstance(&output)
	if !ok {
		tbcontext.WarningF(u.Ctx, "get userAgent output fail")
		return false
	}

	u.userAgentRes.Data = &output
	return true
}

func (u *userAgentAction) BuildOut() {
	if u == nil || u.userAgentRes == nil {
		return
	}
	if u.userAgentRes.Error == nil {
		u.userAgentRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": u.userAgentRes.GetData(),
	}
	u.ObjResponse.SetOutData(outData)
	return
}

func (u *userAgentAction) BuildLog() {
	if u == nil || u.userAgentReq == nil || u.userAgentReq.Data == nil {
		return
	}
	return
}
