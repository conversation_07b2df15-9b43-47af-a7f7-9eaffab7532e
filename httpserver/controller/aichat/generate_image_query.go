package aichat

import (
	"context"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	p "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/generateImageQuery"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

// GenerateImageQuery 获取代理详情函数
func GenerateImageQuery(ctx context.Context, req ghttp.Request) ghttp.Response {
	return GenerateImageQueryActionInstance(ctx, req).UiFuncHandler()
}

type GenerateImageQueryAction struct {
	*uiclient.UIBaseAction
	GenerateImageQueryReq *p.GenerateImageQueryReqIdl
	GenerateImageQueryRes *p.GenerateImageQueryResIdl
	global                *types.GenerateImageQueryBaseData
}

// GenerateImageQueryActionInstance 获取AgentDetailAction实例，如果上下文或请求为nil则返回nil
func GenerateImageQueryActionInstance(ctx context.Context, req ghttp.Request) *GenerateImageQueryAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &GenerateImageQueryAction{
		UIBaseAction:          &uiclient.UIBaseAction{},
		GenerateImageQueryReq: &p.GenerateImageQueryReqIdl{},
		GenerateImageQueryRes: &p.GenerateImageQueryResIdl{},
		global:                &types.GenerateImageQueryBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.GenerateImageQueryReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.GenerateImageQueryRes.Data = new(p.GenerateImageQueryRes)
	return obj
}

// ParseParams 解析参数函数
func (obj *GenerateImageQueryAction) ParseParams() {
	if nil == obj || nil == obj.GenerateImageQueryReq || nil == obj.GenerateImageQueryRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.GenerateImageQueryReq.GetData()
}

// GetPrivateInfo 获取私有信息，包括机器人UID、会话ID和用户ID
func (obj *GenerateImageQueryAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GenerateImageQueryReq || nil == obj.GenerateImageQueryReq.Data {
		return nil
	}
	taskID := obj.GenerateImageQueryReq.GetData().GetTaskId()
	arrPrivateInfo := map[string]any{
		"task_id": taskID,
	}
	return arrPrivateInfo
}

// CheckPrivate 判断是否为私有方法，如果不存在该方法则返回false，否则返回true
func (obj *GenerateImageQueryAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GenerateImageQueryReq {
		return false
	}
	return true
}

// Execute Execute 执行方法
func (obj *GenerateImageQueryAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GenerateImageQueryReq || nil == obj.GenerateImageQueryReq.Data {
		return false
	}
	errNo := aichat.DoGenerateImageQuery(obj.Ctx, obj.global, obj.GenerateImageQueryRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

// BuildOut BuildOut 用于构建输出数据，包括错误信息和响应数据
func (obj *GenerateImageQueryAction) BuildOut() {
	if nil == obj || nil == obj.GenerateImageQueryRes {
		return
	}
	if nil == obj.GenerateImageQueryRes.Error {
		// 默认赋值成功
		obj.GenerateImageQueryRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.GenerateImageQueryRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

// BuildLog 构建日志，用于记录请求的相关信息和处理过程中遇到的异常信息
func (obj *GenerateImageQueryAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.GenerateImageQueryReq || nil == obj.GenerateImageQueryReq.Data {
		return
	}
}
