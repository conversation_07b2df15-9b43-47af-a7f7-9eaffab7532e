package aichat

import (
	"context"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	p "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getAgentDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

// GetAgentDetail 获取代理详情函数
func GetAgentDetail(ctx context.Context, req ghttp.Request) ghttp.Response {
	return GetAgentDetailActionInstance(ctx, req).UiFuncHandler()
}

type GetAgentDetailAction struct {
	*uiclient.UIBaseAction
	GetAgentDetailReq *p.GetAgentDetailReqIdl
	GetAgentDetailRes *p.GetAgentDetailResIdl
	global            *types.GetAgentDetailBaseData
}

// GetAgentDetailActionInstance 获取AgentDetailAction实例，如果上下文或请求为nil则返回nil
func GetAgentDetailActionInstance(ctx context.Context, req ghttp.Request) *GetAgentDetailAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &GetAgentDetailAction{
		UIBaseAction:      &uiclient.UIBaseAction{},
		GetAgentDetailReq: &p.GetAgentDetailReqIdl{},
		GetAgentDetailRes: &p.GetAgentDetailResIdl{},
		global:            &types.GetAgentDetailBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.GetAgentDetailReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.GetAgentDetailRes.Data = new(p.GetAgentDetailRes)
	return obj
}

// ParseParams 解析参数函数
func (obj *GetAgentDetailAction) ParseParams() {
	if nil == obj || nil == obj.GetAgentDetailReq || nil == obj.GetAgentDetailRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.GetAgentDetailReq.GetData()
}

// GetPrivateInfo 获取私有信息，包括机器人UID、会话ID和用户ID
func (obj *GetAgentDetailAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetAgentDetailReq || nil == obj.GetAgentDetailReq.Data {
		return nil
	}
	botUid := obj.GetAgentDetailReq.GetData().GetBotUid()
	conversationId := obj.GetAgentDetailReq.GetData().GetConversationId()
	sourcePage := obj.GetAgentDetailReq.GetData().GetSourcePage()
	arrPrivateInfo := map[string]any{
		"bot_uid":         botUid,
		"conversation_id": conversationId,
		"source_page":     sourcePage,
	}
	return arrPrivateInfo
}

// CheckPrivate 判断是否为私有方法，如果不存在该方法则返回false，否则返回true
func (obj *GetAgentDetailAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetAgentDetailReq {
		return false
	}
	return true
}

// Execute Execute 执行方法
func (obj *GetAgentDetailAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetAgentDetailReq || nil == obj.GetAgentDetailReq.Data {
		return false
	}
	errNo := aichat.DoGetAgentDetail(obj.Ctx, obj.global, obj.GetAgentDetailRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

// BuildOut BuildOut 用于构建输出数据，包括错误信息和响应数据
func (obj *GetAgentDetailAction) BuildOut() {
	if nil == obj || nil == obj.GetAgentDetailRes {
		return
	}
	if nil == obj.GetAgentDetailRes.Error {
		// 默认赋值成功
		obj.GetAgentDetailRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.GetAgentDetailRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

// BuildLog 构建日志，用于记录请求的相关信息和处理过程中遇到的异常信息
func (obj *GetAgentDetailAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.GetAgentDetailReq || nil == obj.GetAgentDetailReq.Data {
		return
	}
}
