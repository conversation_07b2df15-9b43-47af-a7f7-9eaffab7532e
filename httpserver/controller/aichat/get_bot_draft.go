package aichat

import (
	"context"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	// 项目自身库
	aichatProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getBotDraft"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

// GetBotDraft 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func GetBotDraft(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 通过getBotDraftActionInstance实例化getBotDraftAction类，并调用UiFuncHandler()方法给ghttp.Response赋值
	return getBotDraftActionInstance(ctx, req).UiFuncHandler()
}

// getBotDraftAction 服务相关结构体
type getBotDraftAction struct {
	// 组合UIBaseAction基础类，并实现基类方法
	*uiclient.UIBaseAction
	// 通过proto定义输入数据
	getBotDraftReq *aichatProto.GetBotDraftReqIdl
	// 通过proto定义输出数据
	getBotDraftRes *aichatProto.GetBotDraftResIdl
	// 通过types封装跟service层交互的数据
	baseData *types.GetBotDraftBaseData
}

// getBotDraftActionInstance 创建一个getBotDraftAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回getBotDraftAction实例
func getBotDraftActionInstance(ctx context.Context, req ghttp.Request) *getBotDraftAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &getBotDraftAction{
		UIBaseAction:   &uiclient.UIBaseAction{},
		getBotDraftReq: &aichatProto.GetBotDraftReqIdl{},
		getBotDraftRes: &aichatProto.GetBotDraftResIdl{},
		baseData:       &types.GetBotDraftBaseData{},
	}
	// 通过Init方法赋值
	if !obj.Init(ctx, req, obj, obj.getBotDraftReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

// ParseParams 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *getBotDraftAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.getBotDraftReq == nil || obj.getBotDraftReq.Data == nil {
		return
	}
	obj.baseData.Request = obj.getBotDraftReq.GetData()
}

// GetPrivateInfo 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *getBotDraftAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.getBotDraftReq == nil || obj.getBotDraftReq.Data == nil {
		return nil
	}
	privateInfo := map[string]any{
		"check_login": true,
		"need_login":  true,
	}
	return privateInfo
}

// CheckPrivate 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *getBotDraftAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getBotDraftReq == nil {
		return false
	}
	return true
}

// BuildOut 构建输出数据的方法
// @Param：无
// @Return：无
func (obj *getBotDraftAction) BuildOut() {
	if obj == nil || obj.getBotDraftRes == nil {
		return
	}
	if obj.getBotDraftRes.Error == nil {
		obj.getBotDraftRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}
	outData := map[string]any{
		"data": obj.getBotDraftRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

// BuildLog 创建日志
// @Param：无
// @Return：无
func (obj *getBotDraftAction) BuildLog() {
	return
}

// Execute 执行getBotDraftAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *getBotDraftAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getBotDraftReq == nil || obj.getBotDraftReq.Data == nil {
		return false
	}
	errno := service.GetBotDraft(obj.Ctx, obj.baseData, obj.getBotDraftRes)
	if errno != 0 {
		obj.Error(errno, "service call fail", true)
		return false
	}
	return true
}
