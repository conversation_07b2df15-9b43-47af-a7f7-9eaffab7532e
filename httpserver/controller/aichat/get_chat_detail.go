package aichat

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getChatDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

func GetChatDetail(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getChatDetailActionInstance(ctx, req).UiFuncHandler()
}

type getChatDetailAction struct {
	*uiclient.UIBaseAction
	getChatDetailReq *getChatDetail.GetChatDetailReqIdl
	getChatDetailRes *getChatDetail.GetChatDetailResIdl
	global           *types.GetChatDetailBaseData
}

func getChatDetailActionInstance(ctx context.Context, req ghttp.Request) *getChatDetailAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &getChatDetailAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		getChatDetailReq: &getChatDetail.GetChatDetailReqIdl{},
		getChatDetailRes: &getChatDetail.GetChatDetailResIdl{},
		global:           &types.GetChatDetailBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getChatDetailReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getChatDetailRes.Data = new(getChatDetail.GetChatDetailRes)
	return obj
}

func (obj *getChatDetailAction) ParseParams() {
	if nil == obj || nil == obj.getChatDetailReq || nil == obj.getChatDetailRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getChatDetailReq.GetData()
}

func (obj *getChatDetailAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getChatDetailReq || nil == obj.getChatDetailReq.Data {
		return nil
	}

	// 临时关掉接口的签名校验
	// obj.ObjRequest.AddStrategy("check_sign", false)

	chatUK := obj.getChatDetailReq.GetData().GetChatUk()

	arrPrivateInfo := map[string]any{
		"check_login": true,
		"chat_uk":     chatUK,
	}

	return arrPrivateInfo
}

func (obj *getChatDetailAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getChatDetailReq {
		return false
	}

	return true
}

func (obj *getChatDetailAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getChatDetailReq || nil == obj.getChatDetailReq.Data {
		return false
	}
	errNo := aichat.DoGetChatDetail(obj.Ctx, obj.getChatDetailReq, obj.global, obj.getChatDetailRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *getChatDetailAction) BuildOut() {
	if nil == obj || nil == obj.getChatDetailRes {
		return
	}
	if nil == obj.getChatDetailRes.Error {
		// 默认赋值成功
		obj.getChatDetailRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getChatDetailRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getChatDetailAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getChatDetailReq || nil == obj.getChatDetailReq.Data {
		return
	}
}
