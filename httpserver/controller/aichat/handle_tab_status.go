package aichat

import (
	"context"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	client "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/handleTabStatus"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

// HandleTabStatus @Description 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func HandleTabStatus(ctx context.Context, req ghttp.Request) ghttp.Response {
	return handleTabStatusActionInstance(ctx, req).UiFuncHandler()
}

// @Description handleTabStatusAction类的定义
type handleTabStatusAction struct {
	*uiclient.UIBaseAction
	handleTabStatusReq *handleTabStatus.HandleTabStatusReqIdl
	handleTabStatusRes *handleTabStatus.HandleTabStatusResIdl
	baseData           *types.HandleTabStatusBaseData
}

// @Description 创建一个handleTabStatusAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回handleTabStatusAction实例
func handleTabStatusActionInstance(ctx context.Context, req ghttp.Request) *handleTabStatusAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &handleTabStatusAction{
		UIBaseAction:       &uiclient.UIBaseAction{},
		handleTabStatusReq: &handleTabStatus.HandleTabStatusReqIdl{},
		handleTabStatusRes: &handleTabStatus.HandleTabStatusResIdl{
			Data:  &handleTabStatus.HandleTabStatusRes{},
			Error: &client.Error{},
		},
		baseData: &types.HandleTabStatusBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.handleTabStatusReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

// ParseParams @Description 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *handleTabStatusAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.handleTabStatusReq == nil || obj.handleTabStatusReq.Data == nil {
		return
	}
	obj.baseData.Request = obj.handleTabStatusReq.GetData()
}

// GetPrivateInfo @Description 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *handleTabStatusAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.handleTabStatusReq == nil || obj.handleTabStatusReq.Data == nil {
		return nil
	}
	//obj.ObjRequest.AddStrategy("check_sign", false)

	privateInfo := map[string]any{
		"check_login": true,
		"need_login":  true,
	}
	return privateInfo
}

// CheckPrivate @Description 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *handleTabStatusAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.handleTabStatusReq == nil {
		return false
	}
	return true
}

// BuildOut @Description 用于构建并返回输出数据
// @Param：无
// @Return：无
func (obj *handleTabStatusAction) BuildOut() {
	if obj == nil || obj.handleTabStatusRes == nil {
		return
	}
	if obj.handleTabStatusRes.Error == nil {
		obj.handleTabStatusRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}

	outData := map[string]any{
		"data": obj.handleTabStatusRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)

	return
}

// BuildLog @Description 创建日志
// @Param：无
// @Return：无
func (obj *handleTabStatusAction) BuildLog() {
	return
}

// Execute @Description 执行handleTabStatusAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *handleTabStatusAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.handleTabStatusReq == nil || obj.handleTabStatusReq.Data == nil {
		return false
	}
	errno := service.HandleTabStatus(obj.Ctx, obj.baseData, obj.handleTabStatusRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}
