package aichat

import (
	"context"
	"errors"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/agentInteractMsg"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/agentinteractmsg/adapter"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/agentinteractmsg/gen"
)

func AgentInteractMsg(ctx context.Context, req ghttp.Request) ghttp.Response {
	return agentInteractMsgActionInstance(ctx, req).UiFuncHandler()
}

type agentInteractMsgAction struct {
	*uiclient.UIBaseAction
	req *agentInteractMsg.AgentInteractMsgReqIdl
	res *agentInteractMsg.AgentInteractMsgResIdl
}

func agentInteractMsgActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if ctx == nil || req == nil {
		return nil
	}
	ua := &agentInteractMsgAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		req:          &agentInteractMsg.AgentInteractMsgReqIdl{},
		res:          &agentInteractMsg.AgentInteractMsgResIdl{},
	}
	if !ua.Init(ctx, req, ua, ua.req) {
		tbcontext.WarningF(ctx, "UIBase init fail")
	}
	return ua
}

func (u *agentInteractMsgAction) ParseParams() {
	return
}

func (u *agentInteractMsgAction) GetPrivateInfo() map[string]any {
	if u == nil || u.ObjRequest == nil || u.req == nil || u.req.Data == nil {
		return nil
	}
	data := u.req.GetData()
	privateInfo := map[string]any{
		"last_msg_id": data.GetLastMsgId(),
	}
	return privateInfo
}

func (u *agentInteractMsgAction) CheckPrivate() bool {
	if u == nil || u.ObjRequest == nil || u.req == nil || u.req.Data == nil {
		return false
	}
	return true
}

func (u *agentInteractMsgAction) Execute() bool {
	if u == nil || u.ObjRequest == nil || u.req == nil || u.req.Data == nil {
		return false
	}

	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(u.Ctx, tbcontext.GetLogidString(u.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}

	engCtx := resource.AgentInteractMsgEngine.NewContext(u.Ctx, opts...)
	defer func() {
		engine.ReleaseContext(engCtx)
	}()

	engCtx.MustRegisterInstance(&u.UIBaseAction)
	err := resource.AgentInteractMsgEngine.Run(engCtx)

	// 算子中设置过非0错误，使用设置的错误号直接返回
	var v engine.State
	if errors.As(err, &v) {
		tbcontext.WarningF(u.Ctx, "op err, code:[%d], errmsg:[%s]", v.Code(), err.Error())
		u.Error(v.Code(), err.Error(), false)
		return false
	}

	// 没设置过错误号，兜底返回错误
	if err != nil {
		tbcontext.WarningF(u.Ctx, "agentInteractMsg exgraph engine run fail, err=%v", err)
		u.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, tiebaerror.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return false
	}

	var output agentInteractMsg.AgentInteractMsgRes
	ok := engCtx.MutableInstance(&output)
	if !ok {
		tbcontext.WarningF(u.Ctx, "get agentInteractMsg output fail")
		return false
	}

	u.res.Data = &output
	return true
}

func (u *agentInteractMsgAction) BuildOut() {
	if u == nil || u.res == nil {
		return
	}
	if u.res.Error == nil {
		u.res.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": u.res.GetData(),
	}
	u.ObjResponse.SetOutData(outData)
	return
}

func (u *agentInteractMsgAction) BuildLog() {
	if u == nil || u.req == nil || u.req.Data == nil {
		return
	}
	return
}
