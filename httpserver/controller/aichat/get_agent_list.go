package aichat

import (
	"context"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getAgentList"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

func GetAgentList(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getAgentListActionInstance(ctx, req).UiFuncHandler()
}

type getAgentListAction struct {
	*uiclient.UIBaseAction
	getAgentListReq *getAgentList.GetAgentListReqIdl
	getAgentListRes *getAgentList.GetAgentListIdl
	global          *types.GetAgentListBaseData
}

func getAgentListActionInstance(ctx context.Context, req ghttp.Request) *getAgentListAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &getAgentListAction{
		UIBaseAction:    &uiclient.UIBaseAction{},
		getAgentListReq: &getAgentList.GetAgentListReqIdl{},
		getAgentListRes: &getAgentList.GetAgentListIdl{},
		global:          &types.GetAgentListBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getAgentListReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getAgentListRes.Data = new(getAgentList.GetAgentListRes)
	return obj
}

func (obj *getAgentListAction) ParseParams() {
	if nil == obj || nil == obj.getAgentListReq || nil == obj.getAgentListRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getAgentListReq.GetData()
}

func (obj *getAgentListAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getAgentListReq || nil == obj.getAgentListReq.Data {
		return nil
	}

	fid := obj.getAgentListReq.GetData().GetFid()
	source := obj.getAgentListReq.GetData().GetSource()
	pn := obj.getAgentListReq.GetData().GetPn()
	rn := obj.getAgentListReq.GetData().GetRn()
	arrPrivateInfo := map[string]any{
		"fid":    fid,
		"source": source,
		"pn":     pn,
		"rn":     rn,
	}

	return arrPrivateInfo
}

func (obj *getAgentListAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getAgentListReq {
		return false
	}

	return true
}

func (obj *getAgentListAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getAgentListReq || nil == obj.getAgentListReq.Data {
		return false
	}
	errNo := aichat.DoGetAgentList(obj.Ctx, obj.global, obj.getAgentListRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *getAgentListAction) BuildOut() {
	if nil == obj || nil == obj.getAgentListRes {
		return
	}
	if nil == obj.getAgentListRes.Error {
		// 默认赋值成功
		obj.getAgentListRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getAgentListRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getAgentListAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getAgentListReq || nil == obj.getAgentListReq.Data {
		return
	}
}
