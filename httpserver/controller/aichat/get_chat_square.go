package aichat

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getChatSquare"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

func GetChatSquare(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getChatSquareActionInstance(ctx, req).UiFuncHandler()
}

type getChatSquareAction struct {
	*uiclient.UIBaseAction
	getChatSquareReq *getChatSquare.GetChatSquareReqIdl
	getChatSquareRes *getChatSquare.GetChatSquareResIdl
	global           *types.GetChatSquareBaseData
}

func getChatSquareActionInstance(ctx context.Context, req ghttp.Request) *getChatSquareAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &getChatSquareAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		getChatSquareReq: &getChatSquare.GetChatSquareReqIdl{},
		getChatSquareRes: &getChatSquare.GetChatSquareResIdl{},
		global:           &types.GetChatSquareBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getChatSquareReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getChatSquareRes.Data = new(getChatSquare.GetChatSquareResData)
	return obj
}

func (obj *getChatSquareAction) ParseParams() {
	if nil == obj || nil == obj.getChatSquareReq || nil == obj.getChatSquareRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getChatSquareReq.GetData()
}

func (obj *getChatSquareAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getChatSquareReq || nil == obj.getChatSquareReq.Data {
		return nil
	}

	pn := obj.getChatSquareReq.GetData().GetPn()
	rn := obj.getChatSquareReq.GetData().GetRn()
	roleType := obj.getChatSquareReq.GetData().GetRoleType()
	if pn == 0 {
		pn = 1
	}
	if rn == 0 {
		rn = 10
	}

	arrPrivateInfo := map[string]any{
		"check_login": true, // 默认需要登录
		"pn":          pn,
		"rn":          rn,
		"role_type":   roleType,
	}

	return arrPrivateInfo
}

func (obj *getChatSquareAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getChatSquareReq {
		return false
	}

	return true
}

func (obj *getChatSquareAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getChatSquareReq || nil == obj.getChatSquareReq.Data {
		return false
	}
	errNo := aichat.DoGetChatSquare(obj.Ctx, obj.global, obj.getChatSquareRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *getChatSquareAction) BuildOut() {
	if nil == obj || nil == obj.getChatSquareRes {
		return
	}
	if nil == obj.getChatSquareRes.Error {
		// 默认赋值成功
		obj.getChatSquareRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getChatSquareRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getChatSquareAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getChatSquareReq || nil == obj.getChatSquareReq.Data {
		return
	}
}
