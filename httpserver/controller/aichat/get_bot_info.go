package aichat

import (
	"context"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getAichatBotInfo"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

func GetBotInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getBotInfoActionInstance(ctx, req).UiFuncHandler()
}

type getBotInfoAction struct {
	*uiclient.UIBaseAction
	getBotInfoReq *getAichatBotInfo.GetAichatBotInfoReqIdl
	getBotInfoRes *getAichatBotInfo.GetAichatBotInfoResIdl
	global        *types.GetAiChatBotInfoBaseData
}

func getBotInfoActionInstance(ctx context.Context, req ghttp.Request) *getBotInfoAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &getBotInfoAction{
		UIBaseAction:  &uiclient.UIBaseAction{},
		getBotInfoReq: &getAichatBotInfo.GetAichatBotInfoReqIdl{},
		getBotInfoRes: &getAichatBotInfo.GetAichatBotInfoResIdl{},
		global:        &types.GetAiChatBotInfoBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getBotInfoReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getBotInfoRes.Data = new(getAichatBotInfo.GetAichatBotInfoRes)
	return obj
}

func (obj *getBotInfoAction) ParseParams() {
	if nil == obj || nil == obj.getBotInfoReq || nil == obj.getBotInfoRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getBotInfoReq.GetData()
}

func (obj *getBotInfoAction) GetPrivateInfo() map[string]any {
	// 临时关掉接口的签名校验
	//obj.ObjRequest.AddStrategy("check_sign", false)
	if nil == obj || nil == obj.ObjRequest || nil == obj.getBotInfoReq || nil == obj.getBotInfoReq.Data {
		return nil
	}

	botUk := obj.getBotInfoReq.GetData().GetBotUk()

	arrPrivateInfo := map[string]any{
		"bot_uk": botUk,
	}
	return arrPrivateInfo
}

func (obj *getBotInfoAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getBotInfoReq {
		return false
	}
	return true
}

func (obj *getBotInfoAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getBotInfoReq {
		return false
	}
	errNo := aichat.DoGetBotInfo(obj.Ctx, obj.global, obj.getBotInfoRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *getBotInfoAction) BuildOut() {
	if nil == obj || nil == obj.getBotInfoRes {
		return
	}
	if nil == obj.getBotInfoRes.Error {
		// 默认赋值成功
		obj.getBotInfoRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getBotInfoRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getBotInfoAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getBotInfoReq || nil == obj.getBotInfoReq.Data {
		return
	}
}
