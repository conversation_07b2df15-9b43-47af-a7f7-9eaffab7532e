package aichat

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"

	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	fetchAiBotProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/fetchAiBot"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/aichat"
)

type fetchAiBotAction struct {
	*uiclient.UIBaseAction
	fetchAiBotReq *fetchAiBotProto.FetchAiBotReqIdl
	fetchAiBotRes *fetchAiBotProto.FetchAiBotResIdl
	baseData      *types.FetchAiBotBaseData
}

func FetchAiBot(ctx context.Context, req ghttp.Request) ghttp.Response {
	return fetchAiBotActionInstance(ctx, req).UiFuncHandler()
}

func fetchAiBotActionInstance(ctx context.Context, req ghttp.Request) *fetchAiBotAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &fetchAiBotAction{
		UIBaseAction:  &uiclient.UIBaseAction{},
		fetchAiBotReq: &fetchAiBotProto.FetchAiBotReqIdl{},
		fetchAiBotRes: &fetchAiBotProto.FetchAiBotResIdl{},
		baseData:      &types.FetchAiBotBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.fetchAiBotReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	obj.fetchAiBotRes.Data = &fetchAiBotProto.FetchAiBotRes{}
	return obj
}

func (obj *fetchAiBotAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.fetchAiBotReq == nil || obj.fetchAiBotReq.Data == nil {
		return
	}
	obj.baseData.Request = obj.fetchAiBotReq.GetData()
}

func (obj *fetchAiBotAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.fetchAiBotReq == nil || obj.fetchAiBotReq.Data == nil {
		return nil
	}
	privateInfo := map[string]any{
		"check_login": true,
		"need_login":  true,
		"bot_uk_list": obj.fetchAiBotReq.GetData().GetBotUkList(),
	}
	return privateInfo
}

func (obj *fetchAiBotAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.fetchAiBotReq == nil {
		return false
	}
	return true
}

func (obj *fetchAiBotAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.fetchAiBotReq == nil || obj.fetchAiBotReq.Data == nil {
		return false
	}
	errno := service.FetchAiBot(obj.Ctx, obj.baseData, obj.fetchAiBotRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}

func (obj *fetchAiBotAction) BuildOut() {
	if obj == nil || obj.fetchAiBotRes == nil {
		return
	}
	if obj.fetchAiBotRes.Error == nil {
		obj.fetchAiBotRes.Error = &clientProto.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.fetchAiBotRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *fetchAiBotAction) BuildLog() {
	return
}
