package springfestival2024

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/springfestival2024"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/springfestival2024/getRandomBlessings"

	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
)

func GetRandomBlessings(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getRandomBlessingsActionInstance(ctx, req).UiFuncHandler()
}

type getRandomBlessingsAction struct {
	*uiclient.UIBaseAction
	getRandomBlessingsReq *getRandomBlessings.GetRandomBlessingsReqIdl
	getRandomBlessingsRes *getRandomBlessings.GetRandomBlessingsResIdl
}

func getRandomBlessingsActionInstance(ctx context.Context, req ghttp.Request) *getRandomBlessingsAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &getRandomBlessingsAction{
		UIBaseAction:          &uiclient.UIBaseAction{},
		getRandomBlessingsReq: &getRandomBlessings.GetRandomBlessingsReqIdl{},
		getRandomBlessingsRes: &getRandomBlessings.GetRandomBlessingsResIdl{},
	}
	if !obj.Init(ctx, req, obj, obj.getRandomBlessingsReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.getRandomBlessingsRes.Data = &getRandomBlessings.GetRandomBlessingsRes{}
	return obj
}

func (obj *getRandomBlessingsAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.getRandomBlessingsReq == nil || obj.getRandomBlessingsRes.Data == nil {
		return
	}
}

func (obj *getRandomBlessingsAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.getRandomBlessingsReq == nil || obj.getRandomBlessingsReq.Data == nil {
		return nil
	}

	privateInfo := map[string]any{
		"check_login": true,
	}
	return privateInfo
}

func (obj *getRandomBlessingsAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getRandomBlessingsReq == nil {
		return false
	}
	return true
}

func (obj *getRandomBlessingsAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getRandomBlessingsReq == nil || obj.getRandomBlessingsReq.Data == nil {
		return false
	}
	errNo := service.GetRandomBlessings(obj.Ctx, obj.getRandomBlessingsRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}
	return true
}

func (obj *getRandomBlessingsAction) BuildOut() {
	if obj == nil || obj.getRandomBlessingsRes == nil {
		return
	}
	if obj.getRandomBlessingsRes.Error == nil {
		obj.getRandomBlessingsRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}

	outData := map[string]any{
		"data": obj.getRandomBlessingsRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getRandomBlessingsAction) BuildLog() {
	if obj == nil || obj.Ctx == nil || obj.Req == nil || obj.getRandomBlessingsReq == nil || obj.getRandomBlessingsReq.Data == nil {
		return
	}
}
