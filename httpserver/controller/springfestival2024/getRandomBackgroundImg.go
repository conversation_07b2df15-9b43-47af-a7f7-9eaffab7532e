package springfestival2024

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/springfestival2024"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/springfestival2024/getRandomBackgroundImg"

	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
)

func GetRandomBackgroundImg(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getRandomBackgroundImgActionInstance(ctx, req).UiFuncHandler()
}

type getRandomBackgroundImgAction struct {
	*uiclient.UIBaseAction
	getRandomBackgroundImgReq *getRandomBackgroundImg.GetRandomBackgroundImgReqIdl
	getRandomBackgroundImgRes *getRandomBackgroundImg.GetRandomBackgroundImgResIdl
}

func getRandomBackgroundImgActionInstance(ctx context.Context, req ghttp.Request) *getRandomBackgroundImgAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &getRandomBackgroundImgAction{
		UIBaseAction:              &uiclient.UIBaseAction{},
		getRandomBackgroundImgReq: &getRandomBackgroundImg.GetRandomBackgroundImgReqIdl{},
		getRandomBackgroundImgRes: &getRandomBackgroundImg.GetRandomBackgroundImgResIdl{},
	}
	if !obj.Init(ctx, req, obj, obj.getRandomBackgroundImgReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.getRandomBackgroundImgRes.Data = &getRandomBackgroundImg.GetRandomBackgroundImgRes{}
	return obj
}

func (obj *getRandomBackgroundImgAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.getRandomBackgroundImgReq == nil || obj.getRandomBackgroundImgRes.Data == nil {
		return
	}
}

func (obj *getRandomBackgroundImgAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.getRandomBackgroundImgReq == nil || obj.getRandomBackgroundImgReq.Data == nil {
		return nil
	}

	privateInfo := map[string]any{
		"check_login": true,
	}
	return privateInfo
}

func (obj *getRandomBackgroundImgAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getRandomBackgroundImgReq == nil {
		return false
	}
	return true
}

func (obj *getRandomBackgroundImgAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getRandomBackgroundImgReq == nil || obj.getRandomBackgroundImgReq.Data == nil {
		return false
	}
	errNo := service.GetRandomBackgroundImg(obj.Ctx, obj.getRandomBackgroundImgRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}
	return true
}

func (obj *getRandomBackgroundImgAction) BuildOut() {
	if obj == nil || obj.getRandomBackgroundImgRes == nil {
		return
	}
	if obj.getRandomBackgroundImgRes.Error == nil {
		obj.getRandomBackgroundImgRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}

	outData := map[string]any{
		"data": obj.getRandomBackgroundImgRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getRandomBackgroundImgAction) BuildLog() {
	if obj == nil || obj.Ctx == nil || obj.Req == nil || obj.getRandomBackgroundImgReq == nil || obj.getRandomBackgroundImgReq.Data == nil {
		return
	}
}
