package dialog

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/dialog/showDialog"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/dialog"
)

func ShowDialog(ctx context.Context, req ghttp.Request) ghttp.Response {
	return showDialogActionInstance(ctx, req).UiFuncHandler()
}

type showDialogAction struct {
	*uiclient.UIBaseAction
	showDialogReq *showDialog.ShowDialogReqIdl
	showDialogRes *showDialog.ShowDialogResIdl
	global        *types.ShowDialogBaseData
}

func showDialogActionInstance(ctx context.Context, req ghttp.Request) *showDialogAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &showDialogAction{
		UIBaseAction:  &uiclient.UIBaseAction{},
		showDialogReq: &showDialog.ShowDialogReqIdl{},
		showDialogRes: &showDialog.ShowDialogResIdl{},
		global:        &types.ShowDialogBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.showDialogReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.showDialogRes.Data = new(showDialog.ShowDialogRes)
	return obj
}

func (obj *showDialogAction) ParseParams() {
	if nil == obj || nil == obj.showDialogReq || nil == obj.showDialogRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.showDialogReq.GetData()
}

func (obj *showDialogAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.showDialogReq || nil == obj.showDialogReq.Data {
		return nil
	}

	dialogName := obj.showDialogReq.GetData().GetDialogName()
	dialogtype := obj.showDialogReq.GetData().GetType()

	arrPrivateInfo := map[string]any{
		"dialog_name":   dialogName,
		"type":          dialogtype,
		"user_identify": obj.showDialogReq.GetData().GetUserIdentify(),
		"token":         obj.showDialogReq.GetData().GetToken(),
	}

	return arrPrivateInfo
}

func (obj *showDialogAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.showDialogReq {
		return false
	}

	return true
}

func (obj *showDialogAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.showDialogReq || nil == obj.showDialogReq.Data {
		return false
	}
	errNo := dialog.ShowDialog(obj.Ctx, obj.global, obj.showDialogRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *showDialogAction) BuildOut() {
	if nil == obj || nil == obj.showDialogRes {
		return
	}
	if nil == obj.showDialogRes.Error {
		// 默认赋值成功
		obj.showDialogRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.showDialogRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *showDialogAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.showDialogReq || nil == obj.showDialogReq.Data {
		return
	}
}
