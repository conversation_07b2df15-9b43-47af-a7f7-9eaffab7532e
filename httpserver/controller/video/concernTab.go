package video

import (
	"context"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tracecode"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/video/concernTab"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/video"
	"strconv"
)

func ConcernTab(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getConcernTabActionInstance(ctx, req).UiFuncHandler()
}

type ConcernTabAction struct {
	*uiclient.UIBaseAction
	ConcernTabReq *concernTab.ConcernTabReqIdl
	ConcernTabRes *concernTab.ConcernTabResIdl
	global        *types.CConcernTabBaseData
}

func getConcernTabActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &ConcernTabAction{
		UIBaseAction:  &uiclient.UIBaseAction{},
		ConcernTabReq: &concernTab.ConcernTabReqIdl{},
		ConcernTabRes: &concernTab.ConcernTabResIdl{},
		global:        &types.CConcernTabBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.ConcernTabReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.global.StaticField = types.NewConcernTabStaticField()
	obj.ConcernTabRes.Data = new(concernTab.ConcernTabRes)
	return obj
}

func (obj *ConcernTabAction) ParseParams() {
	if nil == obj || nil == obj.ConcernTabReq || nil == obj.ConcernTabReq.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.ConcernTabReq.GetData()
}

func (obj *ConcernTabAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ConcernTabReq || nil == obj.ConcernTabReq.Data {
		return nil
	}

	data := obj.ConcernTabReq.Data

	limit := data.GetLimit()
	if limit == 0 {
		limit = 10
	}

	scrW := data.GetCommon().GetScrW()
	if scrW == 0 {
		scrW = 1280
	}

	scrH := data.GetCommon().GetScrH()
	if scrH == 0 {
		scrH = 1920
	}

	scrDip := data.GetCommon().GetScrDip()
	if scrDip == 0 {
		scrDip = 3
	}

	arrPrivateInfo := map[string]interface{}{
		"feed_id":   data.GetFeedId(),
		"limit":     limit,
		"load_type": data.GetLoadType(),
		"scr_w":     scrW,
		"scr_h":     scrH,
		"scr_dip":   scrDip,
		"q_type":    data.GetCommon().GetQType(),
	}

	return arrPrivateInfo
}

func (obj *ConcernTabAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ConcernTabReq {
		return false
	}

	staticField := obj.global.StaticField
	staticField.Limit = common.Tvttt(obj.ObjRequest.GetPrivateAttr("limit", 10), common.TTT_INT).(int)
	staticField.LoadType = common.Tvttt(obj.ObjRequest.GetPrivateAttr("load_type", 0), common.TTT_INT).(int)
	staticField.IntFeedId = common.Tvttt(obj.ObjRequest.GetPrivateAttr("feed_id", 0), common.TTT_INT).(int)
	staticField.IntScreenWidth = common.Tvttt(obj.ObjRequest.GetPrivateAttr("scr_w", 1280), common.TTT_INT32).(int32)
	staticField.IntScreenHeight = common.Tvttt(obj.ObjRequest.GetPrivateAttr("scr_h", 1920), common.TTT_INT32).(int32)
	staticField.IntScreenDip = common.Tvttt(obj.ObjRequest.GetPrivateAttr("scr_dip", 3), common.TTT_FLOAT64).(float64)
	staticField.IntQType = common.Tvttt(obj.ObjRequest.GetPrivateAttr("q_type", 0), common.TTT_INT32).(int32)
	staticField.RequestTimes = common.Tvttt(obj.ObjRequest.GetPrivateAttr("request_times", 0), common.TTT_INT32).(int32)

	//视频沉浸态关注Tab打底数据话题富文本解析版本控制，为此增加了对安卓版本的读取，以前默认都是1。
	staticField.IntClientType = common.Tvttt(obj.ObjRequest.GetCommonAttr("client_type", 1), common.TTT_INT).(int)
	staticField.StrClientVersion = common.Tvttt(obj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)

	if staticField.Limit < 0 || (staticField.LoadType != 0 && staticField.LoadType != 1) {
		tbcontext.WarningF(obj.Ctx, "input params invalid. limit: %d, loadType: %d", staticField.Limit, staticField.LoadType)
		obj.Error(tiebaerror.ERR_MO_PARAM_INVALID, stcdefine.GetErrMsg(tiebaerror.ERR_MO_PARAM_INVALID), true)
		return false
	}

	//关注列表：请求第一页数据时需load_type=0 feed_id=0;请求第n页(n>1)时需load_type=1,feed_id !=0;否则arch getFeedNewsList接口报错
	if (staticField.LoadType == 1 && staticField.IntFeedId == 0) || (staticField.LoadType == 0 && staticField.IntFeedId != 0) {
		tbcontext.WarningF(obj.Ctx, "input params invalid. limit: %d, loadType: %d", staticField.Limit, staticField.LoadType)
		obj.Error(tiebaerror.ERR_MO_PARAM_INVALID, stcdefine.GetErrMsg(tiebaerror.ERR_MO_PARAM_INVALID), true)
		return false
	}

	return true
}

func (obj *ConcernTabAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ConcernTabReq || nil == obj.ConcernTabReq.Data {
		return false
	}
	errNo := video.ConcernTab(obj.Ctx, obj.global, obj.ConcernTabRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *ConcernTabAction) BuildOut() {
	if nil == obj || nil == obj.ConcernTabRes {
		return
	}
	if nil == obj.ConcernTabRes.Error {
		//默认赋值成功
		obj.ConcernTabRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	//先把结构体转成map, 然后塞入concern_list下面的thread_list字段
	if obj.ConcernTabRes.GetData().GetConcernList() != nil && len(obj.ConcernTabRes.GetData().GetConcernList().GetThreadList()) == 0 {
		res := make(map[string]interface{})
		res["error_code"] = obj.ConcernTabRes.GetError().GetErrorno()
		res["error_msg"] = obj.ConcernTabRes.GetError().GetErrmsg()
		data := common.StructToMap(obj.ConcernTabRes.GetData())
		for k ,v := range data {
			res[k] = v
		}
		if listMap, ok := res["concern_list"].(map[string]interface{}); ok {
			listMap["thread_list"] = make([]*client.ThreadInfo, 0)
		}

		res["log_param"] = getLogParam(obj)

		obj.ObjResponse.SetOutData(res)
	} else {
		obj.ConcernTabRes.Data.LogParam = getLogParam(obj)

		obj.ObjResponse.SetStructData(obj.ConcernTabRes)
	}

}

// getLogParam 获取打点数据
func getLogParam(obj *ConcernTabAction) []*client.FeedKV {
	var baseData = obj.global

	if baseData == nil || baseData.Request == nil {
		return []*client.FeedKV{}
	}
	tc, _ := tracecode.GetTracecode(obj.Ctx)
	//LoadType 0就映射成1 1就是2
	refreshType := "0"
	if baseData.Request.GetLoadType() == 0 {
		refreshType = "1"
	} else if baseData.Request.GetLoadType() == 1 {
		refreshType = "2"
	}
	return []*client.FeedKV{
		{
			Key:   proto.String("trace_code"),
			Value: proto.String(tc),
		},
		{
			Key:   proto.String("request_times"),
			Value: proto.String(strconv.FormatInt(int64(baseData.Request.GetRequestTimes()), 10)),
		},
		{
			Key:   proto.String("refresh_type"),
			Value: proto.String(refreshType),
		},
		{
			Key:   proto.String("tieba_cuid"),
			Value: proto.String(baseData.Request.GetCommon().GetCuid()),
		},
		{
			Key:   proto.String("uid"),
			//Value: proto.String(common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_STRING).(string)),
			Value: proto.String(common.Tvttt(obj.global.StaticField.UserId, common.TTT_STRING).(string)),
		},
		{
			Key:   proto.String("page"),
			Value: proto.String("video"),
		},
		{
			Key:   proto.String("tab"),
			Value: proto.String("video_concernTab"),
		},
	}
}

func (obj *ConcernTabAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.ConcernTabReq || nil == obj.ConcernTabReq.Data {
		return
	}
}
