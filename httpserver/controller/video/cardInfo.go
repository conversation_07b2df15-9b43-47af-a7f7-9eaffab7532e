package video

import (
	"context"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/crypt"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/video/cardInfo"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/video"
)

func CardInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getCardInfoActionInstance(ctx, req).UiFuncHandler()
}

type CardInfoAction struct {
	*uiclient.UIBaseAction
	CardInfoReq *cardInfo.VideoCardInfoReqIdl
	CardInfoRes *cardInfo.VideoCardInfoResIdl
	global      *types.CCardInfoBaseData
}

func getCardInfoActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &CardInfoAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		CardInfoReq:  &cardInfo.VideoCardInfoReqIdl{},
		CardInfoRes:  &cardInfo.VideoCardInfoResIdl{},
		global:       &types.CCardInfoBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.CardInfoReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.global.StaticField = types.NewCardInfoStaticField()
	obj.CardInfoRes.Data = new(cardInfo.VideoCardInfoRes)
	return obj
}

func (obj *CardInfoAction) ParseParams() {
	if nil == obj || nil == obj.CardInfoReq || nil == obj.CardInfoReq.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.CardInfoReq.GetData()
}

func (obj *CardInfoAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.CardInfoReq || nil == obj.CardInfoReq.Data {
		return nil
	}

	data := obj.CardInfoReq.Data

	arrPrivateInfo := map[string]interface{}{
		"url": data.GetUrl(),
	}

	return arrPrivateInfo
}

func (obj *CardInfoAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.CardInfoReq || nil == obj.CardInfoReq.Data || len(obj.CardInfoReq.Data.GetUrl()) == 0 {
		return false
	}

	return true
}

func (obj *CardInfoAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.CardInfoReq || nil == obj.CardInfoReq.Data {
		return false
	}
	errNo := video.CardInfo(obj.Ctx, obj.global, obj.CardInfoRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *CardInfoAction) BuildOut() {
	if nil == obj || nil == obj.CardInfoRes {
		return
	}
	if nil == obj.CardInfoRes.Error {
		//默认赋值成功
		obj.CardInfoRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	dataStr, _ := jsoniter.MarshalToString(obj.CardInfoRes.GetData())
	dataStr, _ = crypt.CBCEncrypter(dataStr, "mjb&q&UEt)PLJzoy")

	outData := map[string]any{
		"data": dataStr,
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *CardInfoAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.CardInfoReq || nil == obj.CardInfoReq.Data {
		return
	}
}
