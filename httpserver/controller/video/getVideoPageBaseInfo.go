package video

import (
	"context"
	"errors"
	"strconv"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	getvideopagebaseinfoproto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/video/getVideoPageBaseInfo"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/video/getvideopagebaseinfo/adapter"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/video/getvideopagebaseinfo/gen"
)

// GetVideoPageBaseInfo _
func GetVideoPageBaseInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getVideoPageBaseInfoActionInstance(ctx, req).UiFuncHandler()
}

// getVideoPageBaseInfoAction _
type getVideoPageBaseInfoAction struct {
	*uiclient.UIBaseAction
	getVideoPageBaseInfoReq *getvideopagebaseinfoproto.GetVideoPageBaseInfoReqIdl
	getVideoPageBaseInfoRes *getvideopagebaseinfoproto.GetVideoPageBaseInfoResIdl
}

// getVideoPageBaseInfoActionInstance _
func getVideoPageBaseInfoActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	obj := &getVideoPageBaseInfoAction{
		UIBaseAction:            &uiclient.UIBaseAction{},
		getVideoPageBaseInfoReq: &getvideopagebaseinfoproto.GetVideoPageBaseInfoReqIdl{},
		getVideoPageBaseInfoRes: &getvideopagebaseinfoproto.GetVideoPageBaseInfoResIdl{
			Error: &client.Error{
				Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
				Errmsg:  proto.String(""),
				Usermsg: proto.String(""),
			},
			Data: &getvideopagebaseinfoproto.GetVideoPageBaseInfoRes{},
		},
	}
	if !obj.Init(ctx, req, obj, obj.getVideoPageBaseInfoReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	return obj
}

// ParseParams 格式化参数，由private_params算子代替
func (g *getVideoPageBaseInfoAction) ParseParams() {
	return
}

// GetPrivateInfo 获取私有参数，由private_params算子代替
func (g *getVideoPageBaseInfoAction) GetPrivateInfo() map[string]any {
	return map[string]any{}
}

// CheckPrivate 校验私有参数，由private_params算子代替
func (g *getVideoPageBaseInfoAction) CheckPrivate() bool {
	return true
}

func (g *getVideoPageBaseInfoAction) Execute() bool {
	if g == nil || g.ObjRequest == nil || g.ObjResponse == nil || g.getVideoPageBaseInfoRes == nil {
		return false
	}

	ll := logging.WrapLogit(g.Ctx, tbcontext.GetLogidString(g.Ctx), loggerResource.GetLoggerService())
	defer func() {
		logging.ReleaseLogitLogger(ll)
	}()

	engCtx := resource.GetVideoPageBaseInfoEngine.NewContext(g.Ctx, []engine.ContextOption{
		engine.WithLogger(ll.AsEngineLogger()),
	}...)
	defer func() {
		engine.ReleaseContext(engCtx)
	}()

	engCtx.MustRegisterInstance(&g.UIBaseAction)
	err := resource.GetVideoPageBaseInfoEngine.Run(engCtx)

	// 记录算子耗时
	engCtx.RangeOpCost(func(opName string, cost time.Duration) {
		g.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	// 算子中设置过错误号，使用算子设置的错误号作为返回
	var state engine.State
	if errors.As(err, &state) {
		tbcontext.WarningF(g.Ctx, "exgraph execute fail, code=%d, errmsg=%s", state.Code(), state.Error())
		g.Error(state.Code(), state.Error(), false)
		return false
	}

	// 未设置过错误号，兜底返回错误信息
	if err != nil {
		tbcontext.WarningF(g.Ctx, "exgraph execute fail, err=%v", err)
		g.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, stcdefine.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return false
	}

	var output getvideopagebaseinfoproto.GetVideoPageBaseInfoRes
	ok := engCtx.MutableInstance(&output)
	if !ok {
		tbcontext.WarningF(g.Ctx, "get get_video_page_base_info output fail")
		return false
	}
	g.getVideoPageBaseInfoRes.Data = &output
	return true
}

func (g *getVideoPageBaseInfoAction) BuildOut() {
	if g == nil || g.getVideoPageBaseInfoRes == nil {
		return
	}

	g.ObjResponse.SetOutData(
		map[string]any{
			"data": g.getVideoPageBaseInfoRes.GetData(),
		},
	)
	return
}

func (g *getVideoPageBaseInfoAction) BuildLog() {
	return
}
