package video

import (
	"context"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/video/redIcon"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/video"
)

func RedIcon(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getRedIconActionInstance(ctx, req).UiFuncHandler()
}

type RedIconAction struct {
	*uiclient.UIBaseAction
	RedIconReq *redIcon.VideoRedIconReqIdl
	RedIconRes *redIcon.VideoRedIconResIdl
	global     *types.CRedIconBaseData
}

func getRedIconActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &RedIconAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		RedIconReq:   &redIcon.VideoRedIconReqIdl{},
		RedIconRes:   &redIcon.VideoRedIconResIdl{},
		global:       &types.CRedIconBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.RedIconReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.global.StaticField = types.NewRedIconStaticField()
	obj.RedIconRes.Data = new(redIcon.VideoRedIconRes)
	return obj
}

func (obj *RedIconAction) ParseParams() {
	if nil == obj || nil == obj.RedIconReq || nil == obj.RedIconReq.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.RedIconReq.GetData()
}

func (obj *RedIconAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.RedIconReq || nil == obj.RedIconReq.Data {
		return nil
	}

	data := obj.RedIconReq.Data

	arrPrivateInfo := map[string]interface{}{
		"call_from": data.GetCallFrom(),
	}

	return arrPrivateInfo
}

func (obj *RedIconAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.RedIconReq {
		return false
	}

	return true
}

func (obj *RedIconAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.RedIconReq || nil == obj.RedIconReq.Data {
		return false
	}
	errNo := video.VideoRedIcon(obj.Ctx, obj.global, obj.RedIconRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *RedIconAction) BuildOut() {
	if nil == obj || nil == obj.RedIconRes {
		return
	}
	if nil == obj.RedIconRes.Error {
		//默认赋值成功
		obj.RedIconRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	obj.ObjResponse.SetStructData(obj.RedIconRes)

}

func (obj *RedIconAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.RedIconReq || nil == obj.RedIconReq.Data {
		return
	}
}
