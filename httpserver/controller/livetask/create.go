package livetask

import (
	"context"

	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/livetask"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	createProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/liveTask/create"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// Create 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func Create(ctx context.Context, req ghttp.Request) ghttp.Response {
	return createActionInstance(ctx, req).UiFuncHandler()
}

// createAction 服务相关结构体
type createAction struct {
	*uiclient.UIBaseAction
	createReq *createProto.CreateCommentLiveTaskReqIdl
	createRes *createProto.CreateCommentLiveTaskResIdl
	baseData  *types.CreateBaseData
}

// createActionInstance 创建一个createAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回createAction实例
func createActionInstance(ctx context.Context, req ghttp.Request) *createAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &createAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		createReq:    &createProto.CreateCommentLiveTaskReqIdl{},
		createRes: &createProto.CreateCommentLiveTaskResIdl{
			Data:  &createProto.CreateCommentLiveTaskRes{},
			Error: &client.Error{},
		},
		baseData: &types.CreateBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.createReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

// ParseParams 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *createAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.createReq == nil || obj.createReq.Data == nil {
		return
	}
	obj.baseData.Request = obj.createReq.GetData()
}

// GetPrivateInfo 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *createAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.createReq == nil || obj.createReq.Data == nil {
		return nil
	}
	privateInfo := map[string]any{
		"check_login": true,
		"need_login":  true,
	}
	return privateInfo
}

// CheckPrivate 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *createAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.createReq == nil {
		return false
	}
	return true
}

// BuildOut 构建输出数据的方法
// @Param：无
// @Return：无
func (obj *createAction) BuildOut() {
	if obj == nil || obj.createRes == nil {
		return
	}
	if obj.createRes.Error == nil {
		obj.createRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.createRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

// BuildLog 创建日志
// @Param：无
// @Return：无
func (obj *createAction) BuildLog() {
	return
}

// Execute 执行createAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *createAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.createReq == nil || obj.createReq.Data == nil {
		return false
	}
	errno, errmsg, data := livetask.CreateLiveTask(obj.Ctx, obj.baseData)
	if errno == tiebaerror.ERR_PARAM_ERROR || errno == tiebaerror.ERR_CALL_SERVICE_FAIL {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	} else if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, errmsg, true)
		return false
	}
	obj.createRes.Data = data
	return true
}
