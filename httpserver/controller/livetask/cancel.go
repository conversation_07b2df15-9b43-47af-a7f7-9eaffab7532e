package livetask

import (
	"context"

	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	cancelproto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/liveTask/cancel"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/livetask"
)

// Cancel Function Cancel
// 取消操作实例，返回ghttp.Response类型的响应值
func Cancel(ctx context.Context, req ghttp.Request) ghttp.Response {
	return cancelActionInstance(ctx, req).UiFuncHandler()
}

type cancelAction struct {
	*uiclient.UIBaseAction
	cancelReq *cancelproto.CancelReqIdl
	cancelRes *cancelproto.CancelResIdl
	global    *types.CancelBaseData
}

// cancelActionInstance cancelActionInstance 创建一个取消操作实例，返回指针类型的cancelAction结构体指针
// ctx (context.Context) 上下文对象，不能为nil
// req (ghttp.Request) HTTP请求对象，不能为nil
// 返回值 (cancelAction) 类型为*cancelAction，如果ctx或req为nil则返回nil
func cancelActionInstance(ctx context.Context, req ghttp.Request) *cancelAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &cancelAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		cancelReq:    &cancelproto.CancelReqIdl{},
		cancelRes:    &cancelproto.CancelResIdl{},
		global:       &types.CancelBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.cancelReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.cancelRes.Error = nil
	return obj
}

// ParseParams 解析参数函数
// 参数：nil
// 返回值：无返回值
func (obj *cancelAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.cancelReq == nil || obj.cancelReq.Data == nil {
		return
	}
	obj.global.Request = obj.cancelReq.GetData()
}

// GetPrivateInfo 获取私有信息，返回一个包含三个键值对的map，键为"check_login"、"forum_id"和"private_info"，分别表示是否需要登录、论坛ID以及私有信息
func (obj *cancelAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.cancelReq == nil || obj.cancelReq.Data == nil {
		return nil
	}
	forumId := obj.global.Request.GetForumId()
	taskId := obj.global.Request.GetTaskId()
	return map[string]any{
		"need_login":  true,
		"check_login": true,
		"forum_id":    forumId,
		"task_id":     taskId,
	}
}

// CheckPrivate 判断是否为私有请求，返回bool类型值，如果obj为nil或者ObjRequest和cancelReq均为nil则返回false，否则返回true
func (obj *cancelAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.cancelReq == nil {
		return false
	}
	return true
}

// Execute Execute 执行取消操作，返回一个bool类型的结果，表示是否成功
// 如果对象或者上下文、请求、响应为nil，则返回false
func (obj *cancelAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.cancelReq == nil || obj.cancelReq.Data == nil {
		return false
	}
	errNo := livetask.CancelTask(obj.Ctx, obj.global, obj.cancelRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}
	return true
}

// BuildOut BuildOut 用于构建取消动作的输出信息，如果对象或取消结果为空则直接返回
func (obj *cancelAction) BuildOut() {
	if obj == nil || obj.cancelRes == nil {
		return
	}
	if obj.cancelRes.Error == nil {
		obj.cancelRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
}

// BuildLog BuildLog
// 构建日志，如果对象为空或上下文、请求、取消响应都为nil则不进行操作
func (obj *cancelAction) BuildLog() {
	if obj == nil || obj.Ctx == nil || obj.Req == nil || obj.cancelRes == nil {
		return
	}
}
