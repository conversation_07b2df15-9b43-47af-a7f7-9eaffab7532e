package pb

import (
	"context"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	golibExgraph "icode.baidu.com/baidu/tieba-server-go/golib2/exgraph"
	"icode.baidu.com/baidu/tieba-server-go/golib2/passport"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/pb/floor/core"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/pb/floor/ext"
)

const FloorEngineName = "pb_floor"

var floorExgraph engine.Engine

func init() {
	err := golibExgraph.InitExgraph()
	if err != nil {
		panic(err)
	}
	floorExgraph, err = golibExgraph.GetEngineByName(FloorEngineName)
	if err != nil {
		panic(err)
	}
	passport.InitDefault()
}

func PbFloor(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getPbFloorActionInstance(ctx, req).UiFuncHandler()
}

type floorAction struct {
	*uiclient.UIBaseAction
	floorReq *floor.PbFloorReqIdl
	floorRes *floor.PbFloorResIdl
	global   *types.CPbFloorBaseData
}

func getPbFloorActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &floorAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		floorReq:     &floor.PbFloorReqIdl{},
		floorRes:     &floor.PbFloorResIdl{},
		global:       &types.CPbFloorBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.floorReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.floorRes.Data = new(floor.PbFloorRes)
	return obj
}

func (obj *floorAction) ParseParams() {
	if nil == obj || nil == obj.floorReq || nil == obj.ObjRequest {
		return
	}
	if nil == obj.floorRes.Error {
		//默认赋值成功
		obj.floorRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}
	obj.global.Request = obj.floorReq
}

func (obj *floorAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.floorReq {
		return nil
	}
	obj.global.StaticField = types.NewPbFloorStaticField()

	obj.ObjRequest.SetStrategy(map[string]interface{}{
		"check_nonu": false,
	})
	floorData := obj.floorReq.GetData()
	pn := floorData.GetPn()
	if pn == 0 {
		pn = 1
	}

	arrPrivateInfo := map[string]interface{}{
		"check_login": false,
		"kz":          floorData.GetKz(),
		//"z"    : intval($this->_getInput("z", 0)),
		"pid":             floorData.GetPid(),
		"spid":            floorData.GetSpid(),
		"pn":              pn,
		"scr_w":           floorData.GetCommon().GetScrW(),
		"scr_h":           floorData.GetCommon().GetScrH(),
		"scr_dip":         floorData.GetCommon().GetScrDip(),
		"q_type":          floorData.GetCommon().GetQType(),
		"is_comm_reverse": floorData.GetIsCommReverse(),
		"forum_id":        floorData.GetForumId(),
	}

	return arrPrivateInfo
}

func (obj *floorAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.floorReq {
		return false
	}

	return true
}

func (obj *floorAction) Execute() bool {
	if nil == obj || nil == obj.floorReq || nil == obj.ObjRequest {
		return false
	}

	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}

	exctx := floorExgraph.NewContext(obj.Ctx, opts...)

	exctx.MustRegisterInstance(&obj.global)
	exctx.MustRegisterInstance(&obj.floorRes.Data)

	err := floorExgraph.Run(exctx)
	if err != nil {
		tbcontext.WarningF(obj.Ctx, "exgraph engine run fail.err:%s", err.Error())
	}

	exctx.RangeOpCost(func(opName string, cost time.Duration) {
		obj.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	return true
}

func (obj *floorAction) BuildOut() {
	if nil == obj || nil == obj.floorRes {
		return
	}
	obj.ObjResponse.SetStructData(obj.floorRes)
}

func (obj *floorAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.floorReq {
		return
	}
}
