package pb

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/hiphoto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	golibExgraph "icode.baidu.com/baidu/tieba-server-go/golib2/exgraph"
	"icode.baidu.com/baidu/tieba-server-go/golib2/passport"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/golib2/trace"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/pb/picpage/core"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/pb/picpage/ext"
)

const CoreEngineName = "pb_picpage"
const LiteEngineName = "pb_piclite"

var picpageExgraph engine.Engine
var picpageLiteExgraph engine.Engine

func init() {
	err := golibExgraph.InitExgraph()
	if err != nil {
		panic(err)
	}
	picpageExgraph, err = golibExgraph.GetEngineByName(CoreEngineName)
	if err != nil {
		panic(err)
	}
	picpageLiteExgraph, err = golibExgraph.GetEngineByName(LiteEngineName)
	if err != nil {
		panic(err)
	}
	passport.InitDefault()
}

func Picpage(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getPicpageActionInstance(ctx, req).UiFuncHandler()
}

type picpageAction struct {
	*uiclient.UIBaseAction
	picpageReq *picpage.PicpageReqIdl
	picpageRes *picpage.PicpageResIdl
	global     *types.CPicpageBaseData
}

func getPicpageActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &picpageAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		picpageReq:   &picpage.PicpageReqIdl{},
		picpageRes:   &picpage.PicpageResIdl{},
		global:       &types.CPicpageBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.picpageReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.picpageRes.Data = new(picpage.PicpageRes)
	return obj
}

func (obj *picpageAction) ParseParams() {
	if nil == obj || nil == obj.picpageReq || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.picpageReq
}

func (obj *picpageAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.picpageReq {
		return nil
	}

	obj.global.StaticField = types.NewPicpageStaticField()
	obj.ObjRequest.SetStrategy(map[string]interface{}{
		"check_nonu": false,
	})

	picpageReq := obj.picpageReq.GetData()
	picId := strings.TrimSpace(obj.Req.PostFormDefault("pic_id", ""))
	if picId == "" {
		picId = obj.Req.QueryDefault("pic_id", "")
	}

	forumName := strings.TrimSpace(obj.Req.PostFormDefault("kw", ""))
	if forumName == "" {
		forumName = obj.Req.QueryDefault("kw", "")
	}

	arrPrivateInfo := map[string]interface{}{
		"check_login":        false,
		"forum_name":         forumName,
		"pic_id":             picId,
		"tid":                picpageReq.GetTid(),
		"next":               picpageReq.GetNext(),
		"prev":               picpageReq.GetPrev(),
		"is_note":            picpageReq.GetIsNote(),
		"aladdin_src_id":     picpageReq.GetAladdinSrcId(),
		"filter_first_floor": picpageReq.GetFilterFirstFloor(),
		"not_see_lz":         picpageReq.GetNotSeeLz(),
		"scr_w":              picpageReq.GetScrW(),
		"scr_h":              picpageReq.GetScrH(),
		"scr_dip":            picpageReq.GetScrDip(),
		"q_type":             picpageReq.GetQType(),
		"r":                  picpageReq.GetR(),
		"picfrom":            strings.TrimSpace(picpageReq.GetPicfrom()),
		"user_id":            picpageReq.GetUserId(),
		"intFid":             picpageReq.GetForumId(),
		"pic_index":          picpageReq.GetPicIndex(),
		"da_idfa":            strings.TrimSpace(picpageReq.GetDaIdfa()),
		"platform":           strings.TrimSpace(picpageReq.GetPlatform()),
		"_os_version":        strings.TrimSpace(picpageReq.GetXOsVersion()),
		"shoubai_cuid":       strings.TrimSpace(picpageReq.GetShoubaiCuid()),
		"obj_type":           strings.TrimSpace(picpageReq.GetObjType()),
		"is_top_agree":       picpageReq.GetIsTopAgree(),
		"source":             picpageReq.GetSource(),
		"cuid_galaxy2":       strings.TrimSpace(picpageReq.GetCuidGalaxy2()),
		"post_id":            picpageReq.GetPostId(),
		"ad_ext_params":      strings.TrimSpace(picpageReq.GetAdExtParams()),     // 实时嗅探参数
		"app_transmit_data":  strings.TrimSpace(picpageReq.GetAppTransmitData()), // 12.11新增的APP透传参数
		"current_post_id":    picpageReq.GetCurrentPostId(),                      // 请求大图页对应的post_id
	}

	fid, _ := hiphoto.DecodePicUrlCryptRetTwoID(picId)
	obj.global.StaticField.FidFromPicID = fid

	tbcontext.AddNotice(obj.Ctx, "tid", picpageReq.GetTid())
	tbcontext.AddNotice(obj.Ctx, "pic_id", picId)

	return arrPrivateInfo
}

func (obj *picpageAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.picpageReq {
		return false
	}
	objReq := obj.ObjRequest
	forumId := common.Tvttt(objReq.GetPrivateAttr("intFid", 0), common.TTT_UINT32).(uint32)
	forumName := common.Tvttt(objReq.GetPrivateAttr("forum_name", 0), common.TTT_STRING).(string)
	tid := common.Tvttt(objReq.GetPrivateAttr("tid", 0), common.TTT_INT64).(int64)
	next := common.Tvttt(objReq.GetPrivateAttr("next", 0), common.TTT_INT).(int)
	prev := common.Tvttt(objReq.GetPrivateAttr("prev", 0), common.TTT_INT).(int)
	notSeeLz := common.Tvttt(objReq.GetPrivateAttr("not_see_lz", 0), common.TTT_INT).(int)

	if forumName == "" && forumId != 0 && forumId != 24981790 {
		tbcontext.WarningF(obj.Ctx, "request error: forum name is empty.")
		obj.Error(tiebaerror.ERR_MO_PARAM_INVALID, stcdefine.GetErrMsg(tiebaerror.ERR_MO_PARAM_INVALID), true)
		return false
	}

	if tid < 0 {
		tbcontext.WarningF(obj.Ctx, "request error: illegal tid: %d", tid)
		obj.Error(tiebaerror.ERR_MO_PARAM_INVALID, stcdefine.GetErrMsg(tiebaerror.ERR_MO_PARAM_INVALID), true)
		return false
	}

	if prev < 0 {
		tbcontext.WarningF(obj.Ctx, "request error: illegal prev: %d", prev)
		obj.Error(tiebaerror.ERR_MO_PARAM_INVALID, stcdefine.GetErrMsg(tiebaerror.ERR_MO_PARAM_INVALID), true)
		return false
	}

	if next < 0 {
		tbcontext.WarningF(obj.Ctx, "request error: illegal next: %d", next)
		obj.Error(tiebaerror.ERR_MO_PARAM_INVALID, stcdefine.GetErrMsg(tiebaerror.ERR_MO_PARAM_INVALID), true)
		return false
	}

	if notSeeLz < 0 {
		tbcontext.WarningF(obj.Ctx, "request error: illegal not_see_lz: %d", notSeeLz)
		obj.Error(tiebaerror.ERR_MO_PARAM_INVALID, stcdefine.GetErrMsg(tiebaerror.ERR_MO_PARAM_INVALID), true)
		return false
	}

	return true
}

func (obj *picpageAction) Execute() bool {
	if nil == obj || nil == obj.picpageReq || nil == obj.ObjRequest {
		return false
	}

	obj.global.DataSec = golibExgraph.NewDataSec()
	defer func() {
		golibExgraph.ReleaseDataSec(obj.global.DataSec)
	}()
	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}
	var e engine.Engine
	subappType := common.Tvttt(obj.ObjRequest.GetCommonAttr("subapp_type", ""), common.TTT_STRING).(string)
	if clientvers.IsNoAPP(subappType) {
		e = picpageLiteExgraph
	} else {
		e = picpageExgraph
	}
	exctx := e.NewContext(obj.Ctx, opts...)
	exctx.MustRegisterInstance(&obj.global)
	exctx.MustRegisterInstance(&obj.picpageRes.Data)
	err := e.Run(exctx)
	if err != nil {
		tbcontext.WarningF(obj.Ctx, "exgraph engine run fail.err:%s", err.Error())
	}

	exctx.RangeOpCost(func(opName string, cost time.Duration) {
		obj.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	return true
}

func (obj *picpageAction) BuildOut() {
	if nil == obj || nil == obj.picpageRes {
		return
	}
	obj.ObjResponse.SetStructData(obj.picpageRes)

	obj.ObjResponse.SetAppendDataFunc(func(stOutDataPtr interface{}) error {
		frsPageRes, ok := stOutDataPtr.(*picpage.PicpageResIdl)
		if !ok {
			return fmt.Errorf("get picpageIdl fail")
		}
		if frsPageRes.Data == nil {
			return fmt.Errorf("res data is nil")
		}
		logId, err := tbcontext.GetLogidUint32(obj.Ctx)
		if err != nil {
			tbcontext.WarningF(obj.Ctx, "get log id fail: %v", err)
		}
		traceTime := trace.GetTime(obj.Ctx, "server_time")
		frsPageRes.Data.Ctime = proto.Int32(0)
		frsPageRes.Data.Time = proto.Int64(time.Now().Unix())
		frsPageRes.Data.Logid = proto.Uint32(logId)
		frsPageRes.Data.ServerTime = proto.Int32(int32(traceTime.Microseconds()))

		return nil
	})
}

func (obj *picpageAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.picpageReq {
		return
	}
}
