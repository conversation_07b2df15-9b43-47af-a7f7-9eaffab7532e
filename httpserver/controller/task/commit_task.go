package task

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/task/commitTask"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/task"
)

func CommitTask(ctx context.Context, req ghttp.Request) ghttp.Response {
	return commitTaskActionInstance(ctx, req).UiFuncHandler()
}

type commitTaskAction struct {
	*uiclient.UIBaseAction
	commitTaskReq *commitTask.CommitTaskReqIdl
	commitTaskRes *commitTask.CommitTaskResIdl
	global        *types.CommitTaskBaseData
}

func commitTaskActionInstance(ctx context.Context, req ghttp.Request) *commitTaskAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &commitTaskAction{
		UIBaseAction:  &uiclient.UIBaseAction{},
		commitTaskReq: &commitTask.CommitTaskReqIdl{},
		commitTaskRes: &commitTask.CommitTaskResIdl{},
		global:        &types.CommitTaskBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.commitTaskReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.commitTaskRes.Data = &commitTask.CommitTaskRes{}
	return obj
}

func (obj *commitTaskAction) ParseParams() {
	if nil == obj || nil == obj.commitTaskReq || nil == obj.commitTaskRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.commitTaskReq.GetData()
}

func (obj *commitTaskAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.commitTaskReq || nil == obj.commitTaskReq.Data {
		return nil
	}
	obj.ObjRequest.AddStrategy("check_sign", false)
	arrPrivateInfo := map[string]any{}
	return arrPrivateInfo
}

func (obj *commitTaskAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.commitTaskReq {
		return false
	}
	return true
}

func (obj *commitTaskAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.commitTaskReq || nil == obj.commitTaskReq.Data {
		return false
	}

	errNo := task.NewCommitTask(obj.global).Execute(obj.Ctx, obj.global, obj.commitTaskRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *commitTaskAction) BuildOut() {
	if nil == obj || nil == obj.commitTaskRes {
		return
	}
	if nil == obj.commitTaskRes.Error {
		// 默认赋值成功
		obj.commitTaskRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}
	outData := map[string]any{
		"data": obj.commitTaskRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *commitTaskAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.commitTaskReq || nil == obj.commitTaskReq.Data {
		return
	}
}
