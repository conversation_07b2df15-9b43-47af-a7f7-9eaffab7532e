package recommend

import (
	"context"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"strconv"
	"strings"

	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/recommend/newRecommendCard"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/recommend"
)

func NewRecommendCard(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getNewRecommendCard(ctx, req).UiFuncHandler()
}

type newRecommendCardAction struct {
	*uiclient.UIBaseAction
	newRecommendCardReq *newRecommendCard.NewRecommendCardReq
	newRecommendCardRes *newRecommendCard.NewRecommendCardRes
}

func getNewRecommendCard(ctx context.Context, req ghttp.Request) *newRecommendCardAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &newRecommendCardAction{
		UIBaseAction:        &uiclient.UIBaseAction{},
		newRecommendCardReq: &newRecommendCard.NewRecommendCardReq{},
		newRecommendCardRes: &newRecommendCard.NewRecommendCardRes{},
	}
	if !obj.Init(ctx, req, obj, obj.newRecommendCardReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.newRecommendCardRes.Data = &newRecommendCard.NewRecommendCardData{}
	return obj
}

func (obj *newRecommendCardAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.newRecommendCardReq == nil || obj.newRecommendCardRes.Data == nil {
		return
	}
}

func (obj *newRecommendCardAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.newRecommendCardReq == nil || obj.newRecommendCardRes.Data == nil {
		return nil
	}

	privateInfo := map[string]any{
		"check_login": true,
	}
	return privateInfo
}

func (obj *newRecommendCardAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.newRecommendCardReq == nil {
		return false
	}
	return true
}

func (obj *newRecommendCardAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.newRecommendCardReq == nil {
		return false
	}
	userID := common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", ""), common.TTT_INT64).(int64)
	// 生成page下的第一个组件
	clientType := common.Tvttt(obj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	clientVersion := common.Tvttt(obj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	cuid := common.Tvttt(obj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)

	// 取实验
	var sampleIds []string
	strSampleId := common.Tvttt(obj.ObjRequest.GetCommonAttr("sample_id", ""), common.TTT_STRING).(string)
	if strSampleId == "" {
		strSampleId = obj.getSampleIdCache()
	}
	arrSampleIdsTmp := UbsAbtest.GetUbsAbtestConfig(obj.Ctx, strSampleId, strconv.FormatInt(userID, 10), types.WLUbsAbtestConfigTab)
	for _, tmp := range arrSampleIdsTmp {
		if _, ok := tmp["sid"]; ok {
			sampleIds = append(sampleIds, tmp["sid"])
		}
	}

	errNo := service.GetNewRecommendCard(obj.Ctx, clientType, clientVersion, cuid, userID, obj.newRecommendCardReq,
		obj.newRecommendCardRes, sampleIds)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}
	return true
}

func (obj *newRecommendCardAction) BuildOut() {
	if obj == nil || obj.newRecommendCardRes == nil {
		return
	}
	if obj.newRecommendCardRes.Error == nil {
		obj.newRecommendCardRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}

	outData := map[string]any{
		"data": obj.newRecommendCardRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *newRecommendCardAction) BuildLog() {
	if obj == nil || obj.Ctx == nil || obj.Req == nil || obj.newRecommendCardReq == nil {
		return
	}
}


func (obj *newRecommendCardAction) getSampleIdCache() string {
	userId := common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	cuid := common.Tvttt(obj.ObjRequest.GetCommonAttr("CUID", ""), common.TTT_STRING).(string)
	intClientType := common.Tvttt(obj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	strClientVersion := common.Tvttt(obj.ObjRequest.GetCommonAttr("client_version", 0), common.TTT_STRING).(string)
	userInfo := []string{
		strconv.FormatInt(userId, 10),
		cuid,
		strconv.Itoa(intClientType),
		strClientVersion,
	}
	userInfoStr := strings.Join(userInfo, "-")
	keyMD5UserInfo := php2go.Md5(userInfoStr)

	redisKey := types.PreKeySampleIdCache + keyMD5UserInfo
	stringCmder := resource.RedisSign.Get(obj.Ctx, redisKey)
	if stringCmder.Err() != nil {
		return ""
	}
	return stringCmder.Val()
}