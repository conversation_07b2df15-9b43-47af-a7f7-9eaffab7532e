// Package raffle
// @Description:  春节出卡逻辑
package raffle

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/raffle/showPrize"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/raffle"
)

func ShowPrize(ctx context.Context, req ghttp.Request) ghttp.Response {
	return showPrizeActionInstance(ctx, req).UiFuncHandler()
}

type ShowPrizeAction struct {
	*uiclient.UIBaseAction
	ShowPrizeReq *showPrize.ShowPrizeReqIdl
	ShowPrizeRes *showPrize.ShowPrizeResIdl
	global       *types.ShowPrize2024BaseData
}

func showPrizeActionInstance(ctx context.Context, req ghttp.Request) *ShowPrizeAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &ShowPrizeAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		ShowPrizeReq: &showPrize.ShowPrizeReqIdl{},
		ShowPrizeRes: &showPrize.ShowPrizeResIdl{},
		global:       &types.ShowPrize2024BaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.ShowPrizeReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.ShowPrizeRes.Data = new(showPrize.ShowPrizeRes)

	return obj
}

func (obj *ShowPrizeAction) ParseParams() {

}

func (obj *ShowPrizeAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ShowPrizeReq || nil == obj.ShowPrizeReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.ShowPrizeReq.GetData()

	source := obj.ShowPrizeReq.GetData().GetSource()
	pid := obj.ShowPrizeReq.GetData().GetProductId()

	arrPrivateInfo := map[string]any{
		"source":     source,
		"product_id": pid,
	}

	return arrPrivateInfo
}

func (obj *ShowPrizeAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ShowPrizeReq {
		return false
	}

	return true
}

func (obj *ShowPrizeAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ShowPrizeReq || nil == obj.ShowPrizeReq.Data {
		return false
	}
	errNo := raffle.ShowPrize(obj.Ctx, obj.global, obj.ShowPrizeRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *ShowPrizeAction) BuildOut() {
	if nil == obj || nil == obj.ShowPrizeRes {
		return
	}
	if nil == obj.ShowPrizeRes.Error {
		// 默认赋值成功
		obj.ShowPrizeRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.ShowPrizeRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *ShowPrizeAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.ShowPrizeReq || nil == obj.ShowPrizeReq.Data {
		return
	}
}
