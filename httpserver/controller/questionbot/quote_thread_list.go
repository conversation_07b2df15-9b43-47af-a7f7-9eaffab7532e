package questionbot

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	quoteproto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/questionbot/quoteThreadList"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/questionbot"
)

// QuoteThreadList 入口函数
func QuoteThreadList(ctx context.Context, req ghttp.Request) ghttp.Response {
	return quoteThreadListActionInstance(ctx, req).UiFuncHandler()
}

// quoteThreadListAction 服务相关结构体
type quoteThreadListAction struct {
	*uiclient.UIBaseAction
	quoteThreadListReq *quoteproto.QuoteThreadListReqIdl
	quoteThreadListRes *quoteproto.QuoteThreadListResIdl
}

// quoteThreadListActionInstance 创建一个callbackAction实例
func quoteThreadListActionInstance(ctx context.Context, req ghttp.Request) *quoteThreadListAction {
	ar := &quoteThreadListAction{
		UIBaseAction:       &uiclient.UIBaseAction{},
		quoteThreadListReq: &quoteproto.QuoteThreadListReqIdl{},
		quoteThreadListRes: &quoteproto.QuoteThreadListResIdl{
			Error: &client.Error{
				Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
				Errmsg:  proto.String(""),
				Usermsg: proto.String(""),
			},
		},
	}
	if !ar.Init(ctx, req, ar, ar.quoteThreadListReq) {
		tbcontext.FatalF(ar.Ctx, "UIBase init fail")
	}
	return ar
}

// ParseParams 解析参数
func (q *quoteThreadListAction) ParseParams() {
}

// GetPrivateInfo 设置私有参数
func (q *quoteThreadListAction) GetPrivateInfo() map[string]any {
	if q.ObjRequest == nil {
		return nil
	}
	q.ObjRequest.AddStrategy("check_sign", false)
	return map[string]any{
		"check_login": false,
		"need_login":  false,
	}
}

// CheckPrivate 校验合法性
func (q *quoteThreadListAction) CheckPrivate() bool {
	return true
}

// BuildOut 构建输出数据的方法
func (q *quoteThreadListAction) BuildOut() {
	q.ObjResponse.SetOutData(map[string]any{
		"data": q.quoteThreadListRes.GetData(),
	})
	return
}

// BuildLog 创建日志
func (q *quoteThreadListAction) BuildLog() {
	return
}

// Execute 执行callbackAction的逻辑
func (q *quoteThreadListAction) Execute() bool {
	errno := questionbot.QuoteThreadList(q.Ctx, q.UIBaseAction, q.quoteThreadListReq, q.quoteThreadListRes)
	if errno != tiebaerror.ERR_SUCCESS {
		q.Error(errno, tiebaerror.GetErrMsg(errno), true)
		return false
	}
	return true
}
