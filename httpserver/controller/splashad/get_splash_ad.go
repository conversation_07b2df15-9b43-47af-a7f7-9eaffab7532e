package splashad

import (
	"context"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/cmap"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/splashad"

	"icode.baidu.com/baidu/gdp/ghttp"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
)

// GetSplashAd 获取开屏广告信息，返回一个ghttp.Response类型的值
func GetSplashAd(ctx context.Context, req ghttp.Request) ghttp.Response {
	return SplashActionInstance(ctx, req).UiFuncHandler()
}

type SplashAdAction struct {
	*uiclient.UIBaseAction
	SplashAdReq *commonProto.SplashReqIdl
	SplashAdRes *commonProto.SplashResIdl
	global      *types.SplashDataData
}

// SplashActionInstance SplashActionInstance 创建一个 SplashAdAction 实例，用于处理启动广告的请求和返回值
// ctx context.Context 上下文对象，不能为nil
// req ghttp.Request HTTP请求对象，不能为nil
// 返回值 *SplashAdAction SplashAdAction类型指针，包含了初始化后的所有信息
func SplashActionInstance(ctx context.Context, req ghttp.Request) *SplashAdAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &SplashAdAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		SplashAdReq:  &commonProto.SplashReqIdl{},
		SplashAdRes:  &commonProto.SplashResIdl{},
		global:       &types.SplashDataData{},
	}
	if !obj.Init(ctx, req, obj, obj.SplashAdReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.global.Req = obj.SplashAdReq
	obj.SplashAdRes = new(commonProto.SplashResIdl)
	obj.global.TmpData = cmap.New()
	return obj
}

// ParseParams 解析参数，用于处理请求参数
func (obj *SplashAdAction) ParseParams() {
	// common 中拿不到header 中的 ua，这里判断下，如果拿不到 手动 set 进去..
	if obj.SplashAdReq.Data.GetCommon().GetUserAgent() == "" {
		ua := obj.Req.HTTPRequest().UserAgent()
		obj.ObjRequest.AddCommonAttr("user_agent", ua)
	}
}

// GetPrivateInfo 获取私有信息，返回一个map，包含两个键值对："check_login"为false，"need_login"也为false
func (obj *SplashAdAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.SplashAdReq {
		return nil
	}
	//不需要登录
	obj.ObjRequest.AddStrategy("check_sign", false)
	privateInfo := map[string]any{
		"check_login": false,
		"need_login":  false,
	}
	return privateInfo

}

// CheckPrivate 检查是否为私有对象，如果是则返回true，否则返回false
func (obj *SplashAdAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.SplashAdReq {
		return false
	}

	return true
}

// Execute Execute 执行操作，返回一个bool类型的结果值，表示是否已经完成了操作。
// 参数列表:
//
//	none
//
// 返回值:
//
//	bool - 如果操作成功，则返回true；如果操作失败，则返回false。
func (obj *SplashAdAction) Execute() bool {
	errNo := splashad.GetSplashAd(obj.Ctx, obj.global, obj.SplashAdRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return true
	}

	return true
}

// BuildOut BuildOut 构建输出数据，包括错误信息和返回数据
// 如果对象或响应为nil，则不进行任何操作
// 如果响应中存在错误信息，则调用Error方法记录错误并退出函数
// 否则将数据添加到输出数据中，并设置到响应中
func (obj *SplashAdAction) BuildOut() {
	if nil == obj || nil == obj.SplashAdRes {
		return
	}
	if obj.SplashAdRes.Error != nil {
		obj.Error(int(obj.SplashAdRes.GetError().GetErrorno()), obj.SplashAdRes.GetError().GetErrmsg(), true)
		return
	}
	outData := map[string]any{
		"data": obj.SplashAdRes.Data,
	}
	obj.ObjResponse.SetOutData(outData)
}

// BuildLog BuildLog
// 构建日志，用于记录请求的相关信息和处理结果
func (obj *SplashAdAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.SplashAdReq || nil == obj.SplashAdReq.Data {
		return
	}
}
