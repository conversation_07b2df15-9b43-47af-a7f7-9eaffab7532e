package publisher

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	publisherConfProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/publisher/publisherConf"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/publisher"
)

// GetPublisherConf @Description 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func GetPublisherConf(ctx context.Context, req ghttp.Request) ghttp.Response {
	return publisherConfActionInstance(ctx, req).UiFuncHandler()
}

// @Description publisherConfAction类的定义
type publisherConfAction struct {
	*uiclient.UIBaseAction
	publisherConfReq *publisherConfProto.GetPublisherConfReqIdl
	publisherConfRes *publisherConfProto.GetPublisherConfResIdl
	baseData         *types.PublisherConfBaseData
}

// @Description 创建一个publisherConfAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回publisherConfAction实例
func publisherConfActionInstance(ctx context.Context, req ghttp.Request) *publisherConfAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &publisherConfAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		publisherConfReq: &publisherConfProto.GetPublisherConfReqIdl{},
		publisherConfRes: &publisherConfProto.GetPublisherConfResIdl{
			Data:  &publisherConfProto.GetPublisherConfRes{},
			Error: &client.Error{},
		},
		baseData: &types.PublisherConfBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.publisherConfReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

// ParseParams @Description 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *publisherConfAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.publisherConfReq == nil || obj.publisherConfReq.Data == nil {
		return
	}
	obj.baseData.Request = obj.publisherConfReq.GetData()
}

// GetPrivateInfo @Description 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *publisherConfAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.publisherConfReq == nil || obj.publisherConfReq.Data == nil {
		return nil
	}
	privateInfo := map[string]any{
		"check_login": false,
		"need_login":  true,
	}
	return privateInfo
}

// CheckPrivate @Description 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *publisherConfAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.publisherConfReq == nil {
		return false
	}
	return true
}

// BuildOut @Description 用于构建并返回输出数据
// @Param：无
// @Return：无
func (obj *publisherConfAction) BuildOut() {
	if obj == nil || obj.publisherConfRes == nil {
		return
	}
	if obj.publisherConfRes.Error == nil {
		obj.publisherConfRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.publisherConfRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

// BuildLog @Description 创建日志
// @Param：无
// @Return：无
func (obj *publisherConfAction) BuildLog() {
	return
}

// Execute @Description 执行publisherConfAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *publisherConfAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.publisherConfReq == nil || obj.publisherConfReq.Data == nil {
		return false
	}
	errno := publisher.GetPublisherConf(obj.Ctx, obj.baseData, obj.publisherConfRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}
