package member

import (
	"context"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/member/popups"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/member"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func Popups(ctx context.Context, req ghttp.Request) ghttp.Response {
	return PopupsActionInstance(ctx, req).UiFuncHandler()
}

type PopupsAction struct {
	*uiclient.UIBaseAction
	PopupsReq *popups.GetMemberPopupsReqIdl
	PopupsRes *popups.GetMemberPopupsResIdl
	global    *types.GetMemberPopupsBaseData
}

func PopupsActionInstance(ctx context.Context, req ghttp.Request) *PopupsAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &PopupsAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		PopupsReq:    &popups.GetMemberPopupsReqIdl{},
		PopupsRes:    &popups.GetMemberPopupsResIdl{},
		global:       &types.GetMemberPopupsBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.PopupsReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.PopupsRes.Data = new(popups.GetMemberPopupsRes)

	return obj
}

func (obj *PopupsAction) ParseParams() {

}

func (obj *PopupsAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.PopupsReq || nil == obj.PopupsReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.PopupsReq.GetData()

	did := obj.PopupsReq.GetData().GetDialogId()

	arrPrivateInfo := map[string]any{
		"dialog_id": did,
	}

	return arrPrivateInfo
}

func (obj *PopupsAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.PopupsReq {
		return false
	}

	return true
}

func (obj *PopupsAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.PopupsReq || nil == obj.PopupsReq.Data {
		return false
	}
	errNo := member.GetMemberPopups(obj.Ctx, obj.global, obj.PopupsRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *PopupsAction) BuildOut() {
	if nil == obj || nil == obj.PopupsRes {
		return
	}
	if nil == obj.PopupsRes.Error {
		// 默认赋值成功
		obj.PopupsRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.PopupsRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *PopupsAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.PopupsReq || nil == obj.PopupsReq.Data {
		return
	}
}
