package excellent

import (
	"context"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/excellent/getBackRecommendForum"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/excellent"
)

// 对照组：全部用户都不出
const NORMAL_GROUP_A = "12_42_re_returen_ba_rec_a"

// 实验组一：兴趣形成期用户出
const TRIAL_GROUP_B = "12_42_re_returen_ba_rec_b"

// 实验组二：全部用户出
const TRIAL_GROUP_C = "12_42_re_returen_ba_rec_c"

func GetBackRecommendForum(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getBackRecommendForumActionInstance(ctx, req).UiFuncHandler()
}

type getBackRecommendForumAction struct {
	*uiclient.UIBaseAction
	getBackRecommendForumReq *getBackRecommendForum.GetBackRecommendForumReqIdl
	getBackRecommendForumRes *getBackRecommendForum.GetBackRecommendForumResIdl
	global                   *types.GetBackRecommendForumBaseData
}

func getBackRecommendForumActionInstance(ctx context.Context, req ghttp.Request) *getBackRecommendForumAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &getBackRecommendForumAction{
		UIBaseAction:             &uiclient.UIBaseAction{},
		getBackRecommendForumReq: &getBackRecommendForum.GetBackRecommendForumReqIdl{},
		getBackRecommendForumRes: &getBackRecommendForum.GetBackRecommendForumResIdl{},
		global:                   &types.GetBackRecommendForumBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getBackRecommendForumReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getBackRecommendForumRes.Data = new(getBackRecommendForum.GetBackRecommendForumRes)
	return obj
}

func (obj *getBackRecommendForumAction) ParseParams() {
	if nil == obj || nil == obj.getBackRecommendForumReq || nil == obj.getBackRecommendForumRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getBackRecommendForumReq.GetData()
}

func (obj *getBackRecommendForumAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getBackRecommendForumReq || nil == obj.getBackRecommendForumReq.Data {
		return nil
	}

	forumID := obj.getBackRecommendForumReq.GetData().GetForumId()
	forumName := obj.getBackRecommendForumReq.GetData().GetForumName()
	arrPrivateInfo := map[string]any{
		"forum_id":   forumID,
		"forum_name": forumName,
	}

	return arrPrivateInfo
}

func (obj *getBackRecommendForumAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getBackRecommendForumReq {
		return false
	}
	forumID := common.Tvttt(obj.ObjRequest.GetPrivateAttr("forum_id", 0), common.TTT_UINT32).(uint32)
	forumName := common.Tvttt(obj.ObjRequest.GetPrivateAttr("forum_name", ""), common.TTT_STRING).(string)
	if forumID == 0 || forumName == "" {
		tbcontext.WarningF(obj.Ctx, "request error: forum_id or forum_name is empty.")
		obj.Error(tiebaerror.ERR_MO_PARAM_INVALID, stcdefine.GetErrMsg(tiebaerror.ERR_MO_PARAM_INVALID), true)
		return false
	}
	return true
}

func (obj *getBackRecommendForumAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getBackRecommendForumReq || nil == obj.getBackRecommendForumReq.Data {
		return false
	}
	//实验组b、实验组c进行相关吧推荐
	arrSampleId := obj.getSampleIds()
	if php2go.InArray(TRIAL_GROUP_B, arrSampleId) || php2go.InArray(TRIAL_GROUP_C, arrSampleId) {
		//实验组b需要兴趣周期用户判断
		needInterestCycle := php2go.InArray(TRIAL_GROUP_B, arrSampleId)
		errNo := excellent.GetBackRecommendForum(obj.Ctx, obj.global, obj.getBackRecommendForumRes, needInterestCycle)
		if errNo != tiebaerror.ERR_SUCCESS {
			obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
			return false
		}
	}
	return true
}

func (obj *getBackRecommendForumAction) BuildOut() {
	if nil == obj || nil == obj.getBackRecommendForumRes {
		return
	}
	if nil == obj.getBackRecommendForumRes.Error {
		// 默认赋值成功
		obj.getBackRecommendForumRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getBackRecommendForumRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getBackRecommendForumAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getBackRecommendForumReq || nil == obj.getBackRecommendForumReq.Data {
		return
	}
}

func (obj *getBackRecommendForumAction) getSampleIds() (sampleIds []string) {
	strSampleId := common.Tvttt(obj.ObjRequest.GetCommonAttr("sample_id", ""), common.TTT_STRING).(string)
	if strSampleId == "" {
		strSampleId = obj.getSampleIdCache()
	}
	uid := common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	arrSampleIdsTmp := UbsAbtest.GetUbsAbtestConfig(obj.Ctx, strSampleId, strconv.FormatInt(uid, 10), types.WLUbsAbtestConfigTab)
	for _, tmp := range arrSampleIdsTmp {
		if _, ok := tmp["sid"]; ok {
			sampleIds = append(sampleIds, tmp["sid"])
		}
	}
	return sampleIds
}

func (obj *getBackRecommendForumAction) getSampleIdCache() string {
	cuid := common.Tvttt(obj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
	userId := common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", ""), common.TTT_INT64).(int64)
	intClinetType := common.Tvttt(obj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	strClientVersion := common.Tvttt(obj.ObjRequest.GetCommonAttr("client_version", 0), common.TTT_STRING).(string)
	userInfo := []string{
		strconv.FormatInt(userId, 10),
		cuid,
		strconv.Itoa(intClinetType),
		strClientVersion,
	}
	userInfoStr := strings.Join(userInfo, "-")
	keyMD5UserInfo := php2go.Md5(userInfoStr)

	redisKey := types.PreKeySampleIdCache + keyMD5UserInfo
	val, err := resource.CacheSign.Get(obj.Ctx, redisKey)
	if err != nil {
		return ""
	}
	tbcontext.WarningF(obj.Ctx, "[no_need_care] cuid %s get sample_id is %s", cuid, val)
	return val
}
