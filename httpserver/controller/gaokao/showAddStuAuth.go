package gaokao

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/gaokao/showAddStuAuth"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/gaokao"
)

func ShowAddStuAuth(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getShowAddStuAuthActionInstance(ctx, req).UiFuncHandler()
}

type showAddStuAuthAction struct {
	*uiclient.UIBaseAction
	showAddStuAuthReq *showAddStuAuth.ShowAddStuAuthReqIdl
	showAddStuAuthRes *showAddStuAuth.ShowAddStuAuthResIdl
	global            *types.ShowAddStuAuthBaseData
}

func getShowAddStuAuthActionInstance(ctx context.Context, req ghttp.Request) *showAddStuAuthAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &showAddStuAuthAction{
		UIBaseAction:      &uiclient.UIBaseAction{},
		showAddStuAuthReq: &showAddStuAuth.ShowAddStuAuthReqIdl{},
		showAddStuAuthRes: &showAddStuAuth.ShowAddStuAuthResIdl{},
		global:            &types.ShowAddStuAuthBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.showAddStuAuthReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.showAddStuAuthRes.Data = new(showAddStuAuth.ShowAddStuAuthRes)
	return obj
}

func (obj *showAddStuAuthAction) ParseParams() {
	if nil == obj || nil == obj.showAddStuAuthReq || nil == obj.showAddStuAuthRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.showAddStuAuthReq.GetData()
}

func (obj *showAddStuAuthAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.showAddStuAuthReq || nil == obj.showAddStuAuthReq.Data {
		return nil
	}

	privateInfo := map[string]interface{}{
		"check_login": true,
		"need_login":  true,
	}
	return privateInfo
}

func (obj *showAddStuAuthAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.showAddStuAuthReq {
		return false
	}

	return true
}

func (obj *showAddStuAuthAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.showAddStuAuthReq || nil == obj.showAddStuAuthReq.Data {
		return false
	}
	obj.global.UserID = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", ""), common.TTT_UINT64).(uint64)

	errNo := gaokao.ShowAddStuAuth(obj.Ctx, obj.global, obj.showAddStuAuthRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}
	return true
}

func (obj *showAddStuAuthAction) BuildOut() {
	if nil == obj || nil == obj.showAddStuAuthRes {
		return
	}
	if nil == obj.showAddStuAuthRes.Error {
		// 默认赋值成功
		obj.showAddStuAuthRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.showAddStuAuthRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *showAddStuAuthAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.showAddStuAuthReq || nil == obj.showAddStuAuthReq.Data {
		return
	}
}
