package gaokao

import (
	"context"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/gaokao"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/gaokao/warmup"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func Warmup(ctx context.Context, req ghttp.Request) ghttp.Response {
	return WarmupActionInstance(ctx, req).UiFuncHandler()
}

type WarmupAction struct {
	*uiclient.UIBaseAction
	WarmupReq *warmup.WarmupReqIdl
	WarmupRes *warmup.WarmupResIdl
	global    *types.GaokaoWarmupData
}

func WarmupActionInstance(ctx context.Context, req ghttp.Request) *WarmupAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &WarmupAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		WarmupReq:    &warmup.WarmupReqIdl{},
		WarmupRes:    &warmup.WarmupResIdl{},
		global:       &types.GaokaoWarmupData{},
	}
	if !obj.Init(ctx, req, obj, obj.WarmupReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.WarmupRes.Data = new(warmup.WarmupRes)

	return obj
}

func (obj *WarmupAction) ParseParams() {
}

func (obj *WarmupAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.WarmupReq || nil == obj.WarmupReq.Data {
		return nil
	}

	obj.global.Request = obj.WarmupReq.GetData()

	arrPrivateInfo := map[string]any{}

	return arrPrivateInfo
}

func (obj *WarmupAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.WarmupReq {
		return false
	}

	return true
}

func (obj *WarmupAction) Execute() bool {
	obj.global.StaticField = new(types.GaokaoWarmupStaticField)
	obj.global.StaticField.CallFrom = obj.global.Request.GetCallFrom()
	obj.global.StaticField.ForumName = obj.global.Request.GetForumName()
	obj.global.StaticField.UserID = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	obj.global.StaticField.ClientVersion = common.Tvttt(obj.ObjRequest.GetCommonAttr("client_version", 0), common.TTT_STRING).(string)
	obj.global.StaticField.ClientType = common.Tvttt(obj.ObjRequest.GetCommonAttr("client_type", 1), common.TTT_INT).(int)

	if nil == obj || nil == obj.ObjRequest || nil == obj.WarmupReq || nil == obj.WarmupReq.Data {
		return false
	}
	errNo := gaokao.Warmup(obj.Ctx, obj.global, obj.WarmupRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *WarmupAction) BuildOut() {
	if nil == obj || nil == obj.WarmupRes {
		return
	}
	if nil == obj.WarmupRes.Error {
		// 默认赋值成功
		obj.WarmupRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.WarmupRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *WarmupAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.WarmupReq || nil == obj.WarmupReq.Data {
		return
	}
}
