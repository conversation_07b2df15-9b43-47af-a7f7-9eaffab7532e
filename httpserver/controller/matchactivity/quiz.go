package matchactivity

import (
	"context"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/matchActivityQuiz"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/matchactivity"
)

func GetQuiz(ctx context.Context, req ghttp.Request) ghttp.Response {
	return quizActionInstance(ctx, req).UiFuncHandler()
}

type quizAction struct {
	*uiclient.UIBaseAction
	req    *matchActivityQuiz.MatchActivityQuizReqIdl
	res    *matchActivityQuiz.MatchActivityQuizResIdl
	global *types.MatchActivityQuizBaseData
}

func quizActionInstance(ctx context.Context, req ghttp.Request) *quizAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &quizAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		req:          &matchActivityQuiz.MatchActivityQuizReqIdl{},
		res:          &matchActivityQuiz.MatchActivityQuizResIdl{},
		global:       &types.MatchActivityQuizBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.req) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.res.Data = new(matchActivityQuiz.DataRes)
	return obj
}

func (obj *quizAction) ParseParams() {
	if nil == obj || nil == obj.req || nil == obj.res.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.req.GetData()
	tbcontext.NoticeF(obj.Ctx, "param %+v", obj.req.GetData())
}

func (obj *quizAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.req || nil == obj.req.Data {
		return nil
	}

	// 临时关掉接口的签名校验
	//obj.ObjRequest.AddStrategy("check_sign", false)

	arrPrivateInfo := map[string]any{
		"check_login": true,
	}

	return arrPrivateInfo
}

func (obj *quizAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.req {
		return false
	}

	return true
}

func (obj *quizAction) Execute() bool {
	errNo := matchactivity.GetQuiz(obj.Ctx, obj.global, obj.res)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}
	return true
}

func (obj *quizAction) BuildOut() {
	if nil == obj || nil == obj.res {
		return
	}
	if nil == obj.res.Error {
		// 默认赋值成功
		obj.res.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.res.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *quizAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.req || nil == obj.req.Data {
		return
	}
}
