package widget

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	// 项目自身库
	widgetProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/widget/forumInfo"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/widget"
)

// GetForumInfo 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func GetForumInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 通过getForumInfoActionInstance实例化getForumInfoAction类，并调用UiFuncHandler()方法给ghttp.Response赋值
	return getForumInfoActionInstance(ctx, req).UiFuncHandler()
}

// getForumInfoAction 服务相关结构体
type getForumInfoAction struct {
	// 组合UIBaseAction基础类，并实现基类方法
	*uiclient.UIBaseAction
	// 通过proto定义输入数据
	getForumInfoReq *widgetProto.GetForumInfoReqIdl
	// 通过proto定义输出数据
	getForumInfoRes *widgetProto.GetForumInfoResIdl
	// 通过types封装跟service层交互的数据
	baseData *types.GetForumInfoBaseData
}

// getForumInfoActionInstance 创建一个getForumInfoAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回getForumInfoAction实例
func getForumInfoActionInstance(ctx context.Context, req ghttp.Request) *getForumInfoAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &getForumInfoAction{
		UIBaseAction:    &uiclient.UIBaseAction{},
		getForumInfoReq: &widgetProto.GetForumInfoReqIdl{},
		getForumInfoRes: &widgetProto.GetForumInfoResIdl{
			Data:      &widgetProto.GetForumInfoRes{},
			ErrorCode: &client.Error{},
		},
		baseData: &types.GetForumInfoBaseData{},
	}
	tbcontext.WarningF(ctx, "getForumInfoActionInstance:%s", common.ToString(req))
	if !obj.Init(ctx, req, obj, obj.getForumInfoReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

// ParseParams 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *getForumInfoAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.getForumInfoReq == nil || obj.getForumInfoReq.Data == nil {
		return
	}
	obj.baseData.Request = obj.getForumInfoReq.GetData()
}

// GetPrivateInfo 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *getForumInfoAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.getForumInfoReq == nil || obj.getForumInfoReq.Data == nil {
		return nil
	}
	obj.ObjRequest.AddStrategy("check_sign", false)
	privateInfo := map[string]any{
		"check_login": false,
		"need_login":  true,
		"forum_id":    obj.getForumInfoReq.GetData().GetForumId(),
	}
	return privateInfo
}

// CheckPrivate 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *getForumInfoAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getForumInfoReq == nil {
		return false
	}
	return true
}

// BuildOut 构建输出数据的方法
// @Param：无
// @Return：无
func (obj *getForumInfoAction) BuildOut() {
	if obj == nil || obj.getForumInfoRes == nil {
		return
	}
	if obj.getForumInfoRes.ErrorCode == nil {
		obj.getForumInfoRes.ErrorCode = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.getForumInfoRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

// BuildLog 创建日志
// @Param：无
// @Return：无
func (obj *getForumInfoAction) BuildLog() {
	return
}

// Execute 执行getForumInfoAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *getForumInfoAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getForumInfoReq == nil || obj.getForumInfoReq.Data == nil {
		return false
	}
	errno := service.GetForumInfo(obj.Ctx, obj.baseData, obj.getForumInfoRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}
