package widget

import (
	"context"

	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	client "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	getUserTaskInfoProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/widget/getUserTaskInfo"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/widget"
)

// GetUserTaskInfo 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func GetUserTaskInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getUserTaskInfoActionInstance(ctx, req).UiFuncHandler()
}

// getUserTaskInfoAction 服务相关结构体
type getUserTaskInfoAction struct {
	*uiclient.UIBaseAction
	getUserTaskInfoReq *getUserTaskInfoProto.GetUserTaskInfoReqIdl
	getUserTaskInfoRes *getUserTaskInfoProto.GetUserTaskInfoResIdl
	baseData           *types.GetUserTaskInfoBaseData
}

// getUserTaskInfoActionInstance 创建一个getUserTaskInfoAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回getUserTaskInfoAction实例
func getUserTaskInfoActionInstance(ctx context.Context, req ghttp.Request) *getUserTaskInfoAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &getUserTaskInfoAction{
		UIBaseAction:       &uiclient.UIBaseAction{},
		getUserTaskInfoReq: &getUserTaskInfoProto.GetUserTaskInfoReqIdl{},
		getUserTaskInfoRes: &getUserTaskInfoProto.GetUserTaskInfoResIdl{
			Data:  &getUserTaskInfoProto.GetUserTaskInfoData{},
			Error: &client.Error{},
		},
		baseData: &types.GetUserTaskInfoBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getUserTaskInfoReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

// ParseParams 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *getUserTaskInfoAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.getUserTaskInfoReq == nil || obj.getUserTaskInfoReq.Data == nil {
		return
	}
	obj.baseData.Request = obj.getUserTaskInfoReq.GetData()
}

// GetPrivateInfo 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *getUserTaskInfoAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.getUserTaskInfoReq == nil || obj.getUserTaskInfoReq.Data == nil {
		return nil
	}
	obj.ObjRequest.AddStrategy("check_sign", false)
	privateInfo := map[string]any{
		"check_login": false,
		"need_login":  true,
	}
	return privateInfo
}

// CheckPrivate 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *getUserTaskInfoAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getUserTaskInfoReq == nil {
		return false
	}
	return true
}

// BuildOut 构建输出数据的方法
// @Param：无
// @Return：无
func (obj *getUserTaskInfoAction) BuildOut() {
	if obj == nil || obj.getUserTaskInfoRes == nil {
		return
	}
	if obj.getUserTaskInfoRes.Error == nil {
		obj.getUserTaskInfoRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.getUserTaskInfoRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

// BuildLog 创建日志
// @Param：无
// @Return：无
func (obj *getUserTaskInfoAction) BuildLog() {
	return
}

// Execute 执行getUserTaskInfoAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *getUserTaskInfoAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getUserTaskInfoReq == nil || obj.getUserTaskInfoReq.Data == nil {
		return false
	}
	errno := service.GetUserTaskInfo(obj.Ctx, obj.baseData, obj.getUserTaskInfoRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}
