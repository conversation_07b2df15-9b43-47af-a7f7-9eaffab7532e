package sprite

// AIGC在线生成表情包结果查询接口

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/checkMemeStatus"

	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/sprite"
)

func CheckMemeStatus(ctx context.Context, req ghttp.Request) ghttp.Response {
	return checkMemeStatusActionInstance(ctx, req).UiFuncHandler()
}

type checkMemeStatusAction struct {
	*uiclient.UIBaseAction
	checkMemeStatusReq *checkMemeStatus.CheckMemeStatusReqIdl
	checkMemeStatusRes *checkMemeStatus.CheckMemeStatusResIdl
	global             *types.CheckMemeStatusBaseData
}

func checkMemeStatusActionInstance(ctx context.Context, req ghttp.Request) *checkMemeStatusAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &checkMemeStatusAction{
		UIBaseAction:       &uiclient.UIBaseAction{},
		checkMemeStatusReq: &checkMemeStatus.CheckMemeStatusReqIdl{},
		checkMemeStatusRes: &checkMemeStatus.CheckMemeStatusResIdl{},
		global:             &types.CheckMemeStatusBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.checkMemeStatusReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.checkMemeStatusRes.Data = &checkMemeStatus.CheckMemeStatusRes{}
	return obj
}

func (obj *checkMemeStatusAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.checkMemeStatusReq == nil || obj.checkMemeStatusReq.Data == nil {
		return
	}
	obj.global.Request = obj.checkMemeStatusReq.GetData()
}

func (obj *checkMemeStatusAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.checkMemeStatusReq == nil || obj.checkMemeStatusReq.Data == nil {
		return nil
	}
	return map[string]any{
		"check_login": true,
		"query_id":    obj.checkMemeStatusReq.GetData().GetQueryId(),
	}
}

func (obj *checkMemeStatusAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.checkMemeStatusReq == nil {
		return false
	}
	return true
}

func (obj *checkMemeStatusAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.checkMemeStatusReq == nil || obj.checkMemeStatusReq.Data == nil {
		return false
	}
	errNo := sprite.CheckMemeStatus(obj.Ctx, obj.global, obj.checkMemeStatusRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}
	return true
}

func (obj *checkMemeStatusAction) BuildOut() {
	if obj == nil || obj.checkMemeStatusRes == nil {
		return
	}
	if obj.checkMemeStatusRes.Error == nil {
		obj.checkMemeStatusRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}

	outData := map[string]any{
		"data": obj.checkMemeStatusRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *checkMemeStatusAction) BuildLog() {
	if obj == nil || obj.Ctx == nil || obj.Req == nil || obj.checkMemeStatusReq == nil || obj.checkMemeStatusReq.Data == nil {
		return
	}
}
