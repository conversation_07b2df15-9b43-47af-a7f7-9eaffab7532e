package sprite

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/sendSpriteMsg"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/sprite"
)

func SendSpriteMsg(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getSendSpriteMsgActionInstance(ctx, req).UiFuncHandler()
}

type sendSpriteMsgAction struct {
	*uiclient.UIBaseAction
	sendSpriteMsgReq *sendSpriteMsg.SendSpriteMsgReqIdl
	sendSpriteMsgRes *sendSpriteMsg.SendSpriteMsgResIdl
	global           *types.SendSpriteMsgBaseData
}

func getSendSpriteMsgActionInstance(ctx context.Context, req ghttp.Request) *sendSpriteMsgAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &sendSpriteMsgAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		sendSpriteMsgReq: &sendSpriteMsg.SendSpriteMsgReqIdl{},
		sendSpriteMsgRes: &sendSpriteMsg.SendSpriteMsgResIdl{},
		global:           &types.SendSpriteMsgBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.sendSpriteMsgReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.sendSpriteMsgRes.Data = new(sendSpriteMsg.SendSpriteMsgRes)

	return obj
}

func (obj *sendSpriteMsgAction) ParseParams() {

}

func (obj *sendSpriteMsgAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.sendSpriteMsgReq || nil == obj.sendSpriteMsgReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.sendSpriteMsgReq.GetData()

	content := obj.sendSpriteMsgReq.GetData().GetContent()
	msgKey := obj.sendSpriteMsgReq.GetData().GetMsgKey()
	pa := obj.sendSpriteMsgReq.GetData().GetPa()
	uk := obj.sendSpriteMsgReq.GetData().GetUk()
	extraType := obj.sendSpriteMsgReq.GetData().GetExtraType()
	titleInfo := obj.sendSpriteMsgReq.GetData().GetTitleInfo()
	msgSugInfo := obj.sendSpriteMsgReq.GetData().GetMsgSugInfo()
	arrPrivateInfo := map[string]any{
		"content":      content,
		"msg_key":      msgKey,
		"pa":           pa,
		"uk":           uk,
		"extra_type":   extraType,
		"title_info":   titleInfo,
		"msg_sug_info": msgSugInfo,
	}

	return arrPrivateInfo
}

func (obj *sendSpriteMsgAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.sendSpriteMsgReq {
		return false
	}

	return true
}

func (obj *sendSpriteMsgAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.sendSpriteMsgReq || nil == obj.sendSpriteMsgReq.Data {
		return false
	}
	errNo := sprite.SendSpriteMsg(obj.Ctx, obj.global, obj.sendSpriteMsgRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *sendSpriteMsgAction) BuildOut() {
	if nil == obj || nil == obj.sendSpriteMsgRes {
		return
	}
	if nil == obj.sendSpriteMsgRes.Error {
		// 默认赋值成功
		obj.sendSpriteMsgRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.sendSpriteMsgRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *sendSpriteMsgAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.sendSpriteMsgReq || nil == obj.sendSpriteMsgReq.Data {
		return
	}
}
