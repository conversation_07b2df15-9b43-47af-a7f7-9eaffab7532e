package sprite

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/getSpriteChatDetail"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/sprite"
)

func GetSpriteChatDetail(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getGetSpriteChatDetailActionInstance(ctx, req).UiFuncHandler()
}

type getSpriteChatDetailAction struct {
	*uiclient.UIBaseAction
	getSpriteChatDetailReq *getSpriteChatDetail.GetSpriteChatDetailReqIdl
	getSpriteChatDetailRes *getSpriteChatDetail.GetSpriteChatDetailResIdl
	global                 *types.GetSpriteChatDetailBaseData
}

func getGetSpriteChatDetailActionInstance(ctx context.Context, req ghttp.Request) *getSpriteChatDetailAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &getSpriteChatDetailAction{
		UIBaseAction:           &uiclient.UIBaseAction{},
		getSpriteChatDetailReq: &getSpriteChatDetail.GetSpriteChatDetailReqIdl{},
		getSpriteChatDetailRes: &getSpriteChatDetail.GetSpriteChatDetailResIdl{},
		global:                 &types.GetSpriteChatDetailBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getSpriteChatDetailReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getSpriteChatDetailRes.Data = new(getSpriteChatDetail.GetSpriteChatDetailRes)
	return obj
}

func (obj *getSpriteChatDetailAction) ParseParams() {
	if nil == obj || nil == obj.getSpriteChatDetailReq || nil == obj.getSpriteChatDetailRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getSpriteChatDetailReq.GetData()
}

func (obj *getSpriteChatDetailAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getSpriteChatDetailReq || nil == obj.getSpriteChatDetailReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	from := obj.getSpriteChatDetailReq.GetData().GetSceneFrom()
	query := obj.getSpriteChatDetailReq.GetData().GetQuery()
	scene := obj.getSpriteChatDetailReq.GetData().GetScene()

	arrPrivateInfo := map[string]any{
		"scene_from": from,
		"query":      query,
		"scene":      scene,
	}

	return arrPrivateInfo
}

func (obj *getSpriteChatDetailAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getSpriteChatDetailReq {
		return false
	}

	return true
}

func (obj *getSpriteChatDetailAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getSpriteChatDetailReq || nil == obj.getSpriteChatDetailReq.Data {
		return false
	}
	errNo := sprite.GetSpriteChatDetail(obj.Ctx, obj.global, obj.getSpriteChatDetailRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *getSpriteChatDetailAction) BuildOut() {
	if nil == obj || nil == obj.getSpriteChatDetailRes {
		return
	}
	if nil == obj.getSpriteChatDetailRes.Error {
		// 默认赋值成功
		obj.getSpriteChatDetailRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getSpriteChatDetailRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getSpriteChatDetailAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getSpriteChatDetailReq || nil == obj.getSpriteChatDetailReq.Data {
		return
	}
}
