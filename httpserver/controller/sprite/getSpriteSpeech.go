package sprite

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/getSpriteSpeech"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/sprite"
)

func GetSpriteSpeech(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getGetSpriteSpeechActionInstance(ctx, req).UiFuncHandler()
}

type getSpriteSpeechAction struct {
	*uiclient.UIBaseAction
	getSpriteSpeechReq *getSpriteSpeech.GetSpriteSpeechReqIdl
	getSpriteSpeechRes *getSpriteSpeech.GetSpriteSpeechResIdl
	global             *types.GetSpriteSpeechBaseData
}

func getGetSpriteSpeechActionInstance(ctx context.Context, req ghttp.Request) *getSpriteSpeechAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &getSpriteSpeechAction{
		UIBaseAction:       &uiclient.UIBaseAction{},
		getSpriteSpeechReq: &getSpriteSpeech.GetSpriteSpeechReqIdl{},
		getSpriteSpeechRes: &getSpriteSpeech.GetSpriteSpeechResIdl{},
		global:             &types.GetSpriteSpeechBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getSpriteSpeechReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getSpriteSpeechRes.Data = new(getSpriteSpeech.GetSpriteSpeechRes)
	return obj
}

func (obj *getSpriteSpeechAction) ParseParams() {
	if nil == obj || nil == obj.getSpriteSpeechReq || nil == obj.getSpriteSpeechRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getSpriteSpeechReq.GetData()
}

func (obj *getSpriteSpeechAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getSpriteSpeechReq || nil == obj.getSpriteSpeechReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	scene := obj.getSpriteSpeechReq.GetData().GetScene()
	spriteUk := obj.getSpriteSpeechReq.GetData().GetSpriteUk()
	version := obj.getSpriteSpeechReq.GetData().GetVersion()

	arrPrivateInfo := map[string]any{
		"scene":     scene,
		"sprite_uk": spriteUk,
		"version":   version,
	}

	return arrPrivateInfo
}

func (obj *getSpriteSpeechAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getSpriteSpeechReq {
		return false
	}

	return true
}

func (obj *getSpriteSpeechAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getSpriteSpeechReq || nil == obj.getSpriteSpeechReq.Data {
		return false
	}
	errNo := sprite.GetSpriteSpeech(obj.Ctx, obj.global, obj.getSpriteSpeechRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *getSpriteSpeechAction) BuildOut() {
	if nil == obj || nil == obj.getSpriteSpeechRes {
		return
	}
	if nil == obj.getSpriteSpeechRes.Error {
		// 默认赋值成功
		obj.getSpriteSpeechRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getSpriteSpeechRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getSpriteSpeechAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getSpriteSpeechReq || nil == obj.getSpriteSpeechReq.Data {
		return
	}
}
