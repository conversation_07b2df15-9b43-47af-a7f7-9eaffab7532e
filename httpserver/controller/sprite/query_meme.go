package sprite

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/queryMeme"

	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/sprite"
)

func QueryMeme(ctx context.Context, req ghttp.Request) ghttp.Response {
	return queryMemeActionInstance(ctx, req).UiFuncHandler()
}

type queryMemeAction struct {
	*uiclient.UIBaseAction
	queryMemeReq *queryMeme.QueryMemeReqIdl
	queryMemeRes *queryMeme.QueryMemeResIdl
	global       *types.QueryMemeBaseData
}

func queryMemeActionInstance(ctx context.Context, req ghttp.Request) *queryMemeAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &queryMemeAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		queryMemeReq: &queryMeme.QueryMemeReqIdl{},
		queryMemeRes: &queryMeme.QueryMemeResIdl{},
		global:       &types.QueryMemeBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.queryMemeReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.queryMemeRes.Data = &queryMeme.QueryMemeRes{}
	return obj
}

func (obj *queryMemeAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.queryMemeReq == nil || obj.queryMemeReq.Data == nil {
		return
	}
	obj.global.Request = obj.queryMemeReq.GetData()
}

func (obj *queryMemeAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.queryMemeReq == nil || obj.queryMemeReq.Data == nil {
		return nil
	}

	queryData := obj.queryMemeReq.GetData()
	privateInfo := map[string]any{
		"check_login": true,
		"query":       queryData.GetQuery(),
		"scene":       queryData.GetScene(),
		"forum_id":    queryData.GetForumId(),
		"pn":          queryData.GetPn(),
		"meme_id":     queryData.GetMemeId(),
	}
	return privateInfo
}

func (obj *queryMemeAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.queryMemeReq == nil {
		return false
	}
	return true
}

func (obj *queryMemeAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.queryMemeReq == nil || obj.queryMemeReq.Data == nil {
		return false
	}
	errNo := sprite.QueryMeme(obj.Ctx, obj.global, obj.queryMemeRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}
	return true
}

func (obj *queryMemeAction) BuildOut() {
	if obj == nil || obj.queryMemeRes == nil {
		return
	}
	if obj.queryMemeRes.Error == nil {
		obj.queryMemeRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}

	outData := map[string]any{
		"data": obj.queryMemeRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *queryMemeAction) BuildLog() {
	if obj == nil || obj.Ctx == nil || obj.Req == nil || obj.queryMemeReq == nil || obj.queryMemeReq.Data == nil {
		return
	}
}
