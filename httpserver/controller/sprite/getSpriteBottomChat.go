package sprite

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/getSpriteBottomChat"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/sprite"
)

func GetSpriteBottomChat(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getSpriteBottomChatActionInstance(ctx, req).UiFuncHandler()
}

type getSpriteBottomChatAction struct {
	*uiclient.UIBaseAction
	getSpriteBottomChatReq *getSpriteBottomChat.GetSpriteBottomChatReqIdl
	getSpriteBottomChatRes *getSpriteBottomChat.GetSpriteBottomChatResIdl
	global                 *types.GetSpriteBottomChatBaseData
}

func getSpriteBottomChatActionInstance(ctx context.Context, req ghttp.Request) *getSpriteBottomChatAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &getSpriteBottomChatAction{
		UIBaseAction:           &uiclient.UIBaseAction{},
		getSpriteBottomChatReq: &getSpriteBottomChat.GetSpriteBottomChatReqIdl{},
		getSpriteBottomChatRes: &getSpriteBottomChat.GetSpriteBottomChatResIdl{},
		global:                 &types.GetSpriteBottomChatBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getSpriteBottomChatReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.getSpriteBottomChatRes.Data = new(getSpriteBottomChat.GetSpriteBottomChatRes)
	return obj
}

func (obj *getSpriteBottomChatAction) ParseParams() {
	if nil == obj || nil == obj.getSpriteBottomChatReq || nil == obj.getSpriteBottomChatRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.getSpriteBottomChatReq.GetData()
}

func (obj *getSpriteBottomChatAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getSpriteBottomChatReq || nil == obj.getSpriteBottomChatReq.Data {
		return nil
	}

	// 临时关掉接口的签名校验
	obj.ObjRequest.AddStrategy("check_sign", false)

	query := obj.getSpriteBottomChatReq.GetData().GetQuery()
	rn := obj.getSpriteBottomChatReq.GetData().GetRn()
	uk := obj.getSpriteBottomChatReq.GetData().GetUk()
	fid := obj.getSpriteBottomChatReq.GetData().GetForumId()

	arrPrivateInfo := map[string]any{
		"query":    query,
		"rn":       rn,
		"uk":       uk,
		"forum_id": fid,
	}

	return arrPrivateInfo
}

func (obj *getSpriteBottomChatAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getSpriteBottomChatReq {
		return false
	}

	return true
}

func (obj *getSpriteBottomChatAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.getSpriteBottomChatReq || nil == obj.getSpriteBottomChatReq.Data {
		return false
	}
	errNo := sprite.GetSpriteBottomChat(obj.Ctx, obj.global, obj.getSpriteBottomChatRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *getSpriteBottomChatAction) BuildOut() {
	if nil == obj || nil == obj.getSpriteBottomChatRes {
		return
	}
	if nil == obj.getSpriteBottomChatRes.Error {
		// 默认赋值成功
		obj.getSpriteBottomChatRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.getSpriteBottomChatRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getSpriteBottomChatAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.getSpriteBottomChatReq || nil == obj.getSpriteBottomChatReq.Data {
		return
	}
}
