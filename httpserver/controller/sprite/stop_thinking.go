package sprite

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/stopThinking"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/sprite"
)

// 停止发言接口
func StopThinking(ctx context.Context, req ghttp.Request) ghttp.Response {
	return stopThinkingActionInstance(ctx, req).UiFuncHandler()
}

type stopThinkingAction struct {
	*uiclient.UIBaseAction
	stopThinkingReq *stopThinking.StopThinkingReqIdl
	stopThinkingRes *stopThinking.StopThinkingResIdl
	global          *types.StopThinkingBaseData
}

func stopThinkingActionInstance(ctx context.Context, req ghttp.Request) *stopThinkingAction {
	if ctx == nil || req == nil {
		return nil
	}

	obj := &stopThinkingAction{
		UIBaseAction:    &uiclient.UIBaseAction{},
		stopThinkingReq: &stopThinking.StopThinkingReqIdl{},
		stopThinkingRes: &stopThinking.StopThinkingResIdl{},
		global:          &types.StopThinkingBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.stopThinkingReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.stopThinkingRes.Error = nil
	return obj
}

func (obj *stopThinkingAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.stopThinkingReq == nil || obj.stopThinkingReq.Data == nil {
		return
	}
	obj.global.Request = obj.stopThinkingReq.GetData()
}

func (obj *stopThinkingAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.stopThinkingReq == nil || obj.stopThinkingReq.Data == nil {
		return nil
	}

	return map[string]any{
		"check_login": true,
		"msg_key":     obj.stopThinkingReq.GetData().GetMsgKey(),
	}
}

func (obj *stopThinkingAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.stopThinkingReq == nil {
		return false
	}
	return true
}

func (obj *stopThinkingAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.stopThinkingReq == nil || obj.stopThinkingReq.Data == nil {
		return false
	}
	errNo := sprite.StopThinking(obj.Ctx, obj.global, obj.stopThinkingRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}
	return true
}

func (obj *stopThinkingAction) BuildOut() {
	if obj == nil || obj.stopThinkingRes == nil {
		return
	}
	if obj.stopThinkingRes.Error == nil {
		obj.stopThinkingRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
}

func (obj *stopThinkingAction) BuildLog() {
	if obj == nil || obj.Ctx == nil || obj.Req == nil || obj.stopThinkingReq == nil || obj.stopThinkingReq.Data == nil {
		return
	}
}
