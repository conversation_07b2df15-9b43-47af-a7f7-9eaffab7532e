package sprite

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/threadRecommend"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/sprite"
)

func ThreadRecommend(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getThreadRecommendActionInstance(ctx, req).UiFuncHandler()
}

type threadRecommendAction struct {
	*uiclient.UIBaseAction
	threadRecommendReq *threadRecommend.ThreadRecommendReqIdl
	threadRecommendRes *threadRecommend.ThreadRecommendResIdl
	global             *types.SpriteThreadRecommendBaseData
}

func getThreadRecommendActionInstance(ctx context.Context, req ghttp.Request) *threadRecommendAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &threadRecommendAction{
		UIBaseAction:       &uiclient.UIBaseAction{},
		threadRecommendReq: &threadRecommend.ThreadRecommendReqIdl{},
		threadRecommendRes: &threadRecommend.ThreadRecommendResIdl{},
		global:             &types.SpriteThreadRecommendBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.threadRecommendReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.threadRecommendRes.Data = new(threadRecommend.ThreadRecommendRes)

	return obj
}

func (obj *threadRecommendAction) ParseParams() {

}

func (obj *threadRecommendAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.threadRecommendReq || nil == obj.threadRecommendReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.threadRecommendReq.GetData()

	threadId := obj.global.Request.GetThreadId()
	pn := obj.global.Request.GetPn()
	forumId := obj.global.Request.GetForumId()
	forumName := obj.global.Request.GetForumName()

	arrPrivateInfo := map[string]any{
		"thread_id":  threadId,
		"pn":         pn,
		"forum_id":   forumId,
		"forum_name": forumName,
	}

	return arrPrivateInfo
}

func (obj *threadRecommendAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.threadRecommendReq {
		return false
	}

	return true
}

func (obj *threadRecommendAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.threadRecommendReq || nil == obj.threadRecommendReq.Data {
		return false
	}
	errNo := sprite.ThreadRecommend(obj.Ctx, obj.global, obj.threadRecommendRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *threadRecommendAction) BuildOut() {
	if nil == obj || nil == obj.threadRecommendRes {
		return
	}
	if nil == obj.threadRecommendRes.Error {
		// 默认赋值成功
		obj.threadRecommendRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.threadRecommendRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *threadRecommendAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.threadRecommendReq || nil == obj.threadRecommendReq.Data {
		return
	}
}
