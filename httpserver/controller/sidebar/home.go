package sidebar

import (
	"context"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sidebar/home"

	golibExgraph "icode.baidu.com/baidu/tieba-server-go/golib2/exgraph"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/sidebar/home"
)

const SidebarHomeEngineName = "sidebar_home"

var (
	sidebarHomeExgraph engine.Engine
)

func init() {
	var err error
	err = golibExgraph.InitExgraph()
	if err != nil {
		panic(err)
	}
	sidebarHomeExgraph, err = golibExgraph.GetEngineByName(SidebarHomeEngineName)
	if err != nil {
		panic(err)
	}
}

func Home(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getHomeActionInstance(ctx, req).UiFuncHandler()
}

type HomeAction struct {
	*uiclient.UIBaseAction
	homeReq *home.SidebarHomeReqIdl
	homeRes *home.SidebarHomeResIdl
	global  *types.SidebarHomeBaseData
}

func getHomeActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}

	obj := &HomeAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		homeReq:      &home.SidebarHomeReqIdl{},
		homeRes:      &home.SidebarHomeResIdl{},
		global:       &types.SidebarHomeBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.homeReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.homeRes.Data = new(home.SidebarHomeData)
	return obj
}

func (obj *HomeAction) ParseParams() {
	if nil == obj || nil == obj.homeReq || nil == obj.homeRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.homeReq.GetData()
}

func (obj *HomeAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.homeReq || nil == obj.homeReq.Data {
		return nil
	}
	history := obj.homeReq.GetData().GetHistory()

	arrPrivateInfo := map[string]interface{}{
		"history": common.Tvttt(history, common.TTT_STRING),
	}

	return arrPrivateInfo
}

func (obj *HomeAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.homeReq {
		return false
	}

	return true
}

func (obj *HomeAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.homeReq || nil == obj.homeReq.Data {
		return false
	}

	obj.global.StaticField = new(types.SidebarHomeStaticField)
	staticField := obj.global.StaticField
	staticField.Init()
	staticField.ClientType = common.Tvttt(obj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	staticField.ClientVersion = common.Tvttt(obj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	staticField.UserID = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	pureMode := common.Tvttt(obj.ObjRequest.GetCommonAttr("pure_mode", 0), common.TTT_INT).(int)
	staticField.PureMode = pureMode
	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}

	exctx := sidebarHomeExgraph.NewContext(obj.Ctx, opts...)

	exctx.MustRegisterInstance(&obj.global)
	exctx.MustRegisterInstance(&obj.homeRes.Data)

	err := sidebarHomeExgraph.Run(exctx)
	if err != nil {
		obj.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, tiebaerror.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		tbcontext.FatalF(obj.Ctx, "exgraph engine run fail.err:%s", err.Error())
	}

	exctx.RangeOpCost(func(opName string, cost time.Duration) {
		obj.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	return true
}

func (obj *HomeAction) BuildOut() {
	if nil == obj || nil == obj.homeRes {
		return
	}
	if nil == obj.homeRes.Error {
		//默认赋值成功
		obj.homeRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	obj.ObjResponse.SetStructData(obj.homeRes)
}

func (obj *HomeAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.homeReq || nil == obj.homeReq.Data {
		return
	}
}
