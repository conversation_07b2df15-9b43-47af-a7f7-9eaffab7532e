package question

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/question/guide"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/question"
)

type GuideAction struct {
	*uiclient.UIBaseAction
	GuideReq *guide.GuideReqIdl
	GuideRes *guide.GuideResIdl
	global   *types.CGuideBaseData
}

func Guide(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getGuideActionInstance(ctx, req).UiFuncHandler()
}

func getGuideActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx {
		return nil
	}
	obj := &GuideAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		GuideReq:     &guide.GuideReqIdl{},
		GuideRes:     &guide.GuideResIdl{},
		global:       &types.CGuideBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.GuideReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.global.StaticField = types.NewGuideStaticField()
	obj.GuideRes.Data = new(guide.GuideRes)
	return obj
}

func (obj *GuideAction) Execute() bool {
	if nil == obj {
		return false
	}
	errNo := question.Guide(obj.Ctx, obj.global, obj.GuideRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *GuideAction) CheckPrivate() bool {
	return true
}

func (obj *GuideAction) BuildLog() {
	return
}

func (obj *GuideAction) ParseParams() {
	obj.global.Request = obj.GuideReq.GetData()
}

func (obj *GuideAction) GetPrivateInfo() map[string]any {
	return nil
}

func (obj *GuideAction) BuildOut() {
	if nil == obj || nil == obj.GuideRes {
		return
	}
	obj.ObjResponse.SetStructData(obj.GuideRes)
}
