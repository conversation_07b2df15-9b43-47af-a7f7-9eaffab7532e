package question

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	p "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/question/getQuestionList"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/question"
)

type GetQuestionListAction struct {
	*uiclient.UIBaseAction
	QuestionListReq *p.GetQuestionListReqIdl
	QuestionListRes *p.GetQuestionListResIdl
	global          *types.GetQuestionListBaseData
}

func GetQuestionList(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getQuestionListAction(ctx, req).UiFuncHandler()
}

func getQuestionListAction(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx {
		return nil
	}
	obj := &GetQuestionListAction{
		UIBaseAction:    &uiclient.UIBaseAction{},
		QuestionListReq: &p.GetQuestionListReqIdl{},
		QuestionListRes: &p.GetQuestionListResIdl{},
		global:          &types.GetQuestionListBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.QuestionListReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.QuestionListRes.Data = new(p.GetQuestionListRes)
	return obj
}

func (obj *GetQuestionListAction) Execute() bool {
	clientVersion, _ := common.Tvttt(obj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	clientType, _ := common.Tvttt(obj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	userID, _ := common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	obj.global.StaticField.ClientVersion = clientVersion
	obj.global.StaticField.ClientType = clientType
	obj.global.StaticField.UserID = userID

	errNo := question.GetQuestionList(obj.Ctx, obj.global, obj.QuestionListRes.GetData())
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *GetQuestionListAction) CheckPrivate() bool {
	return true
}

func (obj *GetQuestionListAction) BuildLog() {
	return
}

func (obj *GetQuestionListAction) ParseParams() {
	obj.global.Request = obj.QuestionListReq.GetData()
}

func (obj *GetQuestionListAction) GetPrivateInfo() map[string]any {
	return map[string]any{
		"need_login": true,
		// "check_login": true,
	}
}

func (obj *GetQuestionListAction) BuildOut() {
	if nil == obj || nil == obj.QuestionListRes {
		return
	}

	out := map[string]any{
		"data": obj.QuestionListRes.GetData(),
	}
	obj.ObjResponse.SetOutData(out)
}
