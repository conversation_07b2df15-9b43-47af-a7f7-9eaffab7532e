package activity

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/activity/joinLottery"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/activity"
)

func JoinLottery(ctx context.Context, req ghttp.Request) ghttp.Response {
	return joinLotteryActionInstance(ctx, req).UiFuncHandler()
}

type joinLotteryAction struct {
	*uiclient.UIBaseAction
	joinLotteryReq *joinLottery.JoinLotteryReqIdl
	joinLotteryRes *joinLottery.JoinLotteryResIdl
	baseData       *types.JoinLotteryBaseData
}

func joinLotteryActionInstance(ctx context.Context, req ghttp.Request) *joinLotteryAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &joinLotteryAction{
		UIBaseAction:   &uiclient.UIBaseAction{},
		joinLotteryReq: &joinLottery.JoinLotteryReqIdl{},
		joinLotteryRes: &joinLottery.JoinLotteryResIdl{},
		baseData:       &types.JoinLotteryBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.joinLotteryReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	obj.joinLotteryRes.Data = &joinLottery.JoinLotteryRes{}
	return obj
}

func (j *joinLotteryAction) ParseParams() {
	if nil == j || nil == j.joinLotteryReq || nil == j.joinLotteryRes.Data || nil == j.ObjRequest {
		return
	}
	j.baseData.Request = j.joinLotteryReq.GetData()
}

func (j *joinLotteryAction) GetPrivateInfo() map[string]any {
	if nil == j || nil == j.ObjRequest || nil == j.joinLotteryReq || nil == j.joinLotteryReq.Data {
		return nil
	}
	j.ObjRequest.AddStrategy("check_sign", false)
	arrPrivateInfo := map[string]any{}
	return arrPrivateInfo
}

func (j *joinLotteryAction) CheckPrivate() bool {
	if nil == j || nil == j.ObjRequest || nil == j.joinLotteryReq {
		return false
	}
	return true
}

func (j *joinLotteryAction) Execute() bool {
	if nil == j || nil == j.ObjRequest || nil == j.joinLotteryReq || nil == j.joinLotteryReq.Data {
		return false
	}
	
	errNo := activity.NewJoinLottery(j.baseData).Execute(j.Ctx, j.baseData, j.joinLotteryRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		j.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (j *joinLotteryAction) BuildOut() {
	if nil == j || nil == j.joinLotteryRes {
		return
	}
	if nil == j.joinLotteryRes.Error {
		// 默认赋值成功
		j.joinLotteryRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": j.joinLotteryRes.GetData(),
	}
	j.ObjResponse.SetOutData(outData)
}

func (j *joinLotteryAction) BuildLog() {
	if nil == j || nil == j.Ctx || nil == j.Req || nil == j.joinLotteryReq || nil == j.joinLotteryReq.Data {
		return
	}
}
