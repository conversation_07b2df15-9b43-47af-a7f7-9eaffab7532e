package ad

import (
	"context"
	"encoding/json"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/cmap"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/ad/getFeedAd"

	golibExgraph "icode.baidu.com/baidu/tieba-server-go/golib2/exgraph"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/ad/getFeedAd/core"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/ad/getFeedAd/ext"
)

const GetFeedAdEngineName = "ad_getfeedad"

var (
	getFeedAdExgraph engine.Engine
)

func init() {
	var err error
	err = golibExgraph.InitExgraph()
	if err != nil {
		panic(err)
	}
	getFeedAdExgraph, err = golibExgraph.GetEngineByName(GetFeedAdEngineName)
	if err != nil {
		panic(err)
	}
}

func FeedAd(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getFeedAdActionInstance(ctx, req).UiFuncHandler()
}

type FeedAdAction struct {
	*uiclient.UIBaseAction
	feedAdReq *getFeedAd.GetFeedAdReqIdl
	feedAdRes *getFeedAd.GetFeedAdResIdl
	global    *types.FeedAdBaseData
}

func getFeedAdActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}

	obj := &FeedAdAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		feedAdReq:    &getFeedAd.GetFeedAdReqIdl{},
		feedAdRes:    &getFeedAd.GetFeedAdResIdl{},
		global:       &types.FeedAdBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.feedAdReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.feedAdRes.Data = new(getFeedAd.DataRes)
	return obj
}

func (obj *FeedAdAction) ParseParams() {
	if nil == obj || nil == obj.feedAdReq || nil == obj.feedAdRes ||
		nil == obj.feedAdRes.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.feedAdReq.GetData()

	localTzNameRaw := obj.ObjRequest.GetCommonAttr("local_tz_name", "")
	localTzName, ok := localTzNameRaw.(string)
	if !ok || localTzName == "" {
		// local_tz_name 不存在或为空，做初始化
		obj.ObjRequest.AddCommonAttr("local_tz_name", "Asia/Shanghai")
	}

}

func (obj *FeedAdAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.feedAdReq || nil == obj.feedAdReq.Data {
		return nil
	}
	adPlace := obj.feedAdReq.GetData().GetAdPlace()
	appTransmitData := obj.feedAdReq.GetData().GetAppTransmitData()
	adExtParams := obj.feedAdReq.GetData().GetAdExtParams()
	// TODO : 删除check_sign
	// obj.ObjRequest.AddStrategy("check_sign", false)
	arrPrivateInfo := map[string]interface{}{
		"ad_place":          adPlace,
		"app_transmit_data": appTransmitData,
		"ad_ext_params":     adExtParams,
		"need_call_mapping": 1,
	}

	return arrPrivateInfo
}

func (obj *FeedAdAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.feedAdReq {
		return false
	}

	return true
}

func (obj *FeedAdAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.global || nil == obj.feedAdReq || nil == obj.feedAdReq.Data {
		return false
	}

	obj.global.StaticField = new(types.FeedAdStaticField)
	staticField := obj.global.StaticField
	staticField.Init()
	staticField.UserID = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	staticField.Cuid = common.Tvttt(obj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
	staticField.ClientType = common.Tvttt(obj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	staticField.ClientVersion = common.Tvttt(obj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	staticField.OsVersion = common.Tvttt(obj.ObjRequest.GetCommonAttr("os_version", 0), common.TTT_INT).(int)
	staticField.IntIP = common.Tvttt(obj.ObjRequest.GetCommonAttr("ip_int", 0), common.TTT_UINT32).(uint32)
	staticField.IntNetType = common.Tvttt(obj.ObjRequest.GetCommonAttr("net_type", 0), common.TTT_INT).(int)
	staticField.CuidGalaxy2 = common.Tvttt(obj.ObjRequest.GetCommonAttr("cuid_galaxy2", ""), common.TTT_STRING).(string)
	staticField.Model = common.Tvttt(obj.ObjRequest.GetCommonAttr("model", ""), common.TTT_STRING).(string)
	staticField.BaidUID = common.Tvttt(obj.ObjRequest.GetCommonAttr("baiduid", ""), common.TTT_STRING).(string)
	staticField.Caid = common.Tvttt(obj.ObjRequest.GetCommonAttr("caid", ""), common.TTT_STRING).(string)
	staticField.AndroidID = common.Tvttt(obj.ObjRequest.GetCommonAttr("android_id", ""), common.TTT_STRING).(string)
	staticField.UserAgent = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_agent", ""), common.TTT_STRING).(string)
	staticField.Cmode = common.Tvttt(obj.ObjRequest.GetCommonAttr("cmode", ""), common.TTT_STRING).(string)
	staticField.Mac = common.Tvttt(obj.ObjRequest.GetCommonAttr("mac", ""), common.TTT_STRING).(string)
	staticField.ScreenWidth = common.Tvttt(obj.ObjRequest.GetCommonAttr("scr_w", 0), common.TTT_INT).(int)
	staticField.ScreenHeight = common.Tvttt(obj.ObjRequest.GetCommonAttr("scr_h", 0), common.TTT_INT).(int)
	staticField.ScreenDip = common.Tvttt(obj.ObjRequest.GetCommonAttr("scr_dip", float64(0)), common.TTT_FLOAT64).(float64)
	staticField.Idfa = common.Tvttt(obj.ObjRequest.GetCommonAttr("idfa", ""), common.TTT_STRING).(string)
	staticField.ShouBaiCuid = common.Tvttt(obj.ObjRequest.GetCommonAttr("shoubai_cuid", ""), common.TTT_STRING).(string)
	staticField.StrImei = common.Tvttt(obj.ObjRequest.GetCommonAttr("phone_imei", ""), common.TTT_STRING).(string)
	staticField.IntImei = common.Tvttt(staticField.StrImei, common.TTT_INT).(int)
	staticField.SampleID = common.Tvttt(obj.ObjRequest.GetCommonAttr("sample_id", ""), common.TTT_STRING).(string)

	staticField.AppTransmitData = obj.ObjRequest.GetPrivateAttr("app_transmit_data", nil).(*client.AppTransmitData)
	staticField.AdPlace = common.Tvttt(obj.ObjRequest.GetPrivateAttr("ad_place", ""), common.TTT_STRING).(string)

	staticField.Brand = common.Tvttt(obj.ObjRequest.GetCommonAttr("brand", ""), common.TTT_STRING).(string)

	obj.global.TmpData = cmap.New()

	// ad_place 逻辑处理
	adPlaceParam := map[string]map[string]int{}
	if err := json.Unmarshal([]byte(staticField.AdPlace), &adPlaceParam); err != nil {
		obj.Error(tiebaerror.ERR_PARAM_ERROR, tiebaerror.GetErrMsg(tiebaerror.ERR_PARAM_ERROR), false)
		tbcontext.WarningF(obj.Ctx, "json unmarshal fail: %v", err.Error())
	}
	channelData := map[string]map[string]int{}
	for location, channels := range adPlaceParam {
		for channel, count := range channels {
			if _, exists := channelData[channel]; !exists {
				channelData[channel] = make(map[string]int)
			}
			channelData[channel][location] = count
		}
	}
	staticField.AdNeedParam = channelData

	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}

	exctx := getFeedAdExgraph.NewContext(obj.Ctx, opts...)

	exctx.MustRegisterInstance(&obj.global)
	exctx.MustRegisterInstance(&obj.feedAdRes.Data)

	err := getFeedAdExgraph.Run(exctx)
	if err != nil {
		obj.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, tiebaerror.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		tbcontext.FatalF(obj.Ctx, "exgraph engine run fail.err:%s", err.Error())
	}

	exctx.RangeOpCost(func(opName string, cost time.Duration) {
		obj.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	return true
}

func (obj *FeedAdAction) BuildOut() {
	if nil == obj || nil == obj.feedAdRes {
		return
	}
	if nil == obj.feedAdRes.Error {
		//默认赋值成功
		obj.feedAdRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	obj.ObjResponse.SetStructData(obj.feedAdRes)
}

func (obj *FeedAdAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.feedAdReq || nil == obj.feedAdReq.Data {
		return
	}
}
