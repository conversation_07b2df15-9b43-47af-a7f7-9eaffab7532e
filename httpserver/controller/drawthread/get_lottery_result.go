package drawthread

import (
	"context"
	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	client "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	dtProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/drawthread"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/drawthread"
)

// GetLotteryResult 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func GetLotteryResult(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getLotteryResultActionInstance(ctx, req).UiFuncHandler()
}

// getLotteryResultAction 服务相关结构体
type getLotteryResultAction struct {
	// 组合UIBaseAction基础类，并实现基类方法
	*uiclient.UIBaseAction
	// 通过proto定义输入数据
	getLotteryResultReq *dtProto.GetLotteryResultReqIdl
	// 通过proto定义输出数据
	getLotteryResultRes *dtProto.GetLotteryResultResIdl
	// 通过types封装跟service层交互的数据
	baseData *types.GetLotteryResultBaseData
}

// getLotteryResultActionInstance 创建一个getLotteryResultAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回getLotteryResultAction实例
func getLotteryResultActionInstance(ctx context.Context, req ghttp.Request) *getLotteryResultAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &getLotteryResultAction{
		UIBaseAction:        &uiclient.UIBaseAction{},
		getLotteryResultReq: &dtProto.GetLotteryResultReqIdl{},
		getLotteryResultRes: &dtProto.GetLotteryResultResIdl{
			Data:  &dtProto.GetLotteryResultData{},
			Error: &client.Error{},
		},
		baseData: &types.GetLotteryResultBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.getLotteryResultReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

// ParseParams 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *getLotteryResultAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.getLotteryResultReq == nil || obj.getLotteryResultReq.Data == nil {
		return
	}
	obj.baseData.Request = obj.getLotteryResultReq.GetData()
}

// GetPrivateInfo 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *getLotteryResultAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.getLotteryResultReq == nil || obj.getLotteryResultReq.Data == nil {
		return nil
	}
	privateInfo := map[string]any{
		"check_login": true,
		"need_login":  true,
	}
	return privateInfo
}

// CheckPrivate 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *getLotteryResultAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getLotteryResultReq == nil {
		return false
	}
	return true
}

// BuildOut 构建输出数据的方法
// @Param：无
// @Return：无
func (obj *getLotteryResultAction) BuildOut() {
	if obj == nil || obj.getLotteryResultRes == nil {
		return
	}
	if obj.getLotteryResultRes.Error == nil {
		obj.getLotteryResultRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	outData := map[string]any{
		"data": obj.getLotteryResultRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
	return
}

// BuildLog 创建日志
// @Param：无
// @Return：无
func (obj *getLotteryResultAction) BuildLog() {
	return
}

// Execute 执行getLotteryResultAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *getLotteryResultAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.getLotteryResultReq == nil || obj.getLotteryResultReq.Data == nil {
		return false
	}
	errno := service.GetLotteryResult(obj.Ctx, obj.baseData, obj.getLotteryResultRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}
