package drawthread

import (
	"context"
	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	client "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	drawthreadProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/drawthread"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/drawthread"
)

// MyDrawList 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func MyDrawList(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 通过myDrawListActionInstance实例化myDrawListAction类，并调用UiFuncHandler()方法给ghttp.Response赋值
	return myDrawListActionInstance(ctx, req).UiFuncHandler()
}

// myDrawListAction 服务相关结构体
type myDrawListAction struct {
	// 组合UIBaseAction基础类，并实现基类方法
	*uiclient.UIBaseAction
	// 通过proto定义输入数据
	myDrawListReq *drawthreadProto.MyDrawListReqIdl
	// 通过proto定义输出数据
	myDrawListRes *drawthreadProto.MyDrawListResIdl
	// 通过types封装跟service层交互的数据
	baseData *types.MyDrawListBaseData
}

// myDrawListActionInstance 创建一个myDrawListAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回myDrawListAction实例
func myDrawListActionInstance(ctx context.Context, req ghttp.Request) *myDrawListAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &myDrawListAction{
		UIBaseAction:  &uiclient.UIBaseAction{},
		myDrawListReq: &drawthreadProto.MyDrawListReqIdl{},
		myDrawListRes: &drawthreadProto.MyDrawListResIdl{
			// 如果myDrawListResIdl中存在Data, 则初始化
			Data: &drawthreadProto.MyDrawListDataRes{},
			// 通过client包的Error类初始化
			Error: &client.Error{},
		},
		baseData: &types.MyDrawListBaseData{},
	}
	// 通过Init方法赋值
	if !obj.Init(ctx, req, obj, obj.myDrawListReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

// ParseParams 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *myDrawListAction) ParseParams() {
	if obj == nil || obj.ObjRequest == nil || obj.myDrawListReq == nil || obj.myDrawListReq.Data == nil {
		return
	}
	obj.baseData.Request = obj.myDrawListReq.GetData()
}

// GetPrivateInfo 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *myDrawListAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.myDrawListReq == nil || obj.myDrawListReq.Data == nil {
		return nil
	}
	privateInfo := map[string]any{
		// 如果需要检测是否办公网登录，则设置为true，否则为false， 默认是不需要
		"check_login": true,
		"need_login":  true,
	}
	return privateInfo
}

// CheckPrivate 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *myDrawListAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.myDrawListReq == nil {
		return false
	}
	return true
}

// BuildOut 构建输出数据的方法
// @Param：无
// @Return：无
func (obj *myDrawListAction) BuildOut() {
	if obj == nil || obj.myDrawListRes == nil {
		return
	}
	if obj.myDrawListRes.Error == nil {
		// 通过client包的Error类初始化
		obj.myDrawListRes.Error = &client.Error{
			// 调用google.golang.org/protobuf/proto的方法
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}

	// 当接口返回值有除了error外的字段，生成如下代码，否则不需要生成
	outData := map[string]any{
		"data": obj.myDrawListRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)

	return
}

// BuildLog 创建日志
// @Param：无
// @Return：无
func (obj *myDrawListAction) BuildLog() {
	return
}

// Execute 执行myDrawListAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *myDrawListAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.myDrawListReq == nil || obj.myDrawListReq.Data == nil {
		return false
	}
	// 业务逻辑封装到service层，调用service层方法
	errno := service.MyDrawList(obj.Ctx, obj.baseData, obj.myDrawListRes)
	if errno != tiebaerror.ERR_SUCCESS {
		// 通过stcdefine获取错误消息
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}
