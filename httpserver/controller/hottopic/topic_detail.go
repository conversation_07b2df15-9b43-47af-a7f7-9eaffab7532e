package hottopic

import (
	"context"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/stlog"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	golibExgraph "icode.baidu.com/baidu/tieba-server-go/golib2/exgraph"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/hottopic/core"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/hottopic/ext"
)

const TopicdetailEngineName = "hottopic_topicdetail"

var (
	topicdetailExgraph engine.Engine
)

func init() {
	var err error
	err = golibExgraph.InitExgraph()
	if err != nil {
		panic(err)
	}
	topicdetailExgraph, err = golibExgraph.GetEngineByName(TopicdetailEngineName)
	if err != nil {
		panic(err)
	}
}

func TopicDetail(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getTopicDetailInstance(ctx, req).UiFuncHandler()
}

type TopicDetailAction struct {
	*uiclient.UIBaseAction
	TopicDetailReq *topicDetail.TopicDetailReqIdl
	TopicDetailRes *topicDetail.TopicDetailResIdl
	global         *types.CTopicDetailBaseData
}

func getTopicDetailInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	if nil == ctx || nil == req {
		return nil
	}

	// 打点JA3信息  todo 待确定是否保留
	// HTTP_X_BFE_SSL_JA3_HASH 示例：dof5f23eed31e09c221b323a3ce64109
	// HTTP_X_BFE_SSL_JA3_RAW 示例：771,4865-4866-4867-49195-49196-52393-49199-49200-52392-
	// 49171-49172-156-157-47-53,0-23-65281-1,0-11-35-16-5-13-51-45-43-21,29-23-24,0
	ja3Hash := req.HeaderDefault("X-Bfe-Ssl-Ja3-Hash", "")
	ja3Raw := req.HeaderDefault("X-Bfe-Ssl-Ja3-Raw", "")
	stlog.AddLog(ctx, "http_x_bfe_ssl_ja3_hash", ja3Hash)
	stlog.AddLog(ctx, "http_x_bfe_ssl_ja3_raw", ja3Raw)

	obj := &TopicDetailAction{
		UIBaseAction:   &uiclient.UIBaseAction{},
		TopicDetailReq: &topicDetail.TopicDetailReqIdl{},
		TopicDetailRes: &topicDetail.TopicDetailResIdl{},
		global:         &types.CTopicDetailBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.TopicDetailReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.global.StaticField = new(types.TopicDetailStaticField)
	obj.TopicDetailRes.Data = new(topicDetail.DataRes)
	return obj
}

func (obj *TopicDetailAction) ParseParams() {
	if nil == obj || nil == obj.TopicDetailReq || nil == obj.TopicDetailReq.Data || nil == obj.ObjRequest {
		return
	}
	obj.global.Request = obj.TopicDetailReq.GetData()
}

func (obj *TopicDetailAction) GetPrivateInfo() map[string]interface{} {
	if nil == obj || nil == obj.ObjRequest || nil == obj.TopicDetailReq || nil == obj.TopicDetailReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.TopicDetailReq.GetData()

	arrPrivateInfo := map[string]any{
		"topic_id":   obj.global.Request.GetTopicId(),
		"topic_name": obj.global.Request.GetTopicName(),
		"pn":         obj.TopicDetailReq.GetData().GetPn(),
		"last_id":    obj.TopicDetailReq.GetData().GetLastId(),
	}

	return arrPrivateInfo
}

func (obj *TopicDetailAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.TopicDetailReq {
		return false
	}

	if obj.TopicDetailReq.GetData().GetTopicId() <= 0 && len(obj.TopicDetailReq.GetData().GetTopicName()) == 0 {
		return false
	}

	if obj.TopicDetailReq.GetData().GetPn() > 1 && obj.TopicDetailReq.GetData().GetLastId() <= 0 {
		return false
	}

	obj.global.StaticField.TopicID = obj.TopicDetailReq.GetData().GetTopicId()
	obj.global.StaticField.TopicName = obj.TopicDetailReq.GetData().GetTopicName()

	return true
}

func (obj *TopicDetailAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.TopicDetailReq || nil == obj.TopicDetailReq.Data {
		return false
	}

	opts := []engine.ContextOption{
		engine.WithLogger(logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService()).AsEngineLogger()),
	}

	exctx := topicdetailExgraph.NewContext(obj.Ctx, opts...)

	exctx.MustRegisterInstance(&obj.global)
	exctx.MustRegisterInstance(&obj.TopicDetailRes.Data)

	err := topicdetailExgraph.Run(exctx)
	if err != nil {
		tbcontext.WarningF(obj.Ctx, "exgraph engine run fail.err:%s", err.Error())
	}

	exctx.RangeOpCost(func(opName string, cost time.Duration) {
		obj.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	return true
}

func (obj *TopicDetailAction) BuildOut() {
	if nil == obj || nil == obj.TopicDetailRes {
		return
	}
	if nil == obj.TopicDetailRes.Error {
		// 默认赋值成功
		obj.TopicDetailRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	obj.ObjResponse.SetStructData(obj.TopicDetailRes)
}

func (obj *TopicDetailAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.TopicDetailReq || nil == obj.TopicDetailReq.Data {
		return
	}
}
