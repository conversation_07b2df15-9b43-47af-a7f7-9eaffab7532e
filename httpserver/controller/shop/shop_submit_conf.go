package shop

import (
	"context"
	"errors"
	"strconv"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	shopsubmitconfproto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/shopSubmitConf"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/shop/shopsubmitconf/adapter"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/shop/shopsubmitconf/gen"
)

// ShopSubmitConf _
func ShopSubmitConf(ctx context.Context, req ghttp.Request) ghttp.Response {
	return shopSubmitConfActionInstance(ctx, req).UiFuncHandler()
}

// shopSubmitConfAction _
type shopSubmitConfAction struct {
	*uiclient.UIBaseAction
	shopSubmitConfReq *shopsubmitconfproto.ShopSubmitConfReqIdl
	shopSubmitConfRes *shopsubmitconfproto.ShopSubmitConfResIdl
}

// shopSubmitConfActionInstance _
func shopSubmitConfActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	obj := &shopSubmitConfAction{
		UIBaseAction:      &uiclient.UIBaseAction{},
		shopSubmitConfReq: &shopsubmitconfproto.ShopSubmitConfReqIdl{},
		shopSubmitConfRes: &shopsubmitconfproto.ShopSubmitConfResIdl{
			Error: &client.Error{
				Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
				Errmsg:  proto.String(""),
				Usermsg: proto.String(""),
			},
			Data: &shopsubmitconfproto.ShopSubmitConfRes{
				ShopStatus:  proto.Uint32(0),
				ShopDesc:    proto.String(""),
				AuditReason: proto.String(""),
				QrCode:      proto.String(""),
			},
		},
	}
	if !obj.Init(ctx, req, obj, obj.shopSubmitConfReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	return obj
}

// ParseParams 格式化参数，由private_params算子代替
func (s *shopSubmitConfAction) ParseParams() {
	return
}

// GetPrivateInfo 获取私有参数，由private_params算子代替
func (s *shopSubmitConfAction) GetPrivateInfo() map[string]any {
	data := s.shopSubmitConfReq.GetData()
	privateInfo := map[string]any{
		"forum_id": data.GetForumId(),
	}
	return privateInfo
}

// CheckPrivate 校验私有参数，由private_params算子代替
func (s *shopSubmitConfAction) CheckPrivate() bool {
	if s == nil || s.ObjRequest == nil || s.shopSubmitConfReq == nil || s.shopSubmitConfReq.Data == nil {
		return false
	}
	return true
}

func (s *shopSubmitConfAction) Execute() bool {
	if s == nil || s.ObjRequest == nil || s.ObjResponse == nil || s.shopSubmitConfRes == nil {
		return false
	}

	ll := logging.WrapLogit(s.Ctx, tbcontext.GetLogidString(s.Ctx), loggerResource.GetLoggerService())
	defer func() {
		logging.ReleaseLogitLogger(ll)
	}()

	engCtx := resource.ShopSubmitConfEngine.NewContext(s.Ctx, []engine.ContextOption{
		engine.WithLogger(ll.AsEngineLogger()),
	}...)
	defer func() {
		engine.ReleaseContext(engCtx)
	}()

	engCtx.MustRegisterInstance(&s.UIBaseAction)
	err := resource.ShopSubmitConfEngine.Run(engCtx)

	// 记录算子耗时
	engCtx.RangeOpCost(func(opName string, cost time.Duration) {
		s.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	// 算子中设置过错误号，使用算子设置的错误号作为返回
	var state engine.State
	if errors.As(err, &state) {
		tbcontext.WarningF(s.Ctx, "exgraph execute fail, code=%d, errmsg=%s", state.Code(), state.Error())
		s.Error(state.Code(), state.Error(), false)
		return false
	}

	// 未设置过错误号，兜底返回错误信息
	if err != nil {
		tbcontext.WarningF(s.Ctx, "exgraph execute fail, err=%v", err)
		s.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, stcdefine.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return false
	}

	var output shopsubmitconfproto.ShopSubmitConfRes
	ok := engCtx.MutableInstance(&output)
	if !ok {
		tbcontext.WarningF(s.Ctx, "get shop_submit_conf output fail")
		return false
	}
	s.shopSubmitConfRes.Data = &output
	return true
}

func (s *shopSubmitConfAction) BuildOut() {
	if s == nil || s.shopSubmitConfRes == nil {
		return
	}

	s.ObjResponse.SetOutData(
		map[string]any{
			"data": s.shopSubmitConfRes.GetData(),
		},
	)
	return
}

func (s *shopSubmitConfAction) BuildLog() {
	return
}
