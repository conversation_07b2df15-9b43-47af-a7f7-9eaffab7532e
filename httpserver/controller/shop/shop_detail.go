package shop

import (
	"context"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func GetForumShopDetail(ctx context.Context, req ghttp.Request) ghttp.Response {
	return GetForumShopDetailActionInstance(ctx, req).UiFuncHandler()
}

type GetForumShopDetailAction struct {
	*uiclient.UIBaseAction
	GetForumShopDetailReq *goods.GetForumShopDetailReqIdl
	GetForumShopDetailRes *goods.GetForumShopDetailResIdl
	global                *types.GetForumShopGoodsDetailBaseData
}

func GetForumShopDetailActionInstance(ctx context.Context, req ghttp.Request) *GetForumShopDetailAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &GetForumShopDetailAction{
		UIBaseAction:          &uiclient.UIBaseAction{},
		GetForumShopDetailReq: &goods.GetForumShopDetailReqIdl{},
		GetForumShopDetailRes: &goods.GetForumShopDetailResIdl{},
		global:                &types.GetForumShopGoodsDetailBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.GetForumShopDetailReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.GetForumShopDetailRes.Data = new(goods.GetForumShopDetailRes)

	return obj
}

func (obj *GetForumShopDetailAction) ParseParams() {

}

func (obj *GetForumShopDetailAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetForumShopDetailReq || nil == obj.GetForumShopDetailReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.GetForumShopDetailReq.GetData()

	arrPrivateInfo := map[string]any{}

	return arrPrivateInfo
}

func (obj *GetForumShopDetailAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetForumShopDetailReq {
		return false
	}

	return true
}

func (obj *GetForumShopDetailAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetForumShopDetailReq || nil == obj.GetForumShopDetailReq.Data {
		return false
	}
	errNo := shop.Detail(obj.Ctx, obj.global, obj.GetForumShopDetailRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *GetForumShopDetailAction) BuildOut() {
	if nil == obj || nil == obj.GetForumShopDetailRes {
		return
	}
	if nil == obj.GetForumShopDetailRes.Error {
		// 默认赋值成功
		obj.GetForumShopDetailRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.GetForumShopDetailRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *GetForumShopDetailAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.GetForumShopDetailReq || nil == obj.GetForumShopDetailReq.Data {
		return
	}
}
