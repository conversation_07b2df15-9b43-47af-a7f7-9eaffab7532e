package shop

import (
	"context"
	"errors"
	"strconv"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/searchshopgoodsrecommend/adapter"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/searchshopgoodsrecommend/gen"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
)

// SearchShopGoodsRecommend _
func SearchShopGoodsRecommend(ctx context.Context, req ghttp.Request) ghttp.Response {
	return SearchShopGoodsRecommendActionInstance(ctx, req).UiFuncHandler()
}

// SearchShopGoodsRecommendAction _
type SearchShopGoodsRecommendAction struct {
	*uiclient.UIBaseAction
	searchShopGoodsRecommendReq *goods.SearchShopGoodsReqIdl
	searchShopGoodsRecommendRes *goods.SearchShopGoodsResIdl
}

// SearchShopGoodsRecommendActionInstance _
func SearchShopGoodsRecommendActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	obj := &SearchShopGoodsRecommendAction{
		UIBaseAction:                &uiclient.UIBaseAction{},
		searchShopGoodsRecommendReq: &goods.SearchShopGoodsReqIdl{},
		searchShopGoodsRecommendRes: &goods.SearchShopGoodsResIdl{
			Error: &client.Error{
				Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
				Errmsg:  proto.String(""),
				Usermsg: proto.String(""),
			},
			Data: &goods.SearchShopGoodsRes{},
		},
	}
	if !obj.Init(ctx, req, obj, obj.searchShopGoodsRecommendReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	return obj
}

// ParseParams 格式化参数，由private_params算子代替
func (obj *SearchShopGoodsRecommendAction) ParseParams() {
	return
}

// GetPrivateInfo 获取私有参数，由private_params算子代替
func (obj *SearchShopGoodsRecommendAction) GetPrivateInfo() map[string]any {
	obj.ObjRequest.AddStrategy("check_sign", false)
	return map[string]any{}
}

// CheckPrivate 校验私有参数，由private_params算子代替
func (obj *SearchShopGoodsRecommendAction) CheckPrivate() bool {
	return true
}

func (obj *SearchShopGoodsRecommendAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.ObjResponse == nil || obj.searchShopGoodsRecommendRes == nil {
		return false
	}

	ll := logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService())
	defer func() {
		logging.ReleaseLogitLogger(ll)
	}()

	engCtx := resource.SearchShopGoodsRecommendEngine.NewContext(
		obj.Ctx, []engine.ContextOption{
			engine.WithLogger(ll.AsEngineLogger()),
		}...,
	)
	defer func() {
		engine.ReleaseContext(engCtx)
	}()

	engCtx.MustRegisterInstance(&obj.UIBaseAction)
	err := resource.SearchShopGoodsRecommendEngine.Run(engCtx)

	// 记录算子耗时
	engCtx.RangeOpCost(
		func(opName string, cost time.Duration) {
			obj.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
		},
	)

	// 算子中设置过错误号，使用算子设置的错误号作为返回
	var state engine.State
	if errors.As(err, &state) {
		tbcontext.WarningF(obj.Ctx, "exgraph execute fail, code=%d, errmsg=%s", state.Code(), state.Error())
		obj.Error(state.Code(), state.Error(), false)
		return false
	}

	// 未设置过错误号，兜底返回错误信息
	if err != nil {
		tbcontext.WarningF(obj.Ctx, "exgraph execute fail, err=%v", err)
		obj.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, stcdefine.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return false
	}

	var output goods.SearchShopGoodsRes
	ok := engCtx.MutableInstance(&output)
	if !ok {
		tbcontext.WarningF(obj.Ctx, "get search_shop_goods_recommend output fail")
		return false
	}
	obj.searchShopGoodsRecommendRes.Data = &output
	return true
}

func (obj *SearchShopGoodsRecommendAction) BuildOut() {
	if obj == nil || obj.searchShopGoodsRecommendRes == nil {
		return
	}

	obj.ObjResponse.SetOutData(
		map[string]any{
			"data": obj.searchShopGoodsRecommendRes.GetData(),
		},
	)
	return
}

func (obj *SearchShopGoodsRecommendAction) BuildLog() {
	return
}
