package shop

import (
	"context"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func GetForumShopDataOverview(ctx context.Context, req ghttp.Request) ghttp.Response {
	return GetForumShopDataOverviewActionInstance(ctx, req).UiFuncHandler()
}

type GetForumShopDataOverviewAction struct {
	*uiclient.UIBaseAction
	GetForumShopDataOverViewReq *goods.GetForumShopDataOverViewReqIdl
	GetForumShopDataOverViewRes *goods.GetForumShopDataOverViewResIdl
	global                      *types.GetForumShopGoodsDataOverviewBaseData
}

func GetForumShopDataOverviewActionInstance(ctx context.Context, req ghttp.Request) *GetForumShopDataOverviewAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &GetForumShopDataOverviewAction{
		UIBaseAction:                &uiclient.UIBaseAction{},
		GetForumShopDataOverViewReq: &goods.GetForumShopDataOverViewReqIdl{},
		GetForumShopDataOverViewRes: &goods.GetForumShopDataOverViewResIdl{},
		global:                      &types.GetForumShopGoodsDataOverviewBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.GetForumShopDataOverViewReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.GetForumShopDataOverViewRes.Data = make([]*client.ShopGoodsOrderOverviewData, 0)

	return obj
}

func (obj *GetForumShopDataOverviewAction) ParseParams() {

}

func (obj *GetForumShopDataOverviewAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetForumShopDataOverViewReq || nil == obj.GetForumShopDataOverViewReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.GetForumShopDataOverViewReq.GetData()

	arrPrivateInfo := map[string]any{}

	return arrPrivateInfo
}

func (obj *GetForumShopDataOverviewAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetForumShopDataOverViewReq {
		return false
	}

	return true
}

func (obj *GetForumShopDataOverviewAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetForumShopDataOverViewReq || nil == obj.GetForumShopDataOverViewReq.Data {
		return false
	}
	errNo := shop.OverView(obj.Ctx, obj.global, obj.GetForumShopDataOverViewRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *GetForumShopDataOverviewAction) BuildOut() {
	if nil == obj || nil == obj.GetForumShopDataOverViewRes {
		return
	}
	if nil == obj.GetForumShopDataOverViewRes.Error {
		// 默认赋值成功
		obj.GetForumShopDataOverViewRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.GetForumShopDataOverViewRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *GetForumShopDataOverviewAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.GetForumShopDataOverViewReq || nil == obj.GetForumShopDataOverViewReq.Data {
		return
	}
}
