package shop

import (
	"context"
	"errors"
	"strconv"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/shopIntroduce"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/forumshopintroduce/gen"
)

// ShopIntroduce _
func ShopIntroduce(ctx context.Context, req ghttp.Request) ghttp.Response {
	return shopIntroduceActionInstance(ctx, req).UiFuncHandler()
}

// shopIntroduceAction _
type shopIntroduceAction struct {
	*uiclient.UIBaseAction
	shopIntroduceReq *shopIntroduce.GetShopIntroduceReqIdl
	shopIntroduceRes *shopIntroduce.GetShopIntroduceResIdl
}

// shopIntroduceActionInstance _
func shopIntroduceActionInstance(ctx context.Context, req ghttp.Request) uiclient.UiHander {
	obj := &shopIntroduceAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		shopIntroduceReq: &shopIntroduce.GetShopIntroduceReqIdl{},
		shopIntroduceRes: &shopIntroduce.GetShopIntroduceResIdl{
			Error: &client.Error{
				Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
				Errmsg:  proto.String(""),
				Usermsg: proto.String(""),
			},
		},
	}
	if !obj.Init(ctx, req, obj, obj.shopIntroduceReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	return obj
}

// ParseParams 格式化参数，由private_params算子代替
func (s *shopIntroduceAction) ParseParams() {
	return
}

// GetPrivateInfo 获取私有参数，由private_params算子代替
func (s *shopIntroduceAction) GetPrivateInfo() map[string]any {
	return map[string]any{}
}

// CheckPrivate 校验私有参数，由private_params算子代替
func (s *shopIntroduceAction) CheckPrivate() bool {
	return true
}

func (s *shopIntroduceAction) Execute() bool {
	if s == nil || s.ObjRequest == nil || s.ObjResponse == nil || s.shopIntroduceRes == nil {
		return false
	}

	ll := logging.WrapLogit(s.Ctx, tbcontext.GetLogidString(s.Ctx), loggerResource.GetLoggerService())
	defer func() {
		logging.ReleaseLogitLogger(ll)
	}()

	engCtx := resource.ForumShopIntroduceEngine.NewContext(s.Ctx, []engine.ContextOption{
		engine.WithLogger(ll.AsEngineLogger()),
	}...)
	defer func() {
		engine.ReleaseContext(engCtx)
	}()

	engCtx.MustRegisterInstance(&s.UIBaseAction)

	err := resource.ForumShopIntroduceEngine.Run(engCtx)

	// 记录算子耗时
	engCtx.RangeOpCost(func(opName string, cost time.Duration) {
		s.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	// 算子中设置过错误号，使用算子设置的错误号作为返回
	var state engine.State
	if errors.As(err, &state) {
		tbcontext.WarningF(s.Ctx, "exgraph execute fail, code=%d, errmsg=%s", state.Code(), state.Error())
		s.Error(state.Code(), state.Error(), false)
		return false
	}

	// 未设置过错误号，兜底返回错误信息
	if err != nil {
		tbcontext.WarningF(s.Ctx, "exgraph execute fail, err=%v", err)
		s.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, stcdefine.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return false
	}

	// 获取完整的响应数据
	var output shopIntroduce.GetShopIntroduceResIdl
	ok := engCtx.MutableInstance(&output)
	if !ok {
		tbcontext.WarningF(s.Ctx, "get shop_introduce output fail")
		return false
	}

	s.shopIntroduceRes.Data = output.Data
	return true
}

func (s *shopIntroduceAction) BuildOut() {
	if s == nil || s.shopIntroduceRes == nil {
		return
	}

	s.ObjResponse.SetOutData(
		map[string]any{
			"data": s.shopIntroduceRes.GetData(),
		},
	)
	return
}

func (s *shopIntroduceAction) BuildLog() {
	return
}
