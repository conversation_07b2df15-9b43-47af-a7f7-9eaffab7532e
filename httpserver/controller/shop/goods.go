package shop

import (
	"context"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func ForumShop(ctx context.Context, req ghttp.Request) ghttp.Response {
	return ForumShopActionInstance(ctx, req).UiFuncHandler()
}

type ForumShopAction struct {
	*uiclient.UIBaseAction
	ForumShopReq *goods.ForumShopReqIdl
	ForumShopRes *goods.ForumShopResIdl
	global       *types.ShopGoodsBaseData
}

func ForumShopActionInstance(ctx context.Context, req ghttp.Request) *ForumShopAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &ForumShopAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		ForumShopReq: &goods.ForumShopReqIdl{},
		ForumShopRes: &goods.ForumShopResIdl{},
		global:       &types.ShopGoodsBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.ForumShopReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.ForumShopRes.Data = new(client.ForumShopData)

	return obj
}

func (obj *ForumShopAction) ParseParams() {

}

func (obj *ForumShopAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ForumShopReq || nil == obj.ForumShopReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.ForumShopReq.GetData()

	arrPrivateInfo := map[string]any{}

	return arrPrivateInfo
}

func (obj *ForumShopAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ForumShopReq {
		return false
	}

	return true
}

func (obj *ForumShopAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ForumShopReq || nil == obj.ForumShopReq.Data {
		return false
	}
	errNo := shop.Goods(obj.Ctx, obj.global, obj.ForumShopRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *ForumShopAction) BuildOut() {
	if nil == obj || nil == obj.ForumShopRes {
		return
	}
	if nil == obj.ForumShopRes.Error {
		// 默认赋值成功
		obj.ForumShopRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.ForumShopRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *ForumShopAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.ForumShopReq || nil == obj.ForumShopReq.Data {
		return
	}
}
