package shop

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	client "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	goodsProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop"
)

// AddForumShopGoods 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func AddForumShopGoods(ctx context.Context, req ghttp.Request) ghttp.Response {
	return AddForumShopGoodsActionInstance(ctx, req).UiFuncHandler()
}

// AddForumShopGoodsAction 服务相关结构体
type AddForumShopGoodsAction struct {
	*uiclient.UIBaseAction
	AddForumShopGoodsReq *goodsProto.AddForumShopGoodsReqIdl
	AddForumShopGoodsRes *goodsProto.AddForumShopGoodsResIdl
	baseData             *types.AddForumShopGoodsBaseData
}

// AddForumShopGoodsActionInstance 创建一个AddForumShopGoodsAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回AddForumShopGoodsAction实例
func AddForumShopGoodsActionInstance(ctx context.Context, req ghttp.Request) *AddForumShopGoodsAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &AddForumShopGoodsAction{
		UIBaseAction:         &uiclient.UIBaseAction{},
		AddForumShopGoodsReq: &goodsProto.AddForumShopGoodsReqIdl{},
		AddForumShopGoodsRes: &goodsProto.AddForumShopGoodsResIdl{
			Error: &client.Error{},
		},
		baseData: &types.AddForumShopGoodsBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.AddForumShopGoodsReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	obj.AddForumShopGoodsRes.Data = new(goodsProto.AddForumShopGoodsData)

	return obj
}

// ParseParams 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *AddForumShopGoodsAction) ParseParams() {
}

// GetPrivateInfo 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *AddForumShopGoodsAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.AddForumShopGoodsReq == nil || obj.AddForumShopGoodsReq.Data == nil {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)
	// goodsInfo := &client.ShopGoodsInfo{}

	obj.baseData.Request = obj.AddForumShopGoodsReq.GetData()

	privateInfo := map[string]any{}
	return privateInfo
}

// CheckPrivate 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *AddForumShopGoodsAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.AddForumShopGoodsReq == nil {
		return false
	}
	return true
}

// BuildOut 构建输出数据的方法
// @Param：无
// @Return：无
func (obj *AddForumShopGoodsAction) BuildOut() {
	if obj == nil || obj.AddForumShopGoodsRes == nil {
		return
	}
	if obj.AddForumShopGoodsRes.Error == nil {
		obj.AddForumShopGoodsRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	return
}

// BuildLog 创建日志
// @Param：无
// @Return：无
func (obj *AddForumShopGoodsAction) BuildLog() {
	return
}

// Execute 执行AddForumShopGoodsAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *AddForumShopGoodsAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.AddForumShopGoodsReq == nil || obj.AddForumShopGoodsReq.Data == nil {
		return false
	}
	obj.baseData.UserID = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", ""), common.TTT_UINT64).(uint64)

	errno := service.AddGoods(obj.Ctx, obj.baseData, obj.AddForumShopGoodsRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}
