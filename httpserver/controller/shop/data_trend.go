package shop

import (
	"context"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func GetForumShopDataTrend(ctx context.Context, req ghttp.Request) ghttp.Response {
	return GetForumShopDataTrendActionInstance(ctx, req).UiFuncHandler()
}

type GetForumShopDataTrendAction struct {
	*uiclient.UIBaseAction
	GetForumShopDataTrendReq *goods.GetForumShopDataTrendReqIdl
	GetForumShopDataTrendRes *goods.GetForumShopDataTrendResIdl
	global                   *types.GetForumShopGoodsDataTrendBaseData
}

func GetForumShopDataTrendActionInstance(ctx context.Context, req ghttp.Request) *GetForumShopDataTrendAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &GetForumShopDataTrendAction{
		UIBaseAction:             &uiclient.UIBaseAction{},
		GetForumShopDataTrendReq: &goods.GetForumShopDataTrendReqIdl{},
		GetForumShopDataTrendRes: &goods.GetForumShopDataTrendResIdl{},
		global:                   &types.GetForumShopGoodsDataTrendBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.GetForumShopDataTrendReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.GetForumShopDataTrendRes.Data = make([]*goods.ShopGoodsOrderTrendData, 0)

	return obj
}

func (obj *GetForumShopDataTrendAction) ParseParams() {

}

func (obj *GetForumShopDataTrendAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetForumShopDataTrendReq || nil == obj.GetForumShopDataTrendReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.GetForumShopDataTrendReq.GetData()

	arrPrivateInfo := map[string]any{}

	return arrPrivateInfo
}

func (obj *GetForumShopDataTrendAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetForumShopDataTrendReq {
		return false
	}

	return true
}

func (obj *GetForumShopDataTrendAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetForumShopDataTrendReq || nil == obj.GetForumShopDataTrendReq.Data {
		return false
	}
	errNo := shop.Trend(obj.Ctx, obj.global, obj.GetForumShopDataTrendRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *GetForumShopDataTrendAction) BuildOut() {
	if nil == obj || nil == obj.GetForumShopDataTrendRes {
		return
	}
	if nil == obj.GetForumShopDataTrendRes.Error {
		// 默认赋值成功
		obj.GetForumShopDataTrendRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.GetForumShopDataTrendRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *GetForumShopDataTrendAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.GetForumShopDataTrendReq || nil == obj.GetForumShopDataTrendReq.Data {
		return
	}
}
