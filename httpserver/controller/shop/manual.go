package shop

import (
	"context"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func Manual(ctx context.Context, req ghttp.Request) ghttp.Response {
	return ManualActionInstance(ctx, req).UiFuncHandler()
}

type ManualAction struct {
	*uiclient.UIBaseAction
	ForumShopManualReq *goods.GetForumShopManualReqIdl
	ForumShopManualRes *goods.GetForumShopManualResIdl
	global             *types.ShopGoodsManualBaseData
}

func ManualActionInstance(ctx context.Context, req ghttp.Request) *ManualAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &ManualAction{
		UIBaseAction:       &uiclient.UIBaseAction{},
		ForumShopManualReq: &goods.GetForumShopManualReqIdl{},
		ForumShopManualRes: &goods.GetForumShopManualResIdl{},
		global:             &types.ShopGoodsManualBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.ForumShopManualReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.ForumShopManualRes.Data = make([]*goods.ShopManualData, 0)

	return obj
}

func (obj *ManualAction) ParseParams() {

}

func (obj *ManualAction) GetPrivateInfo() map[string]any {
	if nil == obj {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.ForumShopManualReq

	arrPrivateInfo := map[string]any{}

	return arrPrivateInfo
}

func (obj *ManualAction) CheckPrivate() bool {
	return true
}

func (obj *ManualAction) Execute() bool {
	if nil == obj {
		return false
	}
	errNo := shop.Manual(obj.Ctx, obj.ForumShopManualRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *ManualAction) BuildOut() {
	if nil == obj || nil == obj.ForumShopManualRes {
		return
	}
	if nil == obj.ForumShopManualRes.Error {
		// 默认赋值成功
		obj.ForumShopManualRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.ForumShopManualRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *ManualAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.ForumShopManualReq || nil == obj.ForumShopManualReq.Data {
		return
	}
}
