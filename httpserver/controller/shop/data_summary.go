package shop

import (
	"context"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func GetForumShopDataSummary(ctx context.Context, req ghttp.Request) ghttp.Response {
	return GetForumShopDataSummaryActionInstance(ctx, req).UiFuncHandler()
}

type GetForumShopDataSummaryAction struct {
	*uiclient.UIBaseAction
	GetForumShopDataSummaryReq *goods.GetForumShopDataSummaryReqIdl
	GetForumShopDataSummaryRes *goods.GetForumShopDataSummaryResIdl
	global                     *types.GetForumShopGoodsDataSummaryBaseData
}

func GetForumShopDataSummaryActionInstance(ctx context.Context, req ghttp.Request) *GetForumShopDataSummaryAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &GetForumShopDataSummaryAction{
		UIBaseAction:               &uiclient.UIBaseAction{},
		GetForumShopDataSummaryReq: &goods.GetForumShopDataSummaryReqIdl{},
		GetForumShopDataSummaryRes: &goods.GetForumShopDataSummaryResIdl{},
		global:                     &types.GetForumShopGoodsDataSummaryBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.GetForumShopDataSummaryReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.GetForumShopDataSummaryRes.Data = make([]*goods.ShopGoodsOrderSummaryData, 0)

	return obj
}

func (obj *GetForumShopDataSummaryAction) ParseParams() {

}

func (obj *GetForumShopDataSummaryAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetForumShopDataSummaryReq || nil == obj.GetForumShopDataSummaryReq.Data {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.GetForumShopDataSummaryReq.GetData()

	arrPrivateInfo := map[string]any{}

	return arrPrivateInfo
}

func (obj *GetForumShopDataSummaryAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetForumShopDataSummaryReq {
		return false
	}

	return true
}

func (obj *GetForumShopDataSummaryAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetForumShopDataSummaryReq || nil == obj.GetForumShopDataSummaryReq.Data {
		return false
	}
	errNo := shop.Summary(obj.Ctx, obj.global, obj.GetForumShopDataSummaryRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *GetForumShopDataSummaryAction) BuildOut() {
	if nil == obj || nil == obj.GetForumShopDataSummaryRes {
		return
	}
	if nil == obj.GetForumShopDataSummaryRes.Error {
		// 默认赋值成功
		obj.GetForumShopDataSummaryRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.GetForumShopDataSummaryRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *GetForumShopDataSummaryAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.GetForumShopDataSummaryReq || nil == obj.GetForumShopDataSummaryReq.Data {
		return
	}
}
