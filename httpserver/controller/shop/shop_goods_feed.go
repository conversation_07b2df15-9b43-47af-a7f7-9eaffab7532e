package shop

import (
	"context"
	"errors"
	"strconv"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/ps-se-go/exgraph-plug/logging"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	client "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/shopGoodsFeed"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	_ "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/forumshopgoodsfeed/gen"
)

func ShopGoodsFeed(ctx context.Context, req ghttp.Request) ghttp.Response {
	return ShopGoodsFeedActionInstance(ctx, req).UiFuncHandler()
}

type ShopGoodsFeedAction struct {
	*uiclient.UIBaseAction
	ShopGoodsFeedReq *shopGoodsFeed.ShopGoodsFeedReqIdl
	ShopGoodsFeedRes *shopGoodsFeed.ShopGoodsFeedResIdl
}

func ShopGoodsFeedActionInstance(ctx context.Context, req ghttp.Request) *ShopGoodsFeedAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &ShopGoodsFeedAction{
		UIBaseAction:     &uiclient.UIBaseAction{},
		ShopGoodsFeedReq: &shopGoodsFeed.ShopGoodsFeedReqIdl{},
		ShopGoodsFeedRes: &shopGoodsFeed.ShopGoodsFeedResIdl{
			Error: &client.Error{
				Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
				Errmsg:  proto.String(""),
				Usermsg: proto.String(""),
			},
			Data: &shopGoodsFeed.ShopGoodsFeedResData{},
		},
	}
	if !obj.Init(ctx, req, obj, obj.ShopGoodsFeedReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	return obj
}

func (obj *ShopGoodsFeedAction) ParseParams() {
}

func (obj *ShopGoodsFeedAction) GetPrivateInfo() map[string]any {
	obj.ObjRequest.AddStrategy("check_sign", false)
	arrPrivateInfo := map[string]any{
		"check_login": false,
		"need_login":  true,
	}
	return arrPrivateInfo
}

func (obj *ShopGoodsFeedAction) CheckPrivate() bool {
	return true
}

func (obj *ShopGoodsFeedAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.ObjResponse == nil || obj.ShopGoodsFeedRes == nil {
		return false
	}

	ll := logging.WrapLogit(obj.Ctx, tbcontext.GetLogidString(obj.Ctx), loggerResource.GetLoggerService())
	defer func() {
		logging.ReleaseLogitLogger(ll)
	}()

	engCtx := resource.ForumShopGoodsFeedEngine.NewContext(obj.Ctx, []engine.ContextOption{
		engine.WithLogger(ll.AsEngineLogger()),
	}...)
	defer func() {
		engine.ReleaseContext(engCtx)
	}()

	engCtx.MustRegisterInstance(&obj.UIBaseAction)
	err := resource.ForumShopGoodsFeedEngine.Run(engCtx)

	// 记录算子耗时
	engCtx.RangeOpCost(func(opName string, cost time.Duration) {
		obj.ObjResponse.AddLog("optor_"+opName, strconv.FormatFloat(float64(cost.Microseconds())/1e3, 'f', -1, 64))
	})

	// 算子中设置过错误号，使用算子设置的错误号作为返回
	var state engine.State
	if errors.As(err, &state) {
		tbcontext.WarningF(obj.Ctx, "exgraph execute fail, code=%d, errmsg=%s", state.Code(), state.Error())
		obj.Error(state.Code(), state.Error(), false)
		return false
	}

	// 未设置过错误号，兜底返回错误信息
	if err != nil {
		tbcontext.WarningF(obj.Ctx, "exgraph execute fail, err=%v", err)
		obj.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, stcdefine.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return false
	}

	var output shopGoodsFeed.ShopGoodsFeedResData
	ok := engCtx.MutableInstance(&output)
	if !ok {
		tbcontext.WarningF(obj.Ctx, "get shopGoodsFeed output fail")
		return false
	}

	obj.ShopGoodsFeedRes.Data = &output
	return true
}

func (obj *ShopGoodsFeedAction) BuildOut() {
	if obj == nil || obj.ShopGoodsFeedRes == nil {
		return
	}

	obj.ObjResponse.SetOutData(
		map[string]any{
			"data": obj.ShopGoodsFeedRes.GetData(),
		},
	)
	return
}

func (obj *ShopGoodsFeedAction) BuildLog() {
}
