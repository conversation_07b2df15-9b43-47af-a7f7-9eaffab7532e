package shop

import (
	"context"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func ConvertURL(ctx context.Context, req ghttp.Request) ghttp.Response {
	return ConvertURLActionInstance(ctx, req).UiFuncHandler()
}

type ConvertURLAction struct {
	*uiclient.UIBaseAction
	ForumShopConvertURLReq *goods.ConvertForumShopLinkReqIdl
	ForumShopConvertURLRes *goods.ConvertForumShopLinkResIdl
	global                 *types.ShopGoodsConvertURLBaseData
}

func ConvertURLActionInstance(ctx context.Context, req ghttp.Request) *ConvertURLAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &ConvertURLAction{
		UIBaseAction:           &uiclient.UIBaseAction{},
		ForumShopConvertURLReq: &goods.ConvertForumShopLinkReqIdl{},
		ForumShopConvertURLRes: &goods.ConvertForumShopLinkResIdl{},
		global:                 &types.ShopGoodsConvertURLBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.ForumShopConvertURLReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.ForumShopConvertURLRes.Data = new(goods.ConvertForumShopLinkData)

	return obj
}

func (obj *ConvertURLAction) ParseParams() {

}

func (obj *ConvertURLAction) GetPrivateInfo() map[string]any {
	if nil == obj {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.ForumShopConvertURLReq.GetData()

	arrPrivateInfo := map[string]any{}

	return arrPrivateInfo
}

func (obj *ConvertURLAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ForumShopConvertURLReq {
		return false
	}

	obj.global.UserID = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", ""), common.TTT_UINT64).(uint64)
	return true
}

func (obj *ConvertURLAction) Execute() bool {
	if nil == obj {
		return false
	}
	errNo := shop.Convert(obj.Ctx, obj.global, obj.ForumShopConvertURLRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *ConvertURLAction) BuildOut() {
	if nil == obj || nil == obj.ForumShopConvertURLRes {
		return
	}
	if nil == obj.ForumShopConvertURLRes.Error {
		// 默认赋值成功
		obj.ForumShopConvertURLRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.ForumShopConvertURLRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *ConvertURLAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.ForumShopConvertURLReq || nil == obj.ForumShopConvertURLReq.Data {
		return
	}
}
