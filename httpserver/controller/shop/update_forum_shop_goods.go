package shop

import (
	"context"

	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	client "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	goodsProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop"
)

// UpdateForumShopGoods 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func UpdateForumShopGoods(ctx context.Context, req ghttp.Request) ghttp.Response {
    return updateForumShopGoodsActionInstance(ctx, req).UiFuncHandler()
}

// updateForumShopGoodsAction 服务相关结构体
type updateForumShopGoodsAction struct {
    *uiclient.UIBaseAction
    updateForumShopGoodsReq *goodsProto.UpdateForumShopGoodsReqIdl
    updateForumShopGoodsRes *goodsProto.UpdateForumShopGoodsResIdl
    baseData                *types.UpdateForumShopGoodsBaseData
}

// updateForumShopGoodsActionInstance 创建一个updateForumShopGoodsAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回updateForumShopGoodsAction实例
func updateForumShopGoodsActionInstance(ctx context.Context, req ghttp.Request) *updateForumShopGoodsAction {
    if ctx == nil || req == nil {
        return nil
    }
    obj := &updateForumShopGoodsAction{
        UIBaseAction: &uiclient.UIBaseAction{},
        updateForumShopGoodsReq: &goodsProto.UpdateForumShopGoodsReqIdl{},
        updateForumShopGoodsRes: &goodsProto.UpdateForumShopGoodsResIdl{
            Error: &client.Error{},
        },
        baseData: &types.UpdateForumShopGoodsBaseData{},
    }
    if !obj.Init(ctx, req, obj, obj.updateForumShopGoodsReq) {
        tbcontext.FatalF(obj.Ctx, "UIBase init fail")
    }
    obj.baseData.BaseObj = obj.UIBaseAction
    return obj
}

// ParseParams 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *updateForumShopGoodsAction) ParseParams() {
}

// GetPrivateInfo 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *updateForumShopGoodsAction) GetPrivateInfo() map[string]any {
    if obj == nil || obj.ObjRequest == nil || obj.updateForumShopGoodsReq == nil || obj.updateForumShopGoodsReq.Data == nil {
        return nil
    }

    obj.ObjRequest.AddStrategy("check_sign", false)
    obj.baseData.Request = obj.updateForumShopGoodsReq.GetData()    
    
    privateInfo := map[string]any{}
    return privateInfo
}

// CheckPrivate 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *updateForumShopGoodsAction) CheckPrivate() bool {
    if obj == nil || obj.ObjRequest == nil || obj.updateForumShopGoodsReq == nil {
        return false
    }
    return true
}

// BuildOut 构建输出数据的方法
// @Param：无
// @Return：无
func (obj *updateForumShopGoodsAction) BuildOut() {
    if obj == nil || obj.updateForumShopGoodsRes == nil {
        return
    }
    if obj.updateForumShopGoodsRes.Error == nil {
        obj.updateForumShopGoodsRes.Error = &client.Error{
            Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
            Errmsg:  proto.String(""),
            Usermsg: proto.String(""),
        }
    }
    return
}

// BuildLog 创建日志
// @Param：无
// @Return：无
func (obj *updateForumShopGoodsAction) BuildLog() {
    return
}

// Execute 执行updateForumShopGoodsAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *updateForumShopGoodsAction) Execute() bool {
    if obj == nil || obj.ObjRequest == nil || obj.updateForumShopGoodsReq == nil || obj.updateForumShopGoodsReq.Data == nil {
        return false
    }
    errno := service.UpdateForumShopGoods(obj.Ctx, obj.baseData, obj.updateForumShopGoodsRes)
    if errno != tiebaerror.ERR_SUCCESS {
        obj.Error(errno, stcdefine.GetErrMsg(errno), true)
        return false
    }
    return true
}