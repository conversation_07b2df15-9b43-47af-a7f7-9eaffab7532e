package shop

import (
	"context"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func ChannelInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	return ChannelInfoActionInstance(ctx, req).UiFuncHandler()
}

type ChannelInfoAction struct {
	*uiclient.UIBaseAction
	ForumShopChannelInfoReq *goods.ForumShopChannelInfoReqIdl
	ForumShopChannelInfoRes *goods.ForumShopChannelInfoResIdl
	global                  *types.ShopGoodsChannelInfoBaseData
}

func ChannelInfoActionInstance(ctx context.Context, req ghttp.Request) *ChannelInfoAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &ChannelInfoAction{
		UIBaseAction:            &uiclient.UIBaseAction{},
		ForumShopChannelInfoReq: &goods.ForumShopChannelInfoReqIdl{},
		ForumShopChannelInfoRes: &goods.ForumShopChannelInfoResIdl{},
		global:                  &types.ShopGoodsChannelInfoBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.ForumShopChannelInfoReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.ForumShopChannelInfoRes.Data = new(goods.ChannelInfo)

	return obj
}

func (obj *ChannelInfoAction) ParseParams() {

}

func (obj *ChannelInfoAction) GetPrivateInfo() map[string]any {
	if nil == obj {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)

	obj.global.Request = obj.ForumShopChannelInfoReq.GetData()

	arrPrivateInfo := map[string]any{}

	return arrPrivateInfo
}

func (obj *ChannelInfoAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.ForumShopChannelInfoReq {
		return false
	}

	return true
}

func (obj *ChannelInfoAction) Execute() bool {
	if nil == obj {
		return false
	}
	errNo := shop.ChannelInfo(obj.Ctx, obj.ForumShopChannelInfoRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}

	return true
}

func (obj *ChannelInfoAction) BuildOut() {
	if nil == obj || nil == obj.ForumShopChannelInfoRes {
		return
	}
	if nil == obj.ForumShopChannelInfoRes.Error {
		// 默认赋值成功
		obj.ForumShopChannelInfoRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.ForumShopChannelInfoRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *ChannelInfoAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.ForumShopChannelInfoReq || nil == obj.ForumShopChannelInfoReq.Data {
		return
	}
}
