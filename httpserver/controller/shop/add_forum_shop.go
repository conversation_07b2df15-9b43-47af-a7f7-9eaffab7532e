package shop

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	client "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	goodsProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	service "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop"
)

// AddForumShop 入口函数
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：HTTP响应对象
func AddForumShop(ctx context.Context, req ghttp.Request) ghttp.Response {
	return AddForumShopActionInstance(ctx, req).UiFuncHandler()
}

// AddForumShopAction 服务相关结构体
type AddForumShopAction struct {
	*uiclient.UIBaseAction
	AddForumShopReq *goodsProto.AddForumShopReqIdl
	AddForumShopRes *goodsProto.AddForumShopResIdl
	baseData        *types.AddForumShopBaseData
}

// AddForumShopActionInstance 创建一个AddForumShopAction实例
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：req：HTTP请求对象
// @Return：返回AddForumShopAction实例
func AddForumShopActionInstance(ctx context.Context, req ghttp.Request) *AddForumShopAction {
	if ctx == nil || req == nil {
		return nil
	}
	obj := &AddForumShopAction{
		UIBaseAction:    &uiclient.UIBaseAction{},
		AddForumShopReq: &goodsProto.AddForumShopReqIdl{},
		AddForumShopRes: &goodsProto.AddForumShopResIdl{
			Error: &client.Error{},
		},
		baseData: &types.AddForumShopBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.AddForumShopReq) {
		tbcontext.FatalF(obj.Ctx, "UIBase init fail")
	}
	obj.baseData.BaseObj = obj.UIBaseAction
	return obj
}

// ParseParams 获取数据并赋值给baseData.Request
// @Param：无
// @Return：如果实例或相关字段为 nil，则函数直接返回
func (obj *AddForumShopAction) ParseParams() {
}

// GetPrivateInfo 设置私有信息
// @Param：无
// @Return：返回一个包含私有信息的map[string]any类型数据
func (obj *AddForumShopAction) GetPrivateInfo() map[string]any {
	if obj == nil || obj.ObjRequest == nil || obj.AddForumShopReq == nil || obj.AddForumShopReq.Data == nil {
		return nil
	}

	obj.ObjRequest.AddStrategy("check_sign", false)
	// goodsInfo := &client.ShopGoodsInfo{}

	obj.baseData.Request = obj.AddForumShopReq.GetData()

	privateInfo := map[string]any{}
	return privateInfo
}

// CheckPrivate 校验合法性
// @Param：无
// @Return：返回值是bool类型，表示执行是否合法
func (obj *AddForumShopAction) CheckPrivate() bool {
	if obj == nil || obj.ObjRequest == nil || obj.AddForumShopReq == nil {
		return false
	}
	return true
}

// BuildOut 构建输出数据的方法
// @Param：无
// @Return：无
func (obj *AddForumShopAction) BuildOut() {
	if obj == nil || obj.AddForumShopRes == nil {
		return
	}
	if obj.AddForumShopRes.Error == nil {
		obj.AddForumShopRes.Error = &client.Error{
			Errorno: proto.Int32(tiebaerror.ERR_SUCCESS),
			Errmsg:  proto.String(""),
			Usermsg: proto.String(""),
		}
	}
	return
}

// BuildLog 创建日志
// @Param：无
// @Return：无
func (obj *AddForumShopAction) BuildLog() {
	return
}

// Execute 执行AddForumShopAction的逻辑
// @Param：无
// @Return：返回值是bool类型，表示执行是否成功
func (obj *AddForumShopAction) Execute() bool {
	if obj == nil || obj.ObjRequest == nil || obj.AddForumShopReq == nil || obj.AddForumShopReq.Data == nil {
		return false
	}
	obj.baseData.UserID = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", ""), common.TTT_UINT64).(uint64)

	errno := service.AddShop(obj.Ctx, obj.baseData, obj.AddForumShopRes)
	if errno != tiebaerror.ERR_SUCCESS {
		obj.Error(errno, stcdefine.GetErrMsg(errno), true)
		return false
	}
	return true
}
