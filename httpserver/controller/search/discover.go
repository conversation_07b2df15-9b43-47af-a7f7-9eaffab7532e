package search

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/search/discover"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/search"
)

func Discover(ctx context.Context, req ghttp.Request) ghttp.Response {
	return DiscoverActionInstance(ctx, req).UiFuncHandler()
}

type DiscoverAction struct {
	*uiclient.UIBaseAction
	DiscoverReq *discover.DiscoverReqIdl
	DiscoverRes *discover.DiscoverResIdl
	global      *types.DiscoverBaseData
}

func DiscoverActionInstance(ctx context.Context, req ghttp.Request) *DiscoverAction {
	if nil == ctx || nil == req {
		return nil
	}
	obj := &DiscoverAction{
		UIBaseAction: &uiclient.UIBaseAction{},
		DiscoverReq:  &discover.DiscoverReqIdl{},
		DiscoverRes:  &discover.DiscoverResIdl{},
		global:       &types.DiscoverBaseData{},
	}
	if !obj.Init(ctx, req, obj, obj.DiscoverReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}
	obj.global.BaseObj = obj.UIBaseAction
	obj.DiscoverRes.Data = new(discover.DiscoverRes)

	return obj
}

func (obj *DiscoverAction) ParseParams() {

}

func (obj *DiscoverAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.DiscoverReq || nil == obj.DiscoverReq.Data {
		return nil
	}
	obj.global.Request = obj.DiscoverReq.GetData()
	scenes := obj.DiscoverReq.GetData().GetScenes()
	arrPrivateInfo := map[string]any{
		"scenes": scenes,
	}
	return arrPrivateInfo
}

func (obj *DiscoverAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.DiscoverReq {
		return false
	}
	return true
}

func (obj *DiscoverAction) Execute() bool {
	errNo := search.GetDiscoverInfo(obj.Ctx, obj.global, obj.DiscoverRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}
	return true
}

func (obj *DiscoverAction) BuildOut() {
	if nil == obj || nil == obj.DiscoverRes {
		return
	}
	if nil == obj.DiscoverRes.Error {
		// 默认赋值成功
		obj.DiscoverRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.DiscoverRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *DiscoverAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.DiscoverReq || nil == obj.DiscoverReq.Data {
		return
	}
}
