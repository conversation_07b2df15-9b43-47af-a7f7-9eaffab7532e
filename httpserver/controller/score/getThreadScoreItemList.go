package score

import (
	"context"
	"icode.baidu.com/baidu/gdp/ghttp"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/score/getThreadScoreItemList"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/score"
)

func GetThreadScoreItemList(ctx context.Context, req ghttp.Request) ghttp.Response {
	return getThreadScoreItemListActionInstance(ctx, req).UiFuncHandler()
}

type getThreadScoreItemListAction struct {
	*uiclient.UIBaseAction
	GetThreadScoreItemListReq *getThreadScoreItemList.GetThreadScoreItemListReqIdl
	GetThreadScoreItemListRes *getThreadScoreItemList.GetThreadScoreItemListResIdl
	global                    *types.GetThreadScoreItemListData
}

func getThreadScoreItemListActionInstance(ctx context.Context, req ghttp.Request) *getThreadScoreItemListAction {
	if nil == ctx || nil == req {
		return nil
	}

	obj := &getThreadScoreItemListAction{
		UIBaseAction:              &uiclient.UIBaseAction{},
		GetThreadScoreItemListReq: &getThreadScoreItemList.GetThreadScoreItemListReqIdl{},
		GetThreadScoreItemListRes: &getThreadScoreItemList.GetThreadScoreItemListResIdl{},
		global:                    &types.GetThreadScoreItemListData{},
	}

	if !obj.Init(ctx, req, obj, obj.GetThreadScoreItemListReq) {
		tbcontext.WarningF(obj.Ctx, "UIBase init fail")
	}

	obj.global.BaseObj = obj.UIBaseAction
	// obj.GetThreadScoreItemListReq.Data = new(getThreadScoreItemList.DataReq)
	obj.GetThreadScoreItemListRes.Data = new(getThreadScoreItemList.DataRes)
	tbcontext.NoticeF(ctx, "GetThreadScoreItemList start333, obj:%s", common.ToString(obj))
	return obj
}

func (obj *getThreadScoreItemListAction) ParseParams() {
	if nil == obj || nil == obj.GetThreadScoreItemListReq || nil == obj.GetThreadScoreItemListRes.Data || nil == obj.ObjRequest {
		tbcontext.WarningF(obj.Ctx, "ParseParams error")
		return
	}

	if obj.GetThreadScoreItemListReq.GetData().GetThreadId() == 0 {
		tbcontext.NoticeF(obj.Ctx, "params[thread_id] error")
		return
	}
	if obj.GetThreadScoreItemListReq.GetData().GetForumId() == 0 {
		tbcontext.NoticeF(obj.Ctx, "params[forum_id] error")
		return
	}
	if obj.GetThreadScoreItemListReq.GetData().GetPn() == 0 {
		tbcontext.NoticeF(obj.Ctx, "params[pn] error")
		return
	}
	if obj.GetThreadScoreItemListReq.GetData().GetRn() == 0 {
		tbcontext.NoticeF(obj.Ctx, "params[rn] error")
		return
	}

	obj.global.Request = obj.GetThreadScoreItemListReq.GetData()
}

func (obj *getThreadScoreItemListAction) GetPrivateInfo() map[string]any {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetThreadScoreItemListReq || nil == obj.GetThreadScoreItemListReq.Data {
		tbcontext.WarningF(obj.Ctx, "GetPrivateInfo error")
		return nil
	}

	// privateInfo := map[string]interface{}{
	// 	"check_login": true,
	// 	"need_login":  true,
	// }
	privateInfo := map[string]interface{}{}
	return privateInfo
}

func (obj *getThreadScoreItemListAction) CheckPrivate() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetThreadScoreItemListReq {
		tbcontext.WarningF(obj.Ctx, "CheckPrivate error")
		return false
	}

	return true
}

func (obj *getThreadScoreItemListAction) Execute() bool {
	if nil == obj || nil == obj.ObjRequest || nil == obj.GetThreadScoreItemListReq || nil == obj.GetThreadScoreItemListReq.Data {
		tbcontext.WarningF(obj.Ctx, "Execute error")
		return false
	}

	// 处理参数
	obj.global.UserID = common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", ""), common.TTT_UINT64).(uint64)
	obj.global.ThreadID = obj.GetThreadScoreItemListReq.GetData().GetThreadId()
	obj.global.Pn = obj.GetThreadScoreItemListReq.GetData().GetPn()
	obj.global.Rn = obj.GetThreadScoreItemListReq.GetData().GetRn()
	obj.global.SortType = obj.GetThreadScoreItemListReq.GetData().GetSortType()
	obj.global.NeedUserScore = obj.GetThreadScoreItemListReq.GetData().GetNeedUserScore()
	obj.global.NeedScoreUserNum = obj.GetThreadScoreItemListReq.GetData().GetNeedScoreUserNum()
	obj.global.ForumID = obj.GetThreadScoreItemListReq.GetData().GetForumId()
	// tbcontext.NoticeF(obj.Ctx, "obj.global.info:%s", common.ToString(obj.global))

	// 获取item_ids
	var itemIDs []uint64
	// 12.73版本开始，item_ids为空或解析失败均返回参数错误
	strClientVersion := common.Tvttt(obj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	if clientvers.CompareV2(strClientVersion, ">=", "12.73") {
		if obj.GetThreadScoreItemListReq.GetData().GetItemIds() != "" {
			tmpStr := strings.Split(obj.GetThreadScoreItemListReq.GetData().GetItemIds(), ",")
			for _, v := range tmpStr {
				if itemID, err := strconv.ParseUint(v, 10, 64); err == nil {
					itemIDs = append(itemIDs, itemID)
				} else {
					tbcontext.WarningF(obj.Ctx, "Execute error: client_version > 12.73,item_id is not uint64,item_ids:%s,item_id:%s",
						obj.GetThreadScoreItemListReq.GetData().GetItemIds(), v)
					obj.Error(tiebaerror.ERR_PARAM_ERROR, stcdefine.GetErrMsg(tiebaerror.ERR_PARAM_ERROR), true)
					return false
				}
			}
		}
		if len(itemIDs) == 0 {
			tbcontext.WarningF(obj.Ctx, "Execute error: client_version > 12.73,item_ids is empty,item_ids:%s",
				obj.GetThreadScoreItemListReq.GetData().GetItemIds())
			obj.Error(tiebaerror.ERR_PARAM_ERROR, stcdefine.GetErrMsg(tiebaerror.ERR_PARAM_ERROR), true)
			return false
		}
	}
	obj.global.ItemIds = itemIDs

	errNo := score.GetThreadScoreItemList(obj.Ctx, obj.global, obj.GetThreadScoreItemListRes)
	if errNo != tiebaerror.ERR_SUCCESS {
		obj.Error(errNo, stcdefine.GetErrMsg(errNo), true)
		return false
	}
	return true
}

func (obj *getThreadScoreItemListAction) BuildOut() {
	if nil == obj || nil == obj.GetThreadScoreItemListRes {
		return
	}

	if nil == obj.GetThreadScoreItemListRes.Error {
		// 默认赋值成功
		obj.GetThreadScoreItemListRes.Error = &client.Error{
			Errorno: common.GetInt32Ptr(tiebaerror.ERR_SUCCESS),
			Errmsg:  common.GetStringPtr(""),
			Usermsg: common.GetStringPtr(""),
		}
	}

	outData := map[string]any{
		"data": obj.GetThreadScoreItemListRes.GetData(),
	}
	obj.ObjResponse.SetOutData(outData)
}

func (obj *getThreadScoreItemListAction) BuildLog() {
	if nil == obj || nil == obj.Ctx || nil == obj.Req || nil == obj.GetThreadScoreItemListReq || nil == obj.GetThreadScoreItemListRes.Data {
		return
	}
}
