// Copyright(C) 2022 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2022-06-22, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package httpserver

import (
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/ghttp/pprof"
	"icode.baidu.com/baidu/gdp/metrics/gmetrics"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/clientmw"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/ubmwares"
	"icode.baidu.com/baidu/tieba-qa/traffic-middleware/v2/traffic"
	"icode.baidu.com/baidu/tieba-server-go/go-cache/metrics"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/activity"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/ad"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/aichat"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/bawu"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/dialog"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/drawthread"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/excellent"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/forum"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/gaokao"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/hottopic"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/inspiration"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/livetask"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/matchactivity"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/material"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/member"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/pb"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/publisher"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/question"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/questionbot"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/raffle"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/recommend"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/score"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/search"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/shop"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/sidebar"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/splashad"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/springfestival2024"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/sprite"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/task"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/threadalbum"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/video"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/httpserver/controller/widget"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
)

// httpRouter 获取web路由
//
// HTTP Server 和 Router 的文档：
// http://gdp.baidu-int.com/gdp2/docs/examples/server/20_http_server/
func httpRouter(ser *ghttp.DefaultServer) ghttp.Router {
	logger := resource.LoggerService

	// 若 HTTP Server 内部出现异常，将通过此 logger 打印日志
	ghttp.DefaultLogger = logger

	router := ghttp.NewRouter()
	router.SetLogger(logger)

	// 注册缓存组件路由
	metrics.Default().RegisterExportTo(router)

	// router.Use(ubmwares.GetUbDefaultServiceMiddlewares(ser.Ctx)...)

	// 启用引流中间件, 开启自运维扩展
	ctrl := traffic.NewController("tieba", "go-client-forum", logger, traffic.WithSelfOperation())
	if nil != ctrl {
		ctrl.EnableTrafficMiddleware(router)
		ctrl.EnableTrafficAPI(router)
	}

	// if ser.WriteTimeout > 0 {
	//	// 若 server 有设置 WriteTimeout，则整体加上超时控制
	//	// 这样：
	//	// 1.日志中可以打印出由于 server 超时导致的504异常
	//	// 2.业务逻辑可以更及时的终止运行
	//	router.Use(ghttp.NewTimeoutMiddleWareFunc(ser.WriteTimeout, nil))
	// }
	//
	// // 注册日志中间件，初始化日志功能，打印访问日志( access_log )
	// //  当前 logger 是使用配置文件 conf/logit/service.toml 初始化生成的
	// //  日志文件打印到了 {gdp.LogDir}/service/service.log
	// //  若需要对日志字段进行调整，请修改这里：
	// //  使用自定义的LogFields，不要使用 ghttp.DefaultServerLogFields
	// router.Use(ghttp.NewLogMiddleWareFunc(logger, ghttp.DefaultServerLogFields))
	//

	param := ubmwares.UbMwCustomParam{
		ReadTimeOut: ser.ReadTimeout,
	}
	router.Use(clientmw.GetCustomClientUiHandlerMw(ser.Ctx, param)...)

	// // 注册 panic Recover 中间件，可以处理 handlerFunc 里 出现的 panic
	// // 避免程序整体 panic
	// // 需要注意的是，若是使用 go xxx() 自己新开启的协程，是不能 recover 的
	// router.Use(ghttp.NewRecoverMiddleWareFunc(logger, nil))
	//
	// // 注册采集 HTTP Server 接口指标的中间件,建议放在 panic recover 之后
	router.Use(gmetrics.Default().CollectHTTPServer())

	// 注册路径为 /metrics，包括两个子路径，用于给 prometheus 采集：
	// /metrics/process  :  进程的指标信息
	// /metrics/service  :  RPC Server 和 RPC Client 的指标信息
	gmetrics.Default().RegisterExporterTo(router)

	// pprof
	registerPprof(router)

	router.HandleFunc("any", "/mo_client_forum/c/f/forum/queryBlockAndAppealInfo", forum.QueryBlockAndAppealInfo)
	router.HandleFunc("any", "/c/f/forum/queryBlockAndAppealInfo", forum.QueryBlockAndAppealInfo)
	router.HandleFunc("any", "/mo_client_forum/c/f/pb/picpage", pb.Picpage)
	router.HandleFunc("any", "/c/f/pb/picpage", pb.Picpage)
	// 楼中楼
	router.HandleFunc("any", "/mo_client_forum/c/f/pb/floor", pb.PbFloor)
	router.HandleFunc("any", "/c/f/pb/floor", pb.PbFloor)

	router.HandleFunc("any", "/c/f/sprite/getSpriteSpeech", sprite.GetSpriteSpeech)
	router.HandleFunc("any", "/mo_client_forum/c/f/sprite/getSpriteSpeech", sprite.GetSpriteSpeech)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/forumGuide", forum.ForumGuide)
	router.HandleFunc("any", "/c/f/forum/forumGuide", forum.ForumGuide)

	router.HandleFunc("any", "/mo_client_forum/c/f/forum/gethistoryforum", forum.HistoryForum)
	router.HandleFunc("any", "/c/f/forum/gethistoryforum", forum.HistoryForum)

	router.HandleFunc("any", "/mo_client_forum/c/f/forum/getRecommendForumData", forum.RecommendForumData)
	router.HandleFunc("any", "/c/f/forum/getRecommendForumData", forum.RecommendForumData)
	// 首页进吧回退出推荐吧
	router.HandleFunc("any", "/mo_client_forum/c/f/excellent/getBackRecommendForum", excellent.GetBackRecommendForum)
	router.HandleFunc("any", "/c/f/excellent/getBackRecommendForum", excellent.GetBackRecommendForum)
	// 视频号关注tab
	router.HandleFunc("any", "/mo_client_forum/c/f/video/concernTab", video.ConcernTab)
	router.HandleFunc("any", "/c/f/video/concernTab", video.ConcernTab)
	// 视频号红点
	router.HandleFunc("any", "/mo_client_forum/c/f/video/redIcon", video.RedIcon)
	router.HandleFunc("any", "/c/f/video/redIcon", video.RedIcon)
	// 视频卡片信息
	router.HandleFunc("any", "/mo_client_forum/c/f/video/cardInfo", video.CardInfo)
	router.HandleFunc("any", "/c/f/video/cardInfo", video.CardInfo)
	// frs页专区tab
	// frs页专区tab
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/itemPage", forum.ItemPage)
	router.HandleFunc("any", "/c/f/forum/itemPage", forum.ItemPage)
	// 小精灵底部预设问题
	router.HandleFunc("any", "/c/f/sprite/getSpriteBottomChat", sprite.GetSpriteBottomChat)
	router.HandleFunc("any", "/mo_client_forum/c/f/sprite/getSpriteBottomChat", sprite.GetSpriteBottomChat)
	// 小精灵点击后发消息能力
	router.HandleFunc("any", "/c/f/sprite/sendSpriteMsg", sprite.SendSpriteMsg)
	router.HandleFunc("any", "/mo_client_forum/c/f/sprite/sendSpriteMsg", sprite.SendSpriteMsg)
	// 大聪明聊天落地页
	router.HandleFunc("any", "/c/f/sprite/getSpriteChatDetail", sprite.GetSpriteChatDetail)
	router.HandleFunc("any", "/mo_client_forum/c/f/sprite/getSpriteChatDetail", sprite.GetSpriteChatDetail)
	// 大聪明贴子推荐
	router.HandleFunc("any", "/c/f/sprite/threadRecommend", sprite.ThreadRecommend)
	router.HandleFunc("any", "/mo_client_forum/c/f/sprite/threadRecommend", sprite.ThreadRecommend)
	// 新增查询签到状态的接口
	router.HandleFunc("any", "/c/f/forum/getUserSign", forum.GetUserSign)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/getUserSign", forum.GetUserSign)
	// AI机器人对话落地页
	router.HandleFunc("any", "/c/f/aichat/getChatDetail", aichat.GetChatDetail)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/getChatDetail", aichat.GetChatDetail)
	router.HandleFunc("any", "/c/f/aichat/getAichatBotInfo", aichat.GetBotInfo)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/getAichatBotInfo", aichat.GetBotInfo)
	// 获取随机表情
	router.HandleFunc("any", "/c/f/aichat/getRandomMemeInfo", aichat.GetRandomMemeInfo)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/getRandomMemeInfo", aichat.GetRandomMemeInfo)

	// 赛事竞猜数据接口
	router.HandleFunc("any", "/c/f/matchActivity/quiz", matchactivity.GetQuiz)
	router.HandleFunc("any", "/mo_client_forum/c/f/matchActivity/quiz", matchactivity.GetQuiz)
	// AIGC离线查询表情包接口
	router.HandleFunc("any", "/c/f/sprite/queryMeme", sprite.QueryMeme)
	router.HandleFunc("any", "/mo_client_forum/c/f/sprite/queryMeme", sprite.QueryMeme)
	// AIGC在线生成表情包结果查询接口
	router.HandleFunc("any", "/c/f/sprite/checkMemeStatus", sprite.CheckMemeStatus)
	router.HandleFunc("any", "/mo_client_forum/c/f/sprite/checkMemeStatus", sprite.CheckMemeStatus)
	// 停止发言接口
	router.HandleFunc("any", "/c/f/sprite/stopThinking", sprite.StopThinking)
	router.HandleFunc("any", "/mo_client_forum/c/f/sprite/stopThinking", sprite.StopThinking)
	// AI机器人对话落地页
	router.HandleFunc("any", "/c/f/aichat/getMyChatBots", aichat.GetMyChatBots)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/getMyChatBots", aichat.GetMyChatBots)
	// AI机器人广场页
	router.HandleFunc("any", "/c/f/aichat/getChatSquare", aichat.GetChatSquare)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/getChatSquare", aichat.GetChatSquare)
	// 推荐卡片页
	router.HandleFunc("any", "/c/f/recommend/newRecommendCard", recommend.NewRecommendCard)
	router.HandleFunc("any", "/mo_client_forum/c/f/recommend/newRecommendCard", recommend.NewRecommendCard)
	// AI机器人互动游戏
	router.HandleFunc("any", "/c/f/aichat/getGameProcess", aichat.GetGameProcess)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/getGameProcess", aichat.GetGameProcess)
	router.HandleFunc("any", "/c/f/aichat/restartGame", aichat.RestartGame)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/restartGame", aichat.RestartGame)
	router.HandleFunc("any", "/c/f/aichat/gameOver", aichat.GameOver)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/gameOver", aichat.GameOver)
	router.HandleFunc("any", "/c/f/aichat/getGameListDetail", aichat.GetGameListDetail)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/getGameListDetail", aichat.GetGameListDetail)
	router.HandleFunc("any", "/c/f/aichat/getUserBotSet", aichat.GetUserBotSet)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/getUserBotSet", aichat.GetUserBotSet)

	// 影响力榜单
	router.HandleFunc("any", "/c/f/forum/influenceRank", forum.InfluenceRank)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/influenceRank", forum.InfluenceRank)

	router.HandleFunc("any", "/c/f/forum/influenceRank_shoubai_ugc", forum.InfluenceRank)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/influenceRank_shoubai_ugc", forum.InfluenceRank)

	// 2024春节活动-祈愿灯
	router.HandleFunc("any", "/c/f/springfestival2024/getRandomBlessings", springfestival2024.GetRandomBlessings)
	router.HandleFunc(
		"any", "/mo_client_forum/c/f/springfestival2024/getRandomBlessings", springfestival2024.GetRandomBlessings,
	)
	router.HandleFunc(
		"any", "/c/f/springfestival2024/getRandomBackgroundImg", springfestival2024.GetRandomBackgroundImg,
	)
	router.HandleFunc(
		"any", "/mo_client_forum/c/f/springfestival2024/getRandomBackgroundImg",
		springfestival2024.GetRandomBackgroundImg,
	)

	// 2024春节活动出卡
	router.HandleFunc("any", "/c/f/raffle/showPrize", raffle.ShowPrize)
	router.HandleFunc("any", "/mo_client_forum/c/f/raffle/showPrize", raffle.ShowPrize)

	// 会员弹窗
	router.HandleFunc("any", "/c/f/member/getMemberPopups", member.Popups)
	router.HandleFunc("any", "/mo_client_forum/c/f/member/getMemberPopups", member.Popups)

	// 通用弹窗
	router.HandleFunc("any", "/c/f/dialog/showDialog", dialog.ShowDialog)
	router.HandleFunc("any", "/mo_client_forum/c/f/dialog/showDialog", dialog.ShowDialog)

	// 搜索发现轮播数据下发
	router.HandleFunc("any", "/c/f/search/discover", search.Discover)
	router.HandleFunc("any", "/mo_client_forum/c/f/search/discover", search.Discover)

	// 进吧页获取推荐吧单列表接口
	router.HandleFunc("any", "/c/f/forum/getForumAlbum", forum.GetForumAlbum)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/getForumAlbum", forum.GetForumAlbum)
	// 进吧页吧单列表翻页接口
	router.HandleFunc("any", "/c/f/forum/getForumAlbumMore", forum.GetForumAlbumMore)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/getForumAlbumMore", forum.GetForumAlbumMore)

	// 首页侧边栏接口
	router.HandleFunc("any", "/c/f/sidebar/home", sidebar.Home)
	router.HandleFunc("any", "/mo_client_forum/c/f/sidebar/home", sidebar.Home)

	// 创作灵感列表页
	router.HandleFunc("any", "/c/f/inspiration/getInspirationList", inspiration.GetInspirationList)
	router.HandleFunc("any", "/mo_client_forum/c/f/inspiration/getInspirationList", inspiration.GetInspirationList)
	// 建吧成功frs页吧友关心的话题数据
	router.HandleFunc("any", "/c/f/frs/getRecomTopic", forum.GetRecomTopic)
	router.HandleFunc("any", "/mo_client_forum/c/f/frs/getRecomTopic", forum.GetRecomTopic)

	// 2024高考相关
	router.HandleFunc("any", "/c/f/gaokao/warmup", gaokao.Warmup)
	router.HandleFunc("any", "/mo_client_forum/c/f/gaokao/warmup", gaokao.Warmup)
	// 【12.61】高考考前-认证方式-吧主推荐-吧主推荐H5接口
	router.HandleFunc("any", "/c/f/gaokao/showAddStuAuth", gaokao.ShowAddStuAuth)
	router.HandleFunc("any", "/mo_client_forum/c/f/gaokao/showAddStuAuth", gaokao.ShowAddStuAuth)

	// 搜索智能体
	router.HandleFunc("any", "/c/f/aichat/searchAgent", aichat.SearchAgent)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/searchAgent", aichat.SearchAgent)
	// agent列表
	router.HandleFunc("any", "/c/f/aichat/agentList", aichat.GetAgentList)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/agentList", aichat.GetAgentList)
	// agent会话落地页
	router.HandleFunc("any", "/c/f/aichat/agentDetail", aichat.GetAgentDetail)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/agentDetail", aichat.GetAgentDetail)
	// agent获取历史消息
	router.HandleFunc("any", "/c/f/aichat/getAgentHistoryMsg", aichat.GetAgentHistory)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/getAgentHistoryMsg", aichat.GetAgentHistory)

	router.HandleFunc("any", "/c/f/aichat/squareAgentList", aichat.SquareAgentList)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/squareAgentList", aichat.SquareAgentList)

	router.HandleFunc("any", "/c/f/task/commitTask", task.CommitTask)
	router.HandleFunc("any", "/mo_client_forum/c/f/task/commitTask", task.CommitTask)

	router.HandleFunc("any", "/c/f/activity/joinLottery", activity.JoinLottery)
	router.HandleFunc("any", "/mo_client_forum/c/f/activity/joinLottery", activity.JoinLottery)

	// 【12.64】话题详情页改造
	router.HandleFunc("any", "/c/f/hottopic/topicDetail", hottopic.TopicDetail)
	router.HandleFunc("any", "/mo_client_forum/c/f/hottopic/topicDetail", hottopic.TopicDetail)

	// 获取发布器配置
	router.HandleFunc("any", "/c/f/publisher/conf", publisher.GetPublisherConf)
	router.HandleFunc("any", "/mo_client_forum/c/f/publisher/conf", publisher.GetPublisherConf)

	// agent置顶和删除
	router.HandleFunc("any", "/c/f/aichat/handleTabStatus", aichat.HandleTabStatus)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/handleTabStatus", aichat.HandleTabStatus)

	// [12.68]打分贴
	router.HandleFunc("any", "/c/f/score/getThreadScoreItemList", score.GetThreadScoreItemList)
	router.HandleFunc("any", "/mo_client_forum/c/f/score/getThreadScoreItemList", score.GetThreadScoreItemList)

	// 合辑管理落地页
	router.HandleFunc("any", "/c/f/threadalbum/getAlbumListForManager", threadalbum.GetAlbumListForManager)
	router.HandleFunc(
		"any", "/mo_client_forum/c/f/threadalbum/getAlbumListForManager", threadalbum.GetAlbumListForManager,
	)
	// 翻卡中奖
	router.HandleFunc("any", "/c/f/drawthread/getLotteryResult", drawthread.GetLotteryResult)
	router.HandleFunc("any", "/mo_client_forum/c/f/drawthread/getLotteryResult", drawthread.GetLotteryResult)
	// 添加帖子落地页
	router.HandleFunc("any", "/c/f/threadalbum/getThreadListForAdd", threadalbum.GetThreadListForAdd)
	router.HandleFunc("any", "/mo_client_forum/c/f/threadalbum/getThreadListForAdd", threadalbum.GetThreadListForAdd)
	// [12.70]吧合集验证标题
	router.HandleFunc("any", "/c/f/threadalbum/checkAlbumName", threadalbum.CheckAlbumName)
	router.HandleFunc("any", "/mo_client_forum/c/f/threadalbum/checkAlbumName", threadalbum.CheckAlbumName)

	router.HandleFunc("any", "/mo_client_forum/c/f/forum/getShareInfo", forum.GetShareInfo)
	router.HandleFunc("any", "/c/f/forum/getShareInfo", forum.GetShareInfo)

	router.HandleFunc("any", "/mo_client_forum/c/f/drawthread/publicity", drawthread.Publicity)
	router.HandleFunc("any", "/c/f/drawthread/publicity", drawthread.Publicity)

	router.HandleFunc("any", "/mo_client_forum/c/f/drawthread/myDrawList", drawthread.MyDrawList)
	router.HandleFunc("any", "/c/f/drawthread/myDrawList", drawthread.MyDrawList)

	router.HandleFunc("any", "/mo_client_forum/c/f/aigc/queryGenerateImage", aichat.GenerateImageQuery)
	router.HandleFunc("any", "/c/f/aigc/queryGenerateImage", aichat.GenerateImageQuery)

	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/agentInteractMsg", aichat.AgentInteractMsg)
	router.HandleFunc("any", "/c/f/aichat/agentInteractMsg", aichat.AgentInteractMsg)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/myAgent", aichat.MyAgent)
	router.HandleFunc("any", "/c/f/aichat/myAgent", aichat.MyAgent)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/userAgent", aichat.UserAgent)
	router.HandleFunc("any", "/c/f/aichat/userAgent", aichat.UserAgent)
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/getBotDraft", aichat.GetBotDraft)
	router.HandleFunc("any", "/c/f/aichat/getBotDraft", aichat.GetBotDraft)

	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/getUserRelationAgent", aichat.GetUserRelationAgent)
	router.HandleFunc("any", "/c/f/aichat/getUserRelationAgent", aichat.GetUserRelationAgent)

	// 赏金互助指南
	router.HandleFunc("any", "/mo_client_forum/c/f/question/guide", question.Guide)
	router.HandleFunc("any", "/c/f/question/guide", question.Guide)

	// 问答邀请+赏金互助
	router.HandleFunc("any", "/mo_client_forum/c/f/question/getQuestionList", question.GetQuestionList)
	router.HandleFunc("any", "/c/f/question/getQuestionList", question.GetQuestionList)

	// 取消评论头条
	router.HandleFunc("any", "/c/f/liveTask/cancel", livetask.Cancel)
	router.HandleFunc("any", "/mo_client_forum/c/f/liveTask/cancel", livetask.Cancel)
	// 评论头条负反馈
	router.HandleFunc("any", "/c/f/liveTask/feedback", livetask.Feedback)
	router.HandleFunc("any", "/mo_client_forum/c/f/liveTask/feedback", livetask.Feedback)
	// 吧务删帖理由浮层接口
	router.HandleFunc("any", "/mo_client_forum/c/f/bawu/getDeleteReason", bawu.GetDeleteReason)
	router.HandleFunc("any", "/c/f/bawu/getDeleteReason", bawu.GetDeleteReason)
	// 删帖详情页
	router.HandleFunc("any", "/mo_client_forum/c/f/bawu/getDelThreadInfo", bawu.GetDelThreadInfo)
	router.HandleFunc("any", "/c/f/bawu/getDelThreadInfo", bawu.GetDelThreadInfo)

	// 任务小组件
	router.HandleFunc("any", "/mo_client_forum/c/f/widget/getUserTaskInfo", widget.GetUserTaskInfo)
	router.HandleFunc("any", "/c/f/widget/getUserTaskInfo", widget.GetUserTaskInfo)
	// 快捷进吧小组件
	router.HandleFunc("any", "/mo_client_forum/c/f/widget/getForumInfo", widget.GetForumInfo)
	router.HandleFunc("any", "/c/f/widget/getForumInfo", widget.GetForumInfo)
	// 创建头条贴
	router.HandleFunc("any", "/mo_client_forum/c/f/liveTask/create", livetask.Create)
	router.HandleFunc("any", "/c/f/liveTask/create", livetask.Create)
	// 开屏广告
	router.HandleFunc("any", "/mo_client_forum/c/f/ad/getSplashAd", splashad.GetSplashAd)
	router.HandleFunc("any", "/c/f/ad/getSplashAd", splashad.GetSplashAd)

	// 消息tab智能体数据热更新
	router.HandleFunc("any", "/mo_client_forum/c/f/aichat/fetchAiBot", aichat.FetchAiBot)
	router.HandleFunc("any", "/c/f/aichat/fetchAiBot", aichat.FetchAiBot)

	// 消息tab智能体数据热更新
	router.HandleFunc("any", "/mo_client_forum/c/f/ad/getFeedAd", ad.FeedAd)
	router.HandleFunc("any", "/c/f/ad/getFeedAd", ad.FeedAd)

	// 有料tab主页
	router.HandleFunc("any", "/mo_client_forum/c/f/material/home", material.Home)
	router.HandleFunc("any", "/c/f/material/home", material.Home)
	router.HandleFunc("any", "/mo_client_forum/c/f/material/threadbang", material.ThreadBang)
	router.HandleFunc("any", "/c/f/material/threadbang", material.ThreadBang)
	// 吧榜单二级页
	router.HandleFunc("any", "/mo_client_forum/c/f/material/forumbang", material.Bang)
	router.HandleFunc("any", "/c/f/material/forumbang", material.Bang)

	router.HandleFunc("any", "/c/f/forum/forumShop", shop.ForumShop)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/forumShop", shop.ForumShop)

	router.HandleFunc("any", "/c/f/forum/getForumShopDataOverview", shop.GetForumShopDataOverview)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/getForumShopDataOverview", shop.GetForumShopDataOverview)

	router.HandleFunc("any", "/c/f/forum/getForumShopDataTrend", shop.GetForumShopDataTrend)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/getForumShopDataTrend", shop.GetForumShopDataTrend)

	router.HandleFunc("any", "/c/f/forum/getForumShopDataSummary", shop.GetForumShopDataSummary)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/getForumShopDataSummary", shop.GetForumShopDataSummary)

	// 圈子电商 - 更新商品信息
	router.HandleFunc("any", "/c/f/forum/updateForumShopGoods", shop.UpdateForumShopGoods)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/updateForumShopGoods", shop.UpdateForumShopGoods)

	router.HandleFunc("any", "/c/f/forum/forumShopGoodsList", shop.ForumShopGoodsList)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/forumShopGoodsList", shop.ForumShopGoodsList)

	router.HandleFunc("any", "/c/f/forum/getForumShopChannelInfo", shop.ChannelInfo)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/getForumShopChannelInfo", shop.ChannelInfo)

	router.HandleFunc("any", "/c/f/forum/convertForumShopLink", shop.ConvertURL)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/convertForumShopLink", shop.ConvertURL)

	router.HandleFunc("any", "/c/f/forum/getForumShopManual", shop.Manual)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/getForumShopManual", shop.Manual)

	router.HandleFunc("any", "/c/f/forum/getForumShopDetail", shop.GetForumShopDetail)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/getForumShopDetail", shop.GetForumShopDetail)

	router.HandleFunc("any", "/c/f/forum/addForumShopGoods", shop.AddForumShopGoods)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/addForumShopGoods", shop.AddForumShopGoods)

	router.HandleFunc("any", "/c/f/forum/addForumShop", shop.AddForumShop)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/addForumShop", shop.AddForumShop)

	router.HandleFunc("any", "/c/f/questionbot/quoteThreadList", questionbot.QuoteThreadList)
	router.HandleFunc("any", "/mo_client_forum/c/f/questionbot/quoteThreadList", questionbot.QuoteThreadList)

	router.HandleFunc("any", "/c/f/forum/getForumLevelDetail", forum.GetForumLevelDetail)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/getForumLevelDetail", forum.GetForumLevelDetail)

	router.HandleFunc("any", "/c/f/forum/getBazhuProfitDetail", forum.GetBazhuProfitDetail)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/getBazhuProfitDetail", forum.GetBazhuProfitDetail)

	router.HandleFunc("any", "/c/f/forum/shopGoodsFeed", shop.ShopGoodsFeed)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/shopGoodsFeed", shop.ShopGoodsFeed)

	router.HandleFunc("any", "/c/f/forum/searchShopGoods", shop.Search)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/searchShopGoods", shop.Search)

	router.HandleFunc("any", "/c/f/forum/shopExam", shop.ShopExam)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/shopExam", shop.ShopExam)

	router.HandleFunc("any", "/c/f/forum/searchShopGoodsRecommend", shop.SearchShopGoodsRecommend)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/searchShopGoodsRecommend", shop.SearchShopGoodsRecommend)

	router.HandleFunc("any", "/c/f/forum/shopIntroduce", shop.ShopIntroduce)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/shopIntroduce", shop.ShopIntroduce)

	router.HandleFunc("any", "/c/f/forum/searchForumByTag", forum.SearchForumByTag)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/searchForumByTag", forum.SearchForumByTag)

	//小卖部提交页面
	router.HandleFunc("any", "/c/f/forum/shopSubmitConf", shop.ShopSubmitConf)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/shopSubmitConf", shop.ShopSubmitConf)

	// 卸载挽留页
	router.HandleFunc("any", "/c/f/forum/uninstallLeftInfo", forum.UninstallLeftInfo)
	router.HandleFunc("any", "/mo_client_forum/c/f/forum/uninstallLeftInfo", forum.UninstallLeftInfo)

	// 视频沉浸态
	router.HandleFunc("any", "/c/f/video/getVideoPageBaseInfo", video.GetVideoPageBaseInfo)
	router.HandleFunc("any", "/mo_client_forum/c/f/video/getVideoPageBaseInfo", video.GetVideoPageBaseInfo)
	return router
}

// registerPanel 注册 /debug/panel/，提供了可视化的 UI 让我们可以更方便的查看、观察应用的状态信息
// see http://gdp.baidu-int.com/gdp2/docs/examples/server/26_observer_server/
// func registerPanel(router ghttp.Router) {
//	// 会使用配置文件 /conf/gdp/panel/panel.toml
//	ps := panel.NewDefaultServer(panel.MustAutoLoadConfig())
//	// 默认已限制了不能通过外网访问
//	// 请注意该页面不可通过其他方式被外网访问（如其他内网代理）
//	ps.RegisterToRouter(router, "/debug/panel/")
// }

// registerPprof 注册/debug/pprof 的handler 用于性能分析
//
//	引入的pprof，会给标准库的DefaultServeMux 都注册上/debug/xxx系统的handler
//	开启http pprof, 该功能依赖server 配置一个比较大的WriteTimeout
//	需要 WriteTimeout >30s
func registerPprof(router ghttp.Router) {
	// if env.RunMode() == env.RunModeRelease {
	//	return
	// }

	// 访问地址 http://ip:port/debug/pprof/
	// 千万注意不要让外网可以访问
	pprof.RegisterPProf(router, ghttp.NewInternalIPMiddleWareFunc())
}
