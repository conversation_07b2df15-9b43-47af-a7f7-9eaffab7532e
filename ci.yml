# generated by gdp new: https://gdp.baidu-int.com/gdp3/docs/quickstart/gdp/
# 百度 Go 编译环境使用指南 https://ku.baidu-int.com/d/SzGt0sD37hWmmp
Global:
  version: 2.0
  group_email: <EMAIL>   # 请修改，配置团队邮箱地址

Default:
  profile : [amd64_build] # 若需要部署到 ARM 环境，请在此添加上 arm64_build,并将 adjustArtifacts 的注释去掉
#    adjustArtifacts:
#      enable: true
#      storeStrategy: SEPARATE
#      artifactsProfile: [ amd64_build ]

Profiles:
  - profile:
    name : amd64_build
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      resourceType: MEDIUM
      tools:   # <------ 配置软件版本信息
        - go: 1.22.latest
        - gcc: 12
    build:
      command: make -f Makefile
      cache:                  # http://buildcloud.baidu.com/bcloud/9-bcloud_subcmd#9.15-cache
        enable: true          # 删除模块全部缓存：bcloud cache --delete --repo=baidu/tieba-server-user-base/go-client-commit
        trimeThresholdSize: 3 # 缓存目录大小，3 GB
      #          paths:               # paths 为空时，会使用编译系统默认的缓存目录
      #            - cache
    artifacts:
      release: true

  - profile:
    name : arm64_build
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      resourceType: MEDIUM
      tools:   # <------ 配置软件版本信息
        - go: 1.22.latest
        - gcc: 12
    build:
      command: export GOOS=linux; export GOARCH=arm64; make -f Makefile
      cache:
        enable: true
        trimeThresholdSize: 3
      #          paths:
      #            - cache
    artifacts:
      release: true
      platform:
        arch: AARCH64   # (必填) 产出对应架构，可选值为X86_64、AARCH64、SW_64
        os: LINUX      # (必填) 产出对应操作系统，可选值为LINUX、WINDOWS、DARWIN
