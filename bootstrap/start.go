// Copyright(C) 2022 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2022-03-07, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package bootstrap

import (
	"context"

	"icode.baidu.com/baidu/gdp/extension/gtask"
)

// Start 启动应用,同步的
func Start(ctx context.Context, cfg *Config) error {
	defer InvokeBeforeShutdown()

	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	var g gtask.Group
	ctx = g.WithContext(ctx)

	g.Go(func() error {
		return StartServers(ctx, cfg)
	})

	_, err := g.Wait()
	return err
}