// Copyright(C) 2022 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2022-06-22, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package bootstrap

import (
	"context"
	"log"
	"path/filepath"
	"time"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/extension/observer"
	"icode.baidu.com/baidu/gdp/hestia/starter"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/metrics/gmetrics"
	"icode.baidu.com/baidu/gdp/mysql"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/gdp/net/servicer"
	"icode.baidu.com/baidu/gdp/paas"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/tieba-server-go/go-cache"
	cacheCache "icode.baidu.com/baidu/tieba-server-go/go-cache/cache"
	cacheRedis "icode.baidu.com/baidu/tieba-server-go/go-cache/redis"
	gdp2 "icode.baidu.com/baidu/tieba-server-go/golib2/app"
	golibExgraph "icode.baidu.com/baidu/tieba-server-go/golib2/exgraph"
	loggerResource "icode.baidu.com/baidu/tieba-server-go/golib2/resources/logger"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
)

func init() {
	log.SetFlags(log.LstdFlags | log.Lshortfile | log.Lmicroseconds)
}

// MustInit 组件初始化，若失败，会panic
func MustInit(ctx context.Context) {
	hookStd()

	cache.Init() // 增加此行,初始化cache环境

	// 初始化指标统计组件
	// 会自动的给 ral、mysql、redis 等 rpc client 注册 用于采集指标的 interceptor
	gmetrics.Default().Init()

	initForPaas()

	initLoggers(ctx)

	initRal(ctx)

	loadServicer(ctx)

	initMySQL(ctx)
	//初始化redis
	initRedis(ctx)

	initObserver()

	initEngine()
}

// 重定向 stderr 和 stdout 到指定文件
// stderr 输出到 log/std/stderr.log
// stdout 输出到 log/std/stdout.log
// 会使用配置文件:  conf/logit/stderr.toml 和 conf/logit/stdout.toml
// 若配置文件不存在会使用默认配置
func hookStd() {
	sh := &logit.StdHooker{}
	sh.HookStd()
}

func initForPaas() {
	// 加载潘多拉的 port.conf 配置文件 http://wiki.baidu.com/pages/viewpage.action?pageId=1134316246
	// 会将端口信息写入环境变量，方便后续逻辑使用
	// 若应用部署在 其他平台，可以将该逻辑注释掉
	paas.MustExportPandoraPortConfToOsEnv(filepath.Join(env.ConfDir(), "port.conf"))
}

// initObserver 组件状态的观察、状态信息的导出(问题排查使用)
//
//	每 30 秒会将注册到 servicer.DefaultMapper 里的所有信息导出到 data/observer_dump/ 目录
//	比如一个 servicer 的可用机器列表，使用的 BNS 配置，最终生效的配置等信息
//	都可以从 dump 出的文件里查看到
func initObserver() {
	observer.Add("servicer_mapper", servicer.DefaultMapper)
	df := &observer.FileDumper{
		Duration: 30 * time.Second,
		Dir:      filepath.Join(env.DataDir(), "observer_dump"),
	}
	df.MustStart()
}

// initEngine 初始化图引擎执行对象
func initEngine() {
	resource.AgentInteractMsgEngine = golibExgraph.MustInitOneEngine("agent_interact_msg")
	resource.MyAgentEngine = golibExgraph.MustInitOneEngine("my_agent")
	resource.UserAgentEngine = golibExgraph.MustInitOneEngine("user_agent")
	resource.GetUserRelationAgentEngine = golibExgraph.MustInitOneEngine("get_user_relation_agent")
	resource.GetForumLevelDetailEngine = golibExgraph.MustInitOneEngine("get_forum_level_detail")
	resource.GetBazhuProfitDetailEngine = golibExgraph.MustInitOneEngine("get_bazhu_profit_detail")
	resource.ForumShopGoodsFeedEngine = golibExgraph.MustInitOneEngine("forum_shop_goods_feed")
	resource.ForumShopGoodsSearchEngine = golibExgraph.MustInitOneEngine("forum_shop_goods_search")
	resource.ForumShopExamEngine = golibExgraph.MustInitOneEngine("forum_shop_exam")
	resource.SearchShopGoodsRecommendEngine = golibExgraph.MustInitOneEngine("search_shop_goods_recommend")
	resource.SearchForumByTag = golibExgraph.MustInitOneEngine("search_forum_by_tag")
	resource.ForumShopIntroduceEngine = golibExgraph.MustInitOneEngine("forum_shop_introduce")
	resource.GetVideoPageBaseInfoEngine = golibExgraph.MustInitOneEngine("get_video_page_base_info")
	resource.ShopSubmitConfEngine = golibExgraph.MustInitOneEngine("forum_shop_submit_conf")
	resource.UninstallLeftInfoEngine = golibExgraph.MustInitOneEngine("uninstall_left_info")
}

// initLoggers 初始化logger
// http://gdp.baidu-int.com/gdp2/docs/examples/foundation/12_log/
func initLoggers(ctx context.Context) {
	{
		webLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/service.toml"))
		if err != nil {
			panic(err.Error())
		}
		serviceLoggerConfig, err := logit.LoadConfig("logit/service.toml")
		if err != nil {
			panic(err.Error())
		}
		resource.LoggerService = webLogger
		gdp2.SetServiceLoggerConfig(serviceLoggerConfig)
		loggerResource.SetLoggerService(webLogger)
		starter.BeforeShutdown(webLogger) // 注册 closer,这样可以保证程序进程退出前，日志全部落盘
	}
}

// initRal 初始化 RAL 组件
// http://gdp.baidu-int.com/gdp2/docs/examples/client/31_ral/
func initRal(ctx context.Context) {
	ral.InitDefault(ctx)
}

// loadServicer 加载服务配置
// http://gdp.baidu-int.com/gdp2/docs/examples/client/30_servicer/
func loadServicer(ctx context.Context) {
	pattern := filepath.Join(env.ConfDir(), "servicer", "*.toml")
	servicer.MustLoad(ctx, servicer.LoadOptFilesGlob(pattern, false))
}

// initMySQL 初始化 MySQL Client
// http://gdp.baidu-int.com/gdp2/docs/examples/client/32_mysql/
func initMySQL(_ context.Context) {

	// client 初始化为单例，放到 resource 里去
	// resource.MySQLClientUser=mustInitOneMySQL("ddbs_user")
}

func mustInitOneMySQL(name interface{}) mysql.Client {
	opts := []mysql.ClientOption{
		mysql.OptObserver(mysql.NewMetricsObserverFunc(nil)),

		// MySQL Client 在创建时，默认包含了 LogObserver
		// 定义在 mysql.DefaultObserverFuncs
	}
	client, err := mysql.NewClient(name, opts...)
	// 理论上，只有配置错误，才有可能返回err
	// 如配置不存在，配置内容错误
	if err != nil {
		panic(err.Error())
	}
	return client
}

// initRedis 初始化 Redis Client
//
//	http://gdp.baidu-int.com/gdp2/docs/examples/client/33_redis/
//	不要每次使用的时候创建一个新的 Client：
//	性能会更差，若未 Close 还可能造成协程泄露
func initRedis(_ context.Context) {

	// client 初始化为单例，放到 resource 里去
	resource.RedisSign = mustInitOneRedis("Redis_forum_sign")
	resource.CacheSign = initCache(resource.RedisSign)
	resource.RedisUserGrowth = mustInitOneRedis("Redis_forum_usergrowth")
	resource.CacheUserGrowth = initCache(resource.RedisUserGrowth)
	resource.RedisMsglogic = mustInitOneRedis("Redis_forum_msglogic")
	resource.RedisAdsense = mustInitOneRedis("Redis_forum_adsense")
	resource.RedisCommonb = mustInitOneRedis("Redis_forum_commonb")
	resource.CacheMsglogic = initCache(resource.RedisMsglogic)
	resource.RedisTwLive = mustInitOneRedis("Redis_forum_twlive")
	resource.CacheTwLive = initCache(resource.RedisTwLive)
	resource.RedisPush = mustInitOneRedis("Redis_push")
	resource.CachePush = initCache(resource.RedisPush)
	resource.RedisMember = mustInitOneRedis("Redis_member")
	resource.CacheMember = initCache(resource.RedisMember)
}

func initCache(cli redis.Client) cacheCache.Cache {
	opts := []cacheRedis.Option{}
	cacheCli := cache.NewCacheWithRedis(cli, opts...) // 创建缓存实例
	return cacheCli                                   // 缓存实例保存为单例，业务中操作缓存使用
}

func mustInitOneRedis(name string) redis.Client {
	opts := []redis.ClientOption{
		redis.OptHooker(redis.NewMetricsHook(nil)),
		redis.OptHooker(redis.NewLogHook()), // 打印 ral-worker.log
	}

	client, err := redis.NewClient(name, opts...)
	// 理论上，只有配置错误，才有可能返回err
	// 如配置不存在，配置内容错误
	if err != nil {
		panic(err.Error())
	}
	return client
}

// InvokeBeforeShutdown 退出前执行，资源清理、日志落盘等
func InvokeBeforeShutdown() {
	starter.InvokeBeforeShutdown()
}
