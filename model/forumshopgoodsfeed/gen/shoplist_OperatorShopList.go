// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	shoplist "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/shoplist"
)

var _ = errors.New("")

// OperatorShopListWrap OperatorShopListWrap
type OperatorShopListWrap shoplist.OperatorShopList

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorShopListWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.PrivateParams = nil
		op.CommonParams = nil
		op.ShopList = nil
	}()

	ok0 := ctx.MutableInstance(&op.PrivateParams)
	// op.PrivateParams canLost="false"
	if !ok0 {
		return errors.New("shoplist.OperatorShopList.PrivateParams assign fail, type:privateparams.PrivateParams")
	}
	// op.PrivateParams canNil="false"
	if op.PrivateParams == nil {
		return errors.New("shoplist.OperatorShopList.PrivateParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok1 {
		return errors.New("shoplist.OperatorShopList.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("shoplist.OperatorShopList.CommonParams is nil")
	}

	opErr := ((*shoplist.OperatorShopList)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.ShopList canNil="false"
	if op.ShopList == nil {
		return errors.New("shoplist.OperatorShopList register shoplist.ShopList error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.ShopList); err != nil {
		return errors.New("shoplist.OperatorShopList register shoplist.ShopList error:" + err.Error())
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("shop_goods_feed_shoplist", func() engine.Job {
		return &OperatorShopListWrap{}
	})
	if err != nil {
		panic(err)
	}
}
