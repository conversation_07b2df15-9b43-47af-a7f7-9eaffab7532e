// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	banner "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/banner"
)

var _ = errors.New("")

// OperatorBannerWrap OperatorBannerWrap
type OperatorBannerWrap banner.OperatorBanner

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorBannerWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.PrivateParams = nil
		op.CommonParams = nil
		op.Banner = nil
	}()

	ok0 := ctx.MutableInstance(&op.PrivateParams)
	// op.PrivateParams canLost="false"
	if !ok0 {
		return errors.New("banner.OperatorBanner.PrivateParams assign fail, type:privateparams.PrivateParams")
	}
	// op.PrivateParams canNil="false"
	if op.PrivateParams == nil {
		return errors.New("banner.OperatorBanner.PrivateParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok1 {
		return errors.New("banner.OperatorBanner.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("banner.OperatorBanner.CommonParams is nil")
	}

	opErr := ((*banner.OperatorBanner)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.Banner canNil="false"
	if op.Banner == nil {
		return errors.New("banner.OperatorBanner register banner.Banner error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.Banner); err != nil {
		return errors.New("banner.OperatorBanner register banner.Banner error:" + err.Error())
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("shop_goods_feed_banner", func() engine.Job {
		return &OperatorBannerWrap{}
	})
	if err != nil {
		panic(err)
	}
}
