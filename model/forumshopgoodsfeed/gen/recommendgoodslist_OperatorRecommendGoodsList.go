// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	recommendgoodslist "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/recommendgoodslist"
)

var _ = errors.New("")

// OperatorRecommendGoodsListWrap OperatorRecommendGoodsListWrap
type OperatorRecommendGoodsListWrap recommendgoodslist.OperatorRecommendGoodsList

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorRecommendGoodsListWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.PrivateParams = nil
		op.CommonParams = nil
		op.RecommendGoodsList = nil
	}()

	ok0 := ctx.MutableInstance(&op.PrivateParams)
	// op.PrivateParams canLost="false"
	if !ok0 {
		return errors.New("recommendgoodslist.OperatorRecommendGoodsList.PrivateParams assign fail, type:privateparams.PrivateParams")
	}
	// op.PrivateParams canNil="false"
	if op.PrivateParams == nil {
		return errors.New("recommendgoodslist.OperatorRecommendGoodsList.PrivateParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok1 {
		return errors.New("recommendgoodslist.OperatorRecommendGoodsList.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("recommendgoodslist.OperatorRecommendGoodsList.CommonParams is nil")
	}

	opErr := ((*recommendgoodslist.OperatorRecommendGoodsList)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.RecommendGoodsList canNil="false"
	if op.RecommendGoodsList == nil {
		return errors.New("recommendgoodslist.OperatorRecommendGoodsList register recommendgoodslist.RecommendGoodsList error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.RecommendGoodsList); err != nil {
		return errors.New("recommendgoodslist.OperatorRecommendGoodsList register recommendgoodslist.RecommendGoodsList error:" + err.Error())
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("shop_goods_feed_recommend_goods_list", func() engine.Job {
		return &OperatorRecommendGoodsListWrap{}
	})
	if err != nil {
		panic(err)
	}
}
