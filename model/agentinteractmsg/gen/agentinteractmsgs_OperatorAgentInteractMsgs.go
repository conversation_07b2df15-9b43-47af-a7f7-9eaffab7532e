// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	agentinteractmsgs "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentinteractmsgs"
)

var _ = errors.New("")

// OperatorAgentInteractMsgsWrap OperatorAgentInteractMsgsWrap
type OperatorAgentInteractMsgsWrap agentinteractmsgs.OperatorAgentInteractMsgs

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorAgentInteractMsgsWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.AdapterAgentInteractMsgs = nil
		op.AgentInteractMsgs = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("agentinteractmsgs.OperatorAgentInteractMsgs.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("agentinteractmsgs.OperatorAgentInteractMsgs.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.AdapterAgentInteractMsgs)
	// op.AdapterAgentInteractMsgs canLost="false"
	if !ok1 {
		return errors.New("agentinteractmsgs.OperatorAgentInteractMsgs.AdapterAgentInteractMsgs assign fail, type:agentinteractmsgs.AdapterAgentInteractMsgs")
	}
	// op.AdapterAgentInteractMsgs canNil="false"
	if op.AdapterAgentInteractMsgs == nil {
		return errors.New("agentinteractmsgs.OperatorAgentInteractMsgs.AdapterAgentInteractMsgs is nil")
	}

	opErr := ((*agentinteractmsgs.OperatorAgentInteractMsgs)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.AgentInteractMsgs canNil="false"
	if op.AgentInteractMsgs == nil {
		return errors.New("agentinteractmsgs.OperatorAgentInteractMsgs register agentinteractmsgs.AgentInteractMsgs error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.AgentInteractMsgs); err != nil {
		return errors.New("agentinteractmsgs.OperatorAgentInteractMsgs register agentinteractmsgs.AgentInteractMsgs error:" + err.Error())
	}

	return opErr
}
