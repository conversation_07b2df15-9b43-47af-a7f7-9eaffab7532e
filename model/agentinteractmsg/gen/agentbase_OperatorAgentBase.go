// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	agentbase "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentbase"
)

var _ = errors.New("")

// OperatorAgentBaseWrap OperatorAgentBaseWrap
type OperatorAgentBaseWrap agentbase.OperatorAgentBase

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorAgentBaseWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.AdapterAgentBase = nil
		op.AgentBase = nil
	}()

	ok0 := ctx.MutableInstance(&op.AdapterAgentBase)
	// op.AdapterAgentBase canLost="false"
	if !ok0 {
		return errors.New("agentbase.OperatorAgentBase.AdapterAgentBase assign fail, type:agentbase.AdapterAgentBase")
	}
	// op.AdapterAgentBase canNil="false"
	if op.AdapterAgentBase == nil {
		return errors.New("agentbase.OperatorAgentBase.AdapterAgentBase is nil")
	}

	opErr := ((*agentbase.OperatorAgentBase)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.AgentBase canNil="false"
	if op.AgentBase == nil {
		return errors.New("agentbase.OperatorAgentBase register agentbase.AgentBase error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.AgentBase); err != nil {
		return errors.New("agentbase.OperatorAgentBase register agentbase.AgentBase error:" + err.Error())
	}

	return opErr
}
