// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	userinfo "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/userinfo"
)

var _ = errors.New("")

// OperatorUserInfoWrap OperatorUserInfoWrap
type OperatorUserInfoWrap userinfo.OperatorUserInfo

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorUserInfoWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.AdapterUserInfo = nil
		op.UserInfo = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("userinfo.OperatorUserInfo.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("userinfo.OperatorUserInfo.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.AdapterUserInfo)
	// op.AdapterUserInfo canLost="false"
	if !ok1 {
		return errors.New("userinfo.OperatorUserInfo.AdapterUserInfo assign fail, type:userinfo.AdapterUserInfo")
	}
	// op.AdapterUserInfo canNil="false"
	if op.AdapterUserInfo == nil {
		return errors.New("userinfo.OperatorUserInfo.AdapterUserInfo is nil")
	}

	opErr := ((*userinfo.OperatorUserInfo)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.UserInfo canNil="false"
	if op.UserInfo == nil {
		return errors.New("userinfo.OperatorUserInfo register userinfo.UserInfo error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.UserInfo); err != nil {
		return errors.New("userinfo.OperatorUserInfo register userinfo.UserInfo error:" + err.Error())
	}

	return opErr
}
