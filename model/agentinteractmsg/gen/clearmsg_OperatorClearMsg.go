// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	clearmsg "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/clearmsg"
)

var _ = errors.New("")

// OperatorClearMsgWrap OperatorClearMsgWrap
type OperatorClearMsgWrap clearmsg.OperatorClearMsg

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorClearMsgWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.AdapterClearMsg = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("clearmsg.OperatorClearMsg.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("clearmsg.OperatorClearMsg.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.AdapterClearMsg)
	// op.AdapterClearMsg canLost="false"
	if !ok1 {
		return errors.New("clearmsg.OperatorClearMsg.AdapterClearMsg assign fail, type:clearmsg.AdapterClearMsg")
	}
	// op.AdapterClearMsg canNil="false"
	if op.AdapterClearMsg == nil {
		return errors.New("clearmsg.OperatorClearMsg.AdapterClearMsg is nil")
	}

	opErr := ((*clearmsg.OperatorClearMsg)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	return opErr
}
