package adapter

import (
	"errors"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentbase"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentinteractmsgs"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/agentinteractmsg/gen"
)

type OperatorAgentBaseAdapterWrap gen.OperatorAgentBaseWrap

func (op *OperatorAgentBaseAdapterWrap) DoImpl(ctx *engine.Context) error {
	var instance agentinteractmsgs.AgentInteractMsgs
	ok0 := ctx.MutableInstance(&instance)
	if !ok0 {
		return errors.New("agent.OperatorAgentBaseAdapterWrap.AgentInteractMsgs assign fail, type:agentinteractmsgs.AgentInteractMsgs")
	}
	var botUids []int64
	for _, msg := range instance.GetMessages() {
		botUids = append(botUids, int64(msg.GetBotUid()))
	}
	op.AdapterAgentBase = &agentbase.Input{
		BotUids: botUids,
	}
	if err := ctx.RegisterInstance(&op.AdapterAgentBase); err != nil {
		return errors.New("agentbase.OperatorAgentBaseAdapterWrap register agentbase.AdapterAgentBase error:" + err.Error())
	}

	opErr := ((*gen.OperatorAgentBaseWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("agentinteractmsg_agent", func() engine.Job {
		return &OperatorAgentBaseAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
