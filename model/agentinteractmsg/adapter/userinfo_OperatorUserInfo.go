package adapter

import (
	"errors"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentinteractmsgs"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/userinfo"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/agentinteractmsg/gen"
)

type OperatorUserInfoAdapterWrap gen.OperatorUserInfoWrap

func (op *OperatorUserInfoAdapterWrap) DoImpl(ctx *engine.Context) error {
	var instance agentinteractmsgs.AgentInteractMsgs
	ok0 := ctx.MutableInstance(&instance)
	if !ok0 {
		return errors.New("agent.OperatorUserInfoAdapterWrap.AgentInteractMsgs assign fail, type:agentinteractmsgs.AgentInteractMsgs")
	}
	var userIDs []uint64
	for _, msg := range instance.GetMessages() {
		uid := msg.GetUserId()
		if uid == 0 {
			continue
		}
		userIDs = append(userIDs, uid)
	}
	op.AdapterUserInfo = &userinfo.Input{
		UserIDs: userIDs,
	}
	if err := ctx.RegisterInstance(&op.AdapterUserInfo); err != nil {
		return errors.New("userinfo.OperatorUserInfoAdapterWrap register userinfo.AdapterUserInfo error:" + err.Error())
	}
	opErr := ((*gen.OperatorUserInfoWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("agentinteractmsg_user_info", func() engine.Job {
		return &OperatorUserInfoAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
