package adapter

import (
	"errors"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/clearmsg"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/agentinteractmsg/privateparams"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/agentinteractmsg/gen"
)

type OperatorClearMsgAdapterWrap gen.OperatorClearMsgWrap

func (op *OperatorClearMsgAdapterWrap) DoImpl(ctx *engine.Context) error {
	var params privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&params)
	if !ok0 {
		return errors.New("agentinteractmsgs.OperatorClearMsgAdapterWrap.params assign fail, type:privateparams.PrivateParams")
	}
	if params.GetLastMsgID() >= 0 {
		return nil
	}
	op.AdapterClearMsg = &clearmsg.Input{
		Type: 29,
	}
	if err := ctx.RegisterInstance(&op.AdapterClearMsg); err != nil {
		return errors.New("clearmsg.OperatorClearMsgAdapterWrap register clearmsg.AdapterClearMsg error:" + err.Error())
	}

	opErr := ((*gen.OperatorClearMsgWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("agentinteractmsg_clearmsg", func() engine.Job {
		return &OperatorClearMsgAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
