package adapter

import (
	"errors"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentinteractmsgs"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/agentinteractmsg/privateparams"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/agentinteractmsg/gen"
)

type OperatorAgentInteractMsgsAdapterWrap gen.OperatorAgentInteractMsgsWrap

func (op *OperatorAgentInteractMsgsAdapterWrap) DoImpl(ctx *engine.Context) error {
	var params privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&params)
	if !ok0 {
		return errors.New("agentinteractmsgs.OperatorAgentInteractMsgsAdapterWrap.params assign fail, type:privateparams.PrivateParams")
	}
	op.AdapterAgentInteractMsgs = &agentinteractmsgs.Input{
		LastMsgID: params.GetLastMsgID(),
		Rn:        params.GetRn(),
	}
	if err := ctx.RegisterInstance(&op.AdapterAgentInteractMsgs); err != nil {
		return errors.New("agentinteractmsgs.OperatorAgentInteractMsgsAdapterWrap register agentinteractmsgs.AdapterAgentInteractMsgs error:" + err.Error())
	}

	opErr := ((*gen.OperatorAgentInteractMsgsWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("agentinteractmsg_agent_interact_msgs", func() engine.Job {
		return &OperatorAgentInteractMsgsAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
