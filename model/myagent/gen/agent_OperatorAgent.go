// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	agent "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agent"
)

var _ = errors.New("")

// OperatorAgentWrap OperatorAgentWrap
type OperatorAgentWrap agent.OperatorAgent

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorAgentWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.AdapterAgent = nil
		op.Agent = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("agent.OperatorAgent.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("agent.OperatorAgent.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.AdapterAgent)
	// op.AdapterAgent canLost="false"
	if !ok1 {
		return errors.New("agent.OperatorAgent.AdapterAgent assign fail, type:agent.AdapterAgent")
	}
	// op.AdapterAgent canNil="false"
	if op.AdapterAgent == nil {
		return errors.New("agent.OperatorAgent.AdapterAgent is nil")
	}

	opErr := ((*agent.OperatorAgent)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.Agent canNil="false"
	if op.Agent == nil {
		return errors.New("agent.OperatorAgent register agent.Agent error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.Agent); err != nil {
		return errors.New("agent.OperatorAgent register agent.Agent error:" + err.Error())
	}

	return opErr
}
