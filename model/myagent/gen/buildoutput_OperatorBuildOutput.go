// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	buildoutput "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/myagent/buildoutput"
)

var _ = errors.New("")

// OperatorBuildOutputWrap OperatorBuildOutputWrap
type OperatorBuildOutputWrap buildoutput.OperatorBuildOutput

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorBuildOutputWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.PrivateParams = nil
		op.AgentUIDs = nil
		op.Agent = nil
		op.Whitelist = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("buildoutput.OperatorBuildOutput.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("buildoutput.OperatorBuildOutput.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.PrivateParams)
	// op.PrivateParams canLost="false"
	if !ok1 {
		return errors.New("buildoutput.OperatorBuildOutput.PrivateParams assign fail, type:privateparams.PrivateParams")
	}
	// op.PrivateParams canNil="false"
	if op.PrivateParams == nil {
		return errors.New("buildoutput.OperatorBuildOutput.PrivateParams is nil")
	}

	ok2 := ctx.MutableInstance(&op.AgentUIDs)
	// op.AgentUIDs canLost="false"
	if !ok2 {
		return errors.New("buildoutput.OperatorBuildOutput.AgentUIDs assign fail, type:agentuids.AgentUIDs")
	}
	// op.AgentUIDs canNil="false"
	if op.AgentUIDs == nil {
		return errors.New("buildoutput.OperatorBuildOutput.AgentUIDs is nil")
	}

	ok3 := ctx.MutableInstance(&op.Agent)
	// op.Agent canLost="false"
	if !ok3 {
		return errors.New("buildoutput.OperatorBuildOutput.Agent assign fail, type:agent.Agent")
	}
	// op.Agent canNil="false"
	if op.Agent == nil {
		return errors.New("buildoutput.OperatorBuildOutput.Agent is nil")
	}

	// op.Whitelist canLost="true"
	ctx.MutableInstance(&op.Whitelist)

	opErr := ((*buildoutput.OperatorBuildOutput)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("my_agent_build_output", func() engine.Job {
		return &OperatorBuildOutputWrap{}
	})
	if err != nil {
		panic(err)
	}
}
