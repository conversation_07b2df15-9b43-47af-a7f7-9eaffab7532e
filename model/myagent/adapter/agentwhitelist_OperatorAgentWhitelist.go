package adapter

import (
	"errors"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/myagent/privateparams"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/myagent/gen"
)

type OperatorAgentWhitelistAdapterWrap gen.OperatorAgentWhitelistWrap

func (op *OperatorAgentWhitelistAdapterWrap) DoImpl(ctx *engine.Context) error {
	var params privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&params)
	if !ok0 {
		return errors.New("agent.OperatorAgentWhitelistAdapterWrap.params assign fail, type:privateparams.PrivateParams")
	}
	op.AdapterAgentWhitelist = params
	if err := ctx.RegisterInstance(&op.AdapterAgentWhitelist); err != nil {
		return errors.New("agentwhitelist.OperatorAgentWhitelistAdapterWrap register agentwhitelist.AdapterAgentWhitelist error:" + err.Error())
	}

	opErr := ((*gen.OperatorAgentWhitelistWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("my_agent_whitelist", func() engine.Job {
		return &OperatorAgentWhitelistAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
