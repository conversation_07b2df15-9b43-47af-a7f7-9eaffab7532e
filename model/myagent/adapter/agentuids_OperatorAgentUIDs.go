package adapter

import (
	"errors"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/myagent/privateparams"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/myagent/gen"
)

type OperatorAgentUIDsAdapterWrap gen.OperatorAgentUIDsWrap

func (op *OperatorAgentUIDsAdapterWrap) DoImpl(ctx *engine.Context) error {
	var params privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&params)
	if !ok0 {
		return errors.New("agent.OperatorAgentUIDsAdapterWrap.params assign fail, type:privateparams.PrivateParams")
	}
	op.AdapterAgentUIDs = params
	if err := ctx.RegisterInstance(&op.AdapterAgentUIDs); err != nil {
		return errors.New("agentuids.OperatorAgentUIDsAdapterWrap register agentuids.AdapterAgentUIDs error:" + err.Error())
	}

	opErr := ((*gen.OperatorAgentUIDsWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("my_agent_agent_uids", func() engine.Job {
		return &OperatorAgentUIDsAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
