package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/foruminfo"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/getforumleveldetail/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getforumleveldetail/privateparams"
)

type OperatorForumInfoAdapterWrap gen.OperatorForumInfoWrap

func (op *OperatorForumInfoAdapterWrap) DoImpl(ctx *engine.Context) error {
	var params privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&params)
	if !ok0 {
		return errors.New("forumlevel.OperatorForumLevelAdapterWrap.params assign fail, type:privateparams.PrivateParams")
	}

	op.AdapterForumInfo = &foruminfo.Input{
		ForumIDs: []uint32{uint32(params.GetForumID())},
		NeedExt:  true,
	}
	if err := ctx.RegisterInstance(&op.AdapterForumInfo); err != nil {
		return errors.New("foruminfo.OperatorForumInfoAdapterWrap register foruminfo.AdapterForumInfo error:" + err.Error())
	}

	opErr := ((*gen.OperatorForumInfoWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("get_forum_level_forum_info", func() engine.Job {
		return &OperatorForumInfoAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
