package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/forumforumlevel"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getforumleveldetail/privateparams"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/getforumleveldetail/gen"
)

type OperatorForumForumLevelAdapterWrap gen.OperatorForumForumLevelWrap

func (op *OperatorForumForumLevelAdapterWrap) DoImpl(ctx *engine.Context) error {
	var params privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&params)
	if !ok0 {
		return errors.New("forumlevel.OperatorForumLevelAdapterWrap.params assign fail, type:privateparams.PrivateParams")
	}
	op.AdapterForumForumLevel = &forumforumlevel.Input{
		ForumID: params.GetForumID(),
	}
	if err := ctx.RegisterInstance(&op.AdapterForumForumLevel); err != nil {
		return errors.New("forumforumlevel.OperatorForumForumLevelAdapterWrap register forumforumlevel.AdapterForumForumLevel error:" + err.Error())
	}

	opErr := ((*gen.OperatorForumForumLevelWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("get_forum_level_detail_info", func() engine.Job {
		return &OperatorForumForumLevelAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
