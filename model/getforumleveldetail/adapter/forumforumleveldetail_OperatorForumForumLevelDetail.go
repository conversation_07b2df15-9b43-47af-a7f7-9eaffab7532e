package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/forumforumleveldetail"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/getforumleveldetail/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getforumleveldetail/privateparams"
)

type OperatorForumForumLevelDetailAdapterWrap gen.OperatorForumForumLevelDetailWrap

func (op *OperatorForumForumLevelDetailAdapterWrap) DoImpl(ctx *engine.Context) error {
	var params privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&params)
	if !ok0 {
		return errors.New("forumlevel.OperatorForumLevelAdapterWrap.params assign fail, type:privateparams.PrivateParams")
	}

	op.AdapterForumForumLevelDetail = &forumforumleveldetail.Input{
		ForumID: params.GetForumID(),
	}
	if err := ctx.RegisterInstance(&op.AdapterForumForumLevelDetail); err != nil {
		return errors.New("forumforumleveldetail.OperatorForumForumLevelDetailAdapterWrap register" +
			"forumforumleveldetail.AdapterForumForumLevelDetail error:" + err.Error())
	}

	opErr := ((*gen.OperatorForumForumLevelDetailWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("get_forum_level_detail_list", func() engine.Job {
		return &OperatorForumForumLevelDetailAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
