// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	forumforumleveldetail "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/forumforumleveldetail"
)

var _ = errors.New("")

// OperatorForumForumLevelDetailWrap OperatorForumForumLevelDetailWrap
type OperatorForumForumLevelDetailWrap forumforumleveldetail.OperatorForumForumLevelDetail

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorForumForumLevelDetailWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.AdapterForumForumLevelDetail = nil
		op.ForumForumLevelDetail = nil
	}()

	ok0 := ctx.MutableInstance(&op.AdapterForumForumLevelDetail)
	// op.AdapterForumForumLevelDetail canLost="false"
	if !ok0 {
		return errors.New("forumforumleveldetail.OperatorForumForumLevelDetail.AdapterForumForumLevelDetail assign fail, type:forumforumleveldetail.AdapterForumForumLevelDetail")
	}
	// op.AdapterForumForumLevelDetail canNil="false"
	if op.AdapterForumForumLevelDetail == nil {
		return errors.New("forumforumleveldetail.OperatorForumForumLevelDetail.AdapterForumForumLevelDetail is nil")
	}

	opErr := ((*forumforumleveldetail.OperatorForumForumLevelDetail)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	if err := ctx.RegisterInstance(&op.ForumForumLevelDetail); err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forumforumleveldetail.OperatorForumForumLevelDetail register forumforumleveldetail.ForumForumLevelDetail error:"+err.Error())
	}

	return opErr
}
