// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	buildoutput "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getforumleveldetail/buildoutput"
)

var _ = errors.New("")

// OperatorBuildOutputWrap OperatorBuildOutputWrap
type OperatorBuildOutputWrap buildoutput.OperatorBuildOutput

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorBuildOutputWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.PrivateParams = nil
		op.ForumLevelDetail = nil
		op.ForumLevelInfo = nil
		op.Foruminfo = nil
		op.WordList = nil
	}()

	ok0 := ctx.MutableInstance(&op.PrivateParams)
	// op.PrivateParams canLost="false"
	if !ok0 {
		return errors.New("buildoutput.OperatorBuildOutput.PrivateParams assign fail, type:privateparams.PrivateParams")
	}
	// op.PrivateParams canNil="false"
	if op.PrivateParams == nil {
		return errors.New("buildoutput.OperatorBuildOutput.PrivateParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.ForumLevelDetail)
	// op.ForumLevelDetail canLost="false"
	if !ok1 {
		return errors.New("buildoutput.OperatorBuildOutput.ForumLevelDetail assign fail, type:forumforumleveldetail.ForumForumLevelDetail")
	}
	// op.ForumLevelDetail canNil="false"
	if op.ForumLevelDetail == nil {
		return errors.New("buildoutput.OperatorBuildOutput.ForumLevelDetail is nil")
	}

	ok2 := ctx.MutableInstance(&op.ForumLevelInfo)
	// op.ForumLevelInfo canLost="false"
	if !ok2 {
		return errors.New("buildoutput.OperatorBuildOutput.ForumLevelInfo assign fail, type:forumforumlevel.ForumForumLevel")
	}
	// op.ForumLevelInfo canNil="false"
	if op.ForumLevelInfo == nil {
		return errors.New("buildoutput.OperatorBuildOutput.ForumLevelInfo is nil")
	}

	ok3 := ctx.MutableInstance(&op.Foruminfo)
	// op.Foruminfo canLost="false"
	if !ok3 {
		return errors.New("buildoutput.OperatorBuildOutput.Foruminfo assign fail, type:foruminfo.ForumInfo")
	}
	// op.Foruminfo canNil="false"
	if op.Foruminfo == nil {
		return errors.New("buildoutput.OperatorBuildOutput.Foruminfo is nil")
	}

	ok4 := ctx.MutableInstance(&op.WordList)
	// op.WordList canLost="false"
	if !ok4 {
		return errors.New("buildoutput.OperatorBuildOutput.WordList assign fail, type:wordlist.Wordlist")
	}
	// op.WordList canNil="false"
	if op.WordList == nil {
		return errors.New("buildoutput.OperatorBuildOutput.WordList is nil")
	}

	opErr := ((*buildoutput.OperatorBuildOutput)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("get_forum_level_detail_build_output", func() engine.Job {
		return &OperatorBuildOutputWrap{}
	})
	if err != nil {
		panic(err)
	}
}
