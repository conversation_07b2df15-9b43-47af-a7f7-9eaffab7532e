// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	forumforumlevel "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/forumforumlevel"
)

var _ = errors.New("")

// OperatorForumForumLevelWrap OperatorForumForumLevelWrap
type OperatorForumForumLevelWrap forumforumlevel.OperatorForumForumLevel

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorForumForumLevelWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.AdapterForumForumLevel = nil
		op.ForumForumLevel = nil
	}()

	ok0 := ctx.MutableInstance(&op.AdapterForumForumLevel)
	// op.AdapterForumForumLevel canLost="false"
	if !ok0 {
		return errors.New("forumforumlevel.OperatorForumForumLevel.AdapterForumForumLevel assign fail, type:forumforumlevel.AdapterForumForumLevel")
	}
	// op.AdapterForumForumLevel canNil="false"
	if op.AdapterForumForumLevel == nil {
		return errors.New("forumforumlevel.OperatorForumForumLevel.AdapterForumForumLevel is nil")
	}

	opErr := ((*forumforumlevel.OperatorForumForumLevel)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	if err := ctx.RegisterInstance(&op.ForumForumLevel); err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forumforumlevel.OperatorForumForumLevel register forumforumlevel.ForumForumLevel error:"+err.Error())
	}

	return opErr
}
