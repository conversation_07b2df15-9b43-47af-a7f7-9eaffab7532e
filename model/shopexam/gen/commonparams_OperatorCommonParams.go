// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	commonparams "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
)

var _ = errors.New("")

// OperatorCommonParamsWrap OperatorCommonParamsWrap
type OperatorCommonParamsWrap commonparams.OperatorCommonParams

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorCommonParamsWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.BaseAction = nil
		op.CommonParams = nil
	}()

	ok0 := ctx.MutableInstance(&op.BaseAction)
	// op.BaseAction canLost="false"
	if !ok0 {
		return errors.New("commonparams.OperatorCommonParams.BaseAction assign fail, type:*client.UIBaseAction")
	}
	// op.BaseAction canNil="false"
	if op.BaseAction == nil {
		return errors.New("commonparams.OperatorCommonParams.BaseAction is nil")
	}

	opErr := ((*commonparams.OperatorCommonParams)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("commonparams.OperatorCommonParams register commonparams.CommonParams error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.CommonParams); err != nil {
		return errors.New("commonparams.OperatorCommonParams register commonparams.CommonParams error:" + err.Error())
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("shop_exam_common_params", func() engine.Job {
		return &OperatorCommonParamsWrap{}
	})
	if err != nil {
		panic(err)
	}
}
