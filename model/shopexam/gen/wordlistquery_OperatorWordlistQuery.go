// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	wordlistquery "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopExam/wordlistquery"
)

var _ = errors.New("")

// OperatorWordlistQueryWrap OperatorWordlistQueryWrap
type OperatorWordlistQueryWrap wordlistquery.OperatorWordlistQuery

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorWordlistQueryWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.PrivateParams = nil
		op.WordlistQuery = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("wordlistquery.OperatorWordlistQuery.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("wordlistquery.OperatorWordlistQuery.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.PrivateParams)
	// op.PrivateParams canLost="false"
	if !ok1 {
		return errors.New("wordlistquery.OperatorWordlistQuery.PrivateParams assign fail, type:privateparams.PrivateParams")
	}
	// op.PrivateParams canNil="false"
	if op.PrivateParams == nil {
		return errors.New("wordlistquery.OperatorWordlistQuery.PrivateParams is nil")
	}

	opErr := ((*wordlistquery.OperatorWordlistQuery)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.WordlistQuery canNil="false"
	if op.WordlistQuery == nil {
		return errors.New("wordlistquery.OperatorWordlistQuery register wordlistquery.WordlistQuery error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.WordlistQuery); err != nil {
		return errors.New("wordlistquery.OperatorWordlistQuery register wordlistquery.WordlistQuery error:" + err.Error())
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("shop_exam_wordlist_query", func() engine.Job {
		return &OperatorWordlistQueryWrap{}
	})
	if err != nil {
		panic(err)
	}
}
