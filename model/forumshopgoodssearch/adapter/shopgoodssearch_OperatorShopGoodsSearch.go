package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shopgoodssearch"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/forumshopgoodssearch/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopgoodssearch/privateparams"
)

type OperatorShopGoodsSearchAdapterWrap gen.OperatorShopGoodsSearchWrap

func (op *OperatorShopGoodsSearchAdapterWrap) DoImpl(ctx *engine.Context) error {
	// TODO: 公共算子依赖数据写入 示例如下
	var privateParams privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&privateParams)
	if !ok0 {
		return errors.New("foruminfo.OperatorForumInfoAdapterWrap.privateParams assign fail, type:privateparams.PrivateParams")
	}

	op.AdapterShopGoodsSearch = &shopgoodssearch.Input{
		OnlySug: privateParams.GetOnlySug(),
		Pn:      privateParams.GetPn(),
		Rn:      privateParams.GetRn(),
		Sug:     privateParams.GetSug(),
	}

	if err := ctx.RegisterInstance(&op.AdapterShopGoodsSearch); err != nil {
		return errors.New("shopgoodssearch.OperatorShopGoodsSearchAdapterWrap register shopgoodssearch.AdapterShopGoodsSearch error:" + err.Error())
	}

	opErr := ((*gen.OperatorShopGoodsSearchWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator(
		"shop_goods_search", func() engine.Job {
			return &OperatorShopGoodsSearchAdapterWrap{}
		},
	)
	if err != nil {
		panic(err)
	}
}
