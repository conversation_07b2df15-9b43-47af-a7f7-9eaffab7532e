package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/foruminfo"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shopgoodssearch"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shopgoodssearchrecommend"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/forumshopgoodssearch/gen"
)

type OperatorForumInfoAdapterWrap gen.OperatorForumInfoWrap

func (op *OperatorForumInfoAdapterWrap) DoImpl(ctx *engine.Context) error {
	var goodsList shopgoodssearch.ShopGoodsSearch
	ok0 := ctx.MutableInstance(&goodsList)
	if !ok0 {
		return errors.New("foruminfo.OperatorForumInfoAdapterWrap.goodsList assign fail, type:shopgoodssearch.ShopGoodsSearch")
	}

	var recommendList shopgoodssearchrecommend.ShopGoodsSearchRecommend
	ok1 := ctx.MutableInstance(&recommendList)
	if !ok1 {
		return errors.New(
			"foruminfo.OperatorForumInfoAdapterWrap.recommendList assign fail, type:shopgoodssearchrecommend.ShopGoodsSearchRecommend",
		)
	}

	forumIDs := make([]uint32, 0)

	for _, g := range goodsList.Search().GetGoodsList() {
		forumIDs = append(forumIDs, uint32(g.GetForumId()))
	}
	for _, g := range recommendList.GetRecommend() {
		forumIDs = append(forumIDs, uint32(g.GetForumId()))
	}

	op.AdapterForumInfo = &foruminfo.Input{
		ForumIDs: forumIDs,
	}
	if err := ctx.RegisterInstance(&op.AdapterForumInfo); err != nil {
		return errors.New("foruminfo.OperatorForumInfoAdapterWrap register foruminfo.AdapterForumInfo error:" + err.Error())
	}

	opErr := ((*gen.OperatorForumInfoWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator(
		"shop_goods_search_forum_info", func() engine.Job {
			return &OperatorForumInfoAdapterWrap{}
		},
	)
	if err != nil {
		panic(err)
	}
}
