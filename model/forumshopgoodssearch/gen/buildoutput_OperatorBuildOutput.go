// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	buildoutput "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopgoodssearch/buildoutput"
)

var _ = errors.New("")

// OperatorBuildOutputWrap OperatorBuildOutputWrap
type OperatorBuildOutputWrap buildoutput.OperatorBuildOutput

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorBuildOutputWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.BaseAction = nil
		op.CommonParams = nil
		op.PrivateParams = nil
		op.Search = nil
		op.ForumInfo = nil
		op.Recommend = nil
	}()

	ok0 := ctx.MutableInstance(&op.BaseAction)
	// op.BaseAction canLost="false"
	if !ok0 {
		return errors.New("buildoutput.OperatorBuildOutput.BaseAction assign fail, type:*client.UIBaseAction")
	}
	// op.BaseAction canNil="false"
	if op.BaseAction == nil {
		return errors.New("buildoutput.OperatorBuildOutput.BaseAction is nil")
	}

	ok1 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok1 {
		return errors.New("buildoutput.OperatorBuildOutput.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("buildoutput.OperatorBuildOutput.CommonParams is nil")
	}

	ok2 := ctx.MutableInstance(&op.PrivateParams)
	// op.PrivateParams canLost="false"
	if !ok2 {
		return errors.New("buildoutput.OperatorBuildOutput.PrivateParams assign fail, type:privateparams.PrivateParams")
	}
	// op.PrivateParams canNil="false"
	if op.PrivateParams == nil {
		return errors.New("buildoutput.OperatorBuildOutput.PrivateParams is nil")
	}

	ok3 := ctx.MutableInstance(&op.Search)
	// op.Search canLost="false"
	if !ok3 {
		return errors.New("buildoutput.OperatorBuildOutput.Search assign fail, type:shopgoodssearch.ShopGoodsSearch")
	}
	// op.Search canNil="false"
	if op.Search == nil {
		return errors.New("buildoutput.OperatorBuildOutput.Search is nil")
	}

	ok4 := ctx.MutableInstance(&op.ForumInfo)
	// op.ForumInfo canLost="false"
	if !ok4 {
		return errors.New("buildoutput.OperatorBuildOutput.ForumInfo assign fail, type:foruminfo.ForumInfo")
	}
	// op.ForumInfo canNil="false"
	if op.ForumInfo == nil {
		return errors.New("buildoutput.OperatorBuildOutput.ForumInfo is nil")
	}

	ok5 := ctx.MutableInstance(&op.Recommend)
	// op.Recommend canLost="false"
	if !ok5 {
		return errors.New("buildoutput.OperatorBuildOutput.Recommend assign fail, type:shopgoodssearchrecommend.ShopGoodsSearchRecommend")
	}
	// op.Recommend canNil="false"
	if op.Recommend == nil {
		return errors.New("buildoutput.OperatorBuildOutput.Recommend is nil")
	}

	opErr := ((*buildoutput.OperatorBuildOutput)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("shop_goods_search_build_output", func() engine.Job {
		return &OperatorBuildOutputWrap{}
	})
	if err != nil {
		panic(err)
	}
}
