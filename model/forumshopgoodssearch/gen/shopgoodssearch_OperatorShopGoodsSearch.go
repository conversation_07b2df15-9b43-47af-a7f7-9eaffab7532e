// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	shopgoodssearch "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shopgoodssearch"
)

var _ = errors.New("")

// OperatorShopGoodsSearchWrap OperatorShopGoodsSearchWrap
type OperatorShopGoodsSearchWrap shopgoodssearch.OperatorShopGoodsSearch

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorShopGoodsSearchWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.AdapterShopGoodsSearch = nil
		op.ShopGoodsSearch = nil
	}()

	ok0 := ctx.MutableInstance(&op.AdapterShopGoodsSearch)
	// op.AdapterShopGoodsSearch canLost="false"
	if !ok0 {
		return errors.New("shopgoodssearch.OperatorShopGoodsSearch.AdapterShopGoodsSearch assign fail, type:shopgoodssearch.AdapterShopGoodsSearch")
	}
	// op.AdapterShopGoodsSearch canNil="false"
	if op.AdapterShopGoodsSearch == nil {
		return errors.New("shopgoodssearch.OperatorShopGoodsSearch.AdapterShopGoodsSearch is nil")
	}

	opErr := ((*shopgoodssearch.OperatorShopGoodsSearch)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	if err := ctx.RegisterInstance(&op.ShopGoodsSearch); err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "shopgoodssearch.OperatorShopGoodsSearch register shopgoodssearch.ShopGoodsSearch error:"+err.Error())
	}

	return opErr
}
