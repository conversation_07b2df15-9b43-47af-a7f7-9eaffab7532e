package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentuids"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/getuserrelationagent/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getuserrelationagent/privateparams"
)

type OperatorAgentUIDsAdapterWrap gen.OperatorAgentUIDsWrap

func (op *OperatorAgentUIDsAdapterWrap) DoImpl(ctx *engine.Context) error {
	var privateParams privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&privateParams)
	if !ok0 {
		return errors.New("agentuids.OperatorAgentUIDsAdapterWrap.privateParams assign fail, type:privateparams.PrivateParams")
	}

	var commonParams commonparams.CommonParams
	ok1 := ctx.MutableInstance(&commonParams)
	if !ok1 {
		return errors.New("agentuids.OperatorAgentUIDsAdapterWrap.commonparams assign fail, type:commonparams.CommonParams")
	}

	// 获取用户头像
	portrait := tbportrait.Encode(int64(commonParams.GetUserID()), "", 0)

	op.AdapterAgentUIDs = &agentuids.Input{
		Portrait: portrait,
		UserType: int32(2),
		Pn:       int32(privateParams.GetPn()),
		Rn:       int32(privateParams.GetRn()),
		Scene:    int32(5),
	}
	if err := ctx.RegisterInstance(&op.AdapterAgentUIDs); err != nil {
		return errors.New("agentuids.OperatorAgentUIDsAdapterWrap register agentuids.AdapterAgentUIDs error:" + err.Error())
	}

	opErr := ((*gen.OperatorAgentUIDsWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("get_user_relation_agent_uids", func() engine.Job {
		return &OperatorAgentUIDsAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
