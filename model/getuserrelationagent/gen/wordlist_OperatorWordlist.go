// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	wordlist "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getuserrelationagent/wordlist"
)

var _ = errors.New("")

// OperatorWordlistWrap OperatorWordlistWrap
type OperatorWordlistWrap wordlist.OperatorWordlist

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorWordlistWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.Wordlist = nil
	}()

	opErr := ((*wordlist.OperatorWordlist)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	if err := ctx.RegisterInstance(&op.Wordlist); err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "wordlist.OperatorWordlist register wordlist.Wordlist error:"+err.Error())
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("get_user_relation_agent_wordlist", func() engine.Job {
		return &OperatorWordlistWrap{}
	})
	if err != nil {
		panic(err)
	}
}
