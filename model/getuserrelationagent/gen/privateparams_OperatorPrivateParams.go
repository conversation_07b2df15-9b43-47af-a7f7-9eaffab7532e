// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	privateparams "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getuserrelationagent/privateparams"
)

var _ = errors.New("")

// OperatorPrivateParamsWrap OperatorPrivateParamsWrap
type OperatorPrivateParamsWrap privateparams.OperatorPrivateParams

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorPrivateParamsWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.BaseAction = nil
		op.PrivateParams = nil
	}()

	ok0 := ctx.MutableInstance(&op.BaseAction)
	// op.BaseAction canLost="false"
	if !ok0 {
		return errors.New("privateparams.OperatorPrivateParams.BaseAction assign fail, type:*client.UIBaseAction")
	}
	// op.BaseAction canNil="false"
	if op.BaseAction == nil {
		return errors.New("privateparams.OperatorPrivateParams.BaseAction is nil")
	}

	opErr := ((*privateparams.OperatorPrivateParams)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.PrivateParams canNil="false"
	if op.PrivateParams == nil {
		return errors.New("privateparams.OperatorPrivateParams register privateparams.PrivateParams error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.PrivateParams); err != nil {
		return errors.New("privateparams.OperatorPrivateParams register privateparams.PrivateParams error:" + err.Error())
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("get_user_relation_agent_private_params", func() engine.Job {
		return &OperatorPrivateParamsWrap{}
	})
	if err != nil {
		panic(err)
	}
}
