// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	agentuids "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentuids"
)

var _ = errors.New("")

// OperatorAgentUIDsWrap OperatorAgentUIDsWrap
type OperatorAgentUIDsWrap agentuids.OperatorAgentUIDs

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorAgentUIDsWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.AdapterAgentUIDs = nil
		op.AgentUIDs = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("agentuids.OperatorAgentUIDs.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("agentuids.OperatorAgentUIDs.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.AdapterAgentUIDs)
	// op.AdapterAgentUIDs canLost="false"
	if !ok1 {
		return errors.New("agentuids.OperatorAgentUIDs.AdapterAgentUIDs assign fail, type:agentuids.AdapterAgentUIDs")
	}
	// op.AdapterAgentUIDs canNil="false"
	if op.AdapterAgentUIDs == nil {
		return errors.New("agentuids.OperatorAgentUIDs.AdapterAgentUIDs is nil")
	}

	opErr := ((*agentuids.OperatorAgentUIDs)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.AgentUIDs canNil="false"
	if op.AgentUIDs == nil {
		return errors.New("agentuids.OperatorAgentUIDs register agentuids.AgentUIDs error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.AgentUIDs); err != nil {
		return errors.New("agentuids.OperatorAgentUIDs register agentuids.AgentUIDs error:" + err.Error())
	}

	return opErr
}
