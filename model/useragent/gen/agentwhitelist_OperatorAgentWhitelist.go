// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	agentwhitelist "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentwhitelist"
)

var _ = errors.New("")

// OperatorAgentWhitelistWrap OperatorAgentWhitelistWrap
type OperatorAgentWhitelistWrap agentwhitelist.OperatorAgentWhitelist

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorAgentWhitelistWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.AdapterAgentWhitelist = nil
		op.AgentWhitelist = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("agentwhitelist.OperatorAgentWhitelist.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("agentwhitelist.OperatorAgentWhitelist.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.AdapterAgentWhitelist)
	// op.AdapterAgentWhitelist canLost="false"
	if !ok1 {
		return errors.New("agentwhitelist.OperatorAgentWhitelist.AdapterAgentWhitelist assign fail, type:agentwhitelist.AdapterAgentWhitelist")
	}
	// op.AdapterAgentWhitelist canNil="false"
	if op.AdapterAgentWhitelist == nil {
		return errors.New("agentwhitelist.OperatorAgentWhitelist.AdapterAgentWhitelist is nil")
	}

	opErr := ((*agentwhitelist.OperatorAgentWhitelist)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.AgentWhitelist canNil="false"
	if op.AgentWhitelist == nil {
		return errors.New("agentwhitelist.OperatorAgentWhitelist register agentwhitelist.AgentWhitelist error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.AgentWhitelist); err != nil {
		return errors.New("agentwhitelist.OperatorAgentWhitelist register agentwhitelist.AgentWhitelist error:" + err.Error())
	}

	return opErr
}
