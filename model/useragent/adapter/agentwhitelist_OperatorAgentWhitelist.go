package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentwhitelist"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/useragent/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/useragent/privateparams"
)

type OperatorAgentWhitelistAdapterWrap gen.OperatorAgentWhitelistWrap

func (op *OperatorAgentWhitelistAdapterWrap) DoImpl(ctx *engine.Context) error {
	var privateParams privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&privateParams)
	if !ok0 {
		return errors.New("agentwhitelist.OperatorAgentWhitelistAdapterWrap mutable agentwhitelist.PrivateParams error")
	}

	input := agentwhitelist.Input{
		UserType: privateParams.GetUserType(),
	}
	op.AdapterAgentWhitelist = &input
	if err := ctx.RegisterInstance(&op.AdapterAgentWhitelist); err != nil {
		return errors.New("agentwhitelist.OperatorAgentWhitelistAdapterWrap register agentwhitelist.AdapterAgentWhitelist error:" + err.Error())
	}

	opErr := ((*gen.OperatorAgentWhitelistWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("user_agent_whitelist", func() engine.Job {
		return &OperatorAgentWhitelistAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
