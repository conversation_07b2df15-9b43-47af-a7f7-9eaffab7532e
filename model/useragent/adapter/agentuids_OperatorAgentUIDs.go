package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentuids"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/useragent/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/useragent/privateparams"
)

type OperatorAgentUIDsAdapterWrap gen.OperatorAgentUIDsWrap

func (op *OperatorAgentUIDsAdapterWrap) DoImpl(ctx *engine.Context) error {
	var privateParams privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&privateParams)
	if !ok0 {
		return errors.New("agentuids.OperatorAgentUIDsAdapterWrap assign privateParams fail, type:privateparams.PrivateParams")
	}

	input := agentuids.Input{
		UserType: privateParams.GetUserType(),
		Pn:       privateParams.GetPn(),
		Rn:       privateParams.GetRn(),
		Scene:    int32(agentuids.AgentTabSceneMine),
		Portrait: privateParams.GetPortrait(),
		PaType:   privateParams.GetPaType(),
	}
	op.AdapterAgentUIDs = &input
	if err := ctx.RegisterInstance(&op.AdapterAgentUIDs); err != nil {
		return errors.New("agentuids.OperatorAgentUIDsAdapterWrap register agentuids.AdapterAgentUIDs error:" + err.Error())
	}

	opErr := ((*gen.OperatorAgentUIDsWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("user_agent_agent_uids", func() engine.Job {
		return &OperatorAgentUIDsAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
