package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agent"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentuids"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/useragent/gen"
)

type OperatorAgentAdapterWrap gen.OperatorAgentWrap

func (op *OperatorAgentAdapterWrap) DoImpl(ctx *engine.Context) error {
	var agentUIDs agentuids.AgentUIDs
	ok0 := ctx.MutableInstance(&agentUIDs)
	if !ok0 {
		return errors.New("agent.OperatorAgentAdapterWrap.agentUIDs assign fail, type:agentuids.AgentUIDs")
	}
	op.AdapterAgent = &agent.Input{
		UserIDs:  agentUIDs.GetAgentUIDs(),
		CallFrom: "guest",
	}
	if err := ctx.RegisterInstance(&op.AdapterAgent); err != nil {
		return errors.New("agent.OperatorAgentAdapterWrap register agent.AdapterAgent error:" + err.Error())
	}

	opErr := ((*gen.OperatorAgentWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("user_agent_agent", func() engine.Job {
		return &OperatorAgentAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
