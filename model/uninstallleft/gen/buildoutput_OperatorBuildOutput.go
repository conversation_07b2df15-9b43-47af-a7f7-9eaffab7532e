// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	buildoutput "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/uninstallleft/buildoutput"
)

var _ = errors.New("")

// OperatorBuildOutputWrap OperatorBuildOutputWrap
type OperatorBuildOutputWrap buildoutput.OperatorBuildOutput

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorBuildOutputWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.Ad = nil
		op.Appeal = nil
		op.CommonConfig = nil
		op.BuildOutput = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("buildoutput.OperatorBuildOutput.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("buildoutput.OperatorBuildOutput.CommonParams is nil")
	}

	// op.Ad canLost="true"
	ctx.MutableInstance(&op.Ad)

	// op.Appeal canLost="true"
	ctx.MutableInstance(&op.Appeal)

	// op.CommonConfig canLost="true"
	ctx.MutableInstance(&op.CommonConfig)

	opErr := ((*buildoutput.OperatorBuildOutput)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.BuildOutput canNil="false"
	if op.BuildOutput == nil {
		return errors.New("buildoutput.OperatorBuildOutput register buildoutput.BuildOutput error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.BuildOutput); err != nil {
		return errors.New("buildoutput.OperatorBuildOutput register buildoutput.BuildOutput error:" + err.Error())
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("uninstall_left_build_output", func() engine.Job {
		return &OperatorBuildOutputWrap{}
	})
	if err != nil {
		panic(err)
	}
}
