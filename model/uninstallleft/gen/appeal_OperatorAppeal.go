// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	appeal "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/uninstallleft/appeal"
)

var _ = errors.New("")

// OperatorAppealWrap OperatorAppealWrap
type OperatorAppealWrap appeal.OperatorAppeal

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorAppealWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.Appeal = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("appeal.OperatorAppeal.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("appeal.OperatorAppeal.CommonParams is nil")
	}

	opErr := ((*appeal.OperatorAppeal)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	if err := ctx.RegisterInstance(&op.Appeal); err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "appeal.OperatorAppeal register appeal.Appeal error:"+err.Error())
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("uninstall_left_appeal", func() engine.Job {
		return &OperatorAppealWrap{}
	})
	if err != nil {
		panic(err)
	}
}
