package adapter

import (
	"errors"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/getfidsbystatictag"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/searchForumByTag/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/searchforumbytag/privateparams"
)

type OperatorGetFidsByStaticTagAdapterWrap gen.OperatorGetFidsByStaticTagWrap

func (op *OperatorGetFidsByStaticTagAdapterWrap) DoImpl(ctx *engine.Context) error {
	var params privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&params)
	if !ok0 {
		return errors.New("getfidsbystatictag.OperatorGetFidsByStaticTagAdapterWrap mutable getfidsbystatictag.PrivateParams error")
	}
	var forumType uint32
	switch params.GetSearchType() {
	case "tag":
		forumType = 1
	case "recom":
		forumType = 2
	}
	op.AdapterGetFidsByStaticTag = &getfidsbystatictag.Input{
		TagName:            params.GetTag(),
		Pn:                 params.GetPn(),
		Rn:                 params.GetRn(),
		ForumType:          forumType,
		NeedForumStaticTag: params.GetNeedForumStaticTag(),
		ForumID:            params.GetForumID(),
	}
	if err := ctx.RegisterInstance(&op.AdapterGetFidsByStaticTag); err != nil {
		return errors.New("getfidsbystatictag.OperatorGetFidsByStaticTagAdapterWrap register getfidsbystatictag.AdapterGetFidsByStaticTag error:" + err.Error())
	}

	opErr := ((*gen.OperatorGetFidsByStaticTagWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("search_forum_by_tag_get_fids_by_static_tag", func() engine.Job {
		return &OperatorGetFidsByStaticTagAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
