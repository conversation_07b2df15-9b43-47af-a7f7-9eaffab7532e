package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/gettagsbystatictag"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/searchForumByTag/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/searchforumbytag/privateparams"
)

type OperatorGetTagsByStaticTagAdapterWrap gen.OperatorGetTagsByStaticTagWrap

func (op *OperatorGetTagsByStaticTagAdapterWrap) DoImpl(ctx *engine.Context) error {
	var params privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&params)
	if !ok0 {
		return errors.New("getfidsbystatictag.OperatorGetFidsByStaticTagAdapterWrap mutable getfidsbystatictag.PrivateParams error")
	}
	op.AdapterGetTagsByStaticTag = &gettagsbystatictag.Input{
		TagName: params.GetTag(),
	}
	if err := ctx.RegisterInstance(&op.AdapterGetTagsByStaticTag); err != nil {
		return errors.New("gettagsbystatictag.OperatorGetTagsByStaticTagAdapterWrap register gettagsbystatictag.AdapterGetTagsByStaticTag error:" + err.Error())
	}

	opErr := ((*gen.OperatorGetTagsByStaticTagWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("search_forum_by_tag_get_tags_by_static_tag", func() engine.Job {
		return &OperatorGetTagsByStaticTagAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
