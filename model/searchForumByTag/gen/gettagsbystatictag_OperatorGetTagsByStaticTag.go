// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	gettagsbystatictag "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/gettagsbystatictag"
)

var _ = errors.New("")

// OperatorGetTagsByStaticTagWrap OperatorGetTagsByStaticTagWrap
type OperatorGetTagsByStaticTagWrap gettagsbystatictag.OperatorGetTagsByStaticTag

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorGetTagsByStaticTagWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.AdapterGetTagsByStaticTag = nil
		op.GetTagsByStaticTag = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("gettagsbystatictag.OperatorGetTagsByStaticTag.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("gettagsbystatictag.OperatorGetTagsByStaticTag.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.AdapterGetTagsByStaticTag)
	// op.AdapterGetTagsByStaticTag canLost="false"
	if !ok1 {
		return errors.New("gettagsbystatictag.OperatorGetTagsByStaticTag.AdapterGetTagsByStaticTag assign fail, type:gettagsbystatictag.AdapterGetTagsByStaticTag")
	}
	// op.AdapterGetTagsByStaticTag canNil="false"
	if op.AdapterGetTagsByStaticTag == nil {
		return errors.New("gettagsbystatictag.OperatorGetTagsByStaticTag.AdapterGetTagsByStaticTag is nil")
	}

	opErr := ((*gettagsbystatictag.OperatorGetTagsByStaticTag)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	if err := ctx.RegisterInstance(&op.GetTagsByStaticTag); err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "gettagsbystatictag.OperatorGetTagsByStaticTag register gettagsbystatictag.GetTagsByStaticTag error:"+err.Error())
	}

	return opErr
}
