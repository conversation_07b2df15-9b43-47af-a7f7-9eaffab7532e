// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	searchwordsisforbid "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/searchforumbytag/searchwordsisforbid"
)

var _ = errors.New("")

// OperatorSearchWordsIsForbidWrap OperatorSearchWordsIsForbidWrap
type OperatorSearchWordsIsForbidWrap searchwordsisforbid.OperatorSearchWordsIsForbid

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorSearchWordsIsForbidWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.PrivateParams = nil
		op.CommonParams = nil
		op.SearchWordsIsForbid = nil
	}()

	ok0 := ctx.MutableInstance(&op.PrivateParams)
	// op.PrivateParams canLost="false"
	if !ok0 {
		return errors.New("searchwordsisforbid.OperatorSearchWordsIsForbid.PrivateParams assign fail, type:privateparams.PrivateParams")
	}
	// op.PrivateParams canNil="false"
	if op.PrivateParams == nil {
		return errors.New("searchwordsisforbid.OperatorSearchWordsIsForbid.PrivateParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok1 {
		return errors.New("searchwordsisforbid.OperatorSearchWordsIsForbid.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("searchwordsisforbid.OperatorSearchWordsIsForbid.CommonParams is nil")
	}

	opErr := ((*searchwordsisforbid.OperatorSearchWordsIsForbid)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.SearchWordsIsForbid canNil="false"
	if op.SearchWordsIsForbid == nil {
		return errors.New("searchwordsisforbid.OperatorSearchWordsIsForbid register searchwordsisforbid.SearchWordsIsForbid error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.SearchWordsIsForbid); err != nil {
		return errors.New("searchwordsisforbid.OperatorSearchWordsIsForbid register searchwordsisforbid.SearchWordsIsForbid error:" + err.Error())
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("search_forum_by_tag_search_words_is_forbid", func() engine.Job {
		return &OperatorSearchWordsIsForbidWrap{}
	})
	if err != nil {
		panic(err)
	}
}
