// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	getfidsbystatictag "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/getfidsbystatictag"
)

var _ = errors.New("")

// OperatorGetFidsByStaticTagWrap OperatorGetFidsByStaticTagWrap
type OperatorGetFidsByStaticTagWrap getfidsbystatictag.OperatorGetFidsByStaticTag

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorGetFidsByStaticTagWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.AdapterGetFidsByStaticTag = nil
		op.GetFidsByStaticTag = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("getfidsbystatictag.OperatorGetFidsByStaticTag.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("getfidsbystatictag.OperatorGetFidsByStaticTag.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.AdapterGetFidsByStaticTag)
	// op.AdapterGetFidsByStaticTag canLost="false"
	if !ok1 {
		return errors.New("getfidsbystatictag.OperatorGetFidsByStaticTag.AdapterGetFidsByStaticTag assign fail, type:getfidsbystatictag.AdapterGetFidsByStaticTag")
	}
	// op.AdapterGetFidsByStaticTag canNil="false"
	if op.AdapterGetFidsByStaticTag == nil {
		return errors.New("getfidsbystatictag.OperatorGetFidsByStaticTag.AdapterGetFidsByStaticTag is nil")
	}

	opErr := ((*getfidsbystatictag.OperatorGetFidsByStaticTag)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	if err := ctx.RegisterInstance(&op.GetFidsByStaticTag); err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "getfidsbystatictag.OperatorGetFidsByStaticTag register getfidsbystatictag.GetFidsByStaticTag error:"+err.Error())
	}

	return opErr
}
