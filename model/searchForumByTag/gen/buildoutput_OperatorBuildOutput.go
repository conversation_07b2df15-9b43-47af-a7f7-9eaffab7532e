// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	buildoutput "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/searchforumbytag/buildoutput"
)

var _ = errors.New("")

// OperatorBuildOutputWrap OperatorBuildOutputWrap
type OperatorBuildOutputWrap buildoutput.OperatorBuildOutput

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorBuildOutputWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.BaseAction = nil
		op.CommonParams = nil
		op.PrivateParams = nil
		op.RelatedTagList = nil
		op.ForumList = nil
		op.SearchWordsIsForbid = nil
	}()

	ok0 := ctx.MutableInstance(&op.BaseAction)
	// op.BaseAction canLost="false"
	if !ok0 {
		return errors.New("buildoutput.OperatorBuildOutput.BaseAction assign fail, type:*client.UIBaseAction")
	}
	// op.BaseAction canNil="false"
	if op.BaseAction == nil {
		return errors.New("buildoutput.OperatorBuildOutput.BaseAction is nil")
	}

	ok1 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok1 {
		return errors.New("buildoutput.OperatorBuildOutput.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("buildoutput.OperatorBuildOutput.CommonParams is nil")
	}

	ok2 := ctx.MutableInstance(&op.PrivateParams)
	// op.PrivateParams canLost="false"
	if !ok2 {
		return errors.New("buildoutput.OperatorBuildOutput.PrivateParams assign fail, type:privateparams.PrivateParams")
	}
	// op.PrivateParams canNil="false"
	if op.PrivateParams == nil {
		return errors.New("buildoutput.OperatorBuildOutput.PrivateParams is nil")
	}

	ok3 := ctx.MutableInstance(&op.RelatedTagList)
	// op.RelatedTagList canLost="false"
	if !ok3 {
		return errors.New("buildoutput.OperatorBuildOutput.RelatedTagList assign fail, type:gettagsbystatictag.GetTagsByStaticTag")
	}
	// op.RelatedTagList canNil="false"
	if op.RelatedTagList == nil {
		return errors.New("buildoutput.OperatorBuildOutput.RelatedTagList is nil")
	}

	ok4 := ctx.MutableInstance(&op.ForumList)
	// op.ForumList canLost="false"
	if !ok4 {
		return errors.New("buildoutput.OperatorBuildOutput.ForumList assign fail, type:getfidsbystatictag.GetFidsByStaticTag")
	}
	// op.ForumList canNil="false"
	if op.ForumList == nil {
		return errors.New("buildoutput.OperatorBuildOutput.ForumList is nil")
	}

	ok5 := ctx.MutableInstance(&op.SearchWordsIsForbid)
	// op.SearchWordsIsForbid canLost="false"
	if !ok5 {
		return errors.New("buildoutput.OperatorBuildOutput.SearchWordsIsForbid assign fail, type:searchwordsisforbid.SearchWordsIsForbid")
	}
	// op.SearchWordsIsForbid canNil="false"
	if op.SearchWordsIsForbid == nil {
		return errors.New("buildoutput.OperatorBuildOutput.SearchWordsIsForbid is nil")
	}

	opErr := ((*buildoutput.OperatorBuildOutput)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("search_forum_by_tag_build_output", func() engine.Job {
		return &OperatorBuildOutputWrap{}
	})
	if err != nil {
		panic(err)
	}
}
