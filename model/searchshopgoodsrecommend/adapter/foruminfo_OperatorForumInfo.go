package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/foruminfo"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shopgoodssearchrecommend"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/searchshopgoodsrecommend/gen"
)

type OperatorForumInfoAdapterWrap gen.OperatorForumInfoWrap

func (op *OperatorForumInfoAdapterWrap) DoImpl(ctx *engine.Context) error {
	var recommendList shopgoodssearchrecommend.ShopGoodsSearchRecommend
	ok1 := ctx.MutableInstance(&recommendList)
	if !ok1 {
		return errors.New("foruminfo.OperatorForumInfoAdapterWrap.recommendList assign fail, type:shopgoodssearchrecommend.ShopGoodsSearchRecommend")
	}

	forumIDs := make([]uint32, 0)

	for _, g := range recommendList.GetRecommend() {
		forumIDs = append(forumIDs, uint32(g.GetForumId()))
	}

	op.AdapterForumInfo = &foruminfo.Input{
		ForumIDs: forumIDs,
	}
	if err := ctx.RegisterInstance(&op.AdapterForumInfo); err != nil {
		return errors.New("foruminfo.OperatorForumInfoAdapterWrap register foruminfo.AdapterForumInfo error:" + err.Error())
	}

	opErr := ((*gen.OperatorForumInfoWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator(
		"shop_goods_search_recommend_forum_info", func() engine.Job {
			return &OperatorForumInfoAdapterWrap{}
		},
	)
	if err != nil {
		panic(err)
	}
}
