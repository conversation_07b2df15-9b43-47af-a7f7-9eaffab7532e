// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	foruminfo "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/foruminfo"
)

var _ = errors.New("")

// OperatorForumInfoWrap OperatorForumInfoWrap
type OperatorForumInfoWrap foruminfo.OperatorForumInfo

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorForumInfoWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.AdapterForumInfo = nil
		op.ForumInfo = nil
	}()

	ok0 := ctx.MutableInstance(&op.AdapterForumInfo)
	// op.AdapterForumInfo canLost="false"
	if !ok0 {
		return errors.New("foruminfo.OperatorForumInfo.AdapterForumInfo assign fail, type:foruminfo.AdapterForumInfo")
	}
	// op.AdapterForumInfo canNil="false"
	if op.AdapterForumInfo == nil {
		return errors.New("foruminfo.OperatorForumInfo.AdapterForumInfo is nil")
	}

	opErr := ((*foruminfo.OperatorForumInfo)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.ForumInfo canNil="false"
	if op.ForumInfo == nil {
		return errors.New("foruminfo.OperatorForumInfo register foruminfo.ForumInfo error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.ForumInfo); err != nil {
		return errors.New("foruminfo.OperatorForumInfo register foruminfo.ForumInfo error:" + err.Error())
	}

	return opErr
}
