// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	shopgoodssearchrecommend "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shopgoodssearchrecommend"
)

var _ = errors.New("")

// OperatorShopGoodsSearchRecommendWrap OperatorShopGoodsSearchRecommendWrap
type OperatorShopGoodsSearchRecommendWrap shopgoodssearchrecommend.OperatorShopGoodsSearchRecommend

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorShopGoodsSearchRecommendWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.ShopGoodsSearchRecommend = nil
	}()

	opErr := ((*shopgoodssearchrecommend.OperatorShopGoodsSearchRecommend)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	if err := ctx.RegisterInstance(&op.ShopGoodsSearchRecommend); err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "shopgoodssearchrecommend.OperatorShopGoodsSearchRecommend register shopgoodssearchrecommend.ShopGoodsSearchRecommend error:"+err.Error())
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("shop_goods_search_recommend", func() engine.Job {
		return &OperatorShopGoodsSearchRecommendWrap{}
	})
	if err != nil {
		panic(err)
	}
}
