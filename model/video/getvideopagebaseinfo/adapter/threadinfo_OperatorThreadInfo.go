package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/threadinfo"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/video/getvideopagebaseinfo/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/video/getvideopagebaseinfo/privateparams"
)

type OperatorThreadInfoAdapterWrap gen.OperatorThreadInfoWrap

func (op *OperatorThreadInfoAdapterWrap) DoImpl(ctx *engine.Context) error {
	var privateParams privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&privateParams)
	if !ok0 {
		return errors.New("threadinfo.OperatorThreadInfoAdapterWrap.privateParams assign fail, type:privateparams.PrivateParams")
	}
	threadID := privateParams.GetTID()

	op.AdapterThreadInfo = &threadinfo.Input{
		ThreadIDs: []uint64{threadID},
	}
	if err := ctx.RegisterInstance(&op.AdapterThreadInfo); err != nil {
		return errors.New("threadinfo.OperatorThreadInfoAdapterWrap register threadinfo.AdapterThreadInfo error:" + err.Error())
	}

	opErr := ((*gen.OperatorThreadInfoWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("get_video_page_base_info_thread_info", func() engine.Job {
		return &OperatorThreadInfoAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
