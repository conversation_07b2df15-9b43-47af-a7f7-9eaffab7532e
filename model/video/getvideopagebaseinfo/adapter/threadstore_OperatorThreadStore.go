package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/threadstore"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/video/getvideopagebaseinfo/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/video/getvideopagebaseinfo/privateparams"
)

type OperatorThreadStoreAdapterWrap gen.OperatorThreadStoreWrap

func (op *OperatorThreadStoreAdapterWrap) DoImpl(ctx *engine.Context) error {
	var privateParams privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&privateParams)
	if !ok0 {
		return errors.New("threadstore.OperatorThreadStoreAdapterWrap.privateParams assign fail, type:privateparams.PrivateParams")
	}
	threadID := privateParams.GetTID()

	op.AdapterThreadStore = &threadstore.Input{
		ThreadIDs: []uint64{threadID},
	}
	if err := ctx.RegisterInstance(&op.AdapterThreadStore); err != nil {
		return errors.New("threadstore.OperatorThreadStoreAdapterWrap register threadstore.AdapterThreadStore error:" + err.Error())
	}

	opErr := ((*gen.OperatorThreadStoreWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("get_video_page_base_info_thread_store", func() engine.Job {
		return &OperatorThreadStoreAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
