package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/threadinfo"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/userfollow"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/video/getvideopagebaseinfo/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/video/getvideopagebaseinfo/privateparams"
)

type OperatorUserFollowAdapterWrap gen.OperatorUserFollowWrap

func (op *OperatorUserFollowAdapterWrap) DoImpl(ctx *engine.Context) error {
	var privateParams privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&privateParams)
	if !ok0 {
		return errors.New("userfollow.OperatorUserFollowAdapterWrap.privateParams assign fail, type:privateparams.PrivateParams")
	}
	threadID := privateParams.GetTID()

	var threadInfo threadinfo.ThreadInfo
	ok1 := ctx.MutableInstance(&threadInfo)
	if !ok1 {
		return errors.New("userfollow.OperatorUserFollowAdapterWrap.threadInfo assign fail, type:threadinfo.ThreadInfo")
	}

	var commonParams commonparams.CommonParams
	ok2 := ctx.MutableInstance(&commonParams)
	if !ok2 {
		return errors.New("userfollow.OperatorUserFollowAdapterWrap.commonParams assign fail, type:commonparams.CommonParams")
	}

	thread, ok := threadInfo.GetRawThreads()[threadID]
	if !ok {
		return errors.New("userfollow.OperatorUserFollowAdapterWrap.threadInfo assign fail, type:threadinfo.ThreadInfo")
	}
	op.AdapterUserFollow = &userfollow.Input{
		Follow: &userfollow.QueryInfo{
			UserID:     commonParams.GetUserID(),
			ReqUserIDs: []uint64{uint64(thread.GetUserId())},
		},
	}
	if err := ctx.RegisterInstance(&op.AdapterUserFollow); err != nil {
		return errors.New("userfollow.OperatorUserFollowAdapterWrap register userfollow.AdapterUserFollow error:" + err.Error())
	}

	opErr := ((*gen.OperatorUserFollowWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("get_video_page_base_info_user_follow", func() engine.Job {
		return &OperatorUserFollowAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
