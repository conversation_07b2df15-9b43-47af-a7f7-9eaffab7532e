package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/threadinfo"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/useragreeinfo"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/video/getvideopagebaseinfo/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/video/getvideopagebaseinfo/privateparams"
)

type OperatorUserAgreeInfoAdapterWrap gen.OperatorUserAgreeInfoWrap

func (op *OperatorUserAgreeInfoAdapterWrap) DoImpl(ctx *engine.Context) error {
	var privateParams privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&privateParams)
	if !ok0 {
		return errors.New("useragreeinfo.OperatorUserAgreeInfoAdapterWrap.privateParams assign fail, type:privateparams.PrivateParams")
	}
	threadID := privateParams.GetTID()

	var threadInfo threadinfo.ThreadInfo
	ok1 := ctx.MutableInstance(&threadInfo)
	if !ok1 {
		return errors.New("useragreeinfo.OperatorUserAgreeInfoAdapterWrap.threadInfo assign fail, type:threadinfo.ThreadInfo")
	}

	thread, ok := threadInfo.GetRawThreads()[threadID]
	if !ok {
		return errors.New("useragreeinfo.OperatorUserAgreeInfoAdapterWrap.threadInfo assign fail, type:threadinfo.ThreadInfo")
	}

	op.AdapterUserAgreeInfo = &useragreeinfo.Input{
		PostIDs:  []uint64{thread.GetFirstPostId()},
		ThreadID: threadID,
	}
	if err := ctx.RegisterInstance(&op.AdapterUserAgreeInfo); err != nil {
		return errors.New("useragreeinfo.OperatorUserAgreeInfoAdapterWrap register useragreeinfo.AdapterUserAgreeInfo error:" + err.Error())
	}

	opErr := ((*gen.OperatorUserAgreeInfoWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("get_video_page_base_info_user_agree_info", func() engine.Job {
		return &OperatorUserAgreeInfoAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
