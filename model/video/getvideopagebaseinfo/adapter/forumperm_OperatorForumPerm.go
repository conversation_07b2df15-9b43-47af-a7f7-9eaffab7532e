package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/forumperm"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/threadinfo"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/video/getvideopagebaseinfo/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/video/getvideopagebaseinfo/privateparams"
)

type OperatorForumPermAdapterWrap gen.OperatorForumPermWrap

func (op *OperatorForumPermAdapterWrap) DoImpl(ctx *engine.Context) error {

	var privateParams privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&privateParams)
	if !ok0 {
		return errors.New("forumperm.OperatorForumPermAdapterWrap.privateParams assign fail, type:privateparams.PrivateParams")
	}
	threadID := privateParams.GetTID()

	var threadInfo threadinfo.ThreadInfo
	ok1 := ctx.MutableInstance(&threadInfo)
	if !ok1 {
		return errors.New("forumperm.OperatorForumPermAdapterWrap.threadInfo assign fail, type:threadinfo.ThreadInfo")
	}

	var commonParams commonparams.CommonParams
	ok2 := ctx.MutableInstance(&commonParams)
	if !ok2 {
		return errors.New("forumperm.OperatorForumPermAdapterWrap.commonParams assign fail, type:commonparams.CommonParams")
	}

	var forumID uint32
	if thread, ok := threadInfo.GetRawThreads()[threadID]; ok {
		forumID = thread.GetForumId()
	}

	op.AdapterForumPerm = &forumperm.Input{
		ForumID: forumID,
		UserID:  commonParams.GetUserID(),
		UserIP:  commonParams.GetIpStr(),
	}
	if err := ctx.RegisterInstance(&op.AdapterForumPerm); err != nil {
		return errors.New("forumperm.OperatorForumPermAdapterWrap register forumperm.AdapterForumPerm error:" + err.Error())
	}

	opErr := ((*gen.OperatorForumPermWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("get_video_page_base_info_forum_perm", func() engine.Job {
		return &OperatorForumPermAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
