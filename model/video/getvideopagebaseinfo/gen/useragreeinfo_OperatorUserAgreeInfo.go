// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	useragreeinfo "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/useragreeinfo"
)

var _ = errors.New("")

// OperatorUserAgreeInfoWrap OperatorUserAgreeInfoWrap
type OperatorUserAgreeInfoWrap useragreeinfo.OperatorUserAgreeInfo

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorUserAgreeInfoWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.AdapterUserAgreeInfo = nil
		op.UserAgreeInfo = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("useragreeinfo.OperatorUserAgreeInfo.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("useragreeinfo.OperatorUserAgreeInfo.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.AdapterUserAgreeInfo)
	// op.AdapterUserAgreeInfo canLost="false"
	if !ok1 {
		return errors.New("useragreeinfo.OperatorUserAgreeInfo.AdapterUserAgreeInfo assign fail, type:useragreeinfo.AdapterUserAgreeInfo")
	}
	// op.AdapterUserAgreeInfo canNil="false"
	if op.AdapterUserAgreeInfo == nil {
		return errors.New("useragreeinfo.OperatorUserAgreeInfo.AdapterUserAgreeInfo is nil")
	}

	opErr := ((*useragreeinfo.OperatorUserAgreeInfo)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.UserAgreeInfo canNil="false"
	if op.UserAgreeInfo == nil {
		return errors.New("useragreeinfo.OperatorUserAgreeInfo register useragreeinfo.UserAgreeInfo error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.UserAgreeInfo); err != nil {
		return errors.New("useragreeinfo.OperatorUserAgreeInfo register useragreeinfo.UserAgreeInfo error:" + err.Error())
	}

	return opErr
}
