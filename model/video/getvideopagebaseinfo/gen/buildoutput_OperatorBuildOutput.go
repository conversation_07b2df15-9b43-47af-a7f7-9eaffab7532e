// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	buildoutput "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/video/getvideopagebaseinfo/buildoutput"
)

var _ = errors.New("")

// OperatorBuildOutputWrap OperatorBuildOutputWrap
type OperatorBuildOutputWrap buildoutput.OperatorBuildOutput

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorBuildOutputWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.PrivateParams = nil
		op.ThreadInfo = nil
		op.ForumPerm = nil
		op.ThreadStore = nil
		op.UserAgreeInfo = nil
		op.UserFollow = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("buildoutput.OperatorBuildOutput.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("buildoutput.OperatorBuildOutput.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.PrivateParams)
	// op.PrivateParams canLost="false"
	if !ok1 {
		return errors.New("buildoutput.OperatorBuildOutput.PrivateParams assign fail, type:privateparams.PrivateParams")
	}
	// op.PrivateParams canNil="false"
	if op.PrivateParams == nil {
		return errors.New("buildoutput.OperatorBuildOutput.PrivateParams is nil")
	}

	ok2 := ctx.MutableInstance(&op.ThreadInfo)
	// op.ThreadInfo canLost="false"
	if !ok2 {
		return errors.New("buildoutput.OperatorBuildOutput.ThreadInfo assign fail, type:threadinfo.ThreadInfo")
	}
	// op.ThreadInfo canNil="false"
	if op.ThreadInfo == nil {
		return errors.New("buildoutput.OperatorBuildOutput.ThreadInfo is nil")
	}

	ok3 := ctx.MutableInstance(&op.ForumPerm)
	// op.ForumPerm canLost="false"
	if !ok3 {
		return errors.New("buildoutput.OperatorBuildOutput.ForumPerm assign fail, type:forumperm.ForumPerm")
	}
	// op.ForumPerm canNil="false"
	if op.ForumPerm == nil {
		return errors.New("buildoutput.OperatorBuildOutput.ForumPerm is nil")
	}

	ok4 := ctx.MutableInstance(&op.ThreadStore)
	// op.ThreadStore canLost="false"
	if !ok4 {
		return errors.New("buildoutput.OperatorBuildOutput.ThreadStore assign fail, type:threadstore.ThreadStore")
	}
	// op.ThreadStore canNil="false"
	if op.ThreadStore == nil {
		return errors.New("buildoutput.OperatorBuildOutput.ThreadStore is nil")
	}

	ok5 := ctx.MutableInstance(&op.UserAgreeInfo)
	// op.UserAgreeInfo canLost="false"
	if !ok5 {
		return errors.New("buildoutput.OperatorBuildOutput.UserAgreeInfo assign fail, type:useragreeinfo.UserAgreeInfo")
	}
	// op.UserAgreeInfo canNil="false"
	if op.UserAgreeInfo == nil {
		return errors.New("buildoutput.OperatorBuildOutput.UserAgreeInfo is nil")
	}

	ok6 := ctx.MutableInstance(&op.UserFollow)
	// op.UserFollow canLost="false"
	if !ok6 {
		return errors.New("buildoutput.OperatorBuildOutput.UserFollow assign fail, type:userfollow.UserFollow")
	}
	// op.UserFollow canNil="false"
	if op.UserFollow == nil {
		return errors.New("buildoutput.OperatorBuildOutput.UserFollow is nil")
	}

	opErr := ((*buildoutput.OperatorBuildOutput)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("get_video_page_base_info_build_output", func() engine.Job {
		return &OperatorBuildOutputWrap{}
	})
	if err != nil {
		panic(err)
	}
}
