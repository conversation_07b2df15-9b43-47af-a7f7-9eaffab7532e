// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	threadinfo "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/threadinfo"
)

var _ = errors.New("")

// OperatorThreadInfoWrap OperatorThreadInfoWrap
type OperatorThreadInfoWrap threadinfo.OperatorThreadInfo

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorThreadInfoWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.AdapterThreadInfo = nil
		op.ThreadInfo = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("threadinfo.OperatorThreadInfo.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("threadinfo.OperatorThreadInfo.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.AdapterThreadInfo)
	// op.AdapterThreadInfo canLost="false"
	if !ok1 {
		return errors.New("threadinfo.OperatorThreadInfo.AdapterThreadInfo assign fail, type:threadinfo.AdapterThreadInfo")
	}
	// op.AdapterThreadInfo canNil="false"
	if op.AdapterThreadInfo == nil {
		return errors.New("threadinfo.OperatorThreadInfo.AdapterThreadInfo is nil")
	}

	opErr := ((*threadinfo.OperatorThreadInfo)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.ThreadInfo canNil="false"
	if op.ThreadInfo == nil {
		return errors.New("threadinfo.OperatorThreadInfo register threadinfo.ThreadInfo error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.ThreadInfo); err != nil {
		return errors.New("threadinfo.OperatorThreadInfo register threadinfo.ThreadInfo error:" + err.Error())
	}

	return opErr
}
