// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	userfollow "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/userfollow"
)

var _ = errors.New("")

// OperatorUserFollowWrap OperatorUserFollowWrap
type OperatorUserFollowWrap userfollow.OperatorUserFollow

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorUserFollowWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.AdapterUserFollow = nil
		op.UserFollow = nil
	}()

	ok0 := ctx.MutableInstance(&op.AdapterUserFollow)
	// op.AdapterUserFollow canLost="false"
	if !ok0 {
		return errors.New("userfollow.OperatorUserFollow.AdapterUserFollow assign fail, type:userfollow.AdapterUserFollow")
	}
	// op.AdapterUserFollow canNil="false"
	if op.AdapterUserFollow == nil {
		return errors.New("userfollow.OperatorUserFollow.AdapterUserFollow is nil")
	}

	opErr := ((*userfollow.OperatorUserFollow)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	if err := ctx.RegisterInstance(&op.UserFollow); err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "userfollow.OperatorUserFollow register userfollow.UserFollow error:"+err.Error())
	}

	return opErr
}
