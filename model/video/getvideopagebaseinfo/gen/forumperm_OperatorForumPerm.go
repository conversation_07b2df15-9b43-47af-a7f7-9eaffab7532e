// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	forumperm "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/forumperm"
)

var _ = errors.New("")

// OperatorForumPermWrap OperatorForumPermWrap
type OperatorForumPermWrap forumperm.OperatorForumPerm

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorForumPermWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.AdapterForumPerm = nil
		op.ForumPerm = nil
	}()

	ok0 := ctx.MutableInstance(&op.AdapterForumPerm)
	// op.AdapterForumPerm canLost="false"
	if !ok0 {
		return errors.New("forumperm.OperatorForumPerm.AdapterForumPerm assign fail, type:forumperm.AdapterForumPerm")
	}
	// op.AdapterForumPerm canNil="false"
	if op.AdapterForumPerm == nil {
		return errors.New("forumperm.OperatorForumPerm.AdapterForumPerm is nil")
	}

	opErr := ((*forumperm.OperatorForumPerm)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.ForumPerm canNil="false"
	if op.ForumPerm == nil {
		return errors.New("forumperm.OperatorForumPerm register forumperm.ForumPerm error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.ForumPerm); err != nil {
		return errors.New("forumperm.OperatorForumPerm register forumperm.ForumPerm error:" + err.Error())
	}

	return opErr
}
