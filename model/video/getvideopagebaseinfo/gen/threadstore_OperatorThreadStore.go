// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	threadstore "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/threadstore"
)

var _ = errors.New("")

// OperatorThreadStoreWrap OperatorThreadStoreWrap
type OperatorThreadStoreWrap threadstore.OperatorThreadStore

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorThreadStoreWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.AdapterThreadStore = nil
		op.ThreadStore = nil
	}()

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("threadstore.OperatorThreadStore.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("threadstore.OperatorThreadStore.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.AdapterThreadStore)
	// op.AdapterThreadStore canLost="false"
	if !ok1 {
		return errors.New("threadstore.OperatorThreadStore.AdapterThreadStore assign fail, type:threadstore.AdapterThreadStore")
	}
	// op.AdapterThreadStore canNil="false"
	if op.AdapterThreadStore == nil {
		return errors.New("threadstore.OperatorThreadStore.AdapterThreadStore is nil")
	}

	opErr := ((*threadstore.OperatorThreadStore)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.ThreadStore canNil="false"
	if op.ThreadStore == nil {
		return errors.New("threadstore.OperatorThreadStore register threadstore.ThreadStore error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.ThreadStore); err != nil {
		return errors.New("threadstore.OperatorThreadStore register threadstore.ThreadStore error:" + err.Error())
	}

	return opErr
}
