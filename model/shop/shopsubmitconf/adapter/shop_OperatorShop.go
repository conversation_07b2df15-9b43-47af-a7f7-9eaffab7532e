package adapter

import (
	"errors"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shop"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/shop/shopsubmitconf/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopsubmitconf/privateparams"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
)

type OperatorShopAdapterWrap gen.OperatorShopWrap

func (op *OperatorShopAdapterWrap) DoImpl(ctx *engine.Context) error {
	var privateParams privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&privateParams)
	if !ok0 {
		return errors.New("shop.OperatorShopAdapterWrap.privateParams assign fail, type:privateparams.PrivateParams")
	}
	op.AdapterShop = &shop.Input{
		IsOnlyShop: true,
		ForumID:    privateParams.GetForumID(),
	}
	if err := ctx.RegisterInstance(&op.AdapterShop); err != nil {
		return errors.New("shop.OperatorShopAdapterWrap register shop.AdapterShop error:" + err.Error())
	}

	opErr := ((*gen.OperatorShopWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("shop_submit_conf_operator_shop", func() engine.Job {
		return &OperatorShopAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
