// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	buildoutput "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopsubmitconf/buildoutput"
)

var _ = errors.New("")

// OperatorBuildOutputWrap OperatorBuildOutputWrap
type OperatorBuildOutputWrap buildoutput.OperatorBuildOutput

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorBuildOutputWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.ShopInfo = nil
		op.ShopOpInfo = nil
		op.Output = nil
	}()

	// op.ShopInfo canLost="true"
	ctx.MutableInstance(&op.ShopInfo)

	// op.ShopOpInfo canLost="true"
	ctx.MutableInstance(&op.ShopOpInfo)

	opErr := ((*buildoutput.OperatorBuildOutput)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	if err := ctx.RegisterInstance(&op.Output); err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "buildoutput.OperatorBuildOutput register *buildoutput.Processor error:"+err.Error())
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("shop_submit_conf_build_output", func() engine.Job {
		return &OperatorBuildOutputWrap{}
	})
	if err != nil {
		panic(err)
	}
}
