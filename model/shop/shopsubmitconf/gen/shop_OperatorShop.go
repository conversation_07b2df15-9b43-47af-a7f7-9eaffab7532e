// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"

	shop "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shop"
)

var _ = errors.New("")

// OperatorShopWrap OperatorShopWrap
type OperatorShopWrap shop.OperatorShop

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorShopWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.CommonParams = nil
		op.AdapterShop = nil
		op.Shop = nil
	}()

	var cp commonparams.CommonParams
	ok := ctx.MutableInstance(&cp)
	if !ok {
		return errors.New("shop.OperatorShop get CommonParams fail")
	}

	cv := cp.GetClientVersion()

	if !(clientvers.CompareV2(cv, ">=", "12.82.0")) {
		tbcontext.TraceF(ctx.CallerCtx(), "shop.OperatorShop: version is not supported")
		return nil
	}

	ok0 := ctx.MutableInstance(&op.CommonParams)
	// op.CommonParams canLost="false"
	if !ok0 {
		return errors.New("shop.OperatorShop.CommonParams assign fail, type:commonparams.CommonParams")
	}
	// op.CommonParams canNil="false"
	if op.CommonParams == nil {
		return errors.New("shop.OperatorShop.CommonParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.AdapterShop)
	// op.AdapterShop canLost="false"
	if !ok1 {
		return errors.New("shop.OperatorShop.AdapterShop assign fail, type:shop.AdapterShop")
	}
	// op.AdapterShop canNil="false"
	if op.AdapterShop == nil {
		return errors.New("shop.OperatorShop.AdapterShop is nil")
	}

	opErr := ((*shop.OperatorShop)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	if err := ctx.RegisterInstance(&op.Shop); err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "shop.OperatorShop register shop.Shop error:"+err.Error())
	}

	return opErr
}
