// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	shopopinfo "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopsubmitconf/shopopinfo"
)

var _ = errors.New("")

// OperatorShopOpInfoWrap OperatorShopOpInfoWrap
type OperatorShopOpInfoWrap shopopinfo.OperatorShopOpInfo

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorShopOpInfoWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.ShopOpInfo = nil
	}()

	opErr := ((*shopopinfo.OperatorShopOpInfo)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	if err := ctx.RegisterInstance(&op.ShopOpInfo); err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "shopopinfo.OperatorShopOpInfo register shopopinfo.ShopOpInfo error:"+err.Error())
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("shop_submit_conf_shop_op_info", func() engine.Job {
		return &OperatorShopOpInfoWrap{}
	})
	if err != nil {
		panic(err)
	}
}
