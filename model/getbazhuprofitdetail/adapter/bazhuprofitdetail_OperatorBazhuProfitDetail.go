package adapter

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/bazhuprofitdetail"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/model/getbazhuprofitdetail/gen"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getbazhuprofitdetail/privateparams"
)

type OperatorBazhuProfitDetailAdapterWrap gen.OperatorBazhuProfitDetailWrap

func (op *OperatorBazhuProfitDetailAdapterWrap) DoImpl(ctx *engine.Context) error {
	var privateParams privateparams.PrivateParams
	ok0 := ctx.MutableInstance(&privateParams)
	if !ok0 {
		return errors.New("getbazhuprofitdetail.OperatorBazhuProfitDetailAdapterWrap.privateParams assign fail, type:privateparams.PrivateParams")
	}

	var commonParams commonparams.CommonParams
	ok1 := ctx.MutableInstance(&commonParams)
	if !ok1 {
		return errors.New("getbazhuprofitdetail.OperatorBazhuProfitDetailAdapterWrap.commonParams assign fail, type:commonParams.CommonParams")
	}

	op.AdapterBazhuProfitDetail = &bazhuprofitdetail.Input{
		ForumID: privateParams.GetForumID(),
		UserID:  commonParams.GetUserID(),
		Pn:      privateParams.GetPn(),
		Rn:      privateParams.GetRn(),
	}
	if err := ctx.RegisterInstance(&op.AdapterBazhuProfitDetail); err != nil {
		return errors.New("getbazhuprofitdetail.OperatorBazhuProfitDetailAdapterWrap register getbazhuprofitdetail.AdapterBazhuProfitDetail error:" + err.Error())
	}

	opErr := ((*gen.OperatorBazhuProfitDetailWrap)(op)).DoImpl(ctx)
	return opErr
}

func init() {
	err := engine.RegisterOperator("get_bazhu_profit_detail_list", func() engine.Job {
		return &OperatorBazhuProfitDetailAdapterWrap{}
	})
	if err != nil {
		panic(err)
	}
}
