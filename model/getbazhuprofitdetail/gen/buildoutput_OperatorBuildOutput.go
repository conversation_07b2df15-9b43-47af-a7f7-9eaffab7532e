// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	buildoutput "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getbazhuprofitdetail/buildoutput"
)

var _ = errors.New("")

// OperatorBuildOutputWrap OperatorBuildOutputWrap
type OperatorBuildOutputWrap buildoutput.OperatorBuildOutput

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorBuildOutputWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.PrivateParams = nil
		op.BazhuProfitDetail = nil
	}()

	ok0 := ctx.MutableInstance(&op.PrivateParams)
	// op.PrivateParams canLost="false"
	if !ok0 {
		return errors.New("buildoutput.OperatorBuildOutput.PrivateParams assign fail, type:privateparams.PrivateParams")
	}
	// op.PrivateParams canNil="false"
	if op.PrivateParams == nil {
		return errors.New("buildoutput.OperatorBuildOutput.PrivateParams is nil")
	}

	ok1 := ctx.MutableInstance(&op.BazhuProfitDetail)
	// op.BazhuProfitDetail canLost="false"
	if !ok1 {
		return errors.New("buildoutput.OperatorBuildOutput.BazhuProfitDetail assign fail, type:bazhuprofitdetail.BazhuProfitDetail")
	}
	// op.BazhuProfitDetail canNil="false"
	if op.BazhuProfitDetail == nil {
		return errors.New("buildoutput.OperatorBuildOutput.BazhuProfitDetail is nil")
	}

	opErr := ((*buildoutput.OperatorBuildOutput)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	return opErr
}

func init() {
	err := engine.RegisterOperator("get_bazhu_profit_detail_build_output", func() engine.Job {
		return &OperatorBuildOutputWrap{}
	})
	if err != nil {
		panic(err)
	}
}
