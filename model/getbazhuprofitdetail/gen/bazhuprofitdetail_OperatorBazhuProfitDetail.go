// Code generated by "dev-tools-cli"; DO NOT EDIT.
// Package gen 自动生成的算子包装代码，用于实现注入功能
package gen

import (
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	bazhuprofitdetail "icode.baidu.com/baidu/tieba-server-go/operator-common/operators/bazhuprofitdetail"
)

var _ = errors.New("")

// OperatorBazhuProfitDetailWrap OperatorBazhuProfitDetailWrap
type OperatorBazhuProfitDetailWrap bazhuprofitdetail.OperatorBazhuProfitDetail

// DoImpl 执行算子包装代码，将算子所依赖的对象导入到算子的属性中，并调用真实算子的DoImpl函数
func (op *OperatorBazhuProfitDetailWrap) DoImpl(ctx *engine.Context) error {
	defer func() {
		op.AdapterBazhuProfitDetail = nil
		op.BazhuProfitDetail = nil
	}()

	ok0 := ctx.MutableInstance(&op.AdapterBazhuProfitDetail)
	// op.AdapterBazhuProfitDetail canLost="false"
	if !ok0 {
		return errors.New("bazhuprofitdetail.OperatorBazhuProfitDetail.AdapterBazhuProfitDetail assign fail, type:bazhuprofitdetail.AdapterBazhuProfitDetail")
	}
	// op.AdapterBazhuProfitDetail canNil="false"
	if op.AdapterBazhuProfitDetail == nil {
		return errors.New("bazhuprofitdetail.OperatorBazhuProfitDetail.AdapterBazhuProfitDetail is nil")
	}

	opErr := ((*bazhuprofitdetail.OperatorBazhuProfitDetail)(op)).DoImpl(ctx)
	if opErr != nil {
		return opErr
	}

	// op.BazhuProfitDetail canNil="false"
	if op.BazhuProfitDetail == nil {
		return errors.New("bazhuprofitdetail.OperatorBazhuProfitDetail register bazhuprofitdetail.BazhuProfitDetail error: canNil=false")
	}

	if err := ctx.RegisterInstance(&op.BazhuProfitDetail); err != nil {
		return errors.New("bazhuprofitdetail.OperatorBazhuProfitDetail register bazhuprofitdetail.BazhuProfitDetail error:" + err.Error())
	}

	return opErr
}
