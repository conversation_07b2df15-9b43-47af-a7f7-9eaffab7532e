# app.toml: 应用主配置文件,对应结构体为 bootstrap.Config

# 应用名称，代码里可通过 env.AppName() 方法读取到
AppName = "GDP-Template"

# 运行模式，可配置值：
# debug    : 调试，    对应常量 env.RunModeDebug
# test     : 测试，    对应常量 env.RunModeTest
# release  : 线上发布， 对应常量 env.RunModeRelease
# 程序代码可以通过 env.RunMode() 获取该值
# 详见 http://gdp.baidu-int.com/api/baidu/gdp/env/
RunMode = "release"

# 逻辑 idc，通过配置派生赋值，详见 noahdes 目录
IDC = "{env.APPSPACE_IDC_NAME|bjtest}"

# 是否使用 baidu/gdp/hestia 提供的热重启功能
# 详见 http://wiki.baidu.com/pages/viewpage.action?pageId=1370968292
EnableHestia = false


# HTTPServer 的配置, 对应结构体为 httpserver.Config
[HTTPServer]
# 读 Header + Body 超时时间，ms，可选配置，若不配置，或者为0，将不超时
# 建议：内网 API 服务可以小一些；外网页面可大一些，避免弱网访问失败
# 若遇到读取 request.Body 失败，和此参数有关
# 请根据实际情况进行调整
ReadTimeout=1000

# 写超时时间（从请求读取完开始计算），ms，可选配置
# 应该配置成服务的最大允许时间
# 若使用超时中间件，超时中间件对应的超时时间不应该大于该值
# 若要使用 /debug/pprof 功能，请设置一个大于 30s 的值
# 请根据实际情况进行调整
WriteTimeout=40000 # 40s

# 空闲等待超时时间，ms，可选配置，若为0，会使用 ReadTimeout
# 当设置 keep-alives 开启时(HTTP Server 默认开启)，同一个 tcp 连接，读取下一个请求的等待时间
# 若 client 出现 connection reset by peer，可能和此参数有关
# 请根据实际情况进行调整
IdleTimeout=1000




# 其他配置配置内容建议写入单独的配置文件
