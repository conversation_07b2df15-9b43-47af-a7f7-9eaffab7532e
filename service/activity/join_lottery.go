package activity

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	json "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/codec"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/official"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/usertask"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/activity/joinLottery"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/constants"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type JoinLotteryIns struct {
	userID int64
	actID  int32
	objID  int32
	confs  map[string]string
	cuid   string
}

func NewJoinLottery(baseData *types.JoinLotteryBaseData) *JoinLotteryIns {
	obj := baseData.BaseObj
	if obj == nil {
		return &JoinLotteryIns{}
	}
	userID, _ := common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	cuid, _ := common.Tvttt(obj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
	return &JoinLotteryIns{
		userID: userID,
		cuid:   cuid,
	}
}

// Execute 执行抽奖
func (c *JoinLotteryIns) Execute(ctx context.Context, baseData *types.JoinLotteryBaseData, response *joinLottery.JoinLotteryResIdl) int {
	if baseData == nil || baseData.Request == nil {
		tbcontext.FatalF(ctx, "invalid baseData")
		return tiebaerror.ERR_PARAM_ERROR
	}
	if c.userID <= 0 {
		tbcontext.WarningF(ctx, "user id empty")
		return tiebaerror.ERR_PARAM_ERROR
	}
	req := baseData.Request
	if (req.GetProductId() <= 0 || req.GetPageKey() == "") &&
		(req.GetGameRelatedActId() <= 0 || req.GetGameRelatedTaskId() <= 0 || req.GetSceneId() <= 0 || req.GetTaskId() <= 0) {
		tbcontext.WarningF(ctx, "invalid param, input:["+common.ToString(req)+"]")
		return tiebaerror.ERR_PARAM_ERROR
	}

	var sceneID, taskID int32
	if req.GetGameRelatedActId() > 0 && req.GetGameRelatedTaskId() > 0 && req.GetSceneId() > 0 && req.GetTaskId() > 0 {
		//判断任务是否完成
		c.actID = req.GetGameRelatedActId()
		c.objID = req.GetGameRelatedTaskId()
		sceneID = req.GetSceneId()
		taskID = req.GetTaskId()

		// 判断是不是官方吧签到任务
		signStatus := officialSign(ctx, int64(c.actID), int64(c.objID), uint64(c.userID), response)
		if signStatus != -1 {
			return signStatus
		}
		//获取任务状态
		err, status := getUserTaskByID(ctx, int64(sceneID), int64(taskID), uint64(c.userID), c.cuid)
		if nil != err {
			tbcontext.WarningF(ctx, "get user task by id fail: %v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		if status != constants.UserTaskStatusNotFinish {
			tbcontext.WarningF(ctx, "user task not finish")
			return constants.JoinLotteryStatusNoTimes
		}

	} else {
		c.confs = getActConf(ctx, req.GetProductId(), req.GetPageKey())
		s, _ := c.confs["join_lottery_start_time"]
		start, _ := strconv.ParseInt(s, 10, 64)
		s, _ = c.confs["join_lottery_end_time"]
		end, _ := strconv.ParseInt(s, 10, 64)

		if time.Now().Unix() < start {
			tbcontext.WarningF(ctx, "not start")
			return constants.JoinLotteryStatusNotStart
		}
		if time.Now().Unix() > end {
			tbcontext.WarningF(ctx, "already end")
			return constants.JoinLotteryStatusAlreadyEnd
		}

		s, _ = c.confs["game_related_activity_id"]
		actID, _ := strconv.ParseInt(s, 10, 64)
		c.actID = int32(actID)
		s, _ = c.confs["game_related_task_id"]
		objID, _ := strconv.ParseInt(s, 10, 64)
		c.objID = int32(objID)

		if c.actID <= 0 || c.objID <= 0 {
			tbcontext.WarningF(ctx, "invalid actID or objID, input[%+v]", req)
			return tiebaerror.ERR_PARAM_ERROR
		}
	}

	input := map[string]any{
		"uid":     c.userID,
		"act_id":  c.actID,
		"obj_id":  c.objID,
		"product": "tieba",
		"from":    2,
	}
	body, err := json.Marshal(input)
	if err != nil {
		tbcontext.WarningF(ctx, "json marshal fail: %v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	ralReq := &ghttp.RalRequest{
		Header: map[string][]string{
			"Content-Type": {"application/json"},
		},
		Method: http.MethodPost,
		Path:   "/assemble/task/finish",
		Body:   bytes.NewReader(body),
	}
	var ralResData types.GameTaskFinishData
	ralRes := &ghttp.RalResponse{
		Data:    &ralResData,
		Decoder: codec.JSONDecoder,
	}
	err = ral.RAL(ctx, "game_activity", ralReq, ralRes)
	if ralResData.Errno == constants.JoinLotteryStatusNoTimes || len(ralResData.Data.More) == 0 {
		tbcontext.WarningF(ctx, "call game_activity error, no times, input[%+v], res[%+v], error %v",
			input, ralResData, err)
		return constants.JoinLotteryStatusNoTimes
	}
	if err != nil || ralResData.Errno != constants.JoinLotteryStatusSuccess {
		tbcontext.WarningF(ctx, "call game_activity error, input[%+v], res[%+v], error %v",
			input, ralResData, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	rData := ralResData.Data
	if rData.Data.HitIdx > 0 && int(rData.Data.HitIdx) >= len(rData.Data.Blocks) ||
		int(rData.Data.HitIdx) >= len(rData.More) {
		tbcontext.WarningF(ctx, "call game_activity error, idx out of range, input[%+v], res[%+v], error %v",
			input, ralResData, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	response.Data = formatRes(&ralResData)
	return tiebaerror.ERR_SUCCESS
}

// formatRes 格式化结果
func formatRes(ralResData *types.GameTaskFinishData) *joinLottery.JoinLotteryRes {
	if ralResData == nil {
		return nil
	}
	rData := ralResData.Data
	if int(rData.Data.HitIdx) >= len(rData.Data.Blocks) ||
		int(rData.Data.HitIdx) >= len(rData.More) {
		return nil
	}
	more := rData.More[rData.Data.HitIdx]
	block := rData.Data.Blocks[rData.Data.HitIdx]
	title := "恭喜获得"
	name := more.Name
	isDraw := true
	btns := []*joinLottery.AwardLink{
		{
			BtnDesc: proto.String("查看奖励"),
			BtnLink: proto.String(`tiebaapp://router?params={"type":1,"scheme":null,"url":"https://gamecenter.baidu.com/static/game/tb-welfare.html","extParams":{}}`),
		},
	}
	if more.Type == constants.AwardTypeThanks {
		title = "未中奖"
		name = "很遗憾，再试一次吧"
		isDraw = false
		btns = []*joinLottery.AwardLink{
			{
				BtnDesc: proto.String("我知道了"),
				BtnLink: proto.String("tiebaapp://router?params={\"type\":0,\"scheme\":null,\"url\":null,\"extParams\":{}}"),
			},
		}
	} else if more.Type == constants.AwardTypeGoods {
		btns = append(btns, &joinLottery.AwardLink{
			BtnDesc: proto.String("填写地址"),
			BtnLink: proto.String(`tiebaapp://router?params={"type":1,"scheme":null,"url":"https://gamecenter
.baidu.com/static/game/tb-welfare.html?from=lottery","extParams":{}}`),
		})
	}
	return &joinLottery.JoinLotteryRes{
		RewardInfo: &joinLottery.RewardInfo{
			AwardId:         proto.Int32(more.ID),
			AwardTitle:      proto.String(title),
			AwardName:       proto.String(name),
			AwardImage:      proto.String(block.Img),
			AwardCode:       proto.String(more.Data.Value),
			AwardLink:       btns,
			CanLotteryCount: proto.Int32(rData.Data.LimitTimesLeft),
			PrizePopBg:      proto.String(""),
			IsDraw:          proto.Bool(isDraw),
		},
	}
}

// getActConf 获取活动配置
func getActConf(ctx context.Context, productID int32, forumID string) map[string]string {
	wordlist := fmt.Sprintf("tb_wordlist_redis_activity_scene_config_%d_%s", productID, forumID)
	keys := []string{
		"game_related_activity_id",
		"game_related_task_id",
		"join_lottery_start_time",
		"join_lottery_end_time",
	}
	res, err := wordserver.QueryItems(ctx, wordlist, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "query wordlist failed, err=[%v], keys=[%v]", err, keys)
		return make(map[string]string)
	}
	return res
}

func getUserTaskByID(ctx context.Context, sceneID int64, taskID int64, userID uint64, cuid string) (error, int64) {
	getUserTaskListReq := usertask.GetUserTaskListReq{
		SceneId: &sceneID,
		TaskId:  &taskID,
		UserId:  &userID,
		Cuid:    &cuid,
	}

	getUserTaskListRes := &usertask.GetUserTaskListRes{}
	err := tbservice.Call(ctx, "usertask", "getUserTaskList", getUserTaskListReq, &getUserTaskListRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getUserTaskListRes.Errno == nil || getUserTaskListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:getAiInteractiveGamePlotByIDs fail, err:[%v], output:[%s], input[%s]",
			err, common.ToString(getUserTaskListRes), common.ToString(getUserTaskListReq))
		return fmt.Errorf("call usertask:getUserTaskList fail,err[%w]", err), 0
	}

	if getUserTaskListRes.Data == nil || getUserTaskListRes.Data.TaskList == nil {
		tbcontext.WarningF(ctx, "call usertask:getUserTaskList success, but data is nil, output:[%s], input[%s]",
			common.ToString(getUserTaskListRes), common.ToString(getUserTaskListReq))
		return nil, 0
	}

	taskInfo, ok := getUserTaskListRes.GetData().GetTaskList()[taskID]
	if !ok {
		tbcontext.WarningF(ctx, "call usertask:getUserTaskList success, but the taskInfo of the taskID[%d] is nil, output:[%s], input[%s]",
			taskID, common.ToString(getUserTaskListRes), common.ToString(getUserTaskListReq))
		return nil, 0
	}

	return nil, taskInfo.GetDotaskStatus()
}

func officialSign(ctx context.Context, actID int64, taskID int64, userID uint64, response *joinLottery.JoinLotteryResIdl) int {
	//先判断是不是签到任务并当天任务是不是可领取状态
	input := map[string]any{
		"task_id": taskID,
		"user_id": userID,
		"act_id":  actID,
		"product": "tieba",
	}
	body, err := json.Marshal(input)
	if err != nil {
		tbcontext.WarningF(ctx, "json marshal fail: %v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	ralReq := &ghttp.RalRequest{
		Header: map[string][]string{
			"Content-Type": {"application/json"},
		},
		Method: http.MethodPost,
		Path:   "/assemble/task/progress",
		Body:   bytes.NewReader(body),
	}
	ralResData := official.ActivitySignTaskRsp{}
	ralRes := &ghttp.RalResponse{
		Data:    &ralResData,
		Decoder: codec.JSONDecoder,
	}
	err = ral.RAL(ctx, "game_activity", ralReq, ralRes)
	if err != nil {
		tbcontext.WarningF(ctx, "call game_activity:assemble/task/progress fail, err:[%v]", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	//不是签到任务，继续原有逻辑
	if ralResData.GetData() != nil && ralResData.GetData().GetIsSign() != 1 {
		return -1
	}

	//没完成任务
	if ralResData.GetData().GetStatus() != 1 {
		return constants.JoinLotteryStatusNoTimes
	}

	//发奖
	inputOpenParams := map[string]any{
		"uid":    userID,
		"obj_id": taskID,
		"act_id": actID,
		"scene":  "officialforum",
	}
	openBody, err := json.Marshal(inputOpenParams)
	if err != nil {
		tbcontext.WarningF(ctx, "json marshal fail: %v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	ralOpenParamsReq := &ghttp.RalRequest{
		Header: map[string][]string{
			"Content-Type": {"application/json"},
		},
		Method: http.MethodPost,
		Path:   "/assemble/task/open",
		Body:   bytes.NewReader(openBody),
	}

	ralOpenResData := types.GameTaskOpenRes{}
	ralOpenRes := &ghttp.RalResponse{
		Data:    &ralOpenResData,
		Decoder: codec.JSONDecoder,
	}

	err = ral.RAL(ctx, "game_activity", ralOpenParamsReq, ralOpenRes)
	if err != nil || ralOpenResData.Errno != constants.JoinLotteryStatusSuccess || len(ralOpenResData.Data.RewardList) == 0 {
		tbcontext.WarningF(ctx, "call game_activity error, no times, input[%+v], res[%+v], error %v",
			ralOpenParamsReq, ralOpenResData, err)
		return constants.JoinLotteryStatusNoTimes
	}

	response.Data = formatSignRes(&ralOpenResData.Data)
	return tiebaerror.ERR_SUCCESS
}

func formatSignRes(ralResData *types.GameTaskOpenData) *joinLottery.JoinLotteryRes {
	if len(ralResData.RewardList) <= 0 {
		return &joinLottery.JoinLotteryRes{}
	}

	rewardInfo := ralResData.RewardList[0]
	title := "恭喜获得"
	btns := []*joinLottery.AwardLink{
		{
			BtnDesc: proto.String("查看奖励"),
			BtnLink: proto.String(`tiebaapp://router?params={"type":1,"scheme":null,"url":"https://gamecenter.baidu.com/static/game/tb-welfare.html","extParams":{}}`),
		},
	}
	return &joinLottery.JoinLotteryRes{
		RewardInfo: &joinLottery.RewardInfo{
			AwardId:         proto.Int32(cast.ToInt32(rewardInfo.RewardID)),
			AwardTitle:      proto.String(title),
			AwardName:       proto.String(rewardInfo.Name),
			AwardImage:      proto.String(rewardInfo.ImgURL),
			AwardCode:       proto.String(rewardInfo.Value),
			AwardLink:       btns,
			CanLotteryCount: proto.Int32(cast.ToInt32(rewardInfo.RewardNum)),
			PrizePopBg:      proto.String(""),
			IsDraw:          proto.Bool(true),
		},
	}
}
