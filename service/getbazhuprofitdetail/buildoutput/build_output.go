package buildoutput

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getforumleveldetail/privateparams"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getBazhuProfitDetail"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/bazhuprofitdetail"
)

// opName 算子名称
const opName = "buildoutput"

type ProcessBuildOutput struct {
	PrivateParams privateparams.PrivateParams

	BazhuProfitDetail bazhuprofitdetail.BazhuProfitDetail

	output *getBazhuProfitDetail.GetBazhuProfitDetailRes // 接口返回
}

func NewProcessBuildOutput() *ProcessBuildOutput {
	p := new(ProcessBuildOutput)
	p.output = &getBazhuProfitDetail.GetBazhuProfitDetailRes{
		Details: make([]*getBazhuProfitDetail.BazhuProfitDetail, 0),
	}
	return p
}

func (p *ProcessBuildOutput) Process(ctx context.Context) error {
	// 不可丢失核心数据校验
	if err := p.checkCoreData(); err != nil {
		return err
	}

	// 构建返回字段
	if err := p.buildOutput(ctx); err != nil {
		return err
	}

	return nil
}

// checkCoreData 不可丢失核心数据校验
func (p *ProcessBuildOutput) checkCoreData() error {
	if p.PrivateParams == nil {
		return fmt.Errorf("privateParams is nil")
	}

	if p.BazhuProfitDetail == nil {
		return fmt.Errorf("bazhuProfitDetail is nil")
	}

	return nil
}

// buildOutput 构建返回数据
func (p *ProcessBuildOutput) buildOutput(ctx context.Context) error {
	for _, val := range p.BazhuProfitDetail.GetBazhuProfitDetails() {
		p.output.Details = append(p.output.Details, &getBazhuProfitDetail.BazhuProfitDetail{
			Text:  proto.String(val.Text),
			Money: proto.Float64(val.Profit),
		})
	}
	p.output.RemainProfit = proto.Float64(p.BazhuProfitDetail.GetBazhuRemainProfit())
	p.output.TotalHistoricalProfit = proto.Float64(p.BazhuProfitDetail.GetBazhuTotalProfit())
	p.output.JumpUrl = proto.String(p.getJumpURL(ctx))
	p.output.HasMore = proto.Int32(p.BazhuProfitDetail.GetBazhuProfitDetailsHasMore())
	return nil
}

func (p *ProcessBuildOutput) getJumpURL(ctx context.Context) string {
	redisKeys := []string{"bazhu_profit_withdraw_schema"}
	redisRes, err := wordserver.QueryKeys(ctx, "tb_wordlist_redis_bazhu_mgr_tool_conf", redisKeys)
	if err != nil {
		tbcontext.WarningF(ctx, "get tb_wordlist_redis_bazhu_mgr_tool_conf fail: %v", err)
		return ""
	} else if len(redisRes) != len(redisKeys) {
		tbcontext.WarningF(ctx, "the query out len is not right: %d", len(redisRes))
		return ""
	}
	return redisRes[0]
}
