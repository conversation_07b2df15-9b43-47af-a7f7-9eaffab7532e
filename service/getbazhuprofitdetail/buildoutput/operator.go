/**
 * @Author: gongruiyang
 * @Description:
 * @File:  operator
 * @Date: 2025/02/10 16:49
 */

package buildoutput

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/bazhuprofitdetail"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getbazhuprofitdetail/privateparams"
)

// OperatorBuildOutput 算子定义
type OperatorBuildOutput struct {
	PrivateParams     privateparams.PrivateParams         `inject:"canLost=false,canNil=false"`
	BazhuProfitDetail bazhuprofitdetail.BazhuProfitDetail `inject:"canLost=false,canNil=false"`
}

func (o *OperatorBuildOutput) DoImpl(ctx *engine.Context) error {
	p := NewProcessBuildOutput()
	p.PrivateParams = o.PrivateParams
	p.BazhuProfitDetail = o.BazhuProfitDetail

	err := p.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	// 导出数据，供controller输出返回值使用
	ctx.MustRegisterInstance(p.output)
	return nil
}
