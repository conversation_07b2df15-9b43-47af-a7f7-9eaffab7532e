package recommend

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"golang.org/x/sync/errgroup"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	recomForumUtil "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/recommend"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tracecode"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/recommend/newRecommendCard"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	callfrom "icode.baidu.com/baidu/tieba-server-user-base/render/const"
	"icode.baidu.com/baidu/tieba-server-user-base/render/layers/page"
)

const UserInterestTopForumWordlist = "tb_wordlist_redis_user_interest_recomm"
const RecommFollewedFourmRecommendHeadText = "recomm_follewed_fourm_recommend_head_text"

const ForumFollowedAction = "forum_followed"

const (
	UserActivityLowStatus  = 1
	UserActivityMidStatus  = 2
	UserActivityHighStatus = 3

	RecomRecommendForumLow    = "recom_recommend_forum_low"    // 低活配置
	RecomRecommendForumMeidum = "recom_recommend_forum_meidum" // 中活配置
	RecomRecommendForumHigh   = "recom_recommend_forum_high"   // 高活配置

	RecommendForumCardNumKey = "recommend_forum_card_num" // 返回推荐吧的个数

	recommendTagEnterNumLimit = "recommend_tag_enter_num_limit"
	recommendNewScreeNumLimit = 2
)

func GetNewRecommendCard(ctx context.Context, clientType int, clientVersion, cuid string, userID int64,
	req *newRecommendCard.NewRecommendCardReq, response *newRecommendCard.NewRecommendCardRes, sampleIds []string) int {
	var errno int
	switch req.GetAction() {
	case ForumFollowedAction:
		errno = getForumFollowed(ctx, clientType, clientVersion, cuid, userID, req, response, sampleIds)
	}

	return errno
}

func getForumFollowed(ctx context.Context, clientType int, clientVersion, cuid string, userID int64,
	req *newRecommendCard.NewRecommendCardReq, response *newRecommendCard.NewRecommendCardRes, sampleIds []string) int {
	isHitNewStyle := false
	if clientvers.Compare("12.64", clientVersion) >= 0 && php2go.InArray("12_64_recommend_forum_a", sampleIds) {
		isHitNewStyle = true
	}
	// 12.66 新增需求，ios访问返回新样式
	// 实验由端上控制
	if clientType == clientvers.CLIENT_TYPE_IPHONE {
		isHitNewStyle = true
	}

	var err error
	multi := tbservice.Multi()

	forumIDs := req.GetActionParams()
	forumIDList := strings.Split(forumIDs, ",")
	forumIDInt := 0
	if len(forumIDList) < 1 {
		tbcontext.WarningF(ctx, "fail to get forumIDList val is %v", forumIDList)
		return tiebaerror.ERR_ALADING_INVALIDFORUM
	}
	forumID := forumIDList[0]
	forumIDInt, err = strconv.Atoi(forumID)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to get forumID val is %v", forumID)
		return tiebaerror.ERR_ALADING_INVALIDFORUM
	}

	recommendCardConfig, err := getNewRecommendCardConfig(ctx)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to getNewRecommendCardConfig err %v", err)
		recommendCardConfig.RecommendHeadText = "关注此吧的人也关注了"
	}

	getRecommendForum(ctx, multi, userID, int64(forumIDInt), 10)
	ret, err := multi.GetResult(ctx, "common::frsGetForumRecomList")
	if err != nil {
		tbcontext.WarningF(ctx, "call common::frsGetForumRecomList failed, err: %v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	forumRes, forumOk := ret.(*FrsGetForumRecomListRes)
	userInterestingList := make([]FrsGetForumRecomData, 0)
	if !forumOk || forumRes.Errno != errno.CodeSuccess {
		tbcontext.WarningF(ctx, "call common::getInterestCommit getInterestCommitRes failed, ret: %v", forumRes)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	userInterestingList = forumRes.Data

	if len(userInterestingList) < 1 {
		tbcontext.WarningF(ctx, "call common::getInterestCommit Res len is 0, req: %v,userID is %d", req, userID)
		return tiebaerror.ERR_SUCCESS
	}

	forumIDsUint64 := make([]uint64, 0)
	forumIDsMap := make(map[uint64]FrsGetForumRecomData)
	for _, data := range userInterestingList {
		var frsGetForumRecomData FrsGetForumRecomData
		frsGetForumRecomData = data
		forumIDsUint64 = append(forumIDsUint64, uint64(frsGetForumRecomData.ForumID))
		forumIDsMap[uint64(data.ForumID)] = frsGetForumRecomData
	}

	arrKeys := []string{
		RecomRecommendForumLow,
		RecomRecommendForumMeidum,
		RecomRecommendForumHigh,
		RecommendForumCardNumKey,
		recommendTagEnterNumLimit,
	}
	configResults, err := wordserver.QueryItemsNoPHPSerialized(ctx, UserInterestTopForumWordlist, arrKeys)
	if err != nil || configResults == nil {
		tbcontext.WarningF(ctx, "RecommendForumCard QueryItemsNoPHPSerialized failed, err:[%v]", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	recommendForumCardNumStr, _ := common.Tvttt(configResults[RecommendForumCardNumKey], common.TTT_STRING).(string)
	recommendForumCardNum, _ := strconv.Atoi(recommendForumCardNumStr)
	if recommendForumCardNumStr == "" || recommendForumCardNum <= 0 {
		// 展示的卡片数量为空,直接返回
		tbcontext.WarningF(ctx, "recommendForumCardNum recommend forum num is empty")
		recommendForumCardNum = 3
	}
	tagEnterNumLimit, _ := common.Tvttt(configResults[recommendTagEnterNumLimit], common.TTT_INT).(int)
	eg := new(errgroup.Group)
	var flag int64
	var finalRecommendForumIDs []uint64
	eg.Go(func() error {
		flag, finalRecommendForumIDs = getMultiOutPut(ctx, userID, 0, forumIDsUint64)
		return nil
	})
	recommendTags := make(map[int64]*recomForumUtil.ForumTags)
	if isHitNewStyle {
		// 获取吧标签
		eg.Go(func() error {
			fids := make([]int64, 0, len(forumIDsUint64))
			for _, fid := range forumIDsUint64 {
				fids = append(fids, int64(fid))
			}
			tags, err := recomForumUtil.GetForumRecommendTags(ctx, fids, resource.RedisSign)
			if err != nil {
				tbcontext.WarningF(ctx, "call recomForumUtil::GetForumRecommendTags error: %v", err)
			} else {
				recommendTags = tags
			}
			return nil
		})
	}
	_ = eg.Wait()

	configStr := ""
	switch flag {
	case UserActivityLowStatus:
		configStr = common.Tvttt(configResults[RecomRecommendForumLow], common.TTT_STRING).(string)
	case UserActivityMidStatus:
		configStr = common.Tvttt(configResults[RecomRecommendForumMeidum], common.TTT_STRING).(string)
	case UserActivityHighStatus:
		configStr = common.Tvttt(configResults[RecomRecommendForumHigh], common.TTT_STRING).(string)
	default:
		configStr = common.Tvttt(configResults[RecomRecommendForumLow], common.TTT_STRING).(string)
	}

	finalMap := make(map[uint64]struct{})
	for _, finalRecommendForumID := range finalRecommendForumIDs {
		finalMap[finalRecommendForumID] = struct{}{}
	}

	var forumInfoListRet = make(map[uint64]*client.RecommendForumInfo)
	for _, data := range userInterestingList {
		if _, ok := finalMap[uint64(data.ForumID)]; !ok {
			continue
		}

		re := regexp.MustCompile("[0-9]+")
		split := strings.Split(data.Desc, " ")
		if len(split) < 2 {
			continue
		}

		if (len(split[0]) < 0) || (len(split[1]) < 0) {
			continue
		}

		follewed := re.FindString(split[0])
		memberNum, _ := strconv.Atoi(follewed)
		forum := re.FindString(split[1])
		postNum, _ := strconv.Atoi(forum)

		if len(follewed) < 1 || len(forum) < 1 {
			continue
		}
		if split[0][len(split[0])-1] == 'W' {
			memberNum = memberNum * 10000
		}
		if split[1][len(split[1])-1] == 'W' {
			postNum = postNum * 10000
		}
		// 64版本新增标签
		var recommendTag []string
		slogan := data.Slogan
		if isHitNewStyle {
			if recommendTagsItem, ok := recommendTags[data.ForumID]; ok && nil != recommendTagsItem {
				// 感兴趣的吧标签
				class := recommendTagsItem.TopNForumClass
				if class != "" {
					recommendTag = append(recommendTag, class+"领域热门top5")
				}
				if enterNum := recommendTagsItem.EnterNum; enterNum > int64(tagEnterNumLimit) {
					recommendTag = append(recommendTag, fmt.Sprintf("昨日%s吧友进吧", recomForumUtil.FormatNumberText(enterNum)))
				}
			}
			// 64版本详细简介不为空展示详细简介
			if data.ForumDesc != "" {
				slogan = data.ForumDesc
			}
		}

		forumInfoListRet[uint64(data.ForumID)] = &client.RecommendForumInfo{
			Avatar:      proto.String(data.Avatars),
			ForumId:     proto.Uint64(uint64(data.ForumID)),
			ForumName:   proto.String(data.ForumName),
			IsLike:      proto.Uint32(0),
			MemberCount: proto.Uint32(uint32(memberNum)),
			ThreadCount: proto.Uint32(uint32(postNum)),
			Slogan:      proto.String(slogan),
			TagList:     recommendTag,
		}
	}

	// 生成page下的第一个组件
	// 最终结果组装
	var dataRes []*client.DiscoverTabCard
	var forumListTmp []*client.RecommendForumInfo

	for _, data := range userInterestingList {
		if _, ok := forumInfoListRet[uint64(data.ForumID)]; ok {
			forumListTmp = append(forumListTmp, forumInfoListRet[uint64(data.ForumID)])
		}
	}

	if len(forumListTmp) < recommendForumCardNum {
		tbcontext.WarningF(ctx, "call common::getInterestCommit Res len is less %v, req: %v,userID is %d", len(forumListTmp), req, userID)
		return tiebaerror.ERR_SUCCESS
	}
	if isHitNewStyle {
		// 命中新样式
		// 不满足倍数后的数据舍弃
		multiple := len(forumListTmp) / recommendNewScreeNumLimit
		// 小于一屏不展示
		if 0 == multiple {
			tbcontext.WarningF(ctx, "hit new style data len less %v ", len(forumListTmp))
			return tiebaerror.ERR_SUCCESS
		}
		forumListTmp = forumListTmp[:multiple*recommendNewScreeNumLimit]
	}

	dataRes = append(dataRes, &client.DiscoverTabCard{
		Name:      proto.String(recommendCardConfig.RecommendHeadText),
		ForumList: forumListTmp,
	})

	feedlist := &page.FeedlistOp{
		Callfrom: "recommend",
		Ctx:      ctx,
		CReq: callfrom.CommonReq{
			ClientType:    clientType,
			ClientVersion: clientVersion,
		},
		HotRecomTopForum: &client.DiscoverHotForum{
			TabList: dataRes,
		},
	}
	if isHitNewStyle {
		feedlist.HotRecomTopForum.ShowFrequency = proto.String(configStr)
		feedlist.HotRecomTopForum.ScreenMaxNum = proto.Int32(recommendNewScreeNumLimit)
		feedlist.HotRecomTopForum.IsHitNewStyle = proto.Bool(true)
	}

	pageData := &newRecommendCard.PageData{
		FeedList: feedlist.Render(),
	}

	logParam := getLogParam(ctx, cuid)

	response.Data.PageData = pageData
	response.Data.LogParam = logParam
	if !isHitNewStyle {
		response.Data.ShowFrequency = proto.String(configStr)
	}
	return tiebaerror.ERR_SUCCESS
}

func getMultiOutPut(ctx context.Context, uid, recommendForumCardNum int64, forumIDs []uint64) (flag int64, finallForumIDs []uint64) {
	flag = UserActivityMidStatus
	multi := tbservice.Multi()
	getUserActivityReq := &tbservice.Parameter{
		Service: "common",
		Method:  "getUserActivity",
		Input: map[string]int64{
			"uid": uid,
		},
		Output: &getUserActivityRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
			tbservice.WithCacheResult(),
		},
	}
	multi.Register(ctx, "getUserActivity", getUserActivityReq)

	filterRecommendForumParam := &tbservice.Parameter{
		Service: "common",
		Method:  "filterRecommendForum",
		Input: commonProto.FilterRecommendForumReq{
			Type:     proto.Uint64(1),
			NeedNum:  proto.Uint64(uint64(recommendForumCardNum)),
			ForumIds: forumIDs,
			UserId:   proto.Uint64(uint64(uid)),
		},
		Output: &commonProto.FilterRecommendForumRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
			tbservice.WithRalName("common_go"),
		},
	}
	multi.Register(ctx, "filterRecommendForum", filterRecommendForumParam)
	multi.Call(ctx)

	// 获取用户扩展信息获取
	actRes, err := multi.GetResult(ctx, "getUserActivity")
	if err != nil {
		tbcontext.WarningF(ctx, "call common getUserActivity  fail. the input is %v, err:%v", filterRecommendForumParam, err)
	} else {
		act, ok := actRes.(*getUserActivityRes)
		if !ok {
			tbcontext.WarningF(ctx, "parse getUserActivity error. data:%v", actRes)
		} else {
			if act.Errno != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "call common getUserActivity  fail. the input is %v, err:%v", filterRecommendForumParam, err)
			} else {
				intScore, err := strconv.Atoi(act.Output.ActScore)
				if err != nil {
					tbcontext.WarningF(ctx, "score fail:%v, err:%v", intScore, err)
				} else {
					flag = int64(intScore)
				}
			}

		}
	}

	filterRecommendForumRet, err := multi.GetResult(ctx, "filterRecommendForum")
	if err != nil {
		tbcontext.WarningF(ctx, "call common_go: filterRecommendForum failed, err: %v", err)
	}
	filterRecommendForumRes, filterRecommendForumOk := filterRecommendForumRet.(*commonProto.FilterRecommendForumRes)
	if !filterRecommendForumOk || filterRecommendForumRes == nil || filterRecommendForumRes.Errno == nil ||
		filterRecommendForumRes.GetErrno() != errno.CodeSuccess {
		tbcontext.WarningF(ctx, "call common_go: filterRecommendForum error: %v, res: %+v", err, filterRecommendForumRes)
	} else {

		if filterRecommendForumRes.GetData() != nil {
			finallForumIDs = filterRecommendForumRes.GetData().ForumIds
			return flag, finallForumIDs
		}
		tbcontext.WarningF(ctx, "call common_go: filterRecommendForumRes GetData is nil res: %+v", filterRecommendForumRes)
	}
	return flag, finallForumIDs
}

// 获取词表配置
func getNewRecommendCardConfig(ctx context.Context) (NewRecommendCardConfig, error) {
	var newRecommendCardConfig NewRecommendCardConfig
	// 词表获取
	values, err := wordserver.QueryKeys(ctx, UserInterestTopForumWordlist, []string{RecommFollewedFourmRecommendHeadText})
	if err != nil || len(values) < 1 {
		err = fmt.Errorf("wordserver QueryKey err %w, out :%+v", err, values)
		tbcontext.WarningF(ctx, "wordserver QueryKey err %v %v", err, values)
		return newRecommendCardConfig, err
	}
	if len(values) < 1 {
		tbcontext.WarningF(ctx, "wordserver QueryKey err %v", values)
		return newRecommendCardConfig, fmt.Errorf("wordserver QueryKey err %v", values)
	}
	recommendHeadText := values[0]
	newRecommendCardConfig.RecommendHeadText = recommendHeadText

	return newRecommendCardConfig, nil
}

// getLogParam 获取打点字段
func getLogParam(ctx context.Context, cuid string) []*client.FeedKV {
	tc, _ := tracecode.GetTracecode(ctx)
	return []*client.FeedKV{
		{
			Key:   proto.String("trace_code"),
			Value: proto.String(tc),
		},
		{
			Key:   proto.String("request_times"),
			Value: proto.String(strconv.FormatInt(int64(1), 10)),
		},
		{
			Key:   proto.String("refresh_type"),
			Value: proto.String(strconv.FormatInt(int64(1), 10)),
		},
		{
			Key:   proto.String("tieba_cuid"),
			Value: proto.String(cuid),
		},
		{
			Key:   proto.String("page"),
			Value: proto.String("home"),
		},
		{
			Key:   proto.String("tab"),
			Value: proto.String("home_recom"),
		},
		{
			Key:   proto.String("sub_tab"),
			Value: proto.String("all"),
		},
		{
			Key:   proto.String("rec_forum_card_scene"),
			Value: proto.String("homepage_focus"),
		},
	}
}

func getRecommendForum(ctx context.Context, multi *tbservice.MultiObj, uID int64, forumID int64, rn int64) {

	commonParam := &tbservice.Parameter{
		Service: "common",
		Method:  "frsGetForumRecomListNew",
		Input: map[string]any{
			"user_id":  uID,
			"forum_id": forumID,
			"rn":       rn,
		},
		Output: &FrsGetForumRecomListRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "common::frsGetForumRecomList", commonParam)
	multi.Call(ctx)

	return
}

type FrsGetForumRecomListRes struct {
	Errno  int                    `json:"errno"`
	Errmsg string                 `json:"errmsg"`
	Data   []FrsGetForumRecomData `json:"data"`
	Ie     string                 `json:"ie"`
}

type FrsGetForumRecomData struct {
	ForumID   int64  `json:"forum_id"`
	ForumName string `json:"forum_name"`
	Avatars   string `json:"avatar"`
	Slogan    string `json:"slogan"`
	Desc      string `json:"desc"`
	ForumDesc string `json:"forum_desc"`
}

type NewRecommendCardConfig struct {
	RecommendHeadText string `json:"recommend_head_text"`
}

type getUserActivityRes struct {
	commonRes
	Output struct {
		ActScore string `json:"actScore"`
	} `json:"output"`
}

type commonRes struct {
	Errno  int    `json:"errno"`
	Errmsg string `json:"errmsg"`
	Ie     string `json:"ie"`
}
