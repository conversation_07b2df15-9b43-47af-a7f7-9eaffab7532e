package privateparams

import (
	"context"
	"fmt"

	"github.com/spf13/cast"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
)

// opName 算子名称
const opName = "privateparams"

type Processor struct {
	baseAction *client.UIBaseAction

	pn                 uint32
	rn                 uint32
	tag                string
	searchType         string
	sampleID           string
	clientType         uint32
	clientVersion      string
	needRelatedTag     uint32
	needForumStaticTag uint32
	callFrom           string
	forumID            uint32
}

func NewProcessor() *Processor {
	p := new(Processor)
	return p
}

func (p *Processor) Process(ctx context.Context) error {
	p.pn = cast.ToUint32(p.baseAction.GetOriginalParam("pn"))
	p.rn = cast.ToUint32(p.baseAction.GetOriginalParam("rn"))
	p.tag = cast.ToString(p.baseAction.GetOriginalParam("tag"))
	p.searchType = cast.ToString(p.baseAction.GetOriginalParam("search_type"))
	p.needRelatedTag = cast.ToUint32(p.baseAction.GetOriginalParam("need_related_tag"))
	p.needForumStaticTag = cast.ToUint32(p.baseAction.GetOriginalParam("need_forum_static_tag"))
	p.callFrom = cast.ToString(p.baseAction.GetOriginalParam("call_from"))
	p.forumID = cast.ToUint32(p.baseAction.GetOriginalParam("forum_id"))

	// 参数校验
	if p.tag == "" || p.searchType == "" {
		p.baseAction.Error(tiebaerror.ERR_PARAM_ERROR, stcdefine.GetErrMsg(tiebaerror.ERR_PARAM_ERROR), true)
		return fmt.Errorf("params err, tag: %s, searchType: %s", p.tag, p.searchType)
	}

	if p.pn <= 0 {
		p.pn = 1
	}
	if p.rn <= 0 {
		p.rn = 10
	}
	return nil
}

func (p *Processor) GetPn() uint32 {
	return p.pn
}

func (p *Processor) GetRn() uint32 {
	return p.rn
}

func (p *Processor) GetTag() string {
	return p.tag
}

func (p *Processor) GetSearchType() string {
	return p.searchType
}

func (p *Processor) GetNeedRelatedTag() uint32 {
	return p.needRelatedTag
}

func (p *Processor) GetNeedForumStaticTag() uint32 {
	return p.needForumStaticTag
}

func (p *Processor) GetCallFrom() string {
	return p.callFrom
}

func (p *Processor) GetForumID() uint32 {
	return p.forumID
}
