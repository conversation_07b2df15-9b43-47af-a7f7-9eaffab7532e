package privateparams

// PrivateParams 私有参数算子 导出接口定义
type PrivateParams interface {
	// GetPn pn
	GetPn() uint32
	// GetRn rn
	GetRn() uint32
	// GetTag tag
	GetTag() string
	// GetSearchType searchType
	GetSearchType() string
	// GetNeedRelatedTag needRelatedTag
	GetNeedRelatedTag() uint32
	// GetNeedForumStaticTag needForumStaticTag
	GetNeedForumStaticTag() uint32
	// GetCallFrom callFrom
	GetCallFrom() string
	// GetForumID forumID
	GetForumID() uint32
}
