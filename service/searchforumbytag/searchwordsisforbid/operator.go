package searchwordsisforbid

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/searchforumbytag/privateparams"
)

type OperatorSearchWordsIsForbid struct {
	PrivateParams       privateparams.PrivateParams `inject:"canLost=false,canNil=false"`
	CommonParams        commonparams.CommonParams   `inject:"canLost=false,canNil=false"`
	SearchWordsIsForbid `extract:"canLost=false,canNil=false"`
}

func (op *OperatorSearchWordsIsForbid) DoImpl(ctx *engine.Context) error {
	searchWordsIsForbid := NewProcessSearchWordsIsForbid()
	searchWordsIsForbid.PrivateParams = op.PrivateParams
	searchWordsIsForbid.CommonParams = op.CommonParams

	err := searchWordsIsForbid.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	op.SearchWordsIsForbid = searchWordsIsForbid
	return nil
}
