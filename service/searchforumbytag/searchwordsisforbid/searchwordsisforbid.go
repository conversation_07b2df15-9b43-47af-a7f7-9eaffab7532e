package searchwordsisforbid

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/proto"

	antiProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/anti"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/searchforumbytag/privateparams"
)

type ProcessSearchWordsIsForbid struct {
	CommonParams        commonparams.CommonParams
	PrivateParams       privateparams.PrivateParams
	searchWordsIsForbid uint32
}

const (
	searchWordStatusNormal    = 1 // 不是禁搜词
	searchWordStatusForbidden = 2 // 是禁搜词
)

func (p *ProcessSearchWordsIsForbid) Process(ctx context.Context) error {
	if p.PrivateParams.GetTag() == "" {
		return nil
	}

	u, err := antiConfilterByWords(ctx, p.PrivateParams.GetTag())
	if err != nil {
		return err
	}
	p.searchWordsIsForbid = u
	return nil
}

func NewProcessSearchWordsIsForbid() *ProcessSearchWordsIsForbid {
	return new(ProcessSearchWordsIsForbid)
}

func (p *ProcessSearchWordsIsForbid) GetSearchWordsIsForbid() uint32 {
	return p.searchWordsIsForbid
}

// antiConfilterByWords 过禁搜词
// map[string]uint32 word->status 1表示正常 2表示是禁搜词
func antiConfilterByWords(ctx context.Context, word string) (uint32, error) {
	if len(word) == 0 {
		return searchWordStatusNormal, nil
	}
	multi := tbservice.Multi()
	antiParam := &tbservice.Parameter{
		Service: "anti_go",
		Method:  "antiConfilter",
		Input: &antiProto.AntiConfilterReq{
			Req: &antiProto.AntiConfilterReqInfo{
				ConfilterType: proto.String("Confilter"),
				Reqs: []*antiProto.AntiConfilteReqItem{
					{
						Groupid:        proto.Int32(-1),
						Content:        proto.String(word),
						ReturnPosition: proto.String("no"),
						NoNormalize:    proto.String("yes"),
						Dictlist:       proto.String("search_filter"),
					},
					{
						Groupid:        proto.Int32(-1),
						Content:        proto.String(word),
						ReturnPosition: proto.String("no"),
						NoNormalize:    proto.String("yes"),
						Dictlist:       proto.String("confilter_confilter_search_double"),
					},
					{
						Groupid:        proto.Int32(-1),
						Content:        proto.String(word),
						ReturnPosition: proto.String("no"),
						NoNormalize:    proto.String("yes"),
						Dictlist:       proto.String("search_double2"),
					},
				},
			},
		},
		Output: &antiProto.AntiConfilterRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
			tbservice.WithServiceName("anti"),
		},
	}
	multi.Register(ctx, fmt.Sprintf("antiConfilter::%s", word), antiParam)

	multi.Call(ctx)

	antiConfilterInter, err := multi.GetResult(ctx, fmt.Sprintf("antiConfilter::%s", word))
	if err != nil || antiConfilterInter == nil {
		tbcontext.WarningF(ctx, "call anti_go::antiConfilter fail, err:%v, input:%s, output:%s",
			err, word, common.ToString(antiConfilterInter))
		return searchWordStatusNormal, err
	}
	antiConfilterRes, ok := antiConfilterInter.(*antiProto.AntiConfilterRes)
	if err != nil || !ok || antiConfilterRes == nil || antiConfilterRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call anti_go::antiConfilter fail, err:%v, input:%s, output:%s",
			err, word, common.ToString(antiConfilterRes))
		return searchWordStatusNormal, err
	}

	for _, ans := range antiConfilterRes.GetRes().GetAns() {
		count := ans.GetCount()
		if count > 0 {
			return searchWordStatusForbidden, nil
		}
	}

	return searchWordStatusNormal, nil
}
