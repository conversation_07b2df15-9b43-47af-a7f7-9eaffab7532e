package buildoutput

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/getfidsbystatictag"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/gettagsbystatictag"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/searchforumbytag/privateparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/searchforumbytag/searchwordsisforbid"
)

type OperatorBuildOutput struct {
	BaseAction          *client.UIBaseAction                    `inject:"canLost=false,canNil=false"`
	CommonParams        commonparams.CommonParams               `inject:"canLost=false,canNil=false"`
	PrivateParams       privateparams.PrivateParams             `inject:"canLost=false,canNil=false"`
	RelatedTagList      gettagsbystatictag.GetTagsByStaticTag   `inject:"canLost=false,canNil=false"`
	ForumList           getfidsbystatictag.GetFidsByStaticTag   `inject:"canLost=false,canNil=false"`
	SearchWordsIsForbid searchwordsisforbid.SearchWordsIsForbid `inject:"canLost=false,canNil=false"`
}

func (o *OperatorBuildOutput) DoImpl(ctx *engine.Context) error {
	p := NewProcess()
	p.CommonParams = o.CommonParams
	p.PrivateParams = o.PrivateParams
	p.RelatedTagList = o.RelatedTagList
	p.ForumList = o.ForumList
	p.SearchWordsIsForbid = o.SearchWordsIsForbid

	err := p.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	// 导出数据，供controller输出返回值使用
	ctx.MustRegisterInstance(p.output)
	return nil
}
