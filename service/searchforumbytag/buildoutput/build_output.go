package buildoutput

import (
	"context"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/searchForumByTag"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/getfidsbystatictag"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/gettagsbystatictag"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/searchforumbytag/privateparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/searchforumbytag/searchwordsisforbid"
)

const opName = "buildoutput"

type Processor struct {
	CommonParams  commonparams.CommonParams
	PrivateParams privateparams.PrivateParams

	RelatedTagList      gettagsbystatictag.GetTagsByStaticTag
	ForumList           getfidsbystatictag.GetFidsByStaticTag
	SearchWordsIsForbid searchwordsisforbid.SearchWordsIsForbid
	output              *searchForumByTag.SearchForumByTagRes
}

func NewProcess() *Processor {
	p := new(Processor)
	p.output = &searchForumByTag.SearchForumByTagRes{
		RelatedTagList: make([]*searchForumByTag.RelatedTagList, 0),
		ForumList:      new(searchForumByTag.ForumList),
	}
	return p
}

func (p *Processor) Process(ctx context.Context) error {
	isForbid := p.SearchWordsIsForbid.GetSearchWordsIsForbid()
	if isForbid == 2 {
		return nil
	}
	p.output.RelatedTagList = p.RelatedTagList.GetTags()
	p.output.ForumList = p.ForumList.GetFids()
	return nil
}
