package home

import (
	"context"
	"github.com/goccy/go-json"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sidebar/home"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

//侧边栏内增加签到模块

type TaskList struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("sidebar_home_tasklist", func() engine.Job {
		return &TaskListOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewTaskList(ctx *engine.Context) *TaskList {
	return &TaskList{
		ctx: ctx,
	}
}

// IsValid 算子是否有效
func (t *TaskList) IsValid(ctx context.Context, baseData *types.SidebarHomeBaseData) bool {
	// 检查 baseData 是否为 nil
	if baseData == nil {
		tbcontext.WarningF(ctx, "baseData is nil in IsValid")
		return false
	}
	// 检查 baseData.BaseObj 是否为 nil
	if baseData.BaseObj == nil {
		tbcontext.WarningF(ctx, "baseData.BaseObj is nil in IsValid")
		return false
	}

	// 获取 user_id 和 cuid
	userID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	cuid := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
	clientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	// 检查 user_id 和 cuid 的有效性
	if userID <= 0 {
		tbcontext.WarningF(ctx, "Invalid user_id: %d in IsValid", userID)
		return false
	}

	if cuid == "" {
		tbcontext.WarningF(ctx, "Invalid cuid: %s in IsValid", cuid)
		return false
	}
	// 版本控制，确保客户端版本不低于12.78
	if clientvers.Compare(clientVersion, "12.78.0") > 0 {
		tbcontext.WarningF(ctx, "Client version too low: %s, requires 12.78 or higher", clientVersion)
		return false
	}
	return true
}

// Execute Execute主方法
func (t *TaskList) Execute(ctx context.Context, outData *home.SidebarHomeData, baseData *types.SidebarHomeBaseData) error {
	// 初始化 outData 如果为 nil
	if outData == nil {
		tbcontext.WarningF(ctx, "outData is nil, initializing...")
		outData = &home.SidebarHomeData{}
	}
	// 检查 baseData.BaseObj 是否为 nil
	if baseData.BaseObj == nil {
		tbcontext.WarningF(ctx, "baseData.BaseObj is nil")
		return errno.ErrCallServiceFail
	}
	userID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	cuid := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
	// 准备服务调用的参数
	getUserGrowthTaskListReq := map[string]interface{}{
		"user_id":                 userID,
		"cuid":                    cuid,
		"task_type":               2,
		"act_type":                "page_sign",
		"need_page_sign_progress": 1,
		"need_award_info":         1,
	}

	getUserGrowthTaskListRes := &commonProto.GetUserGrowthTaskListRes{}

	// 调用服务
	err := tbservice.Call(ctx, "common", "getUserGrowthTaskList", getUserGrowthTaskListReq, &getUserGrowthTaskListRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil {
		tbcontext.WarningF(ctx, "getUserTaskInfo call common::getUserGrowthTaskList fail, err=[%v]", err)
		return errno.ErrCallServiceFail
	}
	if getUserGrowthTaskListRes.Errno == nil || getUserGrowthTaskListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "getUserTaskInfo call common::getUserGrowthTaskList fail, err=[%v], output=[%s]", err, common.ToString(getUserGrowthTaskListRes))
		return errno.ErrCallServiceFail
	}

	// 构建响应数据
	taskList := getUserGrowthTaskListRes.GetData().GetDailyTask()
	var formattedTaskList []*client.CommonTaskInfo
	for _, task := range taskList {
		if task.GetActType() == "page_sign" {
			// 获取当前任务的进度
			var progress = int32(task.GetPageSignProgress().GetProgress())

			ext := task.GetExt()
			var extMap map[string]interface{}
			err := json.Unmarshal([]byte(ext), &extMap)
			if err != nil {
				tbcontext.WarningF(ctx, "解析 ext field error: %v", err)
				continue
			}
			// 提取 user_identify
			userIdentify, found := extMap["user_identify"].(float64)
			if !found {
				tbcontext.WarningF(ctx, "user_identify field not found or wrong type")
				continue
			}
			// 提取 award_info
			awardInfoMap, found := extMap["award_info"].(map[string]interface{})
			if !found {
				tbcontext.WarningF(ctx, "award_info field not found or wrong type")
				continue
			}
			// 添加日志以查看提取的 award_info
			tbcontext.WarningF(ctx, "新的Extracted award_info: %+v", awardInfoMap)
			pageSignAwards := make(map[string]interface{})
			for k, v := range awardInfoMap {
				awardDetail, ok := v.(string)
				if !ok {
					tbcontext.WarningF(ctx, "Error converting award_info for key %s to string", k)
					continue
				}
				var awardData map[string]interface{}
				if err := json.Unmarshal([]byte(awardDetail), &awardData); err != nil {
					tbcontext.WarningF(ctx, "Error parsing award_info for key %s: %v", k, err)
					continue
				}

				// 提取奖励信息并构建新的 map
				awardInfo := map[string]interface{}{
					"name":  awardData["name"],
					"icon":  awardData["icon"],
					"pic":   awardData["pic"],
					"type":  awardData["type"],
					"num":   awardData["num"],
					"award": awardData["award"],
				}
				pageSignAwards[k] = awardInfo
			}
			newExt := map[string]interface{}{
				"progress":         progress,
				"page_sign_awards": pageSignAwards,
				"user_identify":    int64(userIdentify),
			}

			formattedExt, _ := json.Marshal(newExt)
			if err != nil {
				tbcontext.WarningF(ctx, "重新编码 newExt 到 JSON 字符串时发生错误: %v", err)
				continue
			}

			formattedTask := &client.CommonTaskInfo{
				Id:                 proto.Int32(int32(task.GetId())),
				Name:               proto.String(task.GetName()),
				TaskIconUrl:        proto.String(task.GetTaskIconUrl()),
				Brief:              proto.String(task.GetBrief()),
				PlatActId:          proto.Int32(int32(task.GetPlatActId())),
				PlatTaskId:         proto.Int32(int32(task.GetPlatTaskId())),
				PlatTaskToken:      proto.String(task.GetPlatTaskToken()),
				PlatPlatformTypeId: proto.Int32(int32(task.GetPlatPlatformTypeId())),
				PlatActToken:       proto.String(task.GetPlatActToken()),
				SceneTaskKey:       proto.String(task.GetSceneTaskKey()),
				TaskType:           proto.Int32(task.GetTaskType()),
				Status:             proto.Int32(task.GetStatus()),
				Weight:             proto.Int32(task.GetWeight()),
				ActType:            proto.String(task.GetActType()),
				IsNeedActive:       proto.Int32(task.GetIsNeedActive()),
				IsPlatTask:         proto.Int32(task.GetIsPlatTask()),
				TargetNum:          proto.Int32(task.GetTargetNum()),
				IsContinuous:       proto.Int32(task.GetIsContinuous()),
				Month:              proto.Int32(task.GetMonth()),
				Week:               proto.Int32(task.GetWeek()),
				TimeInterval:       proto.Int32(task.GetTimeInterval()),
				ClientType:         proto.Int32(task.GetClientType()),
				SceneId:            proto.Int32(task.GetSceneId()),
				SceneSwitch:        proto.String(task.GetSceneSwitch()),
				SceneCallback:      proto.String(task.GetSceneCallback()),
				Comment:            proto.String(task.GetComment()),
				Ext:                proto.String(string(formattedExt)),
				CreateTime:         proto.Int32(int32(task.GetCreateTime())),
				UpdateTime:         proto.Int32(int32(task.GetUpdateTime())),
				ActivateToken:      proto.String(task.GetActivateToken()),
				DotaskStatus:       proto.Int32(task.GetDotaskStatus()),
				StartTime:          proto.Int32(int32(task.GetStartTime())),
				CompleteTime:       proto.Int32(int32(task.GetCompleteTime())),
				TaskProgress: &client.TaskProgress{
					Total:   proto.Int32(task.TaskProgress.GetTotal()),
					Current: proto.Int32(task.TaskProgress.GetCurrent()),
				},
			}
			formattedTask.Ext = proto.String(string(formattedExt))
			formattedTaskList = append(formattedTaskList, formattedTask)
		}

	}
	outData.TaskList = formattedTaskList
	return nil
}

type TaskListOperator struct {
}

func (rdop *TaskListOperator) DoImpl(ctx *engine.Context) error {
	var outData *home.SidebarHomeData
	var baseData *types.SidebarHomeBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewTaskList(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "sidebar_home_tasklist execute fail: %v", err)
		return err
	}
	return nil
}
