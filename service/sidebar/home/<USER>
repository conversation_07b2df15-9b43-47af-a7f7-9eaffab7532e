package home

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sidebar/home"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 获取会员入口信息

const (
	PROFILE_VIP_BANNER_NEAR_EXPIRES_DAY = "profile_banner_vip_near_expires_day" // 会员距过期不足N天 展示将于X天后过期
	PROFILE_VIP_BANNER_EXPIRES_DAY      = "profile_banner_vip_expires_day"      // 会员过期N天内 展示会员已过期N天
	PROFILE_VIP_BANNER_UN_VIP           = "profile_banner_un_vip_conf"
	ONE_DAY_SECOND                      = 86400
	PROFILE_VIP_BANNER_VIP_EXPIRE_gt    = "profile_banner_vip_expires_gt_conf_"
	PROFILE_VIP_BANNER_VIP_EXPIRE_lt    = "profile_banner_vip_expires_lt_conf"
	PROFILE_VIP_BANNER_BACKGROUND_DAY   = "profile_banner_vip_background_day"   // 背景图
	PROFILE_VIP_BANNER_BACKGROUND_NIGHT = "profile_banner_vip_background_night" // 背景图
	PROFILE_VIP_BANNER_VIP_EXPIRE       = "profile_banner_vip_expire_conf"
	PROFILE_VIP_BANNER_SWITCH           = "profile_vip_banner_switch"
)

type VipbannerInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("sidebar_home_vipbanner", func() engine.Job {
		return &VipbannerInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewVipbannerInfo(ctx *engine.Context) *VipbannerInfo {
	return &VipbannerInfo{
		ctx: ctx,
	}
}
func (a *VipbannerInfo) IsValid(ctx context.Context, baseData *types.SidebarHomeBaseData) bool {
	return true
}
func (a *VipbannerInfo) Execute(ctx context.Context, outData *home.SidebarHomeData, baseData *types.SidebarHomeBaseData) error {

	getVipBannerInfo(ctx, baseData.StaticField)
	outData.VipBanner = baseData.StaticField.VipBannerInfo
	return nil
}

type VipbannerInfoOperator struct {
}

func (rdop *VipbannerInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *home.SidebarHomeData
	var baseData *types.SidebarHomeBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewVipbannerInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "sidebar_home_vipbannerInfo execute fail: %v", err)
		return err
	}

	return nil
}

// 和profile接口逻辑一样  service/user/profile/core/response.go  getVipBannerInfo
func getVipBannerInfo(ctx context.Context, staticField *types.SidebarHomeStaticField) {
	// 获取会员临期、过期配置天数
	expiresDayConfig, err := wordserver.QueryKeys(ctx, "tb_wordlist_redis_member",
		[]string{PROFILE_VIP_BANNER_NEAR_EXPIRES_DAY, PROFILE_VIP_BANNER_EXPIRES_DAY})
	if err != nil {
		tbcontext.WarningF(ctx, "get wordlist tb_wordlist_redis_member expires day err: %v", err)
		return
	}
	nearExpiresDay, expiresDay := 10, 10 // 会员临期、过期时间节点 默认兜底 10 天
	if len(expiresDayConfig) == 2 {
		if day, err := strconv.Atoi(expiresDayConfig[0]); err == nil {
			nearExpiresDay = day
		}
		if day, err := strconv.Atoi(expiresDayConfig[1]); err == nil {
			expiresDay = day
		}
	}
	key := PROFILE_VIP_BANNER_UN_VIP
	endTimeStr, vipStr, day := "", "vip", 0 // 会员过期时间、VIP/SVIP、x天后到期/已过期x天
	expiresToday := false                   // 会员是否今天到期
	mParrProps := staticField.ArrUserInfo.GetMParrProps()
	vipInfo := staticField.ArrUserInfo.GetVipInfo()
	if mParrProps != nil && mParrProps.Level != nil && mParrProps.Level.PropsId != nil && mParrProps.Level.EndTime != nil {
		propsId := mParrProps.GetLevel().GetPropsId()
		endTimeUnix := int64(mParrProps.GetLevel().GetEndTime())
		endTime, _ := time.Parse("2006-01-02", time.Unix(endTimeUnix, 0).Format("2006-01-02"))
		nowTimeUnix := time.Now().Unix()
		nowTime, _ := time.Parse("2006-01-02", time.Unix(nowTimeUnix, 0).Format("2006-01-02"))
		if time.Unix(endTimeUnix, 0).Format("2006-01-02") == time.Unix(nowTimeUnix, 0).Format("2006-01-02") {
			expiresToday = true
		}
		if propsId >= 1 {
			if vipInfo != nil && vipInfo.GetVStatus() >= 2 {
				vipStr = "svip"
			}
			if endTimeUnix > nowTimeUnix {
				if int(endTime.Sub(nowTime).Hours()/24) > nearExpiresDay { // 会员非临期 VIP和SVIP配置不同
					key = PROFILE_VIP_BANNER_VIP_EXPIRE_gt + vipStr
				} else {
					key = PROFILE_VIP_BANNER_VIP_EXPIRE_lt
					day = int(endTime.Sub(nowTime).Hours() / 24)
				}
				endTimeStr = time.Unix(endTimeUnix, 0).Format("2006-01-02")
			} else if int(nowTime.Sub(endTime).Hours()/24) <= expiresDay { // 会员过期 且天数没有超过配置天数
				key = PROFILE_VIP_BANNER_VIP_EXPIRE
				day = int(nowTime.Sub(endTime).Hours() / 24)
			}
		}
	}

	res, err := wordserver.QueryKeys(ctx, "tb_wordlist_redis_member", []string{key, PROFILE_VIP_BANNER_SWITCH})
	if err != nil {
		tbcontext.WarningF(ctx, "get wordlist tb_wordlist_redis_member err: %v", err)
		return
	}

	if len(res) != 2 || res[1] == "" {
		return
	}

	staticField.VipBannerInfo = new(home.VipBanner)
	err = jsoniter.Unmarshal([]byte(res[0]), staticField.VipBannerInfo)
	if err != nil {
		tbcontext.WarningF(ctx, "Unmarshal err: %v", err)
		return
	}
	// 获取亮/暗模式默认背景
	backgroundConfig, err := wordserver.QueryKeys(ctx, "tb_wordlist_redis_member",
		[]string{PROFILE_VIP_BANNER_BACKGROUND_DAY, PROFILE_VIP_BANNER_BACKGROUND_NIGHT})
	if err != nil {
		tbcontext.WarningF(ctx, "get wordlist tb_wordlist_redis_member expires day err: %v", err)
		return
	}
	if len(backgroundConfig) == 2 {
		staticField.VipBannerInfo.BackgroundDay = proto.String(backgroundConfig[0])
		staticField.VipBannerInfo.BackgroundNight = proto.String(backgroundConfig[1])
	}

	// sub_tile字段 "有效期至%s" 会员非临期
	if strings.Contains(key, PROFILE_VIP_BANNER_VIP_EXPIRE_gt) {
		if len(staticField.VipBannerInfo.GetSubTitleList()) >= 1 &&
			strings.Contains(staticField.VipBannerInfo.GetSubTitleList()[0], "%s") {
			staticField.VipBannerInfo.GetSubTitleList()[0] = fmt.Sprintf(staticField.VipBannerInfo.GetSubTitleList()[0], endTimeStr)
		}
		if clientvers.Compare(staticField.ClientVersion, "12.56.0") > 0 {
			staticField.VipBannerInfo.ButtonLable = proto.String("立即续费")
		}
	}
	// title字段 "您的%s将于%s天后到期"/"您的%s已过期%s天", 会员临期/会员过期且天数没有超过配置天数 需要拼接该字段
	if key == PROFILE_VIP_BANNER_VIP_EXPIRE_lt || key == PROFILE_VIP_BANNER_VIP_EXPIRE {
		title := "尊贵的贴吧会员"
		if vipStr != "" && !expiresToday && strings.Contains(staticField.VipBannerInfo.GetTitle(), "%s") {
			title = fmt.Sprintf(staticField.VipBannerInfo.GetTitle(), strings.ToUpper(vipStr), strconv.Itoa(day))
		}
		if vipStr != "" && expiresToday && key == PROFILE_VIP_BANNER_VIP_EXPIRE_lt {
			title = fmt.Sprintf("您的%s将于今天到期", strings.ToUpper(vipStr))
		}
		if vipStr != "" && expiresToday && key == PROFILE_VIP_BANNER_VIP_EXPIRE {
			title = fmt.Sprintf("您的%s今天已到期", strings.ToUpper(vipStr))
		}
		staticField.VipBannerInfo.Title = proto.String(title)
		// 从词表获取配置的 vip icon pic
		vipIcon, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_member", fmt.Sprintf("%s_%s_icon", key, vipStr))
		if err != nil {
			tbcontext.WarningF(ctx, "get wordlist tb_wordlist_redis_member vipIcon err: %v", err)
		}
		if vipIcon != "" {
			staticField.VipBannerInfo.VipIcon = proto.String(vipIcon)
		}
	}
	buttonUrl := "https://tieba.baidu.com/mo/q/hybrid-business-vip/payPanel?nonavigationbar=1&customfullscreen=1&user_skin_overlay=0&jumptoforum=memberbuy"
	// 设置按钮默认链接 会员开通收银台链接
	if staticField.VipBannerInfo.ButtonUrl == nil || staticField.VipBannerInfo.GetButtonUrl() == "" || staticField.VipBannerInfo.GetButtonUrl() == buttonUrl {
		clientType := "ios"
		if staticField.ClientType == clientvers.CLIENT_TYPE_ANDROID {
			clientType = "andriod"
		}
		sceneIdStr, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_member", fmt.Sprintf("profile_scene_id_%s_%s", vipStr, clientType))
		if err != nil {
			tbcontext.WarningF(ctx, "get wordlist tb_wordlist_redis_member sceneIdStr err: %v", err)
		}
		if sceneIdStr != "" {
			buttonUrl = buttonUrl + "&scene_id=" + sceneIdStr
		}
		staticField.VipBannerInfo.ButtonUrl = proto.String(buttonUrl)
	}
	if clientvers.Compare(staticField.ClientVersion, "12.56.0") > 0 { // 56版本之前端上样式有问题 不下发该字段
		staticField.VipBannerInfo.Bubble = proto.String("")
	}
}
