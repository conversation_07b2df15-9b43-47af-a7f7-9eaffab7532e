package home

import (
	"context"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sidebar/home"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type GameCard struct {
	clientType    int32
	clientVersion string
	userID        uint64
}

// IsValid 算子是否有效
func (g *GameCard) IsValid(ctx context.Context, baseData *types.SidebarHomeBaseData) bool {
	g.clientType = cast.ToInt32(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 0))
	g.clientVersion = cast.ToString(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""))
	g.userID = cast.ToUint64(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0))

	if g.clientType != clientvers.CLIENT_TYPE_ANDROID || g.userID == 0 {
		// 不是安卓客户端不展示游戏历史记录
		return false
	}

	pureMode := cast.ToInt32(baseData.BaseObj.ObjRequest.GetCommonAttr("pure_mode", 0))
	if pureMode == 1 {
		tbcontext.WarningF(ctx, "pure_mode")
		return false
	}

	return true
}

// Execute 算子执行
func (g *GameCard) Execute(ctx context.Context, outData *home.SidebarHomeData, baseData *types.SidebarHomeBaseData) error {
	input := &commonProto.GetGameHistoryReq{
		UserId: proto.Uint64(g.userID),
		From:   proto.Int32(2),
	}
	output := &commonProto.GetGameHistoryRes{}
	err := tbservice.Call(ctx, "common", "getGameHistory", input, output,
		tbservice.WithConverter(tbservice.JSONITER), tbservice.WithRalName("common_go"))
	if err != nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call common::getGameHistory fail err:[%v], input:[%v], output:[%v]", err,
			common.ToString(input), common.ToString(output))
		return err
	}

	var miniGame []*home.MiniGameStr
	isShow := false
	for _, v := range output.GetData().GetAppInfos() {
		if v.GetIsRecommend() == 0 {
			isShow = true
		}
		miniGame = append(miniGame, &home.MiniGameStr{
			AppId:       proto.String(cast.ToString(v.GetAppId())),
			AppName:     proto.String(v.GetAppName()),
			AppType:     proto.Int32(v.GetAppType()),
			Icon:        proto.String(v.GetIcon()),
			ResourceKey: proto.String(v.GetResourceKey()),
			Source:      proto.Int32(v.GetSource()),
			IsRecommend: proto.Int32(v.GetIsRecommend()),
			ShowTag:     proto.String(v.GetShowTag()),
			JumpUrl:     proto.String(v.GetJumpUrl()),
		})
	}

	if isShow {
		outData.GameCard = &home.GameCard{
			MiniGame: miniGame,
		}
	}
	return nil
}

// DoImpl 算子实现
func (g *GameCard) DoImpl(ctx *engine.Context) error {
	var outData *home.SidebarHomeData
	var baseData *types.SidebarHomeBaseData
	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)

	if !g.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	if err := g.Execute(ctx.CallerCtx(), outData, baseData); err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "sidebar_home_game_card execute fail: %v", err)
		return err
	}

	return nil
}

func init() {
	if err := engine.RegisterOperator("sidebar_home_game_card", func() engine.Job {
		return &GameCard{}
	}); err != nil {
		panic(err)
	}
}
