package home

import (
	"context"
	"encoding/json"
	"github.com/spf13/cast"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	ptotocommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sidebar/home"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 获取历史浏览吧信息

type HistoryForum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("sidebar_home_historyforum", func() engine.Job {
		return &HistoryForumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewHistoryForum(ctx *engine.Context) *HistoryForum {
	return &HistoryForum{
		ctx: ctx,
	}
}

func (a *HistoryForum) IsValid(ctx context.Context, baseData *types.SidebarHomeBaseData) bool {
	// 没有传历史吧id，则不执行
	if len(baseData.Request.GetHistory()) == 0 {
		return false
	}
	return true
}

func (a *HistoryForum) Execute(ctx context.Context, outData *home.SidebarHomeData, baseData *types.SidebarHomeBaseData) error {
	// 解析入参，获取fid
	historyInfo := make([]map[string]any, 0)
	err := json.Unmarshal([]byte(baseData.Request.GetHistory()), &historyInfo)
	if err != nil {
		tbcontext.WarningF(ctx, "get history info fail: %v", err)
	}

	forumIds := make([]uint32, 0)
	for _, forumInfo := range historyInfo {
		if forumId, ok := forumInfo["forum_id"]; ok {
			forumIdInt := common.Tvttt(forumId, common.TTT_UINT32).(uint32)
			if forumIdInt > 0 {
				forumIds = append(forumIds, forumIdInt)
			}
		}
	}

	if len(forumIds) == 0 {
		return nil
	}

	// rpc获取吧信息和吧未读信息
	multi := tbservice.Multi()
	forumParam := &tbservice.Parameter{
		Service: "forum",
		Method:  "mgetBtxInfoEx",
		Input: map[string]any{
			"forum_id": forumIds,
		},
		Output: &forum.MgetBtxInfoExRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}

	multi.Register(ctx, "mgetBtxInfoEx", forumParam)

	officialForumParams := &tbservice.Parameter{
		Service: "common",
		Method:  "getOfficialForumIDList",
		Input: map[string]any{
			"pn":          1,
			"rn":          1000,
			"is_form_mis": 1,
		},
		Output: &ptotocommon.GetOfficialForumIDListRes{},
		Option: []tbservice.Option{},
	}
	multi.Register(ctx, "getOfficialForumIDList", officialForumParams)

	commonParam := &tbservice.Parameter{
		Service: "common",
		Method:  "getHotThread",
		Input: map[string]any{
			"history_info": historyInfo,
		},
		Output: &ptotocommon.GetHotThreadRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getHotThread", commonParam)
	multi.Call(ctx)

	forumResInter, err := multi.GetResult(ctx, "mgetBtxInfoEx")
	forumRes := forumResInter.(*forum.MgetBtxInfoExRes)
	if err != nil || forumRes.Errno == nil || forumRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "call forum::mgetBtxInfoEx fail, err: %v, res: %+v", err, forumRes)
		return errno.ErrCallServiceFail
	}

	hotThreadResInter, err := multi.GetResult(ctx, "getHotThread")
	hotThreadRes := hotThreadResInter.(*ptotocommon.GetHotThreadRes)
	if err != nil || hotThreadRes.Errno == nil || hotThreadRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call common::getHotThread fail, err: %v, res: %+v", err, forumRes)
		// 不阻塞整体流程
	}
	hotThreads := make(map[string][]*ptotocommon.GetHotThread)
	err = common.StructAToStructBCtx(ctx, hotThreadRes.GetData(), &hotThreads)
	if err != nil {
		tbcontext.WarningF(ctx, "StructAToStructBCtx fail, err: %v, res: %+v", err, hotThreadRes.GetData())
		// 不阻塞整体流程
	}

	// 官方吧信息
	officialListsInter, err := multi.GetResult(ctx, "getOfficialForumIDList")
	officialLists, ok := officialListsInter.(*ptotocommon.GetOfficialForumIDListRes)
	if !ok || err != nil || officialLists == nil || officialLists.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call common::getOfficialForumIDList fail, err: %v, res: %+v", err, officialLists)
		// 不阻塞整体流程
	}

	if officialLists.GetData() == nil || officialLists.GetData().GetForumIds() == nil || len(officialLists.GetData().GetForumIds()) == 0 {
		tbcontext.WarningF(ctx, "call common::getOfficialForumIDList fail, err: %v, res: %+v", err, officialLists)
		// 不阻塞整体流程
	}

	officialFidMap := make(map[uint32]struct{})
	for _, officialFid := range officialLists.GetData().GetForumIds() {
		officialFidMap[officialFid] = struct{}{}
	}

	// 关吧过滤
	fnames := make([]string, 0, len(forumRes.GetOutput()))
	for _, finfo := range forumRes.GetOutput() {
		fnames = append(fnames, finfo.GetForumName().GetForumName())
	}
	input := &forum.GetFidByFnameReq{
		QueryWords: fnames,
	}
	forbiddenRes := new(forum.GetFidByFnameRes)
	err = tbservice.Call(ctx, "forum", "getFidByFname", input, forbiddenRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || forbiddenRes.Errno == nil || forbiddenRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "call forum::getFidByFname fail, input:%v, output:%v, err: %v", input, forbiddenRes, err)
		return errno.ErrCallServiceFail
	}

	// 由于部分吧fid有可能为0 所以用吧名称作为key
	forbiddenMap := make(map[string]bool, len(fnames))
	for _, forbiddenInfo := range forbiddenRes.GetForumId() {
		if forbiddenInfo.GetIsForbidden() == 1 {
			forbiddenMap[forbiddenInfo.GetQword()] = true
		} else {
			forbiddenMap[forbiddenInfo.GetQword()] = false
		}
	}

	// 拼接数据
	outData.HistoryForum = make([]*client.HistoryForumInfo, 0, len(forumIds))
	for _, fid := range forumIds {
		metaInfo, ok := forumRes.GetOutput()[fid]
		if !ok {
			continue
		}

		// 过滤被关闭的吧
		if forbidden, ok := forbiddenMap[metaInfo.GetForumName().GetForumName()]; ok && forbidden {
			tbcontext.TraceF(ctx, "forum[id:%d name:%s] is forbidden", fid, metaInfo.GetForumName().GetForumName())
			continue
		}

		forumInfo := &client.HistoryForumInfo{
			ForumId:   proto.Int64(int64(metaInfo.GetForumName().GetForumId())),
			ForumName: proto.String(metaInfo.GetForumName().GetForumName()),
			Avatar:    proto.String(metaInfo.GetCard().GetAvatar()),
		}
		forumIdStr := common.Tvttt(fid, common.TTT_STRING).(string)
		if value, ok := hotThreads[forumIdStr]; ok {
			forumInfo.UnreadNum = proto.Int32(int32(len(value)))
		}

		// 官方吧标识
		if _, ok := officialFidMap[cast.ToUint32(metaInfo.GetForumName().GetForumId())]; ok {
			forumInfo.IsOfficialForum = proto.Int32(1)
		} else {
			forumInfo.IsOfficialForum = proto.Int32(0)
		}

		outData.HistoryForum = append(outData.HistoryForum, forumInfo)
	}

	return nil
}

type HistoryForumOperator struct {
}

func (rdop *HistoryForumOperator) DoImpl(ctx *engine.Context) error {
	var outData *home.SidebarHomeData
	var baseData *types.SidebarHomeBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewHistoryForum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "sidebar_home_historyforum execute fail: %v", err)
		return err
	}

	return nil
}
