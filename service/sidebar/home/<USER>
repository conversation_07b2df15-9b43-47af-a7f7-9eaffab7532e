package home

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"runtime/debug"
	"sync"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	protoCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sidebar/home"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 获取功能列表

type ToolsInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("sidebar_home_toolsinfo", func() engine.Job {
		return &ToolsInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewToolsInfo(ctx *engine.Context) *ToolsInfo {
	return &ToolsInfo{
		ctx: ctx,
	}
}
func (a *ToolsInfo) IsValid(ctx context.Context, baseData *types.SidebarHomeBaseData) bool {
	return true
}
func (a *ToolsInfo) Execute(ctx context.Context, outData *home.SidebarHomeData, baseData *types.SidebarHomeBaseData) error {
	staticField := baseData.StaticField
	err := a.getConfig(ctx, baseData, staticField)
	if err != nil {
		return err
	}

	if len(staticField.NeedTipsToolsList) > 0 {
		err := a.getToolsTips(ctx, staticField)
		if err != nil {
			// 这里只打日志，不阻塞流程
			tbcontext.WarningF(ctx, "getToolsTips fail, err:%v", err)
		}
	}

	// 构建返回值
	resTools := new(home.ToolList)
	// 所有功能列表
	resTools.AllToolsList = make([]*home.ToolBaseInfo, 0, len(staticField.AllToolsMap))
	for _, val := range staticField.AllToolsMap {
		resTools.AllToolsList = append(resTools.AllToolsList, &home.ToolBaseInfo{
			Title:      proto.String(val.Title),
			ActionUri:  proto.String(val.ActionURI),
			ActionType: proto.String(val.ActionType),
			ClassName:  proto.String(val.Class),
			Tips:       proto.String(val.Tips),
			IconDay:    proto.String(val.IconDay),
			IconNight:  proto.String(val.IconNight),
			Scheme:     proto.String(val.Scheme),
			ExtParams:  proto.String(val.ExtParams),
		})
	}

	// 运营位功能列表
	resTools.YunyingToolsList = make([]*home.ToolBaseInfo, 0, len(staticField.YunyingToolNames))
	for _, name := range staticField.YunyingToolNames {
		if val, ok := staticField.AllToolsMap[name]; ok {
			resTools.YunyingToolsList = append(resTools.YunyingToolsList, &home.ToolBaseInfo{
				Title:      proto.String(val.Title),
				ActionUri:  proto.String(val.ActionURI),
				ActionType: proto.String(val.ActionType),
				ClassName:  proto.String(val.Class),
				Tips:       proto.String(val.Tips),
				IconDay:    proto.String(val.IconDay),
				IconNight:  proto.String(val.IconNight),
				Scheme:     proto.String(val.Scheme),
				ExtParams:  proto.String(val.ExtParams),
			})
		}
	}

	// 常用功能列表
	resTools.CommonToolsList = make([]*home.ToolBaseInfo, 0, len(staticField.CommonToolNameList))
	for _, name := range staticField.CommonToolNameList {
		if val, ok := staticField.AllToolsMap[name]; ok {
			resTools.CommonToolsList = append(resTools.CommonToolsList, &home.ToolBaseInfo{
				Title:      proto.String(val.Title),
				ActionUri:  proto.String(val.ActionURI),
				ActionType: proto.String(val.ActionType),
				ClassName:  proto.String(val.Class),
				Tips:       proto.String(val.Tips),
				IconDay:    proto.String(val.IconDay),
				IconNight:  proto.String(val.IconNight),
				Scheme:     proto.String(val.Scheme),
				ExtParams:  proto.String(val.ExtParams),
			})
		}
	}

	// 分类展示功能列表
	resTools.ShowToolsList = make([]*home.ClassToolsInfo, 0, len(staticField.ShowToolClass))
	for _, className := range staticField.ShowToolClass {
		tools, ok := staticField.ToolClassMap[className]
		if !ok {
			continue
		}
		toolsList := make([]*home.ToolBaseInfo, 0, len(tools))
		for _, val := range tools {
			toolsList = append(toolsList, &home.ToolBaseInfo{
				Title:      proto.String(val.Title),
				ActionUri:  proto.String(val.ActionURI),
				ActionType: proto.String(val.ActionType),
				ClassName:  proto.String(val.Class),
				Tips:       proto.String(val.Tips),
				IconDay:    proto.String(val.IconDay),
				IconNight:  proto.String(val.IconNight),
				Scheme:     proto.String(val.Scheme),
				ExtParams:  proto.String(val.ExtParams),
			})
		}
		resTools.ShowToolsList = append(resTools.ShowToolsList, &home.ClassToolsInfo{
			ClassName: proto.String(className),
			ToolsList: toolsList,
		})
	}
	outData.Tools = resTools

	return nil
}

type ToolsInfoOperator struct {
}

func (rdop *ToolsInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *home.SidebarHomeData
	var baseData *types.SidebarHomeBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewToolsInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "sidebar_home_toolsinfo execute fail: %v", err)
		return err
	}

	return nil
}

func (a *ToolsInfo) getConfig(ctx context.Context, baseData *types.SidebarHomeBaseData, staticField *types.SidebarHomeStaticField) error {
	keys := []string{
		"all_tools_list",          // 所有的功能列表
		"yunying_tools_name_list", // 运营位功能名称
		"show_tool_class",         // 需要展示的功能类别
		"common_tools_name_list",  // 常用功能名称列表
	}

	values, err := wordserver.QueryKeys(ctx, types.SideBarConfTableName, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], err=[%v]", types.SideBarConfTableName, keys, err)
		return errno.ErrCallServiceFail
	}
	// 如果数量不对，也认为失败返回错误
	if len(values) != len(keys) {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], values=[%v]", types.SideBarConfTableName, keys, values)
		return errno.ErrCallServiceFail
	}

	// 解析数据
	allToolsList := []*types.SidebarToolInfo{}
	err = jsoniter.UnmarshalFromString(values[0], &allToolsList)
	if err != nil {
		tbcontext.WarningF(ctx, "unmarshal allToolsList fail, err=[%v]", err)
		return errno.ErrCallServiceFail
	}

	yunyingToolNameList := []string{}
	// 运营位不一定配，所以判断一下是否为空
	if len(values[1]) > 0 {
		err = jsoniter.UnmarshalFromString(values[1], &yunyingToolNameList)
		if err != nil {
			tbcontext.WarningF(ctx, "unmarshal yunyingToolNameList fail, err=[%v]", err)
			return errno.ErrCallServiceFail
		}
	}
	showToolClass := []string{}
	err = jsoniter.UnmarshalFromString(values[2], &showToolClass)
	if err != nil {
		tbcontext.WarningF(ctx, "unmarshal showToolClass fail, err=[%v]", err)
		return errno.ErrCallServiceFail
	}

	commonToolNameList := []string{}
	// 常用功能也不一定配，所以判断一下是否为空
	if len(values[3]) > 0 {
		err = jsoniter.UnmarshalFromString(values[3], &commonToolNameList)
		if err != nil {
			tbcontext.WarningF(ctx, "unmarshal commonToolNameList fail, err=[%v]", err)
			return errno.ErrCallServiceFail
		}
	}

	for _, toolInfo := range allToolsList {
		if staticField.PureMode == 1 && toolInfo.NeedFilter == 1 {
			// 审核态模式下过滤掉不需要展示的功能
			continue
		}
		// 12.72以下版本 我的中奖 不展示
		if toolInfo.Title == "我的中奖" {
			if baseData != nil {
				clientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
				if clientvers.CompareV2(clientVersion, "<", "12.72") {
					continue
				}

			}
		}
		staticField.AllToolsMap[toolInfo.Title] = toolInfo
		if _, ok := staticField.ToolClassMap[toolInfo.Class]; ok {
			staticField.ToolClassMap[toolInfo.Class] = append(staticField.ToolClassMap[toolInfo.Class], toolInfo)
		} else {
			staticField.ToolClassMap[toolInfo.Class] = []*types.SidebarToolInfo{toolInfo}
		}
		if toolInfo.NeedExTips == 1 {
			staticField.NeedTipsToolsList = append(staticField.NeedTipsToolsList, toolInfo)
		}
	}

	staticField.YunyingToolNames = yunyingToolNameList
	staticField.ShowToolClass = showToolClass
	staticField.CommonToolNameList = commonToolNameList
	return nil
}

func (a *ToolsInfo) getToolsTips(ctx context.Context, staticField *types.SidebarHomeStaticField) error {
	if len(staticField.NeedTipsToolsList) == 0 {
		return nil
	}

	subWg := &sync.WaitGroup{}

	for _, toolInfo := range staticField.NeedTipsToolsList {
		switch toolInfo.Title {
		case "贴贝商城":
			subWg.Add(1)
			go func(tool *types.SidebarToolInfo) {
				defer func() {
					subWg.Done()
					if e := recover(); e != nil {
						tbcontext.WarningF(ctx, "deal tiebei_mall fail, err:=[%v], stack:=[%v]", e, debug.Stack())
					}
				}()
				err := getTMoneyInfo(ctx, tool, staticField.UserID)
				if err != nil {
					tbcontext.WarningF(ctx, "call getTMoneyInfo fail, tool=[%+v], userID=[%d]", tool, staticField.UserID)
				}
			}(toolInfo)
			break
		}
	}

	subWg.Wait()
	return nil
}

func getTMoneyInfo(ctx context.Context, toolInfo *types.SidebarToolInfo, userID uint64) error {
	if userID <= 0 {
		return nil
	}
	commonInput := map[string]interface{}{
		"user_id": userID,
	}
	commonOut := &protoCommon.GetUserTmoneyRes{}
	err := tbservice.Call(ctx, "common", "getUserTmoney", commonInput, &commonOut, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service common:getUserTmoney, err = %v, input = %v,output = %v",
			err, common.ToString(commonInput), common.ToString(commonOut))
		return errno.ErrCallServiceFail
	}

	tmoney := commonOut.GetData().GetRemainingNum()
	tips := abbreviateNumber(tmoney)
	toolInfo.Tips = tips
	return nil
}

func abbreviateNumber(num float32) string {
	if num < 10000 {
		// 小于一万，直接返回，不处理
		return fmt.Sprintf("%.0f", num)
	} else if num < 10000000 {
		// 小于千万，缩写为**w，保留一位小数，小数为0时不显示
		if int64(num)%10000 < 1000 {
			return fmt.Sprintf("%.0fw", num/10000)
		}
		return fmt.Sprintf("%.1fw", num/10000)
	} else if num <= 9990000000 {
		//小于等于九百九十九千万，缩写为**kw，保留一位小数，小数为0时不显示
		if int64(num)%10000000 < 1000000 {
			return fmt.Sprintf("%.0fkw", num/10000000)
		}
		return fmt.Sprintf("%.1fkw", num/10000000)
	}
	// 超过九百九十九千万，直接返回999kw+
	return "999kw+"
}
