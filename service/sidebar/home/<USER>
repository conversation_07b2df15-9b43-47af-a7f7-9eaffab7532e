package home

import (
	"context"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sidebar/home"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 获取游戏中心信息

type GameInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("sidebar_home_gameinfo", func() engine.Job {
		return &GameInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewGameInfo(ctx *engine.Context) *GameInfo {
	return &GameInfo{
		ctx: ctx,
	}
}
func (a *GameInfo) IsValid(ctx context.Context, baseData *types.SidebarHomeBaseData) bool {
	return true
}
func (a *GameInfo) Execute(ctx context.Context, outData *home.SidebarHomeData, baseData *types.SidebarHomeBaseData) error {
	pureMode := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("pure_mode", 0), common.TTT_INT).(int)
	if 1 == pureMode {
		// 审核态不展示游戏中心
		outData.GameCenter = &home.GameCenterInfo{
			Visible: proto.Bool(false),
		}
		return nil
	}

	keys := []string{
		"show_game_center",  // 是否展示游戏中心
		"all_game_tab_list", // 游戏tab列表
	}

	values, err := wordserver.QueryKeys(ctx, types.SideBarConfTableName, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], err=[%v]", types.SideBarConfTableName, keys, err)
		return errno.ErrCallServiceFail
	}
	// 如果数量不对，也认为失败返回错误
	if len(values) != len(keys) {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], values=[%v]", types.SideBarConfTableName, keys, values)
		return errno.ErrCallServiceFail
	}

	showGameCenter := common.Tvttt(values[0], common.TTT_INT32).(int32)
	if 0 == showGameCenter {
		outData.GameCenter = &home.GameCenterInfo{
			Visible: proto.Bool(false),
		}
		return nil
	}

	gameTabList := make([]*home.GameTabInfo, 0)
	err = jsoniter.UnmarshalFromString(values[1], &gameTabList)
	if err != nil {
		tbcontext.WarningF(ctx, "unmarshal game_tab_list fail: %v", err)
		return errno.ErrCallServiceFail
	}
	outData.GameCenter = &home.GameCenterInfo{
		Visible:   proto.Bool(true),
		ToolsList: gameTabList,
	}
	return nil
}

type GameInfoOperator struct {
}

func (rdop *GameInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *home.SidebarHomeData
	var baseData *types.SidebarHomeBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewGameInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "sidebar_home_gameinfo execute fail: %v", err)
		return err
	}

	return nil
}
