package home

import (
	"context"
	"github.com/spf13/cast"
	"strconv"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	protoCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/sign"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sidebar/home"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 获取关注吧信息

type LikeForum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("sidebar_home_likeforum", func() engine.Job {
		return &LikeForumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewLikeForum(ctx *engine.Context) *LikeForum {
	return &LikeForum{
		ctx: ctx,
	}
}

func (a *LikeForum) IsValid(ctx context.Context, baseData *types.SidebarHomeBaseData) bool {
	// 未登录用户不用查询
	if baseData.StaticField.UserID == 0 {
		return false
	}
	return true
}

// Execute 主方法
func (a *LikeForum) Execute(ctx context.Context, outData *home.SidebarHomeData, baseData *types.SidebarHomeBaseData) error {
	staticField := baseData.StaticField
	// 下发关注的吧跳转链接

	likeForumScheme := "https://tieba.baidu.com/mo/q/hybrid-main-bawu/forumConcern/hybrid?customfullscreen=1&nonavigationbar=1&loadingSignal=1"
	outData.LikeForumScheme = proto.String(likeForumScheme)
	// 下发关注的吧
	err := a.getLikeForum(ctx, baseData)
	if err != nil {
		return err
	}
	// 没有关注的吧就返回空数组
	if len(baseData.StaticField.LikeForumPermRes) == 0 {
		outData.LikeForumList = []*home.ForumLike{}
		return nil
	}

	a.getForumInfoByForumIds(ctx, staticField)

	outData.LikeForumList = make([]*home.ForumLike, 0, len(staticField.LikeForumPermRes))

	// 官方吧列表
	officialLists := a.getOfficialForumIds(ctx)

	for _, arrForum := range staticField.LikeForumPermRes {
		// 如果吧名为空直接过滤，有吧已经被回收的情况
		if arrForum.GetForumName() == "" {
			continue
		}
		likeForum := &home.ForumLike{
			ForumName:   proto.String(arrForum.GetForumName()),
			ForumId:     proto.Uint64(uint64(arrForum.GetForumId())),
			LevelId:     proto.Int32(int32(arrForum.GetLevelId())),
			LevelName:   proto.String(arrForum.GetLevelName()),
			IsSign:      proto.Int32(0),
			IsForbidden: proto.Uint32(0),
		}
		if forumHotInfo, ok := staticField.ArrForumHotNumList[int64(arrForum.GetForumId())]; ok {
			likeForum.HotNum = proto.Int64(forumHotInfo.GetHotValue())
		}
		if forumInfo, ok := staticField.ArrForumBaseInfoList[int64(likeForum.GetForumId())]; ok {
			likeForum.Avatar = proto.String(forumInfo.GetCard().GetAvatar())
			likeForum.IsForbidden = proto.Uint32(uint32(forumInfo.GetForumName().GetForbidden()))
		}
		if isSign, ok := staticField.ArrForumSignInfo[int64(likeForum.GetForumId())]; ok {
			likeForum.IsSign = proto.Int32(isSign)
		}

		// 判断是否为官方吧,增加官方吧标识
		if officialLists != nil && len(officialLists) > 0 {
			if _, ok := officialLists[cast.ToUint32(likeForum.GetForumId())]; ok {
				likeForum.IsOfficialForum = proto.Int32(1)
			} else {
				likeForum.IsOfficialForum = proto.Int32(0)
			}
		} else {
			likeForum.IsOfficialForum = proto.Int32(0)
		}

		outData.LikeForumList = append(outData.LikeForumList, likeForum)
	}

	return nil
}

type LikeForumOperator struct {
}

func (rdop *LikeForumOperator) DoImpl(ctx *engine.Context) error {
	var outData *home.SidebarHomeData
	var baseData *types.SidebarHomeBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewLikeForum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "sidebar_home_likeforum execute fail: %v", err)
		return err
	}

	return nil
}

// 调用perm获取用户关注吧列表
func (a *LikeForum) getLikeForum(ctx context.Context, baseData *types.SidebarHomeBaseData) error {
	staticField := baseData.StaticField
	input := map[string]any{
		"user_id":              staticField.UserID,
		"check_forum":          1,
		"need_forbidden_forum": 1, //与check_forum=1结合，不过滤封禁吧但更新吧名
		"page_type":            1,
		"page_no":              1,
		"page_size":            types.LikeForumPageSize, // 和forumGuide接口保持一致
	}
	if types.FlistSortCustom == baseData.Request.GetSortType() {
		input["sort_type"] = 1
	}

	// 新增关注关系排序 版控 66
	if clientvers.Compare("12.66.0", staticField.ClientVersion) >= 0 {
		input["sort_type"] = types.FlistSortForumTop
	}

	getLikeForumListRes := new(perm.GetLikeForumListRes)
	err := tbservice.Call(ctx, "perm", "getLikeForumList", input, getLikeForumListRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getLikeForumListRes.Errno == nil {
		tbcontext.FatalF(ctx, "call perm::getLikeForumList fail, input:%v, output:%v, err: %v", input, getLikeForumListRes, err)
		return errno.ErrCallServiceFail
	}
	if getLikeForumListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "call perm::getLikeForumList fail, input:%v, output:%v, err: %v", input, getLikeForumListRes, err)
		return errno.ErrCallServiceFail
	}

	// 判断关注吧列表是否存在更多
	if getLikeForumListRes.GetOutput().GetMemberCount() > types.LikeForumPageSize {
		staticField.BoolLikeForumHasMore = true
	}

	likeForumID := make([]uint64, 0)
	likeForumList := make([]*perm.UserOutput, 0)
	for _, v := range getLikeForumListRes.GetOutput().GetMemberList() {
		if v != nil && v.GetForumName() != "" {
			likeForumList = append(likeForumList, v)
			likeForumID = append(likeForumID, uint64(v.GetForumId()))
		}
	}

	staticField.LikeForumPermRes = likeForumList
	staticField.LikeForumIds = likeForumID
	return nil
}

func (a *LikeForum) getForumInfoByForumIds(ctx context.Context, staticField *types.SidebarHomeStaticField) {
	if len(staticField.LikeForumIds) == 0 {
		return
	}

	multi := tbservice.Multi()
	for i := 0; len(staticField.LikeForumIds) > i*types.MultiGetHotNumNum; i++ {
		var fids []uint64
		if len(staticField.LikeForumIds) > (i+1)*types.MultiGetHotNumNum {
			fids = staticField.LikeForumIds[i*types.MultiGetHotNumNum : (i+1)*types.MultiGetHotNumNum]
		} else {
			fids = staticField.LikeForumIds[i*types.MultiGetHotNumNum:]
		}

		iStr := strconv.Itoa(i)
		commonParam := &tbservice.Parameter{
			Service: "common",
			Method:  "mgetHotByForumId",
			Input: map[string]any{
				"forum_ids": fids,
			},
			Output: &protoCommon.MgetHotByForumIdRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetHotByForumId"+iStr, commonParam)

		forumParam := &tbservice.Parameter{
			Service: "forum",
			Method:  "mgetBtxInfoEx",
			Input: map[string]any{
				"forum_id": fids,
			},
			Output: &forum.MgetBtxInfoExRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetBtxInfoEx"+iStr, forumParam)

		// 获取签到数据
		signParam := &tbservice.Parameter{
			Service: "sign",
			Method:  "getUserSignForums",
			Input: map[string]any{
				"forum_id": fids,
				"user_id":  staticField.UserID,
			},
			Output: &sign.GetUserSignForumsRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getUserSignForums"+iStr, signParam)
	}

	multi.Call(ctx)

	forumHotNumList := make(map[int64]*protoCommon.MgetHotByForumId)
	forumBaseInfoList := make(map[int64]*forum.BtxInfo)
	signInfoList := make(map[int64]int32)
	for i := 0; len(staticField.LikeForumIds) > i*types.MultiGetHotNumNum; i++ {
		iStr := strconv.Itoa(i)
		// 并行获取热度值信息
		mgetHotByForumIDInter, err := multi.GetResult(ctx, "mgetHotByForumId"+iStr)
		if err != nil {
			tbcontext.WarningF(ctx, "call common::mgetHotByForumId fail: %v", err)
		} else {
			res := mgetHotByForumIDInter.(*protoCommon.MgetHotByForumIdRes)
			for _, forumInfo := range res.GetOutput() {
				if forumInfo == nil {
					continue
				}
				forumHotNumList[forumInfo.GetForumId()] = forumInfo
			}
		}

		// 获取吧基础信息
		mgetBtxInfoExInter, err := multi.GetResult(ctx, "mgetBtxInfoEx"+iStr)
		if err != nil {
			tbcontext.WarningF(ctx, "call forum::mgetBtxInfoEx fail: %v", err)
		} else {
			res := mgetBtxInfoExInter.(*forum.MgetBtxInfoExRes)
			for fid, forumInfo := range res.GetOutput() {
				if forumInfo == nil {
					continue
				}
				forumBaseInfoList[int64(fid)] = forumInfo
			}
		}
		// 获取吧签到信息
		getUserSignForumsInter, err := multi.GetResult(ctx, "getUserSignForums"+iStr)
		if err != nil {
			tbcontext.WarningF(ctx, "call sign::getUserSignForums fail: %v", err)
		} else {
			res := getUserSignForumsInter.(*sign.GetUserSignForumsRes)
			for fid, signInfo := range res.GetArrUserInfo() {
				if signInfo == nil {
					continue
				}
				signInfoList[int64(fid)] = signInfo.GetIsSignIn()
			}
		}
	}

	staticField.ArrForumBaseInfoList = forumBaseInfoList
	staticField.ArrForumHotNumList = forumHotNumList
	staticField.ArrForumSignInfo = signInfoList
}

func (a *LikeForum) getOfficialForumIds(ctx context.Context) map[uint32]struct{} {
	req := map[string]interface{}{
		"pn":          1,
		"rn":          1000,
		"is_form_mis": 1,
	}
	res := &protoCommon.GetOfficialForumIDListRes{}
	err := tbservice.Call(ctx, "common", "getOfficialForumIDList", req, res)
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS || res.GetData() == nil ||
		res.GetData().GetForumIds() == nil || len(res.GetData().GetForumIds()) <= 0 {
		tbcontext.WarningF(ctx, "call common::getOfficialForumIDList fail: %v", err)
		return nil
	}

	officialForumIds := make(map[uint32]struct{})
	for _, forumID := range res.GetData().GetForumIds() {
		officialForumIds[forumID] = struct{}{}
	}
	return officialForumIds
}
