package home

import (
	"context"
	"fmt"
	"strconv"
	"time"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/format"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/god"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/props"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/usertool"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/usercenter"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sidebar/home"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 获取用户信息

type UserInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("sidebar_home_userinfo", func() engine.Job {
		return &UserInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewUserInfo(ctx *engine.Context) *UserInfo {
	return &UserInfo{
		ctx: ctx,
	}
}
func (a *UserInfo) IsValid(ctx context.Context, baseData *types.SidebarHomeBaseData) bool {
	// 未登录用户不用查询用户信息
	if baseData.StaticField.UserID == 0 {
		return false
	}
	return true
}
func (a *UserInfo) Execute(ctx context.Context, outData *home.SidebarHomeData, baseData *types.SidebarHomeBaseData) error {
	staticField := baseData.StaticField
	err := a.getUserDataEx(ctx, outData, baseData)
	if err != nil {
		return err
	}
	a.getPendant(ctx, baseData.StaticField)

	//大V认证
	tbVip, err := staticField.ArrUserInfo.GetClientTbVipStruct()
	if err == nil && tbVip != nil {
		staticField.TbVip = tbVip
	}

	//贴吧电商
	ecom, err := staticField.ArrUserInfo.GetEcomStruct()
	if err == nil && ecom != nil {
		staticField.Ecom = ecom
	}

	// 大神
	godInfo, _ := staticField.ArrUserInfo.GetGodInfoStruct()
	staticField.ArrNewGod = god.GetUserGodInfo(godInfo)

	// 返回值构建
	a.buildUserInfo(ctx, outData, baseData)
	return nil
}

type UserInfoOperator struct {
}

func (rdop *UserInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *home.SidebarHomeData
	var baseData *types.SidebarHomeBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewUserInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "sidebar_home_userinfo execute fail: %v", err)
		return err
	}

	return nil
}

func (a *UserInfo) getUserDataEx(ctx context.Context, outData *home.SidebarHomeData, baseData *types.SidebarHomeBaseData) error {
	staticField := baseData.StaticField

	multi := tbservice.Multi()
	userDataParam := &tbservice.Parameter{
		Service: "user",
		Method:  "getUserDataEx",
		Input: map[string]interface{}{
			"user_id":        staticField.UserID,
			"need_pass_info": 1,
			"call_from":      "client_home",
			"extra_fields": []string{
				"regtime",
			},
		},
		Output: &user.GetUserDataExRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getUserDataEx", userDataParam)

	rpcInput := map[string]interface{}{
		"user_id":        []uint64{staticField.UserID},
		"call_from":      "client_sidebar_home",
		"client_type":    staticField.ClientType,    // 1 ios 2 android
		"client_version": staticField.ClientVersion, // 版本，用以版控
	}
	if clientvers.Compare(staticField.ClientVersion, "12.59.0") > 0 {
		// 低于12.59版本，call_from传client_user_profile
		rpcInput[`call_from`] = "client_user_profile"
	}
	iconListParam := &tbservice.Parameter{
		Service: "user",
		Method:  "mgetUserShowInfo",
		Input:   rpcInput,
		Output:  &usercenter.MgetUserShowInfoRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
			tbservice.WithServiceName("user/usercenter"),
		},
	}
	multi.Register(ctx, "mgetUserShowInfo", iconListParam)

	multi.Call(ctx)
	iconListResInter, err := multi.GetResult(ctx, "mgetUserShowInfo")
	iconListRes := iconListResInter.(*usercenter.MgetUserShowInfoRes)
	if err != nil || iconListRes.Errno == nil || iconListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call user::mgetUserShowInfo fail, err: %v, res: %+v", err, iconListRes)
		// 不阻塞流程
	} else if clientvers.Compare(staticField.ClientVersion, "12.59.0") > 0 {
		// 低于12.59版本，特殊处理vip标识
		showIconList := make([]*client.UserAttrIcon, 0)
		resIcons, ok := iconListRes.GetData()[int64(staticField.UserID)]
		if ok && len(resIcons.GetShowIconList()) > 0 {
			for _, v := range resIcons.GetShowIconList() {
				if v.GetType() != "vip" {
					continue
				}

				height, err := strconv.Atoi(v.GetHeight())
				if err != nil {
					continue
				}

				width, err := strconv.Atoi(v.GetWidth())
				if err != nil {
					continue
				}

				extAttr := v.GetExtAttr()
				attrList := make([]*client.UserIconExtAttr, 0)
				for _, attr := range extAttr {
					singleAttr := &client.UserIconExtAttr{
						Key:   attr.Key,
						Value: attr.Value,
					}
					attrList = append(attrList, singleAttr)

				}

				userAttrIcon := &client.UserAttrIcon{
					Type:    v.Type,
					SubType: v.SubType,
					Comment: v.Comment,
					ImgSrc:  v.ImgSrc,
					LinkUrl: v.LinkUrl,
					Height:  proto.Int32(int32(height)),
					Width:   proto.Int32(int32(width)),
					ExtAttr: attrList,
				}
				if v.GetSubType() == "vip" || v.GetSubType() == "svip" {
					userAttrIcon.ImgSrc = v.PrefixImgSrc
				}

				showIconList = append(showIconList, userAttrIcon)
			}
		}
		staticField.UserAttrIcon = showIconList
	} else {
		// 大于12.59版本，直接下发icon_list
		showIconList := make([]*client.UserAttrIcon, 0)
		resIcons, ok := iconListRes.GetData()[int64(staticField.UserID)]
		if ok && len(resIcons.GetShowIconList()) > 0 {
			err := common.StructAToStructBCtx(ctx, resIcons.GetShowIconList(), &showIconList)
			if err != nil {
				tbcontext.WarningF(ctx, "convert iconList error err:[%v], icon:[%s]", err, common.ToString(showIconList))
			}
		}
		staticField.UserAttrIcon = showIconList
	}

	userDataInter, err := multi.GetResult(ctx, "getUserDataEx")
	userDataRes := userDataInter.(*user.GetUserDataExRes)
	if err != nil || userDataRes.Errno == nil || userDataRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "call user::getUserDataEx fail, err: %v, res: %+v", err, userDataRes)
		return errno.ErrCallServiceFail
	}

	format.FormatUser(userDataRes.GetUserInfo())
	baseData.StaticField.ArrUserInfo = userDataRes.GetUserInfo()

	return nil
}

func (a *UserInfo) getPendant(ctx context.Context, staticField *types.SidebarHomeStaticField) {
	pendant := props.GetPendant(ctx, staticField.ArrUserInfo, staticField.ClientType, props.PENDANT_LOCATION_PROFILE)
	staticField.ArrPendant = pendant
	res, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_member", "profile_portrait_pendant_jump_url")
	// 没有找到key不打日志
	if err != nil && err != redis.ErrNil {
		tbcontext.WarningF(ctx, "wordserver tb_wordlist_redis_member fail: %v", err)
		return
	}
	if res != "" && pendant != nil {
		jumpMap := make(map[string]string)
		err = jsoniter.Unmarshal([]byte(res), &jumpMap)
		if err != nil {
			tbcontext.WarningF(ctx, "json unmarshal fail: %v", err)
			return
		}
		pendantId := pendant.GetPropsId()
		if staticField.ArrUserInfo.GetThemePackage() != nil && php2go.InArray(int64(pendantId), staticField.ArrUserInfo.GetThemePackage().GetPropsList()) {
			if _, ok := jumpMap["package_url"]; ok {
				pendant.JumpUrl = proto.String(fmt.Sprintf("%s&package_id=%d", jumpMap["package_url"], staticField.ArrUserInfo.GetThemePackage().GetPackageId()))
			}
		} else {
			if _, ok := jumpMap["url"]; ok {
				pendant.JumpUrl = proto.String(fmt.Sprintf("%s&decoratorId=%d", jumpMap["url"], pendantId))
			}
		}
	}
}

func (a *UserInfo) buildUserInfo(ctx context.Context, outData *home.SidebarHomeData, baseData *types.SidebarHomeBaseData) {
	staticField := baseData.StaticField
	portrait := tbportrait.Encode(int64(staticField.UserID), staticField.ArrUserInfo.GetUserName(),
		common.Tvttt(staticField.ArrUserInfo.GetPortraitTime(), common.TTT_INT64).(int64))
	strNameShow := ""
	if staticField.ArrUserInfo.GetProfessionManagerNickName() != "" {
		strNameShow = staticField.ArrUserInfo.GetProfessionManagerNickName()
	} else {
		strNameShow = usertool.GetUserNickNameByVersion(staticField.ClientType, staticField.ClientVersion, &usertool.NickNameByVersion{
			UserNickNameV2: staticField.ArrUserInfo.GetUserNicknameV2(),
			DisplayName:    staticField.ArrUserInfo.GetDisplayName(),
		}, staticField.ArrUserInfo.GetUserNickname())
		if strNameShow == "" {
			strNameShow = staticField.ArrUserInfo.GetUserName()
		}
	}
	isMem := a.getMemberFlag(ctx, staticField)
	sex := staticField.ArrUserInfo.GetPuserinfo().GetSex()

	outData.UserInfo = &client.User{
		Portrait:     proto.String(portrait),
		NameShow:     proto.String(strNameShow),
		IsMem:        proto.Int32(int32(isMem)),
		Pendant:      staticField.ArrPendant,
		ShowIconList: staticField.UserAttrIcon,
		Sex:          proto.Int32(common.Tvttt(sex, common.TTT_INT32).(int32)),
	}
}

func (a *UserInfo) getMemberFlag(ctx context.Context, staticField *types.SidebarHomeStaticField) int64 {
	userInfo := staticField.ArrUserInfo
	parrProps := userInfo.GetUserMParrProps(ctx)
	if parrProps != nil && parrProps.GetLevel() != nil {
		propsId := parrProps.GetLevel().GetPropsId()
		endTime := int64(parrProps.GetLevel().GetEndTime())

		if propsId == 0 || time.Now().Unix() > endTime {
			return 0
		} else {
			return propsId
		}
	}

	return 0
}
