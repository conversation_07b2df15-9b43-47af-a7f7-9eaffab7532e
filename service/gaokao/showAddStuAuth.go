package gaokao

import (
	"context"
	"errors"
	"strconv"

	"github.com/golang/protobuf/proto"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/gaokao/showAddStuAuth"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	ActivityGaoKaoTableName           = "tb_wordlist_redis_activity_scene_config_13" // 2024高考活动词表配置
	ForumManagerRecommendHeadSubTitle = "forum_manager_recommend_head_sub_title"     // 认证方式-吧主推荐H5-头图区-数量上限
	ForumManagerRecommendHeadTitle    = "forum_manager_recommend_head_title"         // 认证方式-吧主推荐H5-头图区-头部文案，支持换行符
	ForumManagerRecommendTailTitle    = "forum_manager_recommend_tail_title"         // 认证方式-吧主推荐H5-底部说明区-说明文案，支持换行符
	ConfigGaoKaoAuthSwitch            = "gaokao_config_auth_switch"                  // 全局认证入口开关，0-关闭，1-开启
)

type GetUserManagerListResp struct {
	ErrNo  int           `json:"error_no"`
	Errmsg string        `json:"error_msg"`
	Output []userManager `json:"output"`
}

type userManager struct {
	ForumID uint32              `json:"forum_id"`
	Roles   []map[string]uint32 `json:"roles"`
}

type ForumInfo struct {
	ForumID   uint32
	ForumName string
}

type ConfigGaoKao struct {
	ForumManagerRecommendHeadSubTitle string
	ForumManagerRecommendHeadTitle    string
	ForumManagerRecommendTailTitle    string
	GaoKaoConfigAuthSwitch            uint32
}

func ShowAddStuAuth(ctx context.Context,
	baseData *types.ShowAddStuAuthBaseData,
	response *showAddStuAuth.ShowAddStuAuthResIdl) int {

	config, err := getWordServer(ctx)
	if err != nil {
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	if config == nil {
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 获取吧的信息
	forumNameInfos, err := getForums(ctx, baseData.UserID)
	if err != nil {
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 根据吧查询吧与学校的绑定关联
	forumNameInfoList, err := mgetForumCollegeByFIDs(ctx, forumNameInfos)
	if err != nil || len(forumNameInfoList) == 0 {
		tbcontext.WarningF(ctx,
			"call mgetForumCollegeByFIDs failed, err:[%#v], output: [%#v]", err, forumNameInfoList)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 获取学生认证信息
	stuAuthInfos, err := getStuAuthFromManager(ctx, forumNameInfoList[0])
	if err != nil {
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 获取用户信息
	mapUserInfo, err := mgetUserData(ctx, stuAuthInfos)
	if err != nil {
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 格式化数据
	response.Data = formatData(ctx, config, forumNameInfoList[0], stuAuthInfos, mapUserInfo)
	return tiebaerror.ERR_SUCCESS
}

// formatData 格式化数据
func formatData(ctx context.Context,
	config *ConfigGaoKao,
	forumInfo ForumInfo,
	stuAuthInfos []*common.StuAuthInfo,
	mapUserInfo map[int64]*user.UserInfo,
) *showAddStuAuth.ShowAddStuAuthRes {

	// 格式化列表数据
	authList := make([]*showAddStuAuth.AuthList, 0)
	for _, v := range stuAuthInfos {
		userID := v.GetUserId()
		if userInfo, ok := mapUserInfo[userID]; ok {

			protrait := tbportrait.Encode(userInfo.GetUserId(), userInfo.GetUserName(), 0)
			authList = append(authList, &showAddStuAuth.AuthList{
				Portrait: proto.String(protrait),
				NickName: proto.String(userInfo.GetUserInfoNameShowV2(ctx)),
				Status:   proto.String(getStatusText(ctx, v.GetStatus(), v.GetPassOrigin())),
			})
			continue
		}
		authList = append(authList, &showAddStuAuth.AuthList{
			Portrait: proto.String(""),
			NickName: proto.String(""),
			Status:   proto.String(getStatusText(ctx, v.GetStatus(), v.GetPassOrigin())),
		})
	}

	// 响应格式化
	return &showAddStuAuth.ShowAddStuAuthRes{
		Head: &showAddStuAuth.Head{
			Title:     proto.String(config.ForumManagerRecommendHeadTitle),
			SubTitle:  proto.String(config.ForumManagerRecommendHeadSubTitle),
			ForumName: proto.String(forumInfo.ForumName),
			ForumId:   proto.Int64(int64(forumInfo.ForumID)),
		},
		Tail: &showAddStuAuth.Tail{
			Title: proto.String(config.ForumManagerRecommendTailTitle),
		},
		Config: &showAddStuAuth.Config{
			AuthSwitch: proto.Uint32(config.GaoKaoConfigAuthSwitch),
		},
		AuthList: authList,
	}
}

// getStuAuthFromManager 获取学生认证信息
func getStuAuthFromManager(ctx context.Context, forumInfo ForumInfo) ([]*common.StuAuthInfo, error) {

	req := new(common.GetStuAuthFromManagerReq)
	req.ForumId = proto.Int64(int64(forumInfo.ForumID))
	outPut := new(common.GetStuAuthFromManagerRes)

	err := tbservice.Call(ctx, "common", "getStuAuthFromManager", req, outPut,
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("common_go"),
	)
	if err != nil || outPut.Errno == nil || outPut.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx,
			"getStuAuthFromManager call service common::getStuAuthFromManager failed, err = %v, input = %v, output = %#v",
			err, req, outPut)
		return nil, errors.New("call common::getStuAuthFromManager failed")
	}
	return outPut.GetData().GetAuthInfo(), nil
}

// mgetForumCollegeByFIDs 根据吧查询吧与学校的绑定关联
func mgetForumCollegeByFIDs(ctx context.Context, ForumInfos []ForumInfo) ([]ForumInfo, error) {

	ForumIDs := make([]int64, 0)
	for _, v := range ForumInfos {
		ForumIDs = append(ForumIDs, int64(v.ForumID))
	}

	req := new(common.MgetForumCollegeByFIDsReq)
	req.ForumId = ForumIDs
	outPut := new(common.MgetForumCollegeByFIDsRes)
	err := tbservice.Call(ctx, "common", "mgetForumCollegeByFIDs", req, outPut,
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("common_go"),
	)
	if err != nil || outPut.Errno == nil || outPut.GetErrno() != tiebaerror.ERR_SUCCESS {

		tbcontext.WarningF(ctx,
			"mgetForumCollegeByFIDs call service common::mgetForumCollegeByFIDs failed, err = %v, input = %v, output = %#v",
			err, req, outPut)
		return nil, errors.New("call common::mgetForumCollegeByFIDs failed")
	}
	mapForumCollege := outPut.GetData()
	ForumInfoList := make([]ForumInfo, 0)
	for _, v := range ForumInfos {
		if _, ok := mapForumCollege[int64(v.ForumID)]; ok {
			ForumInfoList = append(ForumInfoList, v)
		}
	}
	if len(ForumInfoList) == 0 {
		return nil, errors.New("call common::mgetForumCollegeByFIDs result GetForumName len 0")
	}

	return ForumInfoList, nil
}

// getWordServer 读取词表配置
func getWordServer(ctx context.Context) (*ConfigGaoKao, error) {
	keys := []string{
		ForumManagerRecommendHeadSubTitle,
		ForumManagerRecommendHeadTitle,
		ForumManagerRecommendTailTitle,
		ConfigGaoKaoAuthSwitch,
	}
	wordserverRes, err := wordserver.QueryItems(ctx, ActivityGaoKaoTableName, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "getWordServer call wordserver.QueryItems failed, err:[%s]", err.Error())
		return nil, err
	}
	config := new(ConfigGaoKao)
	if v, ok := wordserverRes[ForumManagerRecommendHeadSubTitle]; ok {
		config.ForumManagerRecommendHeadSubTitle = v
	}
	if v, ok := wordserverRes[ForumManagerRecommendHeadTitle]; ok {
		config.ForumManagerRecommendHeadTitle = v
	}
	if v, ok := wordserverRes[ForumManagerRecommendTailTitle]; ok {
		config.ForumManagerRecommendTailTitle = v
	}
	if v, ok := wordserverRes[ConfigGaoKaoAuthSwitch]; ok {
		authSwitch, err := strconv.Atoi(v)
		if err != nil {
			tbcontext.WarningF(ctx, "getWordServer call strconv.Atoi failed, err:[%s], input:[%s]", err.Error(), v)
			return nil, err
		}
		config.GaoKaoConfigAuthSwitch = uint32(authSwitch)
	}
	return config, nil
}

// mgetUserData 获取用户信息
func mgetUserData(ctx context.Context, stuAuthInfos []*common.StuAuthInfo) (map[int64]*user.UserInfo, error) {

	if len(stuAuthInfos) == 0 {
		return nil, nil
	}
	userIds := make([]int64, 0)
	for _, stuAuthInfo := range stuAuthInfos {
		userIds = append(userIds, stuAuthInfo.GetUserId())
	}
	req := &user.MgetUserDataReq{
		UserId: userIds,
	}
	outPut := &user.MgetUserDataRes{}
	opt := tbservice.WithConverter(tbservice.JSONITER)
	err := tbservice.Call(ctx, "user", "mgetUserData", req, outPut, opt)
	if err != nil || outPut.Errno == nil || outPut.GetErrno() != tiebaerror.ERR_SUCCESS {

		tbcontext.WarningF(ctx,
			"mgetUserData call service user::mgetUserData failed, err = %v, input = %v, output = %#v",
			err, req, outPut)
		return nil, errors.New("call user::mgetUserData failed")
	}
	return outPut.GetUserInfo(), nil
}

// getForums 获取吧ID
func getForums(ctx context.Context, userID uint64) ([]ForumInfo, error) {

	forumList, err := getForumByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	forumIDList := make([]uint32, 0)
	for _, v := range forumList {
		forumIDList = append(forumIDList, v.ForumID)
	}

	return getFnameByFid(ctx, forumIDList)
}

// getFnameByFid 根据吧ID列表查询吧信息，过滤吧ID
func getFnameByFid(ctx context.Context, forumIDList []uint32) ([]ForumInfo, error) {

	var err error
	// 根据吧id获取吧信息
	forumReq := &forum.GetFnameByFidReq{
		ForumId: forumIDList,
	}
	forumRes := &forum.GetFnameByFidRes{}
	err = tbservice.Call(ctx, "forum", "getFnameByFid", forumReq, forumRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || forumRes == nil || forumRes.GetErrno() != tiebaerror.ERR_SUCCESS || forumRes.GetForumName() == nil {
		tbcontext.WarningF(ctx,
			"getFnameByFid call service forum::getFnameByFid failed, err = %v, input = %v, output = %#v",
			err, forumReq, forumRes)
		return nil, err
	}
	forumInfos := forumRes.GetForumName()
	forumNameInfos := make([]ForumInfo, 0)
	// 过滤不存在与被封禁的吧
	for _, forumInfo := range forumInfos {

		// 不等于 1 代表吧不存在
		if forumInfo.GetExist() != 1 {
			continue
		}
		// 不等于 0 代表吧没有被封禁
		if forumInfo.GetForbidden() != 0 {
			continue
		}
		forumNameInfos = append(forumNameInfos, ForumInfo{
			ForumID:   forumInfo.GetForumId(),
			ForumName: forumInfo.GetForumName(),
		})
	}
	if len(forumNameInfos) == 0 {
		tbcontext.WarningF(ctx, "filter forumInfos result forumNameInfos len 0, input:[%#v]", forumInfos)
		return nil, errors.New("filter forumInfos result forumNameInfos len 0")
	}

	return forumNameInfos, nil
}

// getForumByUserId 根据用户查询吧主
func getForumByUserID(ctx context.Context, UserID uint64) ([]userManager, error) {

	input := map[string]interface{}{
		"user_id": UserID,
	}
	output := new(GetUserManagerListResp)
	opt := tbservice.WithConverter(tbservice.JSONITER)
	err := tbservice.Call(ctx, "perm", "getUserManagerList", input, output, opt)
	if err != nil || output.ErrNo != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx,
			"getForumByUserId call service perm::getUserManagerList failed, err = %v, input = %v, output = %#v",
			err, input, output)
		return nil, errors.New("call perm::getUserManagerList failed")
	}

	forumList := output.Output
	if len(forumList) == 0 {
		tbcontext.WarningF(ctx, "getForumByUserId result forumList len is 0, input:[%d]", UserID)
		return nil, errors.New("getForumByUserId result forumList len is 0")
	}

	return forumList, nil
}

// 获取认证状态
func getStatusText(_ context.Context, status, passOrigin uint32) string {

	switch status {
	case 0:
		return "审核中"
	case 1:
		if passOrigin == 2 {
			return "审核通过"
		}
		return "通过其他途径已认证成功"
	case 2:
		return "审核驳回"
	default:
		return ""
	}
}
