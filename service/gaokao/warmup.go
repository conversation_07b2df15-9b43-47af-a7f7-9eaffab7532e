package gaokao

import (
	"context"
	"fmt"
	"math/rand"
	"net/url"
	"strconv"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/usertool"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	commonproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/im"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/gaokao/warmup"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	"icode.baidu.com/baidu/tieba-server-go/golib2/php"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	WaitAuditStatus = 0         // 认证状态待审核
	AuditPassStatus = 1         // 认证状态审核通过
	AuditFailStatus = 2         // 认证状态审核未通过
	FrsCallFrom     = "frspage" // 来自frs的调用

	WarmupConfTableName = "tb_wordlist_redis_activity_scene_config_13" // 配置表名
)

type TimelineStruct struct {
	BeginTime int64 `json:"begin_time"`
	EndTime   int64 `json:"end_time"`
}

func Warmup(ctx context.Context, baseData *types.GaokaoWarmupData, response *warmup.WarmupResIdl) int {
	if baseData.StaticField.CallFrom == FrsCallFrom && baseData.StaticField.ForumName == "" {
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 查用户基础信息
	err := getGaokaoUserDataAndForumInfo(ctx, baseData)
	if err != nil {
		tbcontext.FatalF(ctx, "Warmup get user info fail, uid:%d, err:%v", baseData.StaticField.UserID, err)
		return errno.CodeCallServiceFail
	}

	eg := gtask.Group{
		// 允许部分失败，不允许全部失败
		AllowSomeFail: true,
	}

	// 规则区：ai问答
	eg.Go(func() error {
		return getAIanswerInfo(ctx, baseData.StaticField)
	})

	// 规则区：吧主推荐
	eg.Go(func() error {
		return getForumManagerRecomInfo(ctx, baseData.StaticField)
	})

	// 规则区：官方审核
	eg.Go(func() error {
		return getOfficialAuditInfo(ctx, baseData.StaticField)
	})

	succNum, _ := eg.Wait()
	if succNum == 0 {
		tbcontext.FatalF(ctx, "Warmup all service fail")
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 奖励区&其他词表配置信息

	err = getOtherConfig(ctx, baseData.StaticField)
	if err != nil {
		tbcontext.FatalF(ctx, "Warmup get other config fail")
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 构建返回值
	err = buildWarumupOut(ctx, baseData.StaticField, response)
	if err != nil {
		return errno.CodeCallServiceFail
	}
	return 0
}

// 获取用户基础信息&吧名转吧id
func getGaokaoUserDataAndForumInfo(ctx context.Context, baseData *types.GaokaoWarmupData) error {
	static := baseData.StaticField
	if static.UserID == 0 && static.ForumName == "" {
		return nil
	}
	multi := tbservice.Multi()
	if static.UserID > 0 {
		userInfoParam := &tbservice.Parameter{
			Service: "user",
			Method:  "getUserData",
			Input: map[string]any{
				"user_id": static.UserID,
			},
			Output: &user.GetUserDataRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getUserData", userInfoParam)
		userAuthInfoParam := &tbservice.Parameter{
			Service: "common",
			Method:  "mgetStuAuthByUIDs",
			Input: map[string]any{
				"user_ids": []uint64{static.UserID},
			},
			Output: &commonproto.MgetStuAuthByUIDsRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
				tbservice.WithRalName("common_go"),
			},
		}
		multi.Register(ctx, "mgetStuAuthByUIDs", userAuthInfoParam)
	}

	if static.CallFrom == FrsCallFrom {
		getForumParam := &tbservice.Parameter{
			Service: "forum",
			Method:  "getFidByFname",
			Input: map[string]any{
				"query_words": []string{static.ForumName},
			},
			Output: &forum.GetFidByFnameRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getFidByFname", getForumParam)
	}

	multi.Call(ctx)

	if static.UserID > 0 {
		// 获取用户基础信息，头像&昵称
		userRes, err := multi.GetResult(ctx, "getUserData")
		if err != nil {
			tbcontext.WarningF(ctx, "fail to call service user:getUserData, err = %v, uid = %v,output = %v",
				err, static.UserID, common.ToString(userRes))
			return errno.ErrCallServiceFail
		}

		userInfo, ok := userRes.(*user.GetUserDataRes)
		if !ok || userInfo.Errno == nil || userInfo.GetErrno() != errno.ErrSuccess.Errno() || len(userInfo.GetUserInfo()) == 0 {
			tbcontext.WarningF(ctx, "fail to call service user:getUserData, uid = %v,output = %v",
				static.UserID, common.ToString(userInfo))
			return errno.ErrCallServiceFail
		}

		userResInfo := userInfo.GetUserInfo()[0]
		static.UserPortrait = tbportrait.Encode(userResInfo.GetUserId(), userResInfo.GetUserName(), 0)
		strNameShow := ""
		if userResInfo.GetProfessionManagerNickName() != "" {
			strNameShow = userResInfo.GetProfessionManagerNickName()
		} else {
			strNameShow = usertool.GetUserNickNameByVersion(static.ClientType, static.ClientVersion, &usertool.NickNameByVersion{
				UserNickNameV2: userResInfo.GetUserNicknameV2(),
				DisplayName:    userResInfo.GetDisplayName(),
			}, userResInfo.GetUserNickname())
			if strNameShow == "" {
				strNameShow = userResInfo.GetUserName()
			}
		}
		static.UserShowName = strNameShow

		// 获取用户认证信息
		commonRes, err := multi.GetResult(ctx, "mgetStuAuthByUIDs")
		if err != nil {
			tbcontext.WarningF(ctx, "fail to call service common:mgetStuAuthByUIDs,err = %v, uid = %v, output = %v",
				err, static.UserID, common.ToString(commonRes))
			return errno.ErrCallServiceFail
		}

		userAuthRes, ok := commonRes.(*commonproto.MgetStuAuthByUIDsRes)
		if !ok || userAuthRes.Errno == nil || userAuthRes.GetErrno() != int32(errno.ErrSuccess.Errno()) {
			tbcontext.WarningF(ctx, "fail to call service common:mgetStuAuthByUIDs, uid = %v,output = %v",
				static.UserID, common.ToString(userAuthRes))
			return errno.ErrCallServiceFail
		}

		if authInfo, ok := userAuthRes.GetData().GetAuthInfo()[int64(static.UserID)]; ok {
			static.UserAuthInfo = authInfo
		}
	}

	// 获取吧id
	if static.CallFrom == FrsCallFrom {
		forumResInf, err := multi.GetResult(ctx, "getFidByFname")
		if err != nil {
			tbcontext.WarningF(ctx, "fail to call service forum:getFidByFname,err = %v, fid = %v, output = %v",
				err, baseData.StaticField.ForumID, common.ToString(forumResInf))
			return errno.ErrCallServiceFail
		}

		forumRes, ok := forumResInf.(*forum.GetFidByFnameRes)
		if !ok || forumRes.Errno == nil || forumRes.GetErrno() != errno.ErrSuccess.Errno() || len(forumRes.GetForumId()) == 0 {
			tbcontext.WarningF(ctx, "fail to call service forum:getFidByFname, fid = %v, output = %v", err, common.ToString(forumResInf))
			return errno.ErrCallServiceFail
		}
		static.ForumID = int64(forumRes.GetForumId()[0].GetForumId())
	}

	return nil
}

// 获取规则区的AI答题模块信息
func getAIanswerInfo(ctx context.Context, static *types.GaokaoWarmupStaticField) error {
	// 非frs来源，已经认证通过，不需要获取此规则区，直接返回
	if static.CallFrom != FrsCallFrom || (static.UserAuthInfo != nil && static.UserAuthInfo.GetStatus() == AuditPassStatus) {
		return nil
	}

	// 版控，安卓大于等于12.58，ios大于等于12.61
	if (static.ClientType == clientvers.CLIENT_TYPE_ANDROID && clientvers.Compare(static.ClientVersion, "12.58.0") > 0) ||
		(static.ClientType == clientvers.CLIENT_TYPE_IPHONE && clientvers.Compare(static.ClientVersion, "12.61.0") > 0) {
		return nil
	}

	// 获取吧对应的AI游戏剧情
	chatInput := &chat.GetAiInteractiveGamePlotByFidReq{
		Fid: common.GetInt64Ptr(int64(static.ForumID)),
		Uid: common.GetInt64Ptr(int64(static.UserID)),
	}
	chatOutput := &chat.GetAiInteractiveGamePlotByFidRes{}
	err := tbservice.Call(ctx, "chat", "getAiInteractiveGamePlotByFid", chatInput, &chatOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || chatOutput.Errno == nil || chatOutput.GetErrno() != int32(errno.ErrSuccess.Errno()) {
		tbcontext.WarningF(ctx, "fail to call service chat:getAiInteractiveGamePlotByFid, err = %v, input = %v, output = %v",
			err, common.ToString(chatInput), common.ToString(chatOutput))
		return errno.ErrCallServiceFail
	}

	gamePlots := chatOutput.GetData().GetAiInteractiveGamePlot()
	if len(gamePlots) == 0 {
		// 没有剧情，直接返回
		return nil
	}

	// 读词表，查相关配置
	keys := []string{
		"campus_game_plot_bot_uk",        // 校园认证官AI剧情挑战的uk
		"warmup_rule_conf_aianswer_img",  // ai答题规则区的图片
		"warmup_rule_conf_aianswer_btn",  // ai答题规则区的按钮
		"warmup_rule_conf_aianswer_link", // ai答题规则区的跳转链接
	}
	values, err := wordserver.QueryKeys(ctx, WarmupConfTableName, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], err=[%v]", WarmupConfTableName, keys, err)
		return errno.ErrCallServiceFail
	}

	// 如果数量不对，也认为失败返回错误
	if len(values) != len(keys) {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], values=[%v]", WarmupConfTableName, keys, values)
		return errno.ErrCallServiceFail
	}

	if values[0] == "" {
		// 没有配置，直接返回
		return nil
	}
	botUks := []string{}
	err = jsoniter.UnmarshalFromString(values[0], &botUks)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to unmarshal botUks, err = %v", err)
		return errno.ErrCallServiceFail
	}

	hasAIanswer := false
	plotInfo := &chat.AiInteractiveGamePlot{}
	for _, plot := range gamePlots {
		if php2go.InArrayString(plot.GetBotUk(), botUks) {
			hasAIanswer = true
			plotInfo = plot
			break
		}
	}
	if !hasAIanswer {
		// 没有配置校园认证官剧情挑战，直接返回空
		return nil
	}

	// 拼跳转链接
	params := map[string]any{
		"page": "im/AISingleChat",
		"pageParams": map[string]any{
			"paid": plotInfo.GetBotPa(),
			"uk":   plotInfo.GetBotUk(),
		},
	}
	paramsStr, _ := jsoniter.MarshalToString(params)
	encodedStr := url.QueryEscape(paramsStr)
	link := values[3] + encodedStr

	static.AIanswerRule = &warmup.WarmupRule{
		Img:     proto.String(values[1]),
		BtnText: proto.String(values[2]),
		Link:    proto.String(link),
	}

	return nil
}

// 获取规则区的吧主推荐模块信息
func getForumManagerRecomInfo(ctx context.Context, static *types.GaokaoWarmupStaticField) error {
	// 非frs来源，已经认证通过，不需要获取此规则区，直接返回
	if static.CallFrom != FrsCallFrom || static.ForumID == 0 || (static.UserAuthInfo != nil && static.UserAuthInfo.GetStatus() == AuditPassStatus) {
		return nil
	}

	// 查院校映射关系
	commonInput := map[string]any{
		"forum_id": []int64{static.ForumID},
	}
	commonOutput := &commonproto.MgetForumCollegeByFIDsRes{}
	err := tbservice.Call(ctx, "common", "mgetForumCollegeByFIDs", commonInput, &commonOutput,
		tbservice.WithConverter(tbservice.JSONITER), tbservice.WithRalName("common_go"))
	if err != nil || commonOutput.Errno == nil || commonOutput.GetErrno() != int32(errno.ErrSuccess.Errno()) {
		tbcontext.WarningF(ctx, "fail to call service common:mgetForumCollegeByFIDs, err = %v, input = %v, output = %v",
			err, common.ToString(commonInput), common.ToString(commonOutput))
		return errno.ErrCallServiceFail
	}

	if _, ok := commonOutput.GetData()[static.ForumID]; !ok {
		// 没有学校信息，直接返回
		return nil
	}

	// 查吧主信息
	permInput := map[string]any{
		"forum_id": static.ForumID,
	}
	permOutput := &perm.GetBawuListRes{}
	err = tbservice.Call(ctx, "perm", "getBawuList", permInput, &permOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || permOutput.Errno == nil || permOutput.GetErrno() != errno.ErrSuccess.Errno() {
		tbcontext.WarningF(ctx, "fail to call service perm:getBawuList, err = %v, input = %v, output = %v",
			err, common.ToString(permInput), common.ToString(permOutput))
		return errno.ErrCallServiceFail
	}

	if len(permOutput.GetOutput().GetManager()) == 0 {
		// 没有吧主，直接返回
		return nil
	}

	managerUIDs := []uint64{}
	for _, manager := range permOutput.GetOutput().GetManager() {
		uid := manager.GetUser().GetUserId()
		managerUIDs = append(managerUIDs, uid)
	}

	// 查私信开关，将私信开关打开的吧主uid记录下来
	openMsgManagerUIDs := []uint64{}
	multi := tbservice.Multi()
	for _, uid := range managerUIDs {
		imParam := &tbservice.Parameter{
			Service: "im",
			Method:  "getUserMask",
			Input: map[string]any{
				"user_id": uid,
			},
			Output: &im.GetUserMaskRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, fmt.Sprintf("getUserMask_%d", uid), imParam)
	}
	multi.Call(ctx)
	for _, uid := range managerUIDs {
		imRes, err := multi.GetResult(ctx, fmt.Sprintf("getUserMask_%d", uid))
		if err != nil {
			tbcontext.WarningF(ctx, "fail to call service im:getUserMask, err = %v, uid = %v, output = %v",
				err, uid, common.ToString(imRes))
			return errno.ErrCallServiceFail
		}

		imOut, ok := imRes.(*im.GetUserMaskRes)
		if !ok || imOut.Errno == nil || imOut.GetErrno() != errno.ErrSuccess.Errno() {
			tbcontext.WarningF(ctx, "fail to call service im:getUserMask, err = %v, uid = %v, output = %v",
				err, uid, common.ToString(imRes))
			return errno.ErrCallServiceFail
		}

		if imOut.Ret != nil && imOut.GetRet() == 0 {
			openMsgManagerUIDs = append(openMsgManagerUIDs, uid)
		}
	}

	length := len(openMsgManagerUIDs)
	if length == 0 {
		// 没有打开私信的吧主，直接返回
		return nil
	}
	// 随机选一个吧主uid
	var managerUID uint64
	rand.Seed(time.Now().UnixNano())
	if length == 1 {
		// 如果数组只有一个元素，直接选择这个元素
		managerUID = openMsgManagerUIDs[0]
	} else {
		// 如果数组有多个元素，生成一个随机索引并选择对应的元素
		randomIndex := rand.Intn(length)
		managerUID = openMsgManagerUIDs[randomIndex]
	}

	// 查词表读相关配置
	keys := []string{
		"warmup_rule_conf_forum_manager_recom_img",  // 吧主推荐规则区的图片
		"warmup_rule_conf_forum_manager_recom_btn",  // 吧主推荐规则区的按钮
		"warmup_rule_conf_forum_manager_recom_link", // 吧主推荐规则区的跳转链接
	}
	values, err := wordserver.QueryKeys(ctx, WarmupConfTableName, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], err=[%v]", WarmupConfTableName, keys, err)
		return errno.ErrCallServiceFail
	}

	// 如果数量不对，也认为失败返回错误
	if len(values) != len(keys) {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], values=[%v]", WarmupConfTableName, keys, values)
		return errno.ErrCallServiceFail
	}

	// 用uid拼跳转链接
	portrait := tbportrait.Encode(int64(managerUID), "", 0)
	encodePortrait := url.QueryEscape(portrait)
	link := fmt.Sprintf("%s&portrait=%s&source=2&useMainState=false", values[2], encodePortrait)

	static.ForumManagerRecomRule = &warmup.WarmupRule{
		Img:     proto.String(values[0]),
		BtnText: proto.String(values[1]),
		Link:    proto.String(link),
	}
	return nil
}

// 获取官方认证信息
func getOfficialAuditInfo(ctx context.Context, static *types.GaokaoWarmupStaticField) error {
	// 已经认证通过，不需要获取此规则区
	if static.UserAuthInfo != nil && static.UserAuthInfo.GetStatus() == AuditPassStatus {
		return nil
	}

	// 直接从词表获取数据
	keys := []string{
		"official_review_uids",                 // 官方认证官的uid列表
		"warmup_rule_conf_official_audit_img",  // 官方审核规则区的图片
		"warmup_rule_conf_official_audit_bnt",  // 官方审核规则区的按钮
		"warmup_rule_conf_official_audit_link", // 官方审核规则区的跳转链接
	}
	values, err := wordserver.QueryKeys(ctx, WarmupConfTableName, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], err=[%v]", WarmupConfTableName, keys, err)
		return errno.ErrCallServiceFail
	}

	// 如果数量不对，也认为失败返回错误
	if len(values) != len(keys) {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], values=[%v]", WarmupConfTableName, keys, values)
		return errno.ErrCallServiceFail
	}

	if values[0] == "" {
		// 没有配置官方审核的uid列表，直接返回
		return nil
	}

	type UinfoSt struct {
		UID      int64  `json:"uid"`
		NameShow string `json:"name_show"`
	}
	uids := []UinfoSt{}
	err = jsoniter.UnmarshalFromString(values[0], &uids)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to unmarshal official audit uids, err = %v", err)
		return errno.ErrCallServiceFail
	}

	length := len(uids)
	if length == 0 {
		// 没有配置官方审核的uid列表，直接返回
		return nil
	}
	// 随机选一个官方账号
	var officailAuditUinfo UinfoSt
	rand.Seed(time.Now().UnixNano())
	if length == 1 {
		officailAuditUinfo = uids[0]
	} else {
		randomIndex := rand.Intn(length)
		officailAuditUinfo = uids[randomIndex]
	}

	// 根据uid评跳转链接
	params := map[string]any{
		"page": "im/personChat",
		"pageParams": map[string]any{
			"userId":   officailAuditUinfo.UID,
			"nameShow": officailAuditUinfo.NameShow,
		},
	}
	paramsStr, _ := jsoniter.MarshalToString(params)
	encodedStr := url.QueryEscape(paramsStr)
	link := values[3] + encodedStr

	static.OfficialAuditRule = &warmup.WarmupRule{
		Img:     proto.String(values[1]),
		BtnText: proto.String(values[2]),
		Link:    proto.String(link),
	}

	return nil
}

// 获取其他配置信息
func getOtherConfig(ctx context.Context, static *types.GaokaoWarmupStaticField) error {
	// 直接从词表获取数据
	keys := []string{
		"warmup_award_tittle_not_certified",   // 奖励介绍-标题-未认证通过
		"warmup_award_img",                    // 奖励介绍-图片
		"gaokao_config_auth_switch",           // 全局认证开关
		"cvtInfo#timeline",                    // 高考活动时间线
		"warmup_user_btn_toast",               // 用户信息区按按钮toast提示
		"warmup_user_show_text_not_certified", // 用户未进行认证时展示的文案
		"warmup_user_btn",                     // 用户区-已认证用户按钮
		"warmup_user_btn_link",                // 用户区-已认证用户按钮跳转链接
	}
	if static.UserAuthInfo != nil {
		if static.UserAuthInfo.GetStatus() == AuditPassStatus {
			// 已经认证通过
			keys[0] = "warmup_award_tittle_certified" // 奖励介绍-标题-认证通过
		}
		if static.UserAuthInfo.GetStatus() == AuditFailStatus {
			if static.UserAuthInfo.GetCreateOrigin() == 2 { // 通过吧主推荐认证，但是审核被拒了
				keys[5] = "warmup_user_show_text_bazhu_certified_fail"
			} else { // 其他渠道通过，但是被撤回了
				keys[5] = "warmup_user_show_text_certified_fail"
			}
		} else if static.UserAuthInfo.GetStatus() == WaitAuditStatus { // 等待审核
			keys[5] = "warmup_user_show_text_certifying"
		}
	}

	values, err := wordserver.QueryKeys(ctx, WarmupConfTableName, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], err=[%v]", WarmupConfTableName, keys, err)
		return errno.ErrCallServiceFail
	}

	// 如果数量不对，也认为失败返回错误
	if len(values) != len(keys) {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], values=[%v]", WarmupConfTableName, keys, values)
		return errno.ErrCallServiceFail
	}

	// 奖励区数据
	static.AwardInfo = &warmup.WarmupAward{
		Title: proto.String(values[0]),
		Img:   proto.String(values[1]),
	}
	// 配置区数据
	authSwitch := common.Tvttt(values[2], common.TTT_INT32).(int32)
	timelineMap := parseActTimeline(ctx, values[3])
	if timelineMap == nil {
		// 解析失败了，返回错误
		return errno.ErrCallServiceFail
	}
	if _, ok := timelineMap[1]; !ok {
		// 没有配置考前配置时间线，直接返回错误
		return errno.ErrCallServiceFail
	}
	static.ConfigInfo = &warmup.WarmupConfig{
		AuthSwitch:      proto.Int32(authSwitch),
		GaokaoBeginTime: proto.Int64(timelineMap[1].EndTime),
		UserBtnToast:    proto.String(values[4]),
	}

	// 用户区数据
	static.UserShowText = values[5]
	if static.UserAuthInfo != nil && static.UserAuthInfo.GetStatus() == AuditPassStatus {
		// 已认证用户需要下发按钮信息并且展示文案要替换
		static.UserInfoBtn = values[6]
		static.UserInfoBtnLink = values[7]
		if strings.Contains(static.UserInfoBtnLink, "{FID}") {
			static.UserInfoBtnLink = strings.ReplaceAll(static.UserInfoBtnLink,
				"{FID}", fmt.Sprintf("%d", static.UserAuthInfo.GetForumId()))
		}
		if strings.Contains(static.UserInfoBtnLink, "{FNAME}") {
			if static.ForumID > 0 &&
				static.ForumName != "" &&
				static.UserAuthInfo.GetForumId() == static.ForumID {
				static.UserInfoBtnLink = strings.ReplaceAll(static.UserInfoBtnLink,
					"{FNAME}", static.ForumName)
			} else if static.UserAuthInfo.GetForumId() > 0 {
				fidUint32 := uint32(static.UserAuthInfo.GetForumId())
				authForumInfo, ferr := getFnameByFid(ctx, []uint32{fidUint32})
				if ferr == nil && len(authForumInfo) > 0 &&
					authForumInfo[0].ForumID == fidUint32 &&
					authForumInfo[0].ForumName != "" {
					static.UserInfoBtnLink = strings.ReplaceAll(static.UserInfoBtnLink,
						"{FNAME}", authForumInfo[0].ForumName)
				}
			}
		}
		static.UserShowText = static.UserAuthInfo.GetTag()
	}

	return nil
}

// 解析时间线
func parseActTimeline(ctx context.Context, timeline string) map[int]TimelineStruct {
	unserTimeline, err := php.Unserialize([]byte(timeline))
	if nil != err {
		tbcontext.WarningF(ctx, "parse timeline fail. %v err: %v", timeline, err.Error())
		return nil
	}
	timelineMap := make(map[int]TimelineStruct)
	for _, timelineItem := range unserTimeline.([]interface{}) {
		item := strings.TrimSpace(timelineItem.(string))
		conf := strings.Split(item, "##")
		if len(conf) < 2 {
			tbcontext.WarningF(ctx, "parse timeline fail. %v", timeline)
			return nil
		}
		status, err := strconv.Atoi(conf[0])
		if nil != err {
			tbcontext.WarningF(ctx, "parse timeline fail. %v", timeline)
			return nil
		}
		arrTime := strings.Split(conf[1], "@")
		loc, _ := time.LoadLocation("Asia/Shanghai") // 设置时区
		begin, err1 := time.ParseInLocation("2006-01-02 15:04:05", arrTime[0], loc)
		end, err2 := time.ParseInLocation("2006-01-02 15:04:05", arrTime[1], loc)
		if nil != err1 || nil != err2 {
			tbcontext.WarningF(ctx, "parse timeline fail. %v", timeline)
			return nil
		}
		timelineMap[status] = TimelineStruct{
			BeginTime: begin.Unix(),
			EndTime:   end.Unix(),
		}
	}
	return timelineMap
}

// 构建返回数据
func buildWarumupOut(ctx context.Context, static *types.GaokaoWarmupStaticField, response *warmup.WarmupResIdl) error {
	// 用户区数据
	response.Data = new(warmup.WarmupRes)
	outData := response.Data
	if static.UserID > 0 {
		outData.User = &warmup.WarmupUser{
			Portrait: proto.String(static.UserPortrait),
			NickName: proto.String(static.UserShowName),
			ShowText: proto.String(static.UserShowText),
		}
		if static.UserAuthInfo != nil && static.UserAuthInfo.GetStatus() == AuditPassStatus {
			outData.User.BtnText = proto.String(static.UserInfoBtn)
			outData.User.BtnLink = proto.String(static.UserInfoBtnLink)
			outData.User.AuthFid = proto.Int64(static.UserAuthInfo.GetForumId())
		}
	}

	// 规则区
	outData.Rule = make([]*warmup.WarmupRule, 0)
	if static.AIanswerRule != nil {
		outData.Rule = append(outData.Rule, static.AIanswerRule)
	}
	if static.ForumManagerRecomRule != nil {
		outData.Rule = append(outData.Rule, static.ForumManagerRecomRule)
	}
	if static.OfficialAuditRule != nil {
		outData.Rule = append(outData.Rule, static.OfficialAuditRule)
	}

	if static.UserAuthInfo == nil || static.UserAuthInfo.GetStatus() != AuditPassStatus {
		// 非认证通过用户，必须有规则区数据，否则接口报错
		if len(outData.Rule) == 0 {
			tbcontext.WarningF(ctx, "build warmup response fail. no rule data")
			return errno.ErrCallServiceFail
		}
	}

	// 奖励区
	outData.Award = static.AwardInfo

	// 配置区
	outData.Config = static.ConfigInfo
	return nil
}
