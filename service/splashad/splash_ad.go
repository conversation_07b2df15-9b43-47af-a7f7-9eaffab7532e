package splashad

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/business/dsp"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/business/bidding"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/business/dsp/pangolin"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/adsense"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/stlog"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const SplashInteractionTitle = "interaction_title"
const SPlashInteractionDesc = "interaction_desc"

// GetSplashAd 获取开屏广告数据
func GetSplashAd(ctx context.Context, baseData *types.SplashDataData, resp *commonProto.SplashResIdl) int {
	dspConfigMap, _ := wordserver.QueryItemsNoPHPSerialized(
		ctx, "tb_wordlist_redis_ad_dsp_config",
		[]string{
			"splash_interaction_config",
		},
	)
	dspDataList, err := getDspList(ctx, baseData)
	if baseData != nil{
		dsp.LogAdReqCnt(ctx, baseData.TmpData)
	}
	if err != nil {
		tbcontext.WarningF(ctx, "GetSplashAd error, err:%s", err.Error())
		return tiebaerror.ERR_ANTI_ACTSCTRL_FAIL
	}
	transToSplashAdData(ctx, dspDataList, resp, dspConfigMap)
	return tiebaerror.ERR_SUCCESS
}

// getDspList 获取DSP数据列表
func getDspList(ctx context.Context, baseData *types.SplashDataData) ([]*client.App, error) {
	//1.获取开屏物料.. 目前只有穿山甲
	adList, err := getCsjDsp(ctx, baseData)
	if err != nil {
		tbcontext.WarningF(ctx, "getCsjDsp error, err:%s", err.Error())
		return nil, err
	}
	//2.竞价
	apps, err := adxBidding(ctx, baseData, adList)
	if err != nil {
		tbcontext.WarningF(ctx, "adBidding error, err:%s", err.Error())
		return apps, err
	}
	return apps, nil
}

// adBidding 对素材进行竞价处理，返回竞胜的素材..
func adxBidding(ctx context.Context, baseData *types.SplashDataData, adList []*client.App) ([]*client.App, error) {
	if baseData == nil || baseData.BaseObj == nil || baseData.Req == nil {
		return adList, fmt.Errorf("adBidding error, baseData is nil")
	}

	sampleId := ""
	cUid := ""
	shouBaiCUid := ""
	if baseData.Req.Data != nil && baseData.Req.Data.GetCommon() != nil {
		commonData := baseData.Req.Data.GetCommon()
		sampleId = commonData.GetSampleId()
		cUid = commonData.GetCuid()
		shouBaiCUid = commonData.GetShoubaiCuid()
	}

	arrSampleIds := make([]string, 0)
	if baseData.BaseObj.ObjRequest != nil {
		uid := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_STRING).(string)
		arrSampleIds = UbsAbtest.GetUbsAbtestSid(ctx, sampleId, uid, "")
	}

	reqInfo := bidding.ReqInfo{
		PageType:        bidding.SPLASH,
		IsNeedAd:        true,
		Cuid:            cUid,
		ShoubaiCuid:     shouBaiCUid,
		TouchLevel:      map[string]string{},             //开屏目前不需要轻触策略
		ResourceConf:    map[string]*adsense.AdxSource{}, //广告资源配置
		SampleAlias:     arrSampleIds,
		RedisUserGrowth: resource.RedisUserGrowth,
	}
	dataList := map[string]interface{}{
		"afd": adList,
	}

	dataList = bidding.ResourceBid(ctx, dataList, reqInfo, resource.RedisAdsense)
	if dataList == nil || len(dataList) == 0 || dataList["afd"] == nil {
		return adList, fmt.Errorf("bidding error, dataList is empty")
	} else {
		return dataList["afd"].([]*client.App), nil
	}
}

// transToSplashAdData 将广告数据转换为开屏广告数据
func transToSplashAdData(ctx context.Context, dspList []*client.App, resp *commonProto.SplashResIdl, dspConfigMap map[string]interface{}) {
	resp.Data = make([]*commonProto.SplashAdData, len(dspList))
	if len(dspList) == 0 {
		tbcontext.WarningF(ctx, "transToSplashAdData error, dspList is empty")
		return
	}
	for i, dsp := range dspList {
		if len(dsp.GoodsInfo) == 0 || dsp.GoodsInfo[0].LegoCard == nil {
			continue
		}
		splashInfo := handleSplashInfo(ctx, dsp.GoodsInfo, dspConfigMap)
		adData := &commonProto.SplashAdData{
			AdId:         dsp.AdId,
			BusinessType: dsp.BusinessType,
			SplashInfo:   &splashInfo,
			PlatformType: dsp.PlatformType,
			LogParam:     dsp.LogParam,
		}
		resp.Data[i] = adData
	}
}
func handleSplashInfo(ctx context.Context, goodsInfo []*client.GoodsInfo, dspConfigMap map[string]interface{}) string {
	if len(goodsInfo) == 0 || goodsInfo[0].LegoCard == nil {
		return ""
	}
	jsonStr := goodsInfo[0].LegoCard
	var splashInfo map[string]interface{}
	err := json.Unmarshal([]byte(*jsonStr), &splashInfo)
	if err != nil {
		tbcontext.WarningF(ctx, "handleSplashInfo unmarshal splash info error, err:%s,splashInfo:%s", err.Error(), &splashInfo)
		return ""
	}
	interactionTitle := "点击查看详情"
	interactionDesc := "跳转详情页或第三方应用"
	if dspConfigMap != nil {
		if interactionConfigStr, ok := dspConfigMap["splash_interaction_config"]; ok {
			if configStr, ok := interactionConfigStr.(string); ok {
				var interactionConfigMap map[string]string
				_ = json.Unmarshal([]byte(configStr), &interactionConfigMap)
				if interactionConfigMap != nil {
					if title, ok := interactionConfigMap[SplashInteractionTitle]; ok {
						interactionTitle = title
					}
					if desc, ok := interactionConfigMap[SPlashInteractionDesc]; ok {
						interactionDesc = desc
					}

				}
			}
		}
	}
	splashInfo[SplashInteractionTitle] = interactionTitle
	splashInfo[SPlashInteractionDesc] = interactionDesc
	marshal, err := JSONMarshal(splashInfo)
	if err != nil {
		tbcontext.WarningF(ctx, "handleSplashInfo marshal splash info error, err:%s", err.Error())
		return ""
	}
	return string(marshal)
}

// getCsjDsp 获取穿山甲广告信息
func getCsjDsp(ctx context.Context, baseData *types.SplashDataData) ([]*client.App, error) {
	limitConf := &pangolin.LimitConf{
		AdxSource: nil,
		KeySuffix: "frequency_apiCsj",
	}
	if baseData == nil || baseData.BaseObj == nil {
		return nil, fmt.Errorf("getCsjDsp error, baseData is nil")
	}
	c := pangolin.NewCsjDsp(ctx, resource.RedisAdsense, baseData.BaseObj.ObjRequest, limitConf, pangolin.WithNewParam(resource.RedisUserGrowth),
		pangolin.WithTimeoutMs(350),
		pangolin.WithTmpData(baseData.TmpData))

	csjDspList, err := c.GetCsjDsp("splash")
	defer c.Close()

	if err != nil {
		stlog.AddLog(ctx, "apiCsjFailed", err.Error())
		return nil, err
	}
	return csjDspList, nil
}

// Marshal but don't parse "<", ">", "&", U+2028, and U+2029
func JSONMarshal(t interface{}) ([]byte, error) {
	buffer := &bytes.Buffer{}
	encoder := json.NewEncoder(buffer)
	encoder.SetEscapeHTML(false)
	err := encoder.Encode(t)
	// 去掉末尾的换行符
	data := buffer.Bytes()
	if len(data) > 0 && data[len(data)-1] == '\n' {
		data = data[:len(data)-1]
	}
	return data, err
}
