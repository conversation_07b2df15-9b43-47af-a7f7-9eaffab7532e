package core

import (
	"context"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type WordList struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("word_list", func() engine.Job {
		return &WordListOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewWordList(ctx *engine.Context) *WordList {
	return &WordList{
		ctx: ctx,
	}
}

func (a *WordList) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	return true
}

func (a *WordList) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	staticField := baseData.StaticField
	conf, err := getWlConf(ctx, outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx, "getWlConf fail, err=%v", err)
		return err
	}
	staticField.WordList = conf

	return nil
}

type WordListOperator struct {
}

func (rdop *WordListOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewWordList(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "WordList execute fail: %v", err)
		return err
	}

	return nil
}

// 获取词表相关配置hh
func getWlConf(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) (map[string]interface{}, error) {
	wordList := make(map[string]interface{})

	redisKeys := []string{
		"factor_x",
		"factor_y",
		"factor_a",
		"realPostNum",
		"topics_not_the_list_percent", //不在榜话题热度计算系数
		"porn_limit",                  //添加配置项 灰色话题条数限制
	}

	redisRes, err := wordserver.QueryKeys(ctx, "tb_wordlist_redis_hottopic_conf", redisKeys)
	if err != nil || len(redisRes) != len(redisKeys) {
		tbcontext.WarningF(ctx, "wordserver getNotTheListWordlistConf query keys fail, input=%s, err=%v", common.ToString(redisKeys), err)
		return wordList, err
	}

	realPostName := cast.ToInt(redisRes[3])
	if realPostName == 0 {
		realPostName = 20
	}
	if realPostName > 50 {
		realPostName = 50
	}
	wordList["realPostNum"] = realPostName

	factorX := cast.ToInt(redisRes[0])
	if factorX == 0 {
		factorX = 1
	}
	wordList["factor_x"] = factorX

	factorY := cast.ToInt(redisRes[1])
	if factorY == 0 {
		factorY = 2
	}
	wordList["factor_y"] = factorY

	factorA := cast.ToInt(redisRes[2])
	if factorA == 0 {
		factorA = 20
	}
	wordList["factor_a"] = factorA

	wordList["topics_not_the_list_percent"] = cast.ToInt(redisRes[4])

	wordList["porn_limit"] = cast.ToInt(redisRes[5])

	return wordList, nil
}
