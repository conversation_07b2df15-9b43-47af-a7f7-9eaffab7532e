package core

import (
	"context"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbs"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type TopicDetailResp struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("topic_detail_resp", func() engine.Job {
		return &TopicDetailRespOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewTopicDetailResp(ctx *engine.Context) *TopicDetailResp {
	return &TopicDetailResp{
		ctx: ctx,
	}
}
func (a *TopicDetailResp) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {

	return true
}
func (a *TopicDetailResp) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	if baseData == nil || baseData.StaticField == nil {
		return nil
	}
	staticField := baseData.StaticField

	// 打点记录用户排序方式
	baseData.BaseObj.ObjResponse.AddLog("topic_id", staticField.TopicID)

	outData.TopicInfo = staticField.TopicInfo
	outData.User = staticField.User
	// outData.Switch = staticField.Switch
	outData.AreaCardList = []*client.AreaCard{
		{
			AreaType: proto.String("content"),
			CardData: staticField.ContentAreaCardList,
		},
	}
	outData.HasMore = &staticField.HasMore
	if staticField.Meta != nil {
		outData.Meta = &clientTopicDetail.TopicMeta{
			Title:       proto.String(staticField.Meta.Title),
			Description: proto.String(staticField.Meta.Description),
			Keywords:    proto.String(staticField.Meta.Keywords),
		}	
	}

	//获取tbs
	isLogin := true
	if staticField.IsLogin == 0 {
		isLogin = false
	}
	outData.Tbs = proto.String(tbs.CreateTbs(ctx, baseData.BaseObj.Req).Gene(isLogin))

	return nil
}

type TopicDetailRespOperator struct {
}

func (rdop *TopicDetailRespOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewTopicDetailResp(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "topic_detail_response execute fail: %v", err)
		return err
	}

	return nil
}
