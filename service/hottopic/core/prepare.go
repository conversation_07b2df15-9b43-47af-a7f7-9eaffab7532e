package core

import (
	"context"
	"net/url"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Prepare struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("topic_detail_prepare", func() engine.Job {
		return &PrepareOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPrepare(ctx *engine.Context) *Prepare {
	return &Prepare{
		ctx: ctx,
	}
}

func (p *Prepare) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData == nil || baseData.StaticField == nil {
		return false
	}

	objReq := baseData.BaseObj.ObjRequest
	baseData.StaticField.StrClientVersion = common.Tvttt(objReq.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	tbcontext.WarningF(ctx, "client_version_%s", baseData.StaticField.StrClientVersion)
	// 12.60.0 以前不做处理
	if len(baseData.StaticField.StrClientVersion) > 0 && clientvers.Compare("12.60.0", baseData.StaticField.StrClientVersion) < 0 {
		tbcontext.WarningF(ctx, "version[%s] is invaild!", baseData.StaticField.StrClientVersion)
		return false
	}

	return true
}

func (p *Prepare) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	staticField.UserID = common.Tvttt(objReq.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	tbcontext.WarningF(ctx, "staticField.UserID_pre_%d", staticField.UserID)
	//staticField.UserID = 2732760925

	staticField.IsLogin = int32(1)
	if staticField.UserID <= 0 {
		staticField.IsLogin = 0
	}

	tbcontext.WarningF(ctx, "staticField.UserID_pre1_%d staticField.IsLogin_%d", staticField.UserID, staticField.IsLogin)

	staticField.User = &client.User{
		IsLogin: &staticField.IsLogin,
		Id:      &staticField.UserID,
	}

	staticField.IntClientType = common.Tvttt(objReq.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	staticField.StrClientVersion = common.Tvttt(objReq.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	staticField.Cuid = common.Tvttt(objReq.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)

	sampleID := common.Tvttt(objReq.GetCommonAttr("sample_id", ""), common.TTT_STRING).(string)
	staticField.SampleIDs = UbsAbtest.GetUbsAbtestSid(ctx, sampleID, strconv.FormatInt(staticField.UserID, 10), "")

	staticField.TopicID = common.Tvttt(objReq.GetPrivateAttr("topic_id", 0), common.TTT_INT64).(int64)
	staticField.TopicName = common.Tvttt(objReq.GetPrivateAttr("topic_name", ""), common.TTT_STRING).(string)
	if len(staticField.TopicName) > 0 {
		topicName := staticField.TopicName
		if strings.Contains(topicName, "+") {
			tbcontext.WarningF(ctx, "The string[%s] contains +, Replacing + with 2B", staticField.TopicName)
			// 替换加号为 %2B
			topicName = strings.ReplaceAll(staticField.TopicName, "+", "%2B")
		}

		topicName, err := url.QueryUnescape(topicName)
		if err != nil {
			tbcontext.WarningF(ctx, "decode topic_name[%s] fail, err=%v", topicName, err)
		} else {
			tbcontext.WarningF(ctx, "after urldecode, the topicname[%s] become to The string[%s]", staticField.TopicName, topicName)
			staticField.TopicName = topicName
		}
	}

	//话题id和name都不存在
	if len(staticField.TopicName) == 0 && staticField.TopicID <= 0 {
		tbcontext.WarningF(ctx, "topic_name[%s] and topic_id[%d] is invaild!", staticField.TopicName, staticField.TopicID)
		return nil
	}

	return nil
}

type PrepareOperator struct {
}

func (op *PrepareOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData
	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)

	webCtx := ctx.CallerCtx()
	obj := NewPrepare(ctx)
	if !obj.IsValid(webCtx, baseData) {
		return nil
	}

	err := obj.Execute(webCtx, outData, baseData)
	if err != nil {
		tbcontext.WarningF(webCtx, "topic_detail_prepare execute fail, err=%v", err)
		return err
	}
	return nil
}
