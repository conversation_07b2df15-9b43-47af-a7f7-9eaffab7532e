package core

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/frspage"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	hottopicUtil "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/hottopic"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func init() {
	if err := engine.RegisterOperator("topic_detail_topic_info", func() engine.Job {
		return &topicInfo{}
	}); err != nil {
		panic(err.Error())
	}
}

type topicInfo struct {
	topicName string
	topicID   int64
	isDeleted int32
}

func (t *topicInfo) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	return true
}

func (t *topicInfo) Execute(ctx context.Context, outData *frspage.FrsPageDataRes, baseData *types.CTopicDetailBaseData) error {
	req := baseData.Request
	topicID := baseData.StaticField.TopicID
	topicName := baseData.StaticField.TopicName
	topic := hottopicUtil.GetTopicInfo(ctx, int32(topicID), topicName, req.GetIsFrom())
	if topic == nil {
		return errors.New("topic not found")
	}
	baseData.StaticField.TopicInfo = topic.RecomTopicList
	if topic.OpStatus == 0 {
		tbcontext.WarningF(ctx, "the topic is deleted: %v", baseData.StaticField.TopicInfo)
		baseData.StaticField.IsDeleted = 1
		baseData.StaticField.TopicInfo = nil
	}
	baseData.StaticField.TopicID = int64(topic.GetTopicId())
	return nil
}

func (t *topicInfo) DoImpl(ctx *engine.Context) error {
	var outData *frspage.FrsPageDataRes
	var baseData *types.CTopicDetailBaseData
	
	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	if !t.IsValid(ctx.CallerCtx(), baseData) {
		return errors.New("topic_detail_topic_info is not valid")
	}
	if err := t.Execute(ctx.CallerCtx(), outData, baseData); err != nil {
		return err
	}

	return nil
}
