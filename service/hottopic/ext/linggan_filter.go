package ext

import (
	"context"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type LingganFilter struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("linggan_filter", func() engine.Job {
		return &LingganFilterOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewLingganFilter(ctx *engine.Context) *LingganFilter {
	return &LingganFilter{
		ctx: ctx,
	}
}

func (a *<PERSON>ganFilter) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if len(baseData.StaticField.RelateThreadIds) == 0 || baseData.Request.GetIsFrom() != 1 {
		return false
	}
	return true
}

func (a *LingganFilter) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	staticField := baseData.StaticField

	// todo 过滤内容
	relateThreadList := []*client.ThreadInfo{}

	staticField.RelateThreadList = relateThreadList
	return nil
}

type LingganFilterOperator struct {
}

func (rdop *LingganFilterOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewLingganFilter(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "TopicInfo execute fail: %v", err)
		return err
	}

	return nil
}
