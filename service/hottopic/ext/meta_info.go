/*
 * @Author: gao<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-15 19:13:26
 * @LastEditors: gaochaochen <EMAIL>
 * @LastEditTime: 2024-11-20 18:54:19
 * @FilePath: /go-client-forum/service/hottopic/ext/meta_info.go
 * @Description:
 *
 * Copyright (c) 2024 by Baidu, All Rights Reserved.
 */
package ext

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/php"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	DefaultTopicTDKTemplateTitle    = "%s_百度贴吧"
	DefaultTopicTDKTemplateDesc     = "百度贴吧-%s专题，为您展现优质的%s各类信息，在这里您可以找到关于%s的相关内容及最新的%s贴子"
	DefaultTopicTDKTemplateKeywords = "%s，%s热议，%s点评，%s吧友讨论，%s话题"

	PornTopicTDKTemplateTitle    = "%s_百度贴吧"
	PornTopicTDKTemplateDesc     = "含有“%s”等内容的视频在线观看，含有“%s”等内容的原创高清视频，视频主要内容：%s"
	PornTopicTDKTemplateKeywords = "%s"

	VideoPornTopicTDKTemplateTitle    = "%s_百度贴吧"
	VideoPornTopicTDKTemplateDesc     = "%s，%s高清观看视频，%s视频社区在线，%s高清中文网，%s中文动漫版"
	VideoPornTopicTDKTemplateKeywords = "%s"
)

type MetaInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("build_meta", func() engine.Job {
		return &MetaInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPornMetaInfo(ctx *engine.Context) *MetaInfo {
	return &MetaInfo{
		ctx: ctx,
	}
}

func (a *MetaInfo) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	return baseData != nil &&
		baseData.StaticField != nil &&
		baseData.StaticField.TopicInfo != nil &&
		baseData.StaticField.TopicInfo.GetTopicId() != 0 &&
		baseData.StaticField.TopicInfo.GetTopicName() != ""
}

func (a *MetaInfo) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	staticField := baseData.StaticField

	// 获取topicName
	topicID := staticField.TopicInfo.GetTopicId()
	topicName := staticField.TopicInfo.GetTopicName()

	// 默认模板
	metaInfo := types.MetaInfo{
		// Title:       fmt.Sprintf(DefaultTopicTDKTemplateTitle),
		Description: fmt.Sprintf(DefaultTopicTDKTemplateDesc, topicName, topicName, topicName, topicName),
		Keywords:    fmt.Sprintf(DefaultTopicTDKTemplateKeywords, topicName, topicName, topicName, topicName, topicName),
	}

	// 灰色话题需特殊处理
	if baseData.StaticField.TopicInfo.GetIsPorn() == 1 {
		// 灰色话题切词
		words, err := a.getSpilitedWords(ctx, topicID, topicName)
		if err != nil {
			return err
		}
		if len(words) > 3 {
			topicName = `包含"` + words[0] + `"、"` + words[1] + `"等关键词的内容`
		} else if len(words) == 0 {
			tbcontext.WarningF(ctx, "get words empty of topic: %d", topicID)
		} else {
			topicName = `包含"` + strings.Join(words, `"、"`) + `"的内容`
		}

		// 对切词结果进行过滤无用词
		wlKeys := []string{
			"query_video_keyword",
			"porn_split_useless_word_list_0",
			"porn_split_useless_word_list_1",
			"porn_split_useless_word_list_2",
			"porn_split_useless_word_list_3",
		}
		wlTableName := "tb_wordlist_redis_naturesearch_pushtopic"
		wlRes, err := wordserver.QueryKeys(ctx, wlTableName, wlKeys)
		if err != nil {
			tbcontext.WarningF(ctx, "wordserver query keys fail, input=%s, err=%v", common.ToString(wlKeys), err)
			return err
		}
		videoKeyWords := a.getListFromWordList(ctx, wlRes[0])
		pornSplitUnlessWords0 := a.getListFromWordList(ctx, wlRes[1])
		pornSplitUnlessWords1 := a.getListFromWordList(ctx, wlRes[2])
		pornSplitUnlessWords2 := a.getListFromWordList(ctx, wlRes[3])
		pornSplitUnlessWords3 := a.getListFromWordList(ctx, wlRes[4])
		// 判断是否是视频
		isVideo := false
		for _, keyword := range videoKeyWords {
			if strings.Contains(topicName, keyword) {
				isVideo = true
				break
			}
		}
		if isVideo {
			metaInfo.Title = fmt.Sprintf(VideoPornTopicTDKTemplateTitle, topicName)
			metaInfo.Description = fmt.Sprintf(VideoPornTopicTDKTemplateDesc, topicName, topicName, topicName, topicName, topicName)
			metaInfo.Keywords = topicName
		} else {
			arrBlackWordList := append(pornSplitUnlessWords0, pornSplitUnlessWords1...)
			arrBlackWordList = append(arrBlackWordList, pornSplitUnlessWords2...)
			arrBlackWordList = append(arrBlackWordList, pornSplitUnlessWords3...)
			title := ""
			threadList := baseData.StaticField.RelateThreadList
			for _, thread := range threadList {
				title = title + thread.GetTitle()
			}
			if title == "" {
				title = topicName
			}
			req := map[string]interface{}{
				"req": map[string]interface{}{
					"query": title,
				},
			}
			res := &struct {
				ErrNo  uint32 `json:"errno"`
				ErrMsg string `json:"errmsg"`
				Ret    struct {
					ArrWordSegment []string `json:"arr_word_segment"`
				} `json:"ret"`
			}{}
			err := tbservice.Call(ctx, "hottopic", "nlpcWordSeg", req, res, tbservice.WithConverter(tbservice.JSONITER))
			if err != nil || res.ErrNo != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "fail to call service hottopic:nlpcWordSeg or reply of output is nil, "+
					"input = %s, output = %s, err = %v", common.ToString(req), common.ToString(res), err)
			}
			var arrSeg []string
			for _, seg := range res.Ret.ArrWordSegment {
				seg = strings.TrimSpace(seg)
				if seg == "" || len([]rune(seg)) == 1 || a.contains(arrBlackWordList, seg) {
					continue
				}
				arrSeg = append(arrSeg, seg)
			}
			// 去重
			arrSeg = a.unique(arrSeg)
			// 截取前 10 个元素
			if len(arrSeg) > 10 {
				arrSeg = arrSeg[:10]
			}
			// 使用逗号拼接
			strKeyWords := strings.Join(arrSeg, "，")
			// 替换
			metaInfo.Title = fmt.Sprintf(PornTopicTDKTemplateTitle, topicName)
			metaInfo.Description = fmt.Sprintf(PornTopicTDKTemplateDesc, topicName, topicName, topicName)
			metaInfo.Keywords = strKeyWords
		}
	}

	baseData.StaticField.TopicInfo.TopicName = proto.String(topicName)
	baseData.StaticField.Meta = &metaInfo

	return nil
}

func (a *MetaInfo) getSpilitedWords(ctx context.Context, topicID uint64, topicName string) ([]string, error) {
	req := map[string]interface{}{
		"req": map[string]interface{}{
			"topic_id":   topicID,
			"topic_name": topicName,
		},
	}
	res := &struct {
		ErrNo  uint32 `json:"errno"`
		ErrMsg string `json:"errmsg"`
		Ret    string `json:"ret"`
	}{}
	err := tbservice.Call(ctx, "hottopic", "getTopicNameSplitStr", req, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.ErrNo != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service hottopic:getTopicNameSplitStr or reply of output is nil, "+
			"input = %s, output = %s, err = %v", common.ToString(req), common.ToString(res), err)
		return nil, errors.New("fail to call service hottopic:getTopicNameSplitStr")
	}

	if res.Ret == "" {
		return nil, nil
	}

	arrWord := strings.Split(res.Ret, "\t")
	arrWord = a.unique(arrWord) // 去重

	return arrWord, nil

}

func (a *MetaInfo) getListFromWordList(ctx context.Context, raw string) []string {
	res := make([]string, 0)
	tmp, err := php.Unserialize([]byte(raw))
	if err != nil {
		tbcontext.WarningF(ctx, "php Unserialize fail. input:%v, err:%v", raw, err)
		return nil
	}
	err = common.StructAToStructBCtx(ctx, tmp, &res)
	if err != nil {
		tbcontext.WarningF(ctx, "change to []string fail. tmp:%v, err:%v", tmp, err)
		return nil
	}

	return res
}

func (a *MetaInfo) unique(input []string) []string {
	seen := make(map[string]struct{})
	var result []string
	for _, word := range input {
		if _, found := seen[word]; !found {
			seen[word] = struct{}{}
			result = append(result, word)
		}
	}
	return result
}

func (a *MetaInfo) contains(list []string, item string) bool {
	for _, v := range list {
		if v == item {
			return true
		}
	}
	return false
}

type MetaInfoOperator struct {
}

func (rdop *MetaInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewPornMetaInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "TopicInfo execute fail: %v", err)
		return err
	}

	return nil
}
