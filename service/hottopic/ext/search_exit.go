package ext

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type SearchExit struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("search_exit", func() engine.Job {
		return &SearchExitOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewSearchExit(ctx *engine.Context) *SearchExit {
	return &SearchExit{
		ctx: ctx,
	}
}

func (a *SearchExit) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData == nil || baseData.StaticField == nil {
		return false
	}

	//话题第一页没帖子数据退场 话题被删除is_deleted=1也会被退场
	if (baseData.Request.GetPn() <= 1 && len(baseData.StaticField.RelateThreadList) == 0) || baseData.StaticField.IsDeleted == 1 {
		return true
	}

	return false
}

func (a *SearchExit) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	// todo 搜索退场
	if baseData == nil || baseData.StaticField == nil {
		return nil
	}
	staticField := baseData.StaticField

	topicId := staticField.TopicID

	input := map[string]interface{}{
		"type":    "url",
		"op_type": "del",
		"url":     fmt.Sprintf("tieba.baidu.com/hottopic/browse/hottopic?topic_id=%d", topicId),
		"level":   1,
	}

	var output interface{}
	err := tbservice.Call(ctx, "push", "pushDelVippingUrl", input, &output, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil {
		tbcontext.WarningF(ctx, "call push pushDelVippingUrl error, input[%v], errmsg[%s], topic_id[%d]", input, err.Error(), topicId)
		return err
	}

	return nil
}

type SearchExitOperator struct {
}

func (rdop *SearchExitOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewSearchExit(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "search_exit execute fail: %v", err)
		return err
	}

	return nil
}
