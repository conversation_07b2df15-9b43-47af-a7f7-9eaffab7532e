package ext

import (
	"context"
	"github.com/spf13/cast"
	protoCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"sort"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type AdjustArea struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("adjust_area", func() engine.Job {
		return &AdjustAreaOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewAdjustArea(ctx *engine.Context) *AdjustArea {
	return &AdjustArea{
		ctx: ctx,
	}
}

func (a *AdjustArea) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData == nil || baseData.StaticField == nil {
		return false
	}
	if len(baseData.StaticField.RelateThreadList) == 0 &&
		len(baseData.StaticField.ModuleInfo.CommonThread.ThreadList) == 0 &&
		len(baseData.StaticField.ModuleInfo.VoteThread.ThreadList) == 0 &&
		len(baseData.StaticField.ModuleInfo.ScoreThread.ThreadList) == 0 &&
		len(baseData.StaticField.ModuleInfo.PKThread.ThreadList) == 0 &&
		baseData.StaticField.ModuleInfo.AiBot.AiBotUID == 0 &&
		baseData.StaticField.ModuleInfo.AiGameNew.AiGameUID == 0 {
		return false
	}
	return true
}

func (a *AdjustArea) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	if baseData == nil || baseData.StaticField == nil {
		return nil
	}
	staticField := baseData.StaticField

	// 过滤帖子
	staticField.RelateThreadList = filterThreadFromList(ctx, staticField.RelateThreadInfo.FilterThreadIds, staticField.RelateThreadList)
	staticField.ModuleInfo.CommonThread.ThreadList = filterThreadFromList(ctx, staticField.ModuleInfo.CommonThread.ThreadInfo.FilterThreadIds,
		staticField.ModuleInfo.CommonThread.ThreadList)
	staticField.ModuleInfo.VoteThread.ThreadList = filterThreadFromList(ctx, staticField.ModuleInfo.VoteThread.ThreadInfo.FilterThreadIds,
		staticField.ModuleInfo.VoteThread.ThreadList)
	staticField.ModuleInfo.ScoreThread.ThreadList = filterThreadFromList(ctx, staticField.ModuleInfo.ScoreThread.ThreadInfo.FilterThreadIds,
		staticField.ModuleInfo.ScoreThread.ThreadList)
	staticField.ModuleInfo.PKThread.ThreadList = filterThreadFromList(ctx, staticField.ModuleInfo.PKThread.ThreadInfo.FilterThreadIds,
		staticField.ModuleInfo.PKThread.ThreadList)

	// 组装relate_thread信息
	buildRleateThread(ctx, staticField)

	// 组装模块信息（热议贴+实时回复流+，HotPostInfo和realPostInfo 是postId 为key Map 、ai游戏、）
	buildModule(ctx, staticField.ModuleInfo)

	// moduleorder
	moduleorderList := buildModuleOrder(ctx, staticField.ModuleInfo)

	buildFeedList(ctx, outData, baseData, moduleorderList)

	return nil
}

type AdjustAreaOperator struct {
}

func (rdop *AdjustAreaOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewAdjustArea(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "adjust_area execute fail: %v", err)
		return err
	}

	return nil
}

// 过滤帖子
func filterThreadFromList(ctx context.Context, filterThreadIDs []int64, threadList []*client.ThreadInfo) []*client.ThreadInfo {
	if len(filterThreadIDs) == 0 || len(threadList) == 0 {
		return threadList
	}

	var newThreadList []*client.ThreadInfo
	for index := 0; index < len(threadList); index++ {
		threadInfo := threadList[index]
		// tbcontext.WarningF(ctx, "tid_len_%d index_%d tid_%d", len(threadList), index, threadInfo.GetId())
		if php2go.InArray(threadInfo.GetId(), filterThreadIDs) {
			tbcontext.WarningF(ctx, "tid_%d hit filter!", threadInfo.GetId())
		} else {
			newThreadList = append(newThreadList, threadInfo)
		}
	}
	return newThreadList
}

// buildRelateThread
func buildRleateThread(ctx context.Context, staticField *types.TopicDetailStaticField) {
	if staticField.RelateThreadInfo.HotPostInfo == nil {
		return
	}

	// build 热议信息
	for _, threadInfo := range staticField.RelateThreadList {
		var vaildPostList []*client.Post
		threadType := threadInfo.GetThreadType()
		// week50 抽奖贴的时候不下发hot_post_list，不展示热评模块
		if threadType == 76 {
			continue
		}
		if postList, ok := staticField.RelateThreadInfo.HotPostInfo[threadInfo.GetId()]; ok {
			for _, post := range postList {
				if !php2go.InArray(int64(post.GetId()), staticField.RelateThreadInfo.FilterPostIds) {
					// tbcontext.WarningF(ctx, "the hot post_pid[%d] not hit filter post!", post.GetId())
					vaildPostList = append(vaildPostList, post)
				} else {
					tbcontext.WarningF(ctx, "the hot post_pid[%d] hit filter post!", post.GetId())
				}
			}
			// tbcontext.WarningF(ctx, "vaildPostList_%v", vaildPostList)
			threadInfo.HotPostList = vaildPostList
		}
	}
}

// build 模块信息数据流
func buildModule(ctx context.Context, moduleInfo *types.ModuleInfo) {
	// build thread_Info
	buildModuleThread(ctx, moduleInfo.CommonThread)
	buildModuleThread(ctx, moduleInfo.VoteThread)
	buildModuleThread(ctx, moduleInfo.ScoreThread)
	buildModuleThread(ctx, moduleInfo.PKThread)
}

func buildModuleThread(ctx context.Context, moduleThread types.ModuleThread) {
	for _, threadInfo := range moduleThread.ThreadList {
		// build 帖子实时数据流
		if postList, ok := moduleThread.ThreadInfo.RealPostInfo[threadInfo.GetId()]; ok {
			var vaildPostList []*client.Post
			for _, post := range postList {
				if !php2go.InArray(int64(post.GetId()), moduleThread.ThreadInfo.FilterPostIds) {
					// tbcontext.WarningF(ctx, "the real post_pid[%d] not hit filter post!", post.GetId())
					vaildPostList = append(vaildPostList, post)
				} else {
					tbcontext.WarningF(ctx, "the real post_pid[%d] hit filter post!", post.GetId())
				}
			}
			threadInfo.FullPostList = vaildPostList
		}

		// build 帖子热议数据流
		if postList, ok := moduleThread.ThreadInfo.HotPostInfo[threadInfo.GetId()]; ok {
			var vaildPostList []*client.Post
			for _, post := range postList {
				if !php2go.InArray(int64(post.GetId()), moduleThread.ThreadInfo.FilterPostIds) {
					tbcontext.WarningF(ctx, "the hot post_pid[%d] not hit filter post!", post.GetId())
					vaildPostList = append(vaildPostList, post)
				} else {
					tbcontext.WarningF(ctx, "the hot post_pid[%d] hit filter post!", post.GetId())
				}
			}
			// tbcontext.WarningF(ctx, "vaildPostList_%v", vaildPostList)
			threadInfo.HotPostList = vaildPostList
		}

		// 推荐tag
		if threadRecommendTag, ok := moduleThread.ThreadInfo.RecomTagInfo[threadInfo.GetId()]; ok {
			threadInfo.ThreadRecommendTag = threadRecommendTag
		}
	}
}

// 组装module_order 顺序
func buildModuleOrder(ctx context.Context, moduleInfo *types.ModuleInfo) []map[string]types.ModuleOrderInfo {
	var moduleOrderList []map[string]types.ModuleOrderInfo
	var cardTypes = []string{
		"common_thread",
		"vote_thread",
		"score_thread",
		"pk_thread",
		"ai_game",
		"relate_forum",
		"relate_topic",
		"ai_bot",
		"ai_game_new",
	}

	for _, cardType := range cardTypes {
		switch cardType {
		case "common_thread":
			if len(moduleInfo.CommonThread.ThreadList) > 0 {
				moduleOrderList = append(moduleOrderList, map[string]types.ModuleOrderInfo{
					cardType: {
						ModuleOrder: moduleInfo.CommonThread.ModuleOrder,
						ModuleInfo: types.CardDataInfo{
							ThreadList: moduleInfo.CommonThread.ThreadList,
						},
					},
				})
			}
		case "vote_thread":
			if len(moduleInfo.VoteThread.ThreadList) > 0 {
				moduleOrderList = append(moduleOrderList, map[string]types.ModuleOrderInfo{
					cardType: {
						ModuleOrder: moduleInfo.VoteThread.ModuleOrder,
						ModuleInfo: types.CardDataInfo{
							ThreadList: moduleInfo.VoteThread.ThreadList,
						},
					},
				})
			}
		case "score_thread":
			if len(moduleInfo.ScoreThread.ThreadList) > 0 {
				moduleOrderList = append(moduleOrderList, map[string]types.ModuleOrderInfo{
					cardType: {
						ModuleOrder: moduleInfo.ScoreThread.ModuleOrder,
						ModuleInfo: types.CardDataInfo{
							ThreadList: moduleInfo.ScoreThread.ThreadList,
						},
					},
				})
			}
		case "pk_thread":
			if len(moduleInfo.PKThread.ThreadList) > 0 {
				moduleOrderList = append(moduleOrderList, map[string]types.ModuleOrderInfo{
					cardType: {
						ModuleOrder: moduleInfo.PKThread.ModuleOrder,
						ModuleInfo: types.CardDataInfo{
							ThreadList: moduleInfo.PKThread.ThreadList,
						},
					},
				})
			}
		case "ai_game":
			if len(moduleInfo.AiGame.AiGameInfoList) > 0 {
				moduleOrderList = append(moduleOrderList, map[string]types.ModuleOrderInfo{
					cardType: {
						ModuleOrder: moduleInfo.AiGame.ModuleOrder,
						ModuleInfo: types.CardDataInfo{
							AiGameList: moduleInfo.AiGame.AiGameInfoList,
						},
					},
				})
			}
		case "relate_forum":
			if len(moduleInfo.RelateForum.ForumInfoList) > 0 {
				moduleOrderList = append(moduleOrderList, map[string]types.ModuleOrderInfo{
					cardType: {
						ModuleOrder: moduleInfo.RelateForum.ModuleOrder,
						ModuleInfo: types.CardDataInfo{
							ForumInfoList: moduleInfo.RelateForum.ForumInfoList,
						},
					},
				})
			}
		case "relate_topic":
			if len(moduleInfo.RelateTopic.TopicInfoList) > 0 {
				moduleOrderList = append(moduleOrderList, map[string]types.ModuleOrderInfo{
					cardType: {
						ModuleOrder: moduleInfo.RelateTopic.ModuleOrder,
						ModuleInfo: types.CardDataInfo{
							TopicInfoList: moduleInfo.RelateTopic.TopicInfoList,
						},
					},
				})
			}
		case "ai_bot":
			if moduleInfo.AiBot.AiBotInfo != nil {
				moduleOrderList = append(moduleOrderList, map[string]types.ModuleOrderInfo{
					cardType: {
						ModuleOrder: moduleInfo.AiBot.ModuleOrder,
						ModuleInfo: types.CardDataInfo{
							AiBotInfo: moduleInfo.AiBot.AiBotInfo,
						},
					},
				})
			}
		case "ai_game_new":
			if moduleInfo.AiGameNew.AiGameInfo != nil {
				moduleOrderList = append(moduleOrderList, map[string]types.ModuleOrderInfo{
					cardType: {
						ModuleOrder: moduleInfo.AiGameNew.ModuleOrder,
						ModuleInfo: types.CardDataInfo{
							AiGameNew: moduleInfo.AiGameNew.AiGameInfo,
						},
					},
				})
			}
		}
	}

	if len(moduleOrderList) == 0 {
		return moduleOrderList
	}

	moduleOrderList = sortModuleOrderList(ctx, moduleOrderList)
	return moduleOrderList
}

// 对mpodule_order排序
func sortModuleOrderList(ctx context.Context, moduleOrderList []map[string]types.ModuleOrderInfo) []map[string]types.ModuleOrderInfo {
	var pairs []struct {
		Key      string
		Value    int32
		KeyIndex int
	}
	for index, m := range moduleOrderList {
		for key, value := range m {
			pairs = append(pairs, struct {
				Key      string
				Value    int32
				KeyIndex int
			}{key, value.ModuleOrder, index})
		}
	}
	// 定义排序的比较函数
	sort.Slice(pairs, func(i, j int) bool {
		return pairs[i].Value < pairs[j].Value
	})
	sortedModuleOrderList := make([]map[string]types.ModuleOrderInfo, len(moduleOrderList))
	for i, pair := range pairs {
		sortedModuleOrderList[i] = moduleOrderList[pair.KeyIndex]
	}
	return sortedModuleOrderList
}

// 插楼逻辑
func buildFeedList(ctx context.Context, outData *clientTopicDetail.DataRes,
	baseData *types.CTopicDetailBaseData, moduleOrderList []map[string]types.ModuleOrderInfo) {
	staticField := baseData.StaticField
	if nil == staticField {
		return
	}

	if len(moduleOrderList) == 0 && len(staticField.RelateThreadList) == 0 {
		return
	}

	var allThreadMap = make(map[int64]int)

	// 查看是不是官方吧数据
	officialForumIDMap := GetOfficialForumIds(ctx)

	// relate_thread为空，直接遍历moduleOrderList
	if len(staticField.RelateThreadList) == 0 {
		for _, moduleOrderInfoMap := range moduleOrderList {
			for cardType, moduleOrderInfo := range moduleOrderInfoMap {
				buildAreaCardList(ctx, cardType, outData, baseData, &moduleOrderInfo.ModuleInfo, allThreadMap, officialForumIDMap)
			}
		}
		return
	} else if len(moduleOrderList) == 0 {
		buildAreaCardList(ctx, "relate_thread", outData, baseData, &types.CardDataInfo{
			ThreadList: staticField.RelateThreadList,
		}, allThreadMap, officialForumIDMap)
		return
	}

	// 不为空，组装
	for i, _ := range staticField.RelateThreadList {
		for j := 0; j < len(moduleOrderList); j++ {
			moduleOrderInfoMap := moduleOrderList[j]
			for cardType, moduleOrderInfo := range moduleOrderInfoMap {
				if moduleOrderInfo.ModuleOrder > 0 && int(moduleOrderInfo.ModuleOrder) <= i+len(staticField.ContentAreaCardList)+1 {
					buildAreaCardList(ctx, cardType, outData, baseData, &moduleOrderInfo.ModuleInfo, allThreadMap, officialForumIDMap)
					moduleOrderList = append(moduleOrderList[:j], moduleOrderList[j+1:]...)
					j--
				}
			}
		}

		staticField.ContentAreaCardList = append(staticField.ContentAreaCardList, buildAreaCardData(ctx, "relate_thread", &types.CardDataInfo{
			ThreadList: staticField.RelateThreadList,
		}, i, allThreadMap, officialForumIDMap))
	}

	// 末尾模块信息
	for _, moduleOrderInfoMap := range moduleOrderList {
		for cardType, moduleOrderInfo := range moduleOrderInfoMap {
			buildAreaCardList(ctx, cardType, outData, baseData, &moduleOrderInfo.ModuleInfo, allThreadMap, officialForumIDMap)
		}
	}

	// 过滤null
	for i := 0; i < len(staticField.ContentAreaCardList); i++ {
		if staticField.ContentAreaCardList[i] == nil {
			staticField.ContentAreaCardList = append(staticField.ContentAreaCardList[:i], staticField.ContentAreaCardList[i+1:]...)
			i--
		}
	}

	return
}

func buildAreaCardList(ctx context.Context, cardType string, outData *clientTopicDetail.DataRes,
	baseData *types.CTopicDetailBaseData, cardDatainfo *types.CardDataInfo, allThreadMap map[int64]int, officialForumIDMap map[uint32]struct{}) {
	staticField := baseData.StaticField
	switch cardType {
	case "relate_thread", "common_thread", "vote_thread", "score_thread", "pk_thread":
		for index, _ := range cardDatainfo.ThreadList {
			staticField.ContentAreaCardList = append(staticField.ContentAreaCardList,
				buildAreaCardData(ctx, cardType, cardDatainfo, index, allThreadMap, officialForumIDMap))
		}
	case "ai_game":
		for index, _ := range cardDatainfo.AiGameList {
			staticField.ContentAreaCardList = append(staticField.ContentAreaCardList,
				buildAreaCardData(ctx, cardType, cardDatainfo, index, allThreadMap, officialForumIDMap))
		}
	case "relate_forum":
		staticField.ContentAreaCardList = append(staticField.ContentAreaCardList,
			buildAreaCardData(ctx, cardType, cardDatainfo, 0, allThreadMap, officialForumIDMap))
	case "relate_topic":
		staticField.ContentAreaCardList = append(staticField.ContentAreaCardList,
			buildAreaCardData(ctx, cardType, cardDatainfo, 0, allThreadMap, officialForumIDMap))
	case "ai_bot":
		staticField.ContentAreaCardList = append(staticField.ContentAreaCardList,
			buildAreaCardData(ctx, cardType, cardDatainfo, 0, allThreadMap, officialForumIDMap))
	case "ai_game_new":
		staticField.ContentAreaCardList = append(staticField.ContentAreaCardList,
			buildAreaCardData(ctx, cardType, cardDatainfo, 0, allThreadMap, officialForumIDMap))
	}
}

func buildAreaCardData(ctx context.Context, cardType string, cardDataInfo *types.CardDataInfo, index int, allThreadMap map[int64]int,
	officialForumIDMap map[uint32]struct{}) *client.AreaCardData {
	switch cardType {
	case "relate_thread", "common_thread", "vote_thread", "score_thread", "pk_thread":
		if cardDataInfo == nil || len(cardDataInfo.ThreadList) == 0 || len(cardDataInfo.ThreadList) <= index {
			tbcontext.WarningF(ctx, "cardType_%s filter len_%d index_%d", cardType, len(cardDataInfo.ThreadList), index)
			return nil
		}

		// 收敛的地方做处理
		// 重复的帖子，则过滤
		// tbcontext.WarningF(ctx,"card_type_%s  allThreadMap_%v", cardType, allThreadMap)
		if _, ok := allThreadMap[cardDataInfo.ThreadList[index].GetId()]; ok {
			tbcontext.WarningF(ctx, "card_type_%s tid_%d hit repeated! filter", cardType, cardDataInfo.ThreadList[index].GetId())
			return nil
		} else { // 设置已有标识
			// tbcontext.WarningF(ctx, "card_type_%s tid_%d is ok ", cardType, cardDataInfo.ThreadList[index].GetId())
			allThreadMap[cardDataInfo.ThreadList[index].GetId()] = 1
		}

		if cardDataInfo.ThreadList[index] != nil && cardDataInfo.ThreadList[index].ForumInfo != nil {
			if _, ok := officialForumIDMap[cast.ToUint32(cardDataInfo.ThreadList[index].GetFid())]; ok {
				cardDataInfo.ThreadList[index].ForumInfo.IsOfficialForum = proto.Int32(1)
			} else {
				cardDataInfo.ThreadList[index].ForumInfo.IsOfficialForum = proto.Int32(0)
			}
		}

		return &client.AreaCardData{
			CardType: &cardType,
			Data: &client.CardData{
				ThreadInfo: cardDataInfo.ThreadList[index],
			},
		}
	case "ai_game":
		if len(cardDataInfo.AiGameList) == 0 || len(cardDataInfo.AiGameList) <= index {
			tbcontext.WarningF(ctx, "cardType_%s filter len_%d index_%d", cardType, len(cardDataInfo.AiGameList), index)
			return nil
		}
		return &client.AreaCardData{
			CardType: &cardType,
			Data: &client.CardData{
				AiInteractiveGamePlot: cardDataInfo.AiGameList[index],
			},
		}
	case "relate_forum":
		if len(cardDataInfo.ForumInfoList) == 0 {
			tbcontext.WarningF(ctx, "cardType_%s filter len_%d index_%d", cardType, len(cardDataInfo.ForumInfoList), index)
			return nil
		}
		for _, forumInfo := range cardDataInfo.ForumInfoList {
			if forumInfo == nil {
				continue
			}
			if _, ok := officialForumIDMap[cast.ToUint32(forumInfo.GetId())]; ok {
				forumInfo.IsOfficialForum = proto.Int32(1)
			} else {
				forumInfo.IsOfficialForum = proto.Int32(0)
			}
		}
		return &client.AreaCardData{
			CardType: &cardType,
			Data: &client.CardData{
				ForumList: cardDataInfo.ForumInfoList,
			},
		}
	case "relate_topic":
		if len(cardDataInfo.TopicInfoList) == 0 {
			tbcontext.WarningF(ctx, "cardType_%s filter len_%d index_%d", cardType, len(cardDataInfo.TopicInfoList), index)
			return nil
		}
		return &client.AreaCardData{
			CardType: &cardType,
			Data: &client.CardData{
				TopicList: cardDataInfo.TopicInfoList,
			},
		}
	case "ai_bot":
		if cardDataInfo.AiBotInfo == nil {
			tbcontext.WarningF(ctx, "cardType_%s filter len_%d index_%d", cardType, len(cardDataInfo.TopicInfoList), index)
			return nil
		}
		return &client.AreaCardData{
			CardType: &cardType,
			Data: &client.CardData{
				AiBot: cardDataInfo.AiBotInfo,
			},
		}
	case "ai_game_new":
		if cardDataInfo.AiGameNew == nil {
			tbcontext.WarningF(ctx, "cardType_%s filter len_%d index_%d", cardType, len(cardDataInfo.TopicInfoList), index)
			return nil
		}
		return &client.AreaCardData{
			CardType: proto.String("ai_bot"),
			Data: &client.CardData{
				AiBot: cardDataInfo.AiGameNew,
			},
		}
	}
	return nil
}

func GetOfficialForumIds(ctx context.Context) map[uint32]struct{} {
	req := map[string]interface{}{
		"pn":          1,
		"rn":          1000,
		"is_form_mis": 1,
	}
	res := &protoCommon.GetOfficialForumIDListRes{}
	err := tbservice.Call(ctx, "common", "getOfficialForumIDList", req, res)
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS || res.GetData() == nil ||
		res.GetData().GetForumIds() == nil || len(res.GetData().GetForumIds()) <= 0 {
		tbcontext.WarningF(ctx, "call common::getOfficialForumIDList fail: %v", err)
		return nil
	}

	officialForumIds := make(map[uint32]struct{})
	for _, forumID := range res.GetData().GetForumIds() {
		officialForumIds[forumID] = struct{}{}
	}
	return officialForumIds
}
