package ext

import (
	"context"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type UserInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("user_info", func() engine.Job {
		return &UserInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewUserInfo(ctx *engine.Context) *UserInfo {
	return &UserInfo{
		ctx: ctx,
	}
}

func (a *UserInfo) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	//判断是否登陆
	staticField := baseData.StaticField
	if staticField == nil {
		return false
	}

	if staticField.UserID <= 0 {
		return false
	}

	return true
}

func (a *UserInfo) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	// 获取用户信息
	if baseData == nil || baseData.StaticField == nil {
		return nil
	}
	staticField := baseData.StaticField
	input := map[string]interface{}{
		"user_id": staticField.UserID,
	}
	getUserDataRes := new(user.GetUserDataRes)
	err := tbservice.Call(ctx, "user", "getUserData", input, getUserDataRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getUserDataRes.Errno == nil || getUserDataRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "Call user:getUserData failed. input:%v, output:%v, err:%v", input, common.ToString(getUserDataRes), err)
		return nil
	}

	isLogin := int32(1)
	if staticField.UserID <= 0 {
		isLogin = 0
	}

	if len(getUserDataRes.GetUserInfo()) > 0 {
		info := getUserDataRes.GetUserInfo()[0]

		staticField.User = &client.User{
			Id:       proto.Int64(info.GetUserId()),
			NameShow: proto.String(getNameShow(info)),
			IsLogin:  proto.Int32(isLogin),
			Portrait: proto.String(tbportrait.Encode(info.GetUserId(), info.GetUserName(), 0)),
		}
	}

	return nil
}

type UserInfoOperator struct {
}

func (rdop *UserInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewUserInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "UserInfo execute fail: %v", err)
		return err
	}

	return nil
}

func getNameShow(info *user.UserInfo) string {
	nameShow := ""

	if info == nil {
		return nameShow
	}

	nickNameV2 := info.GetUserNicknameV2()
	if nickNameV2 != "" {
		return nickNameV2
	}

	nickName := info.GetUserNickname()
	if nickName != "" {
		return nickName
	}

	userName := info.GetUserName()
	if userName != "" {
		return userName
	}

	return nameShow
}
