package ext

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	//"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type AiGame struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("ai_game", func() engine.Job {
		return &AiGameOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewAiGame(ctx *engine.Context) *AiGame {
	return &AiGame{
		ctx: ctx,
	}
}

func (a *AiGame) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if len(baseData.StaticField.ModuleInfo.AiGame.GameRoomIds) <= 0 || baseData.StaticField.UserID <= 0 {
		tbcontext.WarningF(ctx, "has no aigame_room!")
		return false
	}
	return true
}

func (a *AiGame) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	staticField := baseData.StaticField
	if staticField == nil{
		return nil
	}

	AiGame,_ := getAiInteractiveGamePlotByIDs(ctx, staticField.ModuleInfo.AiGame.GameRoomIds, staticField.UserID)

	staticField.ModuleInfo.AiGame.AiGameInfoList = AiGame
	return nil
}

type AiGameOperator struct {
}

func (rdop *AiGameOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewAiGame(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "ai_game execute fail: %v", err)
		return err
	}

	return nil
}

func getAiInteractiveGamePlotByIDs(ctx context.Context, roomIdList []int64, userID int64)([]*client.AiInteractiveGamePlot, error) {
	if len(roomIdList) == 0 {
		tbcontext.WarningF(ctx, "roomIdList nums invalid")
		return nil, errors.New("roomIdList nums is zero")
	}
	req := &chat.GetAiInteractiveGamePlotByIDsReq{
		Uid:&userID,
		//Uid:proto.Int64(int64(571158760)),
		PlotIds:roomIdList,
	}

	res := &chat.GetAiInteractiveGamePlotByIDsRes{}

	err := tbservice.Call(ctx, "chat", "getAiInteractiveGamePlotByIDs", req, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || *res.Errno != tiebaerror.ERR_SUCCESS || res.Data == nil || len(res.GetData().GetAiInteractiveGamePlot()) == 0 {
		tbcontext.WarningF(ctx, "fail to call service chat:getAiInteractiveGamePlotByIDs, input = %s, " +
			"output = %s, err = %v", common.ToString(req), common.ToString(res), err)
		return nil, errors.New("call feed service error")
	}
	var aiInteractiveGamePlot = []*client.AiInteractiveGamePlot{}
	for _,aiGameInfo := range res.GetData().GetAiInteractiveGamePlot(){
		rate := float64(0)
		if aiGameInfo.GetChallengeCnt() > 0 {
			rate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f",
				float64(aiGameInfo.GetPassCnt()) / float64(aiGameInfo.GetChallengeCnt())), 64)
		}
		aiInteractiveGamePlot = append(aiInteractiveGamePlot, &client.AiInteractiveGamePlot{
			PlotId:           aiGameInfo.Id,
			BotUk:            aiGameInfo.BotUk,
			BotPa:            aiGameInfo.BotPa,
			PortraitUrl:      aiGameInfo.BotPicUrl,
			PlotTitle:        aiGameInfo.PlotTitle,
			PlotDescription:  aiGameInfo.PlotDescription,
			BotBackgroundUrl: aiGameInfo.ChatBackgroundUrl,
			Rate:			  &rate,
		} )
	}
	return aiInteractiveGamePlot, nil
}