package ext

import (
	"context"
	"errors"
	"github.com/golang/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/anti"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type CommonThreadUserFilterOperator struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("common_threaduser_filter", func() engine.Job {
		return &CommonThreadUserFilterOperatorOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewCommonThreadUserFilterOperator(ctx *engine.Context) *CommonThreadUserFilterOperator {
	return &CommonThreadUserFilterOperator{
		ctx: ctx,
	}
}

func (a *CommonThreadUserFilterOperator) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData == nil || baseData.StaticField == nil {
		return false
	}

	if len(baseData.StaticField.RelateThreadList) == 0 &&
		len(baseData.StaticField.ModuleInfo.CommonThread.ThreadList) == 0 &&
		len(baseData.StaticField.ModuleInfo.VoteThread.ThreadList) == 0 &&
		len(baseData.StaticField.ModuleInfo.ScoreThread.ThreadList) == 0 &&
		len(baseData.StaticField.ModuleInfo.PKThread.ThreadList) == 0 {
		return false
	}
	return true
}

func (a *CommonThreadUserFilterOperator) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	if baseData == nil || baseData.StaticField == nil {
		return nil
	}

	relateThreadLists := baseData.StaticField.RelateThreadList
	commonThreadLists := baseData.StaticField.ModuleInfo.CommonThread.ThreadList
	VoteThreadLists := baseData.StaticField.ModuleInfo.VoteThread.ThreadList
	ScoreThreadLists := baseData.StaticField.ModuleInfo.ScoreThread.ThreadList
	PKThreadLists := baseData.StaticField.ModuleInfo.PKThread.ThreadList

	threadLists := append(relateThreadLists, commonThreadLists...)
	threadLists = append(threadLists, VoteThreadLists...)
	threadLists = append(threadLists, ScoreThreadLists...)
	threadLists = append(threadLists, PKThreadLists...)

	// 获取帖子信息
	var uIDList []int64
	var uidTidMap = make(map[int64]int64)
	for _, threadInfo := range threadLists {
		uIDList = append(uIDList, threadInfo.GetAuthor().GetId())
		uidTidMap[threadInfo.GetAuthor().GetId()] = threadInfo.GetId()
	}

	filterUIDs, err := FilterThreadUserMask(ctx, uIDList)
	if nil != err {
		tbcontext.WarningF(ctx, "FilterThreadUserMask fail! err[%v]", err)
		return nil
	}

	var filterThreadIDs []uint64
	for _,uid := range filterUIDs{
		filterThreadIDs = append(filterThreadIDs, uint64(uidTidMap[uid]))
	}

	dealFilter(ctx, filterThreadIDs, baseData)

	return nil
}

type CommonThreadUserFilterOperatorOperator struct {
}

func (rdop *CommonThreadUserFilterOperatorOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewCommonThreadUserFilterOperator(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "TopicInfo execute fail: %v", err)
		return err
	}
	return nil
}

//获取帖子列表 feedDomain
func FilterThreadUserMask(ctx context.Context, uIDList []int64) ([]int64, error) {
	if len(uIDList) == 0 {
		tbcontext.WarningF(ctx, "uid nums invalid")
		return nil, errors.New("uid nums is zero")
	}

	req := &anti.AntiTbmaskQueryReq{
		Req: &anti.ReqDetail{
			IdList:    uIDList,
			MaskList:  []string{"anti_browse"},
			StrMethod: proto.String("tbmask_query"),
		},
	}
	res := &anti.AntiTbmaskQueryRes{}
	err := tbservice.Call(ctx, "anti", "antiTbmaskQuery", req, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || *res.Errno != tiebaerror.ERR_SUCCESS || res.Res == nil  {
		tbcontext.WarningF(ctx, "fail to call service anti:antiTbmaskQuery or Res is nil, " +
			"input = %s, output = %s, err = %v", common.ToString(req), common.ToString(res), err)
		return nil, errors.New("call feed service error")
	}

	var filterUIDs []int64
	for _, antiInfo := range res.Res.ResList {
		if antiInfo.GetAntiBrowse() == 1 {
			filterUIDs = append(filterUIDs, antiInfo.GetId())
		}
	}
	return filterUIDs, nil
}
