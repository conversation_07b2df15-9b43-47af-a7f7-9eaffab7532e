package ext

import (
	"context"
	"github.com/golang/protobuf/proto"
	"strconv"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/hottopic"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ThreadList struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("thread_list", func() engine.Job {
		return &ThreadListOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewThreadList(ctx *engine.Context) *ThreadList {
	return &ThreadList{
		ctx: ctx,
	}
}

func (a *ThreadList) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData == nil || baseData.StaticField == nil {
		return false
	}

	if baseData.StaticField.TopicInfo == nil {
		return false
	}
	return true
}

func (a *ThreadList) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	staticField := baseData.StaticField

	var baiduIDStr string
	if baiduID,ok := baseData.BaseObj.Req.Cookie("BAIDUID");ok {
		baiduIDStr = baiduID.Value
	}

	userInfo := hottopic.UserInfo{
		UserID:  proto.Uint64(uint64(staticField.UserID)),
		BaiduID: &baiduIDStr,
	}

	res, err := hottopic.GetHottopicThreadList(ctx, staticField.TopicInfo, baseData.Request, &userInfo, hottopic.THREAD_LIST_HOT, 0)
	if nil != err || res == nil {
		tbcontext.WarningF(ctx, "hottopic.GetHotpicThreadList error[%v] or res is nil", err)
		return nil
	}

	if staticField.RelateThreadInfo.PostIds == nil {
		staticField.RelateThreadInfo.PostIds = make(map[int64][]int64)
	}

	for _, threadID := range res.ThreadIdList {
		staticField.RelateThreadIds = append(staticField.RelateThreadIds, int64(threadID))
	}

	for _, threadInfo := range res.RelateThreadList {
		showPostId, _ := strconv.ParseUint(threadInfo.GetShowPostId(), 10, 64)
		showPostInfo := types.ShowPostIDInfo{
			ThreadID:   threadInfo.GetThreadId(),
			PostIDList: []uint64{showPostId},
		}
		staticField.RelateThreadInfo.ShowPostID = append(staticField.RelateThreadInfo.ShowPostID, &showPostInfo)

		staticField.RelateThreadInfo.PostIds[int64(threadInfo.GetThreadId())] =
			append(staticField.RelateThreadInfo.PostIds[int64(threadInfo.GetThreadId())], int64(showPostId))
	}

	staticField.RelateThreadInfo.ThreadIds = staticField.RelateThreadIds

	staticField.HasMore = res.HasMore
	return nil
}

type ThreadListOperator struct {
}

func (rdop *ThreadListOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewThreadList(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "ThreadList execute fail: %v", err)
		return err
	}

	return nil
}