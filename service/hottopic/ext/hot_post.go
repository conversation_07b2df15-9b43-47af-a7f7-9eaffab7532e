package ext

import (
	"context"
	"errors"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/processpost"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbrichtext"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/common/meta"
	"sync"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/hottopic"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type HotPost struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("hot_post", func() engine.Job {
		return &HotPostOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewHotPost(ctx *engine.Context) *HotPost {
	return &HotPost{
		ctx: ctx,
	}
}

func (a *HotPost) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData == nil || baseData.StaticField == nil {
		return false
	}

	if len(baseData.StaticField.RelateThreadIds) == 0 &&
		len(baseData.StaticField.ModuleInfo.CommonThread.ThreadInfo.ThreadIds) == 0 {
		return false
	}
	return true
}

func (a *HotPost) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	if baseData == nil || baseData.StaticField == nil {
		return nil
	}
	staticField := baseData.StaticField
	allThreadIDs, _, allPostIDsMap := getAlltidsAndTids(staticField)

	hostPostListMap, err := getHotPostInfo(ctx, allThreadIDs, allPostIDsMap, baseData)
	if nil != err {
		tbcontext.WarningF(ctx, "getHotPostInfo fail! err[%v]", err)
		return nil
	}

	dealHotPost(staticField, hostPostListMap)

	return nil
}

type HotPostOperator struct {
}

func (rdop *HotPostOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewHotPost(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "TopicInfo execute fail: %v", err)
		return err
	}

	return nil
}

func dealHotPost(staticField *types.TopicDetailStaticField, postListMap map[uint64][]*client.Post) {
	//relate_thread
	staticField.RelateThreadInfo.HotPostInfo = make(map[int64][]*client.Post)
	for _, relateThreadID := range staticField.RelateThreadIds {
		if postList, ok := postListMap[uint64(relateThreadID)]; ok {
			staticField.RelateThreadInfo.HotPostInfo[relateThreadID] = postList
		}
	}

	//common_thread
	staticField.ModuleInfo.CommonThread.ThreadInfo.HotPostInfo = make(map[int64][]*client.Post)
	for _, relateThreadID := range staticField.ModuleInfo.CommonThread.ThreadInfo.ThreadIds {
		if postList, ok := postListMap[uint64(relateThreadID)]; ok {
			staticField.ModuleInfo.CommonThread.ThreadInfo.HotPostInfo[relateThreadID] = postList
		}
	}

	/**
	//vote_thread
	staticField.ModuleInfo.VoteThread.ThreadInfo.HotPostInfo = make(map[int64][]*client.Post)
	for _, relateThreadID := range staticField.ModuleInfo.VoteThread.ThreadInfo.ThreadIds {
		if postList, ok := postListMap[uint64(relateThreadID)]; ok {
			staticField.ModuleInfo.VoteThread.ThreadInfo.HotPostInfo[relateThreadID] = postList
		}
	}

	//score_thread
	staticField.ModuleInfo.ScoreThread.ThreadInfo.HotPostInfo = make(map[int64][]*client.Post)
	for _, relateThreadID := range staticField.ModuleInfo.ScoreThread.ThreadInfo.ThreadIds {
		if postList, ok := postListMap[uint64(relateThreadID)]; ok {
			staticField.ModuleInfo.ScoreThread.ThreadInfo.HotPostInfo[relateThreadID] = postList
		}
	}

	//pk_thread
	staticField.ModuleInfo.PKThread.ThreadInfo.HotPostInfo = make(map[int64][]*client.Post)
	for _, relateThreadID := range staticField.ModuleInfo.PKThread.ThreadInfo.ThreadIds {
		if postList, ok := postListMap[uint64(relateThreadID)]; ok {
			staticField.ModuleInfo.PKThread.ThreadInfo.HotPostInfo[relateThreadID] = postList
		}
	}
	 */
}

// getAllTIDs 合并所有帖子id
func getAlltidsAndTids(staticField *types.TopicDetailStaticField) ([]int64, []map[string]int, map[uint64][]uint64) {
	var allThreadIDs []int64
	var tidSortList []map[string]int
	var allPostIDsMap = make(map[uint64][]uint64)
	if staticField == nil {
		return allThreadIDs, tidSortList, allPostIDsMap
	}

	//relateThread
	for _, tid := range staticField.RelateThreadIds {
		allThreadIDs = append(allThreadIDs, tid)
		tidSortList = append(tidSortList, map[string]int{
			"relate_thread": len(staticField.RelateThreadIds),
		})

		var ShowPostIDList []uint64
		for _, showPost := range staticField.RelateThreadInfo.ShowPostID {
			if uint64(tid) == showPost.ThreadID {
				ShowPostIDList = showPost.PostIDList
			}
		}
		allPostIDsMap[uint64(tid)] = ShowPostIDList
	}

	//common_thread
	threadIDs, tidSortListMap := getModuleTidPidInfo("common_thread", staticField.ModuleInfo.CommonThread.ThreadInfo, allPostIDsMap)
	allThreadIDs = append(allThreadIDs, threadIDs...)
	tidSortList = append(tidSortList, tidSortListMap)

	/**
	//vote_thread
	threadIDs, tidSortListMap = getModuleTidPidInfo("vote_thread", staticField.ModuleInfo.VoteThread.ThreadInfo, allPostIDsMap)
	allThreadIDs = append(allThreadIDs, threadIDs...)
	tidSortList = append(tidSortList, tidSortListMap)

	//score_thread
	threadIDs, tidSortListMap = getModuleTidPidInfo("score_thread", staticField.ModuleInfo.ScoreThread.ThreadInfo, allPostIDsMap)
	allThreadIDs = append(allThreadIDs, threadIDs...)
	tidSortList = append(tidSortList, tidSortListMap)

	//pk_thread
	threadIDs, tidSortListMap = getModuleTidPidInfo("pk_thread", staticField.ModuleInfo.PKThread.ThreadInfo, allPostIDsMap)
	allThreadIDs = append(allThreadIDs, threadIDs...)
	tidSortList = append(tidSortList, tidSortListMap)
	 */
	return allThreadIDs, tidSortList, allPostIDsMap
}

func getModuleTidPidInfo(cardType string, moduleThreadInfo types.ThreadInfo, allPostIDsMap map[uint64][]uint64) ([]int64, map[string]int) {
	var threadIDs []int64
	for _, tid := range moduleThreadInfo.ThreadIds {
		threadIDs = append(threadIDs, tid)

		if postList, ok := moduleThreadInfo.PostIds[tid]; ok {
			var hotPostList []uint64
			for _, postID := range postList {
				hotPostList = append(hotPostList, uint64(postID))
			}
			allPostIDsMap[uint64(tid)] = hotPostList
		}
	}

	tidSortListMap := map[string]int{
		cardType: len(moduleThreadInfo.ThreadIds),
	}

	return threadIDs, tidSortListMap
}

func getHotPostInfo(ctx context.Context, tidList []int64, pidListMap map[uint64][]uint64,
	baseData *types.CTopicDetailBaseData) (map[uint64][]*client.Post, error) {

	if len(tidList) == 0 {
		tbcontext.WarningF(ctx, "tidList nums invalid")
		return nil, errors.New("tidList nums is zero")
	}

	wg := sync.WaitGroup{}
	var mux = sync.Mutex{}
	var hotPostListMap = make(map[uint64][]*client.Post)

	for _, tid := range tidList {
		wg.Add(1)
		postList, _ := pidListMap[uint64(tid)]
		go func(ctx context.Context, tid uint64, pidList []uint64, baseData *types.CTopicDetailBaseData) {
			defer wg.Done()
			postList, err := getTopKPostInfo(ctx, tid, pidList, baseData)
			if nil != err || len(postList) == 0 {
				tbcontext.WarningF(ctx, "getTopKPostInfo fail or has no hot_post! err[%v] postList[%v]", err, postList)
				return
			}

			mux.Lock()
			hotPostListMap[tid] = postList
			mux.Unlock()
		}(ctx, uint64(tid), postList, baseData)
	}
	wg.Wait()
	return hotPostListMap, nil
}

func getTopKPostInfo(ctx context.Context, tid uint64, pidList []uint64, baseData *types.CTopicDetailBaseData) ([]*client.Post, error) {
	req := &hottopic.GetTopKPostInfoReq{
		Req: &hottopic.GetTopKPostInfoParam{
			Tid:     &tid,
			UserId:  &baseData.StaticField.UserID,
			PidList: pidList,
			TopK:    proto.Int32(int32(1)),
		},
	}

	res := &hottopic.GetTopKPostInfoResp{}
	err := tbservice.Call(ctx, "hottopic", "getTopKPostInfo", req, res, tbservice.WithConverter(tbservice.JSONITER))
	tbcontext.WarningF(ctx, "getTopKPostInfo_req_%v_res_%v err_%v", *req, common.ToString(res.GetRet()), err)
	if err != nil || res.Errno == nil || *res.Errno != tiebaerror.ERR_SUCCESS || res.Ret == nil {
		tbcontext.WarningF(ctx, "fail to call service hottopic:getTopKPostInfo, input = %s, "+
			"output = %s, err = %v", common.ToString(req), common.ToString(res), err)
		return nil, errors.New("call hottopic getThreadTopic error")
	}

	var hotPostList []*client.Post
	for _, topKPostInfo := range res.Ret.PostInfoList {
		var clientContent []*client.PbContent
		if topKPostInfo.Content != nil {
			var structContentList []*meta.PostStructContent
			if contentList, ok := topKPostInfo.GetContent().([]interface{}); ok {
				for _, content := range contentList {
					var structContent meta.PostStructContent
					err = common.StructAToStructBCtx(ctx, content, &structContent)
					if nil != err {
						tbcontext.WarningF(ctx, "structContent[%v] is not meta.PostStructContent ! err[%v]", content, err)
					}else{
						structContentList = append(structContentList, &structContent)
					}
					clientContent = processRichAbstract(ctx, structContentList, baseData)
				}
			} else {
				tbcontext.WarningF(ctx, "topKPostInfo content is not []interface{}, content[%v]", topKPostInfo.GetContent())
			}
		}

		hotPost := &client.Post{
			Id:            topKPostInfo.PostId,
			Tid:           proto.Int64(int64(topKPostInfo.GetThreadId())),
			Title:         topKPostInfo.Title,
			Time:          proto.Uint32(uint32(topKPostInfo.GetNowTime())),
			Content:       clientContent,
			SubPostNumber: topKPostInfo.CommentNum,
			AuthorId:      topKPostInfo.UserId,
			AddPostNumber: topKPostInfo.CommentNum,
			Author: &client.User{
				Id:       topKPostInfo.UserId,
				Name:     topKPostInfo.Username,
				NameShow: topKPostInfo.Username,//todo 获取昵称，
				Portrait: topKPostInfo.Portrait,
			},
			Agree: &client.Agree{
				AgreeNum:     topKPostInfo.AgreeNum,
				HasAgree:     proto.Int32(int32(topKPostInfo.GetAgree().GetHasAgree())), //todo 确认是否返回，不能调整下游方法
				AgreeType:    proto.Int32(int32(topKPostInfo.GetAgree().GetAgreeType())), //todo 确认是否返回
				DisagreeNum:  topKPostInfo.DisagreeNum,
				DiffAgreeNum: proto.Int64(topKPostInfo.GetAgreeNum() - topKPostInfo.GetDisagreeNum()),
				LzAgree:      nil,
			},
		}

		hotPostList = append(hotPostList, hotPost)
	}

	return hotPostList, nil
}

func processRichAbstract(ctx context.Context, structContent []*meta.PostStructContent, baseData *types.CTopicDetailBaseData) []*client.PbContent {
	if len(structContent) == 0 {
		return nil
	}

	pc := &tbrichtext.GParserCondition
	pc.NewLineCount = 1
	pc.BolParseBdhd = true
	pc.BolParsePhone = true
	pc.BolCheckSpamUrl = true
	pc.BolGraffitiToImg = true

	clientType := baseData.Request.GetCommon().GetXClientType()
	clientVersion := baseData.Request.GetCommon().GetXClientVersion()
	// pb结构化后，解析content方式
	// 对content进行内容处理,包括参数的过滤和调整
	objParserStruct := new(tbrichtext.ParserStructured)
	objParserStruct.SetClientType(int(clientType))
	objParserStruct.SetClientVersion(clientVersion)

	parserOut, err := objParserStruct.Process(ctx, &tbrichtext.ParserStructProcessInput{
		PObjCondition: pc,
		BolEmoji:      true,
		// ScreenWidth:    int(param.screenWidth),
		// ScreenHeight:   int(param.screenHeigh),
		ArrText:        structContent,
		BolIsAllOrigin: true,
		BolNeedTopic:   true,
	})

	if err != nil {
		tbcontext.WarningF(ctx, "parserStructured fail: %v", err)
		return nil
	}

	param := &processpost.ReqParam{
		ClientType:    common.TransforValueToTargetType(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int),
		ClientVersion: common.TransforValueToTargetType(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string),
		Uid:           common.TransforValueToTargetType(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT).(int),
	}
	processPost := processpost.InitParam(param, baseData.BaseObj.ObjRequest.GetStrategyMap())

	tbcontext.WarningF(ctx, "processPost_%v parserOut_%v", common.ToString(processPost), common.ToString(parserOut))

	richContent, err := processpost.ProcessContent(ctx, parserOut.ArrSlotContent, processPost)
	if err != nil {
		tbcontext.WarningF(ctx, "ArrSlotContent ProcessContent fail: %v", err)
	}
	return richContent
}
