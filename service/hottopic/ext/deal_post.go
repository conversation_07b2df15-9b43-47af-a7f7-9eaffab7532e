package ext

import (
	"context"
	"errors"
	"google.golang.org/protobuf/proto"
	user2 "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/agree"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/pb"
	"sync"
	"time"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type DealPost struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("deal_post", func() engine.Job {
		return &DealPostOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewDealPost(ctx *engine.Context) *DealPost {
	return &DealPost{
		ctx: ctx,
	}
}

func (a *DealPost) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData == nil || baseData.StaticField == nil {
		return false
	}

	//postlist and userList
	allPostIDMap, allUserIDs, allUserPostIDMap := getAllPostAndAuthors(ctx, baseData.StaticField)
	if len(allPostIDMap) == 0 || len(allUserPostIDMap) == 0 || len(allUserIDs) == 0 {
		tbcontext.WarningF(ctx, "has no real_post and hot_post! allPostIDMap[%v] allUserIDs[%v] allUserPostIDMap[%v]", allPostIDMap, allUserIDs, allUserPostIDMap)
		return false
	}
	return true
}

func (a *DealPost) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	if baseData == nil || baseData.StaticField == nil {
		return nil
	}
	
	staticField := baseData.StaticField
	allPostIDMap, allUserIDs, allUserPostIDMap := getAllPostAndAuthors(ctx, baseData.StaticField)
	//tbcontext.WarningF(ctx, "allPostIDMap_%v allUserIDs_%v allUserPostIDMap_%v", allPostIDMap, allUserIDs, allUserPostIDMap)
	allPostInfoMap, filterTIDs, filterPIDs := dealPostInfo(ctx, staticField, allPostIDMap, allUserIDs, allUserPostIDMap)
	//tbcontext.WarningF(ctx, "allPostInfoMap_%v  allPostIDMap_%v allUserIDs_%v allUserPostIDMap_%v", allPostInfoMap, allPostIDMap, allUserIDs, allUserPostIDMap)
	buildAllThreadPost(ctx, staticField, allPostInfoMap)
	//tbcontext.WarningF(ctx,"filterTIDs_%v filterPIDs_%v", filterTIDs, filterPIDs)
		//filter tids
	dealFilter(ctx, filterTIDs, baseData)
	//filter pids
	dealFilterPids(ctx, filterPIDs, baseData)
	return nil
}

type DealPostOperator struct {
}

func (rdop *DealPostOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewDealPost(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "adjust_thread execute fail: %v", err)
		return err
	}

	return nil
}

func dealPostInfo(ctx context.Context, staticField *types.TopicDetailStaticField, allPostIDMap map[int64][]int64,
	allUserIDs []int64, allUserPostIDMap map[int64][]int64) (map[int64]*client.Post, []uint64, []uint64) {

	if allPostIDMap == nil || len(allPostIDMap) == 0 || len(allUserIDs) == 0 {
		return nil, nil, nil
	}

	var allPostInfoMap = make(map[int64]*client.Post)
	var mutex = sync.Mutex{}

	wg := sync.WaitGroup{}
	for tid, postIDs := range allPostIDMap {
		if staticField.UserID > 0 {
			//处理点赞信息
			wg.Add(1)
			go func(ctx context.Context, threadId int64, postIDs[]int64, userID int64) {
				defer wg.Done()

				agreeMap,err := getPostAgree(ctx, tid, postIDs, userID)
				if nil != err {
					tbcontext.WarningF(ctx, "getPostAgree err[%v]! tid[%d] postIDs[%v], userID[%d]", err, tid, postIDs, userID)
					return
				}

				//加锁
				mutex.Lock()
				for pid, agreeInfo := range agreeMap{
					if allPostInfoMap[pid] == nil{
						allPostInfoMap[pid] = &client.Post{
							Agree:agreeInfo,
						}
					}else{
						allPostInfoMap[pid].Agree = agreeInfo
					}
				}
				mutex.Unlock()
			}(ctx, tid, postIDs, staticField.UserID)
		}

		//处理用户信息
		wg.Add(1)
		go func(ctx context.Context, allUserIDs []int64) {
			defer wg.Done()
			userInfoMap,err := mgetUserData(ctx, allUserIDs)
			if nil != err {
				tbcontext.WarningF(ctx, "mgetUserData err[%v]!  allUserIDs[%v]", err, allUserIDs)
				return
			}

			//加锁
			mutex.Lock()
			for uid, userInfo := range userInfoMap{
				for _,pid := range allUserPostIDMap[uid]{
					if allPostInfoMap[pid] == nil{
						allPostInfoMap[pid] = &client.Post{
							Author:userInfo,
						}
					}else{
						allPostInfoMap[pid].Author = userInfo
					}
				}
			}
			mutex.Unlock()
		}(ctx, allUserIDs)
	}

	//处理帖子(postmaskinfo 和threadmaskinfo 有一定gap，再次check)和回复的删除信息
	var pids []uint64
	var tids []uint64
	for tid, postIDs := range allPostIDMap {
		for _,pid := range postIDs{
			pids = append(pids, uint64(pid))
			tids = append(tids, uint64(tid))
		}
	}

	var filterTIDs []uint64
	var filterPIDs []uint64

	wg.Add(1)
	go func(ctx context.Context, tids []uint64, pids []uint64) {
		wg.Done()
		var err error
		filterTIDs, filterPIDs, err = getPostMaskInfo(ctx, tids, pids)
		if nil != err{
			tbcontext.WarningF(ctx, "getPostMaskInfo fail! err[%v]", err)
		}
	}(ctx, tids, pids)

	wg.Wait()
	return allPostInfoMap, filterTIDs, filterPIDs
}

func buildAllThreadPost(ctx context.Context, staticField *types.TopicDetailStaticField, allPostInfoMap map[int64]*client.Post){
	//relate_thread
	for _, postList := range staticField.RelateThreadInfo.HotPostInfo{
		buildThreadPost(ctx, postList, allPostInfoMap)
	}

	//common_thread
	for _, postList := range staticField.ModuleInfo.CommonThread.ThreadInfo.HotPostInfo{
		buildThreadPost(ctx, postList, allPostInfoMap)
	}

	for _, postList := range staticField.ModuleInfo.CommonThread.ThreadInfo.RealPostInfo{
		buildThreadPost(ctx, postList, allPostInfoMap)
	}

	//vote_thread
	for _, postList := range staticField.ModuleInfo.VoteThread.ThreadInfo.HotPostInfo{
		buildThreadPost(ctx, postList, allPostInfoMap)
	}

	for _, postList := range staticField.ModuleInfo.VoteThread.ThreadInfo.RealPostInfo{
		buildThreadPost(ctx, postList, allPostInfoMap)
	}

	//score_thread
	for _, postList := range staticField.ModuleInfo.ScoreThread.ThreadInfo.HotPostInfo{
		buildThreadPost(ctx, postList, allPostInfoMap)
	}

	for _, postList := range staticField.ModuleInfo.ScoreThread.ThreadInfo.RealPostInfo{
		buildThreadPost(ctx, postList, allPostInfoMap)
	}

	//pk_thread
	for _, postList := range staticField.ModuleInfo.PKThread.ThreadInfo.HotPostInfo{
		buildThreadPost(ctx, postList, allPostInfoMap)
	}

	for _, postList := range staticField.ModuleInfo.PKThread.ThreadInfo.RealPostInfo{
		buildThreadPost(ctx, postList, allPostInfoMap)
	}
}

func buildThreadPost(ctx context.Context, postList []*client.Post, allPostInfoMap map[int64]*client.Post){
	for _,postInfo := range postList{
		tbcontext.WarningF(ctx, "pid_%d postInfo.Agree_%v",postInfo.GetId(), postInfo.Agree)
		if post,ok := allPostInfoMap[int64(postInfo.GetId())]; ok{
			//agree_info
			if postInfo.Agree != nil && post.Agree != nil {
				postInfo.Agree.HasAgree = post.Agree.HasAgree
				postInfo.Agree.AgreeType = post.Agree.AgreeType
			}

			//author_info
			if postInfo.Author != nil && post.Author != nil {
				postInfo.Author.NameShow = post.Author.NameShow
				postInfo.Author.Portrait = post.Author.Portrait
			}
		}else {
			tbcontext.WarningF(ctx, "the pid_%v has not in allPostInfoMap[%v]", postInfo.GetId(), allPostInfoMap)
		}
	}
}

func getPostAgree(ctx context.Context, threadId int64, postIDs[]int64, userID int64) (map[int64]*client.Agree, error) {
	req := &agree.GetAgreeByUserIdAndPostIdsReq{
		UserId:   &userID,
		PostIds:  postIDs,
		ThreadId: &threadId,
	}

	res := &agree.GetAgreeByUserIdAndPostIdsRes{}
	err := tbservice.Call(ctx, "agree", "getAgreeByUserIdAndPostIds", req, res, tbservice.WithConverter(tbservice.JSONITER))
	//tbcontext.WarningF(ctx, "getAgreeByUserIdAndPostIds_req_%v_res_%v err_%v", *req, common.ToString(res.GetData()), err)
	if err != nil || res.Errno == nil || *res.Errno != tiebaerror.ERR_SUCCESS || res.Data == nil {
		tbcontext.WarningF(ctx, "fail to call service agree:getAgreeByUserIdAndPostIds, input = %s, "+
			"output = %s, err = %v", common.ToString(req), common.ToString(res), err)
		return nil, errors.New("call agree getAgreeByUserIdAndPostIds error")
	}

	var allPostAgree = make(map[int64]*client.Agree)
	for pid, agreeInfo := range res.GetData().Map {
		var hasAgree int32
		if agreeInfo.GetAgreeType() == 2 {
			hasAgree = 1
		}

		allPostAgree[pid] = &client.Agree{
			HasAgree:   &hasAgree,
			AgreeType:    proto.Int32(int32(agreeInfo.GetAgreeType())),
		}
	}
	return allPostAgree, nil
}

func mgetUserData(ctx context.Context, allUserIDs []int64) (map[int64]*client.User, error) {
	req := &user.MgetUserDataExReq{
		UserId:          allUserIDs,
		GetIcon:         proto.Uint32(0),
		NeedGrowthLevel: proto.Uint32(0),
	}

	res := &user.MgetUserDataExRes{}
	err := tbservice.Call(ctx, "user", "mgetUserDataEx", req, res, tbservice.WithConverter(tbservice.JSONITER))
	//tbcontext.WarningF(ctx, "mgetUserData_req_%v_res_%v err_%v", *req, common.ToString(res.GetUserInfo()), err)
	if err != nil || res.Errno == nil || *res.Errno != tiebaerror.ERR_SUCCESS || res.GetUserInfo() == nil {
		tbcontext.WarningF(ctx, "fail to call service user:mgetUserDataEx, input = %s, "+
			"output = %s, err = %v", common.ToString(req), common.ToString(res), err)
		return nil, errors.New("call service mgetUserDataEx error")
	}

	var userInfoMap = make(map[int64]*client.User)
	for _, userInfo := range res.GetUserInfo(){
		portraitTime := time.Now().Unix()
		if userInfo.GetPortraitTime() > 0 {
			portraitTime = int64(userInfo.GetPortraitTime())
		}
		portrait := tbportrait.Encode(int64(userInfo.GetUserId()), userInfo.GetUserName(), portraitTime)
		userInfoMap[int64(userInfo.GetUserId())] = &client.User{
			Id:                  proto.Int64(int64(userInfo.GetUserId())),
			Name:                userInfo.UserName,
			NameShow:            proto.String(user2.GetUserNameShow(userInfo)),
			Portrait:            &portrait,
		}
	}
	return userInfoMap, nil
}

func getPostMaskInfo(ctx context.Context, threadIds []uint64, postIDs[]uint64) ([]uint64, []uint64, error) {
	req := &pb.GetMaskInfoReq{
		Input:&pb.GetMaskInfoInput{
			ThreadIds:    threadIds,
			PostIds:      postIDs,
		},
	}
	res := &pb.GetMaskInfoRes{}
	err := tbservice.Call(ctx, "post", "getMaskInfo", req, res, tbservice.WithConverter(tbservice.JSONITER))
	//tbcontext.WarningF(ctx, "getMaskInfo_req_%v_res_%v err_%v", *req, common.ToString(res.GetOutput()), err)
	if err != nil || res.Errno == nil || *res.Errno != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service post:getMaskInfo, input = %s, "+
			"output = %s, err = %v", common.ToString(req), common.ToString(res), err)
		return nil, nil, errors.New("call post getMaskInfo error")
	}

	if res.Output == nil || (res.GetOutput().GetThreadsMaskStatus() == nil && res.GetOutput().GetPostsMaskStatus() == nil) {
		return nil, nil, nil
	}

	var filterPIDs []uint64
	var filterTIDs []uint64

	for tid,maskInfo := range res.GetOutput().GetThreadsMaskStatus(){
		if maskInfo.GetIsKeyDeleted() > 0 || maskInfo.GetIsKeyVisible() > 0 || maskInfo.GetIsKeyMask() > 0 {
			tbcontext.WarningF(ctx, "the tid[%d] hit mask! mask_info[%v]", tid, common.ToString(maskInfo))
			filterTIDs = append(filterTIDs, tid)
		}
	}

	for pid, maskInfo := range res.GetOutput().GetPostsMaskStatus(){
		if maskInfo.GetIsKeyDeleted() > 0 || maskInfo.GetIsKeyVisible() > 0 || maskInfo.GetIsKeyMask() > 0 {
			tbcontext.WarningF(ctx, "the pid[%d] hit mask! mask_info[%v]", pid, common.ToString(maskInfo))
			filterPIDs = append(filterPIDs, pid)
		}
	}
	return filterTIDs, filterPIDs, nil
}


func getAllPostAndAuthors(ctx context.Context, staticField *types.TopicDetailStaticField) (map[int64][]int64, []int64, map[int64][]int64){
	var allPostIDMap = make(map[int64][]int64)
	var allUserPostIDMap = make(map[int64][]int64)
	var allUserIDs []int64
	//relate_thread
	for _, postList := range staticField.RelateThreadInfo.HotPostInfo{

		//tbcontext.WarningF(ctx, "r_h_postList_%v", postList)
		userIDs := getPostAndAuthor(ctx, postList, allPostIDMap, allUserPostIDMap)
		//tbcontext.WarningF(ctx, "r_h_userIDs_%v", userIDs)
		allUserIDs = append(allUserIDs, userIDs...)
	}
	//tbcontext.WarningF(ctx, "r_h_allUserIDs_%v allPostIDMap_%v allUserPostIDMap_%v", allUserIDs, allPostIDMap, allUserPostIDMap)
	//common_thread real_post
	for _, postList := range staticField.ModuleInfo.CommonThread.ThreadInfo.RealPostInfo{
		//tbcontext.WarningF(ctx, "c_r_postList_%v", postList)
		userIDs := getPostAndAuthor(ctx, postList, allPostIDMap, allUserPostIDMap)
		//tbcontext.WarningF(ctx, "c_r_userIDs_%v", userIDs)
		allUserIDs = append(allUserIDs, userIDs...)
	}
	//tbcontext.WarningF(ctx, "c_r_allUserIDs_%v allPostIDMap_%v allUserPostIDMap_%v", allUserIDs, allPostIDMap, allUserPostIDMap)
	//common_thread hot_post
	for _, postList := range staticField.ModuleInfo.CommonThread.ThreadInfo.HotPostInfo{
		//tbcontext.WarningF(ctx, "r_h_postList_%v", postList)
		userIDs := getPostAndAuthor(ctx, postList, allPostIDMap, allUserPostIDMap)
		//tbcontext.WarningF(ctx, "c_h_userIDs_%v", userIDs)
		allUserIDs = append(allUserIDs, userIDs...)
	}
	//tbcontext.WarningF(ctx, "c_h_allUserIDs_%v allPostIDMap_%v allUserPostIDMap_%v", allUserIDs, allPostIDMap, allUserPostIDMap)
	//vote_thread real_post
	for _, postList := range staticField.ModuleInfo.VoteThread.ThreadInfo.RealPostInfo{
		tbcontext.WarningF(ctx, "v_r_postList_%v", postList)
		userIDs := getPostAndAuthor(ctx, postList, allPostIDMap, allUserPostIDMap)
		tbcontext.WarningF(ctx, "v_r_userIDs_%v", userIDs)
		allUserIDs = append(allUserIDs, userIDs...)
	}
	//tbcontext.WarningF(ctx, "v_r_allUserIDs_%v allPostIDMap_%v allUserPostIDMap_%v", allUserIDs, allPostIDMap, allUserPostIDMap)
	//vote_thread hot_post
	for _, postList := range staticField.ModuleInfo.VoteThread.ThreadInfo.HotPostInfo{
		//tbcontext.WarningF(ctx, "v_h_postList_%v", postList)
		userIDs := getPostAndAuthor(ctx, postList, allPostIDMap, allUserPostIDMap)
		//tbcontext.WarningF(ctx, "v_h_userIDs_%v", userIDs)
		allUserIDs = append(allUserIDs, userIDs...)
	}

	//tbcontext.WarningF(ctx, "v_h_allUserIDs_%v allPostIDMap_%v allUserPostIDMap_%v", allUserIDs, allPostIDMap, allUserPostIDMap)
	//score_thread real_post
	for _, postList := range staticField.ModuleInfo.ScoreThread.ThreadInfo.RealPostInfo{
		//tbcontext.WarningF(ctx, "s_r_postList_%v", postList)
		userIDs := getPostAndAuthor(ctx, postList, allPostIDMap, allUserPostIDMap)
		//tbcontext.WarningF(ctx, "s_r_userIDs_%v", userIDs)
		allUserIDs = append(allUserIDs, userIDs...)
	}
	//tbcontext.WarningF(ctx, "s_r_allUserIDs_%v allPostIDMap_%v allUserPostIDMap_%v", allUserIDs, allPostIDMap, allUserPostIDMap)
	//score_thread hot_post
	for _, postList := range staticField.ModuleInfo.ScoreThread.ThreadInfo.HotPostInfo{
		//tbcontext.WarningF(ctx, "s_h_postList_%v", postList)
		userIDs := getPostAndAuthor(ctx, postList, allPostIDMap, allUserPostIDMap)
		//tbcontext.WarningF(ctx, "s_h_userIDs_%v", userIDs)
		allUserIDs = append(allUserIDs, userIDs...)
	}
	//tbcontext.WarningF(ctx, "s_h_allUserIDs_%v allPostIDMap_%v allUserPostIDMap_%v", allUserIDs, allPostIDMap, allUserPostIDMap)
	//pk_thread real_post
	for _, postList := range staticField.ModuleInfo.PKThread.ThreadInfo.RealPostInfo{
		//tbcontext.WarningF(ctx, "pk_r_postList_%v", postList)
		userIDs := getPostAndAuthor(ctx, postList, allPostIDMap, allUserPostIDMap)
		//tbcontext.WarningF(ctx, "pk_r_userIDs_%v allPostIDMap_%v allUserPostIDMap_%v", allUserIDs, allPostIDMap, allUserPostIDMap)
		allUserIDs = append(allUserIDs, userIDs...)
	}
	//tbcontext.WarningF(ctx, "pk_r_allUserIDs_%v", allUserIDs)
	//pk_thread hot_post
	for _, postList := range staticField.ModuleInfo.PKThread.ThreadInfo.HotPostInfo{
		//tbcontext.WarningF(ctx, "pk_h_postList_%v", postList)
		userIDs := getPostAndAuthor(ctx, postList, allPostIDMap, allUserPostIDMap)
		//tbcontext.WarningF(ctx, "pk_h_userIDs_%v", userIDs)
		allUserIDs = append(allUserIDs, userIDs...)
	}
	//tbcontext.WarningF(ctx, "pk_h_allUserIDs allPostIDMap_%v allUserPostIDMap_%v", allUserIDs, allPostIDMap, allUserPostIDMap)
	return allPostIDMap, allUserIDs, allUserPostIDMap
}

func getPostAndAuthor(ctx context.Context, postList []*client.Post, allPostIDMap map[int64][]int64, allUserPostIDMap map[int64][]int64) []int64{
	var userIDs []int64
	for _,postInfo := range postList{
		if postInfo.GetId() == 0 || postInfo.GetTid() == 0{
			tbcontext.WarningF(ctx, "pid[%d] or tid[%d] is 0", postInfo.GetId(), postInfo.GetTid())
			continue
		}
		allPostIDMap[postInfo.GetTid()] = append(allPostIDMap[postInfo.GetTid()], int64(postInfo.GetId()))
		//用户信息
		uid := postInfo.GetAuthor().GetId()
		if uid == 0  {
			tbcontext.WarningF(ctx, "the author_id of the pid[%d] is 0", postInfo.GetId())
			continue
		}
		//tbcontext.WarningF(ctx, "uid_%v", uid)
		if _,ok := allUserPostIDMap[uid];ok {
			tbcontext.WarningF(ctx, "uid_%v hit_repeatd ", uid)
			allUserPostIDMap[uid] = append(allUserPostIDMap[uid], int64(postInfo.GetId()))
			continue
		}

		allUserPostIDMap[uid] = append(allUserPostIDMap[uid], int64(postInfo.GetId()))
		userIDs = append(userIDs, uid)
		//tbcontext.WarningF(ctx, "userIDs_%v", userIDs)
	}
	return userIDs
}

func dealFilterPids(ctx context.Context, filterPIDs []uint64, baseData *types.CTopicDetailBaseData){
	tbcontext.WarningF(ctx, "filterPIDs_%v", filterPIDs)
	if len(filterPIDs) == 0 {
		return
	}
	//tbcontext.WarningF(ctx, "relateThreadIDs_%v commonThreadIDs_%v VoteThreadIDs_%v ScoreThreadIDs_%v PKThreadIDs_%v ",
	//	relateThreadIDs, commonThreadIDs, VoteThreadIDs, ScoreThreadIDs, PKThreadIDs)

	var relateFilterPostIDs,commonFilterPostIDs,voteFilterPostIDs,scoreFilterPostIDs,pkFilterPostIDs []int64
	for index ,pID := range filterPIDs{
		tbcontext.WarningF(ctx, "index_%d pid_%d", index, pID)

		//过滤relate_thread hot_post
		relateFilterPostIDs = getFilterPIDsByCardType(ctx, filterPIDs, &baseData.StaticField.RelateThreadInfo)
		//过滤common_thread hot_post+real_post
		commonFilterPostIDs = getFilterPIDsByCardType(ctx, filterPIDs, &baseData.StaticField.ModuleInfo.CommonThread.ThreadInfo)
		//过滤vote_thread post
		voteFilterPostIDs = getFilterPIDsByCardType(ctx, filterPIDs, &baseData.StaticField.ModuleInfo.VoteThread.ThreadInfo)
		//过滤score_thread post
		scoreFilterPostIDs = getFilterPIDsByCardType(ctx, filterPIDs, &baseData.StaticField.ModuleInfo.ScoreThread.ThreadInfo)
		//过滤pk_thread post
		pkFilterPostIDs = getFilterPIDsByCardType(ctx, filterPIDs, &baseData.StaticField.ModuleInfo.PKThread.ThreadInfo)
	}

	baseData.StaticField.RelateThreadInfo.FilterPostIds = append(baseData.StaticField.RelateThreadInfo.FilterPostIds,
		relateFilterPostIDs...)
	baseData.StaticField.ModuleInfo.CommonThread.ThreadInfo.FilterPostIds = append(baseData.StaticField.ModuleInfo.CommonThread.ThreadInfo.FilterPostIds,
		commonFilterPostIDs...)
	baseData.StaticField.ModuleInfo.VoteThread.ThreadInfo.FilterPostIds = append(baseData.StaticField.ModuleInfo.VoteThread.ThreadInfo.FilterPostIds,
		voteFilterPostIDs...)
	baseData.StaticField.ModuleInfo.ScoreThread.ThreadInfo.FilterPostIds = append(baseData.StaticField.ModuleInfo.ScoreThread.ThreadInfo.FilterPostIds,
		scoreFilterPostIDs...)
	baseData.StaticField.ModuleInfo.PKThread.ThreadInfo.FilterPostIds = append(baseData.StaticField.ModuleInfo.PKThread.ThreadInfo.FilterPostIds,
		pkFilterPostIDs...)
	tbcontext.WarningF(ctx, "baseData.StaticField.RelateThreadInfo.FilterPostIds_%v", baseData.StaticField.RelateThreadInfo.FilterPostIds)
}

func getFilterPIDsByCardType(ctx context.Context, filterPIDs []uint64, threadInfo *types.ThreadInfo) []int64 {
	if threadInfo == nil || (threadInfo.RealPostInfo == nil && threadInfo.HotPostInfo == nil) {
		return nil
	}

	var filterModulePIDs []int64
	//实时回复数据流
	for _,postList := range threadInfo.RealPostInfo{
		for _,post := range postList{
			if php2go.InArray(post.GetId(), filterPIDs){
				filterModulePIDs = append(filterModulePIDs, int64(post.GetId()))
			}
		}
	}

	//热议数据
	for _,postList := range threadInfo.HotPostInfo{
		for _,post := range postList{
			if php2go.InArray(post.GetId(), filterPIDs){
				filterModulePIDs = append(filterModulePIDs, int64(post.GetId()))
			}
		}
	}
	return filterModulePIDs
}