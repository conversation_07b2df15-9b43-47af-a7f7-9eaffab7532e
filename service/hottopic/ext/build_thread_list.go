package ext

import (
	"context"
	"strconv"
	"sync"

	"github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/feeddomain"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/userpost"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/hottopic"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type BuildThreadList struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("build_thread_list", func() engine.Job {
		return &BuildThreadListOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewBuildThreadList(ctx *engine.Context) *BuildThreadList {
	return &BuildThreadList{
		ctx: ctx,
	}
}

func (a *BuildThreadList) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData == nil || baseData.StaticField == nil {
		return false
	}

	if len(baseData.StaticField.RelateThreadIds) == 0 &&
		len(baseData.StaticField.ModuleInfo.CommonThread.ThreadInfo.ThreadIds) == 0 &&
		len(baseData.StaticField.ModuleInfo.VoteThread.ThreadInfo.ThreadIds) == 0 &&
		len(baseData.StaticField.ModuleInfo.ScoreThread.ThreadInfo.ThreadIds) == 0 &&
		len(baseData.StaticField.ModuleInfo.PKThread.ThreadInfo.ThreadIds) == 0 {
		return false
	}
	return true
}

func (a *BuildThreadList) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	if baseData == nil || baseData.BaseObj == nil || baseData.StaticField == nil || baseData.BaseObj.ObjRequest == nil {
		return nil
	}

	staticField := baseData.StaticField

	relateThreadIDs := staticField.RelateThreadIds
	commonThreadIDs := staticField.ModuleInfo.CommonThread.ThreadInfo.ThreadIds
	voteThreadIDs := staticField.ModuleInfo.VoteThread.ThreadInfo.ThreadIds
	scoreThreadIDs := staticField.ModuleInfo.ScoreThread.ThreadInfo.ThreadIds
	pkThreadIDs := staticField.ModuleInfo.PKThread.ThreadInfo.ThreadIds

	threadIDs := append(relateThreadIDs, commonThreadIDs...)
	threadIDs = append(threadIDs, voteThreadIDs...)
	threadIDs = append(threadIDs, scoreThreadIDs...)
	threadIDs = append(threadIDs, pkThreadIDs...)
	//去重
	// 获取帖子信息
	var threadIDList []uint64
	for _, threadID := range threadIDs {
		threadIDList = append(threadIDList, uint64(threadID))
	}

	userInfo := hottopic.UserInfo{
		UserID:        proto.Uint64(uint64(staticField.UserID)),
		BaiduID:       proto.String(baseData.BaseObj.ObjRequest.GetCommonAttr("baidu_id", "").(string)),
		Cuid:          proto.String(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", "").(string)),
		ClientType:    proto.Uint32(uint32(staticField.IntClientType)),
		ClientVersion: proto.String(staticField.StrClientVersion),
		SimpleID:      proto.String(baseData.BaseObj.ObjRequest.GetCommonAttr("sample_id", "").(string)),
		OsVersion:     proto.String(baseData.BaseObj.ObjRequest.GetCommonAttr("os_version", "").(string)),
		UserAgent:     proto.String(baseData.BaseObj.ObjRequest.GetCommonAttr("user_agent", "").(string)),
		Model:         proto.String(baseData.BaseObj.ObjRequest.GetCommonAttr("model", "").(string)),
		ScrH:          proto.Uint32(uint32(baseData.BaseObj.ObjRequest.GetCommonAttr("scr_h", 0).(int32))),
		ScrW:          proto.Uint32(uint32(baseData.BaseObj.ObjRequest.GetCommonAttr("scr_w", 0).(int32))),
	}

	var feedList []*feeddomain.ClientThreadInfoEx
	var err error
	var storeTypeMap = make(map[int64]types.CollectInfo)

	var wg = sync.WaitGroup{}
	wg.Add(1)
	go func(ctx context.Context, threadIDList []uint64, userInfo *hottopic.UserInfo) {
		defer wg.Done()
		feedList, err = hottopic.GetFeedDomain(ctx, threadIDList, userInfo)
		if nil != err {
			tbcontext.WarningF(ctx, "hottopic.GetFeedDomain error! err[%s]", err.Error())
			return
		}
	}(ctx, threadIDList, &userInfo)
	wg.Add(1)
	go func(ctx context.Context, threadIDs []uint64, userID int64) {
		defer wg.Done()
		storeTypeMap, _ = getThreadStore(ctx, threadIDs, userID)
		if nil != err {
			tbcontext.WarningF(ctx, "hottopic.GetFeedDomain error! err[%s]", err.Error())
			return
		}
	}(ctx, threadIDList, baseData.StaticField.UserID)
	wg.Wait()
	/**
	feedList,err = hottopic.GetFeedDomain(ctx, threadIDList, &userInfo)
	*/

	if nil != err {
		tbcontext.WarningF(ctx, "hottopic.GetFeedDomain error! err[%s]", err.Error())
		return nil
	}

	//tbcontext.WarningF(ctx, "feed_list[%v]", feedList)

	var threadList []*client.ThreadInfo
	var feedIDMap = make(map[int64]int)
	for _, feedInfo := range feedList {
		if feedInfo == nil || feedInfo.ThreadInfo == nil {
			tbcontext.WarningF(ctx, "hottopic_id[%d] feedInfo[%v or threadInfo] is nil", staticField.TopicID, feedInfo)
			continue
		}
		//设置map tid和index
		tid := feedInfo.ThreadInfo.GetId()
		threadInfo := feedInfo.ThreadInfo
		if tid <= 0 {
			tbcontext.WarningF(ctx, "hottopic_id[%d] tid is less 0, tid[%d]", staticField.TopicID, tid)
			continue
		}

		if threadInfo.Author == nil {
			tbcontext.WarningF(ctx, "hottopic_id[%d] threadInfo.Author is nil, tid[%d]", staticField.TopicID, tid)
		} else {
			threadInfo.Author = &client.User{
				IsLogin:  nil,
				Id:       threadInfo.Author.Id,
				Name:     threadInfo.Author.Name,
				NameShow: threadInfo.Author.NameShow,
				Portrait: threadInfo.Author.Portrait,
			}
		}

		if threadInfo.ForumInfo == nil {
			tbcontext.WarningF(ctx, "hottopic_id[%d] threadInfo.ForumInfo is nil, tid[%d]", staticField.TopicID, tid)
		} else {
			threadInfo.ForumInfo = &client.SimpleForum{
				Id:     threadInfo.ForumInfo.Id,
				Name:   threadInfo.ForumInfo.Name,
				Avatar: threadInfo.ForumInfo.Avatar,
			}
		}
		threadInfo.FirstPostContent = nil
		threadList = append(threadList, threadInfo)

		//设置tid的index
		if _, ok := feedIDMap[tid]; !ok {
			feedIDMap[tid] = len(threadList) - 1
		}

		//todo 继续剪枝用，先留着后续删除
		/**
		threadList = append(threadList, &client.ThreadInfo{
			Id:                         threadInfo.Id,
			Tid:                        threadInfo.Tid,
			Title:                      nil,
			ReplyNum:                   threadInfo.ReplyNum,
			ViewNum:                    nil,
			LastTime:                   threadInfo.LastTime,
			LastTimeInt:                threadInfo.LastTimeInt,
			ThreadTypes:                nil,
			IsTop:                      nil,
			IsGood:                     nil,
			IsVote:                     nil,
			IsBakan:                    nil,
			IsProtal:                   nil,
			IsMeizhi:                   nil,
			IsVoiceThread:              nil,
			IsActivity:                 nil,
			IsNotice:                   nil,
			Author:                     &client.User{
				IsLogin:             nil,
				Id:                  threadInfo.Author.Id,
				Name:                threadInfo.Author.Name,
				NameShow:            threadInfo.Author.NameShow,
				Portrait:            threadInfo.Author.Portrait,
				NoUn:                nil,
				Type:                nil,
				NewUserInfo:         nil,
				Userhide:            nil,
				Balv:                nil,
				IsManager:           nil,
				Rank:                nil,
				BimgUrl:             nil,
				MeizhiLevel:         nil,
				IsVerify:            nil,
				IsInterestman:       nil,
				Iconinfo:            nil,
				TshowIcon:           nil,
				UserType:            nil,
				IsCoreuser:          nil,
				IsHuinibuke:         nil,
				IosBimgFormat:       nil,
				LevelId:             nil,
				IsLike:              nil,
				IsBawu:              nil,
				BawuType:            nil,
				Portraith:           nil,
				Ip:                  nil,
				BDUSS:               nil,
				FansNum:             nil,
				ConcernNum:          nil,
				Sex:                 nil,
				MyLikeNum:           nil,
				Intro:               nil,
				HasConcerned:        nil,
				Passwd:              nil,
				PostNum:             nil,
				TbAge:               nil,
				IsMem:               nil,
				BimgEndTime:         nil,
				PayMemberInfo:       nil,
				Gender:              nil,
				IsMask:              nil,
				UserPics:            nil,
				PrivSets:            nil,
				IsFriend:            nil,
				LikeForum:           nil,
				GroupList:           nil,
				GiftNum:             nil,
				GiftList:            nil,
				IsSelectTail:        nil,
				IsGuanfang:          nil,
				BookmarkCount:       nil,
				BookmarkNewCount:    nil,
				MuteUser:            nil,
				FriendNum:           nil,
				FansNickname:        nil,
				BgPic:               nil,
				ParrScores:          nil,
				NovelFansInfo:       nil,
				VipInfo:             nil,
				GodData:             nil,
				HeavyUser:           nil,
				VipShowInfo:         nil,
				NewTshowIcon:        nil,
				TwAnchorInfo:        nil,
				ProfitList:          nil,
				ConsumeInfo:         nil,
				ThemeCard:           nil,
				VipCloseAd:          nil,
				ActivitySponsor:     nil,
				TbVip:               nil,
				NoPostHigh:          nil,
				Ecom:                nil,
				VisitorNum:          nil,
				TotalVisitorNum:     nil,
				Pendant:             nil,
				AlaInfo:             nil,
				SealPrefix:          nil,
				HasBottleEnter:      nil,
				VideoChannelInfo:    nil,
				SpringVirtualUser:   nil,
				EachOtherFriend:     nil,
				EsportData:          nil,
				AlaLiveInfo:         nil,
				NicknameUpdateTime:  nil,
				ThreadNum:           nil,
				AgreeNum:            nil,
				LeftCallNum:         nil,
				IsInvited:           nil,
				IsFans:              nil,
				PrivThread:          nil,
				IsVideobiggie:       nil,
				IsShowRedpacket:     nil,
				BaijiahaoInfo:       nil,
				BirthdayInfo:        nil,
				CanModifyAvatar:     nil,
				ModifyAvatarDesc:    nil,
				Influence:           nil,
				LevelInfluence:      nil,
				NewGodData:          nil,
				BawuThrones:         nil,
				CallFansInfo:        nil,
				BazhuGrade:          nil,
				IsDefaultAvatar:     nil,
				Uk:                  nil,
				CreationData:        nil,
				FavoriteNum:         nil,
				LiveRoomInfo:        nil,
				BusinessAccountInfo: nil,
				AppealThreadPopover: nil,
				ForumToolAuth:       nil,
				WorkNum:             nil,
				ShowPbPrivateFlag:   nil,
				TotalAgreeNum:       nil,
				WorkcreationData:    nil,
				TiebaUid:            nil,
				FollowFrom:          nil,
				ManagerForum:        nil,
				DisplayAuthType:     nil,
				WorkCreatorInfo:     nil,
				LevelName:           nil,
				EditConfig:          nil,
				IpAddress:           nil,
				IsNicknameEditing:   nil,
				EditingNickname:     nil,
				VirtualImageInfo:    nil,
				UserGrowth:          nil,
				DisplayIntro:        nil,
				NewIconUrl:          nil,
				DynamicUrl:          nil,
				ThemeTail:           nil,
				ThemeBackground:     nil,
				ThemeMyTab:          nil,
				WorldCupInfo:        nil,
				ShakeAdSwitch:       nil,
				Pendants:            nil,
				Pa:                  nil,
				EnableNewHomepage:   nil,
				TargetScheme:        nil,
				Tags:                nil,
				AvatarUrl:           nil,
				UserMarkList:        nil,
				ShowIconList:        nil,
				UserShowInfo:        nil,
				LogParam:            nil,
				LikeForumScheme:     nil,
			},
			LastReplyer:                nil,
			CommentNum:                 threadInfo.CommentNum,
			Abstract:                   nil,
			Media:                      threadInfo.Media,
			VoiceInfo:                  nil,
			MeizhiPic:                  nil,
			MediaNum:                   nil,
			ThreadType:                 threadInfo.ThreadType,
			Fid:                        threadInfo.Fid,
			Fname:                      threadInfo.Fname,
			LivePostType:               nil,
			IsLivepost:                 nil,
			ShowCommented:              nil,
			ClickUrl:                   nil,
			Video:                      threadInfo.Video,
			VideoSwf:                   nil,
			VideoCover:                 threadInfo.VideoCover,
			VideoId:                    threadInfo.VideoId,
			VideoMobileUrl:             nil,
			IsNtitle:                   nil,
			IsBub:                      nil,
			FirstPostId:                nil,
			Zan:                        nil,
			IsGlobalTop:                nil,
			IsPic:                      nil,
			PostList:                   nil,
			CreateTime:                 nil,
			RepostNum:                  nil,
			Topic:                      nil,
			HasCommented:               nil,
			From:                       nil,
			CollectStatus:              nil,
			CollectMarkPid:             nil,
			PostId:                     threadInfo.PostId,
			Time:                       nil,
			IsMembertop:                nil,
			AnchorInfo:                 nil,
			AuthorId:                   nil,
			ValidPostNum:               nil,
			IsLzDeleteAll:              nil,
			IsAd:                       nil,
			Ecom:                       nil,
			Pids:                       nil,
			Location:                   nil,
			Guess:                      nil,
			Timeline:                   nil,
			ActInfo:                    nil,
			HotWeight:                  nil,
			LivecoverSrc:               nil,
			Storecount:                 nil,
			PostNum:                    nil,
			HotTWInfo:                  nil,
			TwzhiboInfo:                nil,
			CategoryName:               nil,
			PollInfo:                   nil,
			Jid:                        nil,
			IsNovel:                    nil,
			IsNovelThank:               nil,
			IsNovelReward:              nil,
			VideoInfo:                  nil,
			PushEndTime:                nil,
			IsCopythread:               nil,
			OperatorFlag:               nil,
			TaskInfo:                   nil,
			PicNum:                     nil,
			IsGodthreadRecommend:       nil,
			YulePostActivity:           nil,
			AppCode:                    nil,
			ExtTails:                   nil,
			PushStatus:                 nil,
			CartoonInfo:                nil,
			LegoCard:                   nil,
			HighTogether:               nil,
			VideoactiveInfo:            nil,
			IsDeal:                     nil,
			DealInfo:                   nil,
			AnimationInfo:              nil,
			SkinInfo:                   nil,
			PsInfo:                     nil,
			BookChapter:                nil,
			IsBookChapter:              nil,
			RecomSource:                nil,
			RecomWeight:                nil,
			LastReadPid:                nil,
			CheakRepeat:                nil,
			AbTag:                      nil,
			RecomReason:                nil,
			VideoAdInfo:                nil,
			RichTitle:                  threadInfo.RichTitle,
			RichAbstract:               threadInfo.RichAbstract,
			AlaInfo:                    nil,
			IsOperateThread:            nil,
			IsTbreadDispatch:           nil,
			TbreadDispatchInfo:         nil,
			AppInfo:                    nil,
			ReportInfo:                 nil,
			VideoChannelInfo:           nil,
			DislikeInfo:                nil,
			DeclareList:                nil,
			MultipleForumList:          nil,
			IsMultiforumThread:         nil,
			AgreeNum:                   nil,
			TopAgreePost:               nil,
			Agree:                      threadInfo.Agree,
			IsPartialVisible:           nil,
			IsLinkThread:               nil,
			LinkInfo:                   nil,
			FreqNum:                    nil,
			IsGod:                      nil,
			ActivityInfo:               nil,
			PicInfo:                    nil,
			IsStoryAudit:               nil,
			ShareNum:                   nil,
			IsCalled:                   nil,
			TiebaGameInformationSource: nil,
			AuditTime:                  nil,
			MiddlePageNum:              nil,
			MiddlePagePassFlag:         nil,
			OriginThreadInfo:           nil,
			FirstPostContent:           nil,
			IsShareThread:              nil,
			RecomExtra:                 nil,
			TransNum:                   nil,
			MultiForumText:             nil,
			StarRankIcon:               nil,
			IsTopic:                    nil,
			TopicUserName:              nil,
			TopicH5Url:                 nil,
			PresentationStyle:          nil,
			OriForumInfo:               nil,
			IsVideobiggieRecomthread:   nil,
			DailyPaperTime:             nil,
			ForumInfo:                  nil,
			NawsInfo:                   nil,
			VideoSegment:               nil,
			IsTopImg:                   nil,
			TShareImg:                  nil,
			TopicModule:                nil,
			IsBjh:                      nil,
			ArticleCover:               nil,
			BjhContentTag:              nil,
			Nid:                        nil,
			IsHeadlinepost:             nil,
			Baijiahao:                  nil,
			IsSCard:                    nil,
			ScardPacketId:              nil,
			ThreadShareLink:            nil,
			IfComment:                  nil,
			IfCommentInfo:              nil,
			TabId:                      nil,
			TabName:                    nil,
			WonderfulPostInfo:          nil,
			PbLinkInfo:                 nil,
			Item:                       nil,
			ItemStar:                   nil,
			IsDeleted:                  nil,
			HotNum:                     nil,
			PbGoodsInfo:                nil,
			IsLocal:                    nil,
			PbEntry:                    nil,
			IsAuthorView:               nil,
			ForumUserLiveMsg:           nil,
			ForumFriendWatchingInfo:    nil,
			WorksInfo:                  nil,
			CollectNum:                 nil,
			ThreadRecommendInfos:       nil,
			RecomTagIcon:               nil,
			IsTiebaplusAd:              nil,
			TiebaplusOrderId:           nil,
			TiebaplusToken:             nil,
			TiebaplusExtraParam:        nil,
			TiebaplusCantDelete:        nil,
			IsFrsMask:                  nil,
			VoiceRoom:                  nil,
			TabShowMode:                nil,
			TiebaplusAd:                nil,
			RecommendTip:               nil,
			EditInfo:                   nil,
			IsPictxt:                   nil,
			ExposureMonitorUrl:         nil,
			ClickMonitorUrl:            nil,
			ThreadRecommendTag:         nil,
			CustomFigure:               nil,
			CustomState:                nil,
			IsHighlight:                nil,
			IsXiuxiuThread:             nil,
			AblumInfo:                  nil,
			ShowAdSubscript:            nil,
			TargetScheme:               nil,
			ConvertBtnType:             nil,
			IsExcellentThread:          nil,
			LiteratureFlag:             nil,
			HotPostList:                nil,
			RobotEntrance:              nil,
			ClickBackCard:              nil,
			PeiwanInfo:                 nil,
			RobotThreadType:            nil,
			BookId:                     nil,
			HeadType:                   nil,
			DisableShare:               nil,
			DisableShareToast:          nil,
			ShareUrl:                   nil,
			TopThreadSetTime:           nil,
			BusinessMix:                nil,
			ChatPrivate:                nil,
			LogParam:                   nil,
			AichatBotCard:              nil,
			GameExt:                    nil,
			PlaceholderCardId:          nil,
			IsHottopThread:             nil,
			DisableReply:               nil,
			ScoreInfo:                  threadInfo.ScoreInfo,
			ShowPostContent:            nil,
			ShowUserList:               nil,
			ShowExtStr:                 nil,
			ShowNewQuestionStyle:       nil,
			TitleAi:                    nil,
			FullPostList:               nil,
		})
		*/
	}

	//帖子信息处理+帖子信息为空的则过滤
	//RelateThreadInfo
	lastThreadList, filteredThreadIDs := buildThreadInfoList(ctx, threadList, feedIDMap, relateThreadIDs)
	if len(lastThreadList) > 0 {
		staticField.RelateThreadList = append(staticField.RelateThreadList, lastThreadList...)
	}

	if len(filteredThreadIDs) > 0 {
		staticField.RelateThreadInfo.FilterThreadIds = append(staticField.RelateThreadInfo.FilterThreadIds, filteredThreadIDs...)
	}

	//普通帖
	staticField.ModuleInfo.CommonThread.ThreadList, filteredThreadIDs = buildThreadInfoList(ctx, threadList, feedIDMap, commonThreadIDs)
	if len(filteredThreadIDs) > 0 {
		staticField.ModuleInfo.CommonThread.ThreadInfo.FilterThreadIds = append(staticField.ModuleInfo.CommonThread.ThreadInfo.FilterThreadIds, filteredThreadIDs...)
	}
	//投票帖
	staticField.ModuleInfo.VoteThread.ThreadList, filteredThreadIDs = buildThreadInfoList(ctx, threadList, feedIDMap, voteThreadIDs)
	if len(filteredThreadIDs) > 0 {
		staticField.ModuleInfo.VoteThread.ThreadInfo.FilterThreadIds = append(staticField.ModuleInfo.VoteThread.ThreadInfo.FilterThreadIds, filteredThreadIDs...)
	}
	//score帖
	staticField.ModuleInfo.ScoreThread.ThreadList, filteredThreadIDs = buildThreadInfoList(ctx, threadList, feedIDMap, scoreThreadIDs)
	if len(filteredThreadIDs) > 0 {
		staticField.ModuleInfo.ScoreThread.ThreadInfo.FilterThreadIds = append(staticField.ModuleInfo.ScoreThread.ThreadInfo.FilterThreadIds, filteredThreadIDs...)
	}
	//pk帖
	staticField.ModuleInfo.PKThread.ThreadList, filteredThreadIDs = buildThreadInfoList(ctx, threadList, feedIDMap, pkThreadIDs)
	if len(filteredThreadIDs) > 0 {
		staticField.ModuleInfo.PKThread.ThreadInfo.FilterThreadIds = append(staticField.ModuleInfo.PKThread.ThreadInfo.FilterThreadIds, filteredThreadIDs...)
	}

	//收藏信息处理
	dealCollectInfo(ctx, baseData.StaticField.RelateThreadList, storeTypeMap)
	dealCollectInfo(ctx, baseData.StaticField.ModuleInfo.CommonThread.ThreadList, storeTypeMap)
	dealCollectInfo(ctx, baseData.StaticField.ModuleInfo.VoteThread.ThreadList, storeTypeMap)
	dealCollectInfo(ctx, baseData.StaticField.ModuleInfo.ScoreThread.ThreadList, storeTypeMap)
	dealCollectInfo(ctx, baseData.StaticField.ModuleInfo.PKThread.ThreadList, storeTypeMap)
	return nil
}

type BuildThreadListOperator struct {
}

func (rdop *BuildThreadListOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewBuildThreadList(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "build_thread_list execute fail: %v", err)
		return err
	}

	return nil
}

// 获取帖子收藏状态  //todo 后续挪到feedDomain
func getThreadStore(ctx context.Context, threadIDList []uint64, userID int64) (map[int64]types.CollectInfo, error) {
	if len(threadIDList) == 0 {
		tbcontext.WarningF(ctx, "tid nums invalid")
		return nil, errors.New("tid nums is zero")
	}

	var storeTypeMap = make(map[int64]types.CollectInfo)
	var storeMux = sync.Mutex{}
	wg := sync.WaitGroup{}
	for _, threadID := range threadIDList {
		wg.Add(1)
		go func(ctx context.Context, threadID uint64, userID int64) {
			defer wg.Done()
			req := userpost.QueryThreadStoreTypeReq{
				Uid:      &userID,
				ThreadId: &threadID,
			}
			res := &userpost.QueryThreadStoreTypeRes{}
			err := tbservice.Call(ctx, "post", "queryThreadStoreType", req, res, tbservice.WithConverter(tbservice.JSONITER))
			if err != nil || res.Errno == nil || *res.Errno != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "fail to call service post:queryThreadStoreType or reply of output is nil, "+
					"input = %s, output = %s, err = %v", common.ToString(req), common.ToString(res), err)
				return
			}

			collectStatus := int32(0)
			collectMarkPid := ""
			if res.Output != nil && res.GetOutput().Reply != nil {
				collectStatus = int32(res.GetOutput().GetReply().GetKeptType())
				collectMarkPid = strconv.FormatInt(int64(res.GetOutput().GetReply().GetMarkPid()), 10)
			}

			storeMux.Lock()
			storeTypeMap[int64(threadID)] = types.CollectInfo{
				CollectStatus:  collectStatus,
				CollectMaskPid: collectMarkPid,
			}
			storeMux.Unlock()
			return
		}(ctx, threadID, userID)
	}
	wg.Wait()

	return storeTypeMap, nil
}

// 获取帖子列表 + 过滤tid列表
func buildThreadInfoList(ctx context.Context, threadList []*client.ThreadInfo, feedIDMap map[int64]int,
	threadIDList []int64) (lastThreadList []*client.ThreadInfo, filterThreadIDs []int64) {
	for _, tid := range threadIDList {
		var threadInfo *client.ThreadInfo
		if index, ok := feedIDMap[int64(tid)]; ok {
			threadInfo = threadList[index]
		} else {
			filterThreadIDs = append(filterThreadIDs, threadInfo.GetId())
			continue
		}

		lastThreadList = append(lastThreadList, threadInfo)
		//tbcontext.WarningF(ctx, "lastThreadList_%v", lastThreadList))
	}
	return
}

func dealCollectInfo(ctx context.Context, threadList []*client.ThreadInfo, storeTypeMap map[int64]types.CollectInfo) {
	for _, threadInfo := range threadList {
		if collectInfo, ok := storeTypeMap[threadInfo.GetId()]; ok {
			threadInfo.CollectStatus = &collectInfo.CollectStatus
			threadInfo.CollectMarkPid = &collectInfo.CollectMaskPid
		}
	}
}
