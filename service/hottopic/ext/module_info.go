package ext

import (
	"context"
	"errors"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/hottopic"
	constants "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/hottopic"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

var validCommonModuleType = []int32{
	constants.TopicCommonModuleTypeRelateForum,
	constants.TopicCommonModuleTypeRelateTopic,
	constants.TopicCommonModuleTypeRelatePkVoteThread,
	constants.TopicCommonModuleTypeRelateVoteThread,
	constants.TopicCommonModuleTypeRelateThread,
	constants.TopicCommonModuleTypeGameRoom,
	constants.TopicCommonModuleTypeScoreThread,
	constants.TopicCommonModuleTypeAiBot,
	constants.TopicCommonModuleTypeAiGameNew,
}

func init() {
	if err := engine.RegisterOperator("module_info", func() engine.Job {
		return &moduleInfo{}
	}); err != nil {
		panic(err.Error())
	}
}

type moduleInfo struct {
	topicID int64
	pn      int32
}

func (t *moduleInfo) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData.StaticField.TopicID <= 0 || baseData.Request.GetPn() > 1 {
		// 初始化module_info
		baseData.StaticField.ModuleInfo = &types.ModuleInfo{
			CommonThread: types.ModuleThread{
				ThreadInfo: types.ThreadInfo{},
			},
			VoteThread: types.ModuleThread{
				ThreadInfo: types.ThreadInfo{},
			},
			ScoreThread: types.ModuleThread{
				ThreadInfo: types.ThreadInfo{},
			},
			PKThread: types.ModuleThread{
				ThreadInfo: types.ThreadInfo{},
			},
			RelateForum: types.ModuleForum{},
			RelateTopic: types.ModuleTopic{},
			AiGame:      types.ModuleAiGame{},
			AiBot:       types.ModuleAiBot{},
			AiGameNew:   types.ModuleAiGameNew{},
		}
		return false
	}

	t.topicID = baseData.StaticField.TopicID
	req := baseData.Request
	t.pn = req.GetPn()
	return true
}

func (t *moduleInfo) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	module := NewModuleInfo()

	g := &gtask.Group{
		AllowSomeFail: true,
	}

	g.Go(func() error {
		// 运营干预
		input := map[string]map[string]any{
			"req": {
				"topic_id":  t.topicID,
				"op_status": 1,
				"limit":     50,
				"order_by":  "module_order",
				"pn":        t.pn,
			},
		}
		output := &hottopic.GetTopicCommonModuleRes{}
		err := tbservice.Call(ctx, "hottopic", "getTopicCommonModule", input, output)
		if err != nil || output.GetErrno() != errno.CodeSuccess {
			tbcontext.WarningF(ctx, "call hottopic::getTopicCommonModule fail err:[%v], input:[%+v], output:[%+v]", err, input, output)
		}
		if len(output.GetRet()) == 0 {
			return nil
		}
		for _, v := range output.GetRet() {
			if !php2go.InArray(v.GetCommonType(), validCommonModuleType) {
				continue
			}
			switch v.GetCommonType() {
			case constants.TopicCommonModuleTypeRelateForum:
				// 相关吧
				if module.RelateForum.RelateForumRaw == nil {
					module.RelateForum.RelateForumRaw = make(map[uint32]*hottopic.RelateForum)
				}
				for _, vv := range v.GetManualConf().GetRelateForums() {
					module.RelateForum.ForumIds = append(module.RelateForum.ForumIds, uint32(vv.GetForumId()))
					module.RelateForum.RelateForumRaw[uint32(vv.GetForumId())] = vv
				}
				module.RelateForum.ModuleOrder = v.GetModuleOrder()
				break
			case constants.TopicCommonModuleTypeRelateTopic:
				// 相关话题
				var topic []*client.RecomTopicList
				for _, vv := range v.GetManualConf().GetRelatedTopics() {
					topic = append(topic, &client.RecomTopicList{
						TopicId:   proto.Uint64(cast.ToUint64(vv.GetTopicId())),
						Tag:       proto.Uint32(cast.ToUint32(vv.GetTopicTag())),
						TopicName: vv.TopicName,
					})
				}
				module.RelateTopic.TopicInfoList = append(module.RelateTopic.TopicInfoList, topic...)
				module.RelateTopic.ModuleOrder = v.GetModuleOrder()
				break
			case constants.TopicCommonModuleTypeGameRoom:
				// ai游戏
				module.AiGame.GameRoomIds = append(module.AiGame.GameRoomIds, int64(v.GetManualConf().GetGameRoom().GetGameRoomId()))
				module.AiGame.ModuleOrder = v.GetModuleOrder()
				break
			case constants.TopicCommonModuleTypeRelatePkVoteThread:
				// PK贴
				dealManualConfThread(v.GetManualConf(), v.GetCommonType(), &module.PKThread.ThreadInfo)
				module.PKThread.ModuleOrder = v.GetModuleOrder()
				break
			case constants.TopicCommonModuleTypeRelateVoteThread:
				// 投票贴
				dealManualConfThread(v.GetManualConf(), v.GetCommonType(), &module.VoteThread.ThreadInfo)
				module.VoteThread.ModuleOrder = v.GetModuleOrder()
				break
			case constants.TopicCommonModuleTypeRelateThread:
				// 普通帖
				dealManualConfThread(v.GetManualConf(), v.GetCommonType(), &module.CommonThread.ThreadInfo)
				module.CommonThread.ModuleOrder = v.GetModuleOrder()
				break
			case constants.TopicCommonModuleTypeScoreThread:
				// 打分帖
				dealManualConfThread(v.GetManualConf(), v.GetCommonType(), &module.ScoreThread.ThreadInfo)
				module.ScoreThread.ModuleOrder = v.GetModuleOrder()
				break
			case constants.TopicCommonModuleTypeAiBot:
				// 普通会话bot
				module.AiBot.AiBotUID = v.GetManualConf().GetAiBot().GetAiBotUid()
				module.AiBot.ModuleOrder = v.GetModuleOrder()
				break
			case constants.TopicCommonModuleTypeAiGameNew:
				// 普通会话bot
				module.AiGameNew.AiGameUID = v.GetManualConf().GetAiGame().GetAiGameUid()
				module.AiGameNew.ModuleOrder = v.GetModuleOrder()
				break
			}
		}
		return nil
	})

	forumIDs := make([]uint32, 0)
	g.Go(func() error {
		// 相关吧
		input := map[string]map[string]any{
			"req": {
				"topic_id": t.topicID,
				"num":      20,
			},
		}
		output := &hottopic.GetTopicRelateForumRes{}
		err := tbservice.Call(ctx, "hottopic", "getTopicRelateForum", input, output)
		if err != nil || output.GetErrno() != errno.CodeSuccess {
			tbcontext.WarningF(ctx, "call hottopic::getTopicRelateForum fail err:[%v], input:[%+v], output:[%+v]", err, input, output)
		}
		if len(output.GetRet()) == 0 {
			return nil
		}
		for _, v := range output.GetRet() {
			forumIDs = append(forumIDs, uint32(v.GetForumId()))
		}
		return nil
	})

	_, err := g.Wait()
	if err != nil {
		tbcontext.WarningF(ctx, "call service err:%v", err)
	}

	// module_info 中运营未配置相关吧，则使用原相关吧模块数据
	if len(module.RelateForum.ForumIds) == 0 {
		module.RelateForum.ForumIds = forumIDs
	}

	baseData.StaticField.ModuleInfo = module
	return nil
}

// dealManualConfThread 处理手动配置
func dealManualConfThread(manualConf *hottopic.ManualConf, commonType int32, res *types.ThreadInfo) {
	if manualConf == nil || res == nil || res.ThreadIds == nil || res.PostIds == nil {
		return
	}
	if res.RecomTagInfo == nil {
		res.RecomTagInfo = make(map[int64]*client.ThreadRecommendTag)
	}
	for _, v := range manualConf.GetRelatedTids() {
		res.ThreadIds = append(res.ThreadIds, v.GetTid())
		if len(v.GetPids()) > 0 {
			res.PostIds[v.GetTid()] = v.GetPids()
		}
		res.RecomTagInfo[v.GetTid()] = &client.ThreadRecommendTag{
			Text: v.Label,
			Type: proto.Int32(commonType),
		}
	}
}

// NewModuleInfo 创建ModuleInfo
func NewModuleInfo() *types.ModuleInfo {
	return &types.ModuleInfo{
		CommonThread: types.ModuleThread{
			ThreadInfo: types.ThreadInfo{
				ThreadIds: []int64{},
				PostIds:   map[int64][]int64{},
			},
		},
		VoteThread: types.ModuleThread{
			ThreadInfo: types.ThreadInfo{
				ThreadIds: []int64{},
				PostIds:   map[int64][]int64{},
			},
		},
		ScoreThread: types.ModuleThread{
			ThreadInfo: types.ThreadInfo{
				ThreadIds: []int64{},
				PostIds:   map[int64][]int64{},
			},
		},
		PKThread: types.ModuleThread{
			ThreadInfo: types.ThreadInfo{
				ThreadIds: []int64{},
				PostIds:   map[int64][]int64{},
			},
		},
		RelateForum: types.ModuleForum{
			ForumIds: []uint32{},
		},
	}
}

func (t *moduleInfo) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	if !t.IsValid(ctx.CallerCtx(), baseData) {
		return errors.New("module_info is not valid")
	}
	if err := t.Execute(ctx.CallerCtx(), outData, baseData); err != nil {
		return err
	}

	return nil
}
