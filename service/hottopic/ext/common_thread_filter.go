package ext

import (
	"context"
	"errors"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type CommonThreadFilter struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("common_thread_filter", func() engine.Job {
		return &CommonThreadFilterOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewCommonThreadFilter(ctx *engine.Context) *CommonThreadFilter {
	return &CommonThreadFilter{
		ctx: ctx,
	}
}

func (a *CommonThreadFilter) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData == nil || baseData.StaticField == nil {
		return false
	}

	if len(baseData.StaticField.RelateThreadIds) == 0 &&
		len(baseData.StaticField.ModuleInfo.CommonThread.ThreadInfo.ThreadIds) == 0 &&
		len(baseData.StaticField.ModuleInfo.VoteThread.ThreadInfo.ThreadIds) == 0 &&
		len(baseData.StaticField.ModuleInfo.ScoreThread.ThreadInfo.ThreadIds) == 0 &&
		len(baseData.StaticField.ModuleInfo.PKThread.ThreadInfo.ThreadIds) == 0 {
		return false
	}
	return true
}

func (a *CommonThreadFilter) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	if baseData == nil || baseData.StaticField == nil {
		return nil
	}

	relateThreadIDs := baseData.StaticField.RelateThreadIds
	commonThreadIDs := baseData.StaticField.ModuleInfo.CommonThread.ThreadInfo.ThreadIds
	VoteThreadIDs := baseData.StaticField.ModuleInfo.VoteThread.ThreadInfo.ThreadIds
	ScoreThreadIDs := baseData.StaticField.ModuleInfo.ScoreThread.ThreadInfo.ThreadIds
	PKThreadIDs := baseData.StaticField.ModuleInfo.PKThread.ThreadInfo.ThreadIds

	threadIDs := append(relateThreadIDs, commonThreadIDs...)
	threadIDs = append(threadIDs, VoteThreadIDs...)
	threadIDs = append(threadIDs, ScoreThreadIDs...)
	threadIDs = append(threadIDs, PKThreadIDs...)
	//去重
	// 获取帖子信息
	var threadIDList []uint64
	for _,threadID := range threadIDs{
		threadIDList = append(threadIDList, uint64(threadID))
	}
	tbcontext.WarningF(ctx, "threadIDList_%v", threadIDList)
	filterThreadIDs, err := FilterThreadMask(ctx, threadIDList, baseData.StaticField.UserID)
	if nil != err {
		tbcontext.WarningF(ctx, "FilterThreadUserMask fail! err[%v]", err)
		return nil
	}

	tbcontext.WarningF(ctx, "filterThreadIDs_%v", filterThreadIDs)
	dealFilter(ctx, filterThreadIDs, baseData)
	return nil
}

type CommonThreadFilterOperator struct {
}

func (rdop *CommonThreadFilterOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewCommonThreadFilter(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "TopicInfo execute fail: %v", err)
		return err
	}
	return nil
}

//获取帖子列表 feedDomain todo 后续挪到基础库
func FilterThreadMask(ctx context.Context, threadIDList []uint64, userID int64) ([]uint64, error) {
	if len(threadIDList) == 0 {
		tbcontext.WarningF(ctx, "tid nums invalid")
		return nil, errors.New("tid nums is zero")
	}

	req := &frs.GetThreadMaskInfoReq{
		ThreadIds:   threadIDList,
	}
	res := &frs.GetThreadMaskInfoRes{}
	err := tbservice.Call(ctx, "post", "getThreadMaskInfo", req, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || *res.Errno != tiebaerror.ERR_SUCCESS || res.Output == nil || len(res.GetOutput().GetThreadInfo()) == 0 {
		tbcontext.WarningF(ctx, "fail to call service post:getThreadMaskInfo, input = %s, output = %s, err = %v", common.ToString(req), common.ToString(res), err)
		return nil, errors.New("call feed service error")
	}

	var filterThreadIDs []uint64
	for _,threadInfo := range res.GetOutput().GetThreadInfo(){
		if threadInfo.GetIsDeleted() == 1 || threadInfo.GetIsPartialVisible() == 1 ||
			threadInfo.GetIsUserBlocked() == 1 || threadInfo.GetIsUserFiltered() == 1 {
			filterThreadIDs = append(filterThreadIDs, threadInfo.GetThreadId())
		}
	}

	return filterThreadIDs, nil
}

func dealFilter(ctx context.Context, filterThreadIDs []uint64, baseData *types.CTopicDetailBaseData){
	tbcontext.WarningF(ctx, "filterThreadIDs_%v", filterThreadIDs)
	relateThreadIDs := baseData.StaticField.RelateThreadIds
	commonThreadIDs := baseData.StaticField.ModuleInfo.CommonThread.ThreadInfo.ThreadIds
	VoteThreadIDs := baseData.StaticField.ModuleInfo.VoteThread.ThreadInfo.ThreadIds
	ScoreThreadIDs := baseData.StaticField.ModuleInfo.ScoreThread.ThreadInfo.ThreadIds
	PKThreadIDs := baseData.StaticField.ModuleInfo.PKThread.ThreadInfo.ThreadIds
	//tbcontext.WarningF(ctx, "relateThreadIDs_%v commonThreadIDs_%v VoteThreadIDs_%v ScoreThreadIDs_%v PKThreadIDs_%v ",
	//	relateThreadIDs, commonThreadIDs, VoteThreadIDs, ScoreThreadIDs, PKThreadIDs)

	var relateFilterThreadIDs,commonFilterThreadIDs,voteFilterThreadIDs,scoreFilterThreadIDs,pkFilterThreadIDs []int64
	for index ,threadID := range filterThreadIDs{
		tbcontext.WarningF(ctx, "index_%d threadID_%d", index, threadID)

		//过滤relate_thread
		if php2go.InArray(int64(threadID), relateThreadIDs) {
			tbcontext.WarningF(ctx, "relate_threadID_%d hit", threadID)
			relateFilterThreadIDs = append(relateFilterThreadIDs, int64(threadID))
		}

		//tbcontext.WarningF(ctx, "relateFilterThreadIDs_%v", relateFilterThreadIDs)

		//过滤common_thread
		if php2go.InArray(int64(threadID), commonThreadIDs){
			tbcontext.WarningF(ctx, "common_threadID_%d hit", threadID)
			commonFilterThreadIDs = append(commonFilterThreadIDs, int64(threadID))
		}

		//tbcontext.WarningF(ctx, "commonFilterThreadIDs_%v", commonFilterThreadIDs)

		//过滤vote_thread
		if php2go.InArray(int64(threadID), VoteThreadIDs){
			tbcontext.WarningF(ctx, "vote_threadID_%d hit", threadID)
			voteFilterThreadIDs = append(voteFilterThreadIDs, int64(threadID))
		}

		//tbcontext.WarningF(ctx, "voteFilterThreadIDs_%v", voteFilterThreadIDs)

		//过滤score_thread
		if php2go.InArray(int64(threadID), ScoreThreadIDs){
			tbcontext.WarningF(ctx, "score_threadID_%d hit", threadID)
			scoreFilterThreadIDs = append(scoreFilterThreadIDs, int64(threadID))
		}

		//tbcontext.WarningF(ctx, "voteFilterThreadIDs_%v", voteFilterThreadIDs)

		//过滤pk_thread
		if php2go.InArray(int64(threadID), PKThreadIDs){
			tbcontext.WarningF(ctx, "pk_threadID_%d hit", threadID)
			pkFilterThreadIDs = append(pkFilterThreadIDs, int64(threadID))
		}

		tbcontext.WarningF(ctx, "pkFilterThreadIDs_%v", pkFilterThreadIDs)
	}

	baseData.StaticField.RelateThreadInfo.FilterThreadIds = append(baseData.StaticField.RelateThreadInfo.FilterThreadIds,
		relateFilterThreadIDs...)
	baseData.StaticField.ModuleInfo.CommonThread.ThreadInfo.FilterThreadIds = append(baseData.StaticField.ModuleInfo.CommonThread.ThreadInfo.FilterThreadIds,
		commonFilterThreadIDs...)
	baseData.StaticField.ModuleInfo.VoteThread.ThreadInfo.FilterThreadIds = append(baseData.StaticField.ModuleInfo.VoteThread.ThreadInfo.FilterThreadIds,
		voteFilterThreadIDs...)
	baseData.StaticField.ModuleInfo.ScoreThread.ThreadInfo.FilterThreadIds = append(baseData.StaticField.ModuleInfo.ScoreThread.ThreadInfo.FilterThreadIds,
		scoreFilterThreadIDs...)
	baseData.StaticField.ModuleInfo.PKThread.ThreadInfo.FilterThreadIds = append(baseData.StaticField.ModuleInfo.PKThread.ThreadInfo.FilterThreadIds,
		pkFilterThreadIDs...)
}
