package ext

import (
	"context"
	"errors"
	"sync"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/hottopic"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	hottopicUtil "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/hottopic"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type RelateTopic struct {
	ctx *engine.Context
}

var TagMap = map[string]uint32{
	"新":  1,
	"热":  2,
	"荐":  3,
	"爆":  4,
	"沸":  5,
	"首发": 6,
}

func init() {
	err := engine.RegisterOperator("relate_topic", func() engine.Job {
		return &RelateTopicOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewRelateTopic(ctx *engine.Context) *RelateTopic {
	return &RelateTopic{
		ctx: ctx,
	}
}

func (a *RelateTopic) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData == nil || baseData.StaticField == nil {
		return false
	}

	if baseData.StaticField.TopicInfo == nil || baseData.Request.GetPn() > 1 || baseData.StaticField.TopicInfo.GetIsPorn() == 1 ||
		baseData.StaticField.TopicInfo.GetComeFrom() == -1 || len(baseData.StaticField.ModuleInfo.RelateTopic.TopicIds) == 0 {
		tbcontext.WarningF(ctx, "relate_topic is not vaild!")
		return false
	}
	return true
}

func (a *RelateTopic) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	staticField := baseData.StaticField

	/**
	var pidListMap = make(map[uint64][]uint64)
	if staticField.RelateThreadInfo.ShowPostID != nil {
		for _, showPostInfo := range staticField.RelateThreadInfo.ShowPostID {
			pidListMap[uint64(showPostInfo.ThreadID)] = showPostInfo.PostIDList
		}
	}

	relateTopicList, _ := getTopiclist(ctx, staticField.RelateThreadIds, pidListMap, staticField.TopicID)
	**/

	relateTopicList := getTopicInfoList(ctx, baseData.StaticField.ModuleInfo.RelateTopic.TopicIds)
	relateTopicList = filterRelateTopicList(ctx, relateTopicList)
	staticField.ModuleInfo.RelateTopic.TopicInfoList = relateTopicList
	return nil
}

type RelateTopicOperator struct {
}

func (rdop *RelateTopicOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewRelateTopic(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "TopicInfo execute fail: %v", err)
		return err
	}

	return nil
}

func getTopicInfoList(ctx context.Context, topicIDs []int64) []*client.RecomTopicList {
	var topicInfoList []*client.RecomTopicList
	wg := sync.WaitGroup{}
	var mux = sync.Mutex{}
	var RelateTopicList []*client.RecomTopicList
	for _,topicID := range topicIDs {
		wg.Add(1)
		go func(topicID int64) {
			defer wg.Done()
			topicInfo := hottopicUtil.GetTopicInfo(ctx, int32(topicID), "", 0)
			if topicInfo != nil {
				mux.Lock()
				RelateTopicList = append(RelateTopicList, topicInfo.RecomTopicList)
				mux.Unlock()
			}else {
				tbcontext.WarningF(ctx, "call hottopicUtil.GetTopicInfo fail! topicInfo is nil, topic_id[%d]", topicID)
			}
		}(topicID)
	}
	wg.Wait()
	return topicInfoList
}



func getTopiclist(ctx context.Context, tidList []int64, pidListMap map[uint64][]uint64, topicID int64) ([]*client.RecomTopicList, error) {
	if len(tidList) == 0 {
		tbcontext.WarningF(ctx, "tidList nums invalid")
		return nil, errors.New("tidList nums is zero")
	}

	wg := sync.WaitGroup{}
	var mux = sync.Mutex{}
	var RelateTopicList []*client.RecomTopicList

	for _, tid := range tidList {
		wg.Add(1)
		go func(ctx context.Context, tid int64, pid int64, topicID int64) {
			defer wg.Done()
			topicList, err := getTopicInfo(ctx, tid, pid, topicID)
			if nil != err || len(topicList) == 0 {
				tbcontext.WarningF(ctx, "getTopicInfo fail or has no relate_topic! err[%v] topicList[%v]", err, topicList)
				return
			}

			mux.Lock()
			RelateTopicList = append(RelateTopicList, topicList...)
			mux.Unlock()
		}(ctx, tid, 0, topicID)

		if pids, ok := pidListMap[uint64(tid)]; ok {
			for _, pid := range pids {
				wg.Add(1)
				go func(ctx context.Context, tid int64, pid int64, topicID int64) {
					defer wg.Done()
					topicList, err := getTopicInfo(ctx, tid, pid, topicID)
					if nil != err || len(topicList) == 0 {
						tbcontext.WarningF(ctx, "getTopicInfo fail or has no relate_topic! err[%v] topicList[%v]", err, topicList)
						return
					}

					mux.Lock()
					RelateTopicList = append(RelateTopicList, topicList...)
					mux.Unlock()
				}(ctx, tid, int64(pid), topicID)
			}
		}
	}
	wg.Wait()
	return RelateTopicList, nil
}

func getTopicInfo(ctx context.Context, tid int64, pid int64, topicID int64) ([]*client.RecomTopicList, error) {
	req := &hottopic.GetThreadTopicReq{
		Req: &hottopic.GetThreadTopicReqData{
			ThreadId: &tid,
			PostId:   &pid,
			Limit:    proto.Int64(int64(4)),
		},
	}

	res := &hottopic.GetThreadTopicRes{}
	err := tbservice.Call(ctx, "hottopic", "getThreadTopic", req, res, tbservice.WithConverter(tbservice.JSONITER))
	tbcontext.WarningF(ctx, "getThreadTopic_req_%v_res_%v err_%v", *req, res.GetRet(), err)
	if err != nil || res.Errno == nil || *res.Errno != tiebaerror.ERR_SUCCESS || res.Ret == nil {
		tbcontext.WarningF(ctx, "fail to call service hottopic:getThreadTopic, input = %s, "+
			"output = %s, err = %v", common.ToString(req), common.ToString(res), err)
		return nil, errors.New("call hottopic getThreadTopic error")
	}

	topicList := res.Ret
	var relateTopicList []*client.RecomTopicList
	for _, topicInfo := range topicList {
		if topicInfo.GetTopicId() == uint64(topicID) {
			continue
		}

		topicTag, ok := TagMap[topicInfo.GetTopicTag()]
		if !ok {
			topicTag = uint32(0)
		}

		relateTopic := &client.RecomTopicList{
			TopicId:            topicInfo.TopicId,
			TopicName:          topicInfo.TopicName,
			DiscussNum:         topicInfo.DiscussNum,
			TopicDesc:          topicInfo.TopicDesc,
			IsVideoTopic:       topicInfo.IsVideoTopic,
			ComeFrom:           topicInfo.ComeFrom,
			ContentSource:      topicInfo.ContentSource,
			InteractionNumPage: topicInfo.InteractionNumPage,
			KeyWord:            topicInfo.KeyWord,
			Tag:                proto.Uint32(topicTag),
		}

		if topicInfo.GetIndFclass() == "成人色情" {
			relateTopic.IsPorn = proto.Int32(1)
		}
		relateTopicList = append(relateTopicList, relateTopic)
	}

	return relateTopicList, nil
}

func filterRelateTopicList(ctx context.Context, relateTopicList []*client.RecomTopicList) []*client.RecomTopicList {
	var relateTopicIDMap = make(map[uint64]int)
	var newRelateTopicList []*client.RecomTopicList

	for _, topicInfo := range relateTopicList {
		//限制5条
		if len(newRelateTopicList) >= 5 {
			break
		}

		//去重
		if _, ok := relateTopicIDMap[topicInfo.GetTopicId()]; ok {
			continue
		}

		//设置已处理标识
		relateTopicIDMap[topicInfo.GetTopicId()] = 1
		newRelateTopicList = append(newRelateTopicList, topicInfo)
	}
	return newRelateTopicList
}
