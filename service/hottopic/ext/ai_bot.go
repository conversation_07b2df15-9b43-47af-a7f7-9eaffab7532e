package ext

import (
	"context"
	"errors"
	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type AiBot struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("ai_bot", func() engine.Job {
		return &AiBotOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewAiBot(ctx *engine.Context) *AiBot {
	return &AiBot{
		ctx: ctx,
	}
}

func (a *AiBot) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	staticField := baseData.StaticField
	if staticField == nil || staticField.ModuleInfo == nil {
		return false
	}
	if staticField.ModuleInfo.AiBot.AiBotUID <= 0 && staticField.ModuleInfo.AiGameNew.AiGameUID <= 0 {
		tbcontext.WarningF(ctx, "has no ai_bot!")
		return false
	}
	return true
}

func (a *AiBot) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	staticField := baseData.StaticField
	if staticField == nil {
		return nil
	}
	aiBotUid := staticField.ModuleInfo.AiBot.AiBotUID
	aiGameUid := staticField.ModuleInfo.AiGameNew.AiGameUID
	botUIDList := make([]int64, 0)
	if aiBotUid > 0 {
		botUIDList = append(botUIDList, aiBotUid)
	}
	if aiGameUid > 0 {
		botUIDList = append(botUIDList, aiGameUid)
	}
	if len(botUIDList) == 0 {
		return nil
	}
	// 获取智能体数据
	BotMap, err := getAiBotByIDs(ctx, []int64{aiBotUid, aiGameUid})
	if err != nil {
		tbcontext.WarningF(ctx, "ai_bot execute getAiBotByIDs fail: %v", err)
		return nil
	}
	if BotMap == nil {
		return nil
	}
	if bot, ok := BotMap[aiBotUid]; ok {
		staticField.ModuleInfo.AiBot.AiBotInfo = bot
	}
	if aiGame, ok := BotMap[aiGameUid]; ok {
		staticField.ModuleInfo.AiGameNew.AiGameInfo = aiGame
	}
	return nil
}

type AiBotOperator struct {
}

func (rdop *AiBotOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewAiBot(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "ai_bot execute fail: %v", err)
		return err
	}

	return nil
}

func getAiBotByIDs(ctx context.Context, botUIDList []int64) (map[int64]*client.AibBot, error) {
	// 根据bot uid获取bot信息
	req := &chat.MgetAiBotUserInfoByUidReq{
		BotUids:                 botUIDList,
		NeedFilterOnlineStatus:  proto.Int32(1),
		NeedFilterAuditStatus:   proto.Int32(1),
		NeedFilterVisibleStatus: proto.Int32(1),
	}
	res := &chat.MgetAiBotUserInfoByUidRes{}
	err := tbservice.Call(ctx, "chat", "mgetAiBotUserInfoByUid", req, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || *res.Errno != tiebaerror.ERR_SUCCESS || res.Data == nil {
		tbcontext.WarningF(ctx, "fail to call service chat:mgetAiBotUserInfoByUid OR Output.Data is Nil, input = %s, "+
			"output = %s, err = %v", common.ToString(req), common.ToString(res), err)
		return nil, errors.New("call chat service error")
	}

	resMap := make(map[int64]*client.AibBot)
	for botUID, botInfo := range res.GetData().GetBotList() {
		bot := &client.AibBot{
			BotType:      proto.Int32(botInfo.GetBotType()),
			RobotVersion: proto.Int32(botInfo.GetBotVersion()),
			AiGamePlot: &client.AiGamePlot{
				PlotTitle:        proto.String(botInfo.GetAiGamePlot().GetPlotTitle()),
				PlotDescription:  proto.String(botInfo.GetAiGamePlot().GetPlotDescription()),
				Rate:             proto.Float64(float64(botInfo.GetAiGamePlot().GetRate())),
				PortraitUrl:      proto.String(botInfo.GetAiGamePlot().GetPortraitUrl()),
				PlotId:           proto.Int32(int32(botInfo.GetAiGamePlot().GetPlotId())),
				BotUk:            proto.String(botInfo.GetChatUk()),
				BotPa:            proto.Int64(botInfo.GetPa()),
				BotBackgroundUrl: proto.String(botInfo.GetAiGamePlot().GetBotBackgroundUrl()),
				CreateUser:       proto.String(botInfo.GetCreateUser()),
				CreateUserAvatar: proto.String(botInfo.GetCreateUserAvatar()),
				DialogueUserNum:  proto.Int32(botInfo.GetDialogueUserNum()),
				RoleType:         proto.String(botInfo.GetRoleType()),
			},
			BotInfo: &client.BotInfo{
				Name:             proto.String(botInfo.GetName()),
				Portrait:         proto.String(botInfo.GetPortrait()),
				BackgroundUrl:    proto.String(botInfo.GetBackgroundUrl()),
				Prologue:         proto.String(botInfo.GetPrologue()),
				Uk:               proto.String(botInfo.GetChatUk()),
				Pa:               proto.Int64(botInfo.GetPa()),
				CreateUser:       proto.String(botInfo.GetCreateUser()),
				CreateUserAvatar: proto.String(botInfo.GetCreateUserAvatar()),
				DialogueUserNum:  proto.Int32(botInfo.GetDialogueUserNum()),
				RoleType:         proto.String(botInfo.GetRoleType()),
			},
		}
		resMap[botUID] = bot
	}
	return resMap, nil
}
