package ext

import (
	"context"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type BuildTopicInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("build_topic_info", func() engine.Job {
		return &BuildTopicInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewBuildTopicInfo(ctx *engine.Context) *BuildTopicInfo {
	return &BuildTopicInfo{
		ctx: ctx,
	}
}

func (a *BuildTopicInfo) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData.StaticField.TopicInfo != nil {
		return false
	}
	return true
}

func (a *BuildTopicInfo) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	staticField := baseData.StaticField

	// todo build topic_info,替换topicInfo
	topicInfo := staticField.TopicInfo

	staticField.TopicInfo = topicInfo
	return nil
}

type BuildTopicInfoOperator struct {
}

func (rdop *BuildTopicInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewBuildTopicInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "TopicInfo execute fail: %v", err)
		return err
	}

	return nil
}
