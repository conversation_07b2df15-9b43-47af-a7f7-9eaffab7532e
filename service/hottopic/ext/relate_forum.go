package ext

import (
	"context"
	"errors"

	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type RelateForum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("relate_forum", func() engine.Job {
		return &RelateForumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewRelateForum(ctx *engine.Context) *RelateForum {
	return &RelateForum{
		ctx: ctx,
	}
}

func (a *RelateForum) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if len(baseData.StaticField.ModuleInfo.RelateForum.ForumIds) <= 0 {
		return false
	}
	return true
}

func (a *RelateForum) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	staticField := baseData.StaticField

	relateForum, _ := GetForumInfoByIDs(ctx, baseData.StaticField.ModuleInfo.RelateForum.ForumIds)

	//tbcontext.WarningF(ctx, "module_info[%v]", common.ToString(baseData.StaticField.ModuleInfo))
	//relateForum := buildRelateForum(ctx, &baseData.StaticField.ModuleInfo.RelateForum)

	staticField.ModuleInfo.RelateForum.ForumInfoList = relateForum
	return nil
}

type RelateForumOperator struct {
}

func (rdop *RelateForumOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewRelateForum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "TopicInfo execute fail: %v", err)
		return err
	}

	return nil
}

func buildRelateForum(ctx context.Context, moudleForum *types.ModuleForum) []*client.SimpleForum {
	var relateForum []*client.SimpleForum
	for _, forumID := range moudleForum.ForumIds {
		if relateForumRaw, ok := moudleForum.RelateForumRaw[forumID]; ok {
			relateForum = append(relateForum, &client.SimpleForum{
				Id:        proto.Int64(int64(forumID)),
				Name:      relateForumRaw.ForumName,
				Avatar:    relateForumRaw.Avatar,
				PostNum:   relateForumRaw.PostNum,
				ThreadNum: relateForumRaw.ThreadNum,
				MemberNum: relateForumRaw.MemberNum,
				Desc:      relateForumRaw.Desc,
			})
		}
	}
	return relateForum
}

// GetForumInfoByIDs todo 挪到基础库
func GetForumInfoByIDs(ctx context.Context, forumIdList []uint32) ([]*client.SimpleForum, error) {
	if len(forumIdList) == 0 {
		tbcontext.WarningF(ctx, "forumIdList nums invalid")
		return nil, errors.New("forumIdList nums is zero")
	}
	req := &forum.MgetBtxInfoExReq{
		ForumId: forumIdList,
	}
	res := &forum.MgetBtxInfoExRes{}

	err := tbservice.Call(ctx, "forum", "mgetBtxInfoEx", req, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || *res.Errno != tiebaerror.ERR_SUCCESS || res.Output == nil || len(res.GetOutput()) == 0 {
		tbcontext.WarningF(ctx, "fail to call service forum:mgetBtxInfoEx, input = %s, output = %s, err = %v",
			common.ToString(req), common.ToString(res), err)
		return nil, errors.New("call feed service error")
	}

	var forumInfoList []*client.SimpleForum
	for _, forumID := range forumIdList {
		if forumInfo, ok := res.GetOutput()[forumID]; ok {
			forumInfoList = append(forumInfoList, &client.SimpleForum{
				Id:        proto.Int64(int64(forumID)),
				Name:      proto.String(forumInfo.GetForumName().GetForumName()),
				Avatar:    proto.String(forumInfo.GetCard().GetAvatar()),
				Desc:      proto.String(forumInfo.GetCard().GetDesc()),
				Slogan:    proto.String(forumInfo.GetCard().GetSlogan()),
				MemberNum: proto.Int32(int32(forumInfo.GetStatistics().GetMemberCount())),
				ThreadNum: proto.Int32(int32(forumInfo.GetStatistics().GetThreadNum())),
				PostNum:   proto.Int32(int32(forumInfo.GetStatistics().GetPostNum())),
			})
		}
	}

	return forumInfoList, nil
}
