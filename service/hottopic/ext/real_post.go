package ext

import (
	"context"
	"errors"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/common/meta"
	"strconv"
	"sync"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/pb"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	wordlistHotTopicConf = "tb_wordlist_redis_hottopic_conf"
)

type RealPost struct {
}

func init() {
	err := engine.RegisterOperator("real_post", func() engine.Job {
		return NewRealPost()
	})
	if err != nil {
		panic(err)
	}
}

func NewRealPost() *RealPost {
	return &RealPost{}
}

func (r *RealPost) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData == nil || baseData.StaticField == nil {
		return false
	}
	if len(baseData.StaticField.ModuleInfo.VoteThread.ThreadInfo.ThreadIds) == 0 &&
		len(baseData.StaticField.ModuleInfo.PKThread.ThreadInfo.ThreadIds) == 0 {
		return false
	}
	return true
}

func (r *RealPost) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	if baseData == nil || baseData.StaticField == nil {
		return nil
	}
	staticField := baseData.StaticField
	tIDs := mergeThreadIDs(staticField)
	rpNum := getRealPostNum(ctx)
	var tpMap, pMap sync.Map
	g := &gtask.Group{
		AllowSomeFail: true,
	}

	for _, tID := range tIDs {
		tID := tID
		g.Go(func() error {
			input := &pb.GetInvertPostsByThreadIdReq{
				ThreadId:          proto.Uint64(uint64(tID)),
				Offset:            proto.Uint64(0),
				ResNum:            proto.Uint32(uint32(rpNum)),
				SeeAuthor:         proto.Uint32(0),
				HasComment:        proto.Uint32(0),
				HasMask:           proto.Uint32(1),
				HasExt:            proto.Uint32(1),
				StructuredContent: proto.Uint32(1),
			}
			output := &pb.GetInvertPostsByThreadIdRes{}
			err := tbservice.Call(ctx, "post", "getInvertPostsByThreadId", input, output)
			if err != nil || output.GetErrno() != errno.CodeSuccess {
				tbcontext.WarningF(ctx, "call post::getInvertPostsByThreadId fail err:[%v], input:[%+v], output:[%+v]", err, input, output)
			}
			if len(output.GetOutput().GetOutput()) == 0 {
				return nil
			}
			postList := output.GetOutput().GetOutput()[0].GetPostInfos()
			var pIDs []int64
			for _, p := range postList {
				pMap.Store(int64(p.GetPostId()), p)
				pIDs = append(pIDs, int64(p.GetPostId()))
			}
			tpMap.Store(tID, pIDs)
			if staticField.UserID <= 0 {
				return nil
			}
			/**
			g.Go(func() error {
				input := &agree.GetAgreeByUserIdAndPostIdsReq{
					ThreadId: proto.Int64(tID),
					PostIds:  pIDs,
					UserId:   proto.Int64(staticField.UserID),
				}
				output := &agree.GetAgreeByUserIdAndPostIdsRes{}
				err := tbservice.Call(ctx, "agree", "getAgreeByUserIdAndPostIds", input, output)
				if err != nil || output.GetErrno() != errno.CodeSuccess {
					tbcontext.WarningF(ctx, "call post::getAgreeByUserIdAndPostIds fail err:[%v], input:[%+v], output:[%+v]", err, input, output)
				}
				if output.GetData() == nil {
					return nil
				}
				for k, v := range output.GetData().GetMap() {
					pAgreeMap.Store(k, v)
				}
				return nil
			})
			 */
			return nil
		})
	}
	_, err := g.Wait()
	if err != nil {
		tbcontext.WarningF(ctx, "call service err:%v", err)
	}
	err = formatPost(ctx, baseData, &tpMap, &pMap)
	if err != nil {
		tbcontext.WarningF(ctx, "formatPost err:%v", err)
	}
	return nil
}

// formatPost 格式化帖子信息
func formatPost(ctx context.Context, baseData *types.CTopicDetailBaseData, tpMap *sync.Map, pMap *sync.Map) error {
	tdsf := baseData.StaticField
	if tdsf == nil || tpMap == nil || pMap == nil {
		return nil
	}
	if tdsf.ModuleInfo == nil {
		return nil
	}
	dealFunc := func(t *types.ThreadInfo) {
		if t == nil {
			return
		}
		if t.RealPostInfo == nil {
			t.RealPostInfo = make(map[int64][]*client.Post)
		}
		for _, tID := range t.ThreadIds {
			v, ok := tpMap.Load(tID)
			if !ok {
				continue
			}
			pIDs, ok := v.([]int64)
			if !ok {
				continue
			}
			for _, pID := range pIDs {
				v, ok = pMap.Load(pID)
				if !ok {
					continue
				}
				p, ok := v.(*post.Post)
				if !ok {
					continue
				}
				//v, _ := pAgreeMap.Load(pID)
				//a, _ := v.(*agree.AgreeMap)
				realPostInfo := buildPost(ctx, tID, p, baseData)
				if realPostInfo != nil {
					t.RealPostInfo[tID] = append(t.RealPostInfo[tID], realPostInfo)
				}
			}
		}

	}
	//dealFunc(&tdsf.ModuleInfo.CommonThread.ThreadInfo)
	dealFunc(&tdsf.ModuleInfo.VoteThread.ThreadInfo)
	//dealFunc(&tdsf.ModuleInfo.ScoreThread.ThreadInfo)
	dealFunc(&tdsf.ModuleInfo.PKThread.ThreadInfo)

	return nil
}

// buildPost 构建帖子信息
func buildPost(ctx context.Context, tID int64, p *post.Post, baseData *types.CTopicDetailBaseData) *client.Post {
	if p == nil {
		return nil
	}

	var clientContent []*client.PbContent
	if p.Content != nil {
		var structContentList []*meta.PostStructContent
		if contentList, ok := p.GetContent().([]interface{}); ok {
			for _, content := range contentList {
				var structContent meta.PostStructContent
				err := common.StructAToStructBCtx(ctx, content, &structContent)
				if nil != err {
					tbcontext.WarningF(ctx, "structContent[%v] is not meta.PostStructContent ! err[%v]", content, err)
				}else{
					structContentList = append(structContentList, &structContent)
				}
			}
			clientContent = processRichAbstract(ctx, structContentList, baseData)
		} else {
			tbcontext.WarningF(ctx, "real_post content is not []interface{}, content[%v]", p.GetContent())
		}
	}

	return &client.Post{
		Tid: proto.Int64(tID),
		Id:  proto.Uint64(p.GetPostId()),
		Author: &client.User{
			Id:   proto.Int64(p.GetUserId()),
			Name: proto.String(p.GetUsername()),
			NameShow: proto.String(p.GetUsername()),//todo get 用户昵称
		},
		Title:         proto.String(p.GetTitle()),
		AddPostNumber: proto.Uint32(p.GetCommentNum()),
		SubPostNumber: proto.Uint32(p.GetCommentNum()),
		Time:          proto.Uint32(p.GetNowTime()),
		Agree: &client.Agree{
			AgreeNum:  proto.Int64(p.GetAgreeNum()),
			DisagreeNum:proto.Int64(p.GetDisagreeNum()),
			DiffAgreeNum:proto.Int64(p.GetAgreeNum() - p.GetDisagreeNum()),
		},
		Content: clientContent,
	}
}

// mergeThreadIds 合并所有帖子id
func mergeThreadIDs(tdsf *types.TopicDetailStaticField) []int64 {
	var allThreadIDs []int64
	if tdsf.ModuleInfo == nil {
		return allThreadIDs
	}
	//allThreadIDs = append(allThreadIDs, tdsf.ModuleInfo.CommonThread.ThreadInfo.ThreadIds...)
	allThreadIDs = append(allThreadIDs, tdsf.ModuleInfo.VoteThread.ThreadInfo.ThreadIds...)
	//allThreadIDs = append(allThreadIDs, tdsf.ModuleInfo.ScoreThread.ThreadInfo.ThreadIds...)
	allThreadIDs = append(allThreadIDs, tdsf.ModuleInfo.PKThread.ThreadInfo.ThreadIds...)
	return allThreadIDs
}

// getRealPostNum 获取实时帖子数量
func getRealPostNum(ctx context.Context) int32 {
	res, err := wordserver.QueryKey(ctx, wordlistHotTopicConf, "real_post_num")
	if err != nil {
		tbcontext.WarningF(ctx, "query wordlist failed, err=[%v], keys=[%s]", err, "real_post_num")
		return 0
	}
	conf, _ := strconv.ParseInt(res, 10, 32)
	return int32(conf)
}

func (r *RealPost) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	if !r.IsValid(ctx.CallerCtx(), baseData) {
		return errors.New("real_post is not valid")
	}
	if err := r.Execute(ctx.CallerCtx(), outData, baseData); err != nil {
		return err
	}

	return nil
}

func buildPostList(ctx context.Context, postInfo *post.Post) *client.Post {

	return nil
}
