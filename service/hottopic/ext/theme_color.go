package ext

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"hash/crc32"
	"net/http"
	"net/url"
	"strings"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/codec"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/utilconst"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientTopicDetail "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/hottopic/topicDetail"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	SubApp   = "tieba_h5"
	CallFrom = "tbtopiclist"
	Ak       = "xA6Ntf6AUkdV8jpiXOtIROBpC1WCgjXT"
	Sk       = "0XE4gIrA3tK6RRoEQp8Kgy677eB3drtY"
)

type HSB struct {
	B int `json:"b"`
	H int `json:"h"`
	S int `json:"s"`
}

type TimePeriod struct {
	Model    int    `json:"model"`
	Value    string `json:"value"`
	ValueHsb HSB    `json:"value_hsb"`
}

type ColorData struct {
	Data []struct {
		Dark  TimePeriod `json:"dark"`
		Day   TimePeriod `json:"day"`
		Night TimePeriod `json:"night"`
	} `json:"data"`
	Errmsg string `json:"errmsg"`
	Errno  int    `json:"errno"`
}

type timeLink struct {
	Dark  TimePeriod `json:"dark"`
	Day   TimePeriod `json:"day"`
	Night TimePeriod `json:"night"`
}

type ThemeColor struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("theme_color", func() engine.Job {
		return &ThemeColorOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewThemeColor(ctx *engine.Context) *ThemeColor {
	return &ThemeColor{
		ctx: ctx,
	}
}

func (a *ThemeColor) IsValid(ctx context.Context, baseData *types.CTopicDetailBaseData) bool {
	if baseData == nil || baseData.StaticField == nil || baseData.StaticField.TopicInfo == nil {
		return false
	}
	return true
}

func (a *ThemeColor) Execute(ctx context.Context, outData *clientTopicDetail.DataRes, baseData *types.CTopicDetailBaseData) error {
	staticField := baseData.StaticField

	themeColor := &client.ThemeColorInfo{
		Day: &client.ThemeElement{
			CommonColor: proto.String(utilconst.DAY_THEME_COLOR_DEFAULT),
		},
		Night: &client.ThemeElement{
			CommonColor: proto.String(utilconst.NIGHT_THEME_COLOR_DEFAULT),
		},
		Dark: &client.ThemeElement{
			CommonColor: proto.String(utilconst.DARK_THEME_COLOR_DEFAULT),
		},
	}

	outData.ThemeColor = themeColor

	//不为空 读取缓存的智能取色 如果没缓存 设置缓存
	redisKey := "topic_smart_color_" + url.QueryEscape(image.DelAuthParamUrl(staticField.TopicInfo.GetTopicPic()))
	res, err := resource.RedisPush.Get(ctx, redisKey).Result()
	if err != nil && !errors.Is(err, redis.ErrNil) {
		tbcontext.WarningF(ctx, "get data from redis failed: key:%s err:%v", redisKey, err)
		return err
	}

	if res != "" {
		var dRvalue timeLink
		err = json.Unmarshal([]byte(res), &dRvalue)
		if err != nil {
			tbcontext.WarningF(ctx, "get Unmarshal data from redis failed: err:%v", err)
			return err
		}

		themeColor = &client.ThemeColorInfo{
			Day: &client.ThemeElement{
				CommonColor: proto.String(dRvalue.Day.Value),
			},
			Night: &client.ThemeElement{
				CommonColor: proto.String(dRvalue.Night.Value),
			},
			Dark: &client.ThemeElement{
				CommonColor: proto.String(dRvalue.Dark.Value),
			},
		}

		outData.ThemeColor = themeColor
	} else {
		ts := time.Now().Unix()
		sign := crc32.ChecksumIEEE([]byte(Ak + fmt.Sprint(ts) + Sk))
		requestUrl := fmt.Sprintf("/platform/v2/c/getMulticolor?method=platform_c_getMulticolor&callfrom=%s&ak=%s&ts=%d&tk=%d", CallFrom, Ak, ts, sign)
		requestBody := url.Values{
			"image":  {staticField.TopicInfo.GetTopicPic()},
			"subapp": {SubApp},
		}

		req := &ghttp.RalRequest{
			APIName: "service_smartcolor",
			Method:  "POST",
			Path:    requestUrl,
			Header: http.Header{
				"pathinfo":     []string{requestUrl},
				"Content-Type": []string{"application/x-www-form-urlencoded"},
			},
			Body: strings.NewReader(requestBody.Encode()),
		}

		var ralResData interface{}
		resp := &ghttp.RalResponse{
			Data:    &ralResData,
			Decoder: codec.JSONDecoder,
		}

		//请求服务
		err := ral.RAL(ctx, "service_smartcolor", req, resp)
		if err != nil && !errors.Is(err, redis.ErrNil) {
			tbcontext.WarningF(ctx, "request service_smartcolor failed: err:%v", err)
			return err
		}
		cacheValue, err := json.Marshal(ralResData)
		if err != nil {
			tbcontext.WarningF(ctx, "get Marshal data from service_smartcolor failed: err:%v", err)
			return err
		}

		var dvalue ColorData
		err = json.Unmarshal([]byte(cacheValue), &dvalue)
		if err != nil {
			tbcontext.WarningF(ctx, "get Unmarshal data from service_smartcolor failed: err:%v", err)
			return err
		}

		if dvalue.Errno != 0 || len(dvalue.Data) == 0 {
			outData.ThemeColor = themeColor
			return nil
		}

		if len(dvalue.Data) >= 1 {
			if dvalue.Data[0].Day.Value == "" {
				dvalue.Data[0].Day.Value = utilconst.DAY_THEME_COLOR_DEFAULT
			}

			if dvalue.Data[0].Night.Value == "" {
				dvalue.Data[0].Night.Value = utilconst.NIGHT_THEME_COLOR_DEFAULT
			}

			if dvalue.Data[0].Dark.Value == "" {
				dvalue.Data[0].Dark.Value = utilconst.DARK_THEME_COLOR_DEFAULT
			}

			themeColor = &client.ThemeColorInfo{
				Day: &client.ThemeElement{
					CommonColor: proto.String(dvalue.Data[0].Day.Value),
				},
				Night: &client.ThemeElement{
					CommonColor: proto.String(dvalue.Data[0].Night.Value),
				},
				Dark: &client.ThemeElement{
					CommonColor: proto.String(dvalue.Data[0].Dark.Value),
				},
			}

			outData.ThemeColor = themeColor

			//设置缓存
			_, err = resource.RedisPush.Set(ctx, redisKey, common.ToString(dvalue.Data[0]), 3600*time.Second).Result()
			if err != nil && !errors.Is(err, redis.ErrNil) {
				tbcontext.WarningF(ctx, "set key[%s] to redis err[%v]", redisKey, err)
				return err
			}
		}
	}

	return nil
}

type ThemeColorOperator struct {
}

func (rdop *ThemeColorOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientTopicDetail.DataRes
	var baseData *types.CTopicDetailBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewThemeColor(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "TopicInfo execute fail: %v", err)
		return err
	}

	return nil
}
