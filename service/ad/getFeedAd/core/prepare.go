package core

import (
	"context"
	"strconv"
	"time"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientAd "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/ad/getFeedAd"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Prepare struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("feedAd_prepare", func() engine.Job {
		return &PrepareOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPrepare(ctx *engine.Context) *Prepare {
	return &Prepare{
		ctx: ctx,
	}
}
func (a *Prepare) IsValid(ctx context.Context, baseData *types.FeedAdBaseData) bool {

	return true
}
func (a *Prepare) Execute(ctx context.Context, outData *clientAd.DataRes, baseData *types.FeedAdBaseData) error {
	staticField := baseData.StaticField
	// objReq := baseData.BaseObj.ObjRequest
	// 处理实验逻辑
	ubsABTestConfig := UbsAbtest.GetUbsAbtestConfig(ctx, staticField.SampleID, strconv.Itoa(int(staticField.UserID)), "")
	var arrSampleIds []string
	for _, value := range ubsABTestConfig {
		arrSampleIds = append(arrSampleIds, value["sid"])
	}
	staticField.ArrSampleIds = arrSampleIds
	staticField.AdSet = getAdSet(ctx, staticField.UserID)

	return nil
}

type PrepareOperator struct {
}

func (rdop *PrepareOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientAd.DataRes
	var baseData *types.FeedAdBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewPrepare(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "prepare execute fail: %v", err)
		return err
	}

	return nil
}

func getAdSet(ctx context.Context, uid int64) bool {
	adSet := true
	// isVip := false
	userInput := map[string]any{
		"user_id": uid,
	}
	userOutput := &user.GetUserDataRes{}
	err := tbservice.Call(ctx, "user", "getUserData", userInput, &userOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || userOutput.Errno == nil || userOutput.GetErrno() != tiebaerror.ERR_SUCCESS ||
		userOutput.UserInfo == nil {
		tbcontext.WarningF(ctx, "call user:getUserData fail, err:[%v], output:[%s]", err, common.ToString(userOutput))
		return adSet
	}
	var mparrProps *user.MParrProps
	userInfo := userOutput.UserInfo[0]
	if userInfo != nil {
		mparrProps = userInfo.GetMParrProps()
	}

	if mparrProps != nil {
		level := mparrProps.Level
		if level != nil {
			now := time.Now().Unix()
			propsId := level.GetPropsId()
			endTime := level.GetEndTime()
			if propsId >= 2 && int64(endTime) >= now {
				// isVip = true
				adSet = false
			}
		}
	}
	// if isVip {
	// 	userAdSetInput := &map[string]any{
	// 		"user_id": uid,
	// 	}
	// 	userAdSetOutput := &celebrity.GetUserAdSetRes{}
	// 	err := tbservice.Call(ctx, "celebrity", "getUserAdSet", userAdSetInput, userAdSetOutput, tbservice.WithConverter(tbservice.JSONITER))
	// 	if err != nil || userAdSetOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
	// 		tbcontext.WarningF(ctx, "fail to call service celebrity:getUserAdSet, input = %v, output = %v, err = %v",
	// 			common.ToString(userAdSetInput), common.ToString(userAdSetOutput), err)
	// 	}
	// 	adSetConf := userAdSetOutput.GetData()
	// 	if adSetConf.GetIsOpen() == 1 && adSetConf.GetVipClose() == 1 {
	// 		adSet = false
	// 		tbcontext.WarningF(ctx, "user %d is vip and no ad", uid)
	// 	}
	// }
	return adSet
}
