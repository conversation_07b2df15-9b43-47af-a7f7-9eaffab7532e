package core

import (
	"context"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/business/dsp"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientAd "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/ad/getFeedAd"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/business/bidding"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Response struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("feedAd_response", func() engine.Job {
		return &ResponseOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewResponse(ctx *engine.Context) *Response {
	return &Response{
		ctx: ctx,
	}
}
func (a *Response) IsValid(ctx context.Context, baseData *types.FeedAdBaseData) bool {

	return true
}

// Execute 是 Response 对象的方法
func (a *Response) Execute(ctx context.Context, outData *clientAd.DataRes, baseData *types.FeedAdBaseData) error {
	if nil == baseData || nil == baseData.StaticField {
		return nil
	}
	staticField := baseData.StaticField

	if len(staticField.Frs) > 0 {
		staticField.Frs = a.filterDataList(ctx, baseData, staticField.Frs, "frs")
		outData.Frs = staticField.Frs
	}
	if len(staticField.Recom) > 0 {
		staticField.Recom = a.filterDataList(ctx, baseData, staticField.Recom, "recom")
		outData.Recom = staticField.Recom
	}
	if len(staticField.PbBanner) > 0 {
		staticField.PbBanner = a.filterDataList(ctx, baseData, staticField.PbBanner, "pb_banner")
		outData.PbBanner = staticField.PbBanner
	}
	if len(staticField.PbComment) > 0 {
		staticField.PbComment = a.filterDataList(ctx, baseData, staticField.PbComment, "pb_comment")
		outData.PbComment = staticField.PbComment
	}

	dsp.LogAdReqCnt(ctx, baseData.TmpData)

	return nil
}

// filterDataList 过滤数据列表，返回符合条件的 App 列表
// ctx: 上下文对象，可以传入 nil
// baseData: 广告基本数据，类型为 *types.FeedAdBaseData
// appList: 应用列表，类型为 []*client.App
// page: 页面类型，字符串类型，例如："home"、"search"等
// 返回值: []*client.App，符合条件的 App 列表
func (a *Response) filterDataList(ctx context.Context, baseData *types.FeedAdBaseData, appList []*client.App, page string) []*client.App {
	var reqInfo bidding.ReqInfo
	dataList := make(map[string][]bidding.Item) // 初始化 dataList

	newAppList := make([]*client.App, 0)

	// 只需设置一次的值，移到循环外
	reqInfo.PageType = page
	reqInfo.ClientType = baseData.StaticField.ClientType
	reqInfo.ClientVersion = baseData.StaticField.ClientVersion

	for _, app := range appList {
		var item bidding.Item
		item.Additional = app
		item.ResourceName = app.GetPlatformType()
		item.SubResourceName = app.GetBusinessType()

		dataList["afd"] = append(dataList["afd"], item)
	}

	dataList = bidding.FilterDataList(ctx, reqInfo, dataList)

	for _, item := range dataList["afd"] {
		// 确保 item.Additional 是 *client.App 类型，避免出现空指针
		if app, ok := item.Additional.(*client.App); ok {
			newAppList = append(newAppList, app)
		}
	}

	dsp.LogAdReqCnt(ctx, baseData.TmpData)

	return newAppList
}

type ResponseOperator struct {
}

func (rdop *ResponseOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientAd.DataRes
	var baseData *types.FeedAdBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewResponse(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "response execute fail: %v", err)
		return err
	}

	return nil
}
