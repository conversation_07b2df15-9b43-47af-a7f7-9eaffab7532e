package ext

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/gdp/extension/gtask"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/business/dsp"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientAd "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/ad/getFeedAd"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	ResourceBrandAd  = "brandAd"
	businessBrandAd  = "businessBrandAd"
	BrandAdSampleKey = "12_71_brand_ad"
)

func init() {
	err := engine.RegisterOperator(businessBrandAd, func() engine.Job {
		return &BusinessBrandAdOperator{}
	})
	if err != nil {
		panic(err)
	}
}

type BusinessBrandAd struct {
	ctx *engine.Context
}

func NewBusinessBrandAd(ctx *engine.Context) *BusinessBrandAd {
	return &BusinessBrandAd{
		ctx: ctx,
	}
}

func (b *BusinessBrandAd) IsValid(ctx context.Context, baseData *types.FeedAdBaseData) bool {
	if nil == b || nil == ctx || nil == baseData || nil == baseData.StaticField {
		return false
	}
	staticField := baseData.StaticField
	clientVersion := staticField.ClientVersion
	if clientvers.Compare("12.71.0", clientVersion) < 0 {
		return false
	}

	//小流量
	// arrSampleIds := staticField.ArrSampleIds
	// if !php2go.InArray(BrandAdSampleKey, arrSampleIds) {
	// 	return false
	// }
	// 校验免广告
	if !staticField.AdSet {
		return false
	}
	return true
}

// Execute Execute 执行函数，将商业广告添加到输出数据中
func (b *BusinessBrandAd) Execute(ctx context.Context, outData *clientAd.DataRes, baseData *types.FeedAdBaseData) error {
	if nil == b || nil == ctx || nil == baseData || nil == baseData.BaseObj || nil == baseData.BaseObj.ObjRequest ||
		nil == outData || nil == baseData.StaticField {
		return errors.New("input params invalid")
	}
	staticField := baseData.StaticField
	businessType := "asyncBrandAd"
	adShowTypeVal := "14"
	adClickTypeVal := "15"
	platformType := "external"
	limitConf := &dsp.LimitConf{
		AdxSource: nil,
		KeySuffix: "frequency_brandAd",
	}
	//频控，商业广告展示次数
	brandAdObj := dsp.NewBrandAdDsp(ctx, resource.RedisAdsense, baseData.BaseObj.ObjRequest, limitConf,
		dsp.WithNewParam(resource.RedisUserGrowth))

	brandAdNeedparam := staticField.AdNeedParam["asyncBrandAd"]
	if brandAdNeedparam != nil {
		eg := new(gtask.Group)
		if brandAdNeedparam["pb_banner"] > 0 {
			eg.Go(func() error {
				pbBannerApp, err := brandAdObj.GetDspData("pb_banner")
				dsp.AddAdReqCnt(ctx, baseData.TmpData, "pb_banner", businessType, 1)
				if err != nil {
					tbcontext.WarningF(ctx, "brandDspFailed:%v", err.Error())
					return nil
				}
				pbBannerApp.BusinessType = &businessType
				for _, kv := range pbBannerApp.LogParam {
					if *kv.Key == "ad_show_type" {
						kv.Value = &adShowTypeVal
					}
					if *kv.Key == "ad_click_type" {
						kv.Value = &adClickTypeVal
					}
				}
				pbBannerApp.PlatformType = &platformType
				staticField.AppendBanner([]*client.App{pbBannerApp})
				dsp.AddAdRespCnt(ctx, baseData.TmpData, "pb_banner", businessType, 1)
				return nil
			})
		}
		if brandAdNeedparam["pb_comment"] > 0 {
			eg.Go(func() error {
				pbCommentApp, err := brandAdObj.GetDspData("pb_comment")
				dsp.AddAdReqCnt(ctx, baseData.TmpData, "pb_comment", businessType, 1)
				if err != nil {
					tbcontext.WarningF(ctx, "brandDspFailed:%v", err.Error())
					return nil
				}
				pbCommentApp.BusinessType = &businessType
				for _, kv := range pbCommentApp.LogParam {
					if *kv.Key == "ad_show_type" {
						kv.Value = &adShowTypeVal
					}
					if *kv.Key == "ad_click_type" {
						kv.Value = &adClickTypeVal
					}
				}
				pbCommentApp.PlatformType = &platformType
				dsp.AddAdRespCnt(ctx, baseData.TmpData, "pb_comment", businessType, 1)
				staticField.AppendComment([]*client.App{pbCommentApp})
				return nil
			})
		}
		// 结果等待
		_, err := eg.Wait()
		if err != nil {
			return err
		}
	}

	return nil
}

type BusinessBrandAdOperator struct {
}

func (rdop *BusinessBrandAdOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientAd.DataRes
	var baseData *types.FeedAdBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewBusinessBrandAd(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}
	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "NewBusinessBrandAd execute fail: %v", err)
		return err
	}
	return nil
}
