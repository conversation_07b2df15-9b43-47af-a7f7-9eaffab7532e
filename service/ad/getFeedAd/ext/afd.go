package ext

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"net/url"
	"reflect"
	"strconv"
	"strings"

	"github.com/golang/protobuf/proto"
	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/cmap"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientAd "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/ad/getFeedAd"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/tiebaplus"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/adsense"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/service/afdService"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/business/dsp"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/stlog"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	Recom1PlaceID = "1453093904902"
	Recom2PlaceID = "1453093728320"

	Frs1PlaceID = "0009"
	Frs2PlaceID = "0005"

	PbBanner1PlaceID = "1527488649315"
	PbBanner2PlaceID = "1527489263072"

	PbComment1PlaceID   = "1591063400228"
	PbComment2PlaceID   = "1591063400197"
	defaultForumNameKey = "defaultForumName"
)

type Afd struct {
	ctx *engine.Context

	ac              int
	placeID         string
	mpi             []map[string]interface{}
	clientType      int
	clientVersion   string
	userID          int64
	afdNeedParam    map[string]int
	placeIDMap      map[string]map[int]string
	pn              int
	osVersion       int
	screenWidth     int
	screenHeight    int
	screenDip       float64
	idfa            string
	intIP           uint32
	strIP           string
	isIPv6          bool
	intNetType      int
	strImei         string
	intImei         int
	cuid            string
	shoubaiCuid     string
	cuidGalaxy2     string
	baiduid         string
	caid            string
	androidID       string
	model           string
	userAgent       string
	cmode           string
	mac             string
	brand           string
	adExtParam      map[string]interface{}
	ArrSampleIDs    []string
	appTransmitData *client.AppTransmitData
}

func init() {
	err := engine.RegisterOperator("afd", func() engine.Job {
		return &AfdOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewAfd(ctx *engine.Context) *Afd {
	return &Afd{
		ctx: ctx,
	}
}

func (a *Afd) IsValid(ctx context.Context, baseData *types.FeedAdBaseData) bool {

	objRequest := baseData.BaseObj.ObjRequest

	if objRequest == nil {
		return false
	}

	if baseData.StaticField.AdNeedParam["asyncAfd"] == nil {
		return false
	}
	a.afdNeedParam = baseData.StaticField.AdNeedParam["asyncAfd"]

	tbcontext.TraceF(ctx, "a.afdNeedParam:%s", common.ToString(a.afdNeedParam))

	//验证一下相关参数是否符合要求
	a.userID = baseData.StaticField.UserID
	a.cuid = baseData.StaticField.Cuid
	a.clientType = baseData.StaticField.ClientType
	a.clientVersion = baseData.StaticField.ClientVersion
	a.osVersion = baseData.StaticField.OsVersion
	a.intIP = baseData.StaticField.IntIP
	a.intNetType = baseData.StaticField.IntNetType
	a.cuidGalaxy2 = baseData.StaticField.CuidGalaxy2
	a.model = baseData.StaticField.Model
	a.baiduid = baseData.StaticField.BaidUID
	a.caid = baseData.StaticField.Caid
	a.androidID = baseData.StaticField.AndroidID
	a.userAgent = baseData.StaticField.UserAgent
	a.cmode = baseData.StaticField.Cmode
	a.mac = baseData.StaticField.Mac
	a.screenWidth = baseData.StaticField.ScreenWidth
	a.screenHeight = baseData.StaticField.ScreenHeight
	a.screenDip = baseData.StaticField.ScreenDip
	a.idfa = baseData.StaticField.Idfa
	a.shoubaiCuid = baseData.StaticField.ShouBaiCuid
	a.strImei = baseData.StaticField.StrImei
	a.intImei = baseData.StaticField.IntImei
	a.appTransmitData = baseData.StaticField.AppTransmitData
	a.ArrSampleIDs = baseData.StaticField.ArrSampleIds

	strAdExtParamsRet := objRequest.GetPrivateAttr("ad_ext_params", "").(string)
	var arrAdExtParams map[string]interface{}
	if len(strAdExtParamsRet) != 0 {
		err := jsoniter.Unmarshal([]byte(strAdExtParamsRet), &arrAdExtParams)
		// 贴吧实时嗅探参数 例如：{"iadex":"123,12321"}
		if err == nil && len(arrAdExtParams) != 0 {
			a.adExtParam = arrAdExtParams
		} else {
			tbcontext.WarningF(ctx, "arrAdExtParams empty error:%v, strAdExtParamsRet:%s", err, strAdExtParamsRet)
		}
	}

	a.brand = baseData.StaticField.Brand
	a.placeIDMap = map[string]map[int]string{
		"recom": {
			1: Recom1PlaceID,
			2: Recom2PlaceID,
		},
		"frs": {
			1: Frs1PlaceID,
			2: Frs2PlaceID,
		},
		"pb_banner": {
			1: PbBanner1PlaceID,
			2: PbBanner2PlaceID,
		},
		"pb_comment": {
			1: PbComment1PlaceID,
			2: PbComment2PlaceID,
		},
	}

	a.placeID, a.mpi = a.getPlaceIdAndMpi()

	a.isIPv6 = false
	ipObj := common.CreateHttpIP(ctx, baseData.BaseObj.Req)
	if ipObj.GetIPIntVersion(a.intIP) == common.IP_V6 {
		a.isIPv6 = true
		a.strIP = strconv.Itoa(int(a.intIP))
	} else {
		a.strIP = common.Long2IP(a.intIP)
	}

	if afdIp, ok := baseData.BaseObj.Req.Cookie("AFD_IP"); ok {
		if strAfdIp := strings.TrimPrefix(afdIp.String(), "AFD_IP="); strAfdIp != "" {
			a.strIP = strAfdIp
		}
	}

	if !php2go.InArray(UbsAbtest.AFD_OAID_DEFAULT, a.ArrSampleIDs) && a.intImei == 0 {
		// imei实际为空时 端传过来的值为00000000000000  此时传空值给afd
		a.strImei = ""
	}
	if !baseData.StaticField.AdSet {
		return false
	}
	return true
}

func (a *Afd) Execute(ctx context.Context, outData *clientAd.DataRes, baseData *types.FeedAdBaseData) error {
	//组装入参，理论上拼好所有的广告位id可以一次rpc获取所有的afd物料
	arrAfdInput := a.getAfdInput(ctx, baseData)
	tbcontext.TraceF(ctx, "arrAfdInput:%s", common.ToString(arrAfdInput))

	if arrAfdInput != nil && len(arrAfdInput) > 0 {
		afdData, _ := dsp.GetAfdDspData(ctx, arrAfdInput)
		tbcontext.WarningF(ctx, "dsp.GetAfdDspData:%s", common.ToString(afdData))
		if baseData != nil {
			a.AddAdReqCnt(ctx, baseData.TmpData)
		}
		if afdData != nil {
			if len(afdData.Recom) > 0 && a.afdNeedParam["recom"] > 0 {
				a.processAfdDataItems(afdData.Recom, baseData.StaticField.AppendRecom, "recom")
				dsp.AddAdRespCnt(ctx, baseData.TmpData, "recom", "asyncAfd", len(afdData.GetRecom()))
			}
			if len(afdData.Frs) > 0 && a.afdNeedParam["frs"] > 0 {
				a.processAfdDataItems(afdData.Frs, baseData.StaticField.AppendFrs, "frs")
				dsp.AddAdRespCnt(ctx, baseData.TmpData, "frs", "asyncAfd", len(afdData.GetFrs()))
			}
			if len(afdData.PbBanner) > 0 && a.afdNeedParam["pb_banner"] > 0 {
				a.processAfdDataItems(afdData.PbBanner, baseData.StaticField.AppendBanner, "pb_banner")
				dsp.AddAdRespCnt(ctx, baseData.TmpData, "pb_banner", "asyncAfd", len(afdData.GetPbBanner()))
			}
			if len(afdData.PbComment) > 0 && a.afdNeedParam["pb_comment"] > 0 {
				a.processAfdDataItems(afdData.PbComment, baseData.StaticField.AppendComment, "pb_comment")
				dsp.AddAdRespCnt(ctx, baseData.TmpData, "pb_comment", "asyncAfd", len(afdData.GetPbComment()))
			}
		}

	}
	return nil
}

func (a *Afd) processAfdDataItems(afdData []*client.App, appendFunc func(appList []*client.App), page string) {
	for _, app := range afdData {
		app.BusinessType = proto.String("asyncAfd")
		app.PlatformType = proto.String("external")

		extCpm := float64(0)
		// 无物料信息 或者空物料标记
		if len(app.GoodsInfo) == 0 || app.GetGoodsInfo()[0].GetGoodsStyle() == 1001 {
			app.Price = proto.String("0")
		} else {
			legoCard := map[string]interface{}{}
			err := jsoniter.Unmarshal([]byte(app.GetGoodsInfo()[0].GetLegoCard()), &legoCard)
			if err == nil {
				adCommon := getInfoLegoCardAdCommon(legoCard["ad_common"])
				extInfoStr, ok := adCommon["ext_info"].(string)
				if ok {
					extInfo := baseDecrypt(extInfoStr)
					extCpm = common.Tvttt(extInfo["ad_cpm"], common.TTT_FLOAT64).(float64)
					extCpm = extCpm / 100000
					app.Price = proto.String(strconv.FormatFloat(extCpm, 'f', 2, 64))
				}
			}
		}

		app.LogParam = GetLogParams(a.cuid, page, *app.Price)

	}
	appendFunc(afdData)
}

func GetLogParams(cuid string, page, adPrice string) (logParam []*client.FeedKV) {
	manualOrder := &adsense.MgetDetailByOrderIds{}
	cryptData, _ := tiebaplus.GetOrderExt(manualOrder, cuid, 60)
	token := tiebaplus.GetConsumeToken(cuid)

	params := make(map[string]string)

	//params["ad_id"] = strconv.Itoa(m.ID)
	params["price"] = adPrice

	paramsStr, err := json.Marshal(params)
	if err != nil {
		paramsStr = []byte("{}")
	}

	feedkv := []*client.FeedKV{
		{
			Key:   proto.String("extra_param"),
			Value: proto.String(cryptData),
		},
		{
			Key:   proto.String("token"),
			Value: proto.String(token),
		},
		{
			Key:   proto.String("ad_show_type"),
			Value: proto.String("14"),
		},
		{
			Key:   proto.String("ad_click_type"),
			Value: proto.String("15"),
		},
		{
			Key:   proto.String("page"),
			Value: proto.String(page),
		},
		{
			Key:   proto.String("params"),
			Value: proto.String(string(paramsStr)),
		},
	}
	logParam = append(logParam, feedkv...)
	return logParam
}

func baseDecrypt(str string) map[string]interface{} {
	var res map[string]interface{}
	if str == "" {
		return res
	}

	if len(str) > 3 {
		if str[:3] != "AFD" {
			return res
		}
	}

	if len(str) > 4 {
		nstr := str[4:]
		ch := str[3:4]
		r := common.Tvttt(ch, common.TTT_INT).(int)%2 + 3
		newstr := convertStr(nstr, r)
		dstr, err := base64.StdEncoding.DecodeString(newstr)
		if err != nil {
			return res
		}
		err = json.Unmarshal(dstr, &res)
		if err != nil {
		}
	}
	return res

}

func convertStr(nstr string, r int) string {
	if len(nstr) == 0 || r <= 0 {
		return ""
	}
	l := len(nstr)
	n := l / r
	if l <= 0 {
		return ""
	}

	var str1, str2, str3 string
	str1 = nstr[l-n:]
	if l-n > n {
		str2 = nstr[n : l-n]
	} else {
		str2 = ""
	}
	str3 = nstr[:n]

	return str1 + str2 + str3
}

func getInfoLegoCardAdCommon(input interface{}) map[string]interface{} {
	res := make(map[string]interface{})
	if input == nil {
		return res
	}

	bts, err := jsoniter.Marshal(input)
	if err != nil {
		return res
	}
	err = jsoniter.Unmarshal(bts, &res)
	if err != nil {
		return res
	}
	return res
}

type AfdOperator struct {
}

func (rdop *AfdOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientAd.DataRes
	var baseData *types.FeedAdBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewAfd(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "afd execute fail: %v", err)
		return err
	}

	return nil
}
func (a *Afd) getAfdInput(ctx context.Context, baseData *types.FeedAdBaseData) map[string]interface{} {
	// 获取实验 eid
	arrEid := a.getExperimentEid(ctx, baseData)

	// 获取扩展信息
	ext := a.buildExt(ctx, baseData)

	// 构建请求参数
	adReq := a.buildAdReq(ctx, baseData, ext, arrEid)

	return adReq
}

// getExperimentEid 获取实验eid列表，如果存在通用eid则返回通用eid列表，否则返回空列表
func (a *Afd) getExperimentEid(ctx context.Context, baseData *types.FeedAdBaseData) []string {
	afsInstance := afdService.GetAfdServiceInstance(ctx)
	commonEid := afsInstance.GetCommonEidList(baseData.BaseObj.ObjRequest, afdService.FR_ASYNC)
	if len(commonEid) > 0 {
		return commonEid
	}
	return []string{}
}

// buildExt 构建广告的扩展字段，包括刷新类型、客户端IDFA、SDK版本、IMEI、媒体数量、是否请求广告等信息。
// 如果有品牌信息，则添加到扩展字段中。
// 默认的论坛名称为"TIEBA_SHOUYE_DEFAULT"，如果查询失败，则使用此默认值。
// 返回值：一个包含扩展字段信息的切片，每个元素都是一个映射，key为扩展字段名称，value为对应的值。
func (a *Afd) buildExt(ctx context.Context, baseData *types.FeedAdBaseData) []map[string]interface{} {
	ext := make([]map[string]interface{}, 0)
	freshType := "2"
	defaultForumName, err := wordserver.QueryKey(ctx, WordlistName, defaultForumNameKey)
	if err != nil {
		tbcontext.WarningF(ctx, "wordserver query fail: %v", err)
		defaultForumName = "TIEBA_SHOUYE_DEFAULT"
	}
	// 使用配置映射来替代多个条件判断
	ext = append(ext,
		map[string]interface{}{"k": "freshType", "v": freshType},
		map[string]interface{}{"k": "client_idfa", "v": a.idfa},
		map[string]interface{}{"k": "forum_sdk_version", "v": a.clientVersion},
		map[string]interface{}{"k": "forum_imei", "v": a.strImei},
		map[string]interface{}{"k": "mediaCount", "v": "5"},
		map[string]interface{}{"k": "is_req_ad", "v": "1"},
		map[string]interface{}{"k": "afdconf_ipv4", "v": a.strIP},
	)

	if a.brand != "" {
		ext = append(ext, map[string]interface{}{
			"k": "os_mafa",
			"v": a.brand,
		})
	}

	//TODO AFD会校验吧名，在AFD下掉这个逻辑之前先传 孙笑川
	ext = append(ext, map[string]interface{}{
		"k": "forum_name",
		"v": defaultForumName,
	})

	// 处理扩展参数
	ext = a.handleAdExtParam(ctx, baseData, ext)

	// 处理设备特定信息
	ext = a.handleDeviceSpecificInfo(ctx, baseData, ext)

	return ext
}

func (a *Afd) handleAdExtParam(ctx context.Context, baseData *types.FeedAdBaseData, ext []map[string]interface{}) []map[string]interface{} {
	if iadex, ok := a.adExtParam["iadex"]; ok {
		ext = append(ext, map[string]interface{}{"k": "iadex", "v": iadex})
	}
	if nadCoreVersion, ok := a.adExtParam["nad_core_version"]; ok {
		ext = append(ext, map[string]interface{}{"k": "nad_core_version", "v": nadCoreVersion})
	} else {
		ext = append(ext, map[string]interface{}{"k": "nad_core_version", "v": "********"})
	}
	ua := a.userAgent
	if php2go.InArray(UbsAbtest.AFD_ENCODED_UA_FIX_A, a.ArrSampleIDs) {
		ua = baseData.BaseObj.Req.HeaderDefault("User-Agent", "")
		stlog.AddLog(ctx, "encoded_ua_new_reverse", "asyn_1")
	} else {
		stlog.AddLog(ctx, "encoded_ua_new_reverse", "asyn_0")
	}
	ext = append(ext, map[string]interface{}{
		"k": "encoded_ua_new",
		"v": url.QueryEscape(ua),
	})
	return ext
}

func (a *Afd) handleDeviceSpecificInfo(ctx context.Context, baseData *types.FeedAdBaseData, ext []map[string]interface{}) []map[string]interface{} {
	if a.clientType == clientvers.CLIENT_TYPE_IPHONE {
		ext = a.handleCaid(ctx, ext)
	}

	if a.cmode != "" {
		cmodInt, ok := common.Tvttt(a.cmode, common.TTT_INT).(int)
		if ok {
			ext = append(ext, map[string]interface{}{"k": "cmode", "v": cmodInt})
		}
	}

	if php2go.InArray("afd_ext_param_mac_a", a.ArrSampleIDs) && a.clientType == clientvers.CLIENT_TYPE_ANDROID {
		ext = append(ext, map[string]interface{}{"k": "mac", "v": a.mac})
	}

	// 获得墨卡托数据
	mercatorData := afdService.GetMercatorData(ctx, baseData.BaseObj.ObjRequest, "asyn")
	gps, gpsOk := mercatorData["gps"]
	wgtag, wgtagOk := mercatorData["wgtag"]
	if gpsOk && wgtagOk {
		ext = append(ext, map[string]interface{}{
			"k": "gps",
			"v": gps,
		}, map[string]interface{}{
			"k": "wgtag",
			"v": wgtag,
		})
	}
	return ext
}

func (a *Afd) handleCaid(ctx context.Context, ext []map[string]interface{}) []map[string]interface{} {
	var arrCaid map[string]interface{}
	urlCaid, err := url.QueryUnescape(a.caid)
	if err != nil || urlCaid == "" {
		tbcontext.WarningF(ctx, "aid decode error! caid:[%s], err: %v", a.caid, err)
		return ext
	}

	err = jsoniter.Unmarshal([]byte(urlCaid), &arrCaid)
	if err != nil || len(arrCaid) == 0 {
		tbcontext.WarningF(ctx, "aid decode error! decoded caid: [%+v], err: %v", arrCaid, err)
		return ext
	}

	jsonCaid, err := jsoniter.Marshal(arrCaid["caid"])
	if err == nil {
		ext = append(ext, map[string]interface{}{"k": "caid", "v": string(jsonCaid)})
	}
	ext = append(ext, map[string]interface{}{
		"k": "factors_data",
		"v": arrCaid["factors_data"],
	}, map[string]interface{}{
		"k": "caid_valid",
		"v": arrCaid["caid_valid"],
	})

	if _, ok := arrCaid["factors_data"]; ok && !php2go.InArray(UbsAbtest.AFD_UA_FIX_REVERSE, a.ArrSampleIDs) {
		factorsDataStr, _ := arrCaid["factors_data"].(string)
		if factorsDataStr == "" {
			return ext
		}
		factorsData := make(map[string]interface{})
		factorsDataByte, err := base64.StdEncoding.DecodeString(arrCaid["factors_data"].(string))
		if err != nil {
			return ext
		}
		err = jsoniter.Unmarshal(factorsDataByte, &factorsData)
		if err != nil {
			return ext
		}
		if _, ok := factorsData["factorsForCaid"]; ok {
			if forCaid, ok := factorsData["factorsForCaid"].(map[string]interface{}); ok {
				ext = append(ext, map[string]interface{}{
					"k": "boot_sec_time",
					"v": forCaid["bootSecTime"],
				}, map[string]interface{}{
					"k": "sys_file_time",
					"v": forCaid["sysFileTime"],
				})
				if mobBirthTimeVal, exist := forCaid["mobBirthTime"]; exist && php2go.InArray("pdd_mob_birth_time", a.ArrSampleIDs) {
					ext = append(ext, map[string]interface{}{
						"k": "mob_birth_time",
						"v": mobBirthTimeVal,
					})
				}
			}
		}
	}
	return ext
}

func (a *Afd) buildAdReq(ctx context.Context, baseData *types.FeedAdBaseData, ext []map[string]interface{}, arrEid []string) map[string]interface{} {
	extByte, err := jsoniter.Marshal(ext)
	if err != nil {
		tbcontext.WarningF(ctx, "ext json fail: %v", err)
	}
	extStr := string(extByte)

	idc := baseData.BaseObj.Req.HeaderDefault("X_BD_IDC", env.IDC())

	adReq := map[string]interface{}{
		"pid":          a.placeID,
		"ac":           "1",
		"pn":           "1",
		"ver":          a.clientVersion,
		"uid":          a.userID,
		"bdid":         a.baiduid,
		"cuid":         a.cuid,
		"shoubai_cuid": a.shoubaiCuid,
		"cuid_galaxy2": a.cuidGalaxy2,
		"mod":          a.model,
		"ot":           strconv.Itoa(a.clientType),
		"ov":           a.osVersion,
		"nt":           a.intNetType,
		"ct":           a.clientType,
		"sw":           a.screenWidth,
		"sh":           a.screenHeight,
		"sd":           a.screenDip,
		"imei":         a.strImei,
		"idfa":         a.idfa,
		"ua":           a.userAgent,
		"idc":          idc,
		"refer":        baseData.BaseObj.Req.HeaderDefault("Referer", ""),
		"fmt":          "json",
		"ft":           "2",
		"st":           2,
		"ip":           a.strIP,
		"apna":         "com.baidu.tieba",
		"ext":          extStr,
	}

	if !php2go.InArray(UbsAbtest.AFD_OAID_DEFAULT, a.ArrSampleIDs) {
		adReq["android_id"] = a.androidID
	}

	if php2go.InArray(UbsAbtest.AFD_UA_FIX_REVERSE, a.ArrSampleIDs) {
		adReq["ua"] = baseData.BaseObj.Req.HeaderDefault("User-Agent", "")
	}
	if php2go.InArray(UbsAbtest.AFD_BAIDUID_REVERSE, a.ArrSampleIDs) {
		adReq["bdid"] = a.baiduid
	}

	adReq["ac"] = a.ac
	if len(a.mpi) > 0 {
		mpiByte, _ := json.Marshal(a.mpi)
		adReq["mpi"] = string(mpiByte)
	}

	if a.isIPv6 {
		adReq["ipv6"] = a.strIP
		adReq["ip"] = ""
	}

	strEid := ""
	if len(arrEid) > 0 {
		strEid = strings.Join(arrEid, ",")
	}

	adReq["extra"] = []map[string]interface{}{
		{"k": "eid", "v": strEid},
	}
	adReq["eid"] = strEid

	stlog.AddLog(ctx, "afd_eid", strEid)
	stlog.AddLog(ctx, "afd_place_id", a.placeID)

	return adReq
}

// getPlaceIdAndMpi 拼接所有的广告位id和请求的数量
func (a *Afd) getPlaceIdAndMpi() (string, []map[string]interface{}) {
	mpi := make([]map[string]interface{}, 0)
	var placeIdArr []string
	//AFD对pid和mpi的拼接顺序有要求，所以需要对AfdNeedParam中的页面先按照特定顺序进行排序，再按照排序顺序遍历..
	posList := a.getSortedPosList()
	for _, pos := range posList {
		num, ok := a.afdNeedParam[pos]
		if !ok {
			continue
		}
		if num <= 0 {
			continue
		}
		placeIdTmp := a.placeIDMap[pos][a.clientType]
		placeIdArr = append(placeIdArr, placeIdTmp)

		mpi = append(mpi, map[string]interface{}{
			"pid": placeIdTmp,
			"ac":  num,
		})
		a.ac += num
	}
	return strings.Join(placeIdArr, "_"), mpi
}

// getSortedPosList 根据afdNeeParam返回特定顺序的页面
func (a *Afd) getSortedPosList() []string {
	// 定义优先顺序
	order := []string{"pb_banner", "pb_comment"}
	// 用于记录已添加的 key
	seen := make(map[string]bool)
	var sortedPlaceList []string
	// 添加固定顺序 key
	for _, key := range order {
		if _, ok := a.afdNeedParam[key]; ok {
			sortedPlaceList = append(sortedPlaceList, key)
			seen[key] = true
		}
	}
	// 添加其他 key（未在固定顺序中出现的）
	for key := range a.afdNeedParam {
		if !seen[key] {
			sortedPlaceList = append(sortedPlaceList, key)
		}
	}
	return sortedPlaceList
}

// AddAdReqCnt 根据不同的广告位记录不同广告的请求数量..
func (a *Afd) AddAdReqCnt(ctx context.Context, tmpData cmap.ConcurrentMap) {
	if a.mpi == nil {
		return
	}

	for _, tmpMpi := range a.mpi {
		pos := ""
		ac := 0

		//根据placeId找到对应的pos是啥..
		if pidInf, exist := tmpMpi["pid"]; exist {
			if pidStr, ok := pidInf.(string); ok {
				for posName, appType2PidMap := range a.placeIDMap {
					if pos != "" {
						break
					}
					for _, pid := range appType2PidMap {
						if pid == pidStr {
							pos = posName
							break
						}
					}
				}
			} else {
				tbcontext.WarningF(ctx, "[PidParser] Type assertion failed for 'pid' | expected=string, actual=%T, value=%+v", pidStr, pidStr)
			}
		} else {
			tbcontext.WarningF(ctx, "[PidParser] Missing key 'pid' in arrAfdInput | availableKeys=%v", reflect.ValueOf(tmpMpi).MapKeys())
		}

		//广告请求的数量
		if acInf, exist := tmpMpi["ac"]; exist {
			if intAc, ok := acInf.(int); ok {
				ac = intAc
			} else {
				tbcontext.WarningF(ctx, "[ACParser] Type assertion failed for 'ac' | expected=int, actual=%T, value=%+v", acInf, acInf)
			}
		} else {
			tbcontext.WarningF(ctx, "[ACParser] Missing key 'ac' in arrAfdInput | availableKeys=%v", reflect.ValueOf(tmpMpi).MapKeys())
		}

		if pos == "" {
			continue
		}

		dsp.AddAdReqCnt(ctx, tmpData, pos, "asyncAfd", ac)
	}
}
