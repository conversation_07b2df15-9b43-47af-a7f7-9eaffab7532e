package ext

import (
	"context"
	"errors"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/business/dsp/pangolin"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	clientAd "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/ad/getFeedAd"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	ResourceAPICsj    = "apiCsj"
	businessAPICsj    = "businessApiCsj"
	BusinessAPICsjKey = "12_78_api_csj_pb"

	CsjDaTypeExposure = 20
	CsjDaTypeClick    = 21

	WordlistName = "tb_wordlist_redis_Business_Bidding_Config"
	ShowConfKey  = "adx_ad_show_conf"
)

type BusinessApiCsj struct {
	ctx *engine.Context
}

// init 函数用于在程序启动时执行一些初始化操作。
func init() {
	err := engine.RegisterOperator(businessAPICsj, func() engine.Job {
		return &BusinessApiCsjOperator{}
	})
	if err != nil {
		panic(err)
	}
}

// NewBusinessApiCsj 函数用于创建一个新的 BusinessApiCsj 实例。
func NewBusinessApiCsj(ctx *engine.Context) *BusinessApiCsj {
	return &BusinessApiCsj{
		ctx: ctx,
	}
}

// IsValid 函数用于验证业务逻辑是否有效。
func (b *BusinessApiCsj) IsValid(ctx context.Context, baseData *types.FeedAdBaseData) bool {
	if nil == b || nil == ctx || nil == baseData || nil == baseData.StaticField {
		return false
	}
	staticField := baseData.StaticField
	clientVersion := staticField.ClientVersion
	if clientvers.Compare("12.78.0", clientVersion) < 0 {
		return false
	}

	// arrSampleIds := staticField.ArrSampleIds
	// if !php2go.InArray(BusinessAPICsjKey, arrSampleIds) {
	// 	return false
	// }

	// 校验免广告
	if !staticField.AdSet {
		return false
	}
	return true
}

// Execute 函数用于处理并展示商业广告。
func (b *BusinessApiCsj) Execute(ctx context.Context, outData *clientAd.DataRes, baseData *types.FeedAdBaseData) error {
	if nil == b || nil == ctx || nil == baseData || nil == baseData.BaseObj || nil == baseData.BaseObj.ObjRequest ||
		nil == outData || nil == baseData.StaticField {
		return errors.New("input params invalid")
	}
	staticField := baseData.StaticField

	businessType := "asyncCsj"
	adShowTypeVal := "14"
	adClickTypeVal := "15"
	platformType := "external"
	limitConf := &pangolin.LimitConf{
		AdxSource: nil,
		KeySuffix: "frequency_apiCsj",
	}
	csjNeedparam := staticField.AdNeedParam["asyncCsj"]
	if csjNeedparam != nil {
		eg := new(gtask.Group)
		// 穿山甲pb_banner需求数>0
		if csjNeedparam["pb_banner"] > 0 {
			eg.Go(func() error {
				c := pangolin.NewCsjDsp(ctx, resource.RedisAdsense, baseData.BaseObj.ObjRequest, limitConf,
					pangolin.WithNewParam(resource.RedisUserGrowth), pangolin.WithAdCount(csjNeedparam["pb_banner"]),
					pangolin.WithTimeoutMs(800),
					pangolin.WithTmpData(baseData.TmpData))
				pbBannerApp, err := c.GetCsjDsp("pb_banner")
				defer c.Close()
				if err != nil {
					tbcontext.WarningF(ctx, "apiCsjFailed:%v", err.Error())
					return nil
				}
				for _, p := range pbBannerApp {
					p.BusinessType = &businessType
					for _, kv := range p.LogParam {
						if *kv.Key == "ad_show_type" {
							kv.Value = &adShowTypeVal
						}
						if *kv.Key == "ad_click_type" {
							kv.Value = &adClickTypeVal
						}
					}
					p.PlatformType = &platformType
					for _, goodsInfo := range p.GoodsInfo {
						legoCard := map[string]interface{}{}
						err := jsoniter.Unmarshal([]byte(goodsInfo.GetLegoCard()), &legoCard)
						if err == nil {
							legoCard["source"] = "asyncCsj"
						}
						bytesLegoCard, err := jsoniter.Marshal(legoCard)
						strLegoCard := string(bytesLegoCard)
						if err == nil {
							goodsInfo.LegoCard = &strLegoCard
						}
					}
				}
				baseData.StaticField.AppendBanner(pbBannerApp)
				return nil
			})
		}
		// 穿山甲pb_comment需求数>0
		if csjNeedparam["pb_comment"] > 0 {
			eg.Go(func() error {
				c := pangolin.NewCsjDsp(ctx, resource.RedisAdsense, baseData.BaseObj.ObjRequest, limitConf,
					pangolin.WithNewParam(resource.RedisUserGrowth), pangolin.WithAdCount(csjNeedparam["pb_comment"]),
					pangolin.WithTimeoutMs(800),
					pangolin.WithTmpData(baseData.TmpData))
				pbCommentApp, err := c.GetCsjDsp("pb_comment")
				defer c.Close()
				if err != nil {
					tbcontext.WarningF(ctx, "apiCsjFailed:%v", err.Error())
					return nil
				}
				for _, p := range pbCommentApp {
					p.BusinessType = &businessType
					for _, kv := range p.LogParam {
						if *kv.Key == "ad_show_type" {
							kv.Value = &adShowTypeVal
						}
						if *kv.Key == "ad_click_type" {
							kv.Value = &adClickTypeVal
						}
					}
					p.PlatformType = &platformType
					for _, goodsInfo := range p.GoodsInfo {
						legoCard := map[string]interface{}{}
						err := jsoniter.Unmarshal([]byte(goodsInfo.GetLegoCard()), &legoCard)
						if err == nil {
							legoCard["source"] = "asyncCsj"
						}
						bytesLegoCard, err := jsoniter.Marshal(legoCard)
						strLegoCard := string(bytesLegoCard)
						if err == nil {
							goodsInfo.LegoCard = &strLegoCard
						}
					}
				}
				baseData.StaticField.AppendComment(pbCommentApp)
				return nil
			})
		}
		// 结果等待
		_, err := eg.Wait()
		if err != nil {
			return err
		}
	}

	return nil
}

type BusinessApiCsjOperator struct {
}

func (rdop *BusinessApiCsjOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientAd.DataRes
	var baseData *types.FeedAdBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	csj := NewBusinessApiCsj(ctx)
	if !csj.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := csj.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "test execute fail: %v", err)
		return err
	}

	return nil
}
