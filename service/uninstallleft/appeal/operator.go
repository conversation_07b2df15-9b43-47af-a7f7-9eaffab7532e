package appeal

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
)

// OperatorAppeal 申诉功能位算子
type OperatorAppeal struct {
	CommonParams commonparams.CommonParams `inject:"canLost=false,canNil=false"`
	Appeal       `extract:"canLost=true,canNil=true"`
}

func (op *OperatorAppeal) DoImpl(ctx *engine.Context) error {
	processor := NewProcessor()
	processor.CommonParams = op.CommonParams

	if err := processor.Process(ctx.CallerCtx()); err != nil {
		return err
	}

	op.Appeal = processor
	return nil
}
