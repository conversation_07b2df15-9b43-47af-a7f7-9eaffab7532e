// Package appeal 账号误封申诉功能位
// 处理申诉功能的基础配置和业务状态判断
package appeal

import (
	"context"
	"errors"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
)

// opName 算子名称
const opName = "appeal"

const (
	wordlistTable      = "tb_wordlist_redis_uninstall_left_conf"
	appealBasicInfoKey = "appeal_basic_info" // 申诉功能位配置
)

// 默认配置 - 用于词表失效时兜底
var defaultBasicInfo = &BasicInfo{
	Icon:       "https://tieba-ares.cdn.bcebos.com/mis/2025-8/1754379560180/ec7fcd1c2aa9.png",
	IconNight:  "https://tieba-ares.cdn.bcebos.com/mis/2025-8/1754379561694/d5ff6767091b.png",
	Title:      "账号误封申诉",
	Abstract:   "提交申诉，24小时内反馈结果！",
	ButtonText: "去反馈",
	TargetScheme: "tiebaapp://router/portal?params=%7B%22page%22%3A%22h5%2FopenWebView%22%2C%22pageParams%22%3A%7B%22url%22%3A%22" +
		"https%3A%2F%2Ftiebac.baidu.com%2Fmo%2Fq%2Fuserappeal%3Fsource%3Duninstall%26nomenu%3D1%26noshare%3D1%22%7D%7D",
	ErrorToast:  "你已使用过加急通道，请通过【我的-服务中心】提交问题",
	IsNeedLogin: true, // 功能位3默认需要登录
}

// Processor 申诉功能位处理器
type Processor struct {
	CommonParams commonparams.CommonParams

	basicInfo *BasicInfo // 申诉功能位基础配置

	// 业务状态
	shouldShow      bool // 是否显示申诉功能位
	hasRecentAppeal bool // 是否有近期申诉记录
}

func NewProcessor() *Processor {
	return &Processor{
		shouldShow:      false,
		hasRecentAppeal: false,
	}
}

func (p *Processor) Process(ctx context.Context) error {
	// 加载申诉功能位配置
	if err := p.loadConfigs(ctx); err != nil {
		return err
	}

	// 处理申诉业务逻辑
	if err := p.processBusinessLogic(ctx); err != nil {
		return err
	}

	return nil
}

// processBusinessLogic 处理申诉业务逻辑
func (p *Processor) processBusinessLogic(ctx context.Context) error {
	// TODO: 实现申诉相关业务逻辑

	// 当前固定显示申诉功能位
	p.shouldShow = true
	p.hasRecentAppeal = false

	return nil
}

func (p *Processor) ShouldShow() bool {
	return p.shouldShow
}

func (p *Processor) GetBasicInfo() *BasicInfo {
	if p.basicInfo != nil {
		return p.basicInfo
	}
	return defaultBasicInfo
}

func (p *Processor) HasRecentAppeal() bool {
	return p.hasRecentAppeal
}

// loadConfigs 加载申诉功能位配置
func (p *Processor) loadConfigs(ctx context.Context) error {
	keys := []string{appealBasicInfoKey}

	configData, err := wordserver.QueryItems(ctx, wordlistTable, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "query appeal config fail, table=%s, keys=%v, err=%v",
			wordlistTable, keys, err)
		return errors.New("failed to query appeal config")
	}

	if err := p.parseAppealBasicInfo(ctx, configData[appealBasicInfoKey]); err != nil {
		return err
	}

	return nil
}

// parseAppealBasicInfo 解析申诉功能位配置
func (p *Processor) parseAppealBasicInfo(ctx context.Context, configStr string) error {
	if configStr == "" {
		tbcontext.WarningF(ctx, "appeal basic info config is empty, use default")
		p.basicInfo = defaultBasicInfo
		return nil
	}

	var config BasicInfo
	if err := jsoniter.Unmarshal([]byte(configStr), &config); err != nil {
		tbcontext.WarningF(ctx, "unmarshal appeal basic info fail, config=%s, err=%v, use default",
			configStr, err)
		p.basicInfo = defaultBasicInfo
		return nil
	}

	p.basicInfo = &config
	return nil
}
