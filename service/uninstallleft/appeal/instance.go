package appeal

// BasicInfo 申诉功能位基础信息
type BasicInfo struct {
	Icon         string `json:"icon"`
	IconNight    string `json:"icon_night"`
	Title        string `json:"title"`
	Abstract     string `json:"abstract"`
	ButtonText   string `json:"button_text"`
	TargetScheme string `json:"target_scheme"`
	ErrorToast   string `json:"error_toast"` // 阻止跳转时的提示文案
	IsNeedLogin  bool   `json:"is_need_login"`
}

// Appeal 申诉功能位对外接口
type Appeal interface {
	ShouldShow() bool
	GetBasicInfo() *BasicInfo
	HasRecentAppeal() bool
}
