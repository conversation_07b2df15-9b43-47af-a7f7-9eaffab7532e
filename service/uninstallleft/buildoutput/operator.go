package buildoutput

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/uninstallleft/ad"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/uninstallleft/appeal"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/uninstallleft/commonconfig"
)

// OperatorBuildOutput 构建输出算子定义
type OperatorBuildOutput struct {
	CommonParams commonparams.CommonParams `inject:"canLost=false,canNil=false"`
	Ad           ad.Ad                     `inject:"canLost=true,canNil=true"`
	Appeal       appeal.Appeal             `inject:"canLost=true,canNil=true"`
	CommonConfig commonconfig.CommonConfig `inject:"canLost=true,canNil=true"`
	BuildOutput  `extract:"canLost=false,canNil=false"`
}

// DoImpl 算子入口
func (op *OperatorBuildOutput) DoImpl(ctx *engine.Context) error {
	processor := NewProcessor()
	processor.CommonParams = op.CommonParams
	processor.Ad = op.Ad
	processor.Appeal = op.Appeal
	processor.CommonConfig = op.CommonConfig

	err := processor.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	// 设置extract接口
	op.BuildOutput = processor
	return nil
}
