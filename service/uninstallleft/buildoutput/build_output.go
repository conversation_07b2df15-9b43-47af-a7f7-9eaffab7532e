// Package buildoutput 卸载挽留构建输出算子
// 组装各功能位算子结果为最终接口返回数据
package buildoutput

import (
	"context"
	"strconv"

	"google.golang.org/protobuf/proto"
	uninstallLeftProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/uninstallLeftInfo"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/uninstallleft/ad"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/uninstallleft/appeal"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/uninstallleft/commonconfig"
)

// opName 算子名称
const opName = "build_output"

// Processor 构建输出处理器
type Processor struct {
	CommonParams commonparams.CommonParams
	Ad           ad.Ad
	Appeal       appeal.Appeal
	CommonConfig commonconfig.CommonConfig

	output *uninstallLeftProto.UninstallLeftInfo
}

func NewProcessor() *Processor {
	return &Processor{}
}

func (p *Processor) Process(ctx context.Context) error {
	p.output = &uninstallLeftProto.UninstallLeftInfo{
		List: []*uninstallLeftProto.UninstallLeftInfoItem{},
	}

	// 构建公共配置数据
	p.buildCommonConfigData(ctx)

	// 按顺序构建功能位数据
	p.buildFunctionItems(ctx)

	return nil
}

// buildFunctionItems 按顺序构建功能位：内存不足 -> 广告体验 -> 账号申诉 -> 其他问题
func (p *Processor) buildFunctionItems(ctx context.Context) {
	p.buildMemoryFunctionItem(ctx) // 功能位1：内存不足
	p.buildAdFunctionItem(ctx)     // 功能位2：广告体验
	p.buildAppealFunctionItem(ctx) // 功能位3：账号申诉
	p.buildHelpFunctionItem(ctx)   // 功能位4：其他问题
}

func (p *Processor) buildMemoryFunctionItem(ctx context.Context) {
	if p.output == nil || p.CommonConfig == nil {
		return
	}

	memoryInfo := p.CommonConfig.GetMemoryFunctionInfo()
	if memoryInfo == nil {
		return
	}

	item := &uninstallLeftProto.UninstallLeftInfoItem{
		Type:         proto.String("memory"),
		Icon:         proto.String(memoryInfo.Icon),
		IconNight:    proto.String(memoryInfo.IconNight),
		Title:        proto.String(memoryInfo.Title),
		Abstract:     proto.String(memoryInfo.Abstract),
		ButtonText:   proto.String(memoryInfo.ButtonText),
		TargetScheme: proto.String(memoryInfo.TargetScheme),
		IsNeedLogin:  proto.Bool(memoryInfo.IsNeedLogin),
	}

	// 内存清理功能位：通常不需要error_toast，如果有特殊情况可以在这里添加逻辑
	if memoryInfo.ErrorToast != "" {
		item.ErrorToast = proto.String(memoryInfo.ErrorToast)
	}

	p.output.List = append(p.output.List, item)
}

// buildAdFunctionItem 构建功能位2：广告影响体验
func (p *Processor) buildAdFunctionItem(ctx context.Context) {
	if p.output == nil || p.Ad == nil {
		return
	}

	adBasicInfo := p.Ad.GetBasicInfo()
	if adBasicInfo == nil {
		return
	}

	// 构建基础功能位信息
	adItem := &uninstallLeftProto.UninstallLeftInfoItem{
		Type:         proto.String("ad"),
		Icon:         proto.String(adBasicInfo.Icon),
		IconNight:    proto.String(adBasicInfo.IconNight),
		Title:        proto.String(adBasicInfo.Title),
		Abstract:     proto.String(adBasicInfo.Abstract),
		ButtonText:   proto.String(adBasicInfo.ButtonText),
		TargetScheme: proto.String(adBasicInfo.TargetScheme),
		IsNeedLogin:  proto.Bool(adBasicInfo.IsNeedLogin),
	}

	// 只有在不符合兑换条件时才返回error_toast
	if !p.Ad.IsQualified() {
		adItem.ErrorToast = proto.String(adBasicInfo.ErrorToast)
	}

	// 构建广告功能位特有的数据
	adItem.Data = p.buildAdData(ctx)

	p.output.List = append(p.output.List, adItem)
}

// buildAdData 构建广告功能位的特有数据
func (p *Processor) buildAdData(ctx context.Context) *uninstallLeftProto.UninstallAdData {
	// 构建弹窗信息
	popInfo := &uninstallLeftProto.PopInfo{
		Type:       proto.String(strconv.Itoa(types.DialogTypePageAppUninstall)),
		Token:      proto.String(p.Ad.GetToken()),
		GoodsId:    proto.Uint64(p.Ad.GetGoodsID()),
		LeftTmoney: proto.Uint32(p.Ad.GetLeftTmoney()),
	}

	// 构建错误提示信息（防刷提示）
	errorInfo := &uninstallLeftProto.ErrorInfo{
		Title:    proto.String(p.Ad.GetDialogTitle()),
		Abstract: proto.String(p.Ad.GetDialogAbstract()),
		Url:      proto.String(p.Ad.GetDialogURL()),
	}

	return &uninstallLeftProto.UninstallAdData{
		PopInfo:   popInfo,
		ErrorInfo: errorInfo,
		Qualified: proto.Bool(p.Ad.IsQualified()),
	}
}

// buildAppealFunctionItem 构建功能位3：账号申诉
func (p *Processor) buildAppealFunctionItem(ctx context.Context) {
	if p.output == nil || p.Appeal == nil {
		return
	}

	appealBasicInfo := p.Appeal.GetBasicInfo()
	if appealBasicInfo == nil {
		return
	}

	item := &uninstallLeftProto.UninstallLeftInfoItem{
		Type:         proto.String("appeal"),
		Icon:         proto.String(appealBasicInfo.Icon),
		IconNight:    proto.String(appealBasicInfo.IconNight),
		Title:        proto.String(appealBasicInfo.Title),
		Abstract:     proto.String(appealBasicInfo.Abstract),
		ButtonText:   proto.String(appealBasicInfo.ButtonText),
		TargetScheme: proto.String(appealBasicInfo.TargetScheme),
		IsNeedLogin:  proto.Bool(appealBasicInfo.IsNeedLogin),
	}

	// 申诉功能位：如果有近期申诉记录则返回error_toast
	if p.Appeal.HasRecentAppeal() {
		item.ErrorToast = proto.String(appealBasicInfo.ErrorToast)
	}

	p.output.List = append(p.output.List, item)
	tbcontext.TraceF(ctx, "appeal function item built")
}

// buildHelpFunctionItem 构建功能位4：其他问题帮助
func (p *Processor) buildHelpFunctionItem(ctx context.Context) {
	if p.output == nil || p.CommonConfig == nil {
		return
	}

	helpInfo := p.CommonConfig.GetHelpFunctionInfo()
	if helpInfo == nil {
		return
	}

	item := &uninstallLeftProto.UninstallLeftInfoItem{
		Type:         proto.String("other"),
		Icon:         proto.String(helpInfo.Icon),
		IconNight:    proto.String(helpInfo.IconNight),
		Title:        proto.String(helpInfo.Title),
		Abstract:     proto.String(helpInfo.Abstract),
		ButtonText:   proto.String(helpInfo.ButtonText),
		TargetScheme: proto.String(helpInfo.TargetScheme),
		ErrorToast:   proto.String(helpInfo.ErrorToast),
		IsNeedLogin:  proto.Bool(helpInfo.IsNeedLogin),
	}

	p.output.List = append(p.output.List, item)
	tbcontext.TraceF(ctx, "help function item built")
}

// GetOutput 获取构建的输出数据
// 返回最终组装好的接口返回数据
func (p *Processor) GetOutput() *uninstallLeftProto.UninstallLeftInfo {
	return p.output
}

// buildCommonConfigData 构建公共配置数据
// 包括主页面信息、弹窗配置等全局配置
func (p *Processor) buildCommonConfigData(ctx context.Context) {
	if p.output == nil || p.CommonConfig == nil {
		return
	}

	// 构建主页面信息
	p.buildMainPageInfo(ctx)

	// 构建继续卸载弹窗信息
	p.buildUninstallPopInfo(ctx)

	// 构建未登录弹窗信息
	p.buildNoLoginPopInfo(ctx)
}

// buildMainPageInfo 构建主页面信息
func (p *Processor) buildMainPageInfo(ctx context.Context) {
	if p.output == nil {
		return
	}

	mainPageInfo := p.CommonConfig.GetMainPageInfo()
	if mainPageInfo == nil {
		return
	}

	p.output.MainPage = &uninstallLeftProto.MainPageInfo{
		Title:    proto.String(mainPageInfo.Title),
		Abstract: proto.String(mainPageInfo.Abstract),
	}
}

// buildUninstallPopInfo 构建继续卸载弹窗信息
func (p *Processor) buildUninstallPopInfo(ctx context.Context) {
	if p.output == nil {
		return
	}

	uninstallPopConfig := p.CommonConfig.GetUninstallPopConfig()
	if uninstallPopConfig == nil {
		return
	}

	// 构建弹窗项列表
	popItems := make([]*uninstallLeftProto.UninstallPopItem, 0, len(uninstallPopConfig.List))
	for _, item := range uninstallPopConfig.List {
		popItems = append(popItems, &uninstallLeftProto.UninstallPopItem{
			Icon:         proto.String(item.Icon),
			Title:        proto.String(item.Title),
			Abstract:     proto.String(item.Abstract),
			ButtonText:   proto.String(item.ButtonText),
			TargetScheme: proto.String(item.TargetScheme),
		})
	}

	p.output.UninstallPop = &uninstallLeftProto.UninstallPopInfo{
		Title:    proto.String(uninstallPopConfig.Title),
		Abstract: proto.String(uninstallPopConfig.Abstract),
		List:     popItems,
	}
}

// buildNoLoginPopInfo 构建未登录弹窗信息
func (p *Processor) buildNoLoginPopInfo(ctx context.Context) {
	if p.output == nil {
		return
	}

	noLoginPopConfig := p.CommonConfig.GetNoLoginPopConfig()
	if noLoginPopConfig == nil {
		return
	}

	p.output.NologinPop = &uninstallLeftProto.NoLoginPopItem{
		Title: proto.String(noLoginPopConfig.Title),
		Desc:  proto.String(noLoginPopConfig.Desc),
	}
}
