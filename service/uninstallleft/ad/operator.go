package ad

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
)

// OperatorAd 广告功能位算子
type OperatorAd struct {
	CommonParams commonparams.CommonParams `inject:"canLost=false,canNil=false"`
	Ad           `extract:"canLost=true,canNil=true"`
}

func (op *OperatorAd) DoImpl(ctx *engine.Context) error {
	processor := NewProcessor()
	processor.CommonParams = op.CommonParams

	if err := processor.Process(ctx.CallerCtx()); err != nil {
		return err
	}

	op.Ad = processor
	return nil
}
