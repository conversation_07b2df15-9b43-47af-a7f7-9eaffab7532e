// Package ad 广告影响体验功能位
// 处理SVIP兑换相关的业务逻辑，包括防刷、商品匹配等
package ad

import (
	"context"
	"errors"
	"fmt"
	"time"

	jsoniter "github.com/json-iterator/go"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// opName 算子名称
const opName = "ad"

const (
	wordlistTable     = "tb_wordlist_redis_uninstall_left_conf"
	adBasicInfoKey    = "ad_basic_info"
	adGoodsMappingKey = "ad_goods_mapping"
	adConfigKey       = "ad_config"
)

// 默认配置 - 用于词表失效时兜底
var (
	defaultBasicInfo = &BasicInfo{
		Icon:         "https://tieba-ares.cdn.bcebos.com/mis/2025-8/1754379559800/79d710cd043c.png",
		IconNight:    "https://tieba-ares.cdn.bcebos.com/mis/2025-8/1754379561310/aaefd37e642a.png",
		Title:        "广告影响浏览体验",
		Abstract:     "立即兑换SVIP，享免广告特权！",
		ButtonText:   "立即兑换",
		TargetScheme: "",
		ErrorToast:   "你已领取过体验福利",
		IsNeedLogin:  true, // 功能位2默认需要登录
	}

	defaultConfig = &Config{
		AntiSpamTmoneyThreshold: 1000,
		AntiSpamDays:            30,
		AntiSpamTitle:           "兑换失败，请稍后再试",
		AntiSpamAbstract:        "",
		AntiSpamURL:             "",
	}

	defaultGoodsMapping = &GoodsMapping{
		GoodsList: []*GoodsMappingItem{
			{MinTmoney: 0, MaxTmoney: 0, GoodsID: 582},
			{MinTmoney: 1, MaxTmoney: 49, GoodsID: 606},
			{MinTmoney: 50, MaxTmoney: 99, GoodsID: 592},
			{MinTmoney: 100, MaxTmoney: 499, GoodsID: 593},
			{MinTmoney: 500, MaxTmoney: 899, GoodsID: 594},
			{MinTmoney: 900, MaxTmoney: 999, GoodsID: 595},
			{MinTmoney: 1000, MaxTmoney: 1999, GoodsID: 591},
			{MinTmoney: 2000, MaxTmoney: 7999, GoodsID: 585},
			{MinTmoney: 8000, MaxTmoney: 999999999, GoodsID: 590},
		},
	}
)

// BasicInfo 广告功能位基础信息
type BasicInfo struct {
	Icon         string `json:"icon"`
	IconNight    string `json:"icon_night"`
	Title        string `json:"title"`
	Abstract     string `json:"abstract"`
	ButtonText   string `json:"button_text"`
	TargetScheme string `json:"target_scheme"`
	ErrorToast   string `json:"error_toast"`
	IsNeedLogin  bool   `json:"is_need_login"`
}

// GoodsMapping 商品映射配置
type GoodsMapping struct {
	GoodsList []*GoodsMappingItem `json:"goods_list"`
}

// GoodsMappingItem 商品映射项
type GoodsMappingItem struct {
	MinTmoney int    `json:"min_tmoney"`
	MaxTmoney int    `json:"max_tmoney"`
	GoodsID   uint64 `json:"goods_id"`
}

// Config 防刷配置
type Config struct {
	AntiSpamTmoneyThreshold uint32 `json:"anti_spam_tmoney_threshold"` // 防刷贴贝阈值
	AntiSpamDays            uint32 `json:"anti_spam_days"`             // 防刷检查天数
	AntiSpamTitle           string `json:"anti_spam_title"`            // 防刷提示标题
	AntiSpamAbstract        string `json:"anti_spam_abstract"`         // 防刷提示摘要
	AntiSpamURL             string `json:"anti_spam_url"`              // 防刷提示URL
}

// TransactionRecord 交易记录
type TransactionRecord struct {
	UserID       uint64 `json:"user_id"`
	UpdateTmoney int64  `json:"update_tmoney"`
	CurrTmoney   uint64 `json:"curr_tmoney"`
	OpID         uint32 `json:"op_id"`
	OpSubid      uint64 `json:"op_subid"`
	OpType       uint32 `json:"op_type"`
	UpdateTime   int64  `json:"update_time"`
	OpFrom       string `json:"op_from"`
	Ext          string `json:"ext"`
}

// TransactionRecordData 交易记录数据
type TransactionRecordData struct {
	Count      uint32               `json:"count"`
	HasMore    uint32               `json:"hasmore"`
	RecordList []*TransactionRecord `json:"record_list"`
}

// TransactionRecordResponse 交易记录响应
type TransactionRecordResponse struct {
	Errno *uint32                `json:"errno"`
	Data  *TransactionRecordData `json:"data"`
}

// Processor 广告功能位处理器
type Processor struct {
	CommonParams commonparams.CommonParams

	// 配置数据
	basicInfo    *BasicInfo
	goodsMapping *GoodsMapping
	config       *Config

	// 业务状态
	qualified          bool   // 是否符合兑换条件
	leftTmoney         uint32 // 用户剩余贴贝
	goodsID            uint64 // 匹配的商品ID
	token              string // 弹窗token
	hasRecentExchange  bool   // 是否有近期兑换记录
	shouldShowAntiSpam bool   // 是否应该显示防盗刷提示
	noGoodsMatched     bool   // 是否没有匹配到商品（用于兜底逻辑）
}

func NewProcessor() *Processor {
	return &Processor{}
}

func (p *Processor) Process(ctx context.Context) error {
	userID := p.CommonParams.GetUserID()

	// 加载配置
	if err := p.loadConfigs(ctx); err != nil {
		return err
	}

	// 处理未登录用户：下发基础数据，但不进行业务逻辑处理
	if userID == 0 {
		p.handleUnloggedUser(ctx)
		return nil
	}

	// 获取用户贴贝余额
	tmoney, err := p.getUserTmoneyBalance(ctx, int64(userID))
	if err != nil {
		// 获取贴贝失败时，下发基础数据但标记为不符合条件
		p.handleGetTmoneyFailed(ctx)
		return nil
	}

	// 根据贴贝余额匹配商品
	goodsID := p.matchGoodsByTmoney(tmoney)

	// 检查卸载挽留渠道的防刷逻辑（检查所有配置商品的兑换记录）
	hasRecentExchange, err := p.checkRecentExchange(ctx, userID)
	if err != nil {
		// 防刷检查失败时继续执行，设置为无兑换记录
		hasRecentExchange = false
	}
	p.hasRecentExchange = hasRecentExchange

	// 判断是否符合兑换条件和是否需要防盗刷提示
	// 30天内兑换过且当前贴贝<1000时防薅羊毛，>=1000时按正常价格可以兑换
	antiSpamThreshold := p.GetAntiSpamTmoneyThreshold()
	shouldShowAntiSpam := hasRecentExchange && tmoney < uint64(antiSpamThreshold)
	qualified := goodsID > 0 && !shouldShowAntiSpam

	// 生成弹窗token
	var token string
	if goodsID > 0 {
		// 无论是否符合兑换条件，都生成token用于弹窗验证
		token = p.generateToken(ctx, userID, goodsID)
	}

	// 设置返回数据
	p.qualified = qualified
	p.leftTmoney = uint32(tmoney)
	p.goodsID = goodsID
	p.token = token
	p.shouldShowAntiSpam = shouldShowAntiSpam

	return nil
}

// handleUnloggedUser 处理未登录用户
func (p *Processor) handleUnloggedUser(ctx context.Context) {
	// 未登录用户：下发基础数据，但不符合兑换条件
	p.qualified = false
	p.leftTmoney = 0
	p.goodsID = 0
	p.token = ""
	p.hasRecentExchange = false
	p.shouldShowAntiSpam = false

}

// handleGetTmoneyFailed 处理获取贴贝失败的情况
func (p *Processor) handleGetTmoneyFailed(ctx context.Context) {
	// 获取贴贝失败：下发基础数据，但不符合兑换条件
	p.qualified = false
	p.leftTmoney = 0
	p.goodsID = 0
	p.token = ""
	p.hasRecentExchange = false
	p.shouldShowAntiSpam = false

}

func (p *Processor) IsQualified() bool {
	return p.qualified
}

func (p *Processor) GetLeftTmoney() uint32 {
	return p.leftTmoney
}

func (p *Processor) GetGoodsID() uint64 {
	return p.goodsID
}

func (p *Processor) GetToken() string {
	return p.token
}

func (p *Processor) HasRecentExchange() bool {
	return p.hasRecentExchange
}

func (p *Processor) ShouldShowAntiSpam() bool {
	return p.shouldShowAntiSpam
}

func (p *Processor) GetDialogTitle() string {
	if p.config != nil {
		return p.config.AntiSpamTitle
	}
	return defaultConfig.AntiSpamTitle
}

func (p *Processor) GetDialogAbstract() string {
	if p.config != nil {
		return p.config.AntiSpamAbstract
	}
	return defaultConfig.AntiSpamAbstract
}

func (p *Processor) GetDialogURL() string {
	if p.config != nil {
		return p.config.AntiSpamURL
	}
	return defaultConfig.AntiSpamURL
}

func (p *Processor) GetAntiSpamTmoneyThreshold() uint32 {
	if p.config != nil {
		return p.config.AntiSpamTmoneyThreshold
	}
	return defaultConfig.AntiSpamTmoneyThreshold
}

func (p *Processor) GetAntiSpamDays() uint32 {
	if p.config != nil {
		return p.config.AntiSpamDays
	}
	return defaultConfig.AntiSpamDays
}

// loadConfigs 加载广告功能位配置
func (p *Processor) loadConfigs(ctx context.Context) error {
	keys := []string{adBasicInfoKey, adGoodsMappingKey, adConfigKey}
	configData, err := wordserver.QueryItems(ctx, wordlistTable, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "query ad configs fail, table=%s, keys=%v, err=%v",
			wordlistTable, keys, err)
		return errors.New("failed to query ad configs")
	}

	if err := p.parseBasicInfo(ctx, configData[adBasicInfoKey]); err != nil {
		return err
	}

	if err := p.parseGoodsMapping(ctx, configData[adGoodsMappingKey]); err != nil {
		return err
	}

	if err := p.parseConfig(ctx, configData[adConfigKey]); err != nil {
		return err
	}

	return nil
}

func (p *Processor) parseBasicInfo(ctx context.Context, configStr string) error {
	if configStr == "" {
		tbcontext.WarningF(ctx, "ad basic info config is empty, use default")
		p.basicInfo = defaultBasicInfo
		return nil
	}

	var config BasicInfo
	if err := jsoniter.Unmarshal([]byte(configStr), &config); err != nil {
		tbcontext.WarningF(ctx, "unmarshal ad basic info fail, config=%s, err=%v, use default", configStr, err)
		p.basicInfo = defaultBasicInfo
		return nil
	}

	p.basicInfo = &config
	return nil
}

func (p *Processor) parseGoodsMapping(ctx context.Context, configStr string) error {
	if configStr == "" {
		tbcontext.WarningF(ctx, "ad goods mapping config is empty, use default")
		p.goodsMapping = defaultGoodsMapping
		return nil
	}

	var config GoodsMapping
	if err := jsoniter.Unmarshal([]byte(configStr), &config); err != nil {
		tbcontext.WarningF(ctx, "unmarshal ad goods mapping fail, config=%s, err=%v, use default", configStr, err)
		p.goodsMapping = defaultGoodsMapping
		return nil
	}

	p.goodsMapping = &config
	return nil
}

func (p *Processor) parseConfig(ctx context.Context, configStr string) error {
	if configStr == "" {
		tbcontext.WarningF(ctx, "ad config is empty, use default")
		p.config = defaultConfig
		return nil
	}

	var config Config
	if err := jsoniter.Unmarshal([]byte(configStr), &config); err != nil {
		tbcontext.WarningF(ctx, "unmarshal ad config fail, config=%s, err=%v, use default", configStr, err)
		p.config = defaultConfig
		return nil
	}

	p.config = &config
	return nil
}

func (p *Processor) GetBasicInfo() *BasicInfo {
	var basicInfo *BasicInfo
	if p.basicInfo != nil {
		basicInfo = p.basicInfo
	} else {
		basicInfo = defaultBasicInfo
	}

	// 根据业务状态动态设置 ErrorToast
	result := &BasicInfo{
		Icon:         basicInfo.Icon,
		IconNight:    basicInfo.IconNight,
		Title:        basicInfo.Title,
		Abstract:     basicInfo.Abstract,
		ButtonText:   basicInfo.ButtonText,
		TargetScheme: basicInfo.TargetScheme,
		IsNeedLogin:  basicInfo.IsNeedLogin,
	}

	// 根据不同情况设置不同的 ErrorToast
	if p.noGoodsMatched {
		// 没有匹配到商品时，使用 AntiSpamTitle 作为兜底文案
		if p.config != nil && p.config.AntiSpamTitle != "" {
			result.ErrorToast = p.config.AntiSpamTitle
		} else {
			result.ErrorToast = defaultConfig.AntiSpamTitle
		}
	} else {
		// 有匹配到商品时，使用 ErrorToast 作为防刷文案
		result.ErrorToast = basicInfo.ErrorToast
	}

	return result
}

// getUserTmoneyBalance 获取用户贴贝余额
func (p *Processor) getUserTmoneyBalance(ctx context.Context, userID int64) (uint64, error) {
	if userID <= 0 {
		return 0, errors.New("invalid user_id")
	}

	input := map[string]any{
		"user_id": uint64(userID),
	}
	output := &commonProto.GetUserTmoneyRes{}

	err := tbservice.Call(ctx, "common", "getUserTmoney", input, &output,
		tbservice.WithConverter(tbservice.JSONITER))

	if err != nil || output.Errno == nil || *output.Errno != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call common::getUserTmoney fail, input=%s, output=%s, err=%v",
			common.FmtTojson(input), common.FmtTojson(output), err)
		return 0, errors.New("call common::getUserTmoney fail")
	}

	remainingTmoney := output.GetData().GetRemainingNum()

	return uint64(remainingTmoney), nil
}

// matchGoodsByTmoney 根据贴贝余额匹配商品ID
func (p *Processor) matchGoodsByTmoney(tmoney uint64) uint64 {
	if p.goodsMapping == nil || len(p.goodsMapping.GoodsList) == 0 {
		// 没有商品配置时，标记为没有匹配到商品
		p.noGoodsMatched = true
		return 0
	}

	var matchedItem *GoodsMappingItem
	var bestRange int = -1

	// 找到包含当前贴贝数的最小区间
	for _, item := range p.goodsMapping.GoodsList {
		if tmoney >= uint64(item.MinTmoney) && tmoney <= uint64(item.MaxTmoney) {
			// 计算当前区间大小
			currentRange := item.MaxTmoney - item.MinTmoney
			// 如果是第一次匹配，或者找到了更小的区间（更精确的匹配）
			if bestRange == -1 || currentRange < bestRange {
				matchedItem = item
				bestRange = currentRange
			}
		}
	}

	var resultGoodsID uint64
	if matchedItem != nil {
		resultGoodsID = matchedItem.GoodsID
		p.noGoodsMatched = false
	} else {
		// 没有匹配到商品时，标记为没有匹配到商品
		p.noGoodsMatched = true
	}

	return resultGoodsID
}

// checkRecentExchange 检查卸载挽留渠道的近期兑换记录
func (p *Processor) checkRecentExchange(ctx context.Context, userID uint64) (bool, error) {
	if userID <= 0 {
		return false, errors.New("invalid user_id")
	}

	// 获取防刷配置
	antiSpamDays := p.GetAntiSpamDays()
	if antiSpamDays <= 0 {
		antiSpamDays = 30 // 默认30天
	}

	// 计算查询的时间范围
	startTime := time.Now().AddDate(0, 0, -int(antiSpamDays)).Unix()

	// 获取所有配置的商品ID列表
	configuredGoodsIDs := p.getConfiguredGoodsIDs()
	if len(configuredGoodsIDs) == 0 {
		// 如果没有配置商品，直接返回false
		return false, nil
	}

	// 构建查询参数 - 查询卸载挽留渠道的所有配置商品兑换记录
	input := map[string]any{
		"user_id":    userID,
		"pn":         1,
		"rn":         1,                  // 只查询1条记录即可，接口按时间倒序返回
		"money_type": 0,                  // 贴贝类型
		"op_id":      []int{10021},       // 卸载挽留来源
		"op_subids":  configuredGoodsIDs, // 筛选所有配置的商品ID
	}

	// 调用交易记录查询接口
	output := &TransactionRecordResponse{}
	err := tbservice.Call(ctx, "common", "getTransactionRecord", input, &output,
		tbservice.WithConverter(tbservice.JSONITER))

	if err != nil {
		tbcontext.WarningF(ctx, "call common::getTransactionRecord fail, input=%s, err=%v",
			common.FmtTojson(input), err)
		return false, err
	}

	if output.Errno == nil || *output.Errno != tiebaerror.ERR_SUCCESS {
		errno := uint32(0)
		if output.Errno != nil {
			errno = *output.Errno
		}
		tbcontext.WarningF(ctx, "call common::getTransactionRecord not success, errno=%d, input=%s, output=%s",
			errno, common.FmtTojson(input), common.FmtTojson(output))
		return false, errors.New("call getTransactionRecord fail")
	}

	// 检查是否有近期的卸载挽留兑换记录
	if output.Data != nil && len(output.Data.RecordList) > 0 {
		record := output.Data.RecordList[0] // 只有1条记录，取第一条

		// 检查record是否为空以及时间范围
		if record != nil && record.UpdateTime >= startTime {
			// 由于已经通过op_ids和op_subids筛选了卸载挽留渠道的配置商品，所以有记录就表示有近期兑换
			return true, nil
		}
	}

	return false, nil
}

// isConfiguredGoods 判断是否为配置的商品
func (p *Processor) isConfiguredGoods(goodsID uint64) bool {
	if p.goodsMapping == nil || len(p.goodsMapping.GoodsList) == 0 {
		return false
	}

	// 检查商品ID是否在配置的商品列表中
	for _, item := range p.goodsMapping.GoodsList {
		if item.GoodsID == goodsID {
			return true
		}
	}

	return false
}

// getConfiguredGoodsIDs 获取所有配置的商品ID列表
func (p *Processor) getConfiguredGoodsIDs() []int {
	if p.goodsMapping == nil || len(p.goodsMapping.GoodsList) == 0 {
		return nil
	}

	goodsIDs := make([]int, 0, len(p.goodsMapping.GoodsList))
	for _, item := range p.goodsMapping.GoodsList {
		goodsIDs = append(goodsIDs, int(item.GoodsID))
	}

	return goodsIDs
}

// generateToken 生成弹窗token
func (p *Processor) generateToken(ctx context.Context, userID uint64, goodsID uint64) string {
	if userID == 0 || goodsID == 0 {
		return ""
	}

	pattern := fmt.Sprintf("%s_%d_%d", types.AppUninstallPopTokenSalt, userID, goodsID)
	return php2go.Md5(pattern)
}
