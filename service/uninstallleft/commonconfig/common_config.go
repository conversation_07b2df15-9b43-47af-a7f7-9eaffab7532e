// Package commonconfig 卸载挽留公共配置算子
// 从词表加载主页面信息、弹窗配置、功能位配置等
package commonconfig

import (
	"context"
	"errors"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
)

// opName 算子名称
const opName = "common_config"

const (
	wordlistTable = "tb_wordlist_redis_uninstall_left_conf"

	mainPageInfoKey    = "main_page_info"
	uninstallPopKey    = "uninstall_pop"
	noLoginPopKey      = "nologin_pop"
	memoryBasicInfoKey = "memory_basic_info"
	helpBasicInfoKey   = "help_basic_info"
)

// 默认配置 - 用于词表失效时兜底
var (
	defaultMainPageInfo = &MainPageInfo{
		Title:    "使用过程中遇到问题了吗？",
		Abstract: "我们期望解决你的问题，带来更好的用户体验",
	}

	defaultUninstallPopConfig = &UninstallPopConfig{
		Title:    "还想体验贴吧？我们提供轻量社区服务！",
		Abstract: "不占手机内存，无需新装APP",
		List: []*UninstallPopItemConfig{
			{
				Icon:         "https://tieba-ares.cdn.bcebos.com/mis/2025-8/1754381717888/89e13a9afb77.png",
				Title:        "【微信小程序】访问贴吧",
				Abstract:     "微信搜索贴吧进入小程序",
				ButtonText:   "去体验",
				TargetScheme: "",
			},
			{
				Icon:         "https://tieba-ares.cdn.bcebos.com/mis/2025-8/1754381718306/89b9414440a1.png",
				Title:        "【百度APP】访问贴吧",
				Abstract:     "在底部点击贴吧分区进入",
				ButtonText:   "去体验",
				TargetScheme: "",
			},
		},
	}

	defaultNoLoginPopConfig = &NoLoginPopConfig{
		Title: "一键登录",
		Desc:  "登录后享受更多权益",
	}

	defaultMemoryFunctionInfo = &MemoryFunctionInfo{
		Icon:       "https://tieba-ares.cdn.bcebos.com/mis/2025-8/1754379559423/d89de7ee8c91.png",
		IconNight:  "https://tieba-ares.cdn.bcebos.com/mis/2025-8/1754379560988/fd305790a4d0.png",
		Title:      "手机内存不足 运行慢",
		Abstract:   "清理手机缓存，运行更流畅",
		ButtonText: "去优化",
		TargetScheme: "tiebaapp://router/portal?params=%7B%22page%22%3A%22setting%2FassistFunction%22%2C" +
			"%22pageParams%22%3A%7B%22jumpToItem%22%3A%20%22clearCache%22%2C%22fromPage%22%3A%22uninstallRetain%22%7D%7D",
		ErrorToast:  "",
		IsNeedLogin: false, // 功能位1默认不需要登录
	}

	defaultHelpFunctionInfo = &HelpFunctionInfo{
		Icon:       "https://tieba-ares.cdn.bcebos.com/mis/2025-8/1754379560571/781cef91bba9.png",
		IconNight:  "https://tieba-ares.cdn.bcebos.com/mis/2025-8/1754379562077/3df60afb4a5c.png",
		Title:      "贴吧使用的其他问题",
		Abstract:   "快速解决删贴、吧主违规等问题",
		ButtonText: "去反馈",
		TargetScheme: "tiebaapp://router/portal?params=%7B%22page%22%3A%22h5%2FopenWebView%22%2C%22pageParams%22%3A%7B%22url%22%3A%22" +
			"https%3A%2F%2Ftieba.baidu.com%2Fmo%2Fq%2Fhybrid-main-bawu%2FthreadRecycleStation%2Fhybrid%3F" +
			"source%3Duninstall%26nonavigationbar%3D1%26customfullscreen%3D1%26loadingSignal%3D1%22%7D%7D",
		ErrorToast:  "你的账号无异常",
		IsNeedLogin: false, // 功能位4默认不需要登录
	}
)

// Processor 公共配置处理器
type Processor struct {
	CommonParams commonparams.CommonParams

	mainPageInfo       *MainPageInfo
	uninstallPopConfig *UninstallPopConfig
	noLoginPopConfig   *NoLoginPopConfig
	memoryFunctionInfo *MemoryFunctionInfo
	helpFunctionInfo   *HelpFunctionInfo
}

func NewProcessor() *Processor {
	return &Processor{}
}

func (p *Processor) Process(ctx context.Context) error {
	if err := p.loadConfigs(ctx); err != nil {
		return err
	}
	return nil
}

func (p *Processor) GetMainPageInfo() *MainPageInfo {
	if p.mainPageInfo != nil {
		return p.mainPageInfo
	}
	return defaultMainPageInfo
}

func (p *Processor) GetUninstallPopConfig() *UninstallPopConfig {
	if p.uninstallPopConfig != nil {
		return p.uninstallPopConfig
	}
	return defaultUninstallPopConfig
}

func (p *Processor) GetNoLoginPopConfig() *NoLoginPopConfig {
	if p.noLoginPopConfig != nil {
		return p.noLoginPopConfig
	}
	return defaultNoLoginPopConfig
}

func (p *Processor) GetMemoryFunctionInfo() *MemoryFunctionInfo {
	if p.memoryFunctionInfo != nil {
		return p.memoryFunctionInfo
	}
	return defaultMemoryFunctionInfo
}

func (p *Processor) GetHelpFunctionInfo() *HelpFunctionInfo {
	if p.helpFunctionInfo != nil {
		return p.helpFunctionInfo
	}
	return defaultHelpFunctionInfo
}

// loadConfigs 加载公共配置
func (p *Processor) loadConfigs(ctx context.Context) error {
	keys := []string{
		mainPageInfoKey,
		uninstallPopKey,
		noLoginPopKey,
		memoryBasicInfoKey,
		helpBasicInfoKey,
	}

	configData, err := wordserver.QueryItems(ctx, wordlistTable, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "query common configs fail, table=%s, keys=%v, err=%v",
			wordlistTable, keys, err)
		return errors.New("failed to query common configs")
	}

	// 解析各配置项
	parsers := []struct {
		key    string
		parser func(context.Context, string) error
	}{
		{mainPageInfoKey, p.parseMainPageInfo},
		{uninstallPopKey, p.parseUninstallPopConfig},
		{noLoginPopKey, p.parseNoLoginPopConfig},
		{memoryBasicInfoKey, p.parseMemoryFunctionInfo},
		{helpBasicInfoKey, p.parseHelpFunctionInfo},
	}

	for _, parser := range parsers {
		if err := parser.parser(ctx, configData[parser.key]); err != nil {
			return err
		}
	}

	return nil
}

func (p *Processor) parseMainPageInfo(ctx context.Context, configStr string) error {
	if configStr == "" {
		tbcontext.WarningF(ctx, "main page info config is empty, use default")
		p.mainPageInfo = defaultMainPageInfo
		return nil
	}

	var config MainPageInfo
	if err := jsoniter.Unmarshal([]byte(configStr), &config); err != nil {
		tbcontext.WarningF(ctx, "unmarshal main page info fail, config=%s, err=%v, use default",
			configStr, err)
		p.mainPageInfo = defaultMainPageInfo
		return nil
	}

	p.mainPageInfo = &config
	return nil
}

// parseUninstallPopConfig 解析卸载弹窗配置
func (p *Processor) parseUninstallPopConfig(ctx context.Context, configStr string) error {
	if configStr == "" {
		tbcontext.WarningF(ctx, "uninstall pop config is empty, use default")
		p.uninstallPopConfig = defaultUninstallPopConfig
		return nil
	}

	var config UninstallPopConfig
	if err := jsoniter.Unmarshal([]byte(configStr), &config); err != nil {
		tbcontext.WarningF(ctx, "unmarshal uninstall pop config fail, config=%s, err=%v, use default", configStr, err)
		p.uninstallPopConfig = defaultUninstallPopConfig
		return nil
	}

	p.uninstallPopConfig = &config
	return nil
}

// parseNoLoginPopConfig 解析未登录弹窗配置
func (p *Processor) parseNoLoginPopConfig(ctx context.Context, configStr string) error {
	if configStr == "" {
		tbcontext.WarningF(ctx, "nologin pop config is empty, use default")
		p.noLoginPopConfig = defaultNoLoginPopConfig
		return nil
	}

	var config NoLoginPopConfig
	if err := jsoniter.Unmarshal([]byte(configStr), &config); err != nil {
		tbcontext.WarningF(ctx, "unmarshal nologin pop config fail, config=%s, err=%v, use default", configStr, err)
		p.noLoginPopConfig = defaultNoLoginPopConfig
		return nil
	}

	p.noLoginPopConfig = &config
	return nil
}

// parseMemoryFunctionInfo 解析功能位1配置
func (p *Processor) parseMemoryFunctionInfo(ctx context.Context, configStr string) error {
	if configStr == "" {
		tbcontext.WarningF(ctx, "memory function info config is empty, use default")
		p.memoryFunctionInfo = defaultMemoryFunctionInfo
		return nil
	}

	var config MemoryFunctionInfo
	if err := jsoniter.Unmarshal([]byte(configStr), &config); err != nil {
		tbcontext.WarningF(ctx, "unmarshal memory function info fail, config=%s, err=%v, use default", configStr, err)
		p.memoryFunctionInfo = defaultMemoryFunctionInfo
		return nil
	}

	p.memoryFunctionInfo = &config
	return nil
}

// parseHelpFunctionInfo 解析功能位4配置
func (p *Processor) parseHelpFunctionInfo(ctx context.Context, configStr string) error {
	if configStr == "" {
		tbcontext.WarningF(ctx, "help function info config is empty, use default")
		p.helpFunctionInfo = defaultHelpFunctionInfo
		return nil
	}

	var config HelpFunctionInfo
	if err := jsoniter.Unmarshal([]byte(configStr), &config); err != nil {
		tbcontext.WarningF(ctx, "unmarshal help function info fail, config=%s, err=%v, use default", configStr, err)
		p.helpFunctionInfo = defaultHelpFunctionInfo
		return nil
	}

	p.helpFunctionInfo = &config
	return nil
}
