package commonconfig

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
)

// OperatorCommonConfig 公共配置算子定义
type OperatorCommonConfig struct {
	CommonParams commonparams.CommonParams `inject:"canLost=false,canNil=false"`
	CommonConfig `extract:"canLost=true,canNil=true"`
}

// DoImpl 算子入口
func (op *OperatorCommonConfig) DoImpl(ctx *engine.Context) error {
	processor := NewProcessor()
	processor.CommonParams = op.CommonParams

	err := processor.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	op.CommonConfig = processor
	return nil
}
