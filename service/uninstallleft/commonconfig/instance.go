package commonconfig

// MainPageInfo 主页面信息
type MainPageInfo struct {
	Title    string `json:"title"`
	Abstract string `json:"abstract"`
}

// UninstallPopConfig 卸载弹窗配置
type UninstallPopConfig struct {
	Title    string                    `json:"title"`
	Abstract string                    `json:"abstract"`
	List     []*UninstallPopItemConfig `json:"list"`
}

// UninstallPopItemConfig 卸载弹窗配置项
type UninstallPopItemConfig struct {
	Icon         string `json:"icon"`
	Title        string `json:"title"`
	Abstract     string `json:"abstract"`
	ButtonText   string `json:"button_text"`
	TargetScheme string `json:"target_scheme"`
}

// NoLoginPopConfig 未登录弹窗配置
type NoLoginPopConfig struct {
	Title string `json:"title"`
	Desc  string `json:"desc"`
}

// MemoryFunctionInfo 功能位1（内存不足）配置
type MemoryFunctionInfo struct {
	Icon         string `json:"icon"`
	IconNight    string `json:"icon_night"`
	Title        string `json:"title"`
	Abstract     string `json:"abstract"`
	ButtonText   string `json:"button_text"`
	TargetScheme string `json:"target_scheme"`
	ErrorToast   string `json:"error_toast"`
	IsNeedLogin  bool   `json:"is_need_login"`
}

// HelpFunctionInfo 功能位4（帮助中心）配置
type HelpFunctionInfo struct {
	Icon         string `json:"icon"`
	IconNight    string `json:"icon_night"`
	Title        string `json:"title"`
	Abstract     string `json:"abstract"`
	ButtonText   string `json:"button_text"`
	TargetScheme string `json:"target_scheme"`
	ErrorToast   string `json:"error_toast"`
	IsNeedLogin  bool   `json:"is_need_login"`
}

// CommonConfig 公共配置算子导出接口定义
type CommonConfig interface {
	// GetMainPageInfo 获取主页面信息
	GetMainPageInfo() *MainPageInfo
	// GetUninstallPopConfig 获取继续卸载弹窗配置
	GetUninstallPopConfig() *UninstallPopConfig
	// GetNoLoginPopConfig 获取未登录弹窗配置
	GetNoLoginPopConfig() *NoLoginPopConfig
	// GetMemoryFunctionInfo 获取功能位1（内存不足）配置
	GetMemoryFunctionInfo() *MemoryFunctionInfo
	// GetHelpFunctionInfo 获取功能位4（帮助中心）配置
	GetHelpFunctionInfo() *HelpFunctionInfo
}
