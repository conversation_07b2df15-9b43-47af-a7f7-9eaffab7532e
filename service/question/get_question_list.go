package question

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	cp "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	forumProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/search"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbrichtext"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	p "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/question/getQuestionList"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/common/meta"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/util"
)

const (
	tabTypeRecommend                  = "recommend"
	tabTypeForum                      = "forum"
	tabTypeAll                        = "all"
	tabTypeInvite                     = "invite"
	subTabTypeAll                     = "all"
	subTabTypeBonus                   = "bonus"
	questionConfWordlistTableName     = "tb_wordlist_redis_question"                 // 问题贴配置表名
	gaokaokaohouConfWordlistTableName = "tb_wordlist_redis_activity_scene_config_15" // 高考考后配置表名
	gaokaoScene                       = 1                                            // 高考场景
	questionScene                     = 2                                            // 问题贴场景
)

func GetQuestionList(ctx context.Context, baseData *types.GetQuestionListBaseData, outData *p.GetQuestionListRes) int {
	eg := gtask.Group{
		AllowSomeFail: false,
	}

	baseData.StaticField.GaoKaoAuthInfo = &p.User{}
	outData.User = &p.User{}
	// 高考场景+展示认证高校tab时间范围内 两种情况下需要请求用户认证吧id信息
	isShowGaokaoTab := getIsShowGaokaoTab(ctx)
	if (baseData.Request.GetScene() == gaokaoScene || isShowGaokaoTab > 0) &&
		baseData.StaticField.UserID != 0 {
		int64UserId := int64(baseData.StaticField.UserID)
		// 高考考后场景，需要提前获取用户认证信息用于展示默认定位
		input := &cp.MgetStuAuthByUIDsReq{
			UserIds: []int64{int64UserId},
		}
		output := &cp.MgetStuAuthByUIDsRes{}
		err := tbservice.Call(ctx, "common", "mgetStuAuthByUIDs", input, output,
			tbservice.WithConverter(tbservice.JSONITER), tbservice.WithRalName("common_go"))
		if err != nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service common:mgetStuAuthByUIDs, input = %v, output = %v, err = %v",
				common.ToString(input), common.ToString(output), err)
		} else {
			if authInfo, ok := output.GetData().GetAuthInfo()[int64UserId]; ok {
				baseData.StaticField.GaoKaoAuthInfo.AuthFid = proto.Int64(authInfo.GetForumId())
				baseData.StaticField.GaoKaoAuthInfo.AuthStatus = proto.Uint32(authInfo.GetStatus())
				baseData.StaticField.GaoKaoAuthInfo.Tag = proto.String(authInfo.GetTag())
				if authInfo.GetStatus() == 1 {
					baseData.StaticField.GaoKaoAuthFID = authInfo.GetForumId()
					outData.User.AuthFid = proto.Int64(baseData.StaticField.GaoKaoAuthInfo.GetAuthFid())
					outData.User.Tag = proto.String(baseData.StaticField.GaoKaoAuthInfo.GetTag())
				}
				outData.User.AuthStatus = proto.Uint32(baseData.StaticField.GaoKaoAuthInfo.GetAuthStatus())
			}
		}
	}

	// 下发当前用户的信息
	eg.Go(func() error {
		if baseData.Request.GetScene() == gaokaoScene && baseData.StaticField.UserID == 0 {
			tbcontext.WarningF(ctx, "fail to get user data,scene = %v, err = %v",
				baseData.Request.GetScene(), errors.New("user id is empty"))
			return nil
		}
		isLogin := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("login", 0), common.TTT_UINT32).(uint32)
		u, err := getUserData(ctx, int64(baseData.StaticField.UserID))
		if err != nil {
			tbcontext.WarningF(ctx, "fail to get user data, err = %v", err)
			return err
		}
		outData.User.IsLogin = proto.Uint32(isLogin)
		outData.User.Name = proto.String(getUserNameShow(u))

		// 获取高考考后配置数据
		if baseData.Request.GetScene() == gaokaoScene {
			portraitTime := time.Now().Unix()
			if u.GetPortraitTime() != "" {
				portraitTime = cast.ToInt64(u.GetPortraitTime())
			}
			portraitID := tbportrait.Encode(int64(u.GetUserId()), u.GetUserName(), portraitTime)
			portraitStr := fmt.Sprintf("http://tb.himg.baidu.com/sys/portrait/item/%s.jpg", portraitID)
			outData.User.Portrait = proto.String(portraitStr)
		}
		return nil
	})

	// 获取banner
	eg.Go(func() error {
		// 获取banner的词表配置
		keys := []string{"question_page_banner"}
		wordItems, err := wordserver.QueryItems(ctx, questionConfWordlistTableName, keys)
		if err != nil {
			tbcontext.WarningF(ctx, "fail to query wordlist, keys = %v", common.ToString(keys))
			return err
		}

		// 解析banner配置
		bannerConf := wordItems["question_page_banner"]
		if len(bannerConf) != 0 {
			banner := &p.Banner{}
			err := jsoniter.UnmarshalFromString(bannerConf, banner)
			if err != nil {
				tbcontext.WarningF(ctx, "fail to unmarshal banner conf, err = %v", err)
				return err
			}
			outData.Banner = banner
		}
		return nil
	})

	// 获取高考考后配置数据
	eg.Go(func() error {
		if baseData.Request.GetScene() == gaokaoScene {
			// 获取banner的词表配置
			keys := []string{
				"cvtInfo#timeline",         // 高考考后时间阶段 1:活动开始时间， 2:结束时间
				"confInfo#background_info", // 高考考后主会场头图
				"gaokao2024_auth_str",      // 高考考后主会场个人信息区：未认证用户固定文案
			}
			wordItems, err := wordserver.QueryItems(ctx, gaokaokaohouConfWordlistTableName, keys)
			if err != nil {
				tbcontext.WarningF(ctx, "fail to query wordlist, keys = %v", common.ToString(keys))
				return err
			}
			config := &p.Config{
				ActivityStatus: proto.Int32(-1), // 默认结束
			}
			timelineMap := util.ParseTimeline(ctx, wordItems["cvtInfo#timeline"])
			for activityStatus, timelineInfo := range timelineMap {
				if time.Now().Unix() >= timelineInfo.BeginTime && time.Now().Unix() <= timelineInfo.EndTime {
					config.ActivityStatus = proto.Int32(int32(activityStatus))
					break
				}
			}
			backgroundInfo := &p.ConfigBackgroundInfo{}
			err = jsoniter.UnmarshalFromString(wordItems["confInfo#background_info"], backgroundInfo)
			if err != nil {
				tbcontext.WarningF(ctx, "fail to unmarshal background info, err = %v", err)
				return err
			}
			config.BackgroundInfo = backgroundInfo
			outData.User.AuthGuide = proto.String(wordItems["gaokao2024_auth_str"])
			outData.Config = config
		}
		return nil
	})

	// 获取tab列表
	eg.Go(func() error {
		tabs, err := buildTabList(ctx, baseData)
		if err != nil {
			tbcontext.WarningF(ctx, "fail to build tab list, err = %v", err)
			return err
		}
		outData.Tab = tabs
		if outData.User.GetAuthStatus() == 1 {
			outData.User.AuthFname = proto.String(baseData.StaticField.GaoKaoAuthInfo.GetAuthFname())
		}
		return nil
	})

	var emptyInvite bool
	eg.Go(func() error {
		var list []uint64
		var hasMore uint32
		var checkInfo map[uint64]ExtraInfo
		var err error
		// 获取参数，调用不同service
		if baseData.Request.GetTabType() == tabTypeForum || baseData.Request.GetTabType() == tabTypeAll {
			if baseData.Request.GetScene() == gaokaoScene && baseData.StaticField.GaoKaoAuthFID > 0 &&
				baseData.Request.GetTabId() < 0 && baseData.Request.GetTabType() == tabTypeForum {
				// 高考场景下需要默认展示认证的吧内问题
				baseData.Request.TabId = proto.Int64(baseData.StaticField.GaoKaoAuthFID)
			}
			// 吧内问题列表
			list, hasMore, err = getForumQuestionList(ctx, baseData)
		} else {
			// 邀请列表，需要并行请求全吧问题列表，当邀请列表为空时使用全吧问题兜底
			g := gtask.Group{
				AllowSomeFail: false,
			}
			// 邀请列表
			var inviteList []uint64
			var inviteHasMore uint32

			// 全部问题列表
			var allList []uint64
			var allHasMore uint32

			g.Go(func() error {
				var err error
				inviteList, checkInfo, inviteHasMore, err = getInviteQuestionList(ctx, baseData)
				return err
			})

			// 首次请求推荐tab，需要并行获取全吧问题列表
			if baseData.Request.GetTabType() == tabTypeRecommend {
				tbcontext.AddNotice(ctx, "request_tab_recommend", 1)
				g.Go(func() error {
					var err error
					allList, allHasMore, err = getForumQuestionList(ctx, baseData)
					return err
				})
			}

			_, err = g.Wait()

			list = inviteList
			hasMore = inviteHasMore
			if len(inviteList) == 0 {
				tbcontext.AddNotice(ctx, "empty_invite_list", 1)
				list = allList
				hasMore = allHasMore
				emptyInvite = true
			}
		}

		if err != nil {
			tbcontext.FatalF(ctx, "fail to get question list, err = %v", err)
			return err
		}

		if len(list) == 0 {
			tbcontext.WarningF(ctx, "question list is empty, input = %v", common.ToString(baseData.Request))
			return nil
		}

		threadList, err := buildQuestionList(ctx, list, checkInfo)
		if err != nil {
			tbcontext.WarningF(ctx, "fail to build question list, err = %v", err)
			return err
		}

		outData.ThreadList = threadList
		outData.HasMore = proto.Uint32(hasMore)
		return nil
	})

	_, err := eg.Wait()
	if err != nil {
		tbcontext.WarningF(ctx, "fail to get question list, err = %v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 推荐tab为空的时候，需要将下发的第一个tab类型改成forum
	if len(outData.GetTab()) > 0 && outData.GetTab()[0].GetType() == tabTypeRecommend {
		if emptyInvite {
			outData.GetTab()[0].Type = proto.String(tabTypeAll)
		} else {
			outData.GetTab()[0].Type = proto.String(tabTypeInvite)
		}
	}

	return tiebaerror.ERR_SUCCESS
}

func getUserData(ctx context.Context, uid int64) (*user.UserInfo, error) {
	// 获取用户信息
	input := &user.GetUserDataReq{
		UserId: proto.Int64(uid),
	}
	output := &user.GetUserDataRes{}

	err := tbservice.Call(ctx, "user", "getUserData", input, output, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service user:getUserData, input = %v, output = %v, err = %v",
			common.ToString(input), common.ToString(output), err)
		return nil, errors.New("fail to call user:getUserData")
	}
	return output.GetUserInfo()[0], nil
}

type ExtraInfo struct {
	IsChecked uint32
	Answered  uint32
}

func getInviteQuestionList(ctx context.Context, baseData *types.GetQuestionListBaseData) (
	[]uint64, map[uint64]ExtraInfo, uint32, error) {
	// 获取邀请列表
	inviteInput := &cp.GetInviteListByUidReq{
		Uid:       proto.Uint64(baseData.StaticField.UserID),
		Pn:        proto.Uint32(baseData.Request.GetPn()),
		Rn:        proto.Uint32(baseData.Request.GetRn()),
		// 2025.7.15 推荐tab 不做退场 PM liujinyao
		//HasAnswer: proto.Uint32(0), //
	}

	// 查询悬赏中的贴子
	if baseData.Request.GetSubTabType() == subTabTypeBonus {
		inviteInput.Type = proto.Uint32(1)
	}

	inviteOutput := &cp.GetInviteListByUidRes{}
	err := tbservice.Call(ctx, "common", "getInviteListByUid", inviteInput, inviteOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || inviteOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "fail to get invite list, output = %v, err = %v", common.ToString(inviteOutput), err)
		return nil, nil, 0, errors.New("fail to call common:getInviteListByUid")
	}

	// 更新用户的列表已读
	updateInput := &cp.UpdateUidCheckReq{
		Uid: proto.Uint64(baseData.StaticField.UserID),
	}
	updateOutput := &cp.UpdateUidCheckRes{}

	err = tbservice.Call(ctx, "common", "updateUidCheck", updateInput, updateOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || inviteOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		// 可以不阻塞流程
		tbcontext.WarningF(ctx, "fail to updateUidCheck, output = %v, err = %v", common.ToString(updateOutput), err)
	}

	if len(inviteOutput.GetData().GetList()) == 0 {
		tbcontext.TraceF(ctx, "invite list is empty, input = %v %v", common.ToString(baseData.Request), common.ToString(baseData.StaticField))
		return nil, nil, 0, nil
	}

	// 还要经过一次filterInvalidQuestion
	question := make([]*cp.FilterQuestionItem, 0)
	isCheck := map[uint64]ExtraInfo{}
	for _, q := range inviteOutput.GetData().GetList() {
		isCheck[q.GetTid()] = ExtraInfo{
			IsChecked: q.GetIsCheck(),
			Answered:  q.GetAnswered(),
		}
		question = append(question, &cp.FilterQuestionItem{
			Tid: proto.Uint64(q.GetTid()),
		})
	}
	filterInput := &cp.FilterInvalidQuestionForUIReq{
		QuestionList: question,
		UserId:       proto.Uint64(baseData.StaticField.UserID),
	}
	filterOutput := &cp.FilterInvalidQuestionForUIRes{}
	err = tbservice.Call(ctx, "common", "filterInvalidQuestionForUI", filterInput, filterOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || filterOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service common:filterInvalidQuestionForUI, input = %v, output = %v, err = %v",
			common.ToString(filterInput), common.ToString(filterOutput), err)
	}

	tids := make([]uint64, 0, len(inviteOutput.GetData().GetList()))
	for _, item := range filterOutput.GetData().GetResult() {
		tids = append(tids, item.GetTid())
	}
	hasMore := inviteOutput.GetData().GetHasMore()
	return tids, isCheck, hasMore, nil
}

func getForumQuestionList(ctx context.Context, baseData *types.GetQuestionListBaseData) ([]uint64, uint32, error) {
	pn := baseData.Request.GetPn()
	rn := baseData.Request.GetRn()
	userID := baseData.StaticField.UserID
	tabID := baseData.Request.GetTabId()
	subTabType := baseData.Request.GetSubTabType()

	input := &cp.GetQuestionListReq{
		Pn:  proto.Int64(int64(pn)),
		Rn:  proto.Int64(int64(rn)),
		Uid: proto.Int64(int64(userID)),
	}
	if tabID > 0 {
		input.Fids = []int64{tabID}
	}

	// 是否是高考场景下"全部"tab
	if baseData.Request.GetScene() == 1 && tabID < 0 {
		tbcontext.TraceF(ctx, "getForumQuestionList is hit gaokao all tab")
		input.From = proto.String("gaokao_all_tab")
	}

	// 全部和悬赏中，调用不同的下游service
	method := "getFuseQuestionList"
	if subTabType == subTabTypeAll {
		method = "getFuseQuestionList"
		input.IsShowNewSort = proto.Int32(1)
	} else if subTabType == subTabTypeBonus {
		method = "getNewQuestionList"
		input.Scene = proto.Int32(-1) // 赏金互助场景
	}

	output := &cp.GetQuestionListRes{}
	err := tbservice.Call(ctx, "common", method, input, output, tbservice.WithConverter(tbservice.JSONITER), tbservice.WithRalName("common_go"))
	if err != nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service common:getNewQuestionList, input = %v, output = %v, err = %v",
			common.ToString(input), common.ToString(output), err)
		return nil, 0, errors.New("fail to call common:getNewQuestionList")
	}

	data := output.GetData()
	tids := make([]uint64, 0, len(data.GetQuestionList()))
	for _, t := range data.GetQuestionList() {
		tids = append(tids, uint64(t.GetTid()))
	}
	hasMore := cast.ToUint32(int64(len(data.GetQuestionList())) < data.GetCount())

	return tids, hasMore, nil
}

func buildTabList(ctx context.Context, baseData *types.GetQuestionListBaseData) ([]*p.Tab, error) {
	var tabs = make([]*p.Tab, 0, 15)

	multi := tbservice.Multi()
	// 获取有问答贴的贴吧，组成tab
	searchMachineQAForumReq := &search.SearchMachineQAForumReq{
		Pn:       proto.Uint32(1),
		Rn:       proto.Uint32(40),
		SortType: proto.String("unresolved_count"),
	}

	multi.Register(ctx, "searchMachineQAForum", &tbservice.Parameter{
		Service: "search",
		Method:  "searchMachineQAForum",
		Input:   searchMachineQAForumReq,
		Output:  &search.SearchMachineQAForumRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	})
	// common::queryInviteUserRecForum
	queryInviteUserRecForumReq := &cp.QueryInviteUserRecForumReq{
		UserId: proto.Int64(int64(baseData.StaticField.UserID)),
	}
	multi.Register(ctx, "queryInviteUserRecForum", &tbservice.Parameter{
		Service: "common",
		Method:  "queryInviteUserRecForum",
		Input:   queryInviteUserRecForumReq,
		Output:  &cp.QueryInviteUserRecForumRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	})
	multi.Call(ctx)

	// 获取有问答贴的贴吧，组成tab
	searchMachineQAForumInter, err := multi.GetResult(ctx, "searchMachineQAForum")
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service search:searchMachineQAForum, input = %v, err = %v",
			common.ToString(searchMachineQAForumReq), err)
		return nil, err
	}
	searchMachineQAForumData, ok := searchMachineQAForumInter.(*search.SearchMachineQAForumRes)
	if !ok || searchMachineQAForumData.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service search:searchMachineQAForum, input = %v, output = %v, err = %v",
			common.ToString(searchMachineQAForumReq), common.ToString(searchMachineQAForumData), err)
		return nil, errors.New("fail to call search:searchMachineQAForum")
	}
	// common::queryInviteUserRecForum
	queryInviteUserRecForumInter, err := multi.GetResult(ctx, "queryInviteUserRecForum")
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service common:queryInviteUserRecForum, input = %v, err = %v",
			common.ToString(queryInviteUserRecForumReq), err)
	}
	queryInviteUserRecForumData, ok := queryInviteUserRecForumInter.(*cp.QueryInviteUserRecForumRes)
	if !ok || queryInviteUserRecForumData.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service common:queryInviteUserRecForum, input = %v, output = %v, err = %v",
			common.ToString(queryInviteUserRecForumReq), common.ToString(queryInviteUserRecForumData), err)
	}

	subTab := []*p.SubTab{
		{
			Type: proto.String(subTabTypeAll),
			Name: proto.String("全部"),
		},
		{
			Type: proto.String(subTabTypeBonus),
			Name: proto.String("悬赏中"),
		},
	}

	// 默认添加一个推荐tab 高考场景下返回的是全部
	defaultTabName := "推荐"
	if baseData.Request.GetScene() == gaokaoScene {
		defaultTabName = "全部"
	}
	tabs = append(tabs, &p.Tab{
		Type:   proto.String(tabTypeRecommend),
		Id:     proto.Int64(0),
		Name:   proto.String(defaultTabName),
		SubTab: subTab,
	})

	fids := make([]int64, 0)
	for _, data := range queryInviteUserRecForumData.GetData() {
		fids = append(fids, data.GetForumId())
	}
	for _, forum := range searchMachineQAForumData.GetData().GetRows() {
		fids = append(fids, forum.GetForumId())
	}

	newFidArr := make(map[int64]struct{})

	// 用户认证的高校吧需要展示在推荐后面
	if baseData.StaticField.GaoKaoAuthInfo.GetAuthFid() != 0 {
		newFidArr[baseData.StaticField.GaoKaoAuthInfo.GetAuthFid()] = struct{}{}
		fids = append(fids, baseData.StaticField.GaoKaoAuthInfo.GetAuthFid())
	}

	var gaokaoDefaultFids []int64
	if baseData.Request.GetScene() == gaokaoScene {
		keys := []string{"gaokao_default_fids"}
		wordItems, err := wordserver.QueryItemsNoPHPSerialized(ctx, questionConfWordlistTableName, keys)
		if err != nil {
			tbcontext.WarningF(ctx, "fail to query wordlist, keys = %v", common.ToString(keys))
		} else {
			err := common.StructAToStructBCtx(ctx, wordItems["gaokao_default_fids"], &gaokaoDefaultFids)
			if err != nil {
				tbcontext.WarningF(ctx, "fail to struct convert, res = %v", common.ToString(wordItems["gaokao_default_fids"]))
			} else {
				for _, fid := range gaokaoDefaultFids {
					if _, ok := newFidArr[fid]; !ok {
						newFidArr[fid] = struct{}{}
						fids = append(fids, fid)
					}
				}
			}
		}
	}

	// 默认过滤为认证成功的吧tab 如果传进来的历史吧id内存在认证中的高校吧id，则不进行过滤
	isFilterNoAuthGaokaoTab := true
	// 整理历史吧id用户返回的贴吧tab
	historyFids := make([]int64, 0)
	// 添加历史吧id，用于过滤贴吧tab
	if baseData.Request.GetFids() != "" {
		newFids := strings.Split(baseData.Request.GetFids(), ",")
		for _, fidStr := range newFids {
			fid, err := strconv.ParseInt(fidStr, 10, 64)
			if err == nil {
				if baseData.StaticField.GaoKaoAuthInfo.GetAuthFid() != 0 && fid == baseData.StaticField.GaoKaoAuthInfo.GetAuthFid() {
					isFilterNoAuthGaokaoTab = false
				}
				newFidArr[fid] = struct{}{}
				fids = append(fids, fid)
				historyFids = append(historyFids, fid)
			}
		}
	}

	// 过滤没有求助贴的吧tab 是否有问答数据 hasQuestionForum
	// 是否成功获取到吧对应的问答数量 questionCountSuc
	// 高考场景下只展示高校的吧tab：批量获取高校认证吧 collegeForumData
	// 历史吧吧信息 historyForumInfos
	questionCountSuc, hasQuestionForum, collegeForumData, historyForumInfos :=
		getForumData(ctx, baseData.Request.GetScene(), fids, newFidArr)

	existsMap := make(map[int64]struct{})
	forumTabNum := 0 // 吧tab最多15个
	// 用户认证的高校吧需要展示在推荐后面
	if baseData.StaticField.GaoKaoAuthFID != 0 {
		if forumInfo, ok := historyForumInfos[uint32(baseData.StaticField.GaoKaoAuthFID)]; ok {
			forumName := forumInfo.GetForumName().GetForumName()
			forumTabNum++
			tabs = append(tabs, &p.Tab{
				Type:   proto.String(tabTypeForum),
				Id:     proto.Int64(baseData.StaticField.GaoKaoAuthFID),
				Name:   proto.String(forumName),
				SubTab: subTab,
			})
			existsMap[baseData.StaticField.GaoKaoAuthFID] = struct{}{}
			baseData.StaticField.GaoKaoAuthInfo.AuthFname = proto.String(forumName)
		}
	}

	// 原来的newFidArr会根据fid大小自动排序，这里需要手动排序
	if len(historyFids) > 0 { // 优先添加历史吧
		for _, fid := range historyFids {
			if forumTabNum >= 15 {
				break
			}
			if _, ok := existsMap[fid]; ok {
				continue
			}
			if questionCountSuc && !hasQuestionForum[fid] {
				continue
			}

			// 历史吧信息不存在，过滤掉
			if _, ok := historyForumInfos[uint32(fid)]; !ok {
				continue
			}

			// 高考认证未成功的吧且传递的历史吧ids无用户要认证的吧，需要单独过滤掉
			if isFilterNoAuthGaokaoTab && baseData.StaticField.GaoKaoAuthFID == 0 && baseData.StaticField.GaoKaoAuthInfo.GetAuthFid() == fid {
				continue
			}

			forumName := historyForumInfos[uint32(fid)].GetForumName().GetForumName()
			forumTabNum++
			tabs = append(tabs, &p.Tab{
				Type:   proto.String(tabTypeForum),
				Id:     proto.Int64(fid),
				Name:   proto.String(forumName),
				SubTab: subTab,
			})
			existsMap[fid] = struct{}{}
		}
	}
	for _, data := range queryInviteUserRecForumData.GetData() {
		if forumTabNum >= 15 {
			break
		}
		if _, ok := existsMap[data.GetForumId()]; ok || (questionCountSuc && !hasQuestionForum[data.GetForumId()]) {
			continue
		}
		// 高考场景下只展示高校吧
		if baseData.Request.GetScene() == gaokaoScene {
			if collegeForumData == nil {
				tbcontext.WarningF(ctx, "collegeForumData is nil, fid = %d", data.GetForumId())
				continue
			}
			if collegeName, isCollege := collegeForumData[data.GetForumId()]; !isCollege || collegeName == "" {
				tbcontext.WarningF(ctx, "forum is no college, fid = %d", data.GetForumId())
				continue
			}
		}
		forumTabNum++
		tabs = append(tabs, &p.Tab{
			Type:   proto.String(tabTypeForum),
			Id:     proto.Int64(data.GetForumId()),
			Name:   proto.String(data.GetForumName()),
			SubTab: subTab,
		})
		existsMap[data.GetForumId()] = struct{}{}
	}

	for _, forum := range searchMachineQAForumData.GetData().GetRows() {
		if forumTabNum >= 15 {
			break
		}
		if _, ok := existsMap[forum.GetForumId()]; ok || (questionCountSuc && !hasQuestionForum[forum.GetForumId()]) {
			continue
		}
		// 高考场景下只展示高校吧
		if baseData.Request.GetScene() == gaokaoScene {
			if collegeForumData == nil {
				tbcontext.WarningF(ctx, "collegeForumData is nil, fid = %d", forum.GetForumId())
				continue
			}
			if collegeName, isCollege := collegeForumData[forum.GetForumId()]; !isCollege || collegeName == "" {
				tbcontext.WarningF(ctx, "forum is no college, fid = %d", forum.GetForumId())
				continue
			}
		}
		forumTabNum++
		tabs = append(tabs, &p.Tab{
			Type:   proto.String(tabTypeForum),
			Id:     proto.Int64(forum.GetForumId()),
			Name:   proto.String(forum.GetForumName()),
			SubTab: subTab,
		})
		existsMap[forum.GetForumId()] = struct{}{}
	}

	// 高考场景下，按照求助量获取吧信息后 如未满足15个吧tab，则展示默认展示的固定高校吧tab
	// 吧id由词表配置获取 不进行高校吧验证
	if len(gaokaoDefaultFids) > 0 && forumTabNum < 15 {
		for _, fid := range gaokaoDefaultFids {
			if forumTabNum >= 15 {
				break
			}
			if _, ok := existsMap[fid]; ok || (questionCountSuc && !hasQuestionForum[fid]) {
				continue
			}
			forumTabNum++
			forumName := historyForumInfos[uint32(fid)].GetForumName().GetForumName()
			tabs = append(tabs, &p.Tab{
				Type:   proto.String(tabTypeForum),
				Id:     proto.Int64(fid),
				Name:   proto.String(forumName),
				SubTab: subTab,
			})
			existsMap[fid] = struct{}{}
		}
	}

	return tabs, nil
}

func buildQuestionList(ctx context.Context, threadIDs []uint64, checkInfo map[uint64]ExtraInfo) ([]*p.Thread, error) {
	threadInput := &frs.MgetThreadReq{
		ThreadIds:         threadIDs,
		NeedAbstract:      proto.Uint32(1),
		NeedUserData:      proto.Uint32(1),
		NeedForumName:     proto.Uint32(1),
		NeedPostContent:   proto.Uint32(1),
		StructuredContent: proto.Uint32(1),
		CallFrom:          proto.String("client_frs"),
	}
	threadOutput := &frs.MgetThreadRes{}
	err := tbservice.Call(ctx, "post", "mgetThread", threadInput, threadOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || threadOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service post:mgetThread, input = %v, output = %v, err = %v", common.ToString(threadInput),
			common.ToString(threadOutput), err)
		return nil, errors.New("fail to call post:mgetThread")
	}

	// 获取悬赏金额
	bonusIDs := make([]int64, 0)
	var bonusTmoney map[int64]*cp.ThreadTmoney
	for _, thread := range threadOutput.GetOutput().GetThreadList() {
		bonusIDs = append(bonusIDs, int64(thread.GetThreadId()))
	}
	if len(bonusIDs) > 0 {
		input := &cp.MgetTmoneyByTidReq{
			Tids: bonusIDs,
		}
		output := &cp.MgetTmoneyByTidRes{}
		err := tbservice.Call(ctx, "common", "mgetTmoneyByTid", input, output,
			tbservice.WithConverter(tbservice.JSONITER), tbservice.WithRalName("common_go"))
		if err != nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service common:mgetTmoneyByTid, input = %v, output = %v, err = %v",
				common.ToString(input), common.ToString(output), err)
		}
		bonusTmoney = output.GetData().GetThreadTmoneyData()
	}

	list := make([]*p.Thread, 0)
	for _, id := range threadIDs {
		rawThread := threadOutput.GetOutput().GetThreadList()[id]
		rawUser := threadOutput.GetOutput().GetThreadUserList()[rawThread.GetUserId()]

		if rawThread == nil || rawUser == nil {
			tbcontext.WarningF(ctx, "fail to get thread or user info, id = %v", id)
			continue
		}

		if rawThread.GetIsDeleted() == 1 {
			tbcontext.WarningF(ctx, "thread is deleted, id = %v", id)
			continue
		}

		content, _ := rawThread.GetPostContentStruct()
		richAbstract, _ := buildRichAbstract(ctx, content)

		thread := &p.Thread{
			Id:           proto.Uint64(rawThread.GetThreadId()),
			Title:        proto.String(rawThread.GetTitle()),
			RichAbstract: richAbstract,
			Author: &client.User{
				NameShow: proto.String(getUserNameShow(rawUser)),
				Portrait: proto.String(tbportrait.Encode(rawUser.GetUserId(), rawUser.GetUserName(), 0)),
			},
			Fid:       proto.Uint64(uint64(rawThread.GetForumId())),
			Fname:     proto.String(rawThread.GetForumName()),
			ViewNum:   proto.Uint32(rawThread.GetFreqNum()),
			AgreeNum:  proto.Uint32(uint32(rawThread.GetAgreeNum())),
			AnswerNum: proto.Uint32(rawThread.GetPostNum()),
		}

		if value, ok := checkInfo[id]; ok {
			thread.IsCheck = proto.Uint32(value.IsChecked)
			thread.Answered = proto.Uint32(value.Answered)
		}

		if rawThread.GetRobotThreadType() == 42 {
			// 机器发的赏金贴
			thread.RewardThreadType = proto.String("questionBonusThread")
		} else {
			if tmoney, ok := bonusTmoney[int64(rawThread.GetThreadId())]; ok && tmoney != nil && tmoney.GetTmoney() > 0 {
				// 有悬赏ugc求助贴
				thread.RewardThreadType = proto.String("ugcBonusThread")
			} else {
				// 无悬赏ugc求助贴
				thread.RewardThreadType = proto.String("ugcQuestionThread")
			}
		}
		// 赏金贴额外下发赏金信息
		if tmoney, ok := bonusTmoney[int64(rawThread.GetThreadId())]; ok && tmoney != nil {
			thread.Reward = proto.Uint32(uint32(tmoney.GetTmoney()))
			thread.RewardStatus = proto.Uint32(0)
			if tmoney.GetRewardStatus() == 1 {
				thread.RewardStatus = proto.Uint32(1)
			}
		}

		list = append(list, thread)
	}

	return list, nil
}

func buildRichAbstract(ctx context.Context, structContent []*meta.PostStructContent) ([]*client.PbContent, error) {
	if len(structContent) == 0 {
		tbcontext.WarningF(ctx, "structContent is empty")
		return nil, nil
	}
	objCondition := tbrichtext.GetDefaultParserCondition()
	objCondition.NewLineCount = 1
	objCondition.BolParseBdhd = true
	objCondition.BolParsePhone = true
	objCondition.BolCheckSpamUrl = true
	// 涂鸦在低与7.3.0以下版本或ios端是否显示成图片
	objCondition.BolGraffitiToImg = true
	objParserStruct := &tbrichtext.ParserStructured{}
	// pb结构化后，解析content方式
	// 对content进行内容处理,包括参数的过滤和调整
	pspInput := &tbrichtext.ParserStructProcessInput{
		PObjCondition:  objCondition,
		BolEmoji:       true,
		ScreenWidth:    0,
		ScreenHeight:   0,
		ArrText:        structContent,
		BolIsAllOrigin: true,
		BolNeedTopic:   false,
	}
	parseRes, err := objParserStruct.Process(ctx, pspInput)
	pbContents := parseRes.ArrSlotContent
	if err != nil || len(pbContents) == 0 {
		tbcontext.WarningF(ctx, "parseRes err:%v", err)
		return nil, err
	}
	// 客户端和fe不一样，兼容
	for idx, item := range pbContents {
		// src没有鉴权串
		if len(item.GetOriginSrc()) > 0 {
			item.Src = item.OriginSrc
		}
		// 小表情，把表情链接补充上去
		if item.GetType() == 2 && len(structContent) > idx {
			cnt := structContent[idx]
			if cnt.GetClass() == "BDE_Smiley" && strings.Contains(cnt.GetSrc(), item.GetText()) {
				item.Src = proto.String(structContent[idx].GetSrc())
			}
		}
	}

	contents := make([]*client.PbContent, 0)
	err = common.StructAToStructBCtx(ctx, pbContents, &contents)
	if err != nil {
		tbcontext.WarningF(ctx, "StructAToStructB err=%v", err)
		return nil, err
	}

	return contents, nil
}

func getUserNameShow(userInfo *user.UserInfo) string {
	if userInfo == nil {
		return ""
	}

	nickNameV2 := userInfo.GetUserNicknameV2()
	if nickNameV2 != "" {
		return nickNameV2
	}

	nickName := userInfo.GetUserNickname()
	if nickName != "" {
		return nickName
	}

	userName := userInfo.GetUserName()
	return userName
}

// 判断是否展示高考tab
func getIsShowGaokaoTab(ctx context.Context) int {
	defaultShow := 0
	keys := []string{"gaokao_tab_time"}
	wordItems, err := wordserver.QueryItems(ctx, questionConfWordlistTableName, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to query wordlist, keys = %v", common.ToString(keys))
		return defaultShow
	}

	timeConf := wordItems["gaokao_tab_time"]
	if timeConf == "" {
		tbcontext.WarningF(ctx, "gaokao_tab_time is empty")
		return defaultShow
	}
	timeArr := strings.Split(timeConf, "|")
	if len(timeArr) != 2 {
		tbcontext.WarningF(ctx, "gaokao_tab_time format error")
		return defaultShow
	}
	// 获取时区
	location, _ := time.LoadLocation("Local")
	startTime, err1 := time.ParseInLocation("2006-01-02 15:04:05", timeArr[0], location)
	endTime, err2 := time.ParseInLocation("2006-01-02 15:04:05", timeArr[1], location)
	if err1 != nil || err2 != nil {
		// 如果时间解析出错，则默认不在时间范围内
		tbcontext.WarningF(ctx, "Failed to parse black user config time: %v, %v", err1, err2)
		return defaultShow
	}

	now := time.Now()
	if now.Before(startTime) || now.After(endTime) {
		tbcontext.WarningF(ctx, "user now_time: %v,start_time: %v,end_time: %v", now, startTime, endTime)
		return defaultShow
	}

	return 1
}

// 获取吧信息，包括是否有求助贴，高考场景下是否为高校吧， 历史记录的fid获取btx信息
func getForumData(ctx context.Context, scene uint32, fids []int64, historyFids map[int64]struct{}) (
	bool, map[int64]bool, map[int64]string, map[uint32]*forumProto.BtxInfo) {
	// 过滤没有求助贴的吧tab
	hasQuestionForum := make(map[int64]bool)
	questionCountSuc := true
	// 过滤高考场景下不是高校的吧tab
	collegeForumData := make(map[int64]string)
	historyForumInfos := make(map[uint32]*forumProto.BtxInfo)
	needQueryForumCollegeIds := make([][]int64, 0)
	if scene == 1 {
		needQueryForumCollegeIds = util.ChunkInt(fids, 30)
	}

	multi := tbservice.Multi()
	multi.Register(ctx, "mgetForumQuestionCount", &tbservice.Parameter{
		Service: "common",
		Method:  "mgetForumQuestionCount",
		Input: &cp.MgetForumQuestionCountReq{
			Fids: fids,
		},
		Output: &cp.MgetForumQuestionCountRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
			tbservice.WithRalName("common_go"),
		},
	})

	if len(historyFids) > 0 { // 历史记录的fid，需要获取btx信息
		newFidArrInt32 := make([]uint32, 0, len(historyFids))
		for fid := range historyFids {
			newFidArrInt32 = append(newFidArrInt32, uint32(fid))
		}
		multi.Register(ctx, "mgetBtxInfoEx", &tbservice.Parameter{
			Service: "forum",
			Method:  "mgetBtxInfoEx",
			Input: &forumProto.MgetBtxInfoExReq{
				ForumId: newFidArrInt32,
			},
			Output: &forumProto.MgetBtxInfoExRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		})
	}
	if scene == 1 {
		for key, forumIds := range needQueryForumCollegeIds {
			keyStr := fmt.Sprintf("mgetForumCollegeByFIDs_%d", key)
			multi.Register(ctx, keyStr, &tbservice.Parameter{
				Service: "common",
				Method:  "mgetForumCollegeByFIDs",
				Input: &cp.MgetForumCollegeByFIDsReq{
					ForumId: forumIds,
				},
				Output: &cp.MgetForumCollegeByFIDsRes{},
				Option: []tbservice.Option{
					tbservice.WithConverter(tbservice.JSONITER),
					tbservice.WithRalName("common_go"),
				},
			})
		}
	}
	multi.Call(ctx)
	mgetForumQuestionCountInter, err := multi.GetResult(ctx, "mgetForumQuestionCount")
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service common:mgetForumQuestionCount, input_fids = %v, err = %v",
			common.ToString(fids), err)
		questionCountSuc = false
	} else {
		mgetForumQuestionCountData, ok := mgetForumQuestionCountInter.(*cp.MgetForumQuestionCountRes)
		if !ok || mgetForumQuestionCountData.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service common:mgetForumQuestionCount, input_fids = %v, output = %v, err = %v",
				common.ToString(fids), common.ToString(mgetForumQuestionCountData), err)
			questionCountSuc = false
		} else {
			for fid, data := range mgetForumQuestionCountData.GetData() {
				if data.GetCount() > 0 {
					hasQuestionForum[fid] = true
				}
			}
		}
	}

	if len(historyFids) > 0 {
		mgetBtxInfoExInter, err := multi.GetResult(ctx, "mgetBtxInfoEx")
		if err != nil {
			tbcontext.WarningF(ctx, "fail to call service forum:mgetBtxInfoEx, input_fids = %v, err = %v",
				common.ToString(historyFids), err)
		} else {
			mgetBtxInfoExData, ok := mgetBtxInfoExInter.(*forumProto.MgetBtxInfoExRes)
			if !ok || mgetBtxInfoExData.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "fail to call service forum:mgetBtxInfoEx, input_fids = %v, output = %v, err = %v",
					common.ToString(historyFids), common.ToString(mgetBtxInfoExData), err)
			} else {
				historyForumInfos = mgetBtxInfoExData.GetOutput()
			}
		}
	}

	if scene == 1 {
		for key, forumIds := range needQueryForumCollegeIds {
			keyStr := fmt.Sprintf("mgetForumCollegeByFIDs_%d", key)
			mgetForumCollegeByFIDsInter, err := multi.GetResult(ctx, keyStr)
			if err != nil {
				tbcontext.WarningF(ctx, "fail to call service common:mgetForumCollegeByFIDs, input_fids = %v, err = %v",
					common.ToString(forumIds), err)
			} else {
				mgetForumCollegeByFIDsData, ok := mgetForumCollegeByFIDsInter.(*cp.MgetForumCollegeByFIDsRes)
				if !ok || mgetForumCollegeByFIDsData.GetErrno() != tiebaerror.ERR_SUCCESS {
					tbcontext.WarningF(ctx, "fail to call service common:mgetForumCollegeByFIDs, input_fids = %v, output = %v, err = %v",
						common.ToString(forumIds), common.ToString(mgetForumCollegeByFIDsData), err)
				} else {
					for forumId, college := range mgetForumCollegeByFIDsData.GetData() {
						collegeForumData[forumId] = college
					}
				}
			}
		}
	}
	tbcontext.TraceF(ctx, "questionCountSuc = %v, hasQuestionForum = %v, collegeForumData = %v, historyForumInfos = %v",
		questionCountSuc, common.ToString(hasQuestionForum), common.ToString(collegeForumData), common.ToString(historyForumInfos))
	return questionCountSuc, hasQuestionForum, collegeForumData, historyForumInfos
}
