package question

import (
	"context"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/question/guide"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	WordListGuidePic    = "tb_wordlist_redis_question"
	QuestionGuidePicKey = "question_guide_picture"
)

func Guide(ctx context.Context, baseData *types.CGuideBaseData, response *guide.GuideResIdl) int {
	wordRes, err := wordserver.QueryKey(ctx, WordListGuidePic, QuestionGuidePicKey)
	if err != nil {
		tbcontext.WarningF(ctx, "wordserver query fail: %v", err)
		return tiebaerror.ERROR_MIS_WORDLIST_GET_TABLE_CONTENTS_FAIL
	}
	tbcontext.WarningF(ctx, "wordserver query success, input=%s, output=%v", common.ToString(QuestionGuidePicKey), wordRes)

	// 将词表内容转成数组形式
	picList := []string{}
	err = jsoniter.UnmarshalFromString(wordRes, &picList)
	if err != nil {
		tbcontext.WarningF(ctx, "json unmarshal fail, input=%s, err=%v", common.ToString(QuestionGuidePicKey), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	// 返回值赋值
	response.GetData().ModuleList = picList

	return tiebaerror.ERR_SUCCESS
}
