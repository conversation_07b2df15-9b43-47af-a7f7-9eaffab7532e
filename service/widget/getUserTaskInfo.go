package widget

import (
	"context"
	"errors"
	"math/rand"
	"sort"
	"time"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/god"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/msgpush"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	userProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	getUserTaskInfoProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/widget/getUserTaskInfo"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	DoTaskStatusNoperm    = -1                              // 做任务状态，无权限做任务
	DoTaskStatusNonactive = 0                               // 做任务状态，未激活
	DoTaskStatusNonfinish = 1                               // 做任务状态，未完成
	DoTaskStatusFinish    = 2                               // 做任务状态，已完成
	ConfigTableName       = "tb_wordlist_redis_widget_conf" // 词表名称
)

type AwardInfo struct {
	Name  string `json:"name,omitempty"`
	Award string `json:"award,omitempty"`
	Icon  string `json:"icon,omitempty"`
	Type  int    `json:"type,omitempty"`
	Pic   string `json:"pic,omitempty"`
	Num   int    `json:"num,omitempty"`
}

// GetUserTaskInfo 获取用户未完成任务信息
// @Param：ctx: 上下文对象
// @Param：baseData: 输入数据
// @Param：response: 输出数据
// @Return：int: 错误码
func GetUserTaskInfo(ctx context.Context, baseData *types.GetUserTaskInfoBaseData, response *getUserTaskInfoProto.GetUserTaskInfoResIdl) int {
	// 获取用户ID
	userID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	clientType := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	clientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	cuid := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
	if userID <= 0 {
		response.Data = &getUserTaskInfoProto.GetUserTaskInfoData{
			IsLogin: proto.Int32(0),
		}
		// 未登录还要返回对应的物料
		response.Data.WidgetInfo = getUserTaskWidgetConf(ctx, "task_widget_info_not_login")
		return tiebaerror.ERR_SUCCESS
	}
	err := getUserBaseInfo(ctx, baseData)
	if err != nil {
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 调用 common::getUserGrowthTaskList
	isTiebaVIP := 0
	if len(baseData.UserDataInfo.GetUserInfo()) > 0 {
		if baseData.UserDataInfo.GetUserInfo()[0].GetVipInfo().GetVStatus() != 0 &&
			baseData.UserDataInfo.GetUserInfo()[0].GetVipInfo().GetETime() >= time.Now().Unix() {
			isTiebaVIP = 1
		}
	}
	getUserGrowthTaskListReq := map[string]any{
		"user_id":               userID,
		"cuid":                  cuid,
		"need_target_scheme":    1,
		"client_type":           clientType,
		"client_version":        clientVersion,
		"call_from":             "client",
		"need_check_pre_valid":  1,
		"need_check_show_vaild": 1,
		"data": map[string]any{
			"is_tieba_vip":  isTiebaVIP,
			"task_done_num": 10000000, //取1个特别大的值、前置判断需要
		},
		"need_page_sign_progress": 1,
	}
	getUserGrowthTaskListRes := &commonProto.GetUserGrowthTaskListRes{}
	err = tbservice.Call(ctx, "common", "getUserGrowthTaskList", getUserGrowthTaskListReq, &getUserGrowthTaskListRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil {
		tbcontext.WarningF(ctx, "getUserTaskInfo call common::getUserGrowthTaskList fail, err=[%v]", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	if getUserGrowthTaskListRes.Errno == nil || getUserGrowthTaskListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "getUserTaskInfo call common::getUserGrowthTaskList fail, err=[%v], output=[%s]", err, common.ToString(getUserGrowthTaskListRes))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	dailyTaskList := getUserGrowthTaskListRes.GetData().GetDailyTask()
	filterTaskForWidget(ctx, baseData, dailyTaskList)

	// 获取小组件配置
	key := "task_widget_info_not_finish"
	if len(baseData.UserTaskList) == 0 {
		key = "task_widget_info_finished"
	}
	widgetConf := getUserTaskWidgetConf(ctx, key)

	// 如果配置里面的背景图片大于一张，随机取一个，根据用户id和时间戳生成一个固定的种子，每小时变一次
	rangeIndex := 0
	if len(widgetConf.GetBgUrlList()) > 1 {
		hour := time.Now().Hour()
		seed := userID + int64(hour)
		rand.Seed(seed)
		rangeIndex = rand.Intn(len(widgetConf.GetBgUrlList()))
	}
	if rangeIndex < len(widgetConf.GetBgUrlList()) {
		widgetConf.BgUrlList = []*getUserTaskInfoProto.BackgroundInfo{
			widgetConf.GetBgUrlList()[rangeIndex],
		}
	}
	if rangeIndex < len(widgetConf.GetBubblesUrlList()) {
		widgetConf.BubblesUrlList = []*getUserTaskInfoProto.BackgroundInfo{
			widgetConf.GetBubblesUrlList()[rangeIndex],
		}
	}

	// 构建返回的任务列表
	outTaskList := make([]*getUserTaskInfoProto.TaskInfo, 0, len(baseData.UserTaskList))
	for _, taskInfo := range baseData.UserTaskList {
		taskExt := common.JSONToMap(taskInfo.GetExt())
		weight := *taskInfo.Weight
		taskAwardInfo, ok := taskExt["award_info"]
		if ok {
			weight = 0
			var awardInfoJson []AwardInfo
			err = jsoniter.UnmarshalFromString(common.ToString(taskAwardInfo), &awardInfoJson)
			if err != nil {
				tbcontext.WarningF(ctx, "unmarshal award_info fail, taskId=[%d], ext=[%s],err:=[%v]", taskInfo.GetId(), taskInfo.GetExt(), err)
				continue
			}
			for _, oneAward := range awardInfoJson {
				if oneAward.Type == 0 {
					weight += int32(oneAward.Num)
				}
			}
		}
		outTaskList = append(outTaskList, &getUserTaskInfoProto.TaskInfo{
			Name: taskInfo.Name,
			Exp:  &weight,
			Id:   taskInfo.Id,
		})
	}

	outData := &getUserTaskInfoProto.GetUserTaskInfoData{
		UserInfo: &getUserTaskInfoProto.UserInfo{
			GrowthValue: proto.Int64(int64(baseData.UserGrowthScore)),
			TmoneyValue: proto.Int64(int64(baseData.UserTmoneyRemain)),
		},
		WidgetInfo: widgetConf,
		TaskList:   outTaskList,
		IsLogin:    proto.Int32(1),
	}
	response.Data = outData
	return tiebaerror.ERR_SUCCESS
}

// 过滤任务列表
func filterTaskForWidget(ctx context.Context, baseData *types.GetUserTaskInfoBaseData, taskList []*commonProto.UserGrowthTask) {
	isGod := 0
	if len(baseData.UserDataInfo.GetUserInfo()) > 0 {
		if godInfoSt, err := baseData.UserDataInfo.GetUserInfo()[0].GetGodInfoStruct(); err == nil {
			godInfo := god.GetUserGodInfo(godInfoSt)
			if godInfo.GetFieldId() > 0 && godInfo.GetStatus() == god.STATUS_RECEIVE {
				isGod = 1
			}
		}
	}
	undoTaskList := []*commonProto.UserGrowthTask{}
	addWidgetTask := &commonProto.UserGrowthTask{}
	for _, taskInfo := range taskList {
		if taskInfo.GetActType() == "add_widget" {
			addWidgetTask = taskInfo
		}
		if taskInfo.GetDotaskStatus() != DoTaskStatusFinish && taskInfo.GetDotaskStatus() != DoTaskStatusNoperm {
			undoTaskList = append(undoTaskList, taskInfo)
		}
	}
	if len(undoTaskList) == 0 {
		return
	}

	// 任务排序
	sortTaskList := []*types.SortTask{}
	for _, taskInfo := range undoTaskList {
		taskExt := &types.TaskExt{}
		if taskInfo.GetExt() != "" {
			err := jsoniter.UnmarshalFromString(taskInfo.GetExt(), &taskExt)
			if err != nil {
				tbcontext.WarningF(ctx, "unmarshal ext fail, taskId=[%d], ext=[%s],err:=[%v]", taskInfo.GetId(), taskInfo.GetExt(), err)
				continue
			}
		}
		// 如果有配置了展示名称，就用配置的
		if taskExt.WidgetExt.WidgetShowName != "" {
			taskInfo.Name = proto.String(taskExt.WidgetExt.WidgetShowName)
		}
		// 不是大神时，过滤掉大神任务
		if taskInfo.GetActType() == "set_god" && isGod == 0 {
			continue
		}

		// 如果打开了push开关，就不下发“打开push开关”任务
		if taskInfo.GetActType() == "open_push_switch" && baseData.Request.GetPushSwitch() == 1 && baseData.UserPushSwitch == 1 {
			continue
		}

		// 签到任务单独处理下分数
		if taskInfo.GetActType() == "page_sign" {
			progress := 0
			if taskInfo.PageSignProgress != nil {
				//获取进度
				progress = int(taskInfo.PageSignProgress.GetProgress())
			}

			//不存在则归0

			if progress >= len(taskExt.Scores) { //超过长度
				progress = 0 //重置为0，取第一天数据
			}

			//防止取不到配置异常的情况，只改能取到的，默认配置的是weight
			if _, ok := taskExt.Scores[int(progress+1)]; ok {
				newExp := taskExt.Scores[int(progress+1)]
				taskInfo.Weight = proto.Int32(int32(newExp))
			}
		}
		// 每日访问组件任务只有在添加组件任务完成后的第二天之后才出
		if taskInfo.GetActType() == "enter_by_widget" && addWidgetTask != nil && addWidgetTask.GetDotaskStatus() == DoTaskStatusFinish {
			now := time.Now()
			year, month, day := now.Date()
			// 创建今天0点0分的时间对象
			today := time.Date(year, month, day, 0, 0, 0, 0, now.Location())
			if today.Unix() > addWidgetTask.GetCompleteTime() {
				sortTaskList = append(sortTaskList, &types.SortTask{
					Task:      taskInfo,
					SortValue: taskExt.WidgetExt.WidgetShowSort,
				})
			}
		} else if taskInfo.GetActType() != "enter_by_widget" {
			sortTaskList = append(sortTaskList, &types.SortTask{
				Task:      taskInfo,
				SortValue: taskExt.WidgetExt.WidgetShowSort,
			})
		}
	}

	// 按照配置的顺序排序
	sort.Slice(sortTaskList, func(i int, j int) bool {
		return sortTaskList[i].SortValue > sortTaskList[j].SortValue
	})

	// 只获取前两个任务
	// showcase后改成一个任务了
	for i := 0; i < 1 && i < len(sortTaskList); i++ {
		baseData.UserTaskList = append(baseData.UserTaskList, sortTaskList[i].Task)
	}
}

// 获取基础信息
func getUserBaseInfo(ctx context.Context, baseData *types.GetUserTaskInfoBaseData) error {
	userID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	// 并行调用下游接口
	multi := tbservice.Multi()
	// 调用 user::getUserGrowthScore
	getUserGrowthScoreReq := map[string]any{
		"user_id": userID,
	}
	getUserGrowthScoreRes := &userProto.GetUserGrowthScoreRes{}
	multi.Register(ctx, "getUserGrowthScore", &tbservice.Parameter{
		Service: "user",
		Method:  "getUserGrowthScore",
		Input:   getUserGrowthScoreReq,
		Output:  getUserGrowthScoreRes,
	})

	// 调用 common::getUserTmoney
	getUserTmoneyReq := map[string]any{
		"user_id": userID,
	}
	getUserTmoneyRes := &commonProto.GetUserTmoneyRes{}
	multi.Register(ctx, "getUserTmoney", &tbservice.Parameter{
		Service: "common",
		Method:  "getUserTmoney",
		Input:   getUserTmoneyReq,
		Output:  getUserTmoneyRes,
	})

	// 调用 common::getPageSignProgress
	// getPageSignProgressReq := map[string]any{
	// 	"user_id": userID,
	// }
	// getPageSignProgressRes := &commonProto.GetPageSignProgressRes{}
	// multi.Register(ctx, "getPageSignProgress", &tbservice.Parameter{
	// 	Service: "common",
	// 	Method:  "getPageSignProgress",
	// 	Input:   getPageSignProgressReq,
	// 	Output:  getPageSignProgressRes,
	// })

	getUserDataReq := map[string]any{
		"user_id": userID,
	}
	getUserDataRes := &userProto.GetUserDataRes{}
	multi.Register(ctx, "getUserData", &tbservice.Parameter{
		Service: "user",
		Method:  "getUserData",
		Input:   getUserDataReq,
		Output:  getUserDataRes,
	})

	if baseData.Request.GetPushSwitch() == 1 {
		msgpushInput := map[string]interface{}{
			"user_id": userID,
		}
		msgpushRes := &msgpush.GetUserMaskStatusRes{}
		multi.Register(ctx, "getUserMaskStatus", &tbservice.Parameter{
			Service: "msgpush",
			Method:  "getUserMaskStatus",
			Input:   msgpushInput,
			Output:  msgpushRes,
		})
	}

	// 执行并行调用
	multi.Call(ctx)
	// 获取用户成长值分数
	getUserGrowthScoreResInt, err := multi.GetResult(ctx, "getUserGrowthScore")
	if err != nil {
		tbcontext.WarningF(ctx, "getUserTaskInfo call user:getUserGrowthScore fail, err=[%v]", err)
		return errors.New("call user::getUserGrowthScore fail")
	}
	getUserGrowthScoreRes, ok := getUserGrowthScoreResInt.(*user.GetUserGrowthScoreRes)
	if !ok || getUserGrowthScoreRes.Errno == nil || getUserGrowthScoreRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "getUserTaskInfo call user:getUserGrowthScore fail, err=[%v], uid=[%d], output=[%s]",
			err, userID, common.ToString(getUserGrowthScoreRes))
		return errors.New("call user::getUserGrowthScore fail")
	}
	baseData.UserGrowthScore = getUserGrowthScoreRes.GetUserGrowthScore()

	// 获取用户贴贝数量
	getUserTmoneyResInt, err := multi.GetResult(ctx, "getUserTmoney")
	if err != nil {
		tbcontext.WarningF(ctx, "getUserTaskInfo call common:getUserTmoney fail, err=[%v]", err)
		return errors.New("call common::getUserTmoney fail")
	}
	getUserTmoneyRes, ok = getUserTmoneyResInt.(*commonProto.GetUserTmoneyRes)
	if !ok || getUserTmoneyRes.Errno == nil || getUserTmoneyRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "getUserTaskInfo call common:getUserTmoney fail, err=[%v], uid=[%d], output=[%s]",
			err, userID, common.ToString(getUserTmoneyRes))
		return errors.New("call common::getUserTmoney fail")
	}
	baseData.UserTmoneyRemain = getUserTmoneyRes.GetData().GetRemainingNum()

	// 获取用户连续签到进度
	// getPageSignProgressResInt, err := multi.GetResult(ctx, "getPageSignProgress")
	// if err != nil {
	// 	tbcontext.WarningF(ctx, "getUserTaskInfo call common:getUserTmoney fail, err=[%v]", err)
	// 	return errors.New("call common::getPageSignProgress fail")
	// }
	// getPageSignProgressRes, ok = getPageSignProgressResInt.(*commonProto.GetPageSignProgressRes)
	// if !ok || getPageSignProgressRes.Errno == nil || getPageSignProgressRes.GetErrno() != tiebaerror.ERR_SUCCESS {
	// 	tbcontext.WarningF(ctx, "getUserTaskInfo call common:getPageSignProgress fail, err=[%v], uid=[%d], output=[%s]",
	// 		err, userID, common.ToString(getPageSignProgressRes))
	// 	return errors.New("call common::getPageSignProgress fail")
	// }
	// baseData.UserPageSignInfo = getPageSignProgressRes.GetData()

	// 获取用户基础信息
	getUserDataResInt, err := multi.GetResult(ctx, "getUserData")
	if err != nil {
		tbcontext.WarningF(ctx, "getUserTaskInfo call user:getUserData fail, err=[%v]", err)
		return errors.New("call user::getUserData fail")
	}
	getUserDataRes, ok = getUserDataResInt.(*userProto.GetUserDataRes)
	if !ok || getUserDataRes.Errno == nil || getUserDataRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "getUserTaskInfo call user:getUserData fail, err=[%v], uid=[%d], output=[%s]",
			err, userID, common.ToString(getUserDataRes))
		return errors.New("call user::getUserData fail")
	}
	baseData.UserDataInfo = getUserDataRes

	// 获取用户端内push开关
	if baseData.Request.GetPushSwitch() == 1 {
		msgpushResInt, err := multi.GetResult(ctx, "getUserMaskStatus")
		if err != nil {
			tbcontext.WarningF(ctx, "getUserTaskInfo call msgpush:getUserMaskStatus fail, err=[%v]", err)
			return errors.New("call msgpush:getUserMaskStatus fail")
		}
		msgpushRes, ok := msgpushResInt.(*msgpush.GetUserMaskStatusRes)
		if !ok || msgpushRes.Errno == nil || msgpushRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "getUserTaskInfo call msgpush:getUserMaskStatus fail, err=[%v], uid=[%d], output=[%s]",
				err, userID, common.ToString(msgpushResInt))
			return errors.New("call msgpush:getUserMaskStatus")
		}

		status := common.Tvttt(msgpushRes.GetOutput().GetMessageSwitch(), common.TTT_INT64).(int64)
		// 官方推送开关最低位 1:关闭 0:打开 -1: 默认状态开
		if status > 0 && status&1 == 1 {
			baseData.UserPushSwitch = 0
		} else {
			baseData.UserPushSwitch = 1
		}
	}

	return nil
}

func getUserTaskWidgetConf(ctx context.Context, keyName string) *getUserTaskInfoProto.WidgetInfo {
	// todo 不同形态用不同的默认conf，当前只给了未登录态的，先写进去
	defautWidgetInfo := &getUserTaskInfoProto.WidgetInfo{
		Title:    proto.String("百度贴吧"),
		Logo:     proto.String("https://tieba-ares.cdn.bcebos.com/mis/2024-12/1735620566364/3102163793c1.png"),
		SubTitle: proto.String("登录领贴贝啦!"),
		BgUrlList: []*getUserTaskInfoProto.BackgroundInfo{
			{
				Day:   proto.String("https://tieba-ares.cdn.bcebos.com/mis/2024-12/1735620227839/c550a70f4033.png"),
				Night: proto.String("https://tieba-ares.cdn.bcebos.com/mis/2024-12/1735620227839/c550a70f4033.png"),
			},
		},
	}
	resData, err := wordserver.QueryKey(ctx, ConfigTableName, keyName)
	if err != nil || resData == "" {
		tbcontext.WarningF(ctx, "word server query key error: %v, key:%s", err, keyName)
		return defautWidgetInfo
	}

	widgetConf := &getUserTaskInfoProto.WidgetInfo{}
	err = jsoniter.UnmarshalFromString(resData, &widgetConf)
	if err != nil {
		tbcontext.WarningF(ctx, "word server query key error: %v, data:[%s], key:%s", err, resData, keyName)
		return defautWidgetInfo
	}
	return widgetConf
}
