package widget

import (
	"context"
	"math/rand"
	"net/url"
	"strconv"
	"time"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/ip"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbstring"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	// 项目自身库
	forumProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	permProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	signProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/sign"
	widgetProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/widget/forumInfo"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// GetForumInfo 获取用户吧信息
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：baseData：输入数据
// @Param：response：输出数据
// @Return：返回值是int类型，表示执行是否成功
func GetForumInfo(ctx context.Context, baseData *types.GetForumInfoBaseData, response *widgetProto.GetForumInfoResIdl) int {
	// 获取用户ID和吧ID
	objReq := baseData.BaseObj.ObjRequest
	userID := common.Tvttt(objReq.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	forumID := baseData.Request.GetForumId()
	if userID <= 0 {
		response.Data = &widgetProto.GetForumInfoRes{
			IsLogin: proto.Int32(0),
		}
		response.Data.WidgetInfo = getForumTaskWidgetConf(ctx, "widget_forum_not_login")
		tbcontext.WarningF(ctx, "user not login!")
		return tiebaerror.ERR_SUCCESS
	}
	if forumID <= 0 {
		response.Data = &widgetProto.GetForumInfoRes{
			IsLogin:  proto.Int32(1),
			HasForum: proto.Int32(0),
		}
		tbcontext.WarningF(ctx, "forumID:%d is not valid!", forumID)
		return tiebaerror.ERR_SUCCESS
	}

	// 定义并行调用对象
	multi := tbservice.Multi()

	// 调用forum::mgetBtxInfoEx查询吧信息
	mgetBtxInfoExReq := &forumProto.MgetBtxInfoExReq{
		ForumId: []uint32{uint32(forumID)},
	}
	mgetBtxInfoExRes := &forumProto.MgetBtxInfoExRes{}
	multi.Register(ctx, "mgetBtxInfoEx", &tbservice.Parameter{
		Service: "forum",
		Method:  "mgetBtxInfoEx",
		Input:   mgetBtxInfoExReq,
		Output:  mgetBtxInfoExRes,
	})

	// 调用perm::getPerm查询用户在当前吧的等级
	getPermInput := map[string]interface{}{
		"user_id":  userID,
		"forum_id": forumID,
	}
	ipInt := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("ip_int", 0), common.TTT_UINT32).(uint32)
	ipVersion := ip.GetIPVersion(ip.Long2IP(ipInt))
	if ipVersion == ip.IPV6 {
		getPermInput["user_ip6"] = ipInt
	} else {
		getPermInput["user_ip"] = ipInt
	}
	getPermRes := &permProto.GetPermRes{}
	multi.Register(ctx, "getPerm", &tbservice.Parameter{
		Service: "perm",
		Method:  "getPerm",
		Input:   getPermInput,
		Output:  getPermRes,
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	})

	// 调用sign::getUserSignForums查询用户在当前吧的签到状态
	getUserSignForumsReq := &signProto.GetUserSignForumsReq{
		ForumId: []uint64{forumID},
		UserId:  proto.Uint64(userID),
	}
	getUserSignForumsRes := &signProto.GetUserSignForumsRes{}
	multi.Register(ctx, "getUserSignForums", &tbservice.Parameter{
		Service: "sign",
		Method:  "getUserSignForums",
		Input:   getUserSignForumsReq,
		Output:  getUserSignForumsRes,
	})

	// 执行并行调用
	multi.Call(ctx)

	// 获取并行调用结果
	mgetBtxInfoExResult, err := multi.GetResult(ctx, "mgetBtxInfoEx")
	if err != nil || mgetBtxInfoExResult == nil || mgetBtxInfoExResult.(*forumProto.MgetBtxInfoExRes).GetErrno() != 0 {
		tbcontext.WarningF(ctx, "call forum:mgetBtxInfoEx fail, err:%v, mgetBtxInfoExResult:%s", err, common.ToString(mgetBtxInfoExResult))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	getUserSignForumsResult, err := multi.GetResult(ctx, "getUserSignForums")
	if err != nil || getUserSignForumsResult == nil || getUserSignForumsResult.(*signProto.GetUserSignForumsRes).GetErrno() != 0 {
		tbcontext.WarningF(ctx, "call sign:getUserSignForums fail, err:%v, getUserSignForumsResult:%s", err, common.ToString(getUserSignForumsResult))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	getPermResult, err := multi.GetResult(ctx, "getPerm")
	if err != nil || getPermResult == nil || getPermResult.(*permProto.GetPermRes).GetErrno() != 0 {
		tbcontext.WarningF(ctx, "call perm:getPerm fail, err:%v, getPermResult:%s", err, common.ToString(getPermResult))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	forumInfoRes, ok := mgetBtxInfoExResult.(*forumProto.MgetBtxInfoExRes)
	if !ok || forumInfoRes.Errno == nil || forumInfoRes.GetErrno() != 0 {
		tbcontext.WarningF(ctx, "call forum:mgetFOrumBtxInfoEx fail, err:%v, mgetFOrumBtxInfoEx:%s", err, common.ToString(mgetBtxInfoExResult))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	forumInfo, ok := forumInfoRes.GetOutput()[uint32(forumID)]
	if !ok || forumInfo == nil || forumInfo.Card == nil || forumInfo.ForumName == nil {
		tbcontext.WarningF(ctx, "forumInfo is nil")
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	signInfo := getUserSignForumsResult.(*signProto.GetUserSignForumsRes).GetArrUserInfo()[forumID]
	if signInfo == nil {
		tbcontext.WarningF(ctx, "signInfo is nil")
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	permInfo := getPermResult.(*permProto.GetPermRes).GetOutput().GetGrade()

	frsSchema := "tiebaapp://router/portal?params=" + tbstring.UrlEncode(
		`{"page":"frs/frs","pageParams":{"forumName":"`+forumInfo.GetForumName().GetForumName()+`","fromWidget":1}}`)

	key := "widget_forum_is_sign"
	if signInfo.GetIsSignIn() == 0 {
		// 未签到
		key = "widget_forum_not_sign"
	}
	widgetConf := getForumTaskWidgetConf(ctx, key)
	// 如果配置里面的背景图片大于一张，随机取一个，根据用户id和时间戳生成一个固定的种子，每小时变一次
	rangeIndex := 0
	if len(widgetConf.GetBgUrlList()) > 1 {
		hour := time.Now().Hour()
		seed := int64(userID) + int64(hour)
		rand.Seed(seed)
		rangeIndex = rand.Intn(len(widgetConf.GetBgUrlList()))
	}
	if rangeIndex < len(widgetConf.GetBgUrlList()) {
		widgetConf.BgUrlList = []*widgetProto.WidgetPicInfo{
			widgetConf.GetBgUrlList()[rangeIndex],
		}
	}
	if rangeIndex < len(widgetConf.GetSubTitle()) {
		widgetConf.SubTitle = []string{
			widgetConf.GetSubTitle()[rangeIndex],
		}
	}

	// jumpUrl 拼接当前选定的forumId
	if widgetConf.GetJumpUrl() != "" {
		parsedURL, err := url.Parse(widgetConf.GetJumpUrl())
		if err != nil {
			tbcontext.WarningF(ctx, "解析URL时出错: %s", widgetConf.GetJumpUrl())
			widgetConf.JumpUrl = proto.String("")
		} else {
			queryParams := parsedURL.Query()
			queryParams.Add("forumId", strconv.Itoa(int(forumID)))
			parsedURL.RawQuery = queryParams.Encode()
			widgetConf.JumpUrl = proto.String(parsedURL.String())
		}
	}

	// 拼装返回结果
	response.Data = &widgetProto.GetForumInfoRes{
		ForumInfo: &widgetProto.WidgetForumInfo{
			IsLike:    permInfo.IsLike,
			IsSign:    proto.Uint32(uint32(*signInfo.IsSignIn)),
			LevelId:   permInfo.LevelId,
			LevelName: permInfo.LevelName,
			Avatar:    forumInfo.Card.Avatar,
			ForumName: forumInfo.ForumName.ForumName,
			Schema:    &frsSchema,
			ForumId:   &forumID,
		},
		WidgetInfo: widgetConf,
		IsLogin:    proto.Int32(1),
		HasForum:   proto.Int32(1),
	}

	return tiebaerror.ERR_SUCCESS
}

func getForumTaskWidgetConf(ctx context.Context, keyName string) *widgetProto.WidgetInfo {
	// todo 不同形态用不同的默认conf，当前只给了未登录态的，先写进去
	defautWidgetInfo := &widgetProto.WidgetInfo{
		Title:    proto.String("百度贴吧"),
		Logo:     proto.String("https://tieba-ares.cdn.bcebos.com/mis/2024-12/1735620566364/3102163793c1.png"),
		SubTitle: []string{"Hi~快来登录吧!"},
		BgUrlList: []*widgetProto.WidgetPicInfo{
			{
				Day:   proto.String("https://tieba-ares.cdn.bcebos.com/mis/2024-12/1735627252954/434b96119b92.png"),
				Night: proto.String("https://tieba-ares.cdn.bcebos.com/mis/2024-12/1735627252954/434b96119b92.png"),
			},
		},
	}
	resData, err := wordserver.QueryKey(ctx, ConfigTableName, keyName)
	if err != nil || resData == "" {
		tbcontext.WarningF(ctx, "word server query key error: %v, key:%s", err, keyName)
		return defautWidgetInfo
	}

	widgetConf := &widgetProto.WidgetInfo{}
	err = jsoniter.UnmarshalFromString(resData, &widgetConf)
	if err != nil {
		tbcontext.WarningF(ctx, "word server query key error: %v, data:[%s], key:%s", err, resData, keyName)
		return defautWidgetInfo
	}
	return widgetConf
}
