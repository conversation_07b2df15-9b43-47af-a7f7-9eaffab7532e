package core

import (
	"context"
	"errors"
	"fmt"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"net/http"
	"net/url"
	"sort"
	"strings"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/imgcdn"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/photofrsnew"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/threadtype"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tools/encode"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type PicAlbum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("picpage_album", func() engine.Job {
		return &PicAlbumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPicAlbum(ctx *engine.Context) *PicAlbum {
	return &PicAlbum{
		ctx: ctx,
	}
}
func (a *PicAlbum) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {
	staticField := baseData.StaticField
	if staticField.IsBjh == 1 || staticField.ArrThreadInfo.GetThreadTypes()>>32 != threadtype.PIC_ALBUM {
		return false
	}

	return true
}
func (a *PicAlbum) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField

	err := getPicAlbum(ctx, baseData)
	if err != nil {
		return err
	}

	for _, pic := range staticField.ArrPicAlbum {
		if staticField.IntIsImgCDN == 1 {
			spec := &imgcdn.ProcPic{
				ScreenWidth: staticField.IntScreenWidth,
				ScreenHeigh: staticField.IntScreenHeight,
				ScreenDip:   staticField.IntScreenDIP,
				QType:       staticField.IntQType,
			}

			imgcdn.ProcPicPageNewPicUrl(pic, true, spec, staticField.IntPicStrategyType)
		}

		staticField.ArrPicList = append(staticField.ArrPicList, pic)
	}

	return nil
}

type PicAlbumOperator struct {
}

func (rdop *PicAlbumOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewPicAlbum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "picabulm execute fail: %v", err)
		return err
	}

	return nil
}

func getPicAlbum(ctx context.Context, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField
	seeLz := "0"
	if staticField.IntNotSeeLz == 0 {
		seeLz = "1"
	}

	phoneType := "ios"
	if staticField.IntClientType == clientvers.CLIENT_TYPE_ANDROID {
		phoneType = "android"
	}

	httpIP := common.CreateHttpIP(ctx, baseData.BaseObj.Req)
	ip := httpIP.GetConnectIP("0.0.0.0", true)

	secureKey := common.Tvttt(baseData.BaseObj.ObjRequest.GetStrategy("commit_secure_key", ""), common.TTT_STRING).(string)
	openId := common.Tvttt(baseData.BaseObj.ObjRequest.GetStrategy("commit_open_id", ""), common.TTT_STRING).(string)

	input := url.Values{}
	input.Set("forum_id", common.Tvttt(staticField.IntFid, common.TTT_STRING).(string))
	input.Set("kw", encode.Utf8ToGbkString(staticField.StrForumName))
	input.Set("tid", common.Tvttt(staticField.IntTid, common.TTT_STRING).(string))
	input.Set("next", common.Tvttt(staticField.IntNext, common.TTT_STRING).(string))
	input.Set("prev", common.Tvttt(staticField.IntPrev, common.TTT_STRING).(string))
	input.Set("see_lz", seeLz)
	input.Set("_open_id", "tbclient")
	input.Set("_user_client_ip", ip)
	input.Set("_secure_str", genOpenSecureStr(secureKey, openId, ip))
	input.Set("phone_type", phoneType)
	input.Set("_data_client_type", "2")

	if staticField.StrPicID != "" {
		input.Set("pic_id", staticField.StrPicID)
	}

	photoRes := new(photofrsnew.DataAlbumGuide)
	urlStr := "/photo/bw/dataAlbum/guide?" + input.Encode()
	err := tbservice.Call(ctx, "photo_frs_new", "dataAlbum", input, photoRes,
		tbservice.WithConverter(tbservice.JSONITER), tbservice.WithHttpUrl(urlStr),
		tbservice.WithHttpMethod(http.MethodGet))
	if err != nil || photoRes.No == nil {
		tbcontext.WarningF(ctx, "call %s fail, output:%v, err:%v", urlStr, common.ToString(photoRes), err)
		baseData.BaseObj.Error(tiebaerror.ERR_CLIENT_CALL_PHOTO, tiebaerror.GetErrMsg(tiebaerror.ERR_CLIENT_CALL_PHOTO), true)
		return errors.New("call photo_frs_new fail")
	}

	if photoRes.GetNo() == tiebaerror.ERR_SUCCESS {
		staticField.IntNumOfPics = photoRes.GetData().GetPicAmount()
		tmp := make(map[string]*clientPicpage.PicList, 0)
		if picList, ok := photoRes.GetData().GetPicList().(map[string]interface{}); ok {
			for _, v := range picList {
				if data, ok := v.(map[string]interface{}); ok {
					if descr, ok := data["descr"]; ok {
						if _, ok = descr.(string); ok {
							data["descr"] = nil
						}
					}
				}
			}
		}

		common.StructAToStructBCtx(ctx, photoRes.GetData().GetPicList(), &tmp)
		staticField.ArrPicAlbum = make([]*clientPicpage.PicList, 0)
		for key, v := range tmp {
			if v.GetAuthor() == nil {
				v.Author = new(client.User)
			}
			v.OverallIndex = proto.Int64(common.Tvttt(strings.ReplaceAll(key, "#", ""), common.TTT_INT64).(int64))
			staticField.ArrPicAlbum = append(staticField.ArrPicAlbum, v)
		}
		sort.Slice(staticField.ArrPicList, func(i, j int) bool {
			return staticField.ArrPicList[i].GetOverallIndex() < staticField.ArrPicList[j].GetOverallIndex()
		})

		return nil
	}

	tbcontext.WarningF(ctx, "call %s fail, output:%v", urlStr, common.ToString(photoRes))
	baseData.BaseObj.Error(tiebaerror.ERR_CLIENT_CALL_PHOTO, tiebaerror.GetErrMsg(tiebaerror.ERR_CLIENT_CALL_PHOTO), true)
	return errors.New("call photo_frs_new fail")
}

func genOpenSecureStr(secureKey, openId, ip string) string {
	return php2go.Md5(fmt.Sprintf("open_id=%s&clinet_ip=%s&key=%s", openId, ip, secureKey))
}
