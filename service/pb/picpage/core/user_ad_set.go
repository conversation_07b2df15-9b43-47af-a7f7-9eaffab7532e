package core

import (
	"context"
	"time"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/celebrity"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type UserAdSet struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("picpage_user_ad_set", func() engine.Job {
		return &UserAdSetOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewUserAdSet(ctx *engine.Context) *UserAdSet {
	return &UserAdSet{
		ctx: ctx,
	}
}

func (a *UserAdSet) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {
	if baseData.StaticField == nil || baseData.StaticField.UserID <= 0 || len(baseData.StaticField.AdsenseUserInfo) == 0 {
		return false
	}
	//鸿蒙暂不支持
	if baseData.StaticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
		return false
	}
	// 判断用户会员状态，SVIP才继续执行
	user := baseData.StaticField.AdsenseUserInfo[0]
	if user.GetVipInfo().GetVStatus() >= 2 && user.GetVipInfo().GetETime() >= time.Now().Unix() {
		return true
	}

	return false
}
func (a *UserAdSet) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField

	userAdSetInput := &map[string]any{
		"user_id": staticField.UserID,
	}
	userAdSetOutput := &celebrity.GetUserAdSetRes{}
	err := tbservice.Call(ctx, "celebrity", "getUserAdSet", userAdSetInput, userAdSetOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || userAdSetOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		// 虽然在core里，但并非核心数据，调用失败不阻塞流程，不要给整个图报错，不要阻塞流程
		tbcontext.WarningF(ctx, "fail to call service celebrity:getUserAdSet, input = %v, output = %v, err = %v",
			common.ToString(userAdSetInput), common.ToString(userAdSetOutput), err)
	}

	tbcontext.AddNotice(ctx, "svip_close_live", userAdSetOutput.GetData().GetNeedCloseLive())
	staticField.UserAdSet = userAdSetOutput

	return nil
}

type UserAdSetOperator struct {
}

func (op *UserAdSetOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	u := NewUserAdSet(ctx)
	if !u.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := u.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "UserAdSet execute fail: %v", err)
		return err
	}

	return nil
}
