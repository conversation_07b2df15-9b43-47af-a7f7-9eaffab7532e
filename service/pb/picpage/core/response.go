package core

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"time"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/imgcdn"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/stlog"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tracecode"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	WordListNameTpoint = "tb_wordlist_redis_tpointTagName"
	WordKeyName        = "tagName"
	SildeToastMain     = "滑动进吧查看更多"
	SildeToastExtra    = "释放查看更多内容"
	FrsSchemaTPL       = `tiebaapp://router/portal?params=%s`
)

type Response struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("picpage_response", func() engine.Job {
		return &ResponseOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewResponse(ctx *engine.Context) *Response {
	return &Response{
		ctx: ctx,
	}
}
func (a *Response) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {

	return true
}

// Execute 是 Response 对象的方法
func (a *Response) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField

	tagName, err := wordserver.QueryKey(ctx, WordListNameTpoint, WordKeyName)
	if err != nil {
		tbcontext.WarningF(ctx, "query word %s fail, err: %v", WordListNameTpoint, err)
	}

	picResult := make([]*clientPicpage.PicList, 0)
	n := int64(0)
	for _, value := range staticField.ArrPicList {
		// 添加触点图片推广标示
		if php2go.InArray(value.GetPostId(), staticField.ArrTpoint) {
			value.PicTagname = &clientPicpage.PicTagname{
				PicType: proto.Int32(1),
				TagName: proto.String(tagName),
			}
		}
		if baseData.StaticField.FilterFirstFloor == 1 && uint64(value.GetPostId()) == baseData.StaticField.IntFirstPostID {
			n++
			continue
		}
		value.OverallIndex = proto.Int64(value.GetOverallIndex() - n)
		value.Index = proto.Int64(value.GetIndex() - n)
		value.DescStr = nil
		picResult = append(picResult, value)
	}
	staticField.IntNumOfPics = staticField.IntNumOfPics - int32(n)

	outData.Anti = staticField.ArrAnti
	outData.Forum = staticField.ArrForumInfo
	outData.Thread = staticField.ArrOutThread
	outData.PicAmount = proto.Int64(int64(staticField.IntNumOfPics))
	outData.AlbumList = staticField.ArrAlbumList
	outData.PicList = picResult
	outData.IsNewUrl = proto.Int32(int32(staticField.IntIsImgCDN))
	outData.ShowAdsense = proto.Int32(int32(staticField.ShowAdsense))
	outData.RecomAlaInfo = staticField.ArrRecomLive
	outData.RecomLiveList = staticField.RecomLiveList
	outData.InsertFloorData = staticField.InsertFloorData

	if staticField.NeedAd {
		outData.App = make([]*client.App, 0)
	}

	if isCdnErrUser(staticField) {
		picDeal(outData.PicList)
	}

	changePicExpTime(ctx, outData, baseData)

	baseData.BaseObj.ObjResponse.AddLog("fid", staticField.IntFid)
	baseData.BaseObj.ObjResponse.AddLog("is_new_url", staticField.IntIsImgCDN)
	baseData.BaseObj.ObjResponse.AddLog("obj_type", staticField.IntPicType)

	stlog.AddLog(ctx, "client_forum_pb_pic_page_ret", "success")
	stlog.AddLog(ctx, "client_forum_pb_pic_page_pic_amount", outData.GetPicAmount())
	if outData.GetForum().GetName() == "" {
		baseData.BaseObj.ObjResponse.AddLog("picpage_forum_id", outData.GetForum().GetId())
	}

	// 如果命中右滑进吧的实验，下发跳转frs的相关信息
	if staticField.NeedFrsPageInfo {
		schemaParams := map[string]any{
			"page": "frs/frs",
			"pageParams": map[string]any{
				"fakeThreadId": staticField.IntTid,
				"forumName":    staticField.StrForumName,
				"callFrom":     "230", // 固定传"230" https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/ddpRviE1Ce/rYzdZt_CW4/35eQ5zadJdEtat
			},
		}
		strParams, err := jsoniter.MarshalToString(schemaParams)
		if err != nil {
			tbcontext.WarningF(ctx, "marshal schema params fail: %v", err)
		} else {
			strParams = url.QueryEscape(strParams)
			strSchema := fmt.Sprintf(FrsSchemaTPL, strParams)
			outData.RightSlideInfo = &clientPicpage.RightSlide{
				SildeToastMain:  proto.String(SildeToastMain),
				SildeToastExtra: proto.String(SildeToastExtra),
				Schema:          proto.String(strSchema),
			}
		}
	}

	outData.LogParam = getLogParam(ctx, baseData)

	return nil
}

type ResponseOperator struct {
}

func (rdop *ResponseOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewResponse(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "response execute fail: %v", err)
		return err
	}

	return nil
}

func isCdnErrUser(staticField *types.PicpageStaticField) bool {
	if staticField.UserID <= 0 || len(staticField.AdsenseUserInfo) == 0 {
		return false
	}

	cdnTime := common.Tvttt(staticField.AdsenseUserInfo[0].GetCdnError(), common.TTT_INT64).(int64)
	curTime := time.Now().Unix()
	if curTime-cdnTime <= imgcdn.CDN_USER_TIME {
		return true
	}

	return false
}

func picDeal(picList []*clientPicpage.PicList) {
	for _, one := range picList {
		if one.GetImg().GetOriginal() != nil && one.GetImg().GetOriginal().GetBigCdnSrc() != "" {
			one.GetImg().GetOriginal().BigCdnSrc = proto.String(fmt.Sprintf("http://c.tieba.baidu.com/c/p/img?src=%s", one.GetImg().GetOriginal().GetBigCdnSrc()))
		}
	}
}

// 如果命中实验，将图片过期时间修改为2天后的凌晨5点
func changePicExpTime(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) {
	if !baseData.StaticField.NeedChangePicExpTime {
		return
	}
	timeStr := time.Now().Format("2006-01-02")
	t, _ := time.Parse("2006-01-02", timeStr)
	picAuthEndTime := t.Add(53 * time.Hour)
	for _, picItem := range outData.GetPicList() {
		if picItem.Img == nil || picItem.GetImg().GetOriginal() == nil {
			continue
		}
		bigCdnSrc, _ := image.GenAuthUrlWithEndTime(picItem.GetImg().GetOriginal().GetBigCdnSrc(), &picAuthEndTime)
		picItem.GetImg().GetOriginal().BigCdnSrc = proto.String(bigCdnSrc)
	}
	return
}

// getLogParam 获取打点日志参数
func getLogParam(ctx context.Context, baseData *types.CPicpageBaseData) []*client.FeedKV {
	if baseData == nil || baseData.Request == nil {
		return nil
	}
	tc, _ := tracecode.GetTracecode(ctx)
	return []*client.FeedKV{
		{
			Key:   proto.String("trace_code"),
			Value: proto.String(tc),
		},
		{
			Key:   proto.String("tid"),
			Value: proto.String(strconv.FormatInt(baseData.StaticField.IntTid, 10)),
		},
		{
			Key:   proto.String("fid"),
			Value: proto.String(strconv.FormatInt(int64(baseData.StaticField.IntFid), 10)),
		},
		{
			Key:   proto.String("tieba_cuid"),
			Value: proto.String(baseData.Request.GetData().GetCommon().GetCuid()),
		},
		{
			Key:   proto.String("page"),
			Value: proto.String("pic"),
		},
	}
}
