package core

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Post struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("picpage_post", func() engine.Job {
		return &PostOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPost(ctx *engine.Context) *Post {
	return &Post{
		ctx: ctx,
	}
}
func (a *Post) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {
	return true
}
func (a *Post) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField

	input := map[string]interface{}{
		"thread_ids":     []int64{staticField.IntTid},
		"forum_id":       staticField.IntFid,
		"need_abstract":  1,
		"need_photo_pic": 0,
		"call_from":      "client_frs",
	}
	mgetThreadRes := new(frs.MgetThreadRes)
	err := tbservice.Call(ctx, "post", "mgetThread", input, mgetThreadRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || mgetThreadRes.Errno == nil {
		tbcontext.FatalF(ctx, "Call post:mgetThread failed. input:%v, output:%v, err:%v", input, common.ToString(mgetThreadRes), err)
		baseData.BaseObj.Error(tiebaerror.ERR_CLIENT_CALL_POST, tiebaerror.GetErrMsg(tiebaerror.ERR_CLIENT_CALL_POST), true)
		return errors.New("call post failed")
	}

	if mgetThreadRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "Call post:mgetThread failed. input:%v, output:%v", input, common.ToString(mgetThreadRes))
		baseData.BaseObj.Error(int(mgetThreadRes.GetErrno()), tiebaerror.GetErrMsg(int(mgetThreadRes.GetErrno())), true)
		return errors.New("call post failed")
	}

	if len(mgetThreadRes.GetOutput().GetThreadList()) > 0 && mgetThreadRes.GetOutput().GetThreadList()[uint64(staticField.IntTid)] != nil {
		threadInfo := mgetThreadRes.GetOutput().GetThreadList()[uint64(staticField.IntTid)]
		staticField.ArrThreadInfo = threadInfo
		staticField.IntNumOfLzPics = len(threadInfo.GetMedia())
		staticField.IntFirstPostID = threadInfo.GetFirstPostId()
		if threadInfo.GetNid() != "" {
			staticField.IsBjh = 1
		}

	} else {
		tbcontext.FatalF(ctx, "Call post:mgetThread failed. input:%v, output:%v", input, common.ToString(mgetThreadRes))
		baseData.BaseObj.Error(tiebaerror.ERR_CLIENT_CALL_POST, tiebaerror.GetErrMsg(tiebaerror.ERR_CLIENT_CALL_POST), true)
		return errors.New("call post failed")
	}


	return nil
}

type PostOperator struct {
}

func (rdop *PostOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewPost(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum execute fail: %v", err)
		return err
	}

	return nil
}
