package core

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type User struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("picpage_user", func() engine.Job {
		return &UserOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewUser(ctx *engine.Context) *User {
	return &User{
		ctx: ctx,
	}
}
func (a *User) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {
	if baseData.StaticField.UserID <= 0 {
		return false
	}

	return true
}
func (a *User) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField

	input := map[string]interface{}{
		"user_id": staticField.UserID,
	}
	getUserDataRes := new(user.GetUserDataRes)
	err := tbservice.Call(ctx, "user", "getUserData", input, getUserDataRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getUserDataRes.Errno == nil {
		tbcontext.WarningF(ctx, "Call user:getUserData failed. input:%v, output:%v, err:%v", input, common.ToString(getUserDataRes), err)
		return nil
	}

	staticField.AdsenseUserInfo = getUserDataRes.GetUserInfo()

	return nil
}

type UserOperator struct {
}

func (rdop *UserOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewUser(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "user execute fail: %v", err)
		return err
	}

	return nil
}
