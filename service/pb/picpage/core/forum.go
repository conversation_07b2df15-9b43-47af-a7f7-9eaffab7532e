package core

import (
	"context"
	"errors"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Forum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("picpage_forum", func() engine.Job {
		return &ForumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewForum(ctx *engine.Context) *Forum {
	return &Forum{
		ctx: ctx,
	}
}
func (a *Forum) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {
	return true
}
func (a *Forum) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField

	if staticField.StrForumName == "" {
		staticField.ArrForumInfo = &clientPicpage.PicForumInfo{
			Id:            proto.Uint32(uint32(staticField.IntFid)),
			Name:          proto.String(staticField.StrForumName),
			FirstClass:    proto.String(""),
			SecondClass:   proto.String(""),
			HasPictureFrs: proto.String("1"),
			AlbumForum:    proto.String("1"),
			FrsPage:       proto.Int32(0),
		}

		return nil
	}

	input := map[string]interface{}{
		"forum_name": staticField.StrForumName,
	}
	getBtxInfoByNameRes := new(forum.GetBtxInfoByNameRes)
	err := tbservice.Call(ctx, "forum", "getBtxInfoByName", input, getBtxInfoByNameRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getBtxInfoByNameRes.Errno == nil {
		tbcontext.FatalF(ctx, "Call forum:getBtxInfoByName failed. input:%v, output:%v, err:%v", input, common.ToString(getBtxInfoByNameRes), err)
		baseData.BaseObj.Error(tiebaerror.ERR_CLIENT_CALL_FORUM, tiebaerror.GetErrMsg(tiebaerror.ERR_CLIENT_CALL_FORUM), true)
		return errors.New("call forum failed")
	}

	if getBtxInfoByNameRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "Call forum:getBtxInfoByName failed. input:%v, output:%v", input, common.ToString(getBtxInfoByNameRes))
		baseData.BaseObj.Error(int(getBtxInfoByNameRes.GetErrno()), tiebaerror.GetErrMsg(int(getBtxInfoByNameRes.GetErrno())), true)
		return errors.New("call forum failed")
	}

	if getBtxInfoByNameRes.GetForumId() == nil || getBtxInfoByNameRes.GetForumId().Status == nil {
		tbcontext.WarningF(ctx, "Call forum:getBtxInfoByName failed. input:%v, output:%v", input, common.ToString(getBtxInfoByNameRes))
		baseData.BaseObj.Error(tiebaerror.ERR_MO_PHOTOFORUM_NOT_EXIST, tiebaerror.GetErrMsg(tiebaerror.ERR_MO_PHOTOFORUM_NOT_EXIST), true)
		return errors.New("call forum failed")
	}

	if getBtxInfoByNameRes.GetForumId().GetStatus() != 0 && staticField.IntFid != 0 {
		tbcontext.WarningF(ctx, "Call forum:getBtxInfoByName failed. input:%v, output:%v", input, common.ToString(getBtxInfoByNameRes))
		baseData.BaseObj.Error(tiebaerror.ERR_MO_PHOTOFORUM_NOT_EXIST, tiebaerror.GetErrMsg(tiebaerror.ERR_MO_PHOTOFORUM_NOT_EXIST), true)
		return errors.New("call forum failed")
	}

	staticField.ArrForumInfo = &clientPicpage.PicForumInfo{
		Name:          proto.String(staticField.StrForumName),
		Id:            proto.Uint32(getBtxInfoByNameRes.GetForumId().GetForumId()),
		FirstClass:    proto.String(getBtxInfoByNameRes.GetDir().GetLevel_1Name()),
		SecondClass:   proto.String(getBtxInfoByNameRes.GetDir().GetLevel_2Name()),
		HasPictureFrs: proto.String("0"),
		AlbumForum:    proto.String("1"),
		FrsPage:       proto.Int32(0),
	}

	if getBtxInfoByNameRes.GetAttrs() != nil && getBtxInfoByNameRes.GetAttrs().GetPhotoP1() != "" &&
		getBtxInfoByNameRes.GetAttrs().GetPhotoP1() != nil {
		staticField.ArrForumInfo.HasPictureFrs = proto.String("1")
	}

	staticField.AdsenseForumInfo = getBtxInfoByNameRes

	return nil
}

type ForumOperator struct {
}

func (rdop *ForumOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewForum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum execute fail: %v", err)
		return err
	}

	return nil
}
