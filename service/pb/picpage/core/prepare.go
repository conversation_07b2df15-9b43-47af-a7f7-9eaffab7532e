package core

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	PreKeySampleIdCache      = "user_sid_"
	AfdSmallFlowGroup        = "12_2_picpage_bear_a" // 实验组展示小熊广告不请求原生广告 对照组12_2_picpage_bear
	NotShowAdsense           = 0
	RecomPageToFrsPageSample = "12_66_datu_jinba_test" // 大图页右滑进吧实验
)

type Prepare struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("picpage_prepare", func() engine.Job {
		return &PrepareOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPrepare(ctx *engine.Context) *Prepare {
	return &Prepare{
		ctx: ctx,
	}
}
func (a *Prepare) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {

	return true
}
func (a *Prepare) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest
	staticField.StrForumName = common.Tvttt(objReq.GetPrivateAttr("forum_name", ""), common.TTT_STRING).(string)
	staticField.FilterFirstFloor = common.Tvttt(objReq.GetPrivateAttr("filter_first_floor", 0), common.TTT_INT64).(int64)
	staticField.StrPicID = common.Tvttt(objReq.GetPrivateAttr("pic_id", ""), common.TTT_STRING).(string)
	staticField.IntTid = common.Tvttt(objReq.GetPrivateAttr("tid", 0), common.TTT_INT64).(int64)
	staticField.PostID = common.Tvttt(objReq.GetPrivateAttr("post_id", 0), common.TTT_INT64).(int64)
	tbcontext.AddNotice(ctx, "post_id", staticField.PostID)
	staticField.CurrentPostID = common.Tvttt(objReq.GetPrivateAttr("current_post_id", 0), common.TTT_INT64).(int64)
	staticField.IntNext = common.Tvttt(objReq.GetPrivateAttr("next", 0), common.TTT_INT).(int)
	staticField.IntPrev = common.Tvttt(objReq.GetPrivateAttr("prev", 0), common.TTT_INT).(int)
	staticField.IntIsNote = common.Tvttt(objReq.GetPrivateAttr("is_note", 0), common.TTT_INT).(int)
	staticField.AladdinSrcID = common.Tvttt(objReq.GetPrivateAttr("aladdin_src_id", 0), common.TTT_INT).(int)
	staticField.IntNotSeeLz = common.Tvttt(objReq.GetPrivateAttr("not_see_lz", 0), common.TTT_INT).(int)
	staticField.StrPicfrom = common.Tvttt(objReq.GetPrivateAttr("picfrom", 0), common.TTT_STRING).(string)
	staticField.IntClientType = common.Tvttt(objReq.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	staticField.StrClientVersion = common.Tvttt(objReq.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	staticField.StrShoubaiCuid = common.Tvttt(objReq.GetPrivateAttr("shoubai_cuid", 0), common.TTT_STRING).(string)
	staticField.StrObjType = common.Tvttt(objReq.GetPrivateAttr("obj_type", 0), common.TTT_STRING).(string)
	staticField.IsTopAgree = common.Tvttt(objReq.GetPrivateAttr("is_top_agree", 0), common.TTT_INT).(int)
	staticField.Source = common.Tvttt(objReq.GetPrivateAttr("source", 0), common.TTT_INT).(int)
	staticField.StrShoubaiCuidAnd = common.Tvttt(objReq.GetPrivateAttr("cuid_galaxy2", 0), common.TTT_STRING).(string)
	staticField.Cuid = common.Tvttt(objReq.GetCommonAttr("cuid", 0), common.TTT_STRING).(string)
	staticField.NetType = common.Tvttt(objReq.GetCommonAttr("net_type", 0), common.TTT_INT).(int)
	staticField.StrSampleID = common.Tvttt(objReq.GetCommonAttr("sample_id", 0), common.TTT_STRING).(string)
	staticField.NeedRecomLiveList = true // 默认需要推荐直播列表
	staticField.NeedFrsPageInfo = false  // 默认不下发右滑进吧的相关字段

	// 5.1.0以上版本，使用CDN
	isCdnUrl := common.Tvttt(objReq.GetStrategy("is_cdn_url", 1), common.TTT_INT).(int)
	if isCdnUrl == 1 && clientvers.Compare("5.1.0", staticField.StrClientVersion) >= 0 {
		switch staticField.IntClientType {
		case stcdefine.CLIENT_TYPE_ANDROID, stcdefine.CLIENT_TYPE_HARMONY:
			staticField.IntIsImgCDN = 1
			staticField.IntPicStrategyType = 1
		case stcdefine.CLIENT_TYPE_IPHONE:
			staticField.IntIsImgCDN = 1
			staticField.IntPicStrategyType = 2
		}
	}

	if staticField.IntIsImgCDN == 1 {
		staticField.IntScreenWidth = common.Tvttt(objReq.GetPrivateAttr("scr_w", 0), common.TTT_INT32).(int32)
		staticField.IntScreenHeight = common.Tvttt(objReq.GetPrivateAttr("scr_h", 0), common.TTT_INT32).(int32)
		staticField.IntScreenDIP = common.Tvttt(objReq.GetPrivateAttr("scr_dip", 0), common.TTT_FLOAT64).(float64)
		staticField.IntQType = common.Tvttt(objReq.GetPrivateAttr("q_type", 0), common.TTT_INT32).(int32)

		if staticField.IntScreenWidth <= 0 {
			staticField.IntScreenWidth = 320
		}

		if staticField.IntScreenHeight <= 0 {
			staticField.IntScreenHeight = 320
		}

		if staticField.IntScreenDIP <= 0 {
			staticField.IntScreenDIP = 1
		}

		if staticField.IntQType <= 0 {
			staticField.IntQType = 1
		}
	}

	// 7.1.0以上版本，客户端增加楼层数,返回本图信息
	if (staticField.IntClientType == stcdefine.CLIENT_TYPE_ANDROID ||
		staticField.IntClientType == stcdefine.CLIENT_TYPE_IPHONE ||
		staticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY) &&
		clientvers.Compare("7.1.0", staticField.StrClientVersion) >= 0 {
		staticField.BigPic = true
	}

	staticField.UserID = common.Tvttt(objReq.GetPrivateAttr("user_id", 0), common.TTT_INT64).(int64)
	staticField.IntFid = common.Tvttt(objReq.GetPrivateAttr("intFid", 0), common.TTT_INT32).(int32)
	staticField.PicIndex = common.Tvttt(objReq.GetPrivateAttr("pic_index", 0), common.TTT_INT).(int)

	// 版本小于7.1.0(Android)或小于7.2.0(IOS)的不请求看图广告服务
	needAd := true
	if staticField.IntClientType == stcdefine.CLIENT_TYPE_ANDROID && clientvers.Compare("7.1.0", staticField.StrClientVersion) < 0 {
		needAd = false
	}
	if staticField.IntClientType == stcdefine.CLIENT_TYPE_IPHONE && clientvers.Compare("7.2.0", staticField.StrClientVersion) < 0 {
		needAd = false
	}
	// 鸿蒙版本不请求看图广告服务
	if staticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
		needAd = false
	}
	if staticField.PicIndex > 0 && needAd {
		staticField.NeedAd = true
	}

	if clientvers.Is20205Version(staticField.IntClientType, staticField.StrClientVersion) {
		staticField.NeedRecLive = true
	}

	// 12.27以下不再下发大图页呼吸态数据
	if clientvers.Compare("12.27.0", staticField.StrClientVersion) < 0 {
		staticField.NeedRecLive = false
	}

	if clientvers.Is12_6_BVersion(staticField.IntClientType, staticField.StrClientVersion) {
		staticField.IsYYLive = true
	}

	// 12.5.5纯浏览模式，屏蔽大图页右上角直播间入口
	if common.Tvttt(objReq.GetCommonAttr("cmode", 0), common.TTT_INT).(int) == 2 {
		staticField.NeedRecLive = false
	}

	// 小熊广告实验组流量 展示小熊广告 @<EMAIL>
	if staticField.StrSampleID == "" {
		staticField.StrSampleID = getSampleIdCache(ctx, staticField)
	}

	arrSampleIds := UbsAbtest.GetUbsAbtestConfig(ctx, staticField.StrSampleID,
		common.Tvttt(staticField.UserID, common.TTT_STRING).(string), "tb_wordlist_redis_ubs_abtest_config")
	arrSampleId := make([]string, 0)
	for _, v := range arrSampleIds {
		if _, ok := v["sid"]; ok {
			arrSampleId = append(arrSampleId, v["sid"])
		}
	}

	if len(arrSampleId) > 0 && php2go.InArray(AfdSmallFlowGroup, arrSampleId) {
		staticField.NeedBearAd = true
	}
	// 12.66 版本，大图页右滑进吧 IOS补齐
	// 版控逻辑，安卓大于12.63 IOS大于12.66，鸿蒙大于12.66
	bolVersionHit := false
	if clientvers.Compare("12.66.0", staticField.StrClientVersion) >= 0 &&
		(staticField.IntClientType == stcdefine.CLIENT_TYPE_ANDROID ||
			staticField.IntClientType == stcdefine.CLIENT_TYPE_IPHONE ||
			staticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY) {
		bolVersionHit = true
	}
	if bolVersionHit {
		if len(arrSampleId) > 0 && php2go.InArray(RecomPageToFrsPageSample, arrSampleId) {
			// 命中实验
			if baseData.Request.GetData().GetNeedRightSlideInfo() == 1 {
				staticField.NeedFrsPageInfo = true
			}
			// index frs pb 场景不下发直播信息
			// 安卓端frs场景obj_type传的不是"frs"，是 "1" ...
			if staticField.StrObjType == "index" || staticField.StrObjType == "1" ||
				staticField.StrObjType == "frs" || staticField.StrObjType == "pb" {
				staticField.NeedRecomLiveList = false
			}
		}
	}

	staticField.ShowAdsense = NotShowAdsense

	baseData.BaseObj.ObjResponse.AddLog("fname", staticField.StrForumName)
	baseData.BaseObj.ObjResponse.AddLog("tid", staticField.IntTid)

	return nil
}

type PrepareOperator struct {
}

func (rdop *PrepareOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewPrepare(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "prepare execute fail: %v", err)
		return err
	}

	return nil
}

func getSampleIdCache(ctx context.Context, staticField *types.PicpageStaticField) string {
	md5UserInfo := fmt.Sprintf("%d-%s-%d-%s",
		staticField.UserID, staticField.Cuid, staticField.IntClientType, staticField.StrClientVersion)
	redisKey := PreKeySampleIdCache + md5UserInfo
	sampleId, err := resource.CacheSign.Get(ctx, redisKey)
	if err != nil {
		tbcontext.WarningF(ctx, "get sample info fail: %v", err)
		return ""
	}

	tbcontext.AddNotice(ctx, fmt.Sprintf("cuid %s get sample_id is: %s", staticField.Cuid, sampleId), 1)
	return sampleId
}
