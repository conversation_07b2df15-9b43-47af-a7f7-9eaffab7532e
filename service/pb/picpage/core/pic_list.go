package core

import (
	"context"
	"errors"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/space"
	commonproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/imgcdn"
	molibPhoto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/photo"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/versionmatch"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/actstage"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/agree"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/anti"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/photo"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/pb"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/hiphoto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbimg"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/threadtype"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	"icode.baidu.com/baidu/tieba-server-go/golib2/php"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/golib2/trace"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	ImageMaxHeight  = 980
	StaticWidth     = 194
	ImageDomain     = "imgsrc.baidu.com"
	BlockedImageURL = "http://imgsrc.baidu.com/forum/pic/item/e0fe9925bc315c60e698e1f986b1cb1349547793.jpg"
	CoverPicSpecG   = "w=720;g=0"
)

type PicList struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("picpage_piclist", func() engine.Job {
		return &PicListOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPicList(ctx *engine.Context) *PicList {
	return &PicList{
		ctx: ctx,
	}
}
func (a *PicList) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {
	staticField := baseData.StaticField
	if staticField.IsBjh == 1 || staticField.ArrThreadInfo.GetThreadTypes()>>32 == threadtype.PIC_ALBUM {
		return false
	}

	return true
}
func (a *PicList) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField
	if (staticField.IntClientType == stcdefine.CLIENT_TYPE_ANDROID && clientvers.Compare("*******", staticField.StrClientVersion) < 0) ||
		(staticField.IntClientType == stcdefine.CLIENT_TYPE_IPHONE && clientvers.Compare("8.8.8", staticField.StrClientVersion) < 0) {
		err := getPicList(ctx, baseData)
		if err != nil {
			return err
		}
		trace.TimerStart(ctx, "processRepeat")
		processRepeat(baseData)
		trace.TimerEnd(ctx, "processRepeat")
	} else {
		err := getPicListWithNewLogic(ctx, baseData)
		if err != nil {
			return err
		}

		getPostAndAgreeInfo(ctx, staticField, baseData)

		if staticField.IntPrev == 0 || staticField.IntNext == 0 {
			processRepeat(baseData)
		}
	}

	cutPicListForAdOpti(staticField)

	return nil
}

type PicListOperator struct {
}

func (rdop *PicListOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewPicList(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "piclist execute fail: %v", err)
		return err
	}

	return nil
}

func getPicList(ctx context.Context, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField
	input := map[string]interface{}{
		"forum_id":  staticField.IntFid,
		"thread_id": staticField.IntTid,
		"see_lz":    1,
		"prev":      staticField.IntPrev,
		"next":      staticField.IntNext,
		"pic_id":    staticField.StrPicID,
	}

	if staticField.IntNotSeeLz > 0 {
		input["see_lz"] = 0
	}

	getPbPicListRes := new(photo.GetPbPicListRes)
	err := tbservice.Call(ctx, "photo", "getPbPicList", input, getPbPicListRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getPbPicListRes.Errno == nil {
		tbcontext.FatalF(ctx, "call photo::getPbPicList fail, input:%v, output:%v, err: %v", input, getPbPicListRes, err)
		baseData.BaseObj.Error(tiebaerror.ERR_CLIENT_CALL_PHOTO, tiebaerror.GetErrMsg(tiebaerror.ERR_CLIENT_CALL_PHOTO), true)
		return errors.New("call photo failed")
	}

	if getPbPicListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "Call photo::getPbPicList failed. input:%v, output:%v", input, common.ToString(getPbPicListRes))
		baseData.BaseObj.Error(int(getPbPicListRes.GetErrno()), tiebaerror.GetErrMsg(int(getPbPicListRes.GetErrno())), true)
		return errors.New("call photo failed")
	}

	// 重新计算图片总数
	calPicNumber(ctx, baseData, getPbPicListRes.GetData())

	if getPbPicListRes.GetData() == nil || getPbPicListRes.GetData().PicList == nil {
		baseData.BaseObj.Error(tiebaerror.ERR_MO_PB_PICPAGE_NODATA, tiebaerror.GetErrMsg(tiebaerror.ERR_MO_PB_PICPAGE_NODATA), true)
		return errors.New("call photo failed")
	}

	startIndex := 1
	for _, key := range getPbPicListRes.GetData().GetPicIdList() {

		if _, ok := getPbPicListRes.GetData().GetPicList()[key]; !ok {
			continue
		}

		value := getPbPicListRes.GetData().GetPicList()[key]

		// 统计需求，判断是否是长图
		if value.GetWidth() == 0 {
			value.Width = proto.Int32(320)
		}
		floatHWRatio := float64(value.GetHeight()) / float64(value.GetWidth())
		if floatHWRatio >= 3 && floatHWRatio <= 50 && value.GetWidth() >= 100 {
			staticField.IntPicType = 1
		}

		//处理高度超限时，未根据图片的宽高比对长度进行处理，导致图片的宽高比错误。此处修改为：根据宽高比进行同比例缩放
		if value.GetHeight() > ImageMaxHeight {
			radio := float64(value.GetWidth()) / float64(value.GetHeight())
			value.Height = proto.Int32(ImageMaxHeight)
			value.Width = proto.Int32(int32(ImageMaxHeight * radio))
		}

		//获取图片的post_id集合
		staticField.ArrPostid = append(staticField.ArrPostid, value.GetPostId())

		// 相对于帖子的索引
		arrTempResult := new(clientPicpage.PicList)
		arrTempResult.OverallIndex = proto.Int64(common.Tvttt(strings.ReplaceAll(key, "#", ""), common.TTT_INT64).(int64))

		arrTempResult.SrcUrl = proto.String("")
		arrTempResult.SrcText = proto.String("")
		arrTempResult.PicType = proto.String("")

		arrPicExtra, err := php.Unserialize([]byte(value.GetPicExtra()))
		if err != nil {
			tbcontext.WarningF(ctx, "php unserialize fail:%v", err)
		}

		var waterUrl string
		if arrPicExtraMap, ok := arrPicExtra.(map[string]interface{}); ok {
			if imgSource, ok := arrPicExtraMap["img_source"]; ok {
				if imgSourceMap, ok := imgSource.(map[string]interface{}); ok {
					arrTempResult.SrcUrl = proto.String(common.Tvttt(imgSourceMap["src_url"], common.TTT_STRING).(string))
					arrTempResult.SrcText = proto.String(common.Tvttt(imgSourceMap["src_text"], common.TTT_STRING).(string))
					arrTempResult.PicType = proto.String(common.Tvttt(imgSourceMap["pic_type"], common.TTT_STRING).(string))
				}
			}
			waterUrl = common.Tvttt(arrPicExtraMap["waterurl"], common.TTT_STRING).(string)
		}

		middlePictureWidth := value.GetWidth()
		middlePictureHeight := value.GetHeight()
		if value.GetWidth() > StaticWidth {
			middlePictureWidth = StaticWidth
			middlePictureHeight = value.GetHeight() * StaticWidth / value.GetWidth()
		}

		picId := hiphoto.EncodeId(uint64(value.GetId()), staticField.FidFromPicID)

		picInfo := &molibPhoto.PicInfo{
			Id:           value.GetId(),
			ForumId:      uint32(staticField.FidFromPicID),
			Width:        value.GetWidth(),
			Height:       value.GetHeight(),
			ForumName:    value.GetForumName(),
			CreateTime:   value.GetCreateTime(),
			Format:       value.GetFormat(),
			ScreenWidth:  value.GetScreenWidth(),
			ScreenHeight: value.GetScreenHeight(),
			PicFormat:    int(value.GetPicFormat()),
		}
		picExtra := &molibPhoto.PicExtra{
			Waterurl: waterUrl,
		}
		waterUrl = molibPhoto.GetPictureUrl(ctx, picInfo, picExtra, ImageDomain)

		//支持屏幕自适应
		screenWidth := value.GetWidth()
		screenHeight := value.GetHeight()
		screenWaterUrl := waterUrl

		widthRatio := float64(value.GetWidth()) / float64(staticField.ScreenInfo["width"])
		heightRatio := float64(value.GetHeight()) / float64(staticField.ScreenInfo["height"])
		maxRatio := widthRatio
		if heightRatio > widthRatio {
			maxRatio = heightRatio
		}
		if maxRatio > 1 {
			if widthRatio > heightRatio {
				screenWidth = staticField.ScreenInfo["width"]
				screenHeight = staticField.ScreenInfo["width"] * value.GetHeight() / value.GetWidth()
			} else {
				screenHeight = staticField.ScreenInfo["height"]
				screenWidth = staticField.ScreenInfo["height"] * value.GetWidth() / value.GetHeight()
			}
			screenWaterUrl = molibPhoto.GetPictureUrl(ctx, picInfo, picExtra, ImageDomain)
		}

		arrTempResult.Img = &clientPicpage.Img{
			Original: &clientPicpage.ImgField{
				Id:       proto.String(picId),
				Width:    proto.Int64(int64(value.GetWidth())),
				Height:   proto.Int64(int64(value.GetHeight())),
				Size:     proto.Int64(value.GetSize()),
				Format:   proto.String(value.GetFormat()),
				Waterurl: proto.String(waterUrl),
			},
			Medium: &clientPicpage.ImgField{
				Id:     proto.String(picId),
				Width:  proto.Int64(int64(middlePictureWidth)),
				Height: proto.Int64(int64(middlePictureHeight)),
				Size:   proto.Int64(0),
				Format: proto.String(value.GetFormat()),
				Url:    proto.String(molibPhoto.Pic2Url(ctx, picInfo, "w=194", ImageDomain, 0)),
			},
			Screen: &clientPicpage.ImgField{
				Id:       proto.String(picId),
				Width:    proto.Int64(int64(screenWidth)),
				Height:   proto.Int64(int64(screenHeight)),
				Size:     proto.Int64(0),
				Format:   proto.String(value.GetFormat()),
				Waterurl: proto.String(screenWaterUrl),
			},
		}

		arrTempResult.Author = new(client.User)
		arrTempResult.PostId = proto.Int64(value.GetPostId())
		arrTempResult.DescStr = proto.String(value.GetPicDesc())

		arrTempResult.UserId = proto.Int64(value.GetCreateUserId())
		arrTempResult.UserName = proto.String(value.GetCreateUserName())
		arrTempResult.Index = proto.Int64(int64(startIndex))
		startIndex++
		staticField.ArrCreateUserID = append(staticField.ArrCreateUserID, value.GetCreateUserId())

		// comment_amount ding_amount click_amount已经下线，但是客户端4.3.1在用，没有会报错
		arrTempResult.CommentAmount = proto.Int64(0)
		arrTempResult.DingAmount = proto.Int64(0)
		arrTempResult.ClickAmount = proto.Int64(0)

		arrTempResult.AlbId = proto.String(getPbPicListRes.GetData().GetAlbumId())

		if staticField.IntIsImgCDN == 1 {
			spec := &imgcdn.ProcPic{
				ScreenWidth: staticField.IntScreenWidth,
				ScreenHeigh: staticField.IntScreenHeight,
				ScreenDip:   staticField.IntScreenDIP,
				QType:       staticField.IntQType,
			}
			imgcdn.ProcPicPageNewPicUrl(arrTempResult, true, spec, staticField.IntPicStrategyType)
		}

		boolIsAllOrigin := true
		if clientvers.Compare(staticField.StrClientVersion, "7.0.0") == 1 {
			boolIsAllOrigin = false
		}

		urlStr, err := tbimg.GetUrlByEncodePicId(ctx, arrTempResult.Img.GetOriginal().GetId())
		if err != nil {
			tbcontext.WarningF(ctx, "GetUrlByEncodePicId fail:%v", err)
		}
		arrTempResult.Img.GetOriginal().Url = proto.String(urlStr)

		// 原图url判断
		if arrTempResult.Img.GetOriginal().GetSize() > 512000 {
			arrTempResult.Img.GetOriginal().OriginalSrc = proto.String(arrTempResult.Img.GetOriginal().GetUrl())
		} else {
			// 7.0以后 强行假装有大图
			if boolIsAllOrigin {
				arrTempResult.Img.GetOriginal().OriginalSrc = proto.String(arrTempResult.Img.GetOriginal().GetUrl())
			}
		}

		staticField.ArrPicList = append(staticField.ArrPicList, arrTempResult)

	}

	return nil
}

func getPicListWithNewLogic(ctx context.Context, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	input := map[string]interface{}{
		"forum_id":       staticField.IntFid,
		"thread_id":      staticField.IntTid,
		"see_lz":         1,
		"prev":           staticField.IntPrev,
		"next":           staticField.IntNext,
		"pic_id":         staticField.StrPicID,
		"is_filter_fold": 0,
		"post_id":        staticField.CurrentPostID,
	}
	if staticField.IntNotSeeLz > 0 {
		input["see_lz"] = 0
	}
	if clientvers.Compare("12.14", staticField.StrClientVersion) >= 0 {
		input["is_filter_fold"] = 1
	}

	getPbPicListRes := new(photo.GetPbPicListRes)
	err := tbservice.Call(ctx, "photo", "getPbPicList", input, getPbPicListRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getPbPicListRes.Errno == nil {
		tbcontext.FatalF(ctx, "call photo::getPbPicList fail, input:%v, output:%v, err: %v", input, getPbPicListRes, err)
		baseData.BaseObj.Error(tiebaerror.ERR_CLIENT_CALL_PHOTO, tiebaerror.GetErrMsg(tiebaerror.ERR_CLIENT_CALL_PHOTO), true)
		return errors.New("call photo failed")
	}

	if getPbPicListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "Call photo::getPbPicList failed. input:%v, output:%v", input, common.ToString(getPbPicListRes))
		baseData.BaseObj.Error(int(getPbPicListRes.GetErrno()), tiebaerror.GetErrMsg(int(getPbPicListRes.GetErrno())), true)
		return errors.New("call photo failed")
	}

	// 如果是frs页或者首页的神回复楼层只保留当前楼层图片
	if (staticField.StrObjType == "index" || staticField.StrObjType == "frs") && staticField.IsTopAgree == 1 {
		filterTopAgreePic(staticField, getPbPicListRes.GetData())
	}

	// 重新计算图片总数
	calPicNumber(ctx, baseData, getPbPicListRes.GetData())

	//笔记大图页纯文字贴强插ai图片
	if staticField.IntIsNote == 1 {
		err = getAiPicInfo(ctx, baseData)
		if err != nil {
			return err
		}
	} else if staticField.AladdinSrcID == 60841 {
		//如果首楼有图，则不填充图片
		if getPbPicListRes.GetData() != nil && getPbPicListRes.GetData().GetPicList() != nil {
			//检查首楼图
			firstFloorPic := false
			for _, pic := range getPbPicListRes.GetData().GetPicList() {
				if uint64(pic.GetPostId()) == staticField.IntFirstPostID {
					firstFloorPic = true
					break
				}
			}
			if !firstFloorPic {
				err = get60841PicInfo(ctx, baseData)
				if err != nil {
					return err
				}
			}
		} else {
			//直接插图
			err = get60841PicInfo(ctx, baseData)
			if err != nil {
				return err
			}
		}
	} else {
		// 笔记不判断这个
		if getPbPicListRes.GetData() == nil || getPbPicListRes.GetData().PicList == nil {
			baseData.BaseObj.Error(tiebaerror.ERR_MO_PB_PICPAGE_NODATA, tiebaerror.GetErrMsg(tiebaerror.ERR_MO_PB_PICPAGE_NODATA), true)
			return errors.New("call photo failed")
		}
	}

	indexStart := 1
	for _, key := range getPbPicListRes.GetData().GetPicIdList() {
		if _, ok := getPbPicListRes.GetData().GetPicList()[key]; !ok {
			continue
		}

		value := getPbPicListRes.GetData().GetPicList()[key]

		//获取图片的post_id集合
		staticField.ArrPostid = append(staticField.ArrPostid, value.GetPostId())
		picId := hiphoto.EncodeId(uint64(value.GetId()), staticField.FidFromPicID)

		// 获取图片 strpicid => postid的集合
		staticField.ArrPicid2Postid[picId] = value.GetPostId()
		arrTempResult := new(clientPicpage.PicList)
		arrTempResult.OverallIndex = proto.Int64(common.Tvttt(strings.ReplaceAll(key, "#", ""), common.TTT_INT64).(int64))
		if staticField.IntIsNote == 1 && staticField.IntIsAiPic {
			arrTempResult.OverallIndex = proto.Int64(common.Tvttt(strings.ReplaceAll(key, "#", ""), common.TTT_INT64).(int64) + 1)
		}

		// 如果倒序浏览到楼主图片，重新计算overall_index
		if common.Tvttt(objReq.GetPrivateAttr("r", 0), common.TTT_INT).(int) == 1 &&
			arrTempResult.GetOverallIndex() > int64(staticField.IntNumOfLzPics) {
			arrTempResult.OverallIndex = proto.Int64(int64(staticField.IntNumOfPics) - arrTempResult.GetOverallIndex() +
				int64(staticField.IntNumOfLzPics) + 1)
		}

		if value.GetWidth() == 0 {
			value.Height = proto.Int32(320)
			value.Width = proto.Int32(320)
		}

		floatHWRatio := float64(value.GetHeight()) / float64(value.GetWidth())
		if floatHWRatio >= 3 && floatHWRatio <= 50 && value.GetWidth() >= 100 {
			arrTempResult.IsLongPic = proto.Int64(1)
			staticField.IntPicType = 1
		} else {
			arrTempResult.IsLongPic = proto.Int64(0)
		}

		arrPicExtra, err := php.Unserialize([]byte(value.GetPicExtra()))
		if err != nil {
			tbcontext.WarningF(ctx, "php unserialize fail:%v, extra:%s", err, value.GetPicExtra())
		}

		var waterUrl string
		if arrPicExtraMap, ok := arrPicExtra.(map[string]interface{}); ok {
			waterUrl = common.Tvttt(arrPicExtraMap["waterurl"], common.TTT_STRING).(string)
		}

		picInfo := &molibPhoto.PicInfo{
			Id:           value.GetId(),
			ForumId:      uint32(staticField.FidFromPicID),
			Width:        value.GetWidth(),
			Height:       value.GetHeight(),
			ForumName:    value.GetForumName(),
			CreateTime:   value.GetCreateTime(),
			Format:       value.GetFormat(),
			ScreenWidth:  value.GetScreenWidth(),
			ScreenHeight: value.GetScreenHeight(),
			PicFormat:    int(value.GetPicFormat()),
		}
		picExtra := &molibPhoto.PicExtra{
			Waterurl: waterUrl,
		}
		waterUrl = molibPhoto.GetPictureUrl(ctx, picInfo, picExtra, ImageDomain)
		arrTempResult.Img = &clientPicpage.Img{
			Original: &clientPicpage.ImgField{
				Id:       proto.String(picId),
				Width:    proto.Int64(int64(value.GetWidth())),
				Height:   proto.Int64(int64(value.GetHeight())),
				Size:     proto.Int64(value.GetSize()),
				Format:   proto.String(value.GetFormat()),
				Waterurl: proto.String(waterUrl),
				Pid:      proto.Int64(value.GetId()),
			},
		}

		arrTempResult.Author = new(client.User)
		arrTempResult.PostId = proto.Int64(value.GetPostId())
		arrTempResult.DescStr = proto.String(value.GetPicDesc())
		arrTempResult.UserId = proto.Int64(value.GetCreateUserId())
		arrTempResult.UserName = proto.String(value.GetCreateUserName())
		arrTempResult.AlbId = proto.String(getPbPicListRes.GetData().GetAlbumId())
		arrTempResult.ShowOriginalBtn = proto.Int64(1) // 默认显示“查看原图”按钮
		arrTempResult.Index = proto.Int64(int64(indexStart))
		if staticField.IntIsNote == 1 && staticField.IntIsAiPic {
			arrTempResult.Index = proto.Int64(int64(indexStart) + 1)
		}
		indexStart++
		staticField.ArrCreateUserID = append(staticField.ArrCreateUserID, value.GetCreateUserId())

		if staticField.IntIsImgCDN == 1 {
			spec := &imgcdn.ProcPic{
				ScreenWidth: staticField.IntScreenWidth,
				ScreenHeigh: staticField.IntScreenHeight,
				ScreenDip:   staticField.IntScreenDIP,
				QType:       staticField.IntQType,
			}
			imgTmp := arrTempResult.Img.GetOriginal()

			if imgTmp.GetWidth() >= imgTmp.GetHeight() && ((staticField.IntClientType == stcdefine.CLIENT_TYPE_ANDROID &&
				clientvers.Compare(staticField.StrClientVersion, "*********") <= 0) ||
				staticField.IntClientType == stcdefine.CLIENT_TYPE_IPHONE ||
				staticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY) {
				spec.NeedSampleBigPic = true
			}
			imgcdn.ProcPicPageNewPicUrl(arrTempResult, true, spec, staticField.IntPicStrategyType)
		}

		boolIsAllOrigin := true
		if clientvers.Compare(staticField.StrClientVersion, "7.0.0") == 1 {
			boolIsAllOrigin = false
		}

		urlStr, err := tbimg.GetUrlByEncodePicId(ctx, arrTempResult.Img.GetOriginal().GetId())
		if err != nil {
			tbcontext.WarningF(ctx, "GetUrlByEncodePicId fail:%v", err)
		}
		arrTempResult.Img.GetOriginal().Url = proto.String(urlStr)

		// 原图url判断
		if arrTempResult.Img.GetOriginal().GetSize() > 512000 {
			arrTempResult.Img.GetOriginal().OriginalSrc = proto.String(arrTempResult.Img.GetOriginal().GetUrl())
		} else {
			// 7.0以后 强行假装有大图
			if boolIsAllOrigin {
				arrTempResult.Img.GetOriginal().OriginalSrc = proto.String(arrTempResult.Img.GetOriginal().GetUrl())
			}
		}

		// 获取原图的post_id，用于折叠楼层过滤
		if arrTempResult.Img.GetOriginal().GetId() == staticField.StrPicID {
			staticField.PostID = arrTempResult.GetPostId()
		}

		staticField.ArrPicList = append(staticField.ArrPicList, arrTempResult)
	}

	return nil
}

func getAiPicInfo(ctx context.Context, baseData *types.CPicpageBaseData) error {
	if baseData == nil || baseData.StaticField == nil {
		tbcontext.WarningF(ctx, "get baseData or staticField fail")
		return nil
	}
	staticField := baseData.StaticField

	multi := tbservice.Multi()
	noteInfoParam := &tbservice.Parameter{
		Service: "common",
		Method:  "queryRecomNoteThreadByTid",
		Input: map[string]any{
			"thread_id": staticField.IntTid,
		},
		Output: &commonproto.QueryRecomNoteThreadByTidRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
			tbservice.WithRalName("common_go"),
		},
	}
	multi.Register(ctx, "queryRecomNoteThreadByTid", noteInfoParam)
	multi.Call(ctx)

	commonRes, err := multi.GetResult(ctx, "queryRecomNoteThreadByTid")
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service common:queryRecomNoteThreadByTid,err = %v, tid = %v, output = %v",
			err, staticField.IntTid, common.ToString(commonRes))
		return err
	}

	noteRes, ok := commonRes.(*commonproto.QueryRecomNoteThreadByTidRes)
	if !ok || noteRes.Errno == nil || noteRes.GetErrno() != int32(errno.ErrSuccess.Errno()) {
		tbcontext.WarningF(ctx, "fail to call service common:queryRecomNoteThreadByTid, tid = %v,output = %v",
			staticField.IntTid, common.ToString(noteRes))
		return err
	}

	if noteRes.Data == nil || noteRes.Data.PicUrls == nil {
		tbcontext.NoticeF(ctx, "get noteRes or PicUrls fail")
		return nil
	}

	if noteRes.Data.PicUrls != nil && len(noteRes.Data.PicUrls) > 0 && noteRes.Data.PicUrls[0] != "" && strings.Index(noteRes.Data.PicUrls[0], "tiebapic") != -1 {
		staticField.IntIsAiPic = true
		staticField.IntNumOfPics += 1

		aiPic, err := image.GenAuthUrl(noteRes.Data.PicUrls[0])
		if err != nil {
			tbcontext.WarningF(ctx, "get gen auth url fail: %v", err)
			return err
		}

		picSign := hiphoto.GetPicSignFromUrl(aiPic)
		if picSign == "" {
			tbcontext.WarningF(ctx, "get pic sign fail")
			return err
		}

		picId := hiphoto.DecodePicUrlCrypt(picSign)
		if picId == 0 {
			tbcontext.WarningF(ctx, "get pic id fail")
			return err
		}

		multiPicInfo, err := space.QueryMultiPictureInfo(ctx, []uint64{picId})
		if err != nil {
			tbcontext.WarningF(ctx, "get QueryMultiPictureInfo fail: %v", err)
			return err
		}

		width := multiPicInfo.PicList[0].GetWidth()
		height := multiPicInfo.PicList[0].GetHeight()
		picSpec := fmt.Sprintf("whcrop=%d,%d;g=0", width, height)
		if width == 0 || height == 0 {
			picSpec = CoverPicSpecG
		}

		aiPicInfo := new(clientPicpage.PicList)
		aiPicInfo.OverallIndex = proto.Int64(1)

		aiPicInfo.Img = &clientPicpage.Img{
			Original: &clientPicpage.ImgField{
				Id:        proto.String(hiphoto.EncodeId(staticField.IntFirstPostID, staticField.FidFromPicID)),
				Width:     proto.Int64(int64(width)),
				Height:    proto.Int64(int64(height)),
				Url:       proto.String(aiPic),
				BigCdnSrc: proto.String(tbimg.GenerateNewUrl(aiPic, true, picSpec)),
			},
		}
		aiPicInfo.PostId = proto.Int64(staticField.CurrentPostID)

		staticField.ArrPicList = append(staticField.ArrPicList, aiPicInfo)
	}

	return nil
}

func get60841PicInfo(ctx context.Context, baseData *types.CPicpageBaseData) error {
	if baseData == nil || baseData.StaticField == nil {
		tbcontext.WarningF(ctx, "get baseData or staticField fail")
		return nil
	}
	staticField := baseData.StaticField
	getQueryReviewsReq := &commonproto.MgetQueryReviewsByCondReq{
		Tid: proto.Int64(staticField.IntTid),
	}
	getQueryReviewsRes := &commonproto.MgetQueryReviewsByCondRes{}

	mgetQueryReviewsOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("common_go"),
	}

	err := tbservice.Call(ctx, "common", "mgetQueryReviewsByCond", getQueryReviewsReq, &getQueryReviewsRes, mgetQueryReviewsOption...)
	if err != nil || getQueryReviewsRes == nil || getQueryReviewsRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call common_go:mgetQueryReviewsByCond fail, err:[%v], output:[%s] input:[%s]",
			err, common.ToString(getQueryReviewsRes), common.ToString(getQueryReviewsRes))
		return err
	}

	if getQueryReviewsRes.Data == nil || len(getQueryReviewsRes.Data.Info) == 0 {
		tbcontext.NoticeF(ctx, "get queryReviewsByCondData fail, no data")
		return fmt.Errorf("get queryReviewsByCondData fail, no data")
	}

	imageURL := getQueryReviewsRes.GetData().GetInfo()[0].ImageUrl

	if *imageURL == "" {
		tbcontext.NoticeF(ctx, "get image url fail")
		return fmt.Errorf("get image url fail")
	}

	staticField.IntNumOfPics += 1

	authPic, err := image.GenAuthUrl(*imageURL)
	if err != nil {
		tbcontext.WarningF(ctx, "get gen auth url fail: %v", err)
		return err
	}

	picSign := hiphoto.GetPicSignFromUrl(authPic)
	if picSign == "" {
		tbcontext.WarningF(ctx, "get pic sign fail")
		return err
	}

	picId := hiphoto.DecodePicUrlCrypt(picSign)
	if picId == 0 {
		tbcontext.WarningF(ctx, "get pic id fail")
		return err
	}

	multiPicInfo, err := space.QueryMultiPictureInfo(ctx, []uint64{picId})
	if err != nil {
		tbcontext.WarningF(ctx, "get QueryMultiPictureInfo fail: %v", err)
		return err
	}

	width := multiPicInfo.PicList[0].GetWidth()
	height := multiPicInfo.PicList[0].GetHeight()
	picSpec := fmt.Sprintf("whcrop=%d,%d;g=0", width, height)
	if width == 0 || height == 0 {
		picSpec = CoverPicSpecG
	}

	aiPicInfo := new(clientPicpage.PicList)
	aiPicInfo.OverallIndex = proto.Int64(1)

	aiPicInfo.Img = &clientPicpage.Img{
		Original: &clientPicpage.ImgField{
			Id:        proto.String(hiphoto.EncodeId(staticField.IntFirstPostID, staticField.FidFromPicID)),
			Width:     proto.Int64(int64(width)),
			Height:    proto.Int64(int64(height)),
			Url:       proto.String(authPic),
			BigCdnSrc: proto.String(tbimg.GenerateNewUrl(authPic, true, picSpec)),
		},
	}
	aiPicInfo.PostId = proto.Int64(staticField.CurrentPostID)

	staticField.ArrPicList = append(staticField.ArrPicList, aiPicInfo)
	return nil
}

func filterTopAgreePic(staticField *types.PicpageStaticField, data *photo.GetPbPicListData) {
	for _, value := range data.GetPicList() {
		picId := hiphoto.EncodeId(uint64(value.GetId()), staticField.FidFromPicID)
		if picId == staticField.StrPicID {
			staticField.PostID = value.GetPostId()
			break
		}
	}

	// 过滤非本楼层的数据
	newPicList := make(map[string]*photo.Pic)
	idx := 1
	for _, value := range data.GetPicList() {
		if staticField.PostID == value.GetPostId() {
			newPicList[strconv.Itoa(idx)] = value
			idx++
		}
	}

	data.PicCount = proto.Int32(int32(len(newPicList)))
	data.PicList = newPicList
}

func calPicNumber(ctx context.Context, baseData *types.CPicpageBaseData, picListData *photo.GetPbPicListData) {
	staticField := baseData.StaticField
	if picListData != nil {
		staticField.IntNumOfPics = picListData.GetPicCount()
	}

	// 删除投票贴图片
	if staticField.ArrThreadInfo != nil && staticField.ArrThreadInfo.ThreadTypes != nil {
		threadType := threadtype.GetThreadType(staticField.ArrThreadInfo.GetThreadTypes())
		if threadType["is_poll"] {
			staticField.IntNumOfPics = staticField.IntNumOfPics - deletePollImg(ctx, staticField)
		}
	}

	//添加活动帖的图片总数
	if staticField.ArrThreadInfo.GetThreadTypes()>>32 == threadtype.ACTSTAGE_THREAD {
		input := map[string]interface{}{
			"thread_id": staticField.IntTid,
			"forum_id":  staticField.IntFid,
			"user_id":   staticField.ArrThreadInfo.GetUserId(),
		}
		getActivityInfoRes := new(actstage.GetActivityInfoRes)
		err := tbservice.Call(ctx, "actstage", "getActivityInfo", input, getActivityInfoRes, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || getActivityInfoRes.Errno == nil || getActivityInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call actstage::getActivityInfo fail, input :%v, output:%v, err:%v",
				input, common.ToString(getActivityInfoRes), err)
			return
		}

		strConf := common.Tvttt(baseData.BaseObj.ObjRequest.GetStrategy("pb_activity_new", ""), common.TTT_STRING).(string)
		// 判断版本, 6.9.5 之后抽奖晒图活动图片不需要特殊处理
		if ok, _ := versionmatch.CheckClient(strConf, staticField.IntClientType, staticField.StrClientVersion, "app"); !ok {
			if getActivityInfoRes.GetData() != nil && getActivityInfoRes.GetData().GetActInfo() != nil &&
				getActivityInfoRes.GetData().GetActInfo().GetActivityType() == 2 {
				actInfo := getActivityInfoRes.GetData().GetActInfo()
				//晒图的处理 取参与活动的图 获取活动参与人前三个数据
				input = map[string]interface{}{
					"activity_id": actInfo.GetActivityId(),
					"pn":          1,
					"rn":          3,
				}
				getPhotoInfoByIdRes := new(actstage.GetPhotoInfoByIdRes)
				err := tbservice.Call(ctx, "actstage", "getPhotoInfoById", input, getPhotoInfoByIdRes, tbservice.WithConverter(tbservice.JSONITER))
				if err != nil || getPhotoInfoByIdRes.Errno == nil || getPhotoInfoByIdRes.GetErrno() != tiebaerror.ERR_SUCCESS {
					tbcontext.WarningF(ctx, "call actstage::getPhotoInfoById fail, input :%v, output:%v, err:%v",
						input, common.ToString(getPhotoInfoByIdRes), err)
				}

				shaiPicNum := len(getPhotoInfoByIdRes.GetData().GetList())
				if shaiPicNum > 3 {
					staticField.IntNumOfPics += 3
				} else {
					staticField.IntNumOfPics += int32(shaiPicNum)
				}

			} else {
				staticField.IntNumOfPics = staticField.IntNumOfPics + int32(len(getActivityInfoRes.GetData().GetAwardInfo()))
			}

			if staticField.StrPicfrom == "" || staticField.StrPicfrom == "pb" {
				staticField.IntNumOfPics += 1
			}
		}

	}

}

// deletePollImg 投票贴图片在大图里不展示, 删除图片数
func deletePollImg(ctx context.Context, staticField *types.PicpageStaticField) int32 {
	input := map[string]interface{}{
		"thread_id":          staticField.IntTid, //帖子id
		"offset":             0,
		"res_num":            1,
		"see_author":         0,
		"has_comment":        0,
		"has_mask":           0,
		"has_ext":            1,
		"need_set_pv":        0,
		"structured_content": 1,
	}

	getPostsByThreadIdRes := new(pb.GetPostsByThreadIdRes)
	err := tbservice.Call(ctx, "post", "getPostsByThreadId", input, getPostsByThreadIdRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getPostsByThreadIdRes.Errno == nil || getPostsByThreadIdRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call post::getPostsByThreadId fail, input :%v, output:%v, err:%v",
			input, common.ToString(getPostsByThreadIdRes), err)
		return 0
	}

	imgCount := int32(0)
	if getPostsByThreadIdRes.GetOutput() != nil && getPostsByThreadIdRes.GetOutput().GetOutput() != nil &&
		len(getPostsByThreadIdRes.GetOutput().GetOutput()) > 0 && len(getPostsByThreadIdRes.GetOutput().GetOutput()[0].GetPostInfos()) > 0 {
		arrThreadInfo := getPostsByThreadIdRes.GetOutput().GetOutput()[0].GetPostInfos()[0]
		content, _ := arrThreadInfo.GetContentStruct()
		for _, v := range content {
			if v.GetTag() == "img" {
				imgCount++
			}
		}
	}

	return imgCount
}

func getPostAndAgreeInfo(ctx context.Context, staticField *types.PicpageStaticField, baseData *types.CPicpageBaseData) {
	multi := tbservice.Multi()
	getPostInfoParam := &tbservice.Parameter{
		Service: "post",
		Method:  "getPostInfo",
		Input: map[string]interface{}{
			"post_ids":           staticField.ArrPostid,
			"structured_content": 1,
		},
		Output: &pb.GetPostInfoRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getPostInfo", getPostInfoParam)

	if staticField.UserID > 0 {
		// 只能获取楼层的，不能获取主题帖的
		getAgreeByUserIdAndPostIdsParam := &tbservice.Parameter{
			Service: "agree",
			Method:  "getAgreeByUserIdAndPostIds",
			Input: map[string]interface{}{
				"post_ids":  staticField.ArrPostid,
				"user_id":   staticField.UserID,
				"thread_id": staticField.IntTid,
			},
			Output: &agree.GetAgreeByUserIdAndPostIdsRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getAgreeByUserIdAndPostIds", getAgreeByUserIdAndPostIdsParam)

		// 获取主题帖的点赞信息
		getAgreeByUserIdAndThreadIdParam := &tbservice.Parameter{
			Service: "agree",
			Method:  "getAgreeByUserIdAndThreadId",
			Input: map[string]interface{}{
				"user_id":   staticField.UserID,
				"thread_id": staticField.IntTid,
			},
			Output: &agree.GetAgreeByUserIdAndThreadIdRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getAgreeByUserIdAndThreadId", getAgreeByUserIdAndThreadIdParam)
	}

	antiTbmaskQueryParam := &tbservice.Parameter{
		Service: "anti",
		Method:  "antiTbmaskQuery",
		Input: map[string]interface{}{
			"req": map[string]interface{}{
				"id_list":   staticField.ArrCreateUserID,
				"mask_list": []string{"anti_browse"},
				"strMethod": "tbmask_query",
			},
		},
		Output: &anti.AntiTbmaskQueryRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "antiTbmaskQuery", antiTbmaskQueryParam)

	multi.Call(ctx)

	getPostInfoInter, err := multi.GetResult(ctx, "getPostInfo")
	getPostInfoRes := getPostInfoInter.(*pb.GetPostInfoRes)
	if err != nil || getPostInfoRes.Errno == nil || getPostInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call post::getPostInfo fail, output: %v, err: %v", common.ToString(getPostInfoRes), err)
	} else {
		staticField.ArrPostInfo = getPostInfoRes.GetOutput()
		for _, val := range staticField.ArrPostInfo {
			tpoint, err := val.GetPbTpointStruct()
			if err == nil && tpoint != nil && tpoint.GetIsTpoint() == 1 {
				staticField.ArrTpoint = append(staticField.ArrTpoint, int64(val.GetPostId()))
			}
		}
	}

	if staticField.UserID > 0 {
		getAgreeByUserIdAndPostIdsInter, err := multi.GetResult(ctx, "getAgreeByUserIdAndPostIds")
		getAgreeByUserIdAndPostIdsRes := getAgreeByUserIdAndPostIdsInter.(*agree.GetAgreeByUserIdAndPostIdsRes)
		if err != nil || getAgreeByUserIdAndPostIdsRes.Errno == nil || getAgreeByUserIdAndPostIdsRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call agree::getAgreeByUserIdAndPostIds fail, output: %v, err: %v",
				common.ToString(getAgreeByUserIdAndPostIdsRes), err)
		} else {
			staticField.ArrPostUserAgree = getAgreeByUserIdAndPostIdsRes.GetData().GetMap()
		}

		getAgreeByUserIdAndThreadIdInter, err := multi.GetResult(ctx, "getAgreeByUserIdAndThreadId")
		getAgreeByUserIdAndThreadIdRes := getAgreeByUserIdAndThreadIdInter.(*agree.GetAgreeByUserIdAndThreadIdRes)
		if err != nil || getAgreeByUserIdAndThreadIdRes.Errno == nil || getAgreeByUserIdAndThreadIdRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call agree::getAgreeByUserIdAndThreadId fail, output: %v, err: %v",
				common.ToString(getAgreeByUserIdAndThreadIdRes), err)
		} else {
			staticField.ArrThreadUserAgree = getAgreeByUserIdAndThreadIdRes.GetData().GetList()
		}
	}

	antiTbmaskQueryInter, err := multi.GetResult(ctx, "antiTbmaskQuery")
	antiTbmaskQueryRes := antiTbmaskQueryInter.(*anti.AntiTbmaskQueryRes)
	if err != nil || antiTbmaskQueryRes.Errno == nil || antiTbmaskQueryRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call anti::antiTbmaskQuery fail, output: %v, err: %v",
			common.ToString(antiTbmaskQueryRes), err)
	} else {
		maskList := antiTbmaskQueryRes.GetRes().GetResList()
		continuous := false // 是否是连续封禁
		removeIdx := make(map[int]bool)
		lastPic := staticField.ArrPicList[len(staticField.ArrPicList)-1]
		for key, maskInfo := range maskList {
			if maskInfo.GetAntiBrowse() == 0 {
				if len(staticField.ArrPicList) > key {
					staticField.ArrPicList[key].IsBlockedPic = proto.Int64(0)
				}
				continuous = false
			} else if !continuous || (continuous && len(staticField.ArrPicList) > key &&
				staticField.ArrPicList[key].GetImg().GetOriginal().GetId() == staticField.StrPicID) {
				setBlockPic(staticField, key)
				continuous = true
			} else {
				removeIdx[key] = true
			}
		}

		newPicList := make([]*clientPicpage.PicList, 0)
		for key, val := range staticField.ArrPicList {
			if _, ok := removeIdx[key]; !ok {
				newPicList = append(newPicList, val)
			}
		}
		staticField.ArrPicList = newPicList

		curLastPic := staticField.ArrPicList[len(staticField.ArrPicList)-1]
		if continuous && lastPic.GetOverallIndex() != curLastPic.GetOverallIndex() {
			lastPic.Img.Original.BigCdnSrc = proto.String(BlockedImageURL)
			lastPic.Img.Original.Waterurl = proto.String(BlockedImageURL)
			lastPic.Img.Original.Url = proto.String(BlockedImageURL)
			lastPic.Img.Original.OriginalSrc = proto.String(BlockedImageURL)
			lastPic.IsBlockedPic = proto.Int64(1)
			staticField.ArrPicList = append(staticField.ArrPicList, lastPic)
		}

		// 在封禁大图的基础上，判断楼层号是否被折叠
		// 进行版本控制，折叠楼层在9.1版本上线的
		if clientvers.Compare("9.1", staticField.StrClientVersion) >= 0 {
			filterFoldPic(ctx, staticField)
		}

	}
}

func filterFoldPic(ctx context.Context, staticField *types.PicpageStaticField) {
	formatPostInfo(staticField)
	removeIdx := make(map[int]bool)
	for key, picInfo := range staticField.ArrPicList {
		postId := picInfo.GetPostId()
		if staticField.FormatPostInfo[uint64(postId)] != nil && staticField.FormatPostInfo[uint64(postId)].GetIsFold() == 2 {
			// 不处理第一次请求原图的楼层，因封禁图像优先级高，同样不处理封禁插入的情感图
			if isUserClickReq(staticField) && postId == staticField.PostID || picInfo.GetIsBlockedPic() == 1 {
				continue
			}

			// 对头尾两端的图像，用体感图替换，客户端请求的原图也用体感图代替
			if key == 0 || key == len(staticField.ArrPicList)-1 || picInfo.GetImg().GetOriginal().GetId() == staticField.StrPicID {
				setBlockPic(staticField, key)
				continue
			}

			removeIdx[key] = true
		}
	}

	newPicList := make([]*clientPicpage.PicList, 0)
	for key, val := range staticField.ArrPicList {
		if _, ok := removeIdx[key]; !ok {
			newPicList = append(newPicList, val)
		}
	}
	staticField.ArrPicList = newPicList
}

// isUserClickReq 需要判断是用户点击进入大图页请求还是滑动进入大图页请求
func isUserClickReq(staticField *types.PicpageStaticField) bool {
	if staticField.IntClientType == stcdefine.CLIENT_TYPE_IPHONE && staticField.IntNext > 0 && staticField.IntPrev > 0 ||
		staticField.IntClientType == stcdefine.CLIENT_TYPE_ANDROID ||
		staticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
		return true
	}

	return false
}

func formatPostInfo(staticField *types.PicpageStaticField) {
	for _, postInfo := range staticField.ArrPostInfo {
		staticField.FormatPostInfo[postInfo.GetPostId()] = postInfo
	}
}

// blockPic 将原图替换成封禁图
func setBlockPic(staticField *types.PicpageStaticField, idx int) {
	if len(staticField.ArrPicList) > idx {
		staticField.ArrPicList[idx].Img.Original.BigCdnSrc = proto.String(BlockedImageURL)
		staticField.ArrPicList[idx].Img.Original.Waterurl = proto.String(BlockedImageURL)
		staticField.ArrPicList[idx].Img.Original.Url = proto.String(BlockedImageURL)
		staticField.ArrPicList[idx].Img.Original.OriginalSrc = proto.String(BlockedImageURL)
		staticField.ArrPicList[idx].IsBlockedPic = proto.Int64(1)
	}
}

func processRepeat(baseData *types.CPicpageBaseData) {
	objReq := baseData.BaseObj.ObjRequest
	staticField := baseData.StaticField
	if common.Tvttt(objReq.GetPrivateAttr("r", 0), common.TTT_INT).(int) == 1 {
		sort.Slice(staticField.ArrPicList, func(i, j int) bool {
			return staticField.ArrPicList[i].GetOverallIndex() < staticField.ArrPicList[j].GetOverallIndex()
		})
	}

	if len(staticField.ArrPicList) == 0 {
		return
	}

	if staticField.IntNext > 0 {
		// 正向取数据，将pic_id及之前的图片删除
		if staticField.StrPicID == "" {
			staticField.ArrPicList = staticField.ArrPicList[:len(staticField.ArrPicList)-1]
			return
		}

		lengthStrPicID := len(staticField.StrPicID)

		var arrBakPicList []*clientPicpage.PicList
		// 备份列表，大图页非楼主图片时，点击只看楼主，不去重。
		if staticField.IntNotSeeLz == 0 {
			arrBakPicList = staticField.ArrPicList
		}

		existCurPid := false
		removeIdx := make(map[int]bool, 0)
		for key, value := range staticField.ArrPicList {
			//新增比较长度的逻辑(strlen)
			//原因: 这里的逻辑是用strcmp函数，比较id的大小
			//      但图片的id有2种：老url和新url
			//      老url， id长度为24; 新url,  id长度为40
			//      新老id不具可比性， 因此这里判断下长度。 不同版本的id，不比较
			if len(value.GetImg().GetOriginal().GetId()) == lengthStrPicID && value.GetImg().GetOriginal().GetId() != staticField.StrPicID {
				removeIdx[key] = true
			} else {
				if !staticField.BigPic {
					removeIdx[key] = true
				}
				existCurPid = true
				break
			}
		}

		newPicList := make([]*clientPicpage.PicList, 0)
		for key, value := range staticField.ArrPicList {
			if _, ok := removeIdx[key]; !ok {
				newPicList = append(newPicList, value)
			}
		}
		staticField.ArrPicList = newPicList

		// 如果只看楼主状态，且列表中不存在当前图片，则不去重
		if staticField.IntNotSeeLz == 0 && !existCurPid {
			staticField.ArrPicList = arrBakPicList
		}
	} else if staticField.IntPrev > 0 {
		// //反向取数据，将pic_id及之后的数据删除
		if staticField.StrPicID == "" {
			return
		}

		newPicList := make([]*clientPicpage.PicList, 0)
		for _, value := range staticField.ArrPicList {
			if value.GetImg().GetOriginal().GetId() != staticField.StrPicID {
				newPicList = append(newPicList, value)
			} else {
				if staticField.BigPic {
					newPicList = append(newPicList, value)
				}
				staticField.ArrPicList = newPicList
				break
			}
		}
	}
}

// 大图页广告请求优化
// 策略：1. 只要满足最后一刷5<n<=10就按照n/2分两次下发；2. n <= 5的直接下发；3.其他情况直接截取最后5张留待下次下发
func cutPicListForAdOpti(staticField *types.PicpageStaticField) {
	// ios端bug 12.44版之前不进行优化
	if staticField.IntClientType == stcdefine.CLIENT_TYPE_IPHONE && clientvers.Compare("12.44.0", staticField.StrClientVersion) < 0 {
		return
	}
	if len(staticField.ArrPicList) == 0 {
		return
	}
	lastPic := staticField.ArrPicList[len(staticField.ArrPicList)-1]
	picListNum := int32(len(staticField.ArrPicList))
	minLimit := int32(5)
	maxLimit := int32(10)
	picOrderIndex := int64(0)

	for _, pic := range staticField.ArrPicList {
		if staticField.StrPicID == pic.GetImg().GetOriginal().GetId() {
			picOrderIndex = pic.GetOverallIndex()
			break
		}
	}

	// 图片数量是不是只有一刷
	returnNum := int32(0)
	if picListNum == staticField.IntNumOfPics {
		if picListNum <= minLimit {
			return
		} else if picListNum > minLimit && picListNum <= maxLimit {
			returnNum = int32(math.Ceil(float64(picListNum) / 2))
			if staticField.IntPicStrategyType == 2 && int32(picOrderIndex) < returnNum {
				staticField.ArrPicList = staticField.ArrPicList[:returnNum]
			}
		} else {
			returnNum = int32(len(staticField.ArrPicList)) - 5
			if staticField.IntPicStrategyType == 2 && int32(picOrderIndex) < returnNum {
				staticField.ArrPicList = staticField.ArrPicList[:returnNum]
			}
		}

		return
	}

	// 最后一刷
	if lastPic.GetOverallIndex() == int64(staticField.IntNumOfPics) {
		// 非首刷会携带上一刷的最后一张，这里对阈值5做+1
		if picListNum != staticField.IntNumOfPics {
			minLimit++
			maxLimit++
		}

		if picListNum <= minLimit {
			return
		} else if picListNum > minLimit && picListNum <= maxLimit {
			// 向上取整
			returnNum = (picListNum-1)/2 + 1
		} else {
			returnNum = int32(len(staticField.ArrPicList)) - 5
		}
		if picOrderIndex < int64(returnNum) {
			staticField.ArrPicList = staticField.ArrPicList[:returnNum]
		}
		return
	}

	// 非最后一刷，计算下后面还有多少张
	left := staticField.IntNumOfPics - int32(lastPic.GetOverallIndex())
	if left < minLimit && picListNum+left >= maxLimit {
		cutNum := minLimit - left
		staticField.ArrPicList = staticField.ArrPicList[:picListNum-cutNum]
	}
}
