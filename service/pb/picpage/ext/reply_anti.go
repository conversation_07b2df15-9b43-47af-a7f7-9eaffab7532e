package ext

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/service"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/usertool"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ReplyAnti struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("picpage_reply_anti", func() engine.Job {
		return &ReplyAntiOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewReplyAnti(ctx *engine.Context) *ReplyAnti {
	return &ReplyAnti{
		ctx: ctx,
	}
}
func (a *ReplyAnti) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {
	staticField := baseData.StaticField
	if staticField.IsBjh == 1 || clientvers.Compare("11.8.0", staticField.StrClientVersion) < 0 {
		return false
	}
	return true
}
func (a *ReplyAnti) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	//11.8 新增大图页回复功能，需要下发用户是否可以回复标识anti信息
	staticField := baseData.StaticField
	getReplyInfo(ctx, staticField)
	buildPicUser(staticField)
	buildAnti(ctx, baseData)
	buildThread(staticField)
	return nil
}

type ReplyAntiOperator struct {
}

func (rdop *ReplyAntiOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewReplyAnti(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "reply_anti execute fail: %v", err)
		return err
	}

	return nil
}

// 回去用户是否可以回复标识
func getReplyInfo(ctx context.Context, staticField *types.PicpageStaticField) {
	userIDs := make([]int64, 0)
	userMap := make(map[int64]bool)
	for _, picInfo := range staticField.ArrPicList {
		userMap[picInfo.GetUserId()] = true
	}
	for userID := range userMap {
		userIDs = append(userIDs, userID)
	}

	authorID := staticField.ArrThreadInfo.GetUserId()
	loginUserID := staticField.UserID
	userIDs = append(userIDs, authorID)

	multi := tbservice.Multi()
	getPostInfoParam := &tbservice.Parameter{
		Service: "user",
		Method:  "mgetUserData",
		Input: map[string]interface{}{
			"user_id":            userIDs,
			"structured_content": 1,
		},
		Output: &user.MgetUserDataRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetUserData", getPostInfoParam)

	if staticField.ArrForumInfo.GetId() != 0 && loginUserID != 0 {
		getPostInfoParam := &tbservice.Parameter{
			Service: "perm",
			Method:  "getPerm",
			Input: map[string]interface{}{
				"user_id":  loginUserID,
				"forum_id": staticField.ArrForumInfo.GetId(),
				"user_ip":  0,
				"user_ip6": "",
				"ie":       "utf-8",
			},
			Output: &perm.GetPermRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getPerm", getPostInfoParam)
	}

	multi.Call(ctx)

	if staticField.ArrForumInfo.GetId() != 0 && loginUserID != 0 {
		getPermInter, err := multi.GetResult(ctx, "getPerm")
		getPermRes := getPermInter.(*perm.GetPermRes)
		if err != nil || getPermRes.Errno == nil || getPermRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call perm::getPerm fail, output: %v, err: %v",
				common.ToString(getPermRes), err)
		} else {
			staticField.ArrPerm = getPermRes.GetOutput()
		}
	}

	staticField.ReplyPrivateFlag = 1
	mgetUserDataInter, err := multi.GetResult(ctx, "mgetUserData")
	mgetUserDataRes := mgetUserDataInter.(*user.MgetUserDataRes)
	if err != nil || mgetUserDataRes.Errno == nil || mgetUserDataRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call user::mgetUserData fail, output: %v, err: %v",
			common.ToString(mgetUserDataRes), err)
		return
	}

	staticField.ArrUserInfos = mgetUserDataRes.GetUserInfo()
	staticField.ArrUserInfo = staticField.ArrUserInfos[authorID]
	replyPrivateInfo := staticField.ArrUserInfo.GetPrivSets().GetReply()
	//获取帖子是否只读
	readOnly := staticField.ArrThreadInfo.GetReadonly()

	//>=12.26 判断帖子是否被只读处置,只能主态发帖
	if clientvers.IsLegalVersion("12_26_0", staticField.IntClientType, staticField.StrClientVersion) &&
		loginUserID != authorID && readOnly == 1 {
		staticField.ReplyPrivateFlag = 5
		return
	}

	if replyPrivateInfo == 1 || replyPrivateInfo == 0 {
		return
	}

	//v12.26新增only me 仅自己可回复 逻辑 加上版本控制>=12.25.0
	if replyPrivateInfo == types.ReplyPrivateOnlyMe &&
		clientvers.IsLegalVersion("12_25_0", staticField.IntClientType, staticField.StrClientVersion) && loginUserID != authorID {
		staticField.ReplyPrivateFlag = 4
		return
	}

	//其他同大图页线上逻辑，只处理了5和6，之前判断$intFromUser 和 $intToUser，实际非5和6时未定义，则提前； 但是没对齐pb逻辑，pb还有很多逻辑，会对非5和6如7做判断 ，12.26新提非跟版需求，前面一行处理4
	if loginUserID == authorID || !php2go.InArray(replyPrivateInfo, []int32{types.ReplyPrivateOnlyFans, types.ReplyPrivateOnlyConcern}) {
		return
	}

	var fromUser, toUser int64
	if types.ReplyPrivateOnlyFans == replyPrivateInfo {
		fromUser = loginUserID
		toUser = authorID
	} else if types.ReplyPrivateOnlyConcern == replyPrivateInfo {
		fromUser = authorID
		toUser = loginUserID
	}

	input := map[string]interface{}{
		"user_id": fromUser,
		"req_user_id": []int64{
			toUser,
		},
	}
	getUserFollowInfoRes := new(user.GetUserFollowInfoRes)
	err = tbservice.Call(ctx, "user", "getUserFollowInfo", input, getUserFollowInfoRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getUserFollowInfoRes.Errno == nil || getUserFollowInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "Call user:getUserFollowInfo failed. input:%v, output:%v, err:%v", input, common.ToString(getUserFollowInfoRes), err)
		return
	}

	if len(getUserFollowInfoRes.GetResUserInfos()) > 0 && getUserFollowInfoRes.GetResUserInfos()[0].GetIsFollowed() != 1 {
		switch replyPrivateInfo {
		case types.ReplyPrivateOnlyFans:
			staticField.ReplyPrivateFlag = 2
		case types.ReplyPrivateOnlyConcern:
			staticField.ReplyPrivateFlag = 3
		default:
			staticField.ReplyPrivateFlag = 1
		}
	}
}

func buildPicUser(staticField *types.PicpageStaticField) {
	for _, picInfo := range staticField.ArrPicList {
		for uid, userInfo := range staticField.ArrUserInfos {
			if picInfo.GetUserId() == uid {
				nickname := userInfo.GetUserName()
				if userInfo.GetUserNickname() != "" {
					nickname = userInfo.GetUserNickname()
				}
				param := &usertool.NickNameByVersion{
					UserNickNameV2: userInfo.GetUserNicknameV2(),
					DisplayName:    userInfo.GetDisplayName(),
				}
				picInfo.Nickname = proto.String(
					usertool.GetUserNickNameByVersion(staticField.IntClientType, staticField.StrClientVersion, param, nickname))
				picInfo.Protrait = proto.String(tbportrait.Encode(uid, userInfo.GetUserName(), 0))
			}
		}
	}
}

func buildAnti(ctx context.Context, baseData *types.CPicpageBaseData) {
	staticField := baseData.StaticField
	levelId := staticField.ArrPerm.GetGrade().GetLevelId()
	isLike := staticField.ArrPerm.GetGrade().GetIsLike()
	input := map[string]int64{
		"user_id": staticField.UserID,
		"level":   int64(levelId),
		"is_like": int64(isLike),
	}
	voiceSwitch := service.VoiceSwitch(ctx, input, staticField.ArrForumInfo.GetName())
	staticField.ArrAnti = &client.Anti{
		ReplyPrivateFlag: proto.Int32(int32(staticField.ReplyPrivateFlag)),
		Ifvoice:          proto.Int32(int32(voiceSwitch.Switch)),
		VoiceMessage:     proto.String(voiceSwitch.Message),
	}

	if staticField.UserID > 0 {
		staticField.ArrAnti.Tbs = proto.String(tbs.CreateTbs(ctx, baseData.BaseObj.Req).Gene(true))
	} else {
		staticField.ArrAnti.Tbs = proto.String(tbs.CreateTbs(ctx, baseData.BaseObj.Req).Gene(false))
	}

}

func buildThread(staticField *types.PicpageStaticField) {
	staticField.AuthorInfo = &clientPicpage.UserInfo{
		UserId:   proto.Int64(staticField.ArrUserInfo.GetUserId()),
		UserName: proto.String(staticField.ArrUserInfo.GetUserName()),
		Nickname: proto.String(staticField.ArrUserInfo.GetUserNickname()),
		Protrait: proto.String(tbportrait.Encode(staticField.ArrUserInfo.GetUserId(), staticField.ArrUserInfo.GetUserName(), 0)),
	}

	shareThread := int32(0)
	if staticField.ArrThreadInfo.GetOriginalTid() > 0 {
		shareThread = 1
	}
	threadTypes := staticField.ArrThreadInfo.GetThreadTypes()
	isRepostThread := int32(0)
	if (threadTypes & 0x800) == 0x800 {
		isRepostThread = 1
	}
	staticField.ArrOutThread = &clientPicpage.ThreadInfo{
		ThreadId:           proto.Int64(staticField.IntTid),
		FirstPostId:        proto.Int64(int64(staticField.IntFirstPostID)),
		IsShareThread:      proto.Int32(shareThread),
		IsMultiforumThread: proto.Int32(isRepostThread),
		Author:             staticField.AuthorInfo,
	}
}
