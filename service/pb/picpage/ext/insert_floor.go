package ext

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/yylive"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/ala"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/yuelao2"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/stlog"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/php"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/golib2/trace"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type InsertFloor struct {
	ctx *engine.Context
}

// init 函数用于在程序启动时注册InsertFloorOperator操作符。
func init() {
	err := engine.RegisterOperator("picpage_insert_floor", func() engine.Job {
		return &InsertFloorOperator{}
	})
	if err != nil {
		panic(err)
	}
}

// NewInsertFloor 函数为新创建的 InsertFloor 对象生成一个指针
// 参数 ctx 表示引擎上下文对象指针
// 返回值：返回指向 InsertFloor 对象的指针
func NewInsertFloor(ctx *engine.Context) *InsertFloor {
	return &InsertFloor{
		ctx: ctx,
	}
}

// IsValid 函数用于判断 InsertFloor 是否有效
// 参数 ctx：用于上下文信息的 Context 对象
// 参数 baseData：包含静态字段信息的 CPicpageBaseData 对象
// 返回值：返回一个布尔值，表示是否为有效的 InsertFloor
func (a *InsertFloor) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {
	staticField := baseData.StaticField
	if clientvers.Compare("12.50.0", staticField.StrClientVersion) < 0 {
		return false
	}

	// 用户开启了屏蔽直播，不返回相关数据
	if staticField.UserAdSet.GetData().GetNeedCloseLive() == 1 {
		return false
	}
	//鸿蒙暂不支持插楼
	if staticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
		return false
	}
	return true
}

// Execute 函数用于执行插入楼层操作
func (a *InsertFloor) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField

	if len(staticField.ArrPicList) == 0 {
		return nil
	}

	trace.TimerStart(ctx, "InsertFloor")
	insertFloor(ctx, staticField, baseData)
	trace.TimerEnd(ctx, "InsertFloor")

	return nil
}

type InsertFloorOperator struct {
}

// DoImpl 函数是 InsertFloorOperator 的方法
// 参数 ctx 是 engine.Context 类型的指针，用于获取上下文信息
// 返回值为 error 类型
func (rdop *InsertFloorOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewInsertFloor(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "InsertFloor execute fail: %v", err)
		return err
	}

	return nil
}

// insertFloor 函数将一个静态字段与插楼结构中的内容进行关联并插入到staticField中
func insertFloor(ctx context.Context, staticField *types.PicpageStaticField, baseData *types.CPicpageBaseData) {
	objReq := baseData.BaseObj.ObjRequest

	//先判断一下命中实验的逻辑
	isShowLive := false
	testDisplayYyLive := "12_49_show_live_floor_a"
	arrAbtestConfigs := UbsAbtest.GetUbsAbtestConfig(ctx, staticField.StrSampleID, strconv.FormatInt(staticField.UserID, 10), "")
	arrSampleId := goArrayColumn(arrAbtestConfigs, "sid")
	if len(arrSampleId) != 0 && php2go.InArray(testDisplayYyLive, arrSampleId) {
		isShowLive = true
	}

	//初始化插楼结构
	floorUserKv := make(map[int32]map[int64]int64)

	//返回值里的 pic_amount 是图片总数
	//pic_list.overall_index是当前图片的索引位置，从1开始的
	//入参的pic_index 是点进来是第几张，从1开始
	//从5开始，隔5插1，＜5楼不插
	jumpNum := 5
	startIndex := int(staticField.ArrPicList[0].GetOverallIndex())
	endIndex := int(staticField.ArrPicList[len(staticField.ArrPicList)-1].GetOverallIndex())

	//屏蔽最后一楼不插
	if len(staticField.ArrPicList) > 0 && int64(endIndex) == int64(staticField.IntNumOfPics) {
		endIndex = endIndex - 1
	}

	startFloor := startIndex
	endFloor := endIndex
	if startIndex >= 5 {
		startFloor = startIndex - (startIndex % jumpNum)
	}
	if startFloor < 5 {
		startFloor = 5
	}
	endFloor = endIndex - (endIndex % jumpNum)

	//这里需要知道端上请求的条数范围，根据插楼规则来计算需要请求多少个直播数据
	needLiveNum := 0

	//防止请求直播间信息超时，主播uid限制最大为30个
	maxAuthorNum := 30

	//计算需要插的楼层
	for i := startFloor; i <= endFloor; i += jumpNum {
		floorUserKv[int32(i)] = make(map[int64]int64)
		floorUserKv[int32(i)][0] = 0
		needLiveNum++
	}

	userPageTitleMap := make(map[int64]string)
	if isShowLive && needLiveNum > 0 {
		//这里取词表配置的干预主播 key主播id，val插入楼层
		recomLiveAuthor := make([]int64, 0)

		wordRes, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_yy_live_config", "picpage_insert_floor_live_author")
		if err != nil {
			tbcontext.WarningF(ctx, "wordserver query fail: %v", err)
		} else {
			liveAuthorInter, err := php.Unserialize([]byte(wordRes))
			if err != nil {
				tbcontext.WarningF(ctx, "php unserialize fail:%v", err)
			}
			liveAuthor := make([]string, 0)
			common.StructAToStructBCtx(ctx, liveAuthorInter, &liveAuthor)
			for _, v := range liveAuthor {
				singleData := strings.Split(v, "#")
				if len(singleData) < 6 {
					continue
				}
				//picpage_insert_floor_live_author
				//8681939#1695278328#1696278328#直播干预标题配置#ad_gray_user#7
				userID := common.Tvttt(singleData[0], common.TTT_STRING).(string)
				//有效期
				startTime := common.Tvttt(singleData[1], common.TTT_INT64).(int64)
				endTime := common.Tvttt(singleData[2], common.TTT_INT64).(int64)
				//页面标题
				title := common.Tvttt(singleData[3], common.TTT_STRING).(string)
				//mis后台上传的人群包的key
				userPackageKey := common.Tvttt(singleData[4], common.TTT_STRING).(string)
				//插入的楼层
				needFloor := common.Tvttt(singleData[5], common.TTT_INT32).(int32)
				timeNow := time.Now().Unix()
				if startTime < timeNow && timeNow < endTime {
					//获取人群包 如果有配置人群包但是不在里面 那就不算干预
					if checkHitUserPackage(ctx, baseData, userPackageKey) {
						cfgUid := strings.Split(userID, ",")
						if needLiveNum+len(recomLiveAuthor) > maxAuthorNum {
							continue
						}
						_, existDefault := floorUserKv[needFloor]
						if !existDefault {
							floorUserKv[needFloor] = make(map[int64]int64)
						}
						for _, vv := range cfgUid {
							tmpUid := common.Tvttt(vv, common.TTT_INT64).(int64)
							recomLiveAuthor = append(recomLiveAuthor, tmpUid)
							userPageTitleMap[tmpUid] = title
							floorUserKv[needFloor][tmpUid] = tmpUid
						}
					}
				}
			}
		}
		//这里取推荐的主播
		input := map[string]interface{}{
			"req": map[string]interface{}{
				"user_id":      staticField.UserID,
				"cuid":         staticField.Cuid,
				"need_content": 1,
				"call_from":    "client_bigpic_insert_floor",
				//"call_from":      "client_bigpic",
				"client_type":    staticField.IntClientType,
				"client_version": staticField.StrClientVersion,
				"net_type":       staticField.NetType,
				"user_ip":        objReq.GetCommonAttr("ip_str", ""),
				"sids":           staticField.StrSampleID,
				"ua_str":         objReq.GetCommonAttr("ua_str", ""),
				"res_num":        needLiveNum,
				"format":         "json",
			},
		}
		recommLiveCommonRes := new(yuelao2.RecomLiveRes)
		err = tbservice.Call(ctx, "yuelaou2", "recommLiveCommon", input, recommLiveCommonRes,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || recommLiveCommonRes.Errno == nil || recommLiveCommonRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call yuelaou2::recommLiveCommon fail, err: %v, input:%v, output:%v", err, input, common.ToString(recommLiveCommonRes))
		} else {
			for _, item := range recommLiveCommonRes.GetData().GetDataDetail().GetThread().GetThreadList() {
				for f, u := range floorUserKv {
					_, existAuthor := u[0]
					if existAuthor {
						floorUserKv[f][item.GetUserId()] = item.GetUserId()
						recomLiveAuthor = append(recomLiveAuthor, item.GetUserId())
						delete(floorUserKv[f], 0)
						break
					}
				}
			}
		}

		//请求用户标签、关注关系、主播直播信息
		if len(recomLiveAuthor) > 0 {

			userLaberKvMap := map[string]string{
				"ds": "打赏过的人",
				"pl": "评论过的人",
				"ck": "常看的人",
				"kg": "看过的人",
				"zf": "最近关注",
				"gz": "关注的人",
			}

			var userLabelMap map[string]string
			userAuthorFollowMap := map[int64]uint32{}

			arrCommonServicesParams := make(map[string]*tbservice.Parameter)
			//获取关注关系
			if clientvers.Compare("12.49.0", staticField.StrClientVersion) >= 0 {
				arrCommonServicesParams["user::getUserFollowInfo"] = &tbservice.Parameter{
					Service: "user",
					Method:  "getUserFollowInfo",
					Input: map[string]interface{}{
						"user_id":     staticField.UserID,
						"req_user_id": recomLiveAuthor,
						"filter_mod":  1, //filter_mod 传1 就是不过滤双无用户
					},
					Output: &user.GetUserFollowInfoRes{},
				}

				if staticField.UserID > 0 {
					redisKey := fmt.Sprintf("%s%d", "user_live_author_label_list_", staticField.UserID)
					rdsRes := resource.RedisAdsense.Get(ctx, redisKey)
					if rdsRes.Err() != nil {
						tbcontext.WarningF(ctx, "call redis adsense key: %s fail. err:%v", redisKey, rdsRes.Err())
					} else {
						userLabelStr := rdsRes.Val()
						err := json.Unmarshal([]byte(userLabelStr), &userLabelMap)
						if err != nil {
							tbcontext.WarningF(ctx, fmt.Sprintf("json.Unmarshal error err:[%s] input:[%#v] output[%#v]\n", err, userLabelStr, userLabelMap))
						}
					}
				}

				multi := tbservice.Multi()
				for k, item := range arrCommonServicesParams {
					if nil == item {
						continue
					}
					multi.Register(ctx, k, item)
				}
				multi.Call(ctx)

				//获取关注关系
				getUserFollowInfoOutput, err := multi.GetResult(ctx, "user::getUserFollowInfo")
				UFollow := getUserFollowInfoOutput.(*user.GetUserFollowInfoRes)
				if err != nil || UFollow.GetErrno() != tiebaerror.ERR_SUCCESS {
					tbcontext.WarningF(ctx, "call user getUserFollowInfo false.output=[%v]", getUserFollowInfoOutput)
				}
				for _, relation := range UFollow.GetResUserInfos() {
					userAuthorFollowMap[relation.GetUserId()] = relation.GetIsFollowed()
				}
			}

			input := map[string]interface{}{
				"cuid":        staticField.Cuid,
				"app_name":    "tieba",
				"client_type": staticField.IntClientType,
				"app_version": staticField.StrClientVersion,
				"sid":         "",
				"user_id":     staticField.UserID,
				"anchor_uids": recomLiveAuthor,
			}
			getTiebaRoomInfoByUserIdsRes := new(ala.GetTiebaRoomInfoByUserIdsRes)
			err = tbservice.Call(ctx, "adsense", "getTiebaRoomInfoByUserIds", input, getTiebaRoomInfoByUserIdsRes, tbservice.WithConverter(tbservice.JSONITER))
			if err != nil || getTiebaRoomInfoByUserIdsRes.Errno == nil || getTiebaRoomInfoByUserIdsRes.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "call adsense::getTiebaRoomInfoByUserIds fail, err: %v, input:%v, output:%v", err, input, common.ToString(getTiebaRoomInfoByUserIdsRes))
			} else {
				data, err := getTiebaRoomInfoByUserIdsRes.GetDataStruct(ctx)
				if err != nil {
					tbcontext.WarningF(ctx, "GetDataStruct fail:%v", err)
				}

				for _, authorUid := range recomLiveAuthor {
					if data == nil || len(data) == 0 || len(data[uint64(authorUid)]) == 0 {
						continue
					}
					liveInfo := data[uint64(authorUid)][0]
					if liveInfo.GetStatus() != 1 {
						continue
					}

					playerSize := &client.WidthHeight{}
					containerSize := &client.WidthHeight{}
					if liveInfo.YyExt.GetIsYyGame() == 1 {
						playerSize.Width = proto.Int32(16)
						playerSize.Height = proto.Int32(9)
						containerSize.Width = proto.Int32(16)
						containerSize.Height = proto.Int32(9)
					} else {
						////screen_direction横屏或者竖屏，0表示竖屏，1表示横屏
						if liveInfo.GetScreen() == "1" {
							playerSize.Width = proto.Int32(11)
							playerSize.Height = proto.Int32(10)
							containerSize.Width = proto.Int32(4)
							containerSize.Height = proto.Int32(3)
						} else if liveInfo.GetScreen() == "0" {
							playerSize.Width = proto.Int32(9)
							playerSize.Height = proto.Int32(16)
							containerSize.Width = proto.Int32(4)
							containerSize.Height = proto.Int32(3)
						}
					}

					var userLabelCurrent string
					if userLabelMap[strconv.FormatInt(int64(liveInfo.GetUid()), 10)] != "" {
						userLabelCurrent = userLabelMap[fmt.Sprintf("%d", int64(liveInfo.GetUid()))]
					}
					//如果redis里没存标签，但是是关注关系
					if userLabelCurrent == "" && userAuthorFollowMap[int64(liveInfo.GetUid())] == 1 {
						userLabelCurrent = "gz"
					}

					userLabel := &client.UniversalLabel{}
					if len(userLabelCurrent) > 0 {
						userLabel.LabelId = proto.String(userLabelCurrent)
						userLabel.LabelText = proto.String(userLaberKvMap[userLabelCurrent])
						userLabel.TextColor = proto.String("#FFFFFFFF")
						userLabel.TextColorBlack = proto.String("#FFFFFFFF")
						userLabel.BackgroundColor = proto.String("#B3FFFFFF")
						userLabel.BackgroundColorBlack = proto.String("#B3FFFFFF ")
						if staticField.IntClientType == 1 {
							userLabel.RoundRadius = proto.Int32(4)
						} else {
							userLabel.RoundRadius = proto.Int32(10)
						}
					}

					pageTitle := "贴吧直播推荐"
					if userPageTitleMap[int64(liveInfo.GetUid())] != "" {
						pageTitle = userPageTitleMap[int64(liveInfo.GetUid())]
					}

					yyExt := new(client.YyExt)
					errAB := common.StructAToStructBCtx(ctx, liveInfo.YyExt, yyExt)
					if errAB != nil {
						tbcontext.WarningF(ctx, "common.StructAToStructBCtx, err:%v", errAB)
					}

					scheme, err := yylive.GetLiveRoomScheme(&yylive.SchemeInput{
						RoomId: int64(liveInfo.GetRoomId()),
						YyExt:  yyExt,
						Source: "pb_datu_eof",
					})
					if err != nil {
						tbcontext.WarningF(ctx, "get_live_room_scheme_error, err:%v", err)
					}

					showCover := common.GetStringPtrDefault(liveInfo.GetCoverVertical(), liveInfo.GetCoverHigh())
					portrait := tbportrait.Encode(int64(liveInfo.GetUid()), "", 0)
					tmp := &clientPicpage.RecomLive{
						Cover:            showCover,
						LiveId:           proto.String(common.Tvttt(liveInfo.GetRoomId(), common.TTT_STRING).(string)),
						AudienceCount:    proto.Uint32(uint32(liveInfo.GetRealOnlineUsers())),
						Title:            proto.String(liveInfo.GetTitle()),
						UserName:         proto.String(liveInfo.GetShowName()),
						Portrait:         proto.String(portrait),
						RoomId:           proto.Uint64(liveInfo.GetRoomId()),
						LabelName:        proto.String(liveInfo.GetLabelName()),
						FeedId:           proto.String(liveInfo.GetFeedId()),
						UserId:           proto.Int64(int64(liveInfo.GetUid())),
						Flv:              proto.String(liveInfo.GetFlv()),
						UserLabel:        userLabel,
						PlayerSize:       playerSize,
						HasConcerned:     proto.Int32(int32(userAuthorFollowMap[int64(liveInfo.GetUid())])),
						PageTitle:        proto.String(pageTitle),
						BackgroundImgUrl: proto.String("https://tieba-ares.cdn.bcebos.com/mis/2023-9/1695292971331/fd1c5cee4d9f.png"),
						Scheme:           proto.String(scheme),
						MoreLiveScheme:   proto.String("com.baidu.tieba://unidispatch/BDPLiveChannel?source=pb_datu_eof"),
					}
					tmp.YyExt = new(client.YyExt)
					common.StructAToStructBCtx(ctx, liveInfo.YyExt, tmp.YyExt)

					//整理数据
					InsertFloorDataItem := new(clientPicpage.InsertFloorDataItem)
					InsertFloorDataItem.LiveData = tmp
					floorNum := int32(0)
					for f, u := range floorUserKv {
						if u[int64(liveInfo.GetUid())] == int64(liveInfo.GetUid()) {
							floorNum = f
							delete(floorUserKv, f)
						}
					}
					if floorNum == 0 {
						continue
					}
					//屏蔽最后一楼不插
					if int64(floorNum) >= int64(staticField.IntNumOfPics) {
						continue
					}
					tmp2 := &clientPicpage.InsertFloorData{
						Type:  proto.String("live"),
						Floor: proto.Int32(floorNum),
						Data:  InsertFloorDataItem,
					}
					staticField.InsertFloorData = append(staticField.InsertFloorData, tmp2)
				}
			}
		}
	}
	sort.Sort(insertFloorSort(staticField.InsertFloorData))
}

type insertFloorSort []*clientPicpage.InsertFloorData

// Len 返回插入排序数组的长度
func (s insertFloorSort) Len() int { return len(s) }

// Swap 函数用于交换两个元素的位置
// 输入参数：
//
//	i: 需要交换位置的元素索引
//	j: 需要交换位置的另一个元素索引
//
// 修改输入数组中两个元素的位置
func (s insertFloorSort) Swap(i, j int) { s[i], s[j] = s[j], s[i] }

// Less 函数用于实现比较接口
func (s insertFloorSort) Less(i, j int) bool { return s[i].GetFloor() < s[j].GetFloor() }

// goArrayColumn 函数用于获取输入的字符串数组中指定键值的字符串集合。
// 参数：
// - input: 包含键值对映射的字符串数组。
// - key: 指定要查询的键值。
// 返回值：
// - 包含指定键值的所有值的字符串数组。
func goArrayColumn(input []map[string]string, key string) []string {
	var res []string
	for _, val := range input {
		if v, ok := val[key]; ok {
			res = append(res, v)
		}
	}
	return res
}

// 判断是否命中广告特殊人群包
func checkHitUserPackage(ctx context.Context, baseData *types.CPicpageBaseData, userPackageKey string) bool {

	if userPackageKey == "all" {
		return true
	}

	staticField := baseData.StaticField
	UserId := staticField.UserID

	// 计算uid hash
	strLineHash, _ := php2go.BaseConvert(php2go.Md5(strconv.FormatInt(UserId, 10))[0:8], 16, 10)
	intLineHash, _ := strconv.Atoi(strLineHash)

	redisKey := userPackageKey + strconv.Itoa(intLineHash%100)
	BoolCmder := resource.RedisAdsense.SIsMember(ctx, redisKey, UserId)
	if BoolCmder.Val() {
		stlog.AddLog(ctx, "is_"+userPackageKey, 1)
		return true
	} else {
		return false
	}
}
