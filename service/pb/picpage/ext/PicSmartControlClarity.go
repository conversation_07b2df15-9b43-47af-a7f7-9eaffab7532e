package ext

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	picSmartClarOP "icode.baidu.com/baidu/tieba-server-go/operator-common/photo/smartControlClarity"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	extPicSmartControlClarity = "picSmartControlClarity"
	PicAuthSampleName         = "pic_auth_exptime_change" // 图片鉴权串过期时间实验key
)

type PicSmartControlClarity struct {
	ctx *engine.Context
}

type PicSmartControlClarOperator struct {
}

func init() {
	err := engine.RegisterOperator(extPicSmartControlClarity, func() engine.Job {
		return &PicSmartControlClarOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPicSmartControlClarity(ctx *engine.Context) *PicSmartControlClarity {
	return &PicSmartControlClarity{
		ctx: ctx,
	}
}

func (op *PicSmartControlClarOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData
	ctx.MutableInstance(&baseData)
	ctx.MutableInstance(&outData)
	webctx := ctx.CallerCtx()
	p := NewPicSmartControlClarity(ctx)
	err := p.Execute(webctx, outData, baseData)
	return err
}

func (p *PicSmartControlClarity) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	// 从threadList获取图片url，该url会做为map的key，所以取字符长度较短的origin_pic字段
	picUrls := make([]string, 0)
	for _, picInfo := range baseData.StaticField.ArrPicList {
		picUrl := picInfo.Img.Original.GetOriginalSrc()
		picUrls = append(picUrls, picUrl)
	}

	// 获取实验配置
	strSampleIDs := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("sample_id", ""), common.TTT_STRING).(string)
	userID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", ""), common.TTT_STRING).(string)
	ubsABTestConfig := UbsAbtest.GetUbsAbtestConfig(ctx, strSampleIDs, userID, "")
	var arrSampleIDs []string
	for _, value := range ubsABTestConfig {
		arrSampleIDs = append(arrSampleIDs, value["sid"])
	}
	if php2go.InArray(PicAuthSampleName, arrSampleIDs) {
		// 命中实验或白名单，需要修改图片鉴权串过期时间
		baseData.StaticField.NeedChangePicExpTime = true
	}

	// 调用公共算子
	op := picSmartClarOP.NewSmartControlClarity(p.ctx, baseData.BaseObj, nil)
	op.SetPicUrls(picUrls)
	err := op.Execute(ctx)
	if err != nil {
		tbcontext.WarningF(ctx, "picSmaartAmplify execute err: %v", err)
		return err
	}
	smartControlClarSW := op.GetSmartControlClarSW()
	picUrlMap := op.GetPicUrlMap()

	if !smartControlClarSW {
		// 如果开关没有打开，不做任何操作
		return nil
	}

	if len(picUrlMap) == 0 {
		// 如果map是空，直接return
		return nil
	}

	// 遍历帖子列表，如果有图片则根据清晰度调控策略修改url
	// 大图页展示，使用的big_cdn_src字段，所以这里只更改big_cdn_src字段
	baseData.DataSec.Mutable(func() {
		for _, picInfo := range baseData.StaticField.ArrPicList {
			picUrl := picInfo.Img.Original.GetOriginalSrc()
			if value, ok := picUrlMap[picUrl]; ok {
				picInfo.Img.Original.BigCdnSrc = proto.String(value)
			}
			picUrls = append(picUrls, picUrl)
		}
	})

	return nil
}
