package ext

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/threadtype"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	WordListGuideCopy           = "tb_wordlist_redis_question"
	QuestionWithTmoneyGuideCopy = "thread_question_placeholder_text"
	QuestionGuideCopy           = "ugc_thread_question_placeholder_text"
)

type GuideCopy struct {
	ctx *engine.Context
}

// init 初始化GuideCopyOperator结构体，并将其注册到engine中作为一个job
func init() {
	err := engine.RegisterOperator("picpage_input_hint", func() engine.Job {
		return &GuideCopyOperator{}
	})
	if err != nil {
		panic(err)
	}
}

// NewGuideCopy 创建一个新的GuideCopy实例，返回指针
func NewGuideCopy(ctx *engine.Context) *GuideCopy {
	return &GuideCopy{
		ctx: ctx,
	}
}

// IsValid 判断指定数据是否有效，不符合条件返回false
func (guide *GuideCopy) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {
	staticField := baseData.StaticField
	if staticField == nil {
		tbcontext.WarningF(ctx, "baseData staticField is NULL")
		return false
	}
	threadInfo := staticField.ArrThreadInfo

	threadTypes := threadtype.GetThreadType(threadInfo.GetThreadTypes())
	// 不是问答帖，返回false
	return threadTypes["is_question"]
}

// Execute 执行操作，根据帖子类型设置引导文案
func (guide *GuideCopy) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField
	if staticField == nil {
		tbcontext.WarningF(ctx, "baseData staticField is NULL")
		return fmt.Errorf("baseData staticField is NULL")
	}
	threadInfo := staticField.ArrThreadInfo

	// 帖子类型
	typeKey := ""
	if threadInfo.GetRobotThreadType() == 42 {
		// 赏金互助贴
		typeKey = QuestionWithTmoneyGuideCopy
	} else {
		// 问答帖
		typeKey = QuestionGuideCopy
	}

	err := setThreadGuideText(ctx, typeKey, outData)
	if err != nil {
		tbcontext.WarningF(ctx, "setThreadGuideText execute fail %v", err)
		return err
	}

	return nil
}

type GuideCopyOperator struct {
}

// DoImpl 实现了engine.Doer接口的方法，用于执行操作
func (opt *GuideCopyOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)

	guideCopy := NewGuideCopy(ctx)
	// 非问答帖直接返回nil
	if !guideCopy.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := guideCopy.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "picpage_guide_copy execute fail: %v", err)
		return err
	}

	return nil
}

// setThreadGuideText 设置互助引导文案
func setThreadGuideText(ctx context.Context, threadTypeKey string, outData *clientPicpage.PicpageRes) error {
	wordRes, err := wordserver.QueryKey(ctx, WordListGuideCopy, threadTypeKey)
	if err != nil {
		tbcontext.WarningF(ctx, "wordserver query fail: %v", err)
		return err
	}
	if wordRes == "" {
		tbcontext.WarningF(ctx, "guide text is empty")
		return fmt.Errorf("guide text is empty")
	}

	outData.InputHint = append(outData.InputHint, &client.InputHintItem{
		Type: common.GetStringPtr("fixed_text"),
		Content: []*client.InputHintContent{
			{
				Type: common.GetStringPtr("text"),
				Text: &wordRes,
			},
		},
	})
	return nil
}
