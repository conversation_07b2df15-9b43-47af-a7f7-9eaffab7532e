package ext

import (
	"context"
	"strings"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/ala"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/yuelao2"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/php"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type RecommLive struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("picpage_recomm_live", func() engine.Job {
		return &RecommLiveOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewRecommLive(ctx *engine.Context) *RecommLive {
	return &RecommLive{
		ctx: ctx,
	}
}
func (a *RecommLive) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {
	// 用户开启了屏蔽直播，不返回相关数据
	if baseData != nil && baseData.StaticField != nil && baseData.StaticField.UserAdSet.GetData().GetNeedCloseLive() == 1 {
		return false
	}
	//鸿蒙暂不支持
	if baseData.StaticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
		return false
	}
	return true
}
func (a *RecommLive) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	if !staticField.NeedRecomLiveList {
		return nil
	}

	tmp := staticField.ArrPicList
	if staticField.NeedAd && staticField.IntNumOfPics > 0 {
		if (len(tmp) == 0 && staticField.StrPicID != "") || (len(tmp) > 0 &&
			tmp[len(tmp)-1].GetOverallIndex() == int64(staticField.IntNumOfPics)) {

			staticField.ShowAdsense = types.ShowAdsense
			recomLiveAuthor := make(map[int64]int64)

			wordRes, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_yy_live_config", "picpage_recom_live_author")
			if err != nil {
				tbcontext.WarningF(ctx, "wordserver query fail: %v", err)
			} else {
				liveAuthorInter, err := php.Unserialize([]byte(wordRes))
				if err != nil {
					tbcontext.WarningF(ctx, "php unserialize fail:%v", err)
				}
				liveAuthor := make([]string, 0)
				common.StructAToStructBCtx(ctx, liveAuthorInter, &liveAuthor)
				for _, v := range liveAuthor {
					singleData := strings.Split(v, "#")
					if len(singleData) < 3 {
						continue
					}
					userID := common.Tvttt(singleData[0], common.TTT_INT64).(int64)
					startTime := common.Tvttt(singleData[1], common.TTT_INT64).(int64)
					endTime := common.Tvttt(singleData[2], common.TTT_INT64).(int64)
					timeNow := time.Now().Unix()
					if startTime < timeNow && timeNow < endTime {
						recomLiveAuthor[userID] = userID
					}
				}
			}

			input := map[string]interface{}{
				"req": map[string]interface{}{
					"user_id":        staticField.UserID,
					"cuid":           staticField.Cuid,
					"need_content":   1,
					"call_from":      "client_bigpic",
					"client_type":    staticField.IntClientType,
					"client_version": staticField.StrClientVersion,
					"net_type":       staticField.NetType,
					"user_ip":        objReq.GetCommonAttr("ip_str", ""),
					"sids":           staticField.StrSampleID,
					"ua_str":         objReq.GetCommonAttr("ua_str", ""),
					"res_num":        8,
					"format":         "json",
				},
			}
			recommLiveCommonRes := new(yuelao2.RecomLiveRes)
			err = tbservice.Call(ctx, "yuelaou2", "recommLiveCommon", input, recommLiveCommonRes,
				tbservice.WithConverter(tbservice.JSONITER))
			if err != nil || recommLiveCommonRes.Errno == nil || recommLiveCommonRes.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "call yuelaou2::recommLiveCommon fail, err: %v, input:%v, output:%v", err, input, common.ToString(recommLiveCommonRes))
			} else {
				for _, item := range recommLiveCommonRes.GetData().GetDataDetail().GetThread().GetThreadList() {
					recomLiveAuthor[item.GetUserId()] = item.GetUserId()
				}
			}

			if len(recomLiveAuthor) > 0 {

				userIDs := make([]int64, 0)
				for userID := range recomLiveAuthor {
					userIDs = append(userIDs, userID)
				}
				// 如果大于8个，取前8个
				if len(userIDs) > 8 {
					userIDs = userIDs[:8]
				}

				input := map[string]interface{}{
					"cuid":        staticField.Cuid,
					"app_name":    "tieba",
					"client_type": staticField.IntClientType,
					"app_version": staticField.StrClientVersion,
					"sid":         "",
					"user_id":     staticField.UserID,
					"anchor_uids": userIDs,
				}
				getTiebaRoomInfoByUserIdsRes := new(ala.GetTiebaRoomInfoByUserIdsRes)
				err = tbservice.Call(ctx, "adsense", "getTiebaRoomInfoByUserIds", input, getTiebaRoomInfoByUserIdsRes, tbservice.WithConverter(tbservice.JSONITER))
				if err != nil || getTiebaRoomInfoByUserIdsRes.Errno == nil || getTiebaRoomInfoByUserIdsRes.GetErrno() != tiebaerror.ERR_SUCCESS {
					tbcontext.WarningF(ctx, "call adsense::getTiebaRoomInfoByUserIds fail, err: %v, input:%v, output:%v", err, input, common.ToString(getTiebaRoomInfoByUserIdsRes))
				} else {
					data, err := getTiebaRoomInfoByUserIdsRes.GetDataStruct(ctx)
					if err != nil {
						tbcontext.WarningF(ctx, "GetDataStruct fail:%v", err)
					}
					for authorUid := range recomLiveAuthor {
						if data == nil || len(data) == 0 || len(data[uint64(authorUid)]) == 0 || len(staticField.RecomLiveList) >= 6 {
							continue
						}
						liveInfo := data[uint64(authorUid)][0]
						if liveInfo.GetStatus() != 1 {
							continue
						}
						showCover := common.GetStringPtrDefault(liveInfo.GetCoverVertical(), liveInfo.GetCoverHigh())
						//tbcontext.WarningF(ctx, "mtx_debug_showCover:%v", *showCover)
						portrait := tbportrait.Encode(int64(liveInfo.GetUid()), "", 0)
						tmp := &clientPicpage.RecomLive{
							Cover:         showCover,
							LiveId:        proto.String(common.Tvttt(liveInfo.GetRoomId(), common.TTT_STRING).(string)),
							AudienceCount: proto.Uint32(uint32(liveInfo.GetRealOnlineUsers())),
							Title:         proto.String(liveInfo.GetTitle()),
							UserName:      proto.String(liveInfo.GetShowName()),
							Portrait:      proto.String(portrait),
							RoomId:        proto.Uint64(liveInfo.GetRoomId()),
							LabelName:     proto.String(liveInfo.GetLabelName()),
							FeedId:        proto.String(liveInfo.GetFeedId()),
							UserId:        proto.Int64(int64(liveInfo.GetUid())),
						}
						tmp.YyExt = new(client.YyExt)
						common.StructAToStructBCtx(ctx, liveInfo.YyExt, tmp.YyExt)
						staticField.RecomLiveList = append(staticField.RecomLiveList, tmp)
					}
				}
			}
		}
	}

	return nil
}

type RecommLiveOperator struct {
}

func (rdop *RecommLiveOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewRecommLive(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "recomm_live execute fail: %v", err)
		return err
	}

	return nil
}
