package ext

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/yylive"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/ala"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/threadtype"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/golib2/trace"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type AddLiveInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("picpage_add_live_info", func() engine.Job {
		return &AddLiveInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewAddLiveInfo(ctx *engine.Context) *AddLiveInfo {
	return &AddLiveInfo{
		ctx: ctx,
	}
}
func (a *AddLiveInfo) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {
	staticField := baseData.StaticField
	if staticField.IsBjh == 1 || staticField.ArrThreadInfo.GetThreadType()>>32 == threadtype.PIC_ALBUM {
		return false
	}

	// 用户开启了屏蔽直播，不返回相关数据
	if staticField.UserAdSet.GetData().GetNeedCloseLive() == 1 {
		return false
	}
	//鸿蒙暂不支持
	if staticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
		return false
	}
	return true
}
func (a *AddLiveInfo) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField
	if clientvers.Compare("12.47.0", staticField.StrClientVersion) >= 0 {
		trace.TimerStart(ctx, "AddLiveInfo")
		addLiveInfo(ctx, staticField)
		trace.TimerEnd(ctx, "AddLiveInfo")
	}

	return nil
}

type AddLiveInfoOperator struct {
}

func (rdop *AddLiveInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewAddLiveInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "post_agree execute fail: %v", err)
		return err
	}

	return nil
}

func addLiveInfo(ctx context.Context, staticField *types.PicpageStaticField) {
	arrAuthorIds := make([]int64, 0)
	for _, picInfo := range staticField.ArrPicList {
		arrAuthorIds = append(arrAuthorIds, picInfo.GetUserId())
	}

	input := map[string]interface{}{
		"cuid":        staticField.Cuid,
		"app_name":    "tieba",
		"client_type": staticField.IntClientType,
		"app_version": staticField.StrClientVersion,
		"sid":         "",
		"user_id":     staticField.UserID,
		"anchor_uids": arrAuthorIds,
	}
	getTiebaRoomInfoByUserIdsRes := new(ala.GetTiebaRoomInfoByUserIdsRes)
	err := tbservice.Call(ctx, "adsense", "getTiebaRoomInfoByUserIds", input, getTiebaRoomInfoByUserIdsRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getTiebaRoomInfoByUserIdsRes.Errno == nil || getTiebaRoomInfoByUserIdsRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call adsense::getTiebaRoomInfoByUserIds fail, err: %v, input:%v, output:%v", err, input, common.ToString(getTiebaRoomInfoByUserIdsRes))
	} else {
		data, err := getTiebaRoomInfoByUserIdsRes.GetDataStruct(ctx)
		if err != nil {
			tbcontext.WarningF(ctx, "GetDataStruct fail:%v", err)
		}
		for _, picInfo := range staticField.ArrPicList {
			if data == nil || len(data) == 0 || len(data[uint64(picInfo.GetUserId())]) == 0 {
				continue
			}
			liveInfo := data[uint64(picInfo.GetUserId())][0]
			if liveInfo.GetStatus() != 1 {
				continue
			}
			showCover := common.GetStringPtrDefault(liveInfo.GetCoverVertical(), liveInfo.GetCoverHigh())
			portrait := tbportrait.Encode(int64(liveInfo.GetUid()), "", 0)
			tmp := &clientPicpage.RecomLive{
				Cover:         showCover,
				LiveId:        proto.String(common.Tvttt(liveInfo.GetRoomId(), common.TTT_STRING).(string)),
				AudienceCount: proto.Uint32(uint32(liveInfo.GetRealOnlineUsers())),
				Title:         proto.String(liveInfo.GetTitle()),
				UserName:      proto.String(liveInfo.GetShowName()),
				Portrait:      proto.String(portrait),
				RoomId:        proto.Uint64(liveInfo.GetRoomId()),
				LabelName:     proto.String(liveInfo.GetLabelName()),
				FeedId:        proto.String(liveInfo.GetFeedId()),
				UserId:        proto.Int64(int64(liveInfo.GetUid())),
			}
			tmp.YyExt = new(client.YyExt)
			errAB := common.StructAToStructBCtx(ctx, liveInfo.YyExt, tmp.YyExt)
			if errAB != nil {
				tbcontext.WarningF(ctx, "common.StructAToStructBCtx, err:%v", errAB)
			}

			scheme, err := yylive.GetLiveRoomScheme(&yylive.SchemeInput{
				RoomId: int64(liveInfo.GetRoomId()),
				YyExt:  tmp.YyExt,
				Source: "pb_live_button",
			})
			if err != nil {
				tbcontext.WarningF(ctx, "get_live_room_scheme_error, err:%v", err)
			}

			GuideInfoTmp := &clientPicpage.GuideInfo{
				Status: proto.Int32(int32(1)),
				Scheme: proto.String(scheme),
				Text:   proto.String("点击进入直播间"),
			}

			picInfo.AlaInfo = tmp
			picInfo.GuideInfo = GuideInfoTmp
		}
	}
}
