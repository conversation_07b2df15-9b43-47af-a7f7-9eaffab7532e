package ext

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/ala"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type RecommAuthor struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("picpage_recomm_author", func() engine.Job {
		return &RecommAuthorOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewRecommAuthor(ctx *engine.Context) *RecommAuthor {
	return &RecommAuthor{
		ctx: ctx,
	}
}
func (a *RecommAuthor) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {
	staticField := baseData.StaticField
	if !staticField.NeedRecLive {
		return false
	}

	// 用户开启了屏蔽直播，不返回相关数据
	if staticField.UserAdSet.GetData().GetNeedCloseLive() == 1 {
		return false
	}
	//鸿蒙暂不支持
	if staticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
		return false
	}
	return true
}
func (a *RecommAuthor) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField
	multi := tbservice.Multi()
	input := map[string]interface{}{
		"level_1_name": staticField.ArrForumInfo.GetFirstClass(),
		"level_2_name": staticField.ArrForumInfo.GetSecondClass(),
		"cuid":         staticField.Cuid,
		"uid":          staticField.UserID,
		"app_version":  staticField.StrClientVersion,
		"network":      staticField.NetType,
		"baiduid":      "",
		"req_num":      10,
		"forum_name":   staticField.StrForumName,
		"is_yy_live":   staticField.IsYYLive,
		"sample_ids":   staticField.StrSampleID,
		"from_page":    "pic_page",
	}
	getPbRecomLiveParam := &tbservice.Parameter{
		Service: "adsense",
		Method:  "getPbRecomLive",
		Input:   input,
		Output:  &ala.GetPbRecomLiveRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getPbRecomLive", getPbRecomLiveParam)

	getIsYyUserParam := &tbservice.Parameter{
		Service: "common",
		Method:  "getIsYyUser",
		Input: map[string]interface{}{
			"cuid":         staticField.Cuid,
			"user_id":      staticField.UserID,
			"sid":          staticField.StrSampleID,
			"_client_type": staticField.IntClientType,
			"version":      staticField.StrClientVersion,
			"subapp_type":  "tieba",
			"ie":           "utf-8",
		},
		Output: &ala.GetPbRecomLiveRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getIsYyUser", getIsYyUserParam)

	multi.Call(ctx)

	getPbRecomLiveInter, err := multi.GetResult(ctx, "getPbRecomLive")
	getPbRecomLiveRes := getPbRecomLiveInter.(*ala.GetPbRecomLiveRes)
	if err != nil || getPbRecomLiveRes.Errno == nil || getPbRecomLiveRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call adsense::getPbRecomLive fail, output: %v, err: %v",
			common.ToString(getPbRecomLiveRes), err)
		return nil
	}

	anchorList, err := getPbRecomLiveRes.GetData().GetAnchorList(ctx)
	if err != nil {
		tbcontext.WarningF(ctx, "GetAnchorList fail: %v, res: %v, input:%v", err, common.ToString(getPbRecomLiveRes), input)
		return nil
	}

	for _, anchor := range anchorList {
		tmp := &clientPicpage.AlaLiveInfo{
			FirstHeadline:  proto.String(anchor.GetFirstHeadline()),
			SecondHeadline: proto.String(anchor.GetSecondHeadline()),
			Cover:          proto.String(anchor.GetCover()),
			LiveId:         proto.Uint64(uint64(anchor.GetLiveId())),
			UserInfo: &client.AlaUserInfo{
				LiveStatus: proto.Uint32(uint32(anchor.GetLiveStatus())),
				UserName:   proto.String(anchor.GetUserName()),
				UserId:     proto.Uint64(uint64(anchor.GetUserId())),
				Portrait:   proto.String(anchor.GetPortrait()),
			},
			AudienceCount: proto.Uint32(uint32(anchor.GetAudienceCount())),
			Description:   proto.String(anchor.GetTitle()),
			LiveStatus:    proto.Int32(int32(anchor.GetLiveStatus())),
			RoomId:        proto.Uint64(uint64(anchor.GetRoomId())),
			LiveType:      proto.Uint32(uint32(anchor.GetLiveType())),
			ThirdRoomId:   proto.String(anchor.GetThirdRoomId()),
			ThirdLiveType: proto.String(anchor.GetThirdLiveType()),
			RouterType:    proto.String(anchor.GetRouterType()),
			LiveFrom:      proto.Int32(int32(anchor.GetLiveFrom())),
			CoverWide:     proto.String(anchor.GetCoverWide()),
			Tag:           proto.String(anchor.GetTag()),
		}
		tmp.YyExt = new(client.YyExt)
		common.StructAToStructBCtx(ctx, anchor.GetYyExt(), tmp.YyExt)
		staticField.ArrRecomLive = append(staticField.ArrRecomLive, tmp)
	}

	return nil
}

type RecommAuthorOperator struct {
}

func (rdop *RecommAuthorOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewRecommAuthor(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "recomm_author execute fail: %v", err)
		return err
	}

	return nil
}
