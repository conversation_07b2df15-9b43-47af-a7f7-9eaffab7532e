package ext

import (
	"context"
	"fmt"
	"google.golang.org/protobuf/proto"
	"html"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/pb"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbrichtext"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/threadtype"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientPicpage "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/picpage"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/common/meta"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/golib2/trace"
	libPb "icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/logic/pb"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"strings"
)

type PostAgree struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("picpage_post_agree", func() engine.Job {
		return &PostAgreeOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPostAgree(ctx *engine.Context) *PostAgree {
	return &PostAgree{
		ctx: ctx,
	}
}
func (a *PostAgree) IsValid(ctx context.Context, baseData *types.CPicpageBaseData) bool {
	staticField := baseData.StaticField
	if staticField.IsBjh == 1 || staticField.ArrThreadInfo.GetThreadType()>>32 == threadtype.PIC_ALBUM {
		return false
	}
	return true
}
func (a *PostAgree) Execute(ctx context.Context, outData *clientPicpage.PicpageRes, baseData *types.CPicpageBaseData) error {
	staticField := baseData.StaticField
	if (staticField.IntClientType == clientvers.CLIENT_TYPE_ANDROID && clientvers.Compare("*******", staticField.StrClientVersion) < 0) ||
		(staticField.IntClientType == clientvers.CLIENT_TYPE_IPHONE && clientvers.Compare("8.8.8", staticField.StrClientVersion) < 0) {

		trace.TimerStart(ctx, "processDesc")
		processDesc(ctx, baseData)
		trace.TimerEnd(ctx, "processDesc")

	} else {

		addImgExtInfo(ctx, staticField)

		processDesc(ctx, baseData)

	}

	return nil
}

type PostAgreeOperator struct {
}

func (rdop *PostAgreeOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientPicpage.PicpageRes
	var baseData *types.CPicpageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewPostAgree(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "post_agree execute fail: %v", err)
		return err
	}

	return nil
}

func processDesc(ctx context.Context, baseData *types.CPicpageBaseData) {
	staticField := baseData.StaticField
	if len(staticField.ArrPicList) == 0 {
		return
	}
	libPb.Process(ctx, &libPb.ProcessData{
		PicList: staticField.ArrPicList,
		StrType: "desc",
	})
}

// V10.0版本添加楼层内容等附加信息
func addImgExtInfo(ctx context.Context, staticField *types.PicpageStaticField) {
	objCondition := tbrichtext.GetDefaultParserCondition()
	objCondition.NewLineCount = 1

	objParserStruct := &tbrichtext.ParserStructured{}
	objParserStruct.SetClientVersion(staticField.StrClientVersion)
	richContent := make(map[uint64][]*meta.PbContent)

	for postId, postInfo := range staticField.FormatPostInfo {
		arrContent, _ := postInfo.GetContentStruct()
		pspInput := &tbrichtext.ParserStructProcessInput{
			PObjCondition:  objCondition,
			BolEmoji:       true,
			ScreenWidth:    0,
			ScreenHeight:   0,
			ArrText:        arrContent,
			BolIsAllOrigin: true,
			BolNeedTopic:   true,
		}
		objResult, err := objParserStruct.Process(ctx, pspInput)
		if err != nil {
			tbcontext.WarningF(ctx, "objParserStruct process fail: %v", err)
		}

		newContent := make([]*meta.PbContent, 0)
		if len(objResult.ArrSlotContent) != 0 {
			// 表情、话题文本不做处理保留，其他的做转换为文本格式
			for _, content := range objResult.ArrSlotContent {
				switch content.GetType() {
				case tbrichtext.SLOT_TYPE_IMG:
					// 默认不存入新的array
				case tbrichtext.SLOT_TYPE_SMILE3:
					newContent = append(newContent, type2text("[自定义表情]"))
				case tbrichtext.SLOT_TYPE_LINK:
					newContent = append(newContent, type2text(content.GetText()))
				case tbrichtext.SLOT_TYPE_BDHD: // 视频
					newContent = append(newContent, type2text("[视频]"))
				case tbrichtext.SLOT_TYPE_EMBED: // flash
					newContent = append(newContent, type2text("[视频]"))
				case tbrichtext.SLOT_TYPE_EMBED_MOVIDEO: // 客户端短视频
					newContent = append(newContent, type2text("[短视频]"))
				case tbrichtext.SLOT_TYPE_AT:
					newContent = append(newContent, type2text(content.GetText()))
				case tbrichtext.SLOT_TYPE_GRAFFITI: // 涂鸦
					newContent = append(newContent, type2text("[涂鸦]"))
				case tbrichtext.SLOT_TYPE_MEME: // 用户自己收藏的表情
					newContent = append(newContent, type2text("[自定义表情]"))
				case tbrichtext.SLOT_TYPE_PHONE: // 电话号
					newContent = append(newContent, type2text(content.GetText()))
				case tbrichtext.SLOT_TYPE_TEXT:
					if strings.TrimSpace(content.GetText()) != "" {
						newContent = append(newContent, content)
					}
				default:
					newContent = append(newContent, content)
				}
			}

			// 判断是不是语音贴，是的话，后边也添加[语言],ptype = 1为语音贴
			if postInfo.GetPtype() == 1 {
				newContent = append(newContent, &meta.PbContent{
					Type: proto.Uint32(tbrichtext.SLOT_TYPE_TEXT),
					Text: proto.String("[语音]"),
				})
			}
		}

		// 如果首楼的内容为空，需要将其内容转化为标题，无标题贴不展示默认文案
		if staticField.IntFirstPostID == postId && postInfo.GetIsNtitle() != 1 && len(newContent) == 0 {
			topicContent := objParserStruct.GetStructFromTopicContent(ctx, postInfo.GetTitle(), 30, 10)
			if len(topicContent) > 0 {
				for _, topicInfo := range topicContent {
					if topicInfo.GetTag() == tbrichtext.CONTENT_ITEM_TAG_TXT {
						newContent = append(newContent, &meta.PbContent{
							Type: proto.Uint32(tbrichtext.SLOT_TYPE_TEXT),
							Text: proto.String(common.Tvttt(topicInfo.GetValue(), common.TTT_STRING).(string)),
						})
					}

					if topicInfo.GetTag() == tbrichtext.CONTENT_ITEM_TAG_A {
						href := topicInfo.GetHref()
						var text string

						if arrValue, ok := topicInfo.GetValue().([]map[string]string); ok && 0 < len(arrValue) {
							valueMap := arrValue[0]
							if textInter, ok := valueMap["value"]; ok {
								text = textInter
								if text == "" {
									continue
								}
							}
						}

						newContent = append(newContent, &meta.PbContent{
							Type: proto.Uint32(tbrichtext.SLOT_TYPE_TOPIC),
							Text: proto.String(text),
							Link: proto.String(href),
						})
					}
				}
			} else {
				newContent = append(newContent, &meta.PbContent{
					Type: proto.Uint32(tbrichtext.SLOT_TYPE_TEXT),
					Text: proto.String(html.UnescapeString(postInfo.GetTitle())),
				})

			}
		}
		richContent[postId] = newContent
	}

	// 获取各楼层回复数
	getCommentNum(ctx, staticField)

	// 添加图片内容
	for _, picInfo := range staticField.ArrPicList {
		// 情感图不添加额外信息
		if picInfo.GetIsBlockedPic() == 1 {
			continue
		}
		postId := picInfo.GetPostId()
		picInfo.PostContent = richContent[uint64(postId)]

		// 添加图片回复的数量, 注意区分是首楼还是回复，添加是否是首楼的标示
		if uint64(postId) == staticField.IntFirstPostID {
			picInfo.CommentNum = proto.Int64(int64(staticField.ArrThreadInfo.GetPostNum()))
			picInfo.Agree = getThreadAgreeInfo(staticField)
			picInfo.IsFirstPost = proto.Int64(1)
		} else {
			picInfo.CommentNum = proto.Int64(int64(staticField.ArrCommentNum[postId]))
			picInfo.Agree = getPostAgreeInfo(staticField, uint64(postId))
			picInfo.IsFirstPost = proto.Int64(0)
		}
	}
}

func type2text(val string) *meta.PbContent {
	return &meta.PbContent{
		Type: proto.Uint32(tbrichtext.SLOT_TYPE_TEXT),
		Text: proto.String(val),
	}
}

func getThreadAgreeInfo(staticField *types.PicpageStaticField) *client.Agree {
	output := &client.Agree{
		AgreeNum:    proto.Int64(staticField.ArrThreadInfo.GetAgreeNum()),
		HasAgree:    proto.Int32(types.NotAgree),
		AgreeType:   proto.Int32(0),
		DisagreeNum: proto.Int64(staticField.ArrThreadInfo.GetDisagreeNum()),
	}
	output.DiffAgreeNum = proto.Int64(output.GetAgreeNum() - output.GetDisagreeNum())

	// 用户为登陆或未返回结果，与PB处理逻辑一致
	if staticField.UserID <= 0 || len(staticField.ArrThreadUserAgree) == 0 {
		return output
	}

	agreeType := staticField.ArrThreadUserAgree[0].GetAgreeType()
	output.HasAgree = proto.Int32(types.HasAgree)
	output.AgreeType = proto.Int32(types.AgreeTypeAgree)
	if agreeType == types.AgreeTypeCai {
		output.AgreeType = proto.Int32(types.AgreeTypeCai)
	}
	return output
}

func getPostAgreeInfo(staticField *types.PicpageStaticField, postID uint64) *client.Agree {
	output := &client.Agree{
		AgreeNum:    proto.Int64(staticField.FormatPostInfo[postID].GetAgreeNum()),
		HasAgree:    proto.Int32(types.NotAgree),
		AgreeType:   proto.Int32(0),
		DisagreeNum: proto.Int64(staticField.FormatPostInfo[postID].GetDisagreeNum()),
	}
	output.DiffAgreeNum = proto.Int64(output.GetAgreeNum() - output.GetDisagreeNum())

	// 用户为登陆或未返回结果，与PB处理逻辑一致
	if staticField.ArrPostUserAgree != nil && staticField.ArrPostUserAgree[int64(postID)] != nil &&
		staticField.ArrPostUserAgree[int64(postID)].GetStatus() == 0 && (output.GetAgreeNum() > 0 || output.GetDisagreeNum() > 0) {
		agreeType := staticField.ArrPostUserAgree[int64(postID)].GetAgreeType()
		output.HasAgree = proto.Int32(types.HasAgree)
		output.AgreeType = proto.Int32(types.AgreeTypeAgree)
		if agreeType == types.AgreeTypeCai {
			output.AgreeType = proto.Int32(types.AgreeTypeCai)
		}
		return output
	}

	return output
}

func getCommentNum(ctx context.Context, staticField *types.PicpageStaticField) {
	postIdMap := make(map[int64]bool)
	for _, postId := range staticField.ArrPostid {
		postIdMap[postId] = true
	}

	multi := tbservice.Multi()

	for postId := range postIdMap {
		getPostInfoParam := &tbservice.Parameter{
			Service: "post",
			Method:  "getCommentList",
			Input: map[string]interface{}{
				"thread_id":  staticField.IntTid,
				"post_id":    postId,
				"comment_id": 0,
				"offset":     0,
				"res_num":    100,
				"status":     2,
			},
			Output: &pb.GetCommentListRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, fmt.Sprintf("getCommentList%d", postId), getPostInfoParam)
	}
	multi.Call(ctx)

	for postId := range postIdMap {
		getCommentListInter, err := multi.GetResult(ctx, fmt.Sprintf("getCommentList%d", postId))
		getCommentListRes := getCommentListInter.(*pb.GetCommentListRes)
		if err != nil || getCommentListRes.Errno == nil || getCommentListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call post::getPostInfo fail, output: %v, err: %v", common.ToString(getCommentListRes), err)
			continue
		}
		staticField.ArrCommentNum[postId] = getCommentListRes.GetOutput().GetCommentNum()
	}
}
