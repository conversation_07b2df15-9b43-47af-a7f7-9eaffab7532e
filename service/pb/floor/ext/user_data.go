package ext

import (
	"context"
	"strconv"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/usercenter"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	callfrom "icode.baidu.com/baidu/tieba-server-user-base/render/const"
	feedhead "icode.baidu.com/baidu/tieba-server-user-base/render/layers/component/feedHead"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type UserData struct {
	ctx *engine.Context
}

const (
	MultiUserNum = 100 // mgetUserForumInfo批量请求个数限制
)

func init() {
	err := engine.RegisterOperator("pbfloor_user_data", func() engine.Job {
		return &UserDataOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewUserData(ctx *engine.Context) *UserData {
	return &UserData{
		ctx: ctx,
	}
}
func (a *UserData) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *UserData) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest
	isLogin := common.Tvttt(objReq.GetCommonAttr("login", false), common.TTT_BOOL).(bool)
	userIcons := make(map[int64][]*client.UserAttrIcon)          // 初始化map
	userShowData := make(map[int64]*client.UserShowInfo)         // 初始化map
	threadAuthorFeedHead := make(map[int64]*client.UserShowInfo) // 初始化map
	userList := make(map[int64]bool, 0)
	if isLogin && staticField.UserId > 0 {
		userList[staticField.UserId] = true
	}

	postUid := staticField.PostInfo.GetFirstPostUserid()
	if postUid > 0 {
		userList[postUid] = true
	}

	for uid := range staticField.PostInfo.GetPostUserMap() {
		if uid > 0 {
			userList[uid] = true
		}
	}

	for uid := range staticField.PostInfo.GetSubPostUserMap() {
		if uid > 0 {
			userList[uid] = true
		}
	}

	for _, sub := range staticField.Comments.GetPostInfos() {
		if sub.GetUserId() > 0 {
			userList[sub.GetUserId()] = true
		}
	}

	tmpUserID := make([]int64, 0)
	for userId := range userList {
		tmpUserID = append(tmpUserID, userId)
	}

	staticField.UserList = tmpUserID

	forumId := int64(staticField.PostInfo.GetForumId())

	mgetUserShowInfoCallFrom := "client_pb_floor"
	if clientvers.IsNoAPP(staticField.StrSubappType) {
		// 如果请求来自端外，调用 mgetUserShowInfo 时 call_from 使用 lite_pb_floor
		mgetUserShowInfoCallFrom = "lite_pb_floor"
	}

	multi := tbservice.Multi()

	for i := 0; len(staticField.UserList) > i*MultiUserNum; i++ {
		var userIds []int64
		if len(staticField.UserList) > (i+1)*MultiUserNum {
			userIds = staticField.UserList[i*MultiUserNum : (i+1)*MultiUserNum]
		} else {
			userIds = staticField.UserList[i*MultiUserNum:]
		}

		getPermParam := &tbservice.Parameter{
			Service: "user",
			Method:  "mgetUserForumInfo",
			Input: map[string]interface{}{
				"user_id":  userIds,
				"forum_id": staticField.PostInfo.GetForumId(),
				"get_icon": staticField.IntPbUserIconSize,
			},
			Output: &user.MgetUserForumInfoRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetUserForumInfo"+strconv.Itoa(i), getPermParam)

		// icons
		var forumIds []int64

		bazhu := baseData.StaticField.Bawulist.GetBawuManagerList(ctx, staticField.IntClientType, staticField.StrClientVersion)
		for _, b := range bazhu {
			userIds = append(userIds, b.GetId())
		}

		for _ = range userIds {
			forumIds = append(forumIds, forumId)
		}
		mgetShowIconListInput := &tbservice.Parameter{
			Service: "user",
			Method:  "mgetShowIconList",
			Input: &usercenter.MgetShowIconListReq{
				UserId:     userIds,
				ForumId:    forumIds,
				CallFrom:   proto.String("client_pb"),
				ClientType: proto.Int32(int32(baseData.StaticField.IntClientType)),
			},
			Output: &usercenter.MgetShowIconListRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
				tbservice.WithServiceName("user/usercenter"),
			},
		}
		multi.Register(ctx, "mgetShowIconList"+strconv.Itoa(i), mgetShowIconListInput)

		// 12.58改为新接口

		if clientvers.Compare("12.58.0", staticField.StrClientVersion) >= 0 {
			var forumIDs []int32
			for _ = range userIds {
				forumIDs = append(forumIDs, int32(forumId))
			}
			mgetUserShowInfoInput := &tbservice.Parameter{
				Service: "user",
				Method:  "mgetUserShowInfo",
				Input: &usercenter.MgetUserShowInfoReq{
					UserId:        userIds,
					ForumId:       forumIDs,
					CallFrom:      proto.String(mgetUserShowInfoCallFrom),
					ClientType:    proto.Int32(int32(baseData.StaticField.IntClientType)),
					ClientVersion: proto.String(staticField.StrClientVersion),
				},
				Output: &usercenter.MgetUserShowInfoRes{},
				Option: []tbservice.Option{
					tbservice.WithConverter(tbservice.JSONITER),
					tbservice.WithServiceName("user/usercenter"),
				},
			}
			multi.Register(ctx, "mgetUserShowInfo"+strconv.Itoa(i), mgetUserShowInfoInput)
		}
	}

	multi.Call(ctx)

	for i := 0; len(staticField.UserList) > i*MultiUserNum; i++ {
		iStr := strconv.Itoa(i)
		// 并行获取热度值信息
		mgetUserForumInfoInter, err := multi.GetResult(ctx, "mgetUserForumInfo"+iStr)
		if err != nil {
			tbcontext.WarningF(ctx, "call user::mgetUserForumInfo fail: %v", err)
		} else {
			res, ok := mgetUserForumInfoInter.(*user.MgetUserForumInfoRes)
			if !ok || res.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "get mgetUserForumInfoRes fail, out:[%v]", common.ToString(res))
			}

			for uid, userInfo := range res.GetUserInfo() {
				if userInfo == nil {
					continue
				}
				staticField.UserInfos[uid] = userInfo
			}
		}

		// 用户icons
		res, err := multi.GetResult(ctx, "mgetShowIconList"+iStr)
		if nil != err {
			tbcontext.WarningF(ctx, "call user mgetShowIconList fail, err:[%v]", err)
		} else {
			out, ok := res.(*usercenter.MgetShowIconListRes)
			if !ok || out.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "get mgetShowIconListRes fail, out:[%v]", common.ToString(res))
			}

			for uid, iconList := range out.GetData() {
				if len(iconList.GetIconList()) == 0 {
					continue
				}

				resIconList := make([]*client.UserAttrIcon, 0)
				err := common.StructAToStructBCtx(ctx, iconList.GetIconList(), &resIconList)
				if err != nil {
					tbcontext.WarningF(ctx, "convert iconList error, uid:[%v] err:[%v], icon:[%s]", uid, err, common.ToString(iconList))
					continue
				}

				userIcons[uid] = resIconList
			}
		}

		// 用户相关展示信息
		if clientvers.Compare("12.58.0", staticField.StrClientVersion) >= 0 {
			userRes, err := multi.GetResult(ctx, "mgetUserShowInfo"+iStr)
			if nil != err {
				tbcontext.WarningF(ctx, "call user mgetUserShowInfo fail, err:[%v]", err)
			} else {
				userOut, ok := userRes.(*usercenter.MgetUserShowInfoRes)
				if !ok || userOut.GetErrno() != tiebaerror.ERR_SUCCESS {
					tbcontext.WarningF(ctx, "get mgetUserShowInfo fail, out:[%v]", common.ToString(res))
				}

				for uid, userShowInfo := range userOut.GetData() {
					if userShowInfo == nil {
						continue
					}
					// userInfo := &client.UsercenterShowInfo{}
					// err := common.StructAToStructBCtx(ctx, userShowInfo, &userInfo)
					// if err != nil {
					// 	tbcontext.WarningF(ctx, "convert userShowInfo error, uid:[%v] err:[%v], icon:[%s]", uid, err, common.ToString(userShowInfo))
					// 	continue
					// }
					iconList := make([]*client.UserAttrIcon, 0)
					err := common.StructAToStructBCtx(ctx, userShowInfo.GetShowIconList(), &iconList)
					if err != nil {
						tbcontext.WarningF(ctx, "convert iconList error, uid:[%v] err:[%v], icon:[%s]", uid, err, common.ToString(userShowInfo.GetShowIconList()))
						continue
					}

					t := &client.ThreadInfo{
						Author: &client.User{
							Id:           proto.Int64(uid),
							ShowIconList: iconList,
						},
					}

					headOp := &feedhead.Op{
						Callfrom: "pb_floor",
						Ctx:      ctx,
						CReq: callfrom.CommonReq{
							ClientType:    staticField.IntClientType,
							ClientVersion: staticField.StrClientVersion,
						},

						Request:       baseData.BaseObj.ObjRequest,
						CardInfo:      t,
						ClientType:    staticField.IntClientType,
						ClientVersion: staticField.StrClientVersion,
					}

					feedHead := headOp.Render()
					userShowData[uid] = &client.UserShowInfo{
						FeedHead: feedHead,
					}

					// 处理楼主icon
					if staticField.ThreadInfo.GetUserId() == uid {

						AuthorIconList := processThreadAuthorIcon(ctx, iconList, staticField.IntClientType, staticField.StrClientVersion)

						headOp := &feedhead.Op{
							Callfrom: "pb_floor",
							Ctx:      ctx,
							CReq: callfrom.CommonReq{
								ClientType:    common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int),
								ClientVersion: common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string),
							},

							Request: baseData.BaseObj.ObjRequest,
							CardInfo: &client.ThreadInfo{
								Author: &client.User{
									Id:           proto.Int64(uid),
									ShowIconList: AuthorIconList,
								},
							},
						}
						threadAuthorFeedHead[uid] = &client.UserShowInfo{
							FeedHead: headOp.Render(),
						}
					}

				}
			}

		}
	}

	staticField.UserShowInfo = userShowData
	staticField.ThreadAuthorShowInfo = threadAuthorFeedHead
	staticField.UserIcons = userIcons

	return nil
}

type UserDataOperator struct {
}

func (rdop *UserDataOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewUserData(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "user_data execute fail: %v", err)
		return err
	}

	return nil
}

// 如果有吧主icon, 直接替换， 否则只能按照目前的展示顺序：昵称-吧主-vip-成长等级-印记， 插入到昵称-vip(如有)后
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/ddpRviE1Ce/woOxg3sOU4/iZLHMk_NTWpSKR
func processThreadAuthorIcon(ctx context.Context, iconList []*client.UserAttrIcon, clientType int, clientVersion string) []*client.UserAttrIcon {
	if len(iconList) == 0 {
		return nil
	}
	icon := "https://tieba-ares.cdn.bcebos.com/mis/2024-3/1710839683475/8740b3ec2aae.png"
	if clientType == stcdefine.CLIENT_TYPE_ANDROID && clientvers.CompareV2(clientVersion, ">=", "12.73") || clientType == stcdefine.CLIENT_TYPE_HARMONY {
		icon = "https://tieba-ares.cdn.bcebos.com/mis/2024-11/1730788971541/d11355588ee2.png"
	}

	resIconList := make([]*client.UserAttrIcon, 0, len(iconList)+1)
	// 楼主icon
	threadAuthorIcon := &client.UserAttrIcon{
		Type:     proto.String("thread_author"),
		SubType:  proto.String("thread_author"),
		Comment:  proto.String("楼主"),
		ImgSrc:   proto.String(icon),
		Height:   proto.Int32(45),
		Width:    proto.Int32(85),
		ShowType: proto.Uint32(2),
	}

	isForumManager := false
	dstIdx := -1 // 目标位置
	for idx, item := range iconList {
		switch item.GetType() {
		case "forum_manager":
			isForumManager = true
			dstIdx = idx
		case "name_show":
			dstIdx = idx
		default:
			// do nothing
		}

		// 有吧主icon
		if isForumManager {
			break
		}
	}

	// 不处理
	if dstIdx == -1 {
		return iconList
	}

	for idx, icon := range iconList {
		if isForumManager && idx == dstIdx {
			resIconList = append(resIconList, threadAuthorIcon)
			continue
		}
		if !isForumManager && idx == dstIdx+1 {
			resIconList = append(resIconList, threadAuthorIcon)
		}
		resIconList = append(resIconList, icon)
	}

	return resIconList
}
