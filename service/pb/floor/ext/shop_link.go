package ext

import (
	"context"
	"html"
	"net/url"
	"regexp"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/threadlist"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ShopLink struct {
	ctx *engine.Context
}

var MatchTIDre = regexp.MustCompile(`/p/(\d+)`)

func init() {
	err := engine.RegisterOperator("pbfloor_shop_link", func() engine.Job {
		return &ShopLinkOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewShopLink(ctx *engine.Context) *ShopLink {
	return &ShopLink{
		ctx: ctx,
	}
}
func (a *ShopLink) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	if baseData == nil || baseData.StaticField == nil {
		return false
	}
	// 鸿蒙暂不支持商店链接
	if baseData.StaticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
		return false
	}
	return true
}
func (a *ShopLink) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField

	// 解析帖子id
	threadIDs := make([]uint64, 0)
	threadIDsExist := make(map[uint64]struct{})
	// 处理主楼
	for _, arrPost := range staticField.PostInfo.GetPostInfos() {
		if pbContentArr, ok := arrPost.GetContent().([]interface{}); ok {
			for _, pbContent := range pbContentArr {
				if pbMap, ok := pbContent.(map[string]interface{}); ok {
					contentData := cast.ToString(pbMap["content-data"])
					tag := cast.ToString(pbMap["tag"])
					href := cast.ToString(pbMap["href"])
					if tag == "a" && html.UnescapeString(contentData) != "" {
						linkData, err := threadlist.GetLinkDataFromContentData(ctx, contentData, href)
						if err != nil || linkData.GetUrlType() != 7 {
							continue
						}
						tid := getThreadID(linkData.GetToUrl())
						if _, ok := threadIDsExist[tid]; !ok && tid > 0 {
							threadIDs = append(threadIDs, tid)
							threadIDsExist[tid] = struct{}{}
						}
					}
				}
			}
		}

		for _, subPost := range arrPost.GetCommentInfo() {
			if pbContentArr, ok := subPost.GetContent().([]interface{}); ok {
				for _, pbContent := range pbContentArr {
					if pbMap, ok := pbContent.(map[string]interface{}); ok {
						contentData := cast.ToString(pbMap["content-data"])
						tag := cast.ToString(pbMap["tag"])
						href := cast.ToString(pbMap["href"])
						if tag == "a" && html.UnescapeString(contentData) != "" {
							linkData, err := threadlist.GetLinkDataFromContentData(ctx, contentData, href)
							if err != nil || linkData.GetUrlType() != 7 {
								continue
							}
							tid := getThreadID(linkData.GetToUrl())
							if _, ok := threadIDsExist[tid]; !ok && tid > 0 {
								threadIDs = append(threadIDs, tid)
								threadIDsExist[tid] = struct{}{}
							}

						}
					}
				}
			}
		}
	}

	baseData.StaticField.ShopGoodsLink = threadIDs

	return nil
}

type ShopLinkOperator struct {
}

func getThreadID(strUrl string) uint64 {
	tid := uint64(0)
	// 编译正则表达式
	// 查找匹配项
	decodedURL, err := url.QueryUnescape(strUrl)
	if err != nil {
		return 0
	}
	match := MatchTIDre.FindStringSubmatch(decodedURL)
	if len(match) > 1 {
		tid = cast.ToUint64(match[1])
	}
	return tid
}

func (rdop *ShopLinkOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewShopLink(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "ShopLink execute fail: %v", err)
		return err
	}

	return nil
}
