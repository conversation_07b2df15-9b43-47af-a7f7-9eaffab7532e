package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/userpost"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Store struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_store", func() engine.Job {
		return &StoreOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewStore(ctx *engine.Context) *Store {
	return &Store{
		ctx: ctx,
	}
}
func (a *Store) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *Store) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest
	isLogin := common.Tvttt(objReq.GetCommonAttr("login", false), common.TTT_BOOL).(bool)

	if !isLogin {
		return nil
	}

	input := map[string]interface{}{
		"uid":       staticField.UserId,
		"thread_id": staticField.PostInfo.GetThreadId(),
	}
	res := new(userpost.QueryThreadStoreTypeRes)
	err := tbservice.Call(ctx, "post", "queryThreadStoreType", input, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service post:queryThreadStoreType, err = %v, input = %v, output = %v", err, input, res)
		return nil
	} else {
		staticField.ThreadStore = res.GetOutput()
	}

	return nil
}

type StoreOperator struct {
}

func (rdop *StoreOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewStore(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "store execute fail: %v", err)
		return err
	}

	return nil
}
