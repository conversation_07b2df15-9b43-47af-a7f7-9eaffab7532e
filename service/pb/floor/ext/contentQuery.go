/**
 * @Author: gongruiyang
 * @Description:
 * @File:  contentQuery
 * @Date: 2024/07/16 下午6:49
 */

package ext

import (
	"context"
	"strings"

	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbrichtext"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	ContentQueryJobName = "pbfloor_content_query"
)

type ContentQuery struct {
	ctx              *engine.Context
	subappType       string
	forumName        string
	queryMatchStatus map[string]bool
}

func init() {
	err := engine.RegisterOperator(ContentQueryJobName, func() engine.Job {
		return &ContentQueryOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewContentQuery(ctx *engine.Context) *ContentQuery {
	return &ContentQuery{
		ctx: ctx,

		queryMatchStatus: make(map[string]bool),
	}
}

type ContentQueryOperator struct{}

func (rdop *ContentQueryOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewContentQuery(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "pbfloor_content_query execute fail: %v", err)
		return err
	}

	return nil
}

func (a *ContentQuery) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {
	if baseData == nil || baseData.StaticField == nil || baseData.StaticField.ForumInfo == nil {
		return false
	}

	level1Name := baseData.StaticField.ForumInfo.GetDir().GetLevel_1Name()
	a.subappType = baseData.StaticField.StrSubappType
	a.forumName = baseData.StaticField.ForumInfo.GetForumName().GetForumName()

	// 仅支持游戏
	if level1Name != "游戏" {
		return false
	}

	// 仅支持端外
	if !clientvers.IsNoAPP(a.subappType) {
		return false
	}

	// 必须有吧名
	if len(a.forumName) == 0 {
		return false
	}

	return true
}

func (a *ContentQuery) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	if baseData == nil || baseData.StaticField == nil || baseData.StaticField.PostList == nil {
		return nil
	}

	baseData.StaticField.PostList.GetCommentInfo()
	var sceneContentBuilder strings.Builder // 使用 strings.Builder 代替字符串能减少拼接字符串时不必要的内存分配和复制
	for _, subPostInfo := range baseData.StaticField.PostList.GetCommentInfo() {
		if subPostInfo == nil {
			continue
		}
		contentStruct, err := subPostInfo.GetContentStruct()
		if err != nil {
			continue
		}
		for _, item := range contentStruct {
			if item.GetTag() == tbrichtext.CONTENT_ITEM_TAG_TXT {
				valueInter := item.GetValue()
				text := common.Tvttt(valueInter, common.TTT_STRING).(string)
				if text == "" || text == "回复 " {
					continue
				}
				sceneContentBuilder.WriteString(text)
				sceneContentBuilder.WriteString(";") // 分隔语句，避免两个单独语句首位相接内容命中 query
			}
		}
	}
	sceneContent := sceneContentBuilder.String()
	if sceneContent == "" {
		return nil
	}

	// 获取楼层文本关键词命中情况
	input := &commonProto.GetGameHitWordsFromTextReq{
		Text:      proto.String(sceneContent),
		ForumName: proto.String(a.forumName),
		IsNew:     proto.Uint32(1), // 标识走新框架逻辑
	}
	output := &commonProto.GetGameHitWordsFromTextRes{}
	err := tbservice.Call(ctx, "common", "getGameHitWordsFromText", input, output, []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithLogField(logit.AutoField("ext_name", ContentQueryJobName)),
		tbservice.WithRalName("common_go"),
	}...)
	if err != nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call common:getGameHitWordsFromText fail, input:[%s], err:[%v]", common.ToString(input), err)
		return err
	}

	// 组装命中词和映射搜索文本
	baseData.StaticField.HitWordMap = make(map[string]string)
	baseData.StaticField.QueryMatchStatus = make(map[string]bool)
	for _, item := range output.GetData() {
		word := item.GetWord()
		gameName := item.GetGameName()
		if word == "" || gameName == "" {
			continue
		}
		baseData.StaticField.HitWordMap[word] = gameName
	}

	return nil
}
