package ext

import (
	"context"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/voice"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/common"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Voice struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_voice", func() engine.Job {
		return &VoiceOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewVoice(ctx *engine.Context) *Voice {
	return &Voice{
		ctx: ctx,
	}
}
func (a *Voice) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *Voice) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField
	if len(staticField.PostInfo.GetPostInfos()) == 0 {
		return nil
	}

	arrPost := staticField.PostInfo.GetPostInfos()[0]
	staticField.ArrUid[arrPost.GetUserId()] = arrPost.GetUserId()
	threadId := staticField.PostInfo.GetThreadId()
	arrVoiceList := make([]int64, 0)
	if arrPost.GetPtype() == 1 {
		arrVoiceList = append(arrVoiceList, int64(arrPost.GetPostId()))
	}

	subPostList := staticField.Comments.GetPostInfos()
	for _, subPost := range subPostList {
		staticField.ArrUid[subPost.GetUserId()] = subPost.GetUserId()
		if subPost.GetPtype() == 1 {
			arrVoiceList = append(arrVoiceList, int64(subPost.GetPostId()))
		}
	}

	for _, pid := range arrVoiceList {
		staticField.Voice = append(staticField.Voice, map[string]interface{}{
			"post_id":   pid,
			"thread_id": threadId,
		})
	}

	if len(staticField.Voice) == 0 {
		return nil
	}

	input := map[string]interface{}{
		"pids": staticField.Voice,
	}
	res := new(voice.GetThreadVoiceInfosByPidsRes)
	err := tbservice.Call(ctx, "voice", "getThreadVoiceInfosByPids", input, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service voice:getThreadVoiceInfosByPids, err = %v, input = %v, output = %v", err, input, res)
		return nil
	} else {
		for _, item := range res.GetRet().GetPostVoiceList() {
			staticField.VoiceList[int64(item.GetPostId())] = &common.Voice{
				Type:       proto.Int32(10),
				DuringTime: proto.Int32(item.GetDuringTime() * 1000),
				VoiceMd5:   proto.String(item.GetVoiceMd5()),
			}
		}
	}

	return nil
}

type VoiceOperator struct {
}

func (rdop *VoiceOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewVoice(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "voice execute fail: %v", err)
		return err
	}

	return nil
}
