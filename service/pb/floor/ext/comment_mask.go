package ext

import (
	"context"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/pb"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type CommentMask struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_comment_mask", func() engine.Job {
		return &CommentMaskOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewCommentMask(ctx *engine.Context) *CommentMask {
	return &CommentMask{
		ctx: ctx,
	}
}
func (a *CommentMask) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *CommentMask) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField

	//12.3主客态：楼中楼，注册getMaskInfo获取楼中楼主客态信息
	if len(staticField.Comments.GetPostInfos()) == 0 {
		return nil
	}

	commentInput := make([]map[string]interface{}, 0)
	for _, postInfo := range staticField.Comments.GetPostInfos() {
		tid := postInfo.GetThreadId()
		pid := postInfo.GetQuote().GetPostId()
		cid := postInfo.GetPostId()

		if tid == 0 || pid == 0 || cid == 0 {
			continue
		}

		commentInput = append(commentInput, map[string]interface{}{
			"thread_id":  tid,
			"post_id":    pid,
			"comment_id": cid,
		})
	}

	if len(commentInput) == 0 {
		return nil
	}

	input := map[string]interface{}{
		"input": map[string]interface{}{
			"comment_infos": commentInput,
		},
	}
	res := new(pb.GetMaskInfoRes)
	err := tbservice.Call(ctx, "post", "getMaskInfo", input, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service post:getMaskInfo, err = %v, input = %v, output = %v", err, input, res)
		return nil
	}

	for _, postInfo := range staticField.Comments.GetPostInfos() {
		cid := postInfo.GetPostId()
		if res.GetOutput().GetSubpostMaskStatus() != nil && res.GetOutput().GetSubpostMaskStatus()[cid] != nil {
			postInfo.IsAuthorView = proto.Int32(int32(res.GetOutput().GetSubpostMaskStatus()[cid].GetIsKeyVisible()))
		}
	}

	return nil
}

type CommentMaskOperator struct {
}

func (rdop *CommentMaskOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewCommentMask(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "comment_mask execute fail: %v", err)
		return err
	}

	return nil
}
