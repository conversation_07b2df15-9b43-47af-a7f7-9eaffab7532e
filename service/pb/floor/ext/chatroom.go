package ext

import (
	"context"
	"html"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/threadlist"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"net/url"
	"strconv"
)

type Chatroom struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_chatroom", func() engine.Job {
		return &ChatroomOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewChatroom(ctx *engine.Context) *Chatroom {
	return &Chatroom{
		ctx: ctx,
	}
}
func (a *Chatroom) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *Chatroom) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField

	arrChatroomId := make([]int64, 0)

	for _, arrPost := range staticField.PostInfo.GetPostInfos() {
		if pbContentArr, ok := arrPost.GetContent().([]interface{}); ok {
			for _, pbContent := range pbContentArr {
				if pbMap, ok := pbContent.(map[string]interface{}); ok {
					contentData := common.Tvttt(pbMap["content-data"], common.TTT_STRING).(string)
					tag := common.Tvttt(pbMap["tag"], common.TTT_STRING).(string)
					href := common.Tvttt(pbMap["href"], common.TTT_STRING).(string)
					if tag == "a" && html.UnescapeString(contentData) != "" {
						linkData, err := threadlist.GetLinkDataFromContentData(ctx, contentData, href)
						if err != nil || linkData.GetUrlType() != 5 {
							continue
						}

						urlObj, err := url.Parse(linkData.GetToUrl())
						if err != nil {
							continue
						}
						chatroomId := common.Tvttt(urlObj.Query().Get("chatroom_id"), common.TTT_INT64).(int64)
						if chatroomId != 0 {
							arrChatroomId = append(arrChatroomId, chatroomId)
							staticField.HasChatroomId = true
						}
					}
				}
			}
		}

		for _, subPost := range arrPost.GetCommentInfo() {
			if pbContentArr, ok := subPost.GetContent().([]interface{}); ok {
				for _, pbContent := range pbContentArr {
					if pbMap, ok := pbContent.(map[string]interface{}); ok {
						contentData := common.Tvttt(pbMap["content-data"], common.TTT_STRING).(string)
						tag := common.Tvttt(pbMap["tag"], common.TTT_STRING).(string)
						href := common.Tvttt(pbMap["href"], common.TTT_STRING).(string)
						if tag == "a" && html.UnescapeString(contentData) != "" {
							linkData, err := threadlist.GetLinkDataFromContentData(ctx, contentData, href)
							if err != nil || linkData.GetUrlType() != 5 {
								continue
							}

							urlObj, err := url.Parse(linkData.GetToUrl())
							if err != nil {
								continue
							}
							chatroomId := common.Tvttt(urlObj.Query().Get("chatroom_id"), common.TTT_INT64).(int64)
							if chatroomId != 0 {
								arrChatroomId = append(arrChatroomId, chatroomId)
								staticField.HasChatroomId = true
							}
						}
					}
				}
			}
		}
	}

	multi := tbservice.Multi()
	for i := 0; len(arrChatroomId) > i*30; i++ {
		var chatroomIds []int64
		if len(arrChatroomId) > (i+1)*30 {
			chatroomIds = arrChatroomId[i*30 : (i+1)*30]
		} else {
			chatroomIds = arrChatroomId[i*30:]
		}

		mgetRoomsDetailParam := &tbservice.Parameter{
			Service: "chat",
			Method:  "mgetRoomsDetail",
			Input: map[string]interface{}{
				"chatroom_id":   chatroomIds,
				"info_level":    1,
				"is_need_forum": 1,
			},
			Output: &chat.GetRoomsDetailRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetRoomsDetail"+strconv.Itoa(i), mgetRoomsDetailParam)
		staticField.ChatroomMutilNum++
	}

	multi.Call(ctx)
	for i := 0; len(arrChatroomId) > i*30; i++ {
		iStr := strconv.Itoa(i)
		//并行获取热度值信息
		mgetRoomsDetailInter, err := multi.GetResult(ctx, "mgetRoomsDetail"+iStr)
		if err != nil {
			tbcontext.WarningF(ctx, "call chat::mgetRoomsDetail fail: %v", err)
		} else {
			mgetRoomsDetail := mgetRoomsDetailInter.(*chat.GetRoomsDetailRes)
			for chatroomId, chatroomInfo := range mgetRoomsDetail.GetData() {
				staticField.ChatroomInfo[chatroomId] = chatroomInfo
			}
		}
	}

	return nil
}

type ChatroomOperator struct {
}

func (rdop *ChatroomOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewChatroom(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "chatroom execute fail: %v", err)
		return err
	}

	return nil
}
