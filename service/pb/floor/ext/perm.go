package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/ip"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Perm struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_perm", func() engine.Job {
		return &PermOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPerm(ctx *engine.Context) *Perm {
	return &Perm{
		ctx: ctx,
	}
}
func (a *Perm) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *Perm) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	if staticField.PostInfo.GetForumId() == 0 {
		return nil
	}

	multi := tbservice.Multi()

	getBawuListParam := &tbservice.Parameter{
		Service: "perm",
		Method:  "getBawuList",
		Input: map[string]interface{}{
			"forum_id": staticField.PostInfo.GetForumId(),
		},
		Output: &perm.GetBawuListRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getBawuList", getBawuListParam)

	if staticField.UserId != 0 {
		getPermInput := map[string]interface{}{
			"user_id":  staticField.UserId,
			"forum_id": staticField.PostInfo.GetForumId(),
		}

		ipInt := common.Tvttt(objReq.GetCommonAttr("ip_int", 0), common.TTT_UINT32).(uint32)
		ipVersion := ip.GetIPVersion(ip.Long2IP(ipInt))
		if ipVersion == ip.IPV6 {
			getPermInput["user_ip6"] = ipInt
		} else {
			getPermInput["user_ip"] = ipInt
		}

		getPermParam := &tbservice.Parameter{
			Service: "perm",
			Method:  "getPerm",
			Input:   getPermInput,
			Output:  &perm.GetPermRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getPerm", getPermParam)
	}


	multi.Call(ctx)

	getBawuListResInter, err := multi.GetResult(ctx, "getBawuList")
	getBawuListRes := getBawuListResInter.(*perm.GetBawuListRes)
	if err != nil || getBawuListRes.Errno == nil || getBawuListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call perm::getBawuList fail, err: %v, output: %v", err, common.ToString(getBawuListRes))
	} else {
		staticField.Bawulist = getBawuListRes.GetOutput()
	}

	if staticField.UserId != 0 {
		getPermResInter, err := multi.GetResult(ctx, "getPerm")
		getPermRes := getPermResInter.(*perm.GetPermRes)
		if err != nil || getPermRes.Errno == nil || getPermRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call perm::getPermRes fail, err: %v, output: %v", err, common.ToString(getPermRes))
		} else {
			staticField.Perm = getPermRes.GetOutput()
		}
	}

	return nil
}

type PermOperator struct {
}

func (rdop *PermOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewPerm(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "perm execute fail: %v", err)
		return err
	}

	return nil
}
