package ext

import (
	"context"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/livegroup"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type TwZhibo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_tw_zhibo", func() engine.Job {
		return &TwZhiboOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewTwZhibo(ctx *engine.Context) *TwZhibo {
	return &TwZhibo{
		ctx: ctx,
	}
}
func (a *TwZhibo) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	if baseData == nil || baseData.StaticField == nil {
		return false
	}
	// 鸿蒙暂不支持图文直播
	if baseData.StaticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
		return false
	}
	return true
}
func (a *TwZhibo) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField

	if staticField.PostInfo.GetForumId() == 0 || staticField.PostInfo.GetThreadId() == 0 {
		return nil
	}

	input := map[string]interface{}{
		"forumId":    staticField.PostInfo.GetForumId(),
		"threadIds":  []uint64{staticField.PostInfo.GetThreadId()},
		"labelCount": 3,
	}
	res := new(livegroup.GetTWInfosByTidsRes)
	err := tbservice.Call(ctx, "livegroup", "getTWInfosByTids", input, res,
		tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service livegroup:getTWInfosByTids, err = %v, input = %v, output = %v", err, input, res)
		return nil
	}

	for _, value := range res.GetTwzhiboInfo() {
		if value.GetMedia().GetType() == "pic" {
			waterPic, _ := image.GenAuthUrl(value.GetMedia().GetWaterPic())
			smallPic, _ := image.GenAuthUrl(value.GetMedia().GetSmallPic())
			bigPic, _ := image.GenAuthUrl(value.GetMedia().GetBigPic())
			if waterPic != "" {
				value.GetMedia().WaterPic = proto.String(waterPic)
			}
			if smallPic != "" {
				value.GetMedia().SmallPic = proto.String(smallPic)
			}
			if bigPic != "" {
				value.GetMedia().BigPic = proto.String(bigPic)
			}
		}
	}

	staticField.TWZhiInfo = make(map[int64]*client.ZhiBoInfoTW)
	common.StructAToStructBCtx(ctx, res.GetTwzhiboInfo(), &staticField.TWZhiInfo)
	return nil
}

type TwZhiboOperator struct {
}

func (rdop *TwZhiboOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewTwZhibo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "tw_zhibo execute fail: %v", err)
		return err
	}

	return nil
}
