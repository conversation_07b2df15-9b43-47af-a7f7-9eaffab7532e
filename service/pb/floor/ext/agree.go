package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	molibUser "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/agree"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Agree struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_agree", func() engine.Job {
		return &AgreeOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewAgree(ctx *engine.Context) *Agree {
	return &Agree{
		ctx: ctx,
	}
}
func (a *Agree) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *Agree) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField

	if staticField.UserId == 0 {
		return nil
	}

	postIds := make([]uint64, 0)
	for _, item := range staticField.Comments.GetPostInfos() {
		postIds = append(postIds, item.GetPostId())
	}

	multi := tbservice.Multi()

	if staticField.UserId != 0 && staticField.PostInfo.GetThreadId() != 0 && len(postIds) > 0 {
		getAgreeByUserIdAndPostIdsParam := &tbservice.Parameter{
			Service: "agree",
			Method:  "getAgreeByUserIdAndPostIds",
			Input: map[string]interface{}{
				"user_id":   staticField.UserId,
				"thread_id": staticField.PostInfo.GetThreadId(),
				"post_ids":  postIds,
			},
			Output: &agree.GetAgreeByUserIdAndPostIdsRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getAgreeByUserIdAndPostIds", getAgreeByUserIdAndPostIdsParam)
	}

	if staticField.UserId != 0 && staticField.PostInfo.GetThreadId() != 0 {
		if len(staticField.PostInfo.GetPostInfos()) > 0 {
			getAgreeByUserIdAndPostIdParam := &tbservice.Parameter{
				Service: "agree",
				Method:  "getAgreeByUserIdAndPostId",
				Input: map[string]interface{}{
					"user_id":   staticField.UserId,
					"thread_id": staticField.PostInfo.GetThreadId(),
					"post_id":   staticField.PostInfo.GetPostInfos()[0].GetPostId(),
				},
				Output: &agree.GetAgreeByUserIdAndPostIdRes{},
				Option: []tbservice.Option{
					tbservice.WithConverter(tbservice.JSONITER),
				},
			}
			multi.Register(ctx, "getAgreeByUserIdAndPostId", getAgreeByUserIdAndPostIdParam)
		}
	}

	multi.Call(ctx)

	staticField.IsBlackWhite = molibUser.IsCreatorWhiteUid(ctx, staticField.UserId)

	if staticField.UserId != 0 && staticField.PostInfo.GetThreadId() != 0 && len(postIds) > 0 {
		getAgreeByUserIdAndPostIdsInter, err := multi.GetResult(ctx, "getAgreeByUserIdAndPostIds")
		getAgreeByUserIdAndPostIdsRes := getAgreeByUserIdAndPostIdsInter.(*agree.GetAgreeByUserIdAndPostIdsRes)
		if err != nil || getAgreeByUserIdAndPostIdsRes.Errno == nil || getAgreeByUserIdAndPostIdsRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call agree::getAgreeByUserIdAndPostIds fail, err: %v, output: %v", err, common.ToString(getAgreeByUserIdAndPostIdsRes))
		} else {
			staticField.SubPostAgreeData = getAgreeByUserIdAndPostIdsRes.GetData().GetMap()
		}
	}

	if staticField.UserId != 0 && staticField.PostInfo.GetThreadId() != 0 {
		getAgreeByUserIdAndPostIdInter, err := multi.GetResult(ctx, "getAgreeByUserIdAndPostId")
		getAgreeByUserIdAndPostIdRes := getAgreeByUserIdAndPostIdInter.(*agree.GetAgreeByUserIdAndPostIdRes)
		if err != nil || getAgreeByUserIdAndPostIdRes.Errno == nil || getAgreeByUserIdAndPostIdRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call agree::getAgreeByUserIdAndPostId fail, err: %v, output: %v", err, common.ToString(getAgreeByUserIdAndPostIdRes))
		} else {
			staticField.PostAgreeData = getAgreeByUserIdAndPostIdRes.GetData()
		}

	}
	return nil
}

type AgreeOperator struct {
}

func (rdop *AgreeOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewAgree(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "agree execute fail: %v", err)
		return err
	}

	return nil
}
