package ext

import (
	"context"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type HandlePost struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_handle_post", func() engine.Job {
		return &HandlePostOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewHandlePost(ctx *engine.Context) *HandlePost {
	return &HandlePost{
		ctx: ctx,
	}
}
func (a *HandlePost) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *HandlePost) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	// 框架改版，支持多吧贴问题
	if clientvers.ReversionCompare(clientvers.REVERSION_CLIENT_TYPE_IPHONE_ONLINE, staticField.StrClientVersion, staticField.IntClientType) >= 0 {
		currentForumId := common.Tvttt(objReq.GetPrivateAttr("forum_id", 0), common.TTT_UINT32).(uint32) // 当前传入吧id
		forumIdSharedList := make([]uint32, 0)                                                           // 转贴吧列表
		vForumId := uint32(0)                                                                            // 当前回贴的来源吧id

		// 只有转贴需要处理来源吧
		if staticField.ThreadInfo != nil {
			// 获取到贴子类型
			threadTypes := staticField.ThreadInfo.GetThreadTypes()
			staticField.IsRepostThread = (threadTypes & 0x800) == 0x800 // 是否为转贴

			// 当前回贴的转发吧id
			if len(staticField.PostInfo.GetPostInfos()) > 0 {
				vForumId = uint32(staticField.PostInfo.GetPostInfos()[0].GetVForumId())
			}

			// 判断来源吧id是否在吧列表内，过滤非法数据
			forumIdSharedList = staticField.ThreadInfo.GetForumIdShared()
		}

		// 转贴情况下
		if staticField.IsRepostThread {
			// 客态情况下，按当前客态吧获取吧相关数据
			if currentForumId > 0 && php2go.InArray(currentForumId, forumIdSharedList) {
				staticField.PostInfo.ForumId = proto.Uint32(currentForumId)
				staticField.IntRepostThreadStatus = types.RepostBrowserStatusSingleForum
			} else if vForumId > 0 { // 主态情况下，如果有来源吧，按来源吧获取吧相关数据
				staticField.PostInfo.ForumId = proto.Uint32(vForumId)
				staticField.IntRepostThreadStatus = types.RepostBrowserStatusMultiForum
			} else { // 主态情况下，如果没有来源吧，只标识主态
				staticField.IntRepostThreadStatus = types.RepostBrowserStatusMultiForum
			}
		}
	}

	return nil
}

type HandlePostOperator struct {
}

func (rdop *HandlePostOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewHandlePost(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "handle_post execute fail: %v", err)
		return err
	}

	return nil
}
