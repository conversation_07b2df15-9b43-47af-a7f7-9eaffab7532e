package ext

import (
	"context"
	"fmt"
	"html"
	"strconv"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/bigsmile"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/imgcdn"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/processpost"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/usertool"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/adsense"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	utilCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbrichtext"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/common/meta"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ProcessPost struct {
	ctx *engine.Context
}

const (
	VOICE_SHOW_BUTTON = 1
	VOICE_SHOW_TEXT   = 2
	VOICE_SHOW_LINK   = 3

	// 大图flag
	FLAG_BIG_IMG_URL = 2
	// 跳转链接flag
	FLAG_JUMP_URL = 1
	// 官方网站type
	TYPE_OFFICIAL_WEBSET = 1
	// 电话type
	TYPE_OFFICIAL_PHONE = 2
	// 有图片type
	TYPE_HAS_IMG = 1
	// 无图片type
	TYPE_NO_IMG = 2
	// 视频type
	TYPE_HAS_VIEDO = 3

	BTNLINK_TEXT = "---查看详情---"

	TYPE_TEMPLATE_NO_IMG = 0
	// 有图片无描述区
	TYPE_TEMPLATE_HAS_IMG_NO_DES = 1
	// 有图片有描述区
	TYPE_TEMPLATE_HAS_IMG_HAS_DES = 2
	// 有视频
	TYPE_TEMPLATE_HAS_VIDEO = 6
	TYPE_TEMPLATE_SKIN      = 7 // 触点皮肤
	// 有图片有描述区有电话
	TYPE_TEMPLATE_HAS_IMG_HAS_DES_HAS_TEL = 4
)

func init() {
	err := engine.RegisterOperator("pbfloor_process_post", func() engine.Job {
		return &ProcessPostOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewProcessPost(ctx *engine.Context) *ProcessPost {
	return &ProcessPost{
		ctx: ctx,
	}
}
func (a *ProcessPost) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *ProcessPost) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField

	processOnePost(ctx, baseData, staticField.PostList, 0)
	if clientvers.Compare("9.8.0", staticField.StrClientVersion) >= 0 {
		processLongPic(staticField.PostList.GetContent().([]*meta.PbContent), baseData)
	}

	// 触点pbfloor展现图片
	tPoint, _ := staticField.PostList.GetPbTpointStruct()
	if tPoint != nil {
		if tPoint.TpType != nil {
			fetchTPointContent(baseData, tPoint)
		} else {
			versionConf := map[int64]clientvers.VersionSwitch{
				stcdefine.CLIENT_TYPE_IPHONE: {
					Switch:     1,
					MinVersion: "7.1.0",
				},
				stcdefine.CLIENT_TYPE_ANDROID: {
					Switch:     1,
					MinVersion: "7.1.0",
				},
				stcdefine.CLIENT_TYPE_HARMONY: {
					Switch:     1,
					MinVersion: "7.1.0",
				},
			}

			isNewVersion := clientvers.Check(versionConf, int64(staticField.IntClientType), staticField.StrClientVersion)
			if isNewVersion {
				process7_1TPoint(baseData, tPoint)
			} else {
				fetchTPointContent(baseData, tPoint)
			}

		}
	}

	tmpContent, ss := tbrichtext.ProcessContentOrder(staticField.PostList.GetContent().([]*meta.PbContent), "post", 2, staticField.ShowSquared)
	staticField.PostList.Content = utilCommon.GetInterfacePtr(tmpContent)
	// 使用开关控制一下，觉得这个九宫格不靠谱，有可能会下掉
	if ss > 0 {
		staticField.ShowSquared = true
	} else {
		staticField.ShowSquared = false
	}

	// 对楼中楼贴子进行富文本处理
	for _, subPost := range staticField.Comments.GetPostInfos() {
		processOnePost(ctx, baseData, subPost, 1)
		tmpContent, _ := tbrichtext.ProcessContentOrder(subPost.GetContent().([]*meta.PbContent), "floor", 2, staticField.ShowSquared)
		subPost.Content = utilCommon.GetInterfacePtr(tmpContent)
	}

	getAtUser(ctx, baseData)

	getTibaPlusPlugInfo(ctx, baseData)

	return nil
}

type ProcessPostOperator struct {
}

func (rdop *ProcessPostOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewProcessPost(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "process_post execute fail: %v", err)
		return err
	}

	return nil
}

func processOnePost(ctx context.Context, baseData *types.CPbFloorBaseData, arrPost *post.Post, isFloor int) {
	staticField := baseData.StaticField

	objCondition := tbrichtext.GetDefaultParserCondition()
	objCondition.NewLineCount = 1
	if staticField.IsParseBdhd {
		objCondition.BolParseBdhd = true
	}
	objCondition.BolCheckSpamUrl = true

	title := arrPost.GetTitle()
	title = html.UnescapeString(title)
	content, _ := arrPost.GetContentStruct()
	canShowNewSmile := bigsmile.CheckNewSmileClientVersion(staticField.StrClientVersion, staticField.IntClientType)

	// 9.8 版本楼中楼 手机号要调起 需解析手机号
	boolIsAllOrigin := false
	if clientvers.Compare("9.8.0", staticField.StrClientVersion) >= 0 {
		boolIsAllOrigin = true
		if staticField.IntPbParsePhone > 0 {
			objCondition.BolParsePhone = true
		}
	}

	if clientvers.CompareV2(staticField.StrClientVersion, ">=", "12.76") {
		wordSeqPost, _ := arrPost.GetWordseqPostStruct()
		if wordSeqPost != nil && clientvers.CompareV2(staticField.StrClientVersion, ">=", "12.76.0") {
			strSampleIDs := utilCommon.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("sample_id", ""), utilCommon.TTT_STRING).(string)
			userID := utilCommon.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", ""), utilCommon.TTT_STRING).(string)
			arrSampleIds := UbsAbtest.GetUbsAbtestConfig(ctx, strSampleIDs, userID, "tb_wordlist_redis_ubs_abtest_config")
			arrSampleId := make([]string, 0, len(arrSampleIds))
			for _, v := range arrSampleIds {
				arrSampleId = append(arrSampleId, v["sid"])
			}
			if php2go.InArray("show_pb_search_a", arrSampleId) {
				content = processpost.BuildWordSeqFromPostInfo(content, wordSeqPost)
			}
		}
	}

	// 富文本处理
	// pb结构化后，解析content方式
	objParserStruct := &tbrichtext.ParserStructured{}
	objParserStruct.SetClientType(staticField.IntClientType)
	objParserStruct.SetClientVersion(staticField.StrClientVersion)

	// 贴+token
	output := processpost.BuildTPCFromThreadPost(content)
	staticField.TiebaPlusPlugFloorTokens = append(staticField.TiebaPlusPlugFloorTokens, output.Token...)

	pspInput := &tbrichtext.ParserStructProcessInput{
		PObjCondition:  objCondition,
		BolEmoji:       false,
		ScreenWidth:    0,
		ScreenHeight:   0,
		ArrText:        output.Post,
		BolIsAllOrigin: boolIsAllOrigin,
		BolNeedTopic:   true,
	}
	objResult, err := objParserStruct.Process(ctx, pspInput)
	if err != nil {
		tbcontext.WarningF(ctx, "obj parser fail: %v", err)
	}

	if (staticField.IntVoiceShow == VOICE_SHOW_TEXT || staticField.IntVoiceShow == VOICE_SHOW_LINK) && arrPost.GetPtype() == 1 {
		pid := arrPost.GetPostId()
		if staticField.VoiceList[int64(pid)] != nil {
			arrVoice := staticField.VoiceList[int64(pid)]
			tmpSlot := new(meta.PbContent)
			tmpSlot.Type = proto.Uint32(uint32(arrVoice.GetType()))
			tmpSlot.DuringTime = proto.Uint32(uint32(arrVoice.GetDuringTime()))
			tmpSlot.VoiceMd5 = proto.String(arrVoice.GetVoiceMd5())
			objResult.ArrSlotContent = append(objResult.ArrSlotContent, tmpSlot)
		}
	} else if staticField.IntVoiceShow == VOICE_SHOW_BUTTON && arrPost.GetPtype() == 1 {
		pid := arrPost.GetPostId()
		if staticField.VoiceList[int64(pid)] != nil {
			arrVoice := staticField.VoiceList[int64(pid)]
			tmpSlot := new(meta.PbContent)
			tmpSlot.Type = proto.Uint32(uint32(arrVoice.GetType()))
			tmpSlot.DuringTime = proto.Uint32(uint32(arrVoice.GetDuringTime()))
			tmpSlot.VoiceMd5 = proto.String(arrVoice.GetVoiceMd5())
			if isFloor == 1 {
				tmpSlot.IsSub = proto.Uint32(1)
			}

			objResult.ArrSlotContent = append(objResult.ArrSlotContent, tmpSlot)
		}
	}

	tmpContent := tbrichtext.ProcessContent(objResult.ArrSlotContent, staticField.StrClientVersion, canShowNewSmile, false)
	for _, slot := range tmpContent {
		if slot.GetType() == tbrichtext.SLOT_TYPE_AT {
			if slot.GetUn() == "贴吧用户_0000000" { // uid为0时会自动生成这个，php不会做一下兼容
				slot.Un = proto.String("")
				slot.Text = proto.String("")
				continue
			}

			if slot.GetUid() > 0 {
				staticField.AtUID = append(staticField.AtUID, slot.GetUid())
			} else {
				staticField.AtUname = append(staticField.AtUname, slot.GetUn())
			}
		}
		if slot.GetType() == tbrichtext.SLOT_TYPE_IMG {
			for _, c := range content {
				if c.GetTag() == tbrichtext.CONTENT_ITEM_TAG_IMG && c.GetSrc() == slot.GetSrc() {
					slot.Height = proto.Uint32(uint32(c.GetHeight()))
					slot.Width = proto.Uint32(uint32(c.GetWidth()))
				}
			}
		}
	}

	arrPost.Content = utilCommon.GetInterfacePtr(tmpContent)
}

func processLongPic(content []*meta.PbContent, baseData *types.CPbFloorBaseData) {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest
	for _, slot := range content {
		if tbrichtext.SLOT_TYPE_IMG == slot.GetType() || tbrichtext.SLOT_TYPE_GRAFFITI == slot.GetType() {
			if staticField.IntPbImgCdn > 0 {
				spec := &imgcdn.ProcPic{
					ScreenWidth:     utilCommon.Tvttt(objReq.GetPrivateAttr("scr_w", 0), utilCommon.TTT_INT32).(int32),
					ScreenHeigh:     utilCommon.Tvttt(objReq.GetPrivateAttr("scr_h", 0), utilCommon.TTT_INT32).(int32),
					ScreenDip:       utilCommon.Tvttt(objReq.GetPrivateAttr("scr_dip", 0), utilCommon.TTT_FLOAT64).(float64),
					QType:           utilCommon.Tvttt(objReq.GetPrivateAttr("q_type", 0), utilCommon.TTT_INT32).(int32),
					SubappType:      staticField.StrSubappType,
					PicStrategyType: staticField.PbPicStrategyType,
					UseCdnUrl:       true,
					ClientVersion:   staticField.StrClientVersion,
					ClientType:      staticField.IntClientType,
				}

				if slot.GetWidth() >= slot.GetHeight() &&
					(staticField.IntClientType != clientvers.CLIENT_TYPE_ANDROID || clientvers.Compare(staticField.StrClientVersion, "12.36.0") < 0) {
					spec.NeedSampleBigPic = true
				}

				imgcdn.PbProcNewPicUrl(slot, spec)
				if time.Now().Unix()-staticField.IntCdnErrTime <= 64800 {
					if slot.GetCdnSrc() != "" {
						slot.CdnSrc = proto.String("http://c.tieba.baidu.com/c/p/img?src=" + slot.GetCdnSrc())
					}
					if slot.GetBigCdnSrc() != "" {
						slot.BigCdnSrc = proto.String("http://c.tieba.baidu.com/c/p/img?src=" + slot.GetBigCdnSrc())
					}
				}
			}
		}
	}
}

func fetchTPointContent(baseData *types.CPbFloorBaseData, tpoint *post.PbTPoint) {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	versionConf := map[int64]clientvers.VersionSwitch{
		stcdefine.CLIENT_TYPE_IPHONE: {
			Switch:     1,
			MinVersion: "6.9.6",
		},
		stcdefine.CLIENT_TYPE_ANDROID: {
			Switch:     1,
			MinVersion: "6.9.6",
		},
		stcdefine.CLIENT_TYPE_HARMONY: {
			Switch:     1,
			MinVersion: "6.9.6",
		},
	}

	// btn link
	staticField.TPointPost = &client.TPointPost{
		IsTuiguang: proto.Uint32(1),
		Position:   proto.String("广告"),
		TemplateId: proto.Int64(tpoint.GetTpBasic().GetLandMark()),
		TagName:    proto.String(tpoint.GetTemplateId()),
	}

	isNewVersion := clientvers.Check(versionConf, int64(staticField.IntClientType), staticField.StrClientVersion)
	if isNewVersion && staticField.IntClientType == clientvers.CLIENT_TYPE_ANDROID &&
		clientvers.Compare(staticField.StrClientVersion, "6.9.7") == 0 {
		isNewVersion = false
	}

	lastType := uint32(0)
	contents := staticField.PostList.GetContent().([]*meta.PbContent)
	tmp := make([]*meta.PbContent, 0)
	for _, content := range contents {
		if content.GetType() == tbrichtext.SLOT_TYPE_LINK {
			if isNewVersion {
				content.Type = proto.Uint32(tbrichtext.SLOT_TYPE_BUTTONLINK)
				content.BtnType = proto.Uint32(0)
				content.Text = nil
			} else {
				content.Text = proto.String("查看详情>>")
			}

		} else if content.GetType() == tbrichtext.SLOT_TYPE_TEXT &&
			(lastType == tbrichtext.SLOT_TYPE_LINK || lastType == tbrichtext.SLOT_TYPE_BUTTONLINK) {
			text := content.GetText()
			if len(text) > len(BTNLINK_TEXT) {
				text = text[:len(BTNLINK_TEXT)]
			}

			if text == BTNLINK_TEXT {
				text = text[len(BTNLINK_TEXT)-1:]
				if text == "" {
					lastType = content.GetType()
					continue
				} else {
					content.Text = proto.String(text)
				}
			}

		} else if content.GetType() == tbrichtext.SLOT_TYPE_IMG {
			// 修复7.0图片展示多一张bug
			lastType = content.GetType()
			continue
		}

		tmp = append(tmp, content)
		lastType = content.GetType()
	}

	staticField.PostList.Content = utilCommon.GetInterfacePtr(tmp)

	images := tpoint.GetTemplateImages()
	if len(images) > 3 {
		images = images[:3]
	}

	for _, img := range images {
		if img.GetHeight() <= 0 || img.GetWidth() <= 0 {
			continue
		}

		// img_type default
		slot := &meta.PbContent{
			Type:  proto.Uint32(tbrichtext.SLOT_TYPE_IMG),
			Src:   proto.String(img.GetImageUrl()),
			Bsize: proto.String(fmt.Sprintf("%d,%d", img.GetWidth(), img.GetHeight())),
		}

		spec := &imgcdn.ProcPic{
			ScreenWidth: utilCommon.Tvttt(objReq.GetPrivateAttr("scr_w", 0), utilCommon.TTT_INT32).(int32),
			ScreenHeigh: utilCommon.Tvttt(objReq.GetPrivateAttr("scr_h", 0), utilCommon.TTT_INT32).(int32),
			ScreenDip:   utilCommon.Tvttt(objReq.GetPrivateAttr("scr_dip", 0), utilCommon.TTT_FLOAT64).(float64),
		}
		imgcdn.PbProcNewPicUrl(slot, spec)

		if img.GetBindUrl() != "" && isNewVersion {
			slot.Type = proto.Uint32(tbrichtext.SLOT_TYPE_PICLINK)
			slot.Link = proto.String(img.GetBindUrl())
			slot.BigCdnSrc = nil
		}

		tmp = append(tmp, slot)
	}

	staticField.PostList.Content = utilCommon.GetInterfacePtr(tmp)

}

func process7_1TPoint(baseData *types.CPbFloorBaseData, tpoint *post.PbTPoint) {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	if tpoint.GetTpType() == TYPE_TEMPLATE_SKIN {
		if len(tpoint.GetTpDetail()) > 0 {
			staticField.SkinInfo = &client.SkinInfo{
				SkinSize:  proto.String(tpoint.GetTpDetail()[0].GetTpSkinClient()),
				Skin:      proto.String("720,80"),
				Url:       proto.String(tpoint.GetTpDetail()[0].GetUrl()),
				ObjId:     proto.String(tpoint.GetTemplateId()),
				MonitorId: proto.String(tpoint.GetMonitorId()),
			}
		}

	}

	staticField.TPointPost = &client.TPointPost{
		IsTuiguang: proto.Uint32(1),
		Position:   proto.String("广告"),
		TemplateId: proto.Int64(tpoint.GetTpBasic().GetLandMark()),
		TagName:    proto.String(tpoint.GetTemplateId()),
	}

	// 把帖子里面的图片去掉
	contents := staticField.PostList.GetContent().([]*meta.PbContent)
	tmp := make([]*meta.PbContent, 0)
	for _, content := range contents {
		if content.GetType() == tbrichtext.SLOT_TYPE_IMG {
			continue
		}

		tmp = append(tmp, content)
	}

	staticField.PostList.Content = utilCommon.GetInterfacePtr(tmp)

	// detail_info 查看详情以及跳转的链接
	if tpoint.GetTpBasic().GetDetailUrl() != "" {
		staticField.TPointPost.DetailInfo = &client.DetailInfo{
			Text: proto.String("查看详情"),
			Url:  proto.String(tpoint.GetTpBasic().GetDetailUrl()),
		}
	}

	is7_5Version := clientvers.Compare("7.5.0", staticField.StrClientVersion) >= 0
	if tpoint.GetTpType() == TYPE_TEMPLATE_HAS_VIDEO {
		if is7_5Version && len(tpoint.GetTpDetail()) > 0 {
			{
				staticField.TPointPost.TVideo = &client.VideoInfo{
					VideoUrl:        proto.String(tpoint.GetTpDetail()[0].GetVideoUrl()),
					VideoWidth:      proto.Uint32(uint32(tpoint.GetTpDetail()[0].GetVideoWidth())),
					VideoHeight:     proto.Uint32(uint32(tpoint.GetTpDetail()[0].GetVideoHeight())),
					ThumbnailUrl:    proto.String(tpoint.GetTpDetail()[0].GetThumbnailUrl()),
					ThumbnailWidth:  proto.Uint32(uint32(tpoint.GetTpDetail()[0].GetVideoWidth())),
					ThumbnailHeight: proto.Uint32(uint32(tpoint.GetTpDetail()[0].GetVideoHeight())),
				}
			}

			if staticField.IntClientType == clientvers.CLIENT_TYPE_ANDROID || clientvers.Compare("7.6.0", staticField.StrClientVersion) >= 0 {
				if len(tpoint.GetTpDetail()) > 0 {
					tpoint.GetTpDetail()[0].Src = proto.String(tpoint.GetTpDetail()[0].GetThumbnailUrl())
					tpoint.GetTpDetail()[0].Width = proto.Int32(tpoint.GetTpDetail()[0].GetVideoWidth())
					tpoint.GetTpDetail()[0].Height = proto.Int32(tpoint.GetTpDetail()[0].GetVideoHeight())
					tpoint.GetTpDetail()[0].Url = proto.String(tpoint.GetTpBasic().GetDetailUrl())
				}
			}
		}
	}

	// t_imgs 图片区
	// 如果没有图片
	if tpoint.GetTpType() == TYPE_TEMPLATE_NO_IMG {
		staticField.TPointPost.TemplateType = proto.Int32(TYPE_NO_IMG)
	} else {

		timages := make([]*client.Timgs, 0)

		tpDetail := tpoint.GetTpDetail()
		if len(tpDetail) > 3 {
			tpDetail = tpDetail[:3]
		}

		for _, image := range tpDetail {
			if image.GetHeight() <= 0 || image.GetWidth() <= 0 {
				continue
			}

			strImgUrl := image.GetSrc()

			// 图片做CDN 处理
			slot := &meta.PbContent{
				Type:  proto.Uint32(tbrichtext.SLOT_TYPE_IMG),
				Src:   proto.String(image.GetSrc()),
				Bsize: proto.String(fmt.Sprintf("%d,%d", image.GetWidth(), image.GetHeight())),
			}

			spec := &imgcdn.ProcPic{
				ScreenWidth: utilCommon.Tvttt(objReq.GetPrivateAttr("scr_w", 0), utilCommon.TTT_INT32).(int32),
				ScreenHeigh: utilCommon.Tvttt(objReq.GetPrivateAttr("scr_h", 0), utilCommon.TTT_INT32).(int32),
				ScreenDip:   utilCommon.Tvttt(objReq.GetPrivateAttr("scr_dip", 0), utilCommon.TTT_FLOAT64).(float64),
				QType:       2,
			}
			imgcdn.PbProcNewPicUrl(slot, spec)
			bsize := slot.GetBsize()

			if slot.GetCdnSrc() != "" {
				strImgUrl = slot.GetCdnSrc()
			}

			var strUrl, strBigCdnUrl, strDesMain, strDesSub string
			var intFlag uint32
			if image.GetUrl() != "" {
				strUrl = image.GetUrl()
				strBigCdnUrl = slot.GetBigCdnSrc()
				intFlag = FLAG_JUMP_URL
			} else if tpoint.GetTpType() == TYPE_TEMPLATE_HAS_VIDEO {
				strBigCdnUrl = slot.GetBigCdnSrc()
				intFlag = FLAG_BIG_IMG_URL
			} else {
				continue
			}

			// 图片描述区
			if tpoint.GetTpType() == TYPE_TEMPLATE_HAS_IMG_HAS_DES || tpoint.GetTpType() == TYPE_TEMPLATE_HAS_IMG_HAS_DES_HAS_TEL {
				strDesMain = image.GetDesc1()
				if image.GetDesc2() != "" {
					strDesSub = image.GetDesc2()
				}
			}

			timages = append(timages, &client.Timgs{
				ImgUrl:    proto.String(strImgUrl),
				Url:       proto.String(strUrl),
				Flag:      proto.Uint32(intFlag),
				DesMain:   proto.String(strDesMain),
				DesSub:    proto.String(strDesSub),
				Bsize:     proto.String(bsize),
				BigCdnUrl: proto.String(strBigCdnUrl),
			})

		}

		staticField.TPointPost.TemplateType = proto.Int32(TYPE_HAS_IMG)
		staticField.TPointPost.TImgs = timages
	}

	// 7.5视频类型修改
	if tpoint.GetTpType() == TYPE_TEMPLATE_HAS_VIDEO && is7_5Version {
		staticField.TPointPost.TemplateType = proto.Int32(TYPE_HAS_VIEDO)
	}

	actBtn := make([]*client.ActBtn, 0)
	if tpoint.GetTpBasic().GetUrl() != "" {
		actBtn = append(actBtn, &client.ActBtn{
			Type: proto.Uint32(TYPE_OFFICIAL_PHONE),
			Url:  proto.String(tpoint.GetTpBasic().GetUrl()),
			Text: proto.String("官方网站"),
		})
	}

	if tpoint.GetTpBasic().GetTel() != "" {
		actBtn = append(actBtn, &client.ActBtn{
			Type: proto.Uint32(TYPE_OFFICIAL_WEBSET),
			Url:  proto.String(tpoint.GetTpBasic().GetTel()),
			Text: proto.String("电话咨询"),
		})
	}

	staticField.TPointPost.ActBtn = actBtn

	// 模板关键字id
	if tpoint.GetMonitorId() != "" {
		staticField.TPointPost.MonitorId = proto.String(tpoint.GetMonitorId())
	}
}

func getAtUser(ctx context.Context, baseData *types.CPbFloorBaseData) {
	staticField := baseData.StaticField
	if len(staticField.AtUID) == 0 && len(staticField.AtUname) == 0 {
		return
	}

	multi := tbservice.Multi()

	if len(staticField.AtUname) > 0 {
		getUidByNamesParam := &tbservice.Parameter{
			Service: "user",
			Method:  "getUidByNames",
			Input: map[string]interface{}{
				"names": staticField.AtUname,
			},
			Output: &user.GetUidByNamesRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getUidByNames", getUidByNamesParam)
	}

	if len(staticField.AtUID) > 0 {
		mgetUserDataParam := &tbservice.Parameter{
			Service: "user",
			Method:  "mgetUserData",
			Input: map[string]interface{}{
				"user_id": staticField.AtUID,
			},
			Output: &user.MgetUserDataRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetUserData", mgetUserDataParam)
	}

	multi.Call(ctx)
	var getUidByNamesRes *user.GetUidByNamesRes
	if len(staticField.AtUname) > 0 {
		getUidByNamesInter, err := multi.GetResult(ctx, "getUidByNames")
		getUidByNamesRes = getUidByNamesInter.(*user.GetUidByNamesRes)
		if err != nil {
			tbcontext.WarningF(ctx, "call user::getUidByNames fail: %v", err)
		}
	}

	var mgetUserDataRes *user.MgetUserDataRes
	if len(staticField.AtUID) > 0 {
		mgetUserDataInter, err := multi.GetResult(ctx, "mgetUserData")
		mgetUserDataRes = mgetUserDataInter.(*user.MgetUserDataRes)
		if err != nil {
			tbcontext.WarningF(ctx, "call user::mgetUserData fail: %v", err)
		}

	}

	for _, subPost := range staticField.Comments.GetPostInfos() {
		contents := subPost.GetContent().([]*meta.PbContent)
		for _, slot := range contents {
			if slot.GetType() == tbrichtext.SLOT_TYPE_AT {
				strPrefix := "@"
				if slot.GetText() != "" && slot.GetText()[0] != '@' {
					strPrefix = ""
				}
				un := slot.GetUn()
				uid := slot.GetUid()
				if uid > 0 && mgetUserDataRes != nil && mgetUserDataRes.GetUserInfo()[uid] != nil {
					userInfo := mgetUserDataRes.GetUserInfo()[uid]
					strName := userInfo.GetUserNickname()
					if strName == "" {
						strName = userInfo.GetUserName()
					}
					param := &usertool.NickNameByVersion{
						UserNickNameV2: userInfo.GetUserNicknameV2(),
						DisplayName:    userInfo.GetDisplayName(),
					}
					strName = usertool.GetUserNickNameByVersion(staticField.IntClientType, staticField.StrClientVersion, param, strName)
					slot.Text = proto.String(strPrefix + strName)
				} else {
					slot.Uid = proto.Int64(0)
					if getUidByNamesRes != nil && getUidByNamesRes.Data != nil && getUidByNamesRes.Data[un] != nil && len(getUidByNamesRes.Data[un]) > 0 {
						slot.Uid = proto.Int64(getUidByNamesRes.Data[un][0].GetUserId())
					}
				}

				slot.Un = nil
			}
		}
	}

	contents := staticField.PostList.GetContent().([]*meta.PbContent)
	for _, slot := range contents {
		if slot.GetType() == tbrichtext.SLOT_TYPE_AT {
			strPrefix := "@"
			if slot.GetText() != "" && slot.GetText()[0] != '@' {
				strPrefix = ""
			}
			un := slot.GetUn()
			uid := slot.GetUid()
			if uid > 0 && mgetUserDataRes != nil && mgetUserDataRes.GetUserInfo()[uid] != nil {
				userInfo := mgetUserDataRes.GetUserInfo()[uid]
				strName := userInfo.GetUserNickname()
				if strName == "" {
					strName = userInfo.GetUserName()
				}
				param := &usertool.NickNameByVersion{
					UserNickNameV2: userInfo.GetUserNicknameV2(),
					DisplayName:    userInfo.GetDisplayName(),
				}
				strName = usertool.GetUserNickNameByVersion(staticField.IntClientType, staticField.StrClientVersion, param, strName)
				slot.Text = proto.String(strPrefix + strName)
			} else {
				slot.Uid = proto.Int64(0)
				if getUidByNamesRes != nil && getUidByNamesRes.Data != nil && getUidByNamesRes.Data[un] != nil && len(getUidByNamesRes.Data[un]) > 0 {
					slot.Uid = proto.Int64(getUidByNamesRes.Data[un][0].GetUserId())
				}
			}

			slot.Un = nil
		}
	}

}

func getTibaPlusPlugInfo(ctx context.Context, baseData *types.CPbFloorBaseData) {
	staticField := baseData.StaticField
	if len(staticField.TiebaPlusPlugFloorTokens) == 0 {
		return
	}

	multi := tbservice.Multi()

	for i, token := range staticField.TiebaPlusPlugFloorTokens {
		decodePluginTokenParam := &tbservice.Parameter{
			Service: "adsense",
			Method:  "decodePluginToken",
			Input: map[string]interface{}{
				"plugin_token":   token,
				"client_type":    staticField.IntClientType,
				"client_version": staticField.StrClientVersion,
			},
			Output: &adsense.DecodePluginTokenRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "decodePluginToken"+strconv.Itoa(i), decodePluginTokenParam)
	}

	multi.Call(ctx)

	for i, token := range staticField.TiebaPlusPlugFloorTokens {
		decodePluginTokenInter, err := multi.GetResult(ctx, "decodePluginToken"+strconv.Itoa(i))
		decodePluginTokenRes := decodePluginTokenInter.(*adsense.DecodePluginTokenRes)
		if err != nil || decodePluginTokenRes.Errno == nil || decodePluginTokenRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call adsense:decodePluginToken fail, err: %v, output: %v", err, utilCommon.ToString(decodePluginTokenRes))
			continue
			continue
		}

		staticField.TiebaPlusPlugInfo[token] = decodePluginTokenRes.GetData()

	}

}
