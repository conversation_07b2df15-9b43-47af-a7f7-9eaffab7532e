package ext

import (
	"context"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/service"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/novel"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type NovelFans struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_novel_fans", func() engine.Job {
		return &NovelFansOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewNovelFans(ctx *engine.Context) *NovelFans {
	return &NovelFans{
		ctx: ctx,
	}
}
func (a *NovelFans) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	if baseData == nil || baseData.StaticField == nil {
		return false
	}
	// 鸿蒙暂不支持小说
	if baseData.StaticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
		return false
	}
	return true
}
func (a *NovelFans) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField

	novelInfo, _ := staticField.ForumInfo.GetAttrs().GetNovelStruct()
	if novelInfo == nil || novelInfo.GetIsNovel() != 1 {
		return nil
	}

	input := map[string]interface{}{
		"forum_id":   staticField.ForumInfo.GetForumName().GetForumId(),
		"author_id":  novelInfo.GetAuthorId(),
		"novel_id":   novelInfo.GetNovelId(),
		"reader_ids": []int64{staticField.PostList.GetUserId()},
	}
	res := new(novel.GetFansLevelRes)
	err := tbservice.Call(ctx, "novel", "getFansLevel", input, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service novel:getFansLevel, err = %v, input = %v, output = %v", err, input, res)
		return nil
	}

	if novelData, ok := res.GetData()[staticField.PostList.GetUserId()]; ok {
		level := novelData.GetFansLevel()
		levelInfo := service.GetLevelInfo(int(level))

		staticField.NovelFansInfo[staticField.PostList.GetUserId()] = &client.NovelFansInfo{
			Level:     proto.Int32(int32(level)),
			LevelName: proto.String(levelInfo.Name),
			LevelIcon: proto.String(levelInfo.Icon),
		}
	}

	return nil
}

type NovelFansOperator struct {
}

func (rdop *NovelFansOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewNovelFans(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "novel_fans execute fail: %v", err)
		return err
	}

	return nil
}
