package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Forum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_forum", func() engine.Job {
		return &ForumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewForum(ctx *engine.Context) *Forum {
	return &Forum{
		ctx: ctx,
	}
}
func (a *Forum) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *Forum) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField

	if staticField.PostInfo.GetForumId() == 0 {
		return nil
	}

	input := map[string]interface{}{
		"forum_id": staticField.PostInfo.GetForumId(),
	}
	res := new(forum.GetBtxInfoRes)
	err := tbservice.Call(ctx, "forum", "getBtxInfo", input, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service forum:getBtxInfo, err = %v, input = %v, output = %v", err, input, res)
		return nil
	}

	staticField.ForumInfo = res

	return nil
}

type ForumOperator struct {
}

func (rdop *ForumOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewForum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum execute fail: %v", err)
		return err
	}

	return nil
}
