package ext

import (
	"context"
	"errors"
	"strings"

	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/php"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/contentforumsearch"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// ContentForumSearch 根据算子名，定义算子结构体
type ContentForumSearch struct {
	ctx           *engine.Context
	clientVersion string // 获取客户端版本
	clientType    int
}

const (
	OperatorName               = "pbfloor_contentForumSearch"
	searchWordConfigTable      = "tb_wordlist_redis_pb_forum_search_word_config"       // 搜索词配置表
	searchWordForumConfigTable = "tb_wordlist_redis_pb_forum_search_forum_word_config" // 吧搜索词配置表
	showConfigTable            = "tb_wordlist_redis_pb_forum_search_config"
	keyCommonShow              = "common_show"         // 通用展示配置
	keyPostShow                = "post_show"           // post展示配置
	keySubPostShow             = "sub_post_show"       // 楼中楼展示配置
	keyThreadShow              = "thread_show"         // thread展示配置
	keyThemeColor              = "theme_color"         //默认 主题色
	keyThemeColorIphone        = "theme_color_iphone"  //ios 主题色
	keyThemeColorAndroid       = "theme_color_android" //安卓 主题色
	keyIconUrl                 = "icon_url"            //默认 icon
	keyIconUrlIphone           = "icon_url_iphone"     //ios icon
	keyIconUrlAndroid          = "icon_url_android"    //安卓 icon
)

type ContentForumSearchOperator struct {
}

// 初始化
func init() {
	// 注册算子
	err := engine.RegisterOperator(OperatorName, func() engine.Job {
		// 返回Operator
		return &ContentForumSearchOperator{}
	})
	if err != nil {
		panic(err)
	}
}

// NewContentForumSearch 初始化算子
func NewContentForumSearch(ctx *engine.Context, baseData *types.CPbFloorBaseData) *ContentForumSearch {
	if baseData == nil || baseData.BaseObj == nil || baseData.BaseObj.ObjRequest == nil {
		return nil
	}
	return &ContentForumSearch{
		ctx: ctx,
		clientVersion: common.TransforValueToTargetType(
			baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string),
		clientType: common.TransforValueToTargetType(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int),
	}
}

// IsValid 算子是否可执行校验
func (c *ContentForumSearch) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {
	// 算子执行条件逻辑
	// 大于等于12.76版本
	if clientvers.CompareV2(c.clientVersion, ">=", "12.76") {
		return true
	}

	// 最终返回true
	return false
}

// Execute 具体的业务实现逻辑
// @Param ctx 上下文对象
// @Param outData 输出的proto结构体
// @Param baseData 输入的结构体
func (c *ContentForumSearch) Execute(ctx context.Context,
	outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	// 获取接口的输入参数
	if nil == c || nil == ctx || nil == baseData || nil == outData || nil == baseData.StaticField.PostInfo ||
		nil == baseData.StaticField || nil == baseData.StaticField.ThreadInfo || nil == baseData.StaticField.PostList {
		return errors.New("input params invalid")
	}

	forumID := baseData.StaticField.PostInfo.GetForumId()
	forumIDStr := cast.ToString(forumID)
	err, words := c.getWords(ctx, forumIDStr)
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to getWords: %v", err)
	}
	if len(words) > 0 {
		baseData.StaticField.PbSearchWords = words
	}
	err, showConfigData, themeColorInfo, iconInfo := c.GetShowConfigData(ctx)
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to GetShowConfigData: %v", err)
	}
	baseData.StaticField.PbSearchConfig = showConfigData
	baseData.StaticField.PbSearchThemeColorInfo = themeColorInfo
	baseData.StaticField.PbSearchIconInfo = iconInfo
	return nil
}

// 获取所有可展示的词
func (c *ContentForumSearch) getWords(ctx context.Context, forumID string) (error, map[string]string) {
	// 读取词表全部数据tb_wordlist_redis_pb_forum_search_word_config
	wordConfigData, err := wordserver.GetTableItems(ctx, searchWordConfigTable)
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to fetch tb_wordlist_redis_pb_forum_search_word_config: %v", err)
		return err, wordConfigData
	}
	// 查询单吧词表
	resData, err := wordserver.QueryKey(ctx, searchWordForumConfigTable, forumID)
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to fetch tb_wordlist_redis_pb_forum_search_forum_word_config: %v,key: s%s", err, forumID)
		return nil, wordConfigData
	}
	if resData != "" {
		words2query, err := php.Unserialize([]byte(resData))
		if err != nil {
			tbcontext.WarningF(ctx, "unserialize words2query fail: %v", err)
			return nil, wordConfigData
		}
		var wordQuery []string
		err = common.StructAToStructBCtx(ctx, words2query, &wordQuery)
		if err != nil {
			tbcontext.WarningF(ctx, "change []string fail: %v", err)
			return nil, wordConfigData
		}
		// 追加单吧关键词
		for _, value := range wordQuery {
			parts := strings.Split(value, " ")
			if len(parts) >= 2 {
				wordConfigData[parts[0]] = parts[1]
			} else {
				tbcontext.WarningF(ctx, "split wordQuery fail: %s", value)
			}
		}
	}
	return nil, wordConfigData
}

// 获取所有展示策略配置
func (c *ContentForumSearch) GetShowConfigData(ctx context.Context) (error, *contentforumsearch.AllShowConfigDataStruct, *client.ThemeColorInfo, string) {
	var showConfigData = &contentforumsearch.AllShowConfigDataStruct{} // 初始化结构体
	var themeColorInfo = &client.ThemeColorInfo{}                      // 初始化结构体
	var iconInfo = ""
	showConfigData = c.GetDefaultShowConfigData()

	// 读取词表tb_wordlist_redis_pb_forum_search_config全部数据
	configData, err := wordserver.QueryItems(ctx, showConfigTable, []string{
		keyCommonShow, keyPostShow, keySubPostShow, keyThreadShow,
		keyThemeColor, keyThemeColorIphone, keyThemeColorAndroid,
		keyIconUrl, keyIconUrlIphone, keyIconUrlAndroid})
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to fetch %s: %v", showConfigTable, err)
		return err, showConfigData, themeColorInfo, iconInfo // 返回错误并返回
	}

	// 处理主题颜色和图标信息
	themeColorInfo = c.GetThemeColorInfo(ctx, configData)
	iconInfo = c.GetIconInfo(ctx, configData)

	// 遍历数据
	newConfigData := make(map[string]*contentforumsearch.ShowConfigDataStruct)
	for key, content := range configData {
		if key != keyCommonShow && key != keyPostShow && key != keySubPostShow && key != keyThreadShow {
			continue // 跳过主题颜色处理，后面单独处理
		}
		oneConfigData := &contentforumsearch.ShowConfigDataStruct{} // 每次循环都新建结构体实例
		err3 := jsoniter.Unmarshal([]byte(content), oneConfigData)
		if err3 != nil {
			tbcontext.WarningF(ctx, "Failed to unmarshal config data for key %s: %v", key, err)
			continue // 记录错误并继续处理其他数据
		}
		newConfigData[key] = oneConfigData
	}

	// 设置showConfigData的各个字段
	if commonShow, ok := newConfigData[keyCommonShow]; ok {
		showConfigData.Thread = commonShow
		showConfigData.Post = commonShow
		showConfigData.SubPost = commonShow
	}
	if threadShow, ok := newConfigData[keyThreadShow]; ok {
		showConfigData.Thread = threadShow
	}
	if postShow, ok := newConfigData[keyPostShow]; ok {
		showConfigData.Post = postShow
	}
	if subPostShow, ok := newConfigData[keySubPostShow]; ok {
		showConfigData.SubPost = subPostShow
	}
	return nil, showConfigData, themeColorInfo, iconInfo
}

// DoImpl 实现入口
func (rdop *ContentForumSearchOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewContentForumSearch(ctx, baseData)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}
	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "content_forum_search execute fail: %v", err)
		return err
	}
	return nil
}

// 1 获取默认展示策略配置 2 获取兜底色 0 获取全部默认配置
func (c *ContentForumSearch) GetDefaultShowConfigData() *contentforumsearch.AllShowConfigDataStruct {
	var showConfigData = &contentforumsearch.AllShowConfigDataStruct{} // 初始化结构体
	// 设置showConfigData的各个字段
	defaultShow := &contentforumsearch.ShowConfigDataStruct{
		WordInterva:               1,
		DisplayCeiling:            2,
		FloorInterva:              2,
		WordDisplay:               2,
		FliterThreadWordBySubPost: 1,
		FliterPostWordBySubPost:   1,
	}
	showConfigData.Common = defaultShow
	showConfigData.Thread = defaultShow
	showConfigData.Post = defaultShow
	showConfigData.SubPost = defaultShow
	return showConfigData
}

// 获取默认主题颜色配置
func (c *ContentForumSearch) GetThemeColorInfo(ctx context.Context, configData map[string]string) *client.ThemeColorInfo {
	var themeColorInfo = &client.ThemeColorInfo{} // 初始化结构体
	defaultThemeColorIphone := "#335ACCFF"        //ios默认的主题颜色
	defaultThemeColorAndroid := "#335ACCFF"       //安卓默认的主题颜色
	defaultThemeColor := "#335ACCFF"              //默认的主题颜色
	defaultThemeColors := map[string]*client.ThemeColorInfo{
		"theme_color_iphone": &client.ThemeColorInfo{
			Day: &client.ThemeElement{
				CommonColor: proto.String(defaultThemeColorIphone),
			}, //日间
			Night: &client.ThemeElement{
				CommonColor: proto.String(defaultThemeColorIphone),
			}, //夜间
			Dark: &client.ThemeElement{
				CommonColor: proto.String(defaultThemeColorIphone),
			}, //暗黑
		},
		"theme_color_android": &client.ThemeColorInfo{
			Day: &client.ThemeElement{
				CommonColor: proto.String(defaultThemeColorAndroid),
			},
			Night: &client.ThemeElement{
				CommonColor: proto.String(defaultThemeColorAndroid),
			},
			Dark: &client.ThemeElement{
				CommonColor: proto.String(defaultThemeColorAndroid),
			},
		},
		"theme_color": &client.ThemeColorInfo{
			Day: &client.ThemeElement{
				CommonColor: proto.String(defaultThemeColor),
			},
			Night: &client.ThemeElement{
				CommonColor: proto.String(defaultThemeColor),
			},
			Dark: &client.ThemeElement{
				CommonColor: proto.String(defaultThemeColor),
			},
		},
	}

	keyThemeColorStr := "theme_color"
	//鸿蒙和安卓使用同一套配置
	if c.clientType == stcdefine.CLIENT_TYPE_IPHONE {
		keyThemeColorStr = "theme_color_iphone"
	} else if c.clientType == stcdefine.CLIENT_TYPE_ANDROID || c.clientType == stcdefine.CLIENT_TYPE_HARMONY {
		keyThemeColorStr = "theme_color_android"
	}

	err2 := jsoniter.Unmarshal([]byte(configData[keyThemeColorStr]), themeColorInfo)
	if err2 != nil {
		tbcontext.WarningF(ctx, "Failed to unmarshal %s data: %v", keyThemeColorStr, err2)
		if themeColor, ok := defaultThemeColors[keyThemeColorStr]; ok {
			return themeColor
		}
		return defaultThemeColors["theme_color"]
	}
	return themeColorInfo
}

// 获取默认icon配置
func (c *ContentForumSearch) GetIconInfo(ctx context.Context, configData map[string]string) string {
	iconList := map[string]string{
		"icon_url_android": "https://tieba-ares.cdn.bcebos.com/mis/2024-12/1735621091187/260c61c43115.png", //蓝色
		"icon_url_iphone":  "https://tieba-ares.cdn.bcebos.com/mis/2024-12/1735621113403/1ff8d1843f8d.png", //紫色
		"icon_url":         "https://tieba-ares.cdn.bcebos.com/mis/2024-12/1734919958661/fa9b7c214cab.png", //默认
	}
	keyIconUrlStr := "icon_url"
	//鸿蒙和安卓使用同一套配置
	if c.clientType == stcdefine.CLIENT_TYPE_IPHONE {
		keyIconUrlStr = "icon_url_iphone"
	} else if c.clientType == stcdefine.CLIENT_TYPE_ANDROID || c.clientType == stcdefine.CLIENT_TYPE_HARMONY {
		keyIconUrlStr = "icon_url_android"
	}

	if iconUrl, ok := configData[keyIconUrlStr]; ok && iconUrl != "" {
		return iconUrl
	}
	if iconUrl, ok := iconList[keyIconUrlStr]; ok && iconUrl != "" {
		return iconUrl
	}

	return iconList["icon_url"]
}
