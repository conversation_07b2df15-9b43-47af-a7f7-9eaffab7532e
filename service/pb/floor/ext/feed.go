package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Feed struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_feed", func() engine.Job {
		return &FeedOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewFeed(ctx *engine.Context) *Feed {
	return &Feed{
		ctx: ctx,
	}
}
func (a *Feed) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *Feed) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField

	if staticField.StrSubappType != "feed" {
		return nil
	}

	input := map[string]interface{}{
		"uids": staticField.ArrUid,
	}
	res := new(common.GetUkByFeedUidsRes)
	err := tbservice.Call(ctx, "common", "getUkByFeedUids", input, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service common:getUkByFeedUids, err = %v, input = %v, output = %v", err, input, res)
		return nil
	} else {
		staticField.ArrUserUK = res.GetData()
	}

	return nil
}

type FeedOperator struct {
}

func (rdop *FeedOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewFeed(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "feed execute fail: %v", err)
		return err
	}

	return nil
}
