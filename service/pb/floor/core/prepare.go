package core

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/versionmatch"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/stlog"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Prepare struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_prepare", func() engine.Job {
		return &PrepareOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPrepare(ctx *engine.Context) *Prepare {
	return &Prepare{
		ctx: ctx,
	}
}
func (a *Prepare) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *Prepare) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest
	staticField.IntKz = common.Tvttt(objReq.GetPrivateAttr("kz", 0), common.TTT_INT64).(int64)
	staticField.IntPid = common.Tvttt(objReq.GetPrivateAttr("pid", 0), common.TTT_INT64).(int64)
	staticField.IntSpid = common.Tvttt(objReq.GetPrivateAttr("spid", 0), common.TTT_INT64).(int64)
	staticField.IntPn = common.Tvttt(objReq.GetPrivateAttr("pn", 0), common.TTT_INT).(int)
	staticField.IsCommReverse = common.Tvttt(objReq.GetPrivateAttr("is_comm_reverse", 0), common.TTT_INT).(int)
	staticField.StrClientVersion = common.Tvttt(objReq.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	staticField.IntClientType = common.Tvttt(objReq.GetCommonAttr("client_type", 0), common.TTT_INT).(int)

	strConf := common.Tvttt(objReq.GetStrategy("pb_positioning_floor", ""), common.TTT_STRING).(string)
	check, _ := versionmatch.CheckClient(strConf, staticField.IntClientType, staticField.StrClientVersion, "app")
	if check {
		staticField.IsPositioningFloor = true
	}

	strConf = common.Tvttt(objReq.GetStrategy("pb_positioning_floor", ""), common.TTT_STRING).(string)
	check, _ = versionmatch.CheckClient(strConf, staticField.IntClientType, staticField.StrClientVersion, "app")
	if check {
		staticField.IsParseBdhd = true
	}

	strConf = common.Tvttt(objReq.GetStrategy("pb_female_icon", ""), common.TTT_STRING).(string)
	check, _ = versionmatch.CheckClient(strConf, staticField.IntClientType, staticField.StrClientVersion, "app")
	if check {
		staticField.GenderIcon = true
	}

	strConf = common.Tvttt(objReq.GetStrategy("pb_show_user_icon", ""), common.TTT_STRING).(string)
	check, confType := versionmatch.CheckClient(strConf, staticField.IntClientType, staticField.StrClientVersion, "app")
	if check {
		staticField.IntPbUserIconSize = confType
	}

	strConf = common.Tvttt(objReq.GetStrategy("pb_parse_phone", ""), common.TTT_STRING).(string)
	check, _ = versionmatch.CheckClient(strConf, staticField.IntClientType, staticField.StrClientVersion, "app")
	if check {
		staticField.IntPbParsePhone = confType
	}

	strConf = common.Tvttt(objReq.GetStrategy("pb_img_cdn", ""), common.TTT_STRING).(string)
	check, confType = versionmatch.CheckClient(strConf, staticField.IntClientType, staticField.StrClientVersion, "app")
	if check {
		staticField.IntPbImgCdn = 1
		staticField.PbPicStrategyType = confType
	}

	staticField.StrSubappType = common.Tvttt(objReq.GetCommonAttr("subapp_type", ""), common.TTT_STRING).(string)
	staticField.UserId = common.Tvttt(objReq.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)

	stlog.AddLog(ctx, "ispv", 1)

	//是否为重生版
	staticField.RebornVersion = clientvers.IsRebornServerVersion(staticField.IntClientType, staticField.StrClientVersion)


	return nil
}

type PrepareOperator struct {
}

func (rdop *PrepareOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewPrepare(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "prepare execute fail: %v", err)
		return err
	}

	return nil
}
