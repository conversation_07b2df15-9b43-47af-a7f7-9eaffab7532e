package core

import (
	"context"
	"fmt"
	"html"
	"math"
	"net/url"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/contentforumsearch"
	common2 "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/threadtype"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/common/meta"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/php"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tools/encode"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/service"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/format"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/imgcdn"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/newabtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/props"
	userUtil "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/usertool"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/adsense"
	protoForum "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/netdisk"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/theme"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/conf"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbapi/midlib/cupid"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbrichtext"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/video"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/util"
)

const (
	// 智能体头像框
	agentPendant = "https://tieba-ares.cdn.bcebos.com/mis/2023-12/1702542639036/2039fb5bfa44.png"
)

var MatchTIDre = regexp.MustCompile(`/p/(\d+)`)

type Response struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_response", func() engine.Job {
		return &ResponseOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewResponse(ctx *engine.Context) *Response {
	return &Response{
		ctx: ctx,
	}
}
func (a *Response) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *Response) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	processFloor(ctx, baseData)
	processUser(ctx, baseData)
	processReplyPrivateFlag(ctx, baseData)
	processABTest(ctx, baseData)

	staticField := baseData.StaticField
	outData.SubpostList = buildSubPost(ctx, baseData)
	outData.Post = buildPost(ctx, baseData)
	outData.Anti = buildAnti(ctx, baseData)
	outData.Page = staticField.Page
	outData.Thread = buildThread(ctx, baseData)
	outData.Forum = buildForum(ctx, baseData)
	outData.DisplayForum = buildDisplayForum(ctx, baseData, outData.Forum)
	if staticField.IsBlackWhite {
		outData.IsBlackWhite = proto.Int32(1)
	} else {
		outData.IsBlackWhite = proto.Int32(0)
	}

	// 处理小助手信息
	processHelper(baseData, outData)

	// 处理智能体
	processAgent(ctx, baseData, outData)
	processShopGoods(ctx, baseData, outData)

	return nil
}

type ResponseOperator struct {
}

func (rdop *ResponseOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewResponse(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "response execute fail: %v", err)
		return err
	}

	return nil
}

func processUser(ctx context.Context, baseData *types.CPbFloorBaseData) {
	staticField := baseData.StaticField

	bawuList := staticField.Bawulist.GetMapManagerList(ctx)
	for strType, arrList := range bawuList {
		for _, role := range arrList {
			if role.GetUser().GetUserId() != 0 && staticField.BawuMap[int64(role.GetUser().GetUserId())] == nil {
				staticField.BawuMap[int64(role.GetUser().GetUserId())] = &types.BawuInfo{
					IsBawu:   1,
					BawuType: strType,
				}
			}
		}
	}

	userListStr := make([]string, 0)
	for _, userId := range staticField.UserList {
		userListStr = append(userListStr, common.Tvttt(userId, common.TTT_STRING).(string))
	}
	// 读词表获取吧等级不展示名单，机器人账号不展示吧等级
	wordRes, err := wordserver.QueryItems(ctx, "tb_wordlist_redis_bot_no_level", userListStr)
	if err != nil {
		tbcontext.WarningF(ctx, " get tb_wordlist_redis_bot_no_level fail: %v", err)
	} else {
		for uidStr, tmp := range wordRes {
			staticField.LevelBlackList[common.Tvttt(uidStr, common.TTT_INT64).(int64)] = tmp
		}
	}

	// processGod := false
	// if clientvers.Compare(staticField.StrClientVersion, "7.2.0") != 1 {
	//	processGod = true
	// }
}

func processShopGoods(ctx context.Context, baseData *types.CPbFloorBaseData, outData *clientFloor.PbFloorRes) {
	if clientvers.CompareV2(baseData.StaticField.StrClientVersion, "<", "12.82") {
		tbcontext.TraceF(ctx, "client version < 12.82, skipped")
		return
	}

	t := baseData.StaticField.PostInfo.GetThreadType()
	if t != threadtype.ForumShopGoodsThread {
		return
	}

	UserIcon := baseData.StaticField.UserIcons

	// 处理主楼
	v := outData.GetPost()
	for i := 0; i < len(v.GetContent()); i++ {
		c := v.GetContent()[i]
		if c.GetType() == tbrichtext.SLOT_TYPE_AT {
			uid := c.GetUid()
			if iconList, ok := UserIcon[uid]; ok {
				for _, icon := range iconList {
					if icon.GetType() == "forum_manager" {
						bazhuPbContent := &client.PbContent{
							Type: proto.Uint32(tbrichtext.SLOT_TYPE_IMG_SPAN), // img_span
							ThemeColor: &client.ThemeColorInfo{
								Day: &client.ThemeElement{
									PatternImage:       proto.String(icon.GetImgSrc()),
									PatternImageHeight: proto.Int64(int64(icon.GetHeight())),
									PatternImageWidth:  proto.Int64(int64(icon.GetWidth())),
								},
								Dark: &client.ThemeElement{
									PatternImage:       proto.String(icon.GetImgSrc()),
									PatternImageHeight: proto.Int64(int64(icon.GetHeight())),
									PatternImageWidth:  proto.Int64(int64(icon.GetWidth())),
								},
							},
						}
						v.Content = append(v.Content[:i+1], append([]*client.PbContent{bazhuPbContent}, v.Content[i+1:]...)...)
					}
				}
			}

		}
	}

	for _, sub := range outData.GetSubpostList() {
		for i := 0; i < len(sub.GetContent()); i++ {
			c := sub.GetContent()[i]
			if c.GetType() == tbrichtext.SLOT_TYPE_AT {
				uid := c.GetUid()
				if iconList, ok := UserIcon[uid]; ok {
					for _, icon := range iconList {
						if icon.GetType() == "forum_manager" {
							bazhuPbContent := &client.PbContent{
								Type: proto.Uint32(tbrichtext.SLOT_TYPE_IMG_SPAN), // img_span
								ThemeColor: &client.ThemeColorInfo{
									Day: &client.ThemeElement{
										PatternImage:       proto.String(icon.GetImgSrc()),
										PatternImageHeight: proto.Int64(int64(icon.GetHeight())),
										PatternImageWidth:  proto.Int64(int64(icon.GetWidth())),
									},
									Dark: &client.ThemeElement{
										PatternImage:       proto.String(icon.GetImgSrc()),
										PatternImageHeight: proto.Int64(int64(icon.GetHeight())),
										PatternImageWidth:  proto.Int64(int64(icon.GetWidth())),
									},
								},
							}
							sub.Content = append(sub.Content[:i+1], append([]*client.PbContent{bazhuPbContent}, sub.Content[i+1:]...)...)
						}
					}
				}
			}
		}
	}
}

func processAgent(ctx context.Context, baseData *types.CPbFloorBaseData, outData *clientFloor.PbFloorRes) {
	if baseData == nil || baseData.StaticField == nil || baseData.StaticField.UserInfos == nil {
		return
	}
	// 鸿蒙版本不处理智能体
	if stcdefine.CLIENT_TYPE_HARMONY == baseData.StaticField.IntClientType {
		return
	}

	aiChatInfo := userUtil.MGetAiChatInfo(ctx, baseData.StaticField.UserInfos)
	if len(aiChatInfo) == 0 {
		return
	}

	// 处理主楼回复
	processPostByAiChatInfo(ctx, baseData, outData, aiChatInfo)

	// 处理楼中楼回复
	processSubPostListByAiChatInfo(ctx, baseData, outData, aiChatInfo)
}

func processPostByAiChatInfo(ctx context.Context, baseData *types.CPbFloorBaseData, outData *clientFloor.PbFloorRes, aiChatInfo map[int64]userUtil.AiChatInfo) {
	if outData.GetPost() != nil && outData.GetPost().GetAuthor() != nil {

		userID := outData.GetPost().GetAuthor().GetId()
		aiInfo, ok1 := aiChatInfo[userID]
		if ok1 && len(aiInfo.Scheme) > 0 {
			outData.Post.Author.IsNeedAutoAt = proto.Int32(1)
			if canJumpAiChatLandPage(baseData.StaticField.StrClientVersion, baseData.StaticField.IntClientType) {
				outData.Post.Author.TargetScheme = proto.String(aiInfo.Scheme)
				if outData.GetPost().GetAuthor().GetUserShowInfo() != nil && outData.GetPost().GetAuthor().GetUserShowInfo().GetFeedHead() != nil {
					outData.Post.Author.UserShowInfo.FeedHead.Schema = proto.String(aiInfo.Scheme)
				}
			}

			originalUserInfo, ok2 := baseData.StaticField.UserInfos[userID]
			if ok2 && originalUserInfo != nil {
				outData.Post.Author.Pa = originalUserInfo.Pa
				outData.Post.Author.Pendant = &client.Pendant{
					ImgUrl: common.GetStringPtr(agentPendant),
				}
				outData.Post.Author.Portrait = proto.String(tbportrait.Encode(userID, "", cast.ToInt64(originalUserInfo.GetPortraitTime())))
			}
			if outData.GetPost().GetFooter() == "" {
				outData.Post.Footer = proto.String("AI生成")
			}
		}

		// 回复内容
		if outData.GetPost().GetContent() != nil && len(outData.GetPost().GetContent()) > 0 {
			for _, c := range outData.GetPost().GetContent() {
				subUID := c.GetUid()
				subAiInfo, ok1 := aiChatInfo[subUID]
				if ok1 && len(subAiInfo.Scheme) > 0 {
					if canJumpAiChatLandPage(baseData.StaticField.StrClientVersion, baseData.StaticField.IntClientType) {
						c.TargetScheme = proto.String(subAiInfo.Scheme)
					}
					c.IsBot = proto.Int32(1)
				}
			}
		}
	}
}

func processSubPostListByAiChatInfo(ctx context.Context, baseData *types.CPbFloorBaseData, outData *clientFloor.PbFloorRes, aiChatInfo map[int64]userUtil.AiChatInfo) {
	if len(outData.GetSubpostList()) > 0 {
		for _, v := range outData.GetSubpostList() {
			if v.GetAuthor() == nil {
				continue
			}
			userID := v.GetAuthor().GetId()
			aiInfo, ok1 := aiChatInfo[userID]
			if ok1 && len(aiInfo.Scheme) > 0 {
				v.Author.IsNeedAutoAt = proto.Int32(1)
				v.Author.Pendant = &client.Pendant{
					ImgUrl: common.GetStringPtr(agentPendant),
				}
				if canJumpAiChatLandPage(baseData.StaticField.StrClientVersion, baseData.StaticField.IntClientType) {
					v.Author.TargetScheme = proto.String(aiInfo.Scheme)
					if v.GetAuthor().GetUserShowInfo() != nil && v.GetAuthor().GetUserShowInfo().GetFeedHead() != nil {
						v.Author.UserShowInfo.FeedHead.Schema = proto.String(aiInfo.Scheme)
					}
				}

				originalUserInfo, ok2 := baseData.StaticField.UserInfos[userID]
				if ok2 && originalUserInfo != nil {
					v.Author.Pa = originalUserInfo.Pa
					v.Author.Portrait = proto.String(tbportrait.Encode(userID, "", cast.ToInt64(originalUserInfo.GetPortraitTime())))
				}
				if v.GetFooter() == "" {
					v.Footer = proto.String("由AI生成")
				}
			}

			// 回复内容
			if v.GetContent() == nil || len(v.GetContent()) == 0 {
				continue
			}
			for _, c := range v.GetContent() {
				subUID := c.GetUid()
				subAiInfo, ok1 := aiChatInfo[subUID]
				if ok1 && len(subAiInfo.Scheme) > 0 {
					if canJumpAiChatLandPage(baseData.StaticField.StrClientVersion, baseData.StaticField.IntClientType) {
						c.TargetScheme = proto.String(subAiInfo.Scheme)
					}
					c.IsBot = proto.Int32(1)
				}
			}
		}
	}
}

func canJumpAiChatLandPage(clientVersion string, clientType int) bool {
	if stcdefine.CLIENT_TYPE_IPHONE == clientType && clientvers.CompareV2(clientVersion, ">=", "12.49.0") {
		return true
	}
	if stcdefine.CLIENT_TYPE_ANDROID == clientType && clientvers.CompareV2(clientVersion, ">=", "12.53.0") {
		return true
	}
	// 鸿蒙不跳转ai页
	if stcdefine.CLIENT_TYPE_HARMONY == clientType {
		return false
	}
	return false
}

// processHelper 处理小助手信息
func processHelper(baseData *types.CPbFloorBaseData, outData *clientFloor.PbFloorRes) {
	if baseData.StaticField == nil || baseData.StaticField.ForumInfo == nil {
		return
	}
	helperInfo := forum.GetHelperInfo(baseData.StaticField.ForumInfo)
	if helperInfo == nil {
		return
	}

	// 小助手标签信息
	tag := &client.TagsInfo{}
	if baseData.StaticField.IntClientType == stcdefine.CLIENT_TYPE_IPHONE {
		tag.ImgInfo = forum.GetHelperIosTagInfo()
	} else {
		tag.ImgInfo = forum.GetHelperAndroidTagInfo()
	}

	if outData.GetPost() != nil && outData.GetPost().GetAuthor() != nil && outData.GetPost().GetAuthor().GetId() == forum.GetHelperId() {
		outData.Post.Author.Name = proto.String(helperInfo.Name)
		outData.Post.Author.NameShow = proto.String(helperInfo.Name)
		outData.Post.Author.AvatarUrl = proto.String(helperInfo.Avatar)
		outData.Post.Author.TargetScheme = proto.String(forum.GetHelperScheme())
		outData.Post.Author.BawuType = proto.String(forum.GetHelperRoleName())
		outData.Post.Author.Tags = append(outData.Post.Author.Tags, tag)
	}
	if outData.GetSubpostList() == nil {
		return
	}
	for _, v := range outData.GetSubpostList() {
		if v.GetAuthor() != nil && v.GetAuthor().GetId() == forum.GetHelperId() {
			v.Author.Name = proto.String(helperInfo.Name)
			v.Author.NameShow = proto.String(helperInfo.Name)
			v.Author.AvatarUrl = proto.String(helperInfo.Avatar)
			v.Author.TargetScheme = proto.String(forum.GetHelperScheme())
			v.Author.BawuType = proto.String(forum.GetHelperRoleName())
			v.Author.Tags = append(v.Author.Tags, tag)
		}
		if v.GetContent() == nil {
			continue
		}
		for _, c := range v.GetContent() {
			if c.GetUid() == forum.GetHelperId() {
				c.Text = proto.String(helperInfo.Name)
				c.TargetScheme = proto.String(forum.GetHelperScheme())
			}
		}
	}
}

func processFloor(ctx context.Context, baseData *types.CPbFloorBaseData) {
	staticField := baseData.StaticField

	var (
		majorSwitch  = 0
		beyondTime   = 180
		amountSwitch = 0
		amountHours  = 259200
		flowSwitch   = 1
		userIds      = make([]int64, 0)
		flowUserIds  = make([]int64, 0)
	)
	keys, values, err := wordserver.GetTableContents(ctx, "tb_wordlist_redis_tb_ueg_major_config_info", 0, -1)
	if err != nil {
		tbcontext.WarningF(ctx, " get tb_wordlist_redis_tb_ueg_major_config_info fail: %v", err)
	} else {
		kvMap := make(map[string]string)
		for i, key := range keys {
			kvMap[key] = values[i]
		}

		// 0 默认关闭 回复贴主主开关
		majorSwitch = common.Tvttt(kvMap["reply_major_switch"], common.TTT_INT).(int)
		// 默认3分钟
		beyondTime = common.Tvttt(kvMap["beyond_time"], common.TTT_INT).(int)
		// 回复贴二级开关
		amountSwitch = common.Tvttt(kvMap["amount_switch_post"], common.TTT_INT).(int)
		// 区分增量或者存量时间值 默认是3天
		amountHours = common.Tvttt(kvMap["amount_period"], common.TTT_INT).(int)
		// 流量开关总开关 默认开启流量开关
		flowSwitch = common.Tvttt(kvMap["flow_switch"], common.TTT_INT).(int)
		// 指定用户userId集合
		userIdsInter, err := php.Unserialize([]byte(kvMap["user_ids"]))
		if err != nil {
			tbcontext.WarningF(ctx, "php unserialize user_ids fail: %v", err)
		} else {
			err = common.StructAToStructBCtx(ctx, userIdsInter, &userIds)
			if err != nil {
				tbcontext.WarningF(ctx, "convert user_ids to []int64 failed: %v", err)
			}
		}

		// 用户尾数流量集合
		flowUserIdsInter, err := php.Unserialize([]byte(kvMap["flow_user_ids"]))
		if err != nil {
			tbcontext.WarningF(ctx, "php unserialize flow_user_ids fail: %v", err)
		} else {
			err = common.StructAToStructBCtx(ctx, flowUserIdsInter, &flowUserIds)
			if err != nil {
				tbcontext.WarningF(ctx, "convert flow_user_ids to []int64 failed: %v", err)
			}
		}
	}

	for _, arrComment := range staticField.Comments.GetPostInfos() {
		newComment := new(client.SubPostList)
		newComment.Id = proto.Uint64(arrComment.GetPostId())
		newComment.Title = proto.String("")
		newComment.Floor = proto.Uint32(arrComment.GetPostNo())
		newComment.Time = proto.Uint32(arrComment.GetNowTime())
		newComment.IsGiftpost = proto.Int32(int32(arrComment.GetIsGiftpost()))

		if arrComment.GetAgreeNum() < 0 {
			arrComment.AgreeNum = proto.Int64(0)
		}
		if arrComment.GetDisagreeNum() < 0 {
			arrComment.DisagreeNum = proto.Int64(0)
		}

		newComment.Agree = &client.Agree{
			AgreeNum:     proto.Int64(arrComment.GetAgreeNum()),
			DisagreeNum:  proto.Int64(arrComment.GetDisagreeNum()),
			DiffAgreeNum: proto.Int64(arrComment.GetAgreeNum() - arrComment.GetDisagreeNum()),
			HasAgree:     proto.Int32(types.NotAgree),
			AgreeType:    proto.Int32(0),
		}
		agreeData := staticField.SubPostAgreeData[int64(arrComment.GetPostId())]
		if agreeData != nil {
			newComment.Agree.HasAgree = proto.Int32(types.HasAgree)
			newComment.Agree.AgreeType = proto.Int32(int32(agreeData.GetAgreeType()))
		}

		newComment.Location = new(client.Lbs)
		common.StructAToStructBCtx(ctx, arrComment.GetLocation(), newComment.Location)

		newComment.Author = &client.User{
			Id:       proto.Int64(arrComment.GetUserId()),
			Name:     proto.String(arrComment.GetUsername()),
			Portrait: proto.String(tbportrait.Encode(arrComment.GetUserId(), arrComment.GetUsername(), 0)),
			// OuterId:
		}

		// 是否命中流量信号
		flowSign := 0
		if flowSwitch == 1 {
			if arrComment.GetUserId() != 0 && (php2go.InArray(arrComment.GetUserId(), userIds) || php2go.InArray(arrComment.GetUserId()%10, flowUserIds)) {
				flowSign = 1
			}
		} else {
			flowSign = 2
		}

		authorViewStatus := 0
		// 1表示命中用户id 2表示全量使用新增主态逻辑
		if flowSign == 1 || flowSign == 2 {
			floorTimeRet := time.Now().Unix() - int64(arrComment.GetNowTime())
			if arrComment.GetIsAuthorView() == 1 && majorSwitch == 1 &&
				floorTimeRet >= int64(beyondTime) && arrComment.AfterVisibleAudit != nil && arrComment.GetAfterVisibleAudit() == 0 {
				// 如果未返回after_visible_createtime字段，帖子是存量
				if arrComment.GetAfterVisibleCreatetime() == 0 {
					// 存量开关开，文案显示，存量开关关，文案不显示
					if amountSwitch > 0 {
						authorViewStatus = 1
					}
				} else {
					if arrComment.GetAfterVisibleCreatetime()-int64(arrComment.GetNowTime()) > int64(amountHours) {
						// 存量
						if amountSwitch > 0 {
							authorViewStatus = 1
						}
					} else {
						// 增量
						authorViewStatus = 1
					}
				}
			}
		}

		// app端显示文案提示逻辑 1 显示 0不显示
		newComment.IsAuthorView = proto.Int32(int32(authorViewStatus))

		// 删掉经纬度信息
		if newComment.GetLocation().GetLat() != "" {
			newComment.GetLocation().Lat = nil
		}
		if newComment.GetLocation().GetLng() != "" {
			newComment.GetLocation().Lng = nil
		}

		contents := arrComment.GetContent().([]*meta.PbContent)
		postPlugInfo := make(map[string]*adsense.DecodePluginToken)
		for _, content := range contents {
			if content.GetType() == tbrichtext.SLOT_TYPE_TPC && staticField.TiebaPlusPlugInfo[content.GetText()] != nil {
				postPlugInfo[content.GetText()] = staticField.TiebaPlusPlugInfo[content.GetText()]
			}
		}

		clientContent := make([]*client.PbContent, 0)
		common.StructAToStructBCtx(ctx, contents, &clientContent)
		newComment.Content = clientContent
		if len(postPlugInfo) > 0 {
			objParser := &tbrichtext.ParserStructured{}
			objParser.SetClientVersion(staticField.StrClientVersion)
			objParser.SetClientType(staticField.IntClientType)
			objParser.SetFid(int64(staticField.ForumInfo.GetForumName().GetForumId()))
			newComment.Content = objParser.ProcessTiebaplusComponent(ctx, clientContent, postPlugInfo, 0)
		}

		// 端外回流切词 - 楼中楼
		if len(baseData.StaticField.HitWordMap) > 0 {
			newComment.Content = util.ContentReStruct(newComment.Content, staticField.HitWordMap, staticField)
		}

		staticField.SubPostList = append(staticField.SubPostList, newComment)

	}

	totalPage := int32(math.Ceil(float64(staticField.Comments.GetValidPostNum()) / types.FloorPageSize))
	currentPage := int32(staticField.IntPn)
	if currentPage > totalPage {
		currentPage = totalPage
	}
	if staticField.IsCommReverse == 1 {
		currentPage = totalPage
	}

	staticField.Page = &client.Page{
		PageSize:    proto.Int32(types.FloorPageSize),
		CurrentPage: proto.Int32(currentPage),
		TotalCount:  proto.Int32(int32(staticField.Comments.GetValidPostNum())),
		TotalPage:   proto.Int32(totalPage),
	}

	// todo 后续处理
	// contents := staticField.PostList.GetContent().([]*meta.PbContent)
	// postPlugInfo := make(map[string]*adsense.DecodePluginToken)
	// for _, content := range contents {
	//	if content.GetType() == tbrichtext.SLOT_TYPE_TPC && staticField.TiebaPlusPlugInfo[content.GetText()] != nil {
	//		postPlugInfo[content.GetText()] = staticField.TiebaPlusPlugInfo[content.GetText()]
	//	}
	// }
	//
	// if len(postPlugInfo) > 0 {
	//	clientContent := make([]*client.PbContent, 0)
	//	utilCommon.StructAToStructBCtx(ctx,  contents, &clientContent)
	//	objParser := &tbrichtext.ParserStructured{}
	//	clientContent = objParser.ProcessTiebaplusComponent(ctx, clientContent, postPlugInfo, 0)
	// }

}

func processReplyPrivateFlag(ctx context.Context, baseData *types.CPbFloorBaseData) {
	staticField := baseData.StaticField

	staticField.IntReplyPrivateFlag = 1
	lzUid := staticField.PostInfo.GetFirstPostUserid()
	readOnly := staticField.ThreadInfo.GetReadonly()
	replyPrivateInfo := int32(0)
	if staticField.UserInfos[lzUid] != nil {
		replyPrivateInfo = staticField.UserInfos[lzUid].GetPrivSets().GetReply()
	}

	isMember := false
	hasShield := false
	vipInfo := staticField.UserInfos[lzUid].GetVipInfo()
	if vipInfo != nil && vipInfo.GetVStatus() > 1 && int64(vipInfo.GetETime()) > time.Now().Unix() {
		isMember = true
	}

	// >=12.26 判断帖子是否被只读处置,客态不允许发帖
	if clientvers.IsLegalVersion("12_26_0", staticField.IntClientType, staticField.StrClientVersion) &&
		staticField.UserId != lzUid && readOnly == 1 {
		staticField.IntReplyPrivateFlag = 5
		return
	}

	if replyPrivateInfo == 1 || replyPrivateInfo == 2 {
		return
	}

	// v12.26新增only me 仅自己可回复 逻辑 加上版本控制>=12.25.0
	if types.ReplyPrivateOnlyMe == replyPrivateInfo &&
		clientvers.IsLegalVersion("12_25_0", staticField.IntClientType, staticField.StrClientVersion) && staticField.UserId != lzUid {
		staticField.IntReplyPrivateFlag = 4
		return
	}

	// 同楼中楼线上逻辑，只处理了5和6，之前判断$intFromUser 和 $intToUser，实际非5和6时未定义，则提前； 但是没对齐pb逻辑，pb还有很多逻辑，会对非5和6如7做判断 12.26新增非跟版需求，前面一行处理4
	if staticField.UserId == lzUid || (replyPrivateInfo != types.ReplyPrivateOnlyFans && replyPrivateInfo != types.ReplyPrivateOnlyConcern) {
		return
	}

	var fromUser, toUser int64
	if replyPrivateInfo == types.ReplyPrivateOnlyFans {
		fromUser = staticField.UserId
		toUser = lzUid
	} else {
		fromUser = lzUid
		toUser = staticField.UserId
	}

	if fromUser == 0 || toUser == 0 {
		return
	}

	input := map[string]interface{}{
		"user_id":     fromUser,
		"req_user_id": []int64{toUser},
	}

	res := new(user.GetUserFollowInfoRes)
	err := tbservice.Call(ctx, "user", "getUserFollowInfo", input, res,
		tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service user:getUserFollowInfo, err = %v, input = %v, output = %v", err, input, res)
		return
	}

	if len(res.GetResUserInfos()) > 0 && res.GetResUserInfos()[0].GetIsFollowed() != 1 {
		switch replyPrivateInfo {
		case types.ReplyPrivateOnlyFans:
			staticField.IntReplyPrivateFlag = 2
		case types.ReplyPrivateOnlyConcern:
			staticField.IntReplyPrivateFlag = 3
		default:
			staticField.IntReplyPrivateFlag = 1
		}
	}

	if isMember {
		shieldInput := &common2.MgetShieldSwitchByUIDsReq{
			UserIds: []uint64{uint64(lzUid)},
		}
		shieldRes := new(common2.MgetShieldSwitchByUIDsRes)

		err := tbservice.Call(ctx, "common", "mgetShieldSwitchByUIDs", shieldInput, shieldRes,
			tbservice.WithConverter(tbservice.JSONITER), tbservice.WithRalName("common_go"))
		if err != nil || shieldRes.Errno == nil || shieldRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service user:mgetShieldSwitchByUIDs, err = %v, input = %v, output = %v", err, shieldInput, shieldRes)
			return
		}
		data := shieldRes.GetData()
		if data != nil {
			if status, ok := data.GetInfo()[uint64(lzUid)]; ok && status.GetShieldStatus() == 1 {
				hasShield = true
			}
		}
	}

	if staticField.IntReplyPrivateFlag == 3 && hasShield {
		staticField.IntReplyPrivateFlag = 9
	}
}

func processABTest(ctx context.Context, baseData *types.CPbFloorBaseData) {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	cuid := common.Tvttt(objReq.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
	compareVersion := "10.0"

	if staticField == nil {
		return
	}
	// 鸿蒙直接返回，ShowSquared为false
	if stcdefine.CLIENT_TYPE_ANDROID == staticField.IntClientType {
		compareVersion = clientvers.VERSION_ANDROID_10_1
	} else if stcdefine.CLIENT_TYPE_IPHONE == staticField.IntClientType {
		compareVersion = clientvers.VERSION_IOS_10_1
	} else if stcdefine.CLIENT_TYPE_HARMONY == staticField.IntClientType {
		return
	}

	if clientvers.Compare(compareVersion, staticField.StrClientVersion) >= 0 {
		abtestObj := newabtest.GetNewAbTest(ctx)
		abtestData := abtestObj.GetAbtestResult(staticField.IntClientType, staticField.StrClientVersion, cuid, staticField.UserId)

		if abtestData[types.AbtestIdPbFoldSmallFlow] == "b" {
			staticField.ShowSquared = true
		}
	}

}

// todo 以后看看
func getCupid(ctx context.Context, baseData *types.CPbFloorBaseData) {
	staticField := baseData.StaticField

	res := map[string]bool{
		"has_in_photo_black_list": false,
		"album_forum":             false,
		"ban_pic_topic":           false,
		"no_post_pic":             false,
		"album_good_smallflow":    false,
		"is_album_post":           false,
		"forever_ban":             false,
		"brand_zone":              false,
		"has_paper":               false,
	}

	input := map[string]interface{}{
		"forum_id":   staticField.ForumInfo.GetForumName().GetForumId(),
		"forum_name": encode.Utf8ToGbkString(staticField.ForumInfo.GetForumName().GetForumName()),
		"type":       "frs",
	}

	output := cupid.Query(ctx, input)
	for key := range output {
		switch key {
		case types.CupidPhotoBlackList:
			res["has_in_photo_black_list"] = true
		case types.CupidAlbumPhoto:
			res["album_forum"] = true
		case types.CupidBanPicTopic:
			res["ban_pic_topic"] = true
		case types.CupidBanPostPic:
			res["no_post_pic"] = true
		case types.CupidDefaultPhotoFrs:
			res["album_good_smallflow"] = true
		case types.CupidPhotoPbSwitch:
			res["is_album_post"] = true
		case types.CupidForeverBanPower:
			res["forever_ban"] = true
		case types.CupidBrandZone:
			res["brand_zone"] = true
		case types.CupidRulePaper:
			res["has_paper"] = true
		}
	}

	// arrForumRes := new(client.SimpleForum)
	// if res["has_in_photo_black_list"] {
	//	arrForumRes.ban = true
	// }
	//
	// arrForumRes
	// staticField.Cupid = arrForumRes
}

func buildSubPost(ctx context.Context, baseData *types.CPbFloorBaseData) []*client.SubPostList {
	staticField := baseData.StaticField

	if staticField != nil && staticField.SubPostList != nil && staticField.StrClientVersion != "" &&
		clientvers.CompareV2(staticField.StrClientVersion, ">=", "12.76") {
		DealSubPostByHitWord(ctx, staticField)
	}
	for subPostPos, subPost := range staticField.SubPostList {
		if staticField.IntClientType == stcdefine.CLIENT_TYPE_IPHONE && len(subPost.GetContent()) > 0 {
			for _, content := range subPost.GetContent() {
				if content.GetText() != "" {
					text := content.GetText()
					for k, v := range conf.EmojiNames {
						text = strings.Replace(text, v, k, -1)
					}
					content.Text = proto.String(text)
				}
			}
		}

		userInfo := staticField.UserInfos[subPost.GetAuthor().GetId()]
		if userInfo != nil {
			clientUserInfo := getClientUserInfo(ctx, userInfo, staticField, "", false)
			format.FormatClientUser(clientUserInfo)
			subPost.Author = &client.User{
				Id:               proto.Int64(clientUserInfo.GetId()),
				Name:             proto.String(clientUserInfo.GetName()),
				NameShow:         proto.String(clientUserInfo.GetNameShow()),
				Portrait:         proto.String(clientUserInfo.GetPortrait()),
				Type:             proto.Int32(clientUserInfo.GetType()),
				LevelId:          proto.Int32(clientUserInfo.GetLevelId()),
				IsLike:           proto.Int32(clientUserInfo.GetIsLike()),
				TshowIcon:        clientUserInfo.GetTshowIcon(),
				NewTshowIcon:     clientUserInfo.GetNewTshowIcon(),
				Iconinfo:         clientUserInfo.GetIconinfo(),
				IsBawu:           proto.Int32(clientUserInfo.GetIsBawu()),
				PrivSets:         clientUserInfo.GetPrivSets(),
				BawuType:         proto.String(clientUserInfo.GetBawuType()),
				Gender:           proto.Int32(clientUserInfo.GetGender()),
				IsMem:            proto.Int32(clientUserInfo.GetIsMem()),
				TbVip:            clientUserInfo.GetTbVip(),
				GodData:          clientUserInfo.GetGodData(),
				SealPrefix:       proto.String(clientUserInfo.GetSealPrefix()),
				AlaInfo:          clientUserInfo.GetAlaInfo(),
				Uk:               proto.String(clientUserInfo.GetUk()),
				NewGodData:       clientUserInfo.GetNewGodData(),
				DisplayAuthType:  proto.Int32(clientUserInfo.GetDisplayAuthType()),
				WorkcreationData: clientUserInfo.WorkcreationData,
				BazhuGrade:       clientUserInfo.GetBazhuGrade(),
				LevelName:        proto.String(clientUserInfo.GetLevelName()),
				BaijiahaoInfo:    clientUserInfo.BaijiahaoInfo,
				ShowIconList:     clientUserInfo.GetShowIconList(),
				UserShowInfo:     clientUserInfo.GetUserShowInfo(),
			}

			// 添加用户头像挂件
			subPost.Author.Pendant = props.GetPendant(ctx, userInfo, staticField.IntClientType, props.PENDANT_LOCATION_PB)
			// 官方号/商户号标识
			if userInfo.GetBusinessAccount().GetStatus() == 1 {
				subPost.Author.BusinessAccountInfo = &client.BusinessAccountInfo{
					IsBusinessAccount:      proto.Int32(1),
					IsForumBusinessAccount: proto.Int32(0),
				}

				if php2go.InArray(int64(staticField.ForumInfo.GetForumName().GetForumId()), userInfo.GetBusinessAccount().GetFid()) {
					subPost.Author.BusinessAccountInfo.IsForumBusinessAccount = proto.Int32(1)
				}
			}
		}

		// 特殊处理下礼物贴的图片返回类型 1改成3
		if subPost.GetIsGiftpost() == 1 {
			for _, content := range subPost.GetContent() {
				if content.GetType() == tbrichtext.SLOT_TYPE_LINK {
					content.Type = proto.Uint32(tbrichtext.SLOT_TYPE_IMG)
					content.Src = proto.String(content.GetText())
				}
			}
		}

		getSubPostPanContent(ctx, subPost, subPostPos, staticField)

		getChatroomInfo(ctx, subPost, staticField)

		processShopLink(ctx, subPost, staticField)
		//视频资源处理
		if staticField.IntClientType == stcdefine.CLIENT_TYPE_ANDROID && clientvers.CompareV2(staticField.StrClientVersion, ">=", "12.85") {
			videoHotLink(ctx, subPost, staticField)
		}
	}
	return staticField.SubPostList
}

func buildThread(ctx context.Context, baseData *types.CPbFloorBaseData) *client.ThreadInfo {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	arrThread := &client.ThreadInfo{
		Id:         proto.Int64(int64(staticField.PostInfo.GetThreadId())),
		Title:      proto.String(strings.Replace(staticField.PostList.GetTitle(), "回复：", "", 1)),
		ReplyNum:   proto.Int32(int32(staticField.PostInfo.GetTotalPostNum())),
		CommentNum: proto.Int32(0),
		ThreadType: proto.Int32(staticField.PostInfo.GetThreadType()),
		Fid:        proto.Int64(int64(staticField.ForumInfo.GetForumName().GetForumId())),
		Fname:      proto.String(html.UnescapeString(staticField.ForumInfo.GetForumName().GetForumName())),
		RepostNum:  proto.Int32(0),
		Topic: &client.Topic{
			IsLpost:   proto.Int32(0),
			TopicType: proto.Int32(0),
		},
		CollectStatus:  proto.Int32(0),
		CollectMarkPid: proto.String("0"),
		ValidPostNum:   proto.Uint32(staticField.PostInfo.GetValidPostNum()),
		IsLzDeleteAll:  proto.Uint32(0),
		IsAd:           proto.Uint32(0),
		Ecom:           proto.String(""),
		Pids:           proto.String(common.Tvttt(staticField.PostList.GetPostId(), common.TTT_STRING).(string) + ","),
	}

	if staticField.IsRepostThread {
		arrThread.IsMultiforumThread = proto.Int32(1)
	} else {
		arrThread.IsMultiforumThread = proto.Int32(0)
	}

	if staticField.TWZhiInfo[int64(staticField.PostInfo.GetThreadId())] != nil {
		arrThread.TwzhiboInfo = staticField.TWZhiInfo[int64(staticField.PostInfo.GetThreadId())]
	}

	videoInfo, _ := staticField.ThreadInfo.GetVideoInfoStruct(ctx)
	if videoInfo != nil {
		strModel := common.TransforValueToTargetType(objReq.GetCommonAttr("model", ""), common.TTT_STRING).(string)
		sampleId := common.TransforValueToTargetType(objReq.GetCommonAttr("sample_id", ""), common.TTT_STRING).(string)
		osVersion := common.TransforValueToTargetType(objReq.GetCommonAttr("os_version", ""), common.TTT_STRING).(string)

		videoProcess := video.CreateTbVideoProcessObj(ctx, video.TbVideoExtParam{
			ClientType:    staticField.IntClientType,
			ClientVersion: staticField.StrClientVersion,
			Uid:           staticField.UserId,
			Model:         strModel,
			OsVersion:     osVersion,
			SampleId:      sampleId,
		})

		videoProcess.Execute(videoInfo, video.REQUEST_FROM_CLIENT)
		arrThread.VideoInfo = new(client.VideoInfo)
		common.StructAToStructBCtx(ctx, videoInfo, arrThread.VideoInfo)
	} else if staticField.ThreadInfo.GetMedia() != nil {
		arrThread.Media = make([]*client.Media, 0)
		for _, arrItem := range staticField.ThreadInfo.GetMedia() {
			arrSpec := &imgcdn.ProcPic{
				ScreenWidth:   common.Tvttt(objReq.GetPrivateAttr("scr_w", 0), common.TTT_INT32).(int32),
				ScreenHeigh:   common.Tvttt(objReq.GetPrivateAttr("scr_h", 0), common.TTT_INT32).(int32),
				ScreenDip:     common.Tvttt(objReq.GetPrivateAttr("scr_dip", 0), common.TTT_FLOAT64).(float64),
				QType:         common.Tvttt(objReq.GetPrivateAttr("q_type", 0), common.TTT_INT32).(int32),
				UseCdnUrl:     true,
				ClientVersion: staticField.StrClientVersion,
				ClientType:    staticField.IntClientType,
			}
			if staticField.IntClientType == clientvers.CLIENT_TYPE_IPHONE {
				arrSpec.PicStrategyType = imgcdn.PB_PIC_STRATEGY_IOS_910
			} else {
				arrSpec.PicStrategyType = imgcdn.PB_PIC_STRATEGY_ANDROID_910
			}
			arrSlot := new(client.Media)
			arrSlot.WaterPic = proto.String(arrItem.GetWaterPic())
			arrSlot.SmallPic = proto.String(arrItem.GetSmallPic())
			arrSlot.BigPic = proto.String(arrItem.GetBigPic())
			if arrItem.Vpic != nil {
				arrSlot.Vpic = proto.String(arrItem.GetVpic())
			}
			if arrItem.Vsrc != nil {
				arrSlot.Vsrc = proto.String(arrItem.GetVsrc())
			}
			if arrItem.Vhsrc != nil {
				arrSlot.Vhsrc = proto.String(arrItem.GetVhsrc())
			}
			arrSlot.Width = proto.Uint32(common.Tvttt(arrItem.GetWidth(), common.TTT_UINT32).(uint32))
			arrSlot.Height = proto.Uint32(common.Tvttt(arrItem.GetHeight(), common.TTT_UINT32).(uint32))
			if arrItem.PostId != nil {
				arrSlot.PostId = proto.Int64(common.Tvttt(arrItem.GetPostId(), common.TTT_INT64).(int64))
			}
			if arrItem.GetType() == "pic" {
				arrSlot.Type = proto.Int32(tbrichtext.SLOT_TYPE_IMG)
			} else if arrItem.GetType() == "abstract" {
				arrSlot.Type = proto.Int32(tbrichtext.SLOT_TYPE_TEXT)
			} else if arrItem.GetType() == "flash" {
				arrSlot.Type = proto.Int32(tbrichtext.SLOT_TYPE_EMBED)
			} else if arrItem.GetType() == "music" {
				arrSlot.Type = proto.Int32(tbrichtext.SLOT_TYPE_MUSIC)
			}
			arrSlot.OriginSize = proto.Uint32(common.Tvttt(arrItem.GetWidth(), common.TTT_UINT32).(uint32))
			arrSlot.Bsize = proto.String(fmt.Sprintf("%s,%s", arrItem.GetWidth(), arrItem.GetHeight()))

			if arrSlot.GetWidth() >= arrSlot.GetHeight() &&
				(staticField.IntClientType != clientvers.CLIENT_TYPE_ANDROID || clientvers.Compare(staticField.StrClientVersion, "12.36.0") < 0) {
				arrSpec.NeedSampleBigPic = true
			}
			imgcdn.PbProcNewPicUrlByMedia(arrSlot, arrItem.GetBigPic(), arrSpec)

			arrThread.Media = append(arrThread.Media, arrSlot)
		}
	}

	if staticField.ThreadStore != nil {
		arrThread.CollectStatus = proto.Int32(int32(staticField.ThreadStore.GetReply().GetKeptType()))
		arrThread.CollectMarkPid = proto.String(common.Tvttt(staticField.ThreadStore.GetReply().GetMarkPid(), common.TTT_STRING).(string))
	}

	uid := staticField.PostInfo.GetFirstPostUserid()
	userInfo := staticField.UserInfos[uid]
	if userInfo != nil {
		clientUserInfo := getClientUserInfo(ctx, userInfo, staticField, "", true)
		format.FormatClientUser(clientUserInfo)
		arrThread.Author = &client.User{
			Id:              proto.Int64(clientUserInfo.GetId()),
			Name:            proto.String(clientUserInfo.GetName()),
			NameShow:        proto.String(clientUserInfo.GetNameShow()),
			Portrait:        proto.String(clientUserInfo.GetPortrait()),
			Type:            proto.Int32(clientUserInfo.GetType()),
			LevelId:         proto.Int32(clientUserInfo.GetLevelId()),
			IsLike:          proto.Int32(clientUserInfo.GetIsLike()),
			TshowIcon:       clientUserInfo.GetTshowIcon(),
			NewTshowIcon:    clientUserInfo.GetNewTshowIcon(),
			IsMem:           proto.Int32(clientUserInfo.GetIsMem()),
			AlaInfo:         clientUserInfo.GetAlaInfo(),
			Uk:              proto.String(clientUserInfo.GetUk()),
			NewGodData:      clientUserInfo.GetNewGodData(),
			DisplayAuthType: proto.Int32(clientUserInfo.GetDisplayAuthType()),
			LevelName:       proto.String(clientUserInfo.GetLevelName()),
			BaijiahaoInfo:   clientUserInfo.BaijiahaoInfo,
		}

		if arrThread.GetAuthor().GetTshowIcon() == nil {
			arrThread.GetAuthor().TshowIcon = make([]*client.TshowInfo, 0)
		}

		if arrThread.GetAuthor().GetNewTshowIcon() == nil {
			arrThread.GetAuthor().NewTshowIcon = make([]*client.TshowInfo, 0)
		}
	}

	return arrThread
}

func buildForum(ctx context.Context, baseData *types.CPbFloorBaseData) *client.SimpleForum {
	staticField := baseData.StaticField
	staticField.Bawulist.GetManager()

	arrForum := &client.SimpleForum{
		Id:          proto.Int64(int64(staticField.ForumInfo.GetForumName().GetForumId())),
		Name:        proto.String(staticField.ForumInfo.GetForumName().GetForumName()),
		IsExists:    proto.Int32(staticField.ForumInfo.GetForumName().GetExist()),
		FirstClass:  proto.String(staticField.ForumInfo.GetDir().GetLevel_1Name()),
		SecondClass: proto.String(staticField.ForumInfo.GetDir().GetLevel_2Name()),
		// IsLiked:     proto.Uint32(staticField.Perm.GetIsLike(ctx)),
	}

	// 是否为品牌吧，1为是，2为过期
	if specialForum, ok := staticField.ForumInfo.GetAttrs().GetSpecialForum().(map[string]interface{}); ok {
		isBrandForum := common.GetMapValueByPathInt32Default(specialForum, "is_brand_forum", 0)
		arrForum.IsBrandForum = proto.Int32(isBrandForum)
	} else {
		arrForum.IsBrandForum = proto.Int32(0)
	}

	return arrForum
}

func buildDisplayForum(ctx context.Context, baseData *types.CPbFloorBaseData, forumInfo *client.SimpleForum) *client.SimpleForum {
	staticField := baseData.StaticField
	if clientvers.IsNoForum(staticField.ForumInfo.GetForumName().GetForumId(), staticField.ForumInfo.GetForumName().GetForumName()) {
		return nil
	}

	displayForum := proto.Clone(forumInfo).(*client.SimpleForum)
	displayForum.Avatar = proto.String("")
	displayForum.IsLiked = proto.Uint32(0)
	displayForum.IsSigned = proto.Uint32(0)
	displayForum.Ext = proto.String("")
	displayForum.LevelId = proto.Uint32(0)
	return displayForum
}

func getClientUserInfo(ctx context.Context, userInfo *user.UserInfo, staticField *types.PbFloorStaticField, strIp string, extra bool) *client.User {
	if userInfo.GetUserId() == 0 {
		return &client.User{
			Name:     proto.String(strIp),
			Id:       proto.Int64(0),
			Portrait: proto.String(tbportrait.Encode(0, strIp, 0)),
			NameShow: proto.String(strIp),
			IsLike:   proto.Int32(0),
			Type:     proto.Int32(0),
		}
	}
	nameShow := userInfo.GetUserName()
	if userInfo.GetUserNickname() != "" {
		nameShow = userInfo.GetUserNickname()
	}
	nnParam := &usertool.NickNameByVersion{
		UserNickNameV2: userInfo.GetUserNicknameV2(),
		DisplayName:    userInfo.GetDisplayName(),
	}
	nameShow = usertool.GetUserNickNameByVersion(staticField.IntClientType, staticField.StrClientVersion, nnParam, nameShow)

	var tbVip *client.TbVipInfo
	if userInfo.GetTbVip() != "" && userInfo.GetTbVip() != nil {
		tbVip = new(client.TbVipInfo)
		err := common.StructAToStructBCtx(ctx, userInfo.GetTbVip(), tbVip)
		if err != nil {
			tbcontext.WarningF(ctx, "change TbVip fail: %+v", err)
		}
	}

	arrResult := &client.User{
		Name:     proto.String(userInfo.GetUserName()),
		Id:       proto.Int64(userInfo.GetUserId()),
		Portrait: proto.String(tbportrait.Encode(userInfo.GetUserId(), userInfo.GetUserName(), time.Now().Unix())),
		NameShow: proto.String(nameShow),
		TbVip:    tbVip,
	}

	arrResult.LevelId = proto.Int32(int32(userInfo.GetLevelId()))
	arrResult.LevelName = proto.String(userInfo.GetLevelName())
	arrResult.IsLike = proto.Int32(int32(userInfo.GetIsLike()))
	arrResult.DisplayAuthType = proto.Int32(int32(userInfo.GetDisplayAuthType()))
	arrResult.BazhuGrade, _ = userInfo.GetBazhuGradeStruct()
	arrResult.WorkCreatorInfo, _ = userInfo.GetWorkCreatorInfoStruct()
	arrResult.IsSelectTail = proto.Int32(int32(userInfo.GetUserTailInfo(ctx)))
	if userInfo.GetUserType() > 0 {
		arrResult.Type = proto.Int32(2)
	} else {
		arrResult.Type = proto.Int32(1)
	}
	arrResult.TshowIcon = forum.GetTshowIconAndMemberIcon(ctx, userInfo, staticField.ForumInfo.GetForumName().GetForumId())
	arrResult.NewTshowIcon = GetMemberPkInfo(ctx, userInfo, &protoForum.GetBtxInfoByNameRes{
		ForumId: &protoForum.ForumIdOut{
			ForumId: proto.Uint32(staticField.ForumInfo.GetForumName().GetForumId()),
		},
		Attrs: staticField.ForumInfo.GetAttrs(),
	}, staticField.UserId, staticField.StrClientVersion)
	arrResult.Iconinfo = buildIconInfo(ctx, userInfo.GetNewIconinfo())
	arrResult.Gender = proto.Int32(int32(userInfo.GetUserSex()))
	arrResult.SealPrefix = proto.String(props.SealPrefix(ctx, userInfo))

	mParrProps := userInfo.GetMParrProps()
	var propsId, endTime int32
	var picUrl string
	if mParrProps != nil && mParrProps.GetLevel() != nil && int64(mParrProps.GetLevel().GetEndTime()) >= time.Now().Unix() {
		propsId = int32(mParrProps.GetLevel().GetPropsId())
		endTime = int32(mParrProps.GetLevel().GetEndTime())
		picUrl = mParrProps.GetLevel().GetPicUrl()
	}

	arrResult.IsMem = proto.Int32(propsId)
	arrResult.PayMemberInfo = &client.PayMemberInfo{
		PropsId: proto.Int32(propsId),
		EndTime: proto.Int32(endTime),
		PicUrl:  proto.String(picUrl),
	}

	if userInfo.GetUserType() > 0 {
		arrResult.UserType = proto.Int32(2)
		arrResult.IsVerify = proto.Int32(1)
	} else {
		arrResult.UserType = proto.Int32(1)
		arrResult.IsVerify = proto.Int32(0)
	}

	alaInfo := new(client.AlaUserInfo)
	if userInfo.GetAlaInfo() != "" {
		err := common.StructAToStructBCtx(ctx, userInfo.GetAlaInfo(), alaInfo)
		if err != nil {
			tbcontext.WarningF(ctx, "get alainfo fail: %v", err)
		}
	}
	arrResult.AlaInfo = alaInfo
	if userInfo.GetPrivSets() != nil && userInfo.GetPrivSets().Live != nil && userInfo.GetPrivSets().GetLive() != 1 {
		arrResult.AlaInfo.LiveStatus = nil
		arrResult.AlaInfo.LiveId = nil
		arrResult.AlaInfo.AnchorLive = nil
		arrResult.AlaInfo.EnterLive = nil
	}

	if staticField.ArrUserUK[userInfo.GetUserId()] != "" {
		arrResult.Uk = proto.String(staticField.ArrUserUK[userInfo.GetUserId()])
	}

	var privSets *client.PrivSets
	if userInfo.GetPrivSets() != nil {
		privSets = new(client.PrivSets)
		err := common.StructAToStructBCtx(ctx, userInfo.GetPrivSets(), privSets)
		if err != nil {
			tbcontext.WarningF(ctx, "change PrivSets fail: %+v", err)
		}
	}
	arrResult.PrivSets = privSets
	arrResult.SpringVirtualUser = getSpringVirtualUser(ctx, userInfo)

	clientGodInfo, _ := userInfo.GetClientGodInfoStruct()
	godInfo, _ := userInfo.GetGodInfoStruct()
	fieldInt, _ := strconv.Atoi(godInfo.GetField())
	if clientGodInfo == nil {
		arrResult.NewGodData = &client.NewGodInfo{
			FieldId: proto.Uint32(0),
		}
	} else {
		clientGodInfo.FieldId = proto.Uint32(uint32(fieldInt))
		arrResult.NewGodData = clientGodInfo
	}

	if staticField.LevelBlackList[userInfo.GetUserId()] == "1" {
		arrResult.LevelId = nil
		arrResult.LevelName = nil
	}

	if staticField.BawuMap[userInfo.GetUserId()] != nil {
		arrResult.BawuType = proto.String(staticField.BawuMap[userInfo.GetUserId()].BawuType)
		arrResult.IsBawu = proto.Int32(staticField.BawuMap[userInfo.GetUserId()].IsBawu)
	}

	if !extra {
		arrResult.IsLike = proto.Int32(0)
		arrResult.Type = proto.Int32(0)
	}

	arrResult.BaijiahaoInfo = &client.BaijiahaoInfo{
		Avatar: proto.String(""),
	}

	if userIcons, exist := staticField.UserIcons[userInfo.GetUserId()]; exist {
		arrResult.ShowIconList = userIcons
	}

	if showInfo, exist := staticField.UserShowInfo[userInfo.GetUserId()]; exist {
		arrResult.UserShowInfo = showInfo
	}
	// 如果是楼主需要展示楼主icon
	if showInfo, exist := staticField.ThreadAuthorShowInfo[userInfo.GetUserId()]; exist {
		arrResult.UserShowInfo = showInfo
	}

	return arrResult
}

func buildIconInfo(ctx context.Context, iconInfo map[string]*user.NewIcon) []*client.Icon {
	res := make([]*client.Icon, 0)
	i := 0

	iconInfoSlice := make([]*user.NewIcon, 0, len(iconInfo))
	for _, icon := range iconInfo {
		if icon == nil || icon.Icon == nil {
			continue
		}
		iconInfoSlice = append(iconInfoSlice, icon)
	}

	sort.Slice(iconInfoSlice, func(i, j int) bool {
		if iconInfoSlice[i] == nil || iconInfoSlice[i].SlotNo == nil {
			return false
		}
		return iconInfoSlice[i].GetSlotNo() < iconInfoSlice[j].GetSlotNo()
	})

	for _, icon := range iconInfoSlice {
		if icon.GetIcon() == "" {
			continue
		}

		tmp := new(client.Icon)
		err := common.StructAToStructBCtx(ctx, icon, tmp)
		if err != nil {
			tbcontext.WarningF(ctx, "icon changeStruct: %v", err)
		}
		res = append(res, tmp)
		i++
		if i >= 4 {
			break
		}

	}

	return res
}

func getSpringVirtualUser(ctx context.Context, userInfo *user.UserInfo) *client.SpringVirtualUser {
	svu := userInfo.GetSpringVirtualUser()
	tmp := new(user.SpringVirtualUser)
	err := common.StructAToStructBCtx(ctx, svu, tmp)
	if err != nil {
		return nil
	}

	if int64(tmp.GetEndTime()) < time.Now().Unix() {
		return nil
	}

	return &client.SpringVirtualUser{
		IsVirtual: proto.Uint32(tmp.GetIsVirtual()),
		Url:       proto.String(tmp.GetUrl()),
	}
}

func getSubPostPanContent(ctx context.Context, subPost *client.SubPostList, subPostPos int, staticField *types.PbFloorStaticField) {
	for idx, slot := range subPost.GetContent() {
		if slot.GetType() == tbrichtext.SLOT_TYPE_LINK {
			slotNum := make(map[int]int)
			if strings.Contains(slot.GetLink(), "pan.baidu.com") {
				slotNum[idx] = idx
				if staticField.PanPostList[slot.GetLink()] == nil {
					staticField.PanPostList[slot.GetLink()] = make(map[int]map[int]int)
				}
				if staticField.PanPostList[slot.GetLink()][subPostPos] == nil {
					staticField.PanPostList[slot.GetLink()][subPostPos] = make(map[int]int)
				}
				staticField.PanPostList[slot.GetLink()][subPostPos][idx] = idx
			}
		}
	}

	multi := tbservice.Multi()
	req := make([]interface{}, 0, 0) // 请求afd真实参数为afdReq， Multi call input不加数组会报错
	for pan := range staticField.PanPostList {
		if strings.Count(pan, ".com") > 1 {
			continue
		}
		data := ""
		if strings.Index(pan, "pan.baidu.com/share/link") != -1 {
			panUrl, err := url.Parse(pan)
			if err == nil && panUrl != nil &&
				panUrl.Query().Get("uk") != "" && panUrl.Query().Get("shareid") != "" {
				data = fmt.Sprintf("shareid=%s&uk=%s&appid=137130", panUrl.Query().Get("shareid"), panUrl.Query().Get("uk"))
			}
		} else if strings.Index(pan, "pan.baidu.com/s") != -1 {
			shorturl := php2go.Substr(pan, uint(strings.Index(pan, "1")+1), -1)
			if strings.Index(shorturl, "?") != -1 {
				shorturl = shorturl[:strings.Index(shorturl, "?")]
			}
			shorturl = strings.TrimSpace(shorturl)
			data = fmt.Sprintf("shorturl=%s&appid=137130", shorturl)
		}

		tbParam := &tbservice.Parameter{
			Service: "wp_netdisk_new",
			Input:   req,
			Method:  "linkstatus",
			Output:  &netdisk.ShortUrlRes{},
			Option: []tbservice.Option{
				tbservice.WithHttpHeader(map[string][]string{
					"Querystring": {data},
					"Pathinfo":    {"/share/linkstatus"},
				}),
				tbservice.WithConverter(tbservice.JSONITER),
				tbservice.WithHttpUrl("/share/linkstatus?" + data),
				tbservice.WithHttpMethod("GET"),
			},
		}
		multi.Register(ctx, "linkstatus"+pan, tbParam)
	}

	multi.Call(ctx)

	for pan, subPostPos := range staticField.PanPostList {
		isvalid := 0
		panInter, err := multi.GetResult(ctx, "linkstatus"+pan)
		panRes, _ := panInter.(*netdisk.ShortUrlRes)
		if err != nil || panRes.Errno == nil || panRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call shorturlinfo linkstatus fail: %v, %v", err, panRes)
			isvalid = 3
		} else {
			if panRes.GetStatus() == 0 {
				isvalid = 1
			} else {
				isvalid = 0
			}
		}

		if isvalid == 1 {
			for subPos, arrSlotPos := range subPostPos {
				for _, slotPos := range arrSlotPos {
					staticField.SubPostList[subPos].GetContent()[slotPos].Text = proto.String("[有效] " + staticField.SubPostList[subPos].GetContent()[slotPos].GetText())
					staticField.SubPostList[subPos].GetContent()[slotPos].UrlType = proto.Int32(2)
				}
			}
		}

		if isvalid == 0 {
			for subPos, arrSlotPos := range subPostPos {
				for _, slotPos := range arrSlotPos {
					staticField.SubPostList[subPos].GetContent()[slotPos].Text = proto.String("[无效] " + staticField.SubPostList[subPos].GetContent()[slotPos].GetText())
					staticField.SubPostList[subPos].GetContent()[slotPos].UrlType = proto.Int32(1)
				}
			}
		}

		// if isvalid == 3 {
		//
		// }
	}

}

func getChatroomInfo(ctx context.Context, subPost *client.SubPostList, staticField *types.PbFloorStaticField) {
	if !staticField.HasChatroomId {
		return
	}

	for _, content := range subPost.GetContent() {
		if content.GetType() == tbrichtext.SLOT_TYPE_LINK {
			linkUrlInfo, err := url.Parse(content.GetLink())
			if err != nil {
				tbcontext.WarningF(ctx, "parser link fail: %v", err)
			} else {
				chatroomId := common.Tvttt(linkUrlInfo.Query().Get("chatroom_id"), common.TTT_INT64).(int64)
				if staticField.ChatroomInfo[chatroomId] != nil {
					if clientvers.IsLegalVersion("12_33_4", staticField.IntClientType, staticField.StrClientVersion) {
						params := url.QueryEscape(fmt.Sprintf(`{"page":"im/groupChat","refre":"","pageParams":{"roomId":"%d","forumId":"%d"}}`,
							chatroomId, staticField.ChatroomInfo[chatroomId].GetForumInfo().GetForumName().GetForumId()))
						content.Link = proto.String(fmt.Sprintf("tiebaapp://router/portal?params=%s", params))
					}
					content.LinkType = proto.Int32(5)
				}

			}
		}
	}

}

func processShopLink(ctx context.Context, subPost *client.SubPostList, staticField *types.PbFloorStaticField) {
	for _, content := range subPost.GetContent() {
		if content.GetType() == tbrichtext.SLOT_TYPE_LINK {
			tid := getThreadID(content.GetLink())
			for _, t := range staticField.ShopGoodsLink {
				if tid == t {
					content.LinkType = proto.Int32(7)
				}
			}
		}
	}
}

func videoHotLink(ctx context.Context, subPost *client.SubPostList, staticField *types.PbFloorStaticField) {
	fid := staticField.ForumInfo.GetForumName().GetForumId()
	fname := staticField.ForumInfo.GetForumName().GetForumName()
	thread := staticField.ThreadInfo
	for _, content := range subPost.GetContent() {
		if content.GetLinkType() == 8 {
			link := content.GetLink()
			parsedURL, err := url.Parse(link)
			if err != nil {
				tbcontext.WarningF(ctx, "url.Parse fail: err: %v, link: %v", err, link)
				continue
			}
			query := parsedURL.Query()
			linkurl := query.Get("url")
			params := map[string]any{
				"page": "video/listPlay",
				"pageParams": map[string]any{
					"videoData": map[string]any{
						"data": []any{
							map[string]any{
								"fid":           strconv.FormatInt(int64(fid), 10),
								"fname":         fname,
								"tid":           strconv.FormatInt(int64(thread.GetThreadId()), 10),
								"video_address": linkurl,
							},
						},
					},
					"action_type": "from_video_list_page_video_card",
				},
			}
			paramJSON, _ := jsoniter.Marshal(params)

			content.TargetScheme = proto.String("tiebaapp://router/portal?params=" + url.QueryEscape(string(paramJSON)))
		}
	}
}

func buildAnti(ctx context.Context, baseData *types.CPbFloorBaseData) *client.Anti {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	voiceSwitch := service.VoiceSwitch(ctx, map[string]int64{
		"user_id": staticField.UserId,
		"level":   int64(staticField.Perm.GetGrade().GetLevelId()),
		"is_like": int64(staticField.Perm.GetGrade().GetIsLike()),
	}, staticField.ForumInfo.GetForumName().GetForumName())

	isLogin := common.Tvttt(objReq.GetCommonAttr("login", false), common.TTT_BOOL).(bool)
	if !isLogin {
		arrAnti := &client.Anti{
			Tbs:          proto.String(tbs.CreateTbs(ctx, baseData.BaseObj.Req).Gene(isLogin)),
			Ifpost:       proto.Int32(0),
			Ifposta:      proto.Int32(0),
			ForbidFlag:   proto.Int32(1),
			ForbidInfo:   proto.String(getForbidInfo(1)),
			Ifvoice:      proto.Int32(int32(voiceSwitch.Switch)),
			VoiceMessage: proto.String(voiceSwitch.Message),
			Ifxiaoying:   proto.String(""),
		}

		arrAnti.BlockPopInfo = buildBlockPopInfo(arrAnti)
		return arrAnti
	}

	if staticField.Perm == nil {
		arrAnti := &client.Anti{
			Tbs:          proto.String(tbs.CreateTbs(ctx, baseData.BaseObj.Req).Gene(isLogin)),
			Ifpost:       proto.Int32(1),
			Ifposta:      proto.Int32(1),
			ForbidFlag:   proto.Int32(1),
			ForbidInfo:   proto.String(getForbidInfo(1)),
			Ifvoice:      proto.Int32(int32(voiceSwitch.Switch)),
			VoiceMessage: proto.String(voiceSwitch.Message),
			Ifxiaoying:   proto.String(""),
		}

		arrAnti.BlockPopInfo = buildBlockPopInfo(arrAnti)
		return arrAnti
	}

	xiaoyingServiceOutput := service.XiaoyingAnti(ctx, int32(staticField.ForumInfo.GetForumName().GetForumId()))
	arrAnti := &client.Anti{
		Tbs:              proto.String(tbs.CreateTbs(ctx, baseData.BaseObj.Req).Gene(isLogin)),
		Ifpost:           proto.Int32(0),
		Ifposta:          proto.Int32(1),
		ForbidFlag:       proto.Int32(int32(staticField.Perm.GetBlockType())),
		ForbidInfo:       proto.String(getForbidInfo(int(staticField.Perm.GetBlockType()))),
		Ifvoice:          proto.Int32(int32(voiceSwitch.Switch)),
		VoiceMessage:     proto.String(voiceSwitch.Message),
		Ifxiaoying:       proto.String(xiaoyingServiceOutput.ErrMsg),
		ReplyPrivateFlag: proto.Int32(int32(staticField.IntReplyPrivateFlag)),
	}

	if staticField.Perm.GetPerm()["can_post"] {
		arrAnti.Ifpost = proto.Int32(1)
	}

	if staticField.Perm.GetBlockType() > 0 {
		arrAnti.Ifposta = proto.Int32(0)
	}

	wordRes, err := wordserver.QueryKeys(ctx, "tb_wordlist_redis_del_thread_text",
		[]string{"reason_id", "all_switch", fmt.Sprintf("f_%d", staticField.ForumInfo.GetForumName().GetForumId()), "multi_delthread"})
	if err != nil {
		tbcontext.WarningF(ctx, "query tb_wordlist_redis_del_thread_text fail: %v", err)
	} else {
		arrAnti.MultiDelthread = proto.Int32(0)
		if len(wordRes) == 4 && (wordRes[2] == "1" || wordRes[1] == "1") {
			arrAnti.MultiDelthread = proto.Int32(1)
		}

		inputInter, err := php.Unserialize([]byte(wordRes[0]))
		if err != nil {
			tbcontext.WarningF(ctx, "unserialize wordRes fail: %v", err)
		} else {
			words := make([]string, 0)
			err = common.StructAToStructBCtx(ctx, inputInter, &words)
			if err != nil {
				tbcontext.WarningF(ctx, "convert inputInter to struct fail: %v", err)
			} else {
				wordRes, err = wordserver.QueryKeys(ctx, "tb_wordlist_redis_del_thread_text", words)
				if err != nil {
					tbcontext.WarningF(ctx, "query tb_wordlist_redis_del_thread_text fail: %v", err)
				} else {
					arr := make([]*client.DelThreadText, 0)
					for i, word := range words {
						arr = append(arr, &client.DelThreadText{
							TextId:   proto.Int32(common.Tvttt(word, common.TTT_INT32).(int32)),
							TextInfo: proto.String(wordRes[i]),
						})
					}

					arrAnti.DelThreadText = arr
				}
			}
		}
	}

	arrAnti.BlockPopInfo = buildBlockPopInfo(arrAnti)

	return arrAnti
}

func buildBlockPopInfo(arrAnti *client.Anti) *client.BlockPopInfo {
	return &client.BlockPopInfo{
		CanPost:      proto.Int32(arrAnti.GetIfpost()),
		BlockInfo:    proto.String(arrAnti.GetForbidInfo()),
		SubBlockInfo: proto.String(getSubBlockInfo(int(arrAnti.GetForbidFlag()))),
	}
}

func getSubBlockInfo(forbidFlag int) string {
	subBlockInfo := map[int]string{
		3: "点赞、签到、会员加速升级",
	}

	if _, ok := subBlockInfo[forbidFlag]; ok {
		return subBlockInfo[forbidFlag]
	}

	return ""
}

func getForbidInfo(forbidFlag int) string {
	message := map[int]string{
		1: "本吧目前仅限登录用户发贴",
		2: "本吧目前仅限吧务团队及注册满一定时间的老用户发贴",
		3: "吧规说4级就能发言啦~",
		4: "本吧目前仅限吧务团队发贴",
		5: "根据吧规，本吧只能浏览，不能发贴",
	}

	if _, ok := message[forbidFlag]; ok {
		return message[forbidFlag]
	}

	return ""
}

func buildPost(ctx context.Context, baseData *types.CPbFloorBaseData) *client.Post {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	scrW := common.Tvttt(objReq.GetPrivateAttr("scr_w", 0), common.TTT_INT32).(int32)
	deviceScore := common.Tvttt(objReq.GetCommonAttr("device_score", 0), common.TTT_INT).(int)

	arrPost := new(client.Post)
	arrPost.Id = proto.Uint64(staticField.PostList.GetPostId())
	arrPost.Title = proto.String(strings.TrimSpace(html.UnescapeString(staticField.PostList.GetTitle())))
	arrPost.Floor = proto.Uint32(staticField.PostList.GetPostNo())
	arrPost.Time = proto.Uint32(staticField.PostList.GetNowTime())
	arrPost.Content = make([]*client.PbContent, 0)
	common.StructAToStructBCtx(ctx, staticField.PostList.GetContent(), &arrPost.Content)

	// 端外回流切词 - 回复贴
	if len(baseData.StaticField.HitWordMap) > 0 {
		arrPost.Content = util.ContentReStruct(arrPost.Content, staticField.HitWordMap, staticField)
	}

	arrPost.LbsInfo = new(client.Lbs)
	common.StructAToStructBCtx(ctx, staticField.PostList.GetLocation(), arrPost.LbsInfo)
	arrPost.IsVote = proto.Uint32(staticField.PostList.GetIsVote())
	arrPost.IsVote = proto.Uint32(staticField.PostList.GetIsVote())
	arrPost.Agree = &client.Agree{
		AgreeNum:     proto.Int64(staticField.PostList.GetAgreeNum()),
		DisagreeNum:  proto.Int64(staticField.PostList.GetDisagreeNum()),
		DiffAgreeNum: proto.Int64(staticField.PostList.GetAgreeNum() - staticField.PostList.GetDisagreeNum()),
		HasAgree:     proto.Int32(types.NotAgree),
		AgreeType:    proto.Int32(0),
	}
	if staticField.PostAgreeData != nil {
		arrPost.Agree.HasAgree = proto.Int32(types.NotAgree)
		if staticField.PostAgreeData.Status != nil && staticField.PostAgreeData.GetStatus() == 0 {
			arrPost.Agree.HasAgree = proto.Int32(types.HasAgree)
		}
		if staticField.PostAgreeData.AgreeType != nil {
			arrPost.Agree.AgreeType = proto.Int32(staticField.PostAgreeData.GetAgreeType())
			if arrPost.Agree.GetHasAgree() > 0 {
				if arrPost.Agree.GetAgreeType() != types.AgreeTypeCai {
					arrPost.Agree.AgreeType = proto.Int32(types.AgreeTypeAgree)
				}
			}
		}
	}
	if staticField.ShowSquared {
		arrPost.ShowSquared = proto.Int32(1)
	} else {
		arrPost.ShowSquared = proto.Int32(0)
	}
	arrPost.IsPostVisible = proto.Int32(int32(staticField.PostList.GetIsPostVisible()))
	arrPost.IsVoice = proto.Uint32(0)
	if arrPost.GetLbsInfo().GetLat() != "" {
		arrPost.GetLbsInfo().Lat = nil
	}
	if arrPost.GetLbsInfo().GetLng() != "" {
		arrPost.GetLbsInfo().Lng = nil
	}

	userInfo := staticField.UserInfos[staticField.PostList.GetUserId()]
	if staticField.GenderIcon {
		clientUserInfo := getClientUserInfo(ctx, userInfo, staticField, "", true)
		format.FormatClientUser(clientUserInfo)
		arrPost.Author = &client.User{
			Id:                proto.Int64(clientUserInfo.GetId()),
			Name:              proto.String(clientUserInfo.GetName()),
			NameShow:          proto.String(clientUserInfo.GetNameShow()),
			Portrait:          proto.String(clientUserInfo.GetPortrait()),
			Type:              proto.Int32(clientUserInfo.GetType()),
			LevelId:           proto.Int32(clientUserInfo.GetLevelId()),
			IsLike:            proto.Int32(clientUserInfo.GetIsLike()),
			TshowIcon:         clientUserInfo.GetTshowIcon(),
			NewTshowIcon:      clientUserInfo.GetNewTshowIcon(),
			Iconinfo:          clientUserInfo.GetIconinfo(),
			IsBawu:            proto.Int32(clientUserInfo.GetIsBawu()),
			PrivSets:          clientUserInfo.GetPrivSets(),
			BawuType:          proto.String(clientUserInfo.GetBawuType()),
			Gender:            proto.Int32(clientUserInfo.GetGender()),
			IsMem:             proto.Int32(clientUserInfo.GetIsMem()),
			TbVip:             clientUserInfo.GetTbVip(),
			GodData:           clientUserInfo.GetGodData(),
			Pendant:           clientUserInfo.GetPendant(),
			SealPrefix:        proto.String(clientUserInfo.GetSealPrefix()),
			AlaInfo:           clientUserInfo.GetAlaInfo(),
			SpringVirtualUser: clientUserInfo.GetSpringVirtualUser(),
			Uk:                proto.String(clientUserInfo.GetUk()),
			NewGodData:        clientUserInfo.GetNewGodData(),
			DisplayAuthType:   proto.Int32(clientUserInfo.GetDisplayAuthType()),
			LevelName:         proto.String(clientUserInfo.GetLevelName()),
			BaijiahaoInfo:     clientUserInfo.BaijiahaoInfo,
			ShowIconList:      clientUserInfo.GetShowIconList(),
			UserShowInfo:      clientUserInfo.GetUserShowInfo(),
		}

	} else {
		clientUserInfo := getClientUserInfo(ctx, userInfo, staticField, "", true)
		format.FormatClientUser(clientUserInfo)
		arrPost.Author = &client.User{
			Id:                proto.Int64(clientUserInfo.GetId()),
			Name:              proto.String(clientUserInfo.GetName()),
			NameShow:          proto.String(clientUserInfo.GetNameShow()),
			Portrait:          proto.String(clientUserInfo.GetPortrait()),
			Type:              proto.Int32(clientUserInfo.GetType()),
			LevelId:           proto.Int32(clientUserInfo.GetLevelId()),
			IsLike:            proto.Int32(clientUserInfo.GetIsLike()),
			TshowIcon:         clientUserInfo.GetTshowIcon(),
			NewTshowIcon:      clientUserInfo.GetNewTshowIcon(),
			Iconinfo:          clientUserInfo.GetIconinfo(),
			IsBawu:            proto.Int32(clientUserInfo.GetIsBawu()),
			BawuType:          proto.String(clientUserInfo.GetBawuType()),
			IsMem:             proto.Int32(clientUserInfo.GetIsMem()),
			TbVip:             clientUserInfo.GetTbVip(),
			GodData:           clientUserInfo.GetGodData(),
			Pendant:           clientUserInfo.GetPendant(),
			SealPrefix:        proto.String(clientUserInfo.GetSealPrefix()),
			AlaInfo:           clientUserInfo.GetAlaInfo(),
			SpringVirtualUser: clientUserInfo.GetSpringVirtualUser(),
			Uk:                proto.String(clientUserInfo.GetUk()),
			NewGodData:        clientUserInfo.GetNewGodData(),
			DisplayAuthType:   proto.Int32(clientUserInfo.GetDisplayAuthType()),
			BaijiahaoInfo:     clientUserInfo.BaijiahaoInfo,
			ShowIconList:      clientUserInfo.GetShowIconList(),
			UserShowInfo:      clientUserInfo.GetUserShowInfo(),
		}
	}

	if userInfo.GetBusinessAccount().GetStatus() == 1 {
		arrPost.Author.BusinessAccountInfo = &client.BusinessAccountInfo{
			IsBusinessAccount:      proto.Int32(1),
			IsForumBusinessAccount: proto.Int32(0),
		}

		if php2go.InArray(int64(staticField.ForumInfo.GetForumName().GetForumId()), userInfo.GetBusinessAccount().GetFid()) {
			arrPost.Author.BusinessAccountInfo.IsForumBusinessAccount = proto.Int32(1)
		}
	}

	// 添加用户头像挂件
	arrPost.Author.Pendant = props.GetPendant(ctx, userInfo, staticField.IntClientType, props.PENDANT_LOCATION_PB)
	arrPost.Author.SealPrefix = proto.String(props.SealPrefix(ctx, userInfo))

	if staticField.NovelFansInfo[staticField.PostList.GetUserId()] != nil {
		arrPost.Author.NovelFansInfo = new(client.NovelFansInfo)
		common.StructAToStructBCtx(ctx, staticField.NovelFansInfo[staticField.PostList.GetUserId()], arrPost.Author.NovelFansInfo)
	}

	wordRes, err := wordserver.QueryKeys(ctx, "tb_wordlist_redis_device_score", []string{
		"bubble_android_threshold", "bubble_ios_threshold", "bubble_harmony_threshold",
	})
	bubbleIosThreshold := 99
	bubbleAndroidThreshold := 99
	bubbleHarmonyThreshold := 99
	if err != nil {
		tbcontext.WarningF(ctx, "wordserver.QueryKeys err: %s", err.Error())
	} else if len(wordRes) > 2 {
		if wordRes[0] != "" {
			bubbleAndroidThreshold, _ = common.Tvttt(wordRes[0], common.TTT_INT).(int)
		}
		if wordRes[1] != "" {
			bubbleIosThreshold, _ = common.Tvttt(wordRes[1], common.TTT_INT).(int)
		}
		if wordRes[2] != "" {
			bubbleHarmonyThreshold, _ = common.Tvttt(wordRes[2], common.TTT_INT).(int)
		}
	}

	if staticField.PostList.GetPostAttr() != "" {
		strBcode := strings.TrimSpace(staticField.PostList.GetPostAttr())
		arrBcode := usertool.ProcessBcode(strBcode)
		arrPost.BimgUrl = proto.String(usertool.AutoMatchBubbleImg(int32(staticField.IntClientType), int(scrW), int64(arrBcode.Bcode)))
		arrPost.IosBimgFormat = proto.String(arrBcode.Bformat)
		bCode := arrBcode.Bcode

		input := map[string]interface{}{
			"props_id": []int{bCode},
		}
		GetDynamicUrlByPropsId := new(theme.GetDynamicUrlByPropsIdRes)
		err := tbservice.Call(ctx, "theme", "getDynamicUrlByPropsId", input, GetDynamicUrlByPropsId, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || GetDynamicUrlByPropsId.Errno == nil || GetDynamicUrlByPropsId.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call theme:getDynamicUrlByPropsId fail, res: %v, err: %v", GetDynamicUrlByPropsId, err)
		}

		dynamicUrlJson := GetDynamicUrlByPropsId.GetData()[int64(bCode)].GetDynamicUrl()
		dynamicUrlArr := make(map[string]string)
		jsoniter.Unmarshal([]byte(dynamicUrlJson), &dynamicUrlArr)
		dynamicUrl := ""
		if clientvers.Compare(staticField.StrClientVersion, "12.29.4") <= 0 && dynamicUrlArr["afx_url"] != "" {
			dynamicUrl = dynamicUrlArr["afx_url"]
		}

		var bubbleV2 *client.BubbleV2
		if clientvers.Compare(staticField.StrClientVersion, "12.79.0") <= 0 {
			bubbleV2 = &client.BubbleV2{
				ColorDayStart:          common.GetStringPtr(dynamicUrlArr["color_day_start"]),
				ColorNightStart:        common.GetStringPtr(dynamicUrlArr["color_night_start"]),
				MainElementIcon:        common.GetStringPtr(dynamicUrlArr["main_element_icon"]),
				MainElementVideo:       common.GetStringPtr(dynamicUrlArr["main_element_video"]),
				ColorDayEnd:            common.GetStringPtr(dynamicUrlArr["color_day_end"]),
				ColorNightEnd:          common.GetStringPtr(dynamicUrlArr["color_night_end"]),
				SubElementIcon:         common.GetStringPtr(dynamicUrlArr["sub_element_icon"]),
				SubElementVideo:        common.GetStringPtr(dynamicUrlArr["sub_element_video"]),
				CommentColorDayStart:   common.GetStringPtr(dynamicUrlArr["comment_color_day_start"]),
				CommentColorNightStart: common.GetStringPtr(dynamicUrlArr["comment_color_night_start"]),
				CommentColorDayEnd:     common.GetStringPtr(dynamicUrlArr["comment_color_day_end"]),
				CommentColorNightEnd:   common.GetStringPtr(dynamicUrlArr["comment_color_night_end"]),
			}
		}

		if dynamicUrl != "" {
			iosStaticUrl := dynamicUrlArr["ios"]
			androidStaticUrl := dynamicUrlArr["android"]
			iosBimgFormat := dynamicUrlArr["ios_bimg_format"]
			tmpImg := arrPost.GetBimgUrl()
			tmpIosFormat := arrPost.GetIosBimgFormat()
			if staticField.IntClientType == stcdefine.CLIENT_TYPE_ANDROID {
				if deviceScore < bubbleAndroidThreshold {
					// 安卓低端机 下发静态
					dynamicUrl = ""
				} else {
					// 安卓高端机 下发动态
					arrPost.BimgUrl = proto.String(androidStaticUrl)
					arrPost.IosBimgFormat = proto.String(iosBimgFormat)
				}
				// 防止取不到图片，做容错处理
				if androidStaticUrl == "" {
					arrPost.BimgUrl = proto.String(tmpImg)
					arrPost.IosBimgFormat = proto.String(tmpIosFormat)
					dynamicUrl = ""
				}
			} else if staticField.IntClientType == stcdefine.CLIENT_TYPE_IPHONE {
				if deviceScore < bubbleIosThreshold {
					// ios低端机 下发静态
					dynamicUrl = ""
				} else {
					//  ios高端机 下发动态
					arrPost.BimgUrl = proto.String(iosStaticUrl)
					arrPost.IosBimgFormat = proto.String(iosBimgFormat)
				}
				// 防止取不到图片，做容错处理
				if androidStaticUrl == "" {
					arrPost.BimgUrl = proto.String(tmpImg)
					arrPost.IosBimgFormat = proto.String(tmpIosFormat)
					dynamicUrl = ""
				}
			} else if staticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
				if deviceScore < bubbleHarmonyThreshold {
					// harmony 低端机 下发静态
					dynamicUrl = ""
				} else {
					// harmony 高端机 下发动态
					arrPost.BimgUrl = proto.String(androidStaticUrl)
					arrPost.IosBimgFormat = proto.String(iosBimgFormat)
				}
				// 防止取不到图片，做容错处理
				if androidStaticUrl == "" {
					dynamicUrl = ""
					arrPost.BimgUrl = proto.String(tmpImg)
					arrPost.IosBimgFormat = proto.String(tmpIosFormat)
				}
			} else {
				// 其他端下发静态
				dynamicUrl = ""
			}
		}

		if bubbleV2 != nil {
			// 安卓和ios 高端机
			if (staticField.IntClientType == stcdefine.CLIENT_TYPE_ANDROID && deviceScore >= bubbleAndroidThreshold) ||
				(staticField.IntClientType == stcdefine.CLIENT_TYPE_IPHONE && deviceScore >= bubbleIosThreshold) ||
				(staticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY && deviceScore >= bubbleHarmonyThreshold) {
				arrPost.BubbleInfo = &client.ThemeBubble{
					PropsId:  proto.Int32(int32(bCode)),
					BubbleV2: bubbleV2,
				}
			}
		}
	} else {
		arrPost.BimgUrl = proto.String("")
		arrPost.IosBimgFormat = proto.String("")
		arrPost.DynamicUrl = proto.String("")
	}

	arrPost.TpointPost = staticField.TPointPost
	arrPost.SkinInfo = staticField.SkinInfo

	// 如果是转贴主态情况
	// 主态用户的level_id从from_forum['level_id']里面取
	// 客态用户的level_id从user['level_id']里面取
	// 非转贴用户的level_id从user['level_id']里面取
	if staticField.IsRepostThread && staticField.IntRepostThreadStatus == types.RepostBrowserStatusMultiForum {
		// 主态都给过去from_forum数据，如果没有来源吧，数据给空
		arrForum := new(client.SimpleForum)
		arrForum.LevelId = proto.Uint32(arrForum.GetLevelId())
		if staticField.PostList.GetVForumId() > 0 {
			arrForum = buildForum(ctx, baseData)
			arrForum.LevelId = proto.Uint32(uint32(arrPost.GetAuthor().GetLevelId()))
		}

		arrPost.FromForum = &client.SimpleForum{
			Id:          proto.Int64(arrForum.GetId()),
			Name:        proto.String(arrForum.GetName()),
			IsExists:    proto.Int32(arrForum.GetIsExists()),
			Avatar:      proto.String(arrForum.GetAvatar()),
			IsLiked:     proto.Uint32(arrForum.GetIsLiked()),
			IsSigned:    proto.Uint32(arrForum.GetIsSigned()),
			FirstClass:  proto.String(arrForum.GetFirstClass()),
			SecondClass: proto.String(arrForum.GetSecondClass()),
			Ext:         proto.String(arrForum.GetExt()),
			LevelId:     proto.Uint32(arrForum.GetLevelId()),
		}
	}

	if staticField != nil && staticField.PbSearchWords != nil && staticField.PbSearchConfig != nil &&
		staticField.StrClientVersion != "" && clientvers.CompareV2(staticField.StrClientVersion, ">=", "12.76") {
		DealPostByHitWord(ctx, arrPost, staticField.PbSearchWords,
			staticField.PbSearchThemeColorInfo, staticField.PbSearchConfig, staticField.PbSearchIconInfo)
	}

	for _, content := range arrPost.GetContent() {
		if content.GetType() == tbrichtext.SLOT_TYPE_LINK {
			// 解析帖子id
			tid := getThreadID(content.GetLink())
			for _, t := range baseData.StaticField.ShopGoodsLink {
				if tid == t {
					content.LinkType = proto.Int32(7)
					continue
				}
			}

			linkUrlInfo, err := url.Parse(content.GetLink())
			if err != nil {
				tbcontext.WarningF(ctx, "parser link fail: %v", err)
			} else {
				chatroomId := common.Tvttt(linkUrlInfo.Query().Get("chatroom_id"), common.TTT_INT64).(int64)
				if staticField.ChatroomInfo[chatroomId] != nil {
					if clientvers.IsLegalVersion("12_33_4", staticField.IntClientType, staticField.StrClientVersion) {
						params := url.QueryEscape(fmt.Sprintf(`{"page":"im/groupChat","refre":"","pageParams":{"roomId":"%d","forumId":"%d"}}`,
							chatroomId, staticField.ChatroomInfo[chatroomId].GetForumInfo().GetForumName().GetForumId()))
						content.Link = proto.String(fmt.Sprintf("tiebaapp://router/portal?params=%s", params))
					}
					// link_type值与发帖时的url_type值相等，5为群聊链接类型
					content.LinkType = proto.Int32(5)
				}
			}
		}

		//视频资源处理
		if content.GetLinkType() == 8 && staticField.IntClientType == stcdefine.CLIENT_TYPE_ANDROID &&
			clientvers.CompareV2(staticField.StrClientVersion, ">=", "12.85") {
			link := content.GetLink()
			parsedURL, err := url.Parse(link)
			if err != nil {
				tbcontext.WarningF(ctx, "url.Parse fail: err: %v, link: %v", err, link)
				continue
			}
			query := parsedURL.Query()
			linkurl := query.Get("url")
			fid := staticField.ForumInfo.GetForumName().GetForumId()
			fname := staticField.ForumInfo.GetForumName().GetForumName()
			thread := staticField.ThreadInfo
			params := map[string]any{
				"page": "video/listPlay",
				"pageParams": map[string]any{
					"videoData": map[string]any{
						"data": []any{
							map[string]any{
								"fid":           strconv.FormatInt(int64(fid), 10),
								"fname":         fname,
								"tid":           strconv.FormatInt(int64(thread.GetThreadId()), 10),
								"video_address": linkurl,
							},
						},
					},
					"action_type": "from_video_list_page_video_card",
				},
			}
			paramJSON, _ := jsoniter.Marshal(params)
			content.TargetScheme = proto.String("tiebaapp://router/portal?params=" + url.QueryEscape(string(paramJSON)))
		}
	}

	return arrPost

}

func getThreadID(strUrl string) uint64 {
	tid := uint64(0)
	// 编译正则表达式
	// 查找匹配项
	decodedURL, err := url.QueryUnescape(strUrl)
	if err != nil {
		return 0
	}
	match := MatchTIDre.FindStringSubmatch(decodedURL)
	if len(match) > 1 {
		tid = cast.ToUint64(match[1])
	}
	return tid
}

func GetMemberPkInfo(ctx context.Context, userInfo *user.UserInfo, forumInfo *protoForum.GetBtxInfoByNameRes, currentUserId int64, clientVersion string) []*client.TshowInfo {
	newTsShow, isMember := forum.GetNewTshoIcon(ctx, userInfo, forumInfo.GetForumForumId(ctx), forumInfo.GetAttrs(), clientVersion)
	if len(newTsShow) > 0 && isMember {
		if clientvers.Compare(clientVersion, "7.3.9") == -1 {
			if userInfo.GetUserId() == currentUserId {
				newTsShow[0].Url = proto.String(forum.NATIVE_MEMBER_BUY_URL)
			} else {
				newTsShow[0].Url = proto.String(fmt.Sprintf("%s?from=pb&pkUserId=%d", forum.H5_MEMBER_PK_URL, userInfo.GetUserId()))
			}
		} else {
			newTsShow[0].Url = proto.String(forum.NATIVE_MEMBER_BUY_URL)
		}
	}

	return newTsShow
}

// pb吧名直搜 处理楼中楼信息
func DealSubPostByHitWord(ctx context.Context, staticField *types.PbFloorStaticField) {
	if staticField == nil || staticField.SubPostList == nil || staticField.PbSearchWords == nil ||
		staticField.PbSearchConfig == nil || staticField.PbSearchConfig.SubPost == nil {
		return
	}
	newSubPostList := make([]*contentforumsearch.NewPost, 0)
	for _, subPostInfo := range staticField.SubPostList {
		if staticField.PbSearchWords != nil && len(staticField.PbSearchWords) > 0 {
			newSubPbContent := filterPbContentByPbSearch(subPostInfo.Content, staticField.PbSearchWords,
				staticField.PbSearchThemeColorInfo, staticField.PbSearchIconInfo)
			if newSubPbContent != nil {
				subPostInfo.Content = newSubPbContent
			}
		} else {
			subPostInfo.Content = DealPbContent(subPostInfo.Content,
				staticField.PbSearchThemeColorInfo, staticField.PbSearchIconInfo)
		}

		newPostInfo := &contentforumsearch.NewPost{
			PostId:    subPostInfo.GetId(),
			PbContent: subPostInfo.Content,
			PostType:  3,
		}
		newSubPostList = append(newSubPostList, newPostInfo)
	}
	if newSubPostList != nil {
		subPostListInfo := contentforumsearch.DealPostList(ctx, newSubPostList, staticField.PbSearchConfig.SubPost, int32(1))
		if len(subPostListInfo) > 0 {
			for i, subPost := range subPostListInfo {
				staticField.SubPostList[i].Content = subPost.PbContent
			}
		}
	}
}

// pb吧名直搜 处理post信息
func DealPostByHitWord(ctx context.Context, arrPost *client.Post, pbSearchWords map[string]string,
	pbSearchThemeColorInfo *client.ThemeColorInfo, pbSearchConfig *contentforumsearch.AllShowConfigDataStruct, iconInfo string) {
	if arrPost.Content == nil || pbSearchConfig.Thread == nil {
		return
	}

	if pbSearchWords != nil && len(pbSearchWords) > 0 {
		newPbContent := filterPbContentByPbSearch(arrPost.Content, pbSearchWords, pbSearchThemeColorInfo, iconInfo)
		if newPbContent != nil {
			arrPost.Content = newPbContent
		}
	} else {
		arrPost.Content = DealPbContent(arrPost.Content, pbSearchThemeColorInfo, iconInfo)
	}
	threadPostListInfo := contentforumsearch.DealPostList(ctx, []*contentforumsearch.NewPost{
		&contentforumsearch.NewPost{
			PostId:    arrPost.GetId(),
			PbContent: arrPost.Content,
			PostType:  2,
		},
	}, pbSearchConfig.Post, int32(1))
	for _, threadPost := range threadPostListInfo {
		arrPost.Content = threadPost.PbContent
	}
}

// pb吧名直搜 处理贴子pbContent内容
func filterPbContentByPbSearch(pbContents []*client.PbContent, words map[string]string,
	themeColorInfo *client.ThemeColorInfo, iconInfo string) []*client.PbContent {
	newPbContent := make([]*client.PbContent, 0)
	for _, content := range pbContents {
		if content.GetType() != 40 || content.GetSearchType() != 1 {
			newPbContent = append(newPbContent, content)
			continue
		}
		if _, ok := words[content.GetText()]; !ok {
			preNewPbContentIndex := len(newPbContent) - 1
			if preNewPbContentIndex >= 0 {
				preContent, currentContent := contentforumsearch.GetNewPbContent(
					newPbContent[preNewPbContentIndex], content, 1)
				if preContent != nil {
					newPbContent[preNewPbContentIndex] = preContent
				}
				if currentContent != nil {
					newPbContent = append(newPbContent, currentContent)
				}
			} else {
				newPbContent = append(newPbContent, &client.PbContent{
					Text: proto.String(content.GetText()),
					Type: proto.Uint32(0),
				})
			}
			continue
		}
		if themeColorInfo != nil {
			content.ThemeColor = themeColorInfo
		}
		if iconInfo != "" {
			content.Src = proto.String(iconInfo)
		}
		newPbContent = append(newPbContent, content)
	}
	return newPbContent
}

// dealPbContent 处理PbContent数据 增加icon和主题颜色
func DealPbContent(pbContents []*client.PbContent, themeColorInfo *client.ThemeColorInfo, iconInfo string) []*client.PbContent {
	for _, content := range pbContents {
		if content.GetSearchType() == 1 && content.GetType() == 40 {
			if iconInfo != "" {
				content.Src = proto.String(iconInfo)
			}
			if themeColorInfo != nil {
				content.ThemeColor = themeColorInfo
			}
		}
	}
	return pbContents
}
