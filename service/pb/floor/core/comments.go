package core

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/pb"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Comments struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_comments", func() engine.Job {
		return &CommentsOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewComments(ctx *engine.Context) *Comments {
	return &Comments{
		ctx: ctx,
	}
}
func (a *Comments) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *Comments) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField

	if staticField.IsPositioningFloor {
		getComments(ctx, staticField, staticField.IntSpid)
	} else {
		getComments(ctx, staticField, 0)
	}

	return nil
}

type CommentsOperator struct {
}

func (rdop *CommentsOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewComments(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "comments execute fail: %v", err)
		return err
	}

	return nil
}

func getComments(ctx context.Context, staticField *types.PbFloorStaticField, spid int64) {
	offset := (staticField.IntPn - 1) * types.FloorPageSize

	multi := tbservice.Multi()

	getCommentsInput := map[string]interface{}{
		"thread_id":          staticField.IntKz,
		"post_id":            staticField.IntPid,
		"offset":             offset,
		"res_num":            types.FloorPageSize,
		"status":             0,
		"has_mask":           1,
		"comment_id":         spid,
		"structured_content": 1,
		"is_comm_reverse":    staticField.IsCommReverse,
		"need_lzl_dynamic":   1,
	}
	if staticField.UserId > 0 {
		getCommentsInput["uids"] = []int64{staticField.UserId}
	}
	getCommentsParam := &tbservice.Parameter{
		Service: "post",
		Method:  "getComments",
		Input:   getCommentsInput,
		Output:  &pb.GetCommentsRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getComments", getCommentsParam)

	mgetThreadParam := &tbservice.Parameter{
		Service: "post",
		Method:  "mgetThread",
		Input: map[string]interface{}{
			"thread_ids":      []int64{staticField.IntKz},
			"need_abstract":   1,
			"forum_id":        0,
			"need_photo_pic":  0,
			"need_user_data":  0,
			"icon_size":       0,
			"need_forum_name": 1,
			"call_from":       "client_frs",
		},
		Output: &frs.MgetThreadRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetThread", mgetThreadParam)

	multi.Call(ctx)

	mgetThreadResInter, err := multi.GetResult(ctx, "mgetThread")
	mgetThreadRes := mgetThreadResInter.(*frs.MgetThreadRes)
	if err != nil || mgetThreadRes.Errno == nil || mgetThreadRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call post::mgetThread fail, err: %v, output: %v", err, common.ToString(mgetThreadRes))
	}

	if mgetThreadRes.GetOutput().GetThreadList() != nil && mgetThreadRes.GetOutput().GetThreadList()[uint64(staticField.IntKz)] != nil {
		staticField.ThreadInfo = mgetThreadRes.GetOutput().GetThreadList()[uint64(staticField.IntKz)]
	}

	getCommentsResInter, err := multi.GetResult(ctx, "getComments")
	getCommentsRes := getCommentsResInter.(*pb.GetCommentsRes)
	if err != nil || getCommentsRes.Errno == nil || getCommentsRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call post::getComments fail, err: %v, output: %v", err, common.ToString(getCommentsRes))
		return
	}

	staticField.Comments = getCommentsRes.GetOutput()

	// 只针对feed的请求走该过滤逻辑
	if staticField.StrSubappType == stcdefine.SUB_APP_FEED {
		postInfos := make([]*post.Post, 0)
		for _, val := range staticField.Comments.GetPostInfos() {
			feeInfo, err := val.GetFeedinfoStaticStruct()
			if err != nil {
				tbcontext.WarningF(ctx, "GetFeedinfoStaticStruct fail: %v", err)
			} else {
				if feeInfo.GetShow() == 1 {
					postInfos = append(postInfos, val)
				}
			}
		}
		staticField.Comments.PostInfos = postInfos
	}

	if spid > 0 {
		prevNum := staticField.Comments.GetOffset()
		staticField.IntPn = int(prevNum/types.FloorPageSize) + 1
	}

	return
}
