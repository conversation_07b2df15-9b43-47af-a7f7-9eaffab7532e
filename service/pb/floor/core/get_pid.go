package core

import (
	"context"
	"errors"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/pb"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type GetPid struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_get_pid", func() engine.Job {
		return &GetPidOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewGetPid(ctx *engine.Context) *GetPid {
	return &GetPid{
		ctx: ctx,
	}
}
func (a *GetPid) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *GetPid) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField

	if staticField.IntPid == 0 && staticField.IntSpid != 0 {
		input := map[string]interface{}{
			"thread_id":  staticField.IntKz,
			"comment_id": staticField.IntSpid,
		}
		res := new(pb.GetOneCommentByTidAndCidRes)
		err := tbservice.Call(ctx, "post", "getOneCommentByTidAndCid", input, res,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.Errno == nil {
			tbcontext.FatalF(ctx, "fail to call service post:getOneCommentByTidAndCid, err = %v, input = %v, output = %v", err, input, res)
			baseData.BaseObj.Error(tiebaerror.ERR_CLIENT_CALL_POST, tiebaerror.GetErrMsg(tiebaerror.ERR_CLIENT_CALL_POST), true)
			return errors.New("get pid fail")
		}

		if res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service post:getOneCommentByTidAndCid, err = %v, input = %v, output = %v", err, input, res)
			baseData.BaseObj.Error(int(res.GetErrno()), tiebaerror.GetErrMsg(int(res.GetErrno())), true)
			return errors.New("get pid fail")
		}

		pid := res.GetOutput().GetRes().GetPostId()
		if pid == 0 {
			tbcontext.WarningF(ctx, "pid is 0")
			baseData.BaseObj.Error(tiebaerror.ERR_MO_THREAD_NOT_EXIST, tiebaerror.GetErrMsg(tiebaerror.ERR_MO_THREAD_NOT_EXIST), true)
			return errors.New("get pid fail")
		}

		staticField.IntPid = int64(pid)
	}

	return nil
}

type GetPidOperator struct {
}

func (rdop *GetPidOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewGetPid(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "get_pid execute fail: %v", err)
		return err
	}

	return nil
}
