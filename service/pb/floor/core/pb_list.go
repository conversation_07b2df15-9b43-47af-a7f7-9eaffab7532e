package core

import (
	"context"
	"errors"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/pb"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientFloor "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/pb/floor"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type PbList struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("pbfloor_pblist", func() engine.Job {
		return &PbListOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPbList(ctx *engine.Context) *PbList {
	return &PbList{
		ctx: ctx,
	}
}
func (a *PbList) IsValid(ctx context.Context, baseData *types.CPbFloorBaseData) bool {

	return true
}
func (a *PbList) Execute(ctx context.Context, outData *clientFloor.PbFloorRes, baseData *types.CPbFloorBaseData) error {
	staticField := baseData.StaticField

	var errorNo uint32
	if clientvers.ReversionCompare(clientvers.REVERSION_CLIENT_TYPE_IPHONE_ONLINE, staticField.StrClientVersion, staticField.IntClientType) >= 0 {
		errorNo = multiGetPbList(ctx, staticField)
	} else {
		errorNo = getPbList(ctx, staticField)
	}

	if errorNo != tiebaerror.ERR_SUCCESS {
		baseData.BaseObj.Error(int(errorNo), tiebaerror.GetErrMsg(int(errorNo)), true)
		return errors.New("get pb list fail")
	}

	staticField.IntThreadForumId = staticField.PostInfo.GetForumId()
	if staticField.IntThreadForumId == 615140 {
		baseData.BaseObj.Error(tiebaerror.BAWU_PRIVATE_POST, tiebaerror.GetErrMsg(tiebaerror.BAWU_PRIVATE_POST), true)
		return errors.New("forum is invalid")
	}

	if len(staticField.PostInfo.GetPostInfos()) > 0 {
		staticField.PostList = staticField.PostInfo.GetPostInfos()[0]
	}

	return nil
}

type PbListOperator struct {
}

func (rdop *PbListOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientFloor.PbFloorRes
	var baseData *types.CPbFloorBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewPbList(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "pblist execute fail: %v", err)
		return err
	}

	return nil
}

func multiGetPbList(ctx context.Context, staticField *types.PbFloorStaticField) uint32 {
	multi := tbservice.Multi()

	getCommentSrchByThreadIdInput := map[string]interface{}{
		"thread_id":          staticField.IntKz,
		"post_id":            staticField.IntPid,
		"comment_id":         staticField.IntSpid,
		"res_num":            1,
		"see_author":         0,
		"has_comment":        1,
		"has_mask":           1,
		"has_ext":            1,
		"structured_content": 1,
	}
	if staticField.UserId > 0 {
		getCommentSrchByThreadIdInput["uids"] = []int64{staticField.UserId}
	}
	getCommentsParam := &tbservice.Parameter{
		Service: "post",
		Method:  "getCommentSrchByThreadId",
		Input:   getCommentSrchByThreadIdInput,
		Output:  &pb.GetCommentSrchByThreadIdRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getCommentSrchByThreadId", getCommentsParam)

	//加这个主要是为了获取顶踩数，getCommentSrchByThreadId 这个接口arch说这个接口不建议改，会有问题，所以调getPostInfo
	//超过10w楼的场景几乎不可能出现，所以可以忽略下面的getPostInfo单独调用的性能消耗
	mgetThreadParam := &tbservice.Parameter{
		Service: "post",
		Method:  "getPostInfo",
		Input: map[string]interface{}{
			"post_ids": []int64{staticField.IntPid},
		},
		Output: &pb.GetPostInfoRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getPostInfo", mgetThreadParam)

	multi.Call(ctx)

	getCommentSrchByThreadIdInter, err := multi.GetResult(ctx, "getCommentSrchByThreadId")
	getCommentSrchByThreadIdRes := getCommentSrchByThreadIdInter.(*pb.GetCommentSrchByThreadIdRes)
	if err != nil || getCommentSrchByThreadIdRes.Errno == nil {
		tbcontext.FatalF(ctx, "call post::getCommentSrchByThreadId fail, err: %v, output: %v", err, common.ToString(getCommentSrchByThreadIdRes))
		return tiebaerror.ERR_CLIENT_CALL_POST
	}

	if getCommentSrchByThreadIdRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call post::getCommentSrchByThreadId fail, err: %v, output: %v", err, common.ToString(getCommentSrchByThreadIdRes))
		return getCommentSrchByThreadIdRes.GetErrno()
	}

	if len(getCommentSrchByThreadIdRes.GetOutput().GetOutput()) > 0 {
		staticField.PostInfo = getCommentSrchByThreadIdRes.GetOutput().GetOutput()[0]
	}

	if staticField.PostInfo == nil {
		tbcontext.WarningF(ctx, "post info not exist")
		return tiebaerror.ERR_MO_THREAD_NOT_EXIST
	}

	if staticField.PostInfo.GetIsThreadDeleted() == 1 && (staticField.UserId == 0 || staticField.PostInfo.GetFirstPostUserid() != staticField.UserId) {
		tbcontext.WarningF(ctx, "uid: %d, first_post_userid: %d", staticField.UserId, staticField.PostInfo.GetFirstPostUserid())
		return tiebaerror.ERR_MO_THREAD_NOT_EXIST
	}

	// 超过10w高楼 重新获取post_info信息
	if staticField.PostInfo.GetHugeFloor() {
		arrPostInfo := getPostInfo(ctx, staticField.IntPid)
		if len(arrPostInfo) > 0 {
			postUserMap := make(map[int64]string)
			for _, postInfo := range arrPostInfo {
				postUserMap[postInfo.GetUserId()] = postInfo.GetUsername()
			}

			staticField.PostInfo.PostInfos = arrPostInfo
			staticField.PostInfo.PostUserMap = postUserMap
			staticField.PostInfo.SubPostUserMap = make(map[int64]string)
		}
	}

	//如果post_infos的数组长度为0，那么直接返回错误
	if len(staticField.PostInfo.GetPostInfos()) == 0 || int64(staticField.PostInfo.GetPostInfos()[0].GetPostId()) != staticField.IntPid {
		tbcontext.WarningF(ctx, "post info len is 0, %d, pid: %d", len(staticField.PostInfo.GetPostInfos()), staticField.IntPid)
		return tiebaerror.ERR_MO_THREAD_NOT_EXIST
	}

	// post2 处理 之后的顶踩数据从这里取
	getPostInfoInter, err := multi.GetResult(ctx, "getPostInfo")
	getPostInfoRes := getPostInfoInter.(*pb.GetPostInfoRes)
	if err != nil || getPostInfoRes.Errno == nil || getPostInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "call post::getPostInfo fail, err: %v, output: %v", err, common.ToString(getPostInfoRes))
		return tiebaerror.ERR_CLIENT_CALL_POST
	}

	if len(getPostInfoRes.GetOutput()) > 0 {
		staticField.PostInfo2 = getPostInfoRes.GetOutput()[0]
	}

	return tiebaerror.ERR_SUCCESS
}

func getPbList(ctx context.Context, staticField *types.PbFloorStaticField) uint32 {
	multi := tbservice.Multi()

	getCommentSrchByThreadIdInput := map[string]interface{}{
		"thread_id":          staticField.IntKz,
		"post_id":            staticField.IntPid,
		"comment_id":         staticField.IntSpid,
		"res_num":            1,
		"see_author":         0,
		"has_comment":        1,
		"has_mask":           1,
		"has_ext":            1,
		"structured_content": 1,
	}
	if staticField.UserId > 0 {
		getCommentSrchByThreadIdInput["uids"] = []int64{staticField.UserId}
	}
	getCommentSrchByThreadIdParam := &tbservice.Parameter{
		Service: "post",
		Method:  "getCommentSrchByThreadId",
		Input:   getCommentSrchByThreadIdInput,
		Output:  &pb.GetCommentSrchByThreadIdRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getCommentSrchByThreadId", getCommentSrchByThreadIdParam)

	multi.Call(ctx)

	getCommentSrchByThreadIdInter, err := multi.GetResult(ctx, "getCommentSrchByThreadId")
	getCommentSrchByThreadIdRes := getCommentSrchByThreadIdInter.(*pb.GetCommentSrchByThreadIdRes)
	if err != nil || getCommentSrchByThreadIdRes.Errno == nil {
		tbcontext.FatalF(ctx, "call post::getCommentSrchByThreadId fail, err: %v, output: %v", err, common.ToString(getCommentSrchByThreadIdRes))
		return tiebaerror.ERR_CLIENT_CALL_POST
	}

	if getCommentSrchByThreadIdRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call post::getCommentSrchByThreadId fail, err: %v, output: %v", err, common.ToString(getCommentSrchByThreadIdRes))
		return getCommentSrchByThreadIdRes.GetErrno()
	}

	if len(getCommentSrchByThreadIdRes.GetOutput().GetOutput()) > 0 {
		staticField.PostInfo = getCommentSrchByThreadIdRes.GetOutput().GetOutput()[0]
	}

	if staticField.PostInfo == nil {
		tbcontext.WarningF(ctx, "post info not exist")
		return tiebaerror.ERR_MO_THREAD_NOT_EXIST
	}

	if staticField.PostInfo.GetIsThreadDeleted() == 1 && (staticField.UserId == 0 || staticField.PostInfo.GetFirstPostUserid() != staticField.UserId) {
		tbcontext.WarningF(ctx, "uid: %d, first_post_userid: %d", staticField.UserId, staticField.PostInfo.GetFirstPostUserid())
		return tiebaerror.ERR_MO_THREAD_NOT_EXIST
	}

	// 超过10w高楼 重新获取post_info信息
	if staticField.PostInfo.GetHugeFloor() {
		arrPostInfo := getPostInfo(ctx, staticField.IntPid)
		if len(arrPostInfo) > 0 {
			postUserMap := make(map[int64]string)
			for _, postInfo := range arrPostInfo {
				postUserMap[postInfo.GetUserId()] = postInfo.GetUsername()
			}

			staticField.PostInfo.PostInfos = arrPostInfo
			staticField.PostInfo.PostUserMap = postUserMap
			staticField.PostInfo.SubPostUserMap = make(map[int64]string)
		}
	}

	//如果post_infos的数组长度为0，那么直接返回错误
	if len(staticField.PostInfo.GetPostInfos()) == 0 || int64(staticField.PostInfo.GetPostInfos()[0].GetPostId()) != staticField.IntPid {
		tbcontext.WarningF(ctx, "post info len is 0, %d, pid: %d", len(staticField.PostInfo.GetPostInfos()), staticField.IntPid)
		return tiebaerror.ERR_MO_THREAD_NOT_EXIST
	}

	return tiebaerror.ERR_SUCCESS
}

func getPostInfo(ctx context.Context, pid int64) []*post.Post {
	input := map[string]interface{}{
		"post_ids":           []int64{pid},
		"structured_content": 1,
	}
	res := new(pb.GetPostInfoRes)
	err := tbservice.Call(ctx, "post", "getPostInfo", input, res,
		tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil {
		tbcontext.FatalF(ctx, "fail to call service post:getPostInfo, err = %v, input = %v, output = %v", err, input, res)
		return nil
	}

	if res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service post:getPostInfo, err = %v, input = %v, output = %v", err, input, res)
		return nil
	}

	return res.GetOutput()
}
