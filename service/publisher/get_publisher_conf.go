package publisher

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/redis"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	commonPb "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	hottopicProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/hottopic"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/score"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/search"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/threadtype"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	publisherPb "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/publisher/publisherConf"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/php"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	recommendTopicCount       = 3 // 推荐话题数量
	recommendTopicModuleTitle = "话题灵感推荐"
)

// GetPublisherConf @Description 获取redis存储的帖子草稿数据
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：baseData：输入数据
// @Param：response：输出数据
// @Return：int：错误码
func GetPublisherConf(ctx context.Context, baseData *types.PublisherConfBaseData, response *publisherPb.GetPublisherConfResIdl) int {
	if baseData == nil || baseData.Request == nil {
		tbcontext.WarningF(ctx, "GetPublisherConf Input Invalid！Input:%v", baseData)
		return tiebaerror.ERR_INPUT_PARAM
	}

	// 获取话题灵感推荐卡 - *********以上版本
	obj := baseData.BaseObj
	sampleID, _ := common.Tvttt(obj.ObjRequest.GetCommonAttr("sample_id", ""), common.TTT_STRING).(string)
	userID, _ := common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", "0"), common.TTT_INT64).(int64)
	sids := UbsAbtest.GetUbsAbtestSid(obj.Ctx, sampleID, strconv.FormatInt(userID, 10), "")
	strClientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	if clientvers.Compare("*********", strClientVersion) >= 0 && php2go.InArrayString("12_71_huatilinggantuijian_a", sids) {
		fid := baseData.Request.GetFid()
		if fid > 0 {
			err := getRecommendTopicInfo(ctx, baseData, response)
			if err != nil {
				tbcontext.WarningF(ctx, "GetRecommendTopicInfo fail: %v", err)
			}
		}
	}

	tid := baseData.Request.GetTid()

	multi := tbservice.Multi()

	if baseData.Request.GetTab() == "seek" {
		tmoneyParams := &tbservice.Parameter{
			Service: "common",
			Method:  "getUserTmoney",
			Input: map[string]interface{}{
				"user_id": userID,
			},
			Output: &commonPb.GetUserTmoneyRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}

		multi.Register(ctx, "getUserTmoney", tmoneyParams)
	}

	if tid > 0 {
		// 获取草稿数据
		getDraftInfoParam := &tbservice.Parameter{
			Service: "common",
			Method:  "getDraftInfo",
			Input:   &commonPb.GetDraftInfoReq{Tid: proto.Int64(tid)},
			Output:  &commonPb.GetDraftInfoRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
				tbservice.WithRalName("common_go"),
			},
		}
		multi.Register(ctx, "getDraftInfo", getDraftInfoParam)
		// 获取帖子数据
		userParams := &tbservice.Parameter{
			Service: "post",
			Method:  "mgetThread",
			Input: &frs.MgetThreadReq{
				ThreadIds:    []uint64{uint64(tid)},
				NeedAbstract: proto.Uint32(0),
				NeedPhotoPic: proto.Uint32(0),
				IconSize:     proto.Uint32(0),
				CallFrom:     proto.String("client_frs"),
			},
			Output: &frs.MgetThreadRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetThread", userParams)
	}

	multi.Call(ctx)

	if tid > 0 {
		// 获取草稿
		getDraftInfoOutput, err := multi.GetResult(ctx, "getDraftInfo")
		if err != nil {
			tbcontext.WarningF(ctx, "call common::getDraftInfo fail: %v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		var getDraftInfoRes *commonPb.GetDraftInfoRes
		var ok bool
		if getDraftInfoRes, ok = getDraftInfoOutput.(*commonPb.GetDraftInfoRes); !ok {
			tbcontext.WarningF(ctx, "call common::getDraftInfo fail: %v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		if getDraftInfoRes == nil || getDraftInfoRes.GetData() == nil || getDraftInfoRes.GetData().GetExt() == "" {
			tbcontext.WarningF(ctx, "GetPublisherConf getDraftInfo is Nil")
			return tiebaerror.ERR_SUCCESS
		}
		draftInfo := getDraftInfoRes.GetData()
		// 获取帖子绑定的榜单id
		mGetThreadOutput, err := multi.GetResult(ctx, "mgetThread")
		if err != nil {
			tbcontext.WarningF(ctx, "call post::mgetThread fail: %v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		var mGetThreadRes *frs.MgetThreadRes
		if mGetThreadRes, ok = mGetThreadOutput.(*frs.MgetThreadRes); !ok {
			tbcontext.WarningF(ctx, "call post::mgetThread fail: %v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		threadList := mGetThreadRes.GetOutput().GetThreadList()
		if threadList[uint64(tid)] == nil {
			tbcontext.WarningF(ctx, "GetPublisherConf mgetThread is Nil")
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		if threadtype.GetThreadType(threadList[uint64(tid)].GetThreadTypes())["is_draw"] {
			// 抽奖贴处理
			infoStruct, _ := threadList[uint64(tid)].GetDrawInfoStruct()
			if infoStruct.GetDrawId() <= 0 {
				tbcontext.WarningF(ctx, "Thread not have draw info")
				return tiebaerror.ERR_SUCCESS
			}
			return dealDrawDraft(ctx, infoStruct.GetDrawId(), draftInfo, response)
		}
		scoreInfo, _ := threadList[uint64(tid)].GetScoreInfoStruct()
		if scoreInfo.GetScoreListId() <= 0 {
			tbcontext.WarningF(ctx, "Thread Not Have Score Info")
			return tiebaerror.ERR_SUCCESS
		}
		// 获取打分贴ext数据
		draftInfoExt := &publisherPb.PublisherExt{}
		err = json.Unmarshal([]byte(draftInfo.GetExt()), &draftInfoExt)
		if err != nil {
			tbcontext.WarningF(ctx, "GetPublisherConf draftInfo Unmarshal err:%v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		// 获取帖子打分榜单打分项审核状态
		itemMap, err := getAllScoreItemByScoreListID(ctx, int64(scoreInfo.GetScoreListId()))
		if err != nil {
			tbcontext.WarningF(ctx, "GetPublisherConf getAllScoreItemByScoreListId err:%v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		// 获取审核文案映射配置
		auditFailedTextMap := getItemAuditFailedText(ctx, "tb_wordlist_redis_score_thread", "audit_text_mapping")
		for _, item := range draftInfoExt.GetScoreInfo().GetScoreItems() {
			item.AuditStatus = proto.Int32(1)
			if itemInfo, ok := itemMap[item.GetTitle()]; ok {
				// 审核不通过 需要标识 并且下发不通过原因
				if itemInfo.GetAuditStatus() == types.MachineAuditFail ||
					itemInfo.GetAuditStatus() == types.PersonAuditFail ||
					itemInfo.GetAuditStatus() == types.PmAuditFail {
					item.AuditStatus = proto.Int32(0)
					if auditFailReason, ok := auditFailedTextMap[itemInfo.GetAuditFailReason()]; ok {
						item.AuditFailedRes = proto.String(auditFailReason)
					}
				}
			}
		}
		pics := getPicURL(ctx, draftInfoExt.GetScoreInfo().GetScoreItems()) // 获取图片url
		response.Data.MetaData = &publisherPb.MetaData{
			Title:   proto.String(draftInfo.GetTitle()),
			Content: proto.String(draftInfo.GetContent()),
			Ext:     proto.String(common.ToString(draftInfoExt)),
			Pics:    pics,
		}
	}

	if baseData.Request.GetTab() == "seek" {
		// 获取草稿
		getUserTmoneyRaw, err := multi.GetResult(ctx, "getUserTmoney")
		if err != nil {
			tbcontext.WarningF(ctx, "call common::getUserTmoney fail: %v", err)
		} else {
			if getUserTmoneyRes, ok := getUserTmoneyRaw.(*commonPb.GetUserTmoneyRes); ok {
				response.Data.AssetInfo = &publisherPb.UserAssetInfo{
					TmoneyBalance: proto.Int32(int32(getUserTmoneyRes.GetData().GetRemainingNum())),
				}
			} else {
				tbcontext.WarningF(ctx, "call common::getUserTmoney fail: %v", err)
			}
		}

		// 默认可以发布悬赏贴
		response.Data.PublishStrategy = &publisherPb.PublishStrategy{
			CanTmoneyThread: proto.Int32(1),
		}

		redisKey := fmt.Sprintf("add_reward_tomoney_thread_num_%d_%s", userID, time.Now().Format("20060102"))

		pubTimes, err := resource.RedisCommonb.Get(obj.Ctx, redisKey).Result()
		if err != nil && !errors.Is(err, redis.ErrNil) {
			tbcontext.FatalF(ctx, "get redis count failed! input[%s] err[%s]", redisKey, err.Error())
		} else if pubTimes != "" {
			tmp, err := strconv.Atoi(pubTimes)
			if err != nil {
				tbcontext.FatalF(ctx, "convert string to int failed! err[%s]", err.Error())
			} else if tmp >= 5 {
				response.Data.PublishStrategy.CanTmoneyThread = proto.Int32(0)
			}
		}
	}

	return tiebaerror.ERR_SUCCESS
}

// getAllScoreItemByScoreListID 获取榜单打分项审核状态
func getAllScoreItemByScoreListID(ctx context.Context, scoreListID int64) (map[string]*score.ScoreItem, error) {
	resMap := make(map[string]*score.ScoreItem)
	getItemReq := &score.GetAllScoreItemByScoreIdReq{
		ScoreListId: []int64{scoreListID},
	}
	getItemRes := &score.GetAllScoreItemByScoreIdRes{}
	err := tbservice.Call(ctx, "score", "getAllScoreItemByScoreListId", getItemReq, getItemRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service score:getAllScoreItemByScoreListId, err = %v, input = %v,output = %v",
			err, common.ToString(getItemReq), common.ToString(getItemRes))
		return resMap, err
	}
	if getItemRes.GetData()[scoreListID] == nil {
		tbcontext.WarningF(ctx, "score:getAllScoreItemByScoreListId res is Nil!")
		return resMap, err
	}
	items := getItemRes.GetData()[scoreListID].GetItems()
	for _, item := range items {
		resMap[item.GetTitle()] = item
	}
	return resMap, nil

}

// getItemAuditFailedText 获取打分项审不通过原因
func getItemAuditFailedText(ctx context.Context, table, key string) map[string]string {
	failedTextMap := make(map[string]string)
	wordlistRes, err := wordserver.QueryKey(ctx, table, key)
	if err != nil {
		tbcontext.WarningF(ctx, "getItemAuditFailedText call wordserver fail, err=[%v]", err)
		return failedTextMap
	}
	textInterface, err := php.Unserialize([]byte(wordlistRes))
	if err != nil {
		tbcontext.WarningF(ctx, "Unmarshal fail: %v", err)
		return failedTextMap
	}
	auditTextArr := make([]string, 0)
	err = common.StructAToStructBCtx(ctx, textInterface, &auditTextArr)
	if err != nil {
		tbcontext.WarningF(ctx, "Unmarshal fail: %v", err)
		return failedTextMap
	}
	for _, text := range auditTextArr {
		split := strings.Split(text, "|")
		if len(split) < 2 {
			continue
		}
		failedTextMap[split[0]] = split[1]
	}
	return failedTextMap
}

// getPicUrl pic id格式的图片转换为url
func getPicURL(ctx context.Context, items []*publisherPb.ScoreItem) []*publisherPb.Pic {
	var pics []*publisherPb.Pic
	for _, item := range items {
		// pic id格式：#(pic,24833702701,506,900) 转换为url
		split := strings.Split(item.GetPic(), ",")
		if len(split) > 1 {
			picID, err := strconv.Atoi(split[1])
			if err != nil {
				tbcontext.WarningF(ctx, "picStr To URL Atoi err:%v", err)
				continue
			}
			pid2UrlInput := []image.Pid2UrlInput{
				{
					ProductName: "tieba",
					PicId:       int64(picID),
					Domain:      proto.String("tiebapic.baidu.com"),
					AuthStr:     proto.String("noauth"),
				},
			}
			url, err := image.BatPid2Url(pid2UrlInput)
			if err != nil {
				tbcontext.WarningF(ctx, "pic To URL err:%v", err)
				continue
			}
			if len(url) >= 1 {
				authURL, err := image.GenAuthUrl(url[0])
				if err != nil {
					tbcontext.WarningF(ctx, "pic Auth err:%v", err)
					continue
				}
				pics = append(pics, &publisherPb.Pic{
					Url:   proto.String(authURL),
					PicId: proto.String(item.GetPic()),
				})
			}

		}
	}
	return pics
}

// 获取灵感卡和热点卡
// nolint:gocyclo
func getRecommendTopicInfo(ctx context.Context, baseData *types.PublisherConfBaseData, response *publisherPb.GetPublisherConfResIdl) error {
	fid := baseData.Request.GetFid()
	if fid <= 0 {
		return errors.New("fid is not valid")
	}

	multi := tbservice.Multi()
	// 获取灵感卡
	searchTopicByTagsParam := &tbservice.Parameter{
		Service: "search",
		Method:  "searchTopicByTags",
		Input: &search.SearchTopicByTagsReq{
			Pn:         proto.String("1"),
			Rn:         proto.String("10"),
			ForumId:    proto.Int64(fid),
			SearchType: proto.Int32(1),
		},
		Output: &search.SearchTopicByTagsRes{},

		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "searchTopicByTags", searchTopicByTagsParam)

	// 获取热点卡
	nowTime := time.Now().Unix()
	getForumRecentHotTopicParam := &tbservice.Parameter{
		Service: "common",
		Method:  "getForumRecentHotTopic",
		Input: map[string]interface{}{
			"forum_id":      fid,
			"is_deleted":    0,
			"status":        0,
			"update_time_s": nowTime - 86400*14, // 14天前
			"update_time_e": nowTime,
		},
		Output: &hottopicProto.GetForumRecentHotTopicRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
			tbservice.WithRalName("common_go"),
		},
	}
	multi.Register(ctx, "getForumRecentHotTopic", getForumRecentHotTopicParam)

	// 获取全吧推荐卡
	req := map[string]any{
		"re_locate":  4,
		"num":        30, // 获取推荐话题卡个数-传其他数接口解析有问题
		"need_cache": 1,
		"need_pic":   0,
	}
	getSearchRecommendReq := &tbservice.Parameter{
		Service: "hottopic",
		Method:  "getSearchRecommend",
		Input: map[string]any{
			"req": req,
		},
		Output: &hottopicProto.GetSearchRecommendRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter("json"),
		},
	}
	multi.Register(ctx, "getSearchRecommend", getSearchRecommendReq)

	multi.Call(ctx)

	// 热点话题结果
	getForumRecentHotTopicRes, err := multi.GetResult(ctx, "getForumRecentHotTopic")
	hotTopicRes, _ := getForumRecentHotTopicRes.(*hottopicProto.GetForumRecentHotTopicRes)
	if err != nil || hotTopicRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service common:getForumRecentHotTopic, input = %v, output = %v, err = %v",
			common.ToString(getForumRecentHotTopicParam.Input), common.ToString(hotTopicRes), err)
	}

	// 灵感话题结果
	searchTopicByTagsRes, err := multi.GetResult(ctx, "searchTopicByTags")
	inspirationTopicRes, _ := searchTopicByTagsRes.(*search.SearchTopicByTagsRes)
	if err != nil || inspirationTopicRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service search:searchTopicByTags, input = %v, output = %v, err = %v",
			common.ToString(searchTopicByTagsParam.Input), common.ToString(inspirationTopicRes), err)
	}

	// 全站热点结果
	result, err := multi.GetResult(ctx, "getSearchRecommend")
	searchRecommend, _ := result.(*hottopicProto.GetSearchRecommendRes)
	if err != nil || searchRecommend.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service hottopic:getSearchRecommend, input = %v, output = %v, err = %v",
			common.ToString(req), common.ToString(searchRecommend), err)
	}

	// 展示话题推荐卡的条件
	// 14天内有热点话题 或 吧内有灵感话题
	if !(len(hotTopicRes.GetData().GetInfo()) > 0 || len(inspirationTopicRes.GetData().GetList()) > 0) {
		tbcontext.WarningF(ctx, "forum has no hot topic or inspiration topic, skip to get recommend topic, fid = %v", fid)
		return nil
	}

	var topicIds []int64
	var backTopicIds []int64 //备用话题，当没有灵感话题时用备用话题来补齐
	// 热点话题
	for _, v := range hotTopicRes.GetData().GetInfo() {
		// 从外面带入发布器的话题需要过滤 12.75 改为无需过滤
		if v.GetTopicId() > 0 {
			// 从两天内的热点话题取一条展示
			if (uint64(nowTime)-v.GetUpdateTime()) <= 86400*2 && len(topicIds) == 0 {
				// 先取一条热点话题，时效为两天内
				topicIds = append(topicIds, int64(v.GetTopicId()))
			}
			// 取指定条数热点话题，作为备用，不受时效限制
			backTopicIds = append(backTopicIds, int64(v.GetTopicId()))
			if len(backTopicIds) >= recommendTopicCount {
				break
			}
		}
	}
	// 灵感话题做补充
	inspirationTopicMap := map[int64]*search.TopicInfo{}
	if len(inspirationTopicRes.GetData().GetList()) == 0 {
		// 灵感话题为空，不补充，用备用话题
		topicIds = backTopicIds
	} else {
		for _, v := range inspirationTopicRes.GetData().GetList() {
			if v.GetTopicId() > 0 {
				topicIds = append(topicIds, v.GetTopicId())
				inspirationTopicMap[v.GetTopicId()] = v
			}
			// 最多展示3条
			if len(topicIds) >= recommendTopicCount {
				break
			}
		}
	}

	// 根据是否在热榜，话题的热度计算规则有区别
	topicBangIds := map[int64]struct{}{}
	for _, v := range searchRecommend.GetRet() {
		intTopicID, _ := strconv.ParseInt(v.GetMulId(), 10, 64)
		topicBangIds[intTopicID] = struct{}{}
	}

	if len(topicIds) <= 0 {
		tbcontext.WarningF(ctx, "no topic to recommend, fid = %v", fid)
		return nil
	}

	// tbcontext.NoticeF(ctx, "topicIds:%s", common.ToString(topicIds))
	topicParam := &hottopicProto.GetTopicListReq{
		Req: &hottopicProto.GetTopicListReqParam{
			TopicId: topicIds,
		},
	}
	topicRes := &hottopicProto.GetTopicInfoRes{}
	err = tbservice.Call(ctx, "hottopic", "getTopicInfo", topicParam, topicRes, tbservice.WithConverter(tbservice.JSONITER))
	// tbcontext.NoticeF(ctx, "getTopicInfo res:%s", common.ToString(topicRes))
	if err != nil || topicRes == nil || topicRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call hottopic::getTopicInfo failed! input[%v] hotTopicRes[%v]", common.ToString(topicParam), common.ToString(topicRes))
		return errors.New("call service hottopic::getTopicInfo failed")
	}

	if len(topicRes.GetRet()) <= 0 {
		tbcontext.WarningF(ctx, "call hottopic::getTopicInfo ret empty")
		return errors.New("hottopic::getTopicInfo ret empty")
	}

	// 构造话题信息的map，用于查找
	topicInfoMap := map[int64]*hottopicProto.TopicInfo{}
	for _, v := range topicRes.GetRet() {
		intTopicID, _ := strconv.ParseInt(v.GetTopicId(), 10, 64)
		topicInfoMap[intTopicID] = v
	}

	// 读词表，获取灵感话题热度讨论的系数配置
	discussFactor := 0.001
	discussFallbackFactor := 0.3
	items, _ := wordserver.QueryItems(ctx, "tb_wordlist_redis_publisher_conf",
		[]string{"inspiration_topic_discuss_factor", "inspiration_topic_discuss_fallback_factor"})
	if factor, ok := items["inspiration_topic_discuss_factor"]; ok {
		if f, err := strconv.ParseFloat(factor, 64); err == nil {
			discussFactor = f
		}
	}
	if factor, ok := items["inspiration_topic_discuss_fallback_factor"]; ok {
		if f, err := strconv.ParseFloat(factor, 64); err == nil {
			discussFallbackFactor = f
		}
	}

	// 取话题热度系数配置
	notRankPercent := uint64(230)
	res, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_hottopic_conf", "topics_not_the_list_percent")
	if err != nil {
		tbcontext.WarningF(ctx, "query wordlist failed, err=[%v], keys=[topics_not_the_list_percent]", err)
	} else {
		conf, err := strconv.ParseUint(res, 10, 32)
		if err == nil {
			notRankPercent = conf
		}
	}

	topicList := make([]*publisherPb.TopicsItem, 0, len(topicIds))
	// 保证顺序，只去3条数据
	for _, topicIDVal := range topicIds {
		v, ok := topicInfoMap[topicIDVal]
		if !ok {
			continue
		}

		// 处理头像
		headPic := ""
		extra, err := php.Unserialize([]byte(v.GetExtra()))
		if err == nil {
			extraMap, ok := extra.(map[string]interface{})
			if ok {
				if icon, hasIcon := extraMap["head_pic"].(string); hasIcon {
					headPic = icon
				}
			}
		}

		intDiscussNum, _ := strconv.ParseUint(v.GetDiscussNum(), 10, 64)

		_, isBang := topicBangIds[topicIDVal]
		if v.GetComeFrom() == "1" && !isBang && v.GetInteractionNumPage() > 0 && notRankPercent > 0 {
			intDiscussNum = uint64(v.GetInteractionNumPage()) * notRankPercent / 100
		}

		tmpTopicInfo := &publisherPb.TopicsItem{
			TopicId:     proto.Int64(topicIDVal),
			TopicPic:    proto.String(headPic),
			TopicName:   proto.String(v.GetTopicName()),
			DisscussNum: proto.Uint64(intDiscussNum),
			ViewNum:     proto.Uint64(uint64(v.GetInteractionNumPage())),
			TopicType:   proto.String("forum_hot_topic"),
		}

		// 灵感话题的几个字段都需要特殊处理
		if topic, ok := inspirationTopicMap[topicIDVal]; ok {
			// 话题灵感热度，取话题热度
			tmpTopicInfo.TopicPic = proto.String(topic.GetAvater())
			tmpTopicInfo.TopicName = proto.String(topic.GetTopicName())
			tmpTopicInfo.TopicType = proto.String("inspiration_topic")

			viewNum := uint64(discussFactor * float64(topic.GetShowPv()))
			if viewNum > tmpTopicInfo.GetDisscussNum() {
				tbcontext.WarningF(ctx, "inspiration topic view num > discuss num, topic_id = %d, discuss_num = %d, show_pv = %v",
					topicIDVal, tmpTopicInfo.GetDisscussNum(), topic.GetShowPv())
				viewNum = uint64(discussFallbackFactor * float64(tmpTopicInfo.GetDisscussNum()))
			}

			tmpTopicInfo.ViewNum = proto.Uint64(viewNum)
		}

		topicList = append(topicList, tmpTopicInfo)
	}

	response.Data.RecommendTopic = &publisherPb.RecommendTopic{
		ModuleTitle: proto.String(recommendTopicModuleTitle),
		Topics:      topicList,
	}

	return nil
}
