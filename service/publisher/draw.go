package publisher

import (
	"context"
	"strings"

	json "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	commonPb "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	quizProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/quiz"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	publisherPb "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/publisher/publisherConf"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// dealDrawDraft 处理抽奖贴草稿
func dealDrawDraft(ctx context.Context, drawID uint64, draftInfo *commonPb.DraftInfo, response *publisherPb.GetPublisherConfResIdl) int {
	// 获取ext数据
	draftInfoExt := &publisherPb.PublisherExt{}
	err := json.Unmarshal([]byte(draftInfo.GetExt()), &draftInfoExt)
	if err != nil {
		tbcontext.WarningF(ctx, "GetPublisherConf draftInfo Unmarshal err:%v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	tInput := &quizProto.MgetDrawThresholdByCondReq{
		DrawEntityId: proto.Uint64(drawID),
		Type:         proto.Uint32(4),
	}
	tOutput := &quizProto.MgetDrawThresholdByCondRes{}
	err = tbservice.Call(ctx, "quiz", "mgetDrawThresholdByCond", tInput, tOutput,
		tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || tOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call quiz::mgetDrawThresholdByCond fail err:[%v], input:[%+v], output:[%+v]", err, tInput, tOutput)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	pInput := &quizProto.MgetDrawPrizeByCondReq{
		DrawEntityId: proto.Uint64(drawID),
	}
	pOutput := &quizProto.MgetDrawPrizeByCondRes{}
	err = tbservice.Call(ctx, "quiz", "mgetDrawPrizeByCond", pInput, pOutput,
		tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || pOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call quiz::mgetDrawPrizeByCond fail err:[%v], input:[%+v], output:[%+v]", err, pInput, pOutput)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	prizeMap := make(map[string]*quizProto.DrawPrize)
	for i, prize := range pOutput.GetData().GetInfo() {
		key := prize.GetName() + cast.ToString(prize.GetPic())
		prizeMap[key] = pOutput.GetData().GetInfo()[i]
	}

	// 获取审核文案映射配置
	auditFailedTextMap := getItemAuditFailedText(ctx, "tb_wordlist_redis_thread_draw_config", "audit_text_mapping")

	if len(tOutput.GetData().GetInfo()) > 0 {
		itemInfo := tOutput.GetData().GetInfo()[0]
		replayKey := draftInfoExt.GetDrawInfo().GetParticipationRange().GetReplayKey()
		replayKey.AuditStatus = proto.Int32(1)
		// 审核不通过 需要标识 并且下发不通过原因
		if itemInfo.GetAuditStatus() == types.PersonAuditFail || itemInfo.GetAuditStatus() == types.PmAuditFail {
			replayKey.AuditStatus = proto.Int32(0)
			if auditFailReason, ok := auditFailedTextMap[itemInfo.GetAuditReason()]; ok {
				replayKey.AuditFailedRes = proto.String(auditFailReason)
			}
		}

		if itemInfo.GetAuditStatus() == types.MachineAuditFail || itemInfo.GetAuditStatus() == types.MachineAuditPicFail {
			replayKey.AuditStatus = proto.Int32(0)
			replayKey.AuditFailedRes = proto.String("关键词内容异常")
		}
	}

	pics := make([]uint64, 0)
	for _, item := range draftInfoExt.GetDrawInfo().GetReward() {
		item.AuditStatus = proto.Int32(1)
		key := item.GetName() + cast.ToString(item.GetPic())
		if itemInfo, ok := prizeMap[key]; ok {
			// 审核不通过 需要标识 并且下发不通过原因
			// 机审不通过是通过类型判断，人审不通过通过文案判断
			if itemInfo.GetAuditStatus() == types.MachineAuditFail {
				item.AuditStatus = proto.Int32(0)
				item.AuditItem = proto.String("name")
				item.AuditFailedRes = proto.String("奖品名称异常")
			} else if itemInfo.GetAuditStatus() == types.MachineAuditPicFail {
				item.AuditStatus = proto.Int32(0)
				item.AuditItem = proto.String("pic")
				item.AuditFailedRes = proto.String("奖品图异常")
			}

			if itemInfo.GetAuditStatus() == types.PersonAuditFail || itemInfo.GetAuditStatus() == types.PmAuditFail {
				item.AuditStatus = proto.Int32(0)
				if strings.Contains(itemInfo.GetAuditReason(), "奖品图片") {
					item.AuditItem = proto.String("pic")
				} else if strings.Contains(itemInfo.GetAuditReason(), "奖品名称") {
					item.AuditItem = proto.String("name")
				} else if strings.Contains(itemInfo.GetAuditReason(), "奖品价值") {
					item.AuditItem = proto.String("value")
				}
				if auditFailReason, ok := auditFailedTextMap[itemInfo.GetAuditReason()]; ok {
					item.AuditFailedRes = proto.String(auditFailReason)
				}
			}
		}
		pics = append(pics, item.GetPic())
	}

	response.Data.MetaData = &publisherPb.MetaData{
		Title:   proto.String(draftInfo.GetTitle()),
		Content: proto.String(draftInfo.GetContent()),
		Ext:     proto.String(common.ToString(draftInfoExt)),
		Pics:    convertPicToURL(ctx, pics),
	}
	return tiebaerror.ERR_SUCCESS
}

// convertPicToURL pic id格式的图片转换为url
func convertPicToURL(ctx context.Context, items []uint64) []*publisherPb.Pic {
	var pics []*publisherPb.Pic
	for _, item := range items {
		pid2UrlInput := []image.Pid2UrlInput{
			{
				ProductName: "tieba",
				PicId:       int64(item),
				Domain:      proto.String("tiebapic.baidu.com"),
				AuthStr:     proto.String("noauth"),
			},
		}
		url, err := image.BatPid2Url(pid2UrlInput)
		if err != nil {
			tbcontext.WarningF(ctx, "pic To URL err:%v", err)
			continue
		}
		if len(url) >= 1 {
			authURL, err := image.GenAuthUrl(url[0])
			if err != nil {
				tbcontext.WarningF(ctx, "pic Auth err:%v", err)
				continue
			}
			pics = append(pics, &publisherPb.Pic{
				Url:   proto.String(authURL),
				PicId: proto.String(cast.ToString(item)),
			})
		}
	}
	return pics
}
