package excellent

import (
	"context"
	"encoding/json"
	"fmt"
	"hash/crc32"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/golang/protobuf/proto"
	"golang.org/x/sync/errgroup"
	"icode.baidu.com/baidu/gdp/codec"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbstring"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/excellent/getBackRecommendForum"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	GET_BACK_RECOMMEND_FORUM_REDIS_PREFIX = "go-client-forum_get_back_recommend_forum_data"
	FORUM_REASON_REDIS_HASH_PREFIX        = "go-client-forum_forum_reason_hash_"
	FORUM_REASON_REDIS_HASH_SIZE          = 100
)

type forumGraphRes struct {
	Errno  int      `json:"errno"`
	ErrMsg string   `json:"errmsg"`
	Data   []string `json:"data"`
	Ie     string   `json:"ie"`
}

type UserInterestCycleRes struct {
	FeatureStatus []FeatureStatus `json:"feature_status"`
	Status        int             `json:"status"`
}
type FeatureStatus struct {
	Name   string `json:"name"`
	Status int    `json:"status"`
	Value  string `json:"value"`
}

type UserForumLevelReqItem struct {
	UserID  int64  `json:"user_id"`
	ForumID uint32 `json:"forum_id"`
}

func GetBackRecommendForum(ctx context.Context, baseData *types.GetBackRecommendForumBaseData,
	response *getBackRecommendForum.GetBackRecommendForumResIdl, needInterestCycle bool) int {
	forumID := baseData.Request.GetForumId()
	forumName := baseData.Request.GetForumName()
	userID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	cUID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", 0), common.TTT_STRING).(string)

	if forumID <= 0 || forumName == "" {
		tbcontext.WarningF(ctx, "GetSpriteBottomChat input param error, forumID[%v] forumName[%v]", forumID, forumName)
		return tiebaerror.ERR_PARAM_ERROR
	}
	if needInterestCycle {
		//兴趣周期用户判断
		isInterestCycle := userIsInterestCycle(ctx, cUID, strconv.Itoa(int(userID)))
		if !isInterestCycle {
			tbcontext.WarningF(ctx, "user is not in interest cycle, cUID[%v] userID[%v]", cUID, userID)
			return tiebaerror.ERR_SUCCESS
		}
	}

	// 通过redis获取数据
	redisKey := fmt.Sprintf("%s_%d", GET_BACK_RECOMMEND_FORUM_REDIS_PREFIX, forumID)
	if resource.CacheUserGrowth != nil {
		recommendForumStr, err := resource.CacheUserGrowth.Get(ctx, redisKey)
		if err == nil && recommendForumStr != "" {
			if recommendForumStr == "null" {
				return tiebaerror.ERR_SUCCESS
			}
			err = proto.Unmarshal([]byte(recommendForumStr), response.Data)
			// 缓存读取成功且正确更新isLiked字段，就返回
			if err == nil {
				if upUserIsLikedForum(ctx, userID, response) {
					return tiebaerror.ERR_SUCCESS
				}
			}
		}
	}

	//redis中数据为空，获取数据
	errorNo := getRecommendForumData(ctx, forumID, forumName, userID, response)

	// 存到redis中，30分钟缓存
	if errorNo != tiebaerror.ERR_SUCCESS {
		if resource.CacheUserGrowth != nil {
			_ = resource.CacheUserGrowth.Set(ctx, redisKey, "null", 30*time.Minute)
		}
		return errorNo
	}
	resData, err := proto.Marshal(response.Data)
	if err == nil && resource.CacheUserGrowth != nil {
		_ = resource.CacheUserGrowth.Set(ctx, redisKey, string(resData), 30*time.Minute)
	}

	return tiebaerror.ERR_SUCCESS
}

// getRecommendForumData
// 获取首页回退-吧推荐卡片数据
func getRecommendForumData(ctx context.Context, forumID uint32, forumName string, userID int64,
	response *getBackRecommendForum.GetBackRecommendForumResIdl) int {
	// 通过吧图谱接口获取推荐吧list
	recommendForumIDs := getRecommendForumList(ctx, forumID)
	if recommendForumIDs == nil {
		tbcontext.WarningF(ctx, "GetBackRecommendForum getRecommendForumList empty")
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	// 获取吧推荐列表
	recForumList := getForumInfoList(ctx, userID, recommendForumIDs)
	if recForumList == nil {
		tbcontext.WarningF(ctx, "GetBackRecommendForum getForumInfoList empty")
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	// 拼凑卡片引导文案
	cardTitle := forumName
	titleRune := []rune(forumName)
	if len(titleRune) > 5 {
		cardTitle = string(titleRune[0:3]) + "..." + string(titleRune[(len(titleRune)-2):])
	}
	// 相应数据赋值
	response.Data.CardGuide = proto.String("根据“" + cardTitle + "吧”推荐")
	response.Data.ForumList = recForumList

	return tiebaerror.ERR_SUCCESS
}

// getRecommendForumList
// 通过forumID获取相关关联吧列表
func getRecommendForumList(ctx context.Context, forumID uint32) []uint32 {
	forumGraphReq := map[string]interface{}{
		"forum_id":  forumID,
		"limit_num": 6,
	}

	reqOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithLogField(logit.AutoField("forum_id", forumID)),
	}

	forumGraphRet := &forumGraphRes{}

	err := tbservice.Call(ctx, "common", "getForumGraph", forumGraphReq, forumGraphRet, reqOption...)
	if err != nil || forumGraphRet == nil || len(forumGraphRet.Data) < 2 {
		tbcontext.WarningF(ctx, "common::getForumGraph failed, err[%v], ret[%v]", err, forumGraphRet)
		return nil
	}

	var ret []uint32
	for _, forumIDStr := range forumGraphRet.Data {
		forumIDInt, err := strconv.Atoi(forumIDStr)
		if err == nil && forumIDInt > 0 {
			ret = append(ret, uint32(forumIDInt))
		}
	}

	if len(ret) < 2 {
		return nil
	}
	return ret
}

// getForumInfoList
// 获取吧推荐列表
func getForumInfoList(ctx context.Context, userID int64, recommendForumIDs []uint32) []*getBackRecommendForum.ForumList {
	// 并发获取吧信息、用户是否已经关注对应的吧、吧推荐理由
	eg := new(errgroup.Group)

	// 吧扩展信息
	var forumInfoList map[uint32]*forum.BtxInfo
	eg.Go(func() error {
		forumExtInput := map[string]interface{}{
			"forum_id": recommendForumIDs,
		}
		forumRes := &forum.MgetBtxInfoExRes{}
		forumExtOption := []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		}

		err := tbservice.Call(ctx, "forum", "mgetBtxInfoEx", forumExtInput, forumRes, forumExtOption...)

		if err != nil || forumRes == nil || forumRes.Errno == nil || forumRes.GetErrno() != errno.CodeSuccess ||
			len(forumRes.GetOutput()) < 2 {
			tbcontext.WarningF(ctx, "call forum::mgetBtxInfoEx error: %v, res: %+v", err, forumRes)
		} else {
			forumInfoList = forumRes.GetOutput()
		}

		return nil
	})

	// 用户是否关注该吧
	userForumLikeMap := make(map[uint32]uint32)
	if userID > 0 {
		eg.Go(func() error {
			userForumLevelInput := make([]UserForumLevelReqItem, len(recommendForumIDs))
			for idx, forumIDItem := range recommendForumIDs {
				userForumLevelInput[idx] = UserForumLevelReqItem{
					UserID:  userID,
					ForumID: forumIDItem,
				}
			}
			userForumLevelParams := map[string]interface{}{"req": userForumLevelInput}

			userForumLevelRes := &perm.MgetUserForumLevelRes{}

			userForumLevelOption := []tbservice.Option{
				tbservice.WithHttpMethod("POST"),
				tbservice.WithConverter(tbservice.JSONITER),
			}

			err := tbservice.Call(ctx, "perm", "mgetUserForumLevel", userForumLevelParams, userForumLevelRes, userForumLevelOption...)

			if err != nil || userForumLevelRes == nil || userForumLevelRes.Errno == nil ||
				userForumLevelRes.GetErrno() != errno.CodeSuccess || len(userForumLevelRes.GetScoreInfo()) <= 0 {
				tbcontext.WarningF(ctx, "call perm::mgetUserForumLevel error: %v, res: %+v", err, userForumLevelRes)
			} else {
				for _, userForumItem := range userForumLevelRes.GetScoreInfo() {
					userForumLikeMap[userForumItem.GetForumId()] = userForumItem.GetIsLike()
				}
			}

			return nil
		})
	}

	// 吧推荐理由
	recommendReason := make(map[uint32]string)
	eg.Go(func() error {
		reasonMap := getForumReason(ctx, recommendForumIDs)
		if len(reasonMap) > 0 {
			recommendReason = reasonMap
		}
		return nil
	})

	// 结果等待
	_ = eg.Wait()

	if len(forumInfoList) < 2 {
		return nil
	}

	// 组装返回数据，并按照推荐的吧顺序返回
	var dataRes []*getBackRecommendForum.ForumList
	for _, fid := range recommendForumIDs {
		forumInfoItem := forumInfoList[fid]

		if forumInfoItem == nil {
			continue
		}
		// 吧关注数及发贴数
		memberNum := forumInfoItem.GetStatistics().GetMemberCount()
		postNum := forumInfoItem.GetStatistics().GetPostNum()
		// 推荐理由
		recReason := recommendReason[fid]
		if recReason == "" {
			recReason = forumInfoItem.GetCard().GetSlogan()
		}
		if recReason == "" && memberNum > 0 && postNum > 0 {
			recReason = "关注 " + formatUnit(int(memberNum)) + " 帖子 " + formatUnit(int(postNum))
		}
		// 条件判断
		if recReason == "" {
			continue
		}
		// 吧跳转地址
		jumpUrl := "tiebaapp://router/portal?params=" + tbstring.UrlEncode(
			`{"page":"frs/frs","pageParams":{"forumName":"`+forumInfoItem.GetForumName().GetForumName()+`"}}`)

		dataRes = append(dataRes, &getBackRecommendForum.ForumList{
			Id:           proto.Uint64(uint64(fid)),
			Name:         proto.String(forumInfoItem.GetForumName().GetForumName() + "吧"),
			Avatar:       proto.String(forumInfoItem.GetCard().GetAvatar()),
			MemberNum:    proto.Uint32(memberNum),
			PostNum:      proto.Uint32(postNum),
			IsLiked:      proto.Uint32(userForumLikeMap[fid]),
			JumpUrl:      proto.String(jumpUrl),
			RecommendTip: proto.String(recReason),
		})
	}

	if len(dataRes) < 2 {
		return nil
	}
	// 限制最多3条数据
	if len(dataRes) > 3 {
		dataRes = dataRes[:3]
	}
	return dataRes
}

// getForumReason
// 获取吧推荐理由
func getForumReason(ctx context.Context, recommendForumIDs []uint32) map[uint32]string {
	hashKeyForumIDsMap := make(map[string][]string)
	for _, fid := range recommendForumIDs {
		strFid := strconv.FormatUint(uint64(fid), 10)
		hashKey := getForumReasonHashKey(strFid)
		hashKeyForumIDsMap[hashKey] = append(hashKeyForumIDsMap[hashKey], strFid)
	}

	reasonMap := make(map[uint32]string)
	for redisHashKey, fidList := range hashKeyForumIDsMap {
		if len(fidList) == 1 {
			reasonStr, err := resource.RedisUserGrowth.HGet(ctx, redisHashKey, fidList[0]).Result()
			if err == nil && reasonStr != "" {
				fidInt, _ := strconv.Atoi(fidList[0])
				reasonMap[uint32(fidInt)] = reasonStr
			}
			continue
		}
		// 在同一个hashKey中，如果多个fid, 使用HMGet获取数据
		reasonList, err := resource.RedisUserGrowth.HMGet(ctx, redisHashKey, fidList...).Result()
		if err != nil {
			continue
		}
		for idx, valOne := range reasonList {
			if valOneStr, ok := valOne.(string); ok && valOneStr != "" {
				fidStr := fidList[idx]
				fidInt, _ := strconv.Atoi(fidStr)
				reasonMap[uint32(fidInt)] = valOneStr
			}
		}
	}

	return reasonMap
}

// getForumReasonHashKey
// 吧推荐理由hashKey
func getForumReasonHashKey(fid string) string {
	i := int(crc32.ChecksumIEEE([]byte(fid))) % FORUM_REASON_REDIS_HASH_SIZE
	return FORUM_REASON_REDIS_HASH_PREFIX + strconv.Itoa(i)
}

// upUserIsLikedForum
// 实时更新缓存中，用户是否关注过该吧
func upUserIsLikedForum(ctx context.Context, userID int64, response *getBackRecommendForum.GetBackRecommendForumResIdl) bool {
	if response == nil || response.Data == nil || len(response.Data.ForumList) < 2 {
		return false
	}
	// 如果用户未登录，默认显示未关注吧
	if userID == 0 {
		for index := range response.Data.ForumList {
			response.Data.ForumList[index].IsLiked = proto.Uint32(0)
		}
		return true
	}

	// 请求参数
	userForumLevelInput := make([]UserForumLevelReqItem, len(response.Data.ForumList))
	for idx, forumItem := range response.Data.ForumList {
		userForumLevelInput[idx] = UserForumLevelReqItem{
			UserID:  userID,
			ForumID: uint32(forumItem.GetId()),
		}
	}
	userForumLevelParams := map[string]interface{}{"req": userForumLevelInput}
	// 返回数据
	userForumLevelRes := &perm.MgetUserForumLevelRes{}

	// ral服务请求
	userForumLevelOption := []tbservice.Option{
		tbservice.WithHttpMethod("POST"),
		tbservice.WithConverter(tbservice.JSONITER),
	}
	err := tbservice.Call(ctx, "perm", "mgetUserForumLevel", userForumLevelParams, userForumLevelRes, userForumLevelOption...)

	if err != nil || userForumLevelRes == nil || userForumLevelRes.Errno == nil ||
		userForumLevelRes.GetErrno() != errno.CodeSuccess || len(userForumLevelRes.GetScoreInfo()) <= 0 {
		tbcontext.WarningF(ctx, "call perm::mgetUserForumLevel error: %v, res: %+v", err, userForumLevelRes)
		return false
	}
	// 获取用户是否关注该吧列表
	userForumLikeMap := make(map[uint32]uint32)
	for _, userForumItem := range userForumLevelRes.GetScoreInfo() {
		userForumLikeMap[userForumItem.GetForumId()] = userForumItem.GetIsLike()
	}
	// 更新数据
	for index, forumInfo := range response.Data.ForumList {
		if isLiked, ok := userForumLikeMap[uint32(forumInfo.GetId())]; ok {
			response.Data.ForumList[index].IsLiked = proto.Uint32(isLiked)
		} else {
			response.Data.ForumList[index].IsLiked = proto.Uint32(0)
		}
	}
	return true
}

// userIsInterestCycle
// 用户是否在兴趣周期内
func userIsInterestCycle(ctx context.Context, cUid, userID string) bool {
	// 请求参数
	reqParam := map[string]interface{}{
		"user_id": map[string]interface{}{
			"uid":  userID,
			"cuid": cUid,
		},
		"client":      "tieba-search",
		"token":       "b7d93c6d05ac1975eacd5b8fe2a04e8f",
		"service_tag": "common",
		"feature_meta": []map[string]interface{}{
			{"name": "tieba_user_model"},
		},
	}
	// 请求结构体
	req := &ghttp.RalRequest{
		// 在日志和监控中获取的到 api 名称，必须是可枚举的
		APIName: "ums_tieba_user_model",
		Method:  "POST",
		Path:    "/UserModelRestService/get_feature",
		Header: http.Header{
			"pathinfo":     []string{"/UserModelRestService/get_feature"},
			"Content-Type": []string{"application/json"},
		},
	}
	_ = req.WithBody(reqParam, codec.JSONEncoder)

	// 返回结构
	ret := &UserInterestCycleRes{}
	resp := &ghttp.RalResponse{
		Data:    ret,
		Decoder: codec.JSONDecoder,
	}

	//请求服务
	err := ral.RAL(ctx, "ums", req, resp)

	if err != nil || len(ret.FeatureStatus) < 1 {
		tbcontext.WarningF(ctx, "call ums::tieba_user_model error: %v", err)
		return false
	}
	// 数据获取
	featureRet := ret.FeatureStatus[0]
	if featureRet.Status != 0 || featureRet.Value == "" {
		return false
	}
	// 特征数据解析
	featureData := make(map[string]interface{})
	err = json.Unmarshal([]byte(featureRet.Value), &featureData)
	if err != nil {
		tbcontext.WarningF(ctx, "call ums::tieba_user_model json Unmarshal error: %v", err)
		return false
	}
	// 吧关注数计算
	focusForum, focusForumKeyIsExist := featureData["focus_forum"]
	focusForumList, isList := focusForum.([]interface{})
	if !focusForumKeyIsExist || !isList {
		tbcontext.WarningF(ctx, "call ums::tieba_user_model focus_forum key not exist or focus_forum is not slice")
		return false
	}
	if len(focusForumList) >= 11 {
		tbcontext.WarningF(ctx, "call ums::tieba_user_model len(focus_forum) >= 11")
		return false
	}
	// 吧活跃度计算
	huoyueFond, huoyueFondKeyIsExist := featureData["huoyue_fond"]
	huoyueFondList, isOk := huoyueFond.([]interface{})
	if !huoyueFondKeyIsExist || !isOk {
		tbcontext.WarningF(ctx, "call ums::tieba_user_model huoyue_fond key not exist or huoyue_fond is not slice")
		return false
	}
	var huoyueScore float64 = 0
	for _, huoyueFondItem := range huoyueFondList {
		huoyueFondItemList, itemListOk := huoyueFondItem.([]interface{})
		if !itemListOk || len(huoyueFondItemList) < 2 {
			continue
		}
		if huoyueScoreItem, huoyueScoreOk := huoyueFondItemList[1].(float64); huoyueScoreOk {
			huoyueScore += huoyueScoreItem
		}
	}
	if huoyueScore < 6.3 {
		tbcontext.WarningF(ctx, "call ums::tieba_user_model huoyue total score < 6.3")
		return false
	}

	return true
}

// formatUnit
// 格式化单位
func formatUnit(num int) string {
	if num < 10000 {
		return strconv.Itoa(num)
	} else {
		numStr := fmt.Sprintf("%.1fw", float64(num)/10000)
		return strings.Replace(numStr, ".0", "", 1)
	}
}
