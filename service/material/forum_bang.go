package activity

import (
	"context"
	"time"

	"google.golang.org/protobuf/proto"
	quizProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/quiz"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	materialProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/material"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func MaterialForumBang(ctx context.Context, baseData *types.ForumBangBaseData, response *materialProto.ForumBangResIdl) int {

	quizInput := &quizProto.GetTabListReq{
		TabCode:   proto.String(baseData.Request.GetTabCode()),
		IsNeedTab: proto.Int32(baseData.Request.GetIsNeedTab()),
		TopNum:    proto.Int32(10),
	}
	quizOut := &quizProto.GetTabListRes{}
	err := tbservice.Call(ctx, "quiz", "getRankTabContent", quizInput, quizOut, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || quizOut.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call quiz getRankTabContent fail, err:%v, quizOut:%s", err, common.ToString(quizOut))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	tabList := make([]*materialProto.TabInfo, 0)
	forumList := make([]*materialProto.BangList, 0)

	for _, tabInfoItem := range quizOut.GetData().GetTabList() {
		for _, forumInfoItem := range tabInfoItem.GetForumList() {
			hotThread := client.ThreadInfo{
				Title: proto.String(forumInfoItem.GetHotThread().GetTitle()),
				Fid:   proto.Int64(int64(forumInfoItem.GetHotThread().GetForumId())),
				Tid:   proto.Int64(int64(forumInfoItem.GetHotThread().GetThreadId())),
			}
			forumInfo := &materialProto.BangList{
				ForumId:       proto.Int64(forumInfoItem.GetForumId()),
				ForumName:     proto.String(forumInfoItem.GetForumName()),
				Avatar:        proto.String(forumInfoItem.GetAvatar()),
				MemberCount:   proto.Int64(forumInfoItem.GetMemberCount()),
				ScoreDesc:     proto.String(forumInfoItem.GetScoreDesc()),
				TrendIdentify: proto.String(forumInfoItem.GetTrendIdentify()),
				HotThread:     &hotThread,
			}

			forumList = append(forumList, forumInfo)
		}
		tabInfo := &materialProto.TabInfo{
			Name:      proto.String(tabInfoItem.GetName()),
			TabCode:   proto.String(tabInfoItem.GetTabCode()),
			SortRule:  proto.String(tabInfoItem.GetSortRule()),
			ForumList: forumList,
		}
		tabList = append(tabList, tabInfo)
	}
	currentTime := time.Now().Unix()
	response.Data = &materialProto.ForumBangRes{
		TabList:          tabList,
		CurrentTabCode:   proto.String(quizOut.GetData().GetCurrentTabCode()),
		CurrentTimestamp: proto.Uint64(uint64(currentTime)),
		InsertPos: proto.Int32(quizOut.GetData().GetInsertPos()),
	}

	return tiebaerror.ERR_SUCCESS
}
