package activity

import (
	"context"
	"strconv"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	quizProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/quiz"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	tbProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/material"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// ThreadBang 业务逻辑
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：baseData：输入数据
// @Param：response：输出数据
// @Return：错误码
func ThreadBang(ctx context.Context, baseData *types.ThreadBangBaseData, response *tbProto.ThreadBangResIdl) int {
	// 获取公共参数
	userID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	cuid := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
	clientType := baseData.Request.GetCommon().GetXClientType()
	clientVersion := baseData.Request.GetCommon().GetXClientVersion()
	pureMode := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("pure_mode", 0), common.TTT_INT).(int)
	sampleID, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("sample_id", ""), common.TTT_STRING).(string)

	// 构建请求参数
	req := &quizProto.GetBangThreadInfoReq{
		TabCode:       proto.String(baseData.Request.GetTabCode()),
		IsNeedTab:     proto.Uint32(uint32(baseData.Request.GetIsNeedTab())),
		TopNum:        proto.Uint32(uint32(20)),
		IsNeedAll:     proto.Uint32(uint32(0)),
		ClientType:    proto.Int32(clientType),
		ClientVersion: proto.String(clientVersion),
		ScrW:          proto.Uint32(2560),
		ScrH:          proto.Uint32(2560),
		SampleId:      proto.String(sampleID),
		Cuid:          proto.String(cuid),
		PureMode:      proto.Int32(int32(pureMode)),
		UserId:        proto.Int64(userID),
	}

	// 调用下游接口
	res := &quizProto.GetBangThreadInfoRes{}
	err := tbservice.Call(ctx, "quiz", "getBangThreadInfo", req, res)
	if err != nil || res.GetData() == nil {
		tbcontext.WarningF(ctx, "call quiz::getBangThreadInfo fail, input[%v], output[%v]", req, res)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	var TabList []*tbProto.ThreadTabInfo
	for _, v := range res.GetData().GetTabInfo() {
		tmptab := &tbProto.ThreadTabInfo{
			Name:     proto.String(v.GetName()),
			TabCode:  proto.String(v.GetTabCode()),
			SortRule: proto.String(v.GetSortRule()),
			//ThreadList: v.GetThreadList(),
			PicUrl: nil,
			MoreText: proto.String(v.GetMoreText()),
			LabelList : v.GetLabelList(),
		}
		for _, vv := range v.GetThreadList() {
			tmptab.ThreadList = append(tmptab.ThreadList, vv.ThreadInfo)
		}

		TabList = append(TabList, tmptab)
	}

	// 获取当前时间
	now := time.Now()

	// 获取秒级时间戳
	timestamp := now.Unix()

	// 构建响应数据
	response.Data.TabList = TabList
	response.Data.CurrentTabCode = proto.String(res.GetData().GetCurrentTabCode())
	response.Data.CurrentTimestamp = proto.String(strconv.FormatInt(timestamp, 10))
	sids := UbsAbtest.GetUbsAbtestSid(ctx, sampleID, strconv.FormatInt(userID, 10), "")
	response.Data.SampleNames = sids
	response.Data.TabList = TabList
	return tiebaerror.ERR_SUCCESS
}
