package activity

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	json "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	materialProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/material"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// MaterialHome 处理活动首页逻辑
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：baseData：输入数据
// @Param：response：输出数据
// @Return：返回值是int类型，表示执行是否成功
func MaterialHome(ctx context.Context, baseData *types.HomeBaseData, response *materialProto.HomeResIdl) int {

	// 获取词表配置数据
	confData, err := getConfData(ctx)
	if err != nil {
		tbcontext.WarningF(ctx, "get conf data fail, input[%v]", baseData.Request)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	// 获取公参
	arrInput := getPagePrivateInput(baseData)

	// 审核态兜底判断
	pureMode := baseData.Request.GetCommon().GetPureMode()
	if pureMode == 1 && confData.PureModeSwitch {
		confData.ConfInfo = confData.PureModeConfInfo
		buildRes(ctx, baseData, response, confData, nil)
		return tiebaerror.ERR_SUCCESS
	}

	// 通用校验
	checkRes := commonParamCheck(ctx, arrInput, confData)
	if !checkRes {
		tbcontext.WarningF(ctx, "check param fail, input[%v]", arrInput)
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 需要二次处理的conf
	confData = dealConvertConfData(ctx, confData)
	if confData == nil {
		tbcontext.WarningF(ctx, "deal convert conf fail[%v]", arrInput)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 活动模块详情
	activityInfo, err := getActivityInfo(ctx, arrInput, confData)
	if err != nil {
		tbcontext.FatalF(ctx, "getActivityInfo fail[%v]", arrInput)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// build res
	buildRes(ctx, baseData, response, confData, activityInfo)

	return tiebaerror.ERR_SUCCESS
}

// getConfData 获取词表配置数据
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：productId：产品ID
// @Param：pageKey：页面Key
// @Return：返回词表配置数据和错误信息
func getConfData(ctx context.Context) (*types.ConfData, error) {
	tableName := "tb_wordlist_redis_youliao_tab"

	keys, values, err := wordserver.GetTableContents(ctx, tableName, 0, -1)
	if err != nil {
		tbcontext.WarningF(ctx, "call wordlist fail, input[%v], output[%v]", tableName, err)
		return nil, err
	}
	if len(keys) != len(values) {
		tbcontext.WarningF(ctx, "call wordlist fail, input[%v], output[%v]", tableName, err)
		return nil, errors.New("wordlist key and value length not equal")
	}
	res := make(map[string]string)
	for i, key := range keys {
		res[key] = values[i]
	}

	confData := &types.ConfData{
		ConfInfo:         make(map[string]interface{}),
		ConvertConfInfo:  make(map[string]interface{}),
		CheckInfo:        make(map[string]interface{}),
		ActivityInfo:     make(map[string]*types.ActivityInfoConf),
		PureModeConfInfo: make(map[string]interface{}),
	}

	for key, value := range res {
		if key == "activity_pure_mode_switch" {
			confData.PureModeSwitch = value == "1"
			continue
		}

		if strings.HasPrefix(key, "confInfo#") {
			// json string to map
			var confDataDecode map[string]interface{}
			err := json.Unmarshal([]byte(value), &confDataDecode)
			if err == nil && confDataDecode != nil {
				confData.ConfInfo[strings.TrimPrefix(key, "confInfo#")] = confDataDecode
			} else {
				confData.ConfInfo[strings.TrimPrefix(key, "confInfo#")] = value
			}
		} else if strings.HasPrefix(key, "cvtInfo#") {
			confData.ConvertConfInfo[strings.TrimPrefix(key, "cvtInfo#")] = value
		} else if strings.HasPrefix(key, "checkInfo#") {
			confData.CheckInfo[strings.TrimPrefix(key, "checkInfo#")] = value
		} else if strings.HasPrefix(key, "actInfo#") {
			actConf := types.ActivityInfoConf{}
			err := json.Unmarshal([]byte(value), &actConf)
			if err != nil {
				tbcontext.WarningF(ctx, "json.Unmarshal fail, input[%v], output[%v]", tableName, key)
			} else {
				confData.ActivityInfo[strings.TrimPrefix(key, "actInfo#")] = &actConf
			}
		} else if strings.HasPrefix(key, "pureModeConfInfo#") {
			confData.PureModeConfInfo[strings.TrimPrefix(key, "pureModeConfInfo#")] = value
		}
	}
	confData.ConfInfo["current_timestamp"] = time.Now().Unix()
	return confData, nil
}

// getPagePrivateInput 获取页面所需入参
// @Param：request：请求数据
// @Param：confData：配置数据
// @Return：返回页面所需入参
func getPagePrivateInput(baseData *types.HomeBaseData) map[string]interface{} {
	// 获取请求参数
	userID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	cuID, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
	clientType := baseData.Request.GetCommon().GetXClientType()
	clientVersion := baseData.Request.GetCommon().GetXClientVersion()
	pureMode := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("pure_mode", 0), common.TTT_INT).(int)
	sampleID, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("sample_id", ""), common.TTT_STRING).(string)

	arrInput := map[string]interface{}{
		"client_version": clientVersion,
		"client_type":    clientType,
		"user_id":        userID,
		"cuid":           cuID,
		"pure_mode":      pureMode,
		"sample_id":      sampleID,
	}

	return arrInput
}

// commonParamCheck 通用校验
// @Param：arrInput：输入数据
// @Param：confData：配置数据
// @Return：返回值是bool类型，表示校验是否成功
func commonParamCheck(ctx context.Context, arrInput map[string]interface{}, confData *types.ConfData) bool {
	if confData.CheckInfo["check_empty"] != nil {
		arrEmptyCheck := confData.CheckInfo["check_empty"].([]string)
		for _, check := range arrEmptyCheck {
			if arrInput[check] == nil || arrInput[check] == "" {
				tbcontext.WarningF(ctx, "check empty fail! %s ,input[%v]", check, arrInput)
				return false
			}
		}
	}

	if confData.CheckInfo["check_isset"] != nil {
		arrEmptyCheck := confData.CheckInfo["check_isset"].([]string)
		for _, check := range arrEmptyCheck {
			if _, ok := arrInput[check]; !ok {
				tbcontext.WarningF(ctx, "check isset fail! %s ,input[%v]", check, arrInput)
				return false
			}
		}
	}

	if confData.CheckInfo["check_version"] != nil {
		checkVersion := confData.CheckInfo["check_version"].(string)
		if clientvers.Compare(checkVersion, arrInput["client_version"].(string)) < 0 {
			tbcontext.WarningF(ctx, "check version fail! %s ,input[%v]", checkVersion, arrInput)
			return false
		}
	}

	return true
}

// dealConvertConfData 处理需要二次处理的conf
// @Param：arrInput：输入数据
// @Param：confData：配置数据
// @Return：返回处理后的配置数据
func dealConvertConfData(ctx context.Context, confData *types.ConfData) *types.ConfData {
	if confData.ConvertConfInfo["part_show_list"] != nil {
		var partShowList []string
		err := json.Unmarshal([]byte(confData.ConvertConfInfo["part_show_list"].(string)), &partShowList)
		if err != nil {
			tbcontext.WarningF(ctx, "unmarshal part show list fail! %v", err)
		} else {
			confData.ConfInfo["part_show_list"] = partShowList
		}
	}

	return confData
}

// getActivityInfo 获取活动模块详情
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：arrInput：输入数据
// @Param：confData：配置数据
// @Return：返回活动模块详情和错误信息
func getActivityInfo(ctx context.Context, arrInput map[string]interface{}, confData *types.ConfData) (map[string]interface{}, error) {
	var needShowPart []string
	forumTopNum := 3
	threadTopNum := 3
	activityInfo := map[string]interface{}{}

	if confData.ConfInfo["part_show_list"] != nil {
		needShowPart = confData.ConfInfo["part_show_list"].([]string)
	} else {
		for key := range confData.ActivityInfo {
			needShowPart = append(needShowPart, key)
		}
	}

	var needCallService []string
	appointInputInfo := map[string]interface{}{}
	for _, showPartKey := range needShowPart {
		if confData.ActivityInfo[showPartKey] == nil {
			continue
		}
		confActivityInfo := confData.ActivityInfo[showPartKey]
		if confActivityInfo.NeedService == 0 && confActivityInfo.ExtInfo != nil {
			activityInfo[showPartKey] = confActivityInfo.ExtInfo
			continue
		}
		if appointInputInfo[showPartKey] == nil {
			appointInputInfo[showPartKey] = make(map[string]interface{})
		}

		if confActivityInfo.NeedService == 1 && confActivityInfo.ServiceConf.Method != "" {
			if confActivityInfo.NeedUserID == 1 && arrInput["user_id"] == nil || arrInput["user_id"] == 0 {
				continue
			}
			needCallService = append(needCallService, showPartKey)
			if confActivityInfo.ReqInput != nil || len(confActivityInfo.ReqInput) > 0 {
				reqInputConfig := confActivityInfo.ReqInputConfig
				for _, input := range confActivityInfo.ReqInput {
					if arrInput[input] != nil {
						appointInputInfo[showPartKey].(map[string]interface{})[input] = arrInput[input]
					}
				}
				for reqInput, reqValue := range reqInputConfig {
					if showPartKey == "forum_bang" && reqInput == "top_num" {
						topNum, ok := common.Tvttt(reqValue, common.TTT_INT).(int)
						if ok {
							forumTopNum = topNum
						}
					}
					if showPartKey == "thread_bang" && reqInput == "top_num" {
						topNum, ok := common.Tvttt(reqValue, common.TTT_INT).(int)
						if ok {
							threadTopNum = topNum
						}
					}
					if reqValue != nil {
						appointInputInfo[showPartKey].(map[string]interface{})[reqInput] = reqValue
					}
				}
			}
		}
	}

	if len(needCallService) == 0 {
		return activityInfo, nil
	}

	type retData struct {
		Errno  int                    `json:"errno"`
		Errmsg string                 `json:"errmsg"`
		Data   map[string]interface{} `json:"data"`
	}

	multi := tbservice.Multi()
	for _, partName := range needCallService {
		serviceConf := confData.ActivityInfo[partName].ServiceConf
		tbParam := &tbservice.Parameter{
			Service: serviceConf.ServiceName,
			Input:   appointInputInfo[partName],
			Output:  &retData{},
			Method:  serviceConf.Method,
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
				tbservice.WithRalName(serviceConf.RalServiceName),
			},
		}
		multiKey := serviceConf.Method + "::" + partName
		multi.Register(ctx, multiKey, tbParam)
	}

	multi.Call(ctx)

	for _, partName := range needCallService {
		method := confData.ActivityInfo[partName].ServiceConf.Method
		multiKey := method + "::" + partName
		arrMultiRes, err := multi.GetResult(ctx, multiKey)
		arrMultiResMap, ok := arrMultiRes.(*retData)
		if !ok {
			tbcontext.FatalF(ctx, "get result fail! %s ,input[%v], res: %v", multiKey, appointInputInfo[partName], arrMultiRes)
			continue
		}
		if err != nil || arrMultiResMap.Errno != tiebaerror.ERR_SUCCESS {
			moduleLevel := confData.ActivityInfo[partName].ModuleLevel
			if moduleLevel == 1 {
				tbcontext.FatalF(ctx, "call %s error! output:[%v]", multiKey, arrMultiRes)
				return nil, err
			}
			if moduleLevel == 2 {
				tbcontext.FatalF(ctx, "call %s error! output:[%v]", multiKey, arrMultiRes)
				continue
			}
			tbcontext.WarningF(ctx, "call %s error! output:[%v]", multiKey, arrMultiRes)
			continue
		}
		if arrMultiResMap.Data == nil {
			tbcontext.WarningF(ctx, "call service empty, part[%s]", multiKey)
			continue
		}

		if confData.ActivityInfo[partName].ExtInfo != nil {
			for k, v := range confData.ActivityInfo[partName].ExtInfo {
				arrMultiResMap.Data[k] = v
			}
		}
		if partName == "forum_bang" {
			arrMultiResMap.Data["top_num"] = forumTopNum
		}
		if partName == "thread_bang" {
			arrMultiResMap.Data["top_num"] = threadTopNum
		}
		activityInfo[partName] = arrMultiResMap.Data
	}

	return activityInfo, nil
}

// buildRes 构建响应数据
// @Param：response：响应数据
// @Param：confData：配置数据
// @Param：activityInfo：活动模块详情
// @Return：无
func buildRes(ctx context.Context, baseData *types.HomeBaseData, response *materialProto.HomeResIdl,
	confData *types.ConfData, activityInfo map[string]interface{}) {
	isLogin := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("login", false), common.TTT_BOOL).(bool)
	userID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	conf := interface{}(confData.ConfInfo)
	actData := interface{}(activityInfo)
	response.Data.ConfInfo = &conf
	response.Data.ActivityInfo = &actData
	user := interface{}(map[string]interface{}{
		"is_login": isLogin,
	})
	response.Data.User = &user
	response.Data.Tbs = proto.String(tbs.CreateTbs(ctx, baseData.BaseObj.Req).Gene(isLogin))
	sampleID := baseData.Request.GetCommon().GetSampleId()
	sids := UbsAbtest.GetUbsAbtestSid(ctx, sampleID, strconv.FormatInt(userID, 10), "")
	response.Data.SampleNames = sids
	response.Data.Tbs = proto.String(tbs.CreateTbs(ctx, baseData.BaseObj.Req).Gene(isLogin))
}
