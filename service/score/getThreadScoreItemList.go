package score

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/service"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	permProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	scoreProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/score"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/score/getThreadScoreItemList"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func GetThreadScoreItemList(ctx context.Context, baseData *types.GetThreadScoreItemListData, response *getThreadScoreItemList.GetThreadScoreItemListResIdl) int {
	if baseData == nil || baseData.ForumID == 0 || baseData.ThreadID <= 0 {
		tbcontext.WarningF(ctx, "GetThreadScoreItemList params error")
		return tiebaerror.ERR_PARAM_ERROR
	}
	if len(baseData.ItemIds) == 0 {
		if baseData.Pn < 1 || baseData.Rn < 1 {
			tbcontext.WarningF(ctx, "GetThreadScoreItemList params error: item_ids is empty,pn or rn is less than 1")
			return tiebaerror.ERR_PARAM_ERROR
		}
	}
	if baseData.NeedUserScore == 0 {
		baseData.NeedUserScore = 1
	}
	if baseData.NeedScoreUserNum == 0 {
		baseData.NeedScoreUserNum = 1
	}

	// 通过tid获取score_list_id、等级、用户等级
	response.Data = &getThreadScoreItemList.DataRes{}
	scoreListID, threadInfo, userInfo, isLike, err := getLevelInfo(ctx, baseData, response)
	if err != nil {
		tbcontext.WarningF(ctx, "getLevelInfo fail, err=[%v]", err)
	}
	// scoreListID = uint64(69)
	if scoreListID <= 0 {
		tbcontext.WarningF(ctx, "getThreadScoreInfo fail, scoreListId=[%v]", scoreListID)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 调用接口获取数据
	multi := tbservice.Multi()
	getThreadScoreInfoParam := &tbservice.Parameter{
		Service: "score",
		Method:  "getThreadScoreInfo",
		Input: map[string]interface{}{
			"score_list_id":       scoreListID,
			"user_id":             baseData.UserID,
			"sort_type":           baseData.SortType,
			"need_user_score":     baseData.NeedUserScore,    // 默认是1
			"need_score_user_num": baseData.NeedScoreUserNum, // 默认是1
			"pn":                  baseData.Pn,
			"rn":                  baseData.Rn,
			"item_ids":            baseData.ItemIds,
		},
		Output: &scoreProto.GetThreadScoreInfoRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getThreadScoreInfoParam", getThreadScoreInfoParam)
	multi.Call(ctx)

	// 处理数据
	getThreadScoreInfoRes := &scoreProto.GetThreadScoreInfoRes{}
	getThreadScoreInfoInter, err := multi.GetResult(ctx, "getThreadScoreInfoParam")
	if err != nil || getThreadScoreInfoInter == nil {
		tbcontext.WarningF(ctx, "GetThreadScoreInfo call score::getThreadScoreInfo fail, output: %v, err: %v", common.ToString(getThreadScoreInfoInter), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	getThreadScoreInfoRes = getThreadScoreInfoInter.(*scoreProto.GetThreadScoreInfoRes)
	if getThreadScoreInfoRes.Errno == nil || getThreadScoreInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "GetThreadScoreInfo call score::getThreadScoreInfo, output: %v, err: %v", common.ToString(getThreadScoreInfoRes), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 赋值
	response.Data.TotalCount = proto.Uint64(getThreadScoreInfoRes.GetData().GetTotalCount())
	response.Data.ShowIndex = proto.Uint32(getThreadScoreInfoRes.GetData().GetShowIndex())
	response.Data.TotalUserNum = proto.Uint64(getThreadScoreInfoRes.GetData().GetTotalUserNum())
	response.Data.HasMore = proto.Uint32(getThreadScoreInfoRes.GetData().GetHasMore())
	response.Data.ShareIcon = proto.String(getThreadScoreInfoRes.GetData().GetShareIcon())
	response.Data.ShareMsg = proto.String(getThreadScoreInfoRes.GetData().GetShareMsg())
	response.Data.UserTotlaMsg = proto.String(getThreadScoreInfoRes.GetData().GetUserTotlaMsg())
	response.Data.UserFinishMsg = proto.String(getThreadScoreInfoRes.GetData().GetUserFinishMsg())
	response.Data.ValueMsg = proto.String(getThreadScoreInfoRes.GetData().GetValueMsg())
	response.Data.UserUnfinishMsg = proto.String(getThreadScoreInfoRes.GetData().GetUserUnfinishMsg())
	response.Data.ShareTitlePrefix = proto.String(getThreadScoreInfoRes.GetData().GetShareTitlePrefix())
	response.Data.ShareSubtitle = proto.String(getThreadScoreInfoRes.GetData().GetShareSubtitle())
	response.Data.ShareUserSumMsg = proto.String(getThreadScoreInfoRes.GetData().GetShareUserSumMsg())
	response.Data.TabTitle = proto.String(getThreadScoreInfoRes.GetData().GetTabTitle())

	// 处理item
	getThreadScoreInfoData := getThreadScoreInfoRes.GetData()
	var items []*getThreadScoreItemList.ThreadScoreItem
	for _, itemTmp := range getThreadScoreInfoData.GetItems() {
		item := &getThreadScoreItemList.ThreadScoreItem{}
		err = common.StructAToStructBCtx(ctx, itemTmp, item)
		if err != nil {
			tbcontext.WarningF(ctx, "StructAToStructBCtx fail, err = %v, input = %s", err, common.ToString(itemTmp))
		}
		items = append(items, item)
	}
	response.Data.Items = items
	replyPrivateFlag := getReplyPrivateFlag(ctx, threadInfo, userInfo, baseData)

	response.Data.AntiInfo = getAntiInfo(ctx, baseData.UserID, replyPrivateFlag, response.GetData().GetUserLevel(),
		isLike, threadInfo.GetForumName(), baseData)
	response.Data.Author = getAuthor(userInfo)
	return tiebaerror.ERR_SUCCESS
}

// 获取用户等级和打分贴限制等级
func getLevelInfo(ctx context.Context, baseData *types.GetThreadScoreItemListData,
	response *getThreadScoreItemList.GetThreadScoreItemListResIdl) (uint64, *post.ThreadInfo, *user.UserInfo, uint32, error) {
	multi := tbservice.Multi()

	// 获取用户吧内等级
	if baseData.UserID > 0 {
		var userLevelInput []map[string]uint64
		userLevelInput = append(userLevelInput, map[string]uint64{
			"user_id":  baseData.UserID,
			"forum_id": baseData.ForumID,
		})
		userLevelParams := &tbservice.Parameter{
			Service: "perm",
			Method:  "mgetUserForumLevel",
			Input: map[string]interface{}{
				"req": userLevelInput,
			},
			Output: &permProto.MgetUserForumLevelRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "perm::mgetUserForumLevel", userLevelParams)
	}

	// 获取打分贴等级信息
	threadInput := &tbservice.Parameter{
		Service: "post",
		Method:  "mgetThread",
		Input: map[string]interface{}{
			"thread_ids":      []uint64{baseData.ThreadID},
			"need_forum_name": 1,
			"need_user_data":  1,
		},
		Output: &frs.MgetThreadRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "post::mgetThread", threadInput)

	multi.Call(ctx)

	// 贴子等级
	scoreListID := uint64(0)
	threadInfo := &post.ThreadInfo{}
	userInfo := &user.UserInfo{}
	mgetThreadResInfo, err := multi.GetResult(ctx, "post::mgetThread")
	if err == nil || mgetThreadResInfo != nil {
		mgetThreadRes, ok := mgetThreadResInfo.(*frs.MgetThreadRes)

		if ok && mgetThreadRes != nil && mgetThreadRes.GetErrno() == tiebaerror.ERR_SUCCESS {
			resThreadList := mgetThreadRes.GetOutput().GetThreadList()
			threadInfo = resThreadList[baseData.ThreadID]
			userInfo = mgetThreadRes.GetOutput().GetThreadUserList()[threadInfo.GetUserId()]
			scoreInfo, err := threadInfo.GetScoreInfoStruct()
			if err == nil {
				scoreLevelLimit := scoreInfo.GetScoreLevelLimit()
				response.Data.ScoreLevelLimit = proto.Uint32(uint32(scoreLevelLimit))
				scoreListID = scoreInfo.GetScoreListId()
			} else {
				tbcontext.WarningF(ctx, "get score info error, err:[%s] output=[%s]", err.Error(), common.ToString(threadInfo))
			}
		} else {
			tbcontext.WarningF(ctx, "call mgetThread fail, thread_id=[%d], output=[%v]", baseData.ThreadID, mgetThreadResInfo)
		}
	} else {
		tbcontext.WarningF(ctx, "call mgetThread fail, thread_id=[%d], err=[%v]", baseData.ThreadID, err)
	}

	response.Data.UserLevel = proto.Uint32(0)
	isLike := uint32(0)
	if baseData.UserID > 0 {
		// 用户等级
		userLevelDataRet, err := multi.GetResult(ctx, "perm::mgetUserForumLevel")
		if err == nil && userLevelDataRet != nil {
			userLevelDataRes, ok := userLevelDataRet.(*permProto.MgetUserForumLevelRes)
			if ok && userLevelDataRes != nil && userLevelDataRes.GetErrno() == tiebaerror.ERR_SUCCESS {
				userLevelScoreInfos := userLevelDataRes.GetScoreInfo()
				if len(userLevelScoreInfos) == 0 {
					tbcontext.WarningF(ctx, "get user level info error, err:[%s] output=[%s]", err.Error(), common.ToString(userLevelScoreInfos))
				} else {
					userLevelInfo := userLevelScoreInfos[0]
					response.Data.UserLevel = proto.Uint32(userLevelInfo.GetLevelId())
					isLike = userLevelInfo.GetIsLike()
				}
			} else {
				tbcontext.WarningF(ctx, "call perm::mgetUserLevel failed, err:[%v]", userLevelDataRes)
			}
		} else {
			tbcontext.WarningF(ctx, "call perm::mgetUserLevel failed, err:[%s]", err.Error())
		}
	}

	return scoreListID, threadInfo, userInfo, isLike, nil
}

func getAntiInfo(ctx context.Context, UserID uint64, replyPrivateFlag int, levelID uint32, isLike uint32,
	forumName string, baseData *types.GetThreadScoreItemListData) *client.Anti {
	input := map[string]int64{
		"user_id": int64(UserID),
		"level":   int64(levelID),
		"is_like": int64(isLike),
	}
	voiceSwitch := service.VoiceSwitch(ctx, input, forumName)
	arrAnti := &client.Anti{
		ReplyPrivateFlag: proto.Int32(int32(replyPrivateFlag)),
		Ifvoice:          proto.Int32(int32(voiceSwitch.Switch)),
		VoiceMessage:     proto.String(voiceSwitch.Message),
	}

	if UserID > 0 {
		arrAnti.Tbs = proto.String(tbs.CreateTbs(ctx, baseData.BaseObj.Req).Gene(true))
	} else {
		arrAnti.Tbs = proto.String(tbs.CreateTbs(ctx, baseData.BaseObj.Req).Gene(false))
	}
	return arrAnti
}

func getReplyPrivateFlag(ctx context.Context, threadInfo *post.ThreadInfo, userInfo *user.UserInfo, baseData *types.GetThreadScoreItemListData) int {
	readOnly := threadInfo.GetReadonly()
	threadUserID := threadInfo.GetUserId()
	replyPrivateInfo := userInfo.GetPrivSets().GetReply()
	loginUserID := baseData.UserID

	clientType := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	clientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	replyPrivateFlag := 1
	//>=12.26 判断帖子是否被只读处置,只能主态发帖
	if clientvers.IsLegalVersion("12_26_0", clientType, clientVersion) && threadUserID > 0 &&
		loginUserID != uint64(threadUserID) && readOnly == 1 {
		return 5
	}

	if replyPrivateInfo == 1 || replyPrivateInfo == 0 {
		tbcontext.WarningF(ctx, "getReplyPrivateFlag replyPrivateInfo：%d", replyPrivateInfo)
		return replyPrivateFlag
	}

	//v12.26新增only me 仅自己可回复 逻辑 加上版本控制>=12.25.0
	if replyPrivateInfo == types.ReplyPrivateOnlyMe &&
		clientvers.IsLegalVersion("12_25_0", clientType, clientVersion) && loginUserID != uint64(threadUserID) {
		tbcontext.WarningF(ctx, "getReplyPrivateFlag userInfo:%s,threadInfo:%s，replyPrivateInfo：%d,clientType:%d,clientVersion:%s",
			common.ToString(userInfo), common.ToString(threadInfo), replyPrivateInfo, clientType, clientVersion)
		return 4
	}

	//其他同大图页线上逻辑，只处理了5和6，之前判断$intFromUser 和 $intToUser，实际非5和6时未定义，则提前； 但是没对齐pb逻辑，pb还有很多逻辑，会对非5和6如7做判断 ，12.26新提非跟版需求，前面一行处理4
	if loginUserID == uint64(threadUserID) || !php2go.InArray(replyPrivateInfo, []int32{types.ReplyPrivateOnlyFans, types.ReplyPrivateOnlyConcern}) {
		tbcontext.WarningF(ctx, "getReplyPrivateFlag loginUserID:%d,threadUserID:%d，replyPrivateInfo：%d",
			loginUserID, threadUserID, replyPrivateInfo)
		return replyPrivateFlag
	}

	var fromUser, toUser int64
	if types.ReplyPrivateOnlyFans == replyPrivateInfo {
		fromUser = int64(loginUserID)
		toUser = threadUserID
	} else if types.ReplyPrivateOnlyConcern == replyPrivateInfo {
		fromUser = threadUserID
		toUser = int64(loginUserID)
	}

	input := map[string]interface{}{
		"user_id": fromUser,
		"req_user_id": []int64{
			toUser,
		},
	}
	getUserFollowInfoRes := new(user.GetUserFollowInfoRes)
	err := tbservice.Call(ctx, "user", "getUserFollowInfo", input, getUserFollowInfoRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getUserFollowInfoRes.Errno == nil || getUserFollowInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "Call user:getUserFollowInfo failed. input:%v, output:%v, err:%v", input, common.ToString(getUserFollowInfoRes), err)
		return replyPrivateFlag
	}

	if len(getUserFollowInfoRes.GetResUserInfos()) > 0 &&
		getUserFollowInfoRes.GetResUserInfos()[0].GetIsFollowed() != 1 {
		switch replyPrivateInfo {
		case types.ReplyPrivateOnlyFans:
			replyPrivateFlag = 2
		case types.ReplyPrivateOnlyConcern:
			replyPrivateFlag = 3
		default:
			replyPrivateFlag = 1
		}
	}
	return replyPrivateFlag

}

func getAuthor(userInfo *user.UserInfo) *getThreadScoreItemList.UserInfo {
	return &getThreadScoreItemList.UserInfo{
		UserId:   proto.Int64(userInfo.GetUserId()),
		UserName: proto.String(userInfo.GetUserName()),
		Nickname: proto.String(userInfo.GetUserNickname()),
		Protrait: proto.String(tbportrait.Encode(userInfo.GetUserId(), userInfo.GetUserName(), 0)),
	}
}
