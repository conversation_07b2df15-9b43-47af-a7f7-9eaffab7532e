package search

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"github.com/golang/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/hottopic"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/recom"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/search/discover"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/php"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

var fids []int64

// 对照组：搜索发现不从推荐出
const NORMAL_SEARCH_DISCOVER_BY_REC_A = "12_64_search_discover_by_rec_a"

// 实验组：搜索发现从推荐出
const NORMAL_SEARCH_DISCOVER_BY_REC_B = "12_64_search_discover_by_rec_b"

const PATH_REC_SEARCH_SERVICE_RANK = "/tieba.idl.DefaultSearchService/rank"

const SEARCH_DISCOVER_TOPIC_IS_JUMP_SEARCH_PAGE = "search_discover_topic_is_jump_search_page"

// GetDiscoverInfo 查询指定来源的搜索框预置热门内容 热吧/热门话题
func GetDiscoverInfo(ctx context.Context, baseData *types.DiscoverBaseData, response *discover.DiscoverResIdl) int {
	objRequest := baseData.BaseObj.ObjRequest
	uid := common.Tvttt(objRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	cuid := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", 0), common.TTT_STRING).(string)

	var isHarmony bool
	clientType := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	if clientType == stcdefine.CLIENT_TYPE_HARMONY {
		isHarmony = true
	}

	scenesStr := common.Tvttt(objRequest.GetPrivateAttr("scenes", ""), common.TTT_STRING).(string)
	scenesArr := strings.Split(scenesStr, ",")
	sceneMap := make(map[string]bool)
	for _, scene := range scenesArr { // 封装map 用于判断元素是否存在
		if scene != "" {
			sceneMap[scene] = true
		}
	}
	if len(sceneMap) == 0 {
		sceneMap[ForumEntry] = true
		sceneMap[FrsPage] = true
		sceneMap[HomePage] = true
	}

	multi := tbservice.Multi()
	// 获取热吧数据
	if isHarmony || sceneMap[ForumEntry] || sceneMap[FrsPage] {
		// 鸿蒙一期中，所有场景都走热吧数据
		if mGetBtxInfoParam, ok := buildMGetBtxInfoParam(ctx); ok {
			multi.Register(ctx, "mgetBtxInfo", mGetBtxInfoParam)
		}
	}

	// 获取热门话题
	// 判断是否命中搜索发现从推荐出的实验
	isDiscoverByRec := false
	strClientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	if clientvers.Compare("12.64.0.0", strClientVersion) >= 0 {
		sids := UbsAbtest.GetUbsAbtestSid(ctx, baseData.Request.Common.GetSampleId(), strconv.FormatInt(uid, 10), types.WLUbsAbtestConfigTab)
		isDiscoverByRec = php2go.InArrayString(NORMAL_SEARCH_DISCOVER_BY_REC_B, sids)
	}

	if !isHarmony && sceneMap[HomePage] {
		if isDiscoverByRec {
			multi.Register(ctx, "getSearchDiscoverByRec", buildGetSearchDiscoverByRecParam(ctx, uid, cuid))
		} else {
			multi.Register(ctx, "getSearchRecommendWithSort", buildGetSearchRecommendWithSortParam(ctx))
		}
		multi.Register(ctx, "getSearchBoxNewHotCard", buildHotTopicParam(ctx, uid))
	}

	multi.Call(ctx)

	var topicDiscoverInfo *discover.DiscoverInfo
	var newHotPicInfo *discover.DiscoverInfo
	var recDiscoverInfo *discover.DiscoverInfo

	var forumEntryDiscoverInfo, frsPageDiscoverInfo *discover.DiscoverInfo
	discoverForumList := make([]*discover.DiscoverForumInfo, 0)
	discoverForumMap := make(map[int64]*discover.DiscoverForumInfo)

	if isHarmony || sceneMap[ForumEntry] || sceneMap[FrsPage] {
		// 请求来源是进吧页/frs页 返回热吧数据
		forumRes, err := multi.GetResult(ctx, "mgetBtxInfo")
		if err != nil {
			tbcontext.WarningF(ctx, "call forum::mgetBtxInfo fail: %v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		var res *forum.MgetBtxInfoRes
		var ok bool
		if res, ok = forumRes.(*forum.MgetBtxInfoRes); !ok {
			tbcontext.WarningF(ctx, "call forum::mgetBtxInfo fail: %v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		for _, forumInfo := range res.GetOutput() {
			if forumInfo == nil {
				continue
			}
			forumID := int64(forumInfo.GetForumName().GetForumId())
			discoverForumMap[forumID] = &discover.DiscoverForumInfo{
				ForumId:   proto.Uint64(uint64(forumID)),
				ForumName: proto.String(forumInfo.GetForumName().GetForumName()),
				Slogan:    proto.String(forumInfo.GetCard().GetSlogan()),
			}
		}
		// go map有序，需要根据fid重新排序
		for _, fid := range fids {
			if forumInfo, ok := discoverForumMap[fid]; ok {
				discoverForumList = append(discoverForumList, forumInfo)
			}
		}
		for i := 0; i < len(discoverForumList); i++ {
			discoverForumList[i].SearchTitle = proto.String(fmt.Sprintf(HotSearchTitlePrefix, i+1, discoverForumList[i].GetForumName()))
		}
		forumEntryDiscoverInfo = &discover.DiscoverInfo{
			Scene:     proto.String(ForumEntry),
			Type:      proto.String("forum"),
			ForumList: discoverForumList,
		}
		frsPageDiscoverInfo = &discover.DiscoverInfo{
			Scene:     proto.String(FrsPage),
			Type:      proto.String("forum"),
			ForumList: discoverForumList,
		}
	}

	// 首页返回话题、S卡数据，新逻辑返回推荐、S卡数据
	if !isHarmony && sceneMap[HomePage] {
		if isDiscoverByRec {
			discoverRecList := make([]*discover.DiscoverRecInfo, 0)
			recRes, err := multi.GetResult(ctx, "getSearchDiscoverByRec")
			if err != nil {
				tbcontext.WarningF(ctx, "call related-rank::getSearchDiscoverByRec fail: %v", err)
				return tiebaerror.ERR_CALL_SERVICE_FAIL
			}
			var res *recom.GetRecSearchServiceRes
			var ok bool
			if res, ok = recRes.(*recom.GetRecSearchServiceRes); !ok {
				tbcontext.WarningF(ctx, "call related-rank::getSearchDiscoverByRec fail: %v", err)
				return tiebaerror.ERR_CALL_SERVICE_FAIL
			}
			for _, recInfo := range res.SearchResult {
				if recInfo == nil {
					continue
				}
				resourceId := strconv.FormatUint(recInfo.GetId(), 10)
				resourceType := recInfo.GetType()
				resourceName := recInfo.GetName()
				resourceSearchTitle := recInfo.GetDisplayTitle()

				if resourceSearchTitle == "" {
					tbcontext.WarningF(ctx, "getSearchDiscoverByRec searchTitle is empty! recInfo:  %+v", recInfo)
					continue
				}

				tmpDiscoverRecInfo := &discover.DiscoverRecInfo{
					ResourceId:   proto.String(resourceId),
					ResourceName: proto.String(resourceName),
					ResourceType: proto.String(resourceType),
				}

				switch resourceType {
				case DiscoverResourceTypeTopic:
					// 从词表读取开关
					isJumpSearchPage, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_innersearch_switch", SEARCH_DISCOVER_TOPIC_IS_JUMP_SEARCH_PAGE)
					if err != nil {
						tbcontext.WarningF(ctx, "call wordserver.QueryKey err, key:%v, err:%v", SEARCH_DISCOVER_TOPIC_IS_JUMP_SEARCH_PAGE, err)
					}
					if isJumpSearchPage == "1" {
						tbcontext.TraceF(ctx, "%s is true", SEARCH_DISCOVER_TOPIC_IS_JUMP_SEARCH_PAGE)
						searchTitle := fmt.Sprintf(HotTopicTitlePrefix, resourceSearchTitle)
						tmpDiscoverRecInfo.SearchTitle = proto.String(searchTitle)
						tmpDiscoverRecInfo.SearchScheme = proto.String(getDiscoverDefaultSearchScheme(resourceSearchTitle))
						tmpDiscoverRecInfo.SearchBoxScheme = proto.String(getDiscoverDefaultSearchBoxScheme(searchTitle, resourceSearchTitle))
						discoverRecList = append(discoverRecList, tmpDiscoverRecInfo)
					} else {
						if resourceId == "" {
							tbcontext.WarningF(ctx, "getSearchDiscoverByRec topic resourceId is empty! recInfo:  %+v", recInfo)
						} else {
							searchTitle := fmt.Sprintf(HotTopicTitlePrefix, resourceSearchTitle)
							tmpDiscoverRecInfo.SearchTitle = proto.String(searchTitle)
							tmpDiscoverRecInfo.SearchScheme = proto.String(getDiscoverTopicSearchScheme(resourceId, resourceName))
							tmpDiscoverRecInfo.SearchBoxScheme = proto.String(getDiscoverTopicSearchBoxScheme(resourceId, resourceName, searchTitle))
						}
						discoverRecList = append(discoverRecList, tmpDiscoverRecInfo)
					}
				case DiscoverResourceTypeHotQuery, DiscoverResourceThreadKeyword:
					fallthrough
				default:
					// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/Z4V6MsLwlB/538pkaReoKofYD
					searchTitle := fmt.Sprintf(HotTopicTitlePrefix, resourceSearchTitle)
					tmpDiscoverRecInfo.SearchTitle = proto.String(searchTitle)
					tmpDiscoverRecInfo.SearchScheme = proto.String(getDiscoverDefaultSearchScheme(resourceSearchTitle))
					tmpDiscoverRecInfo.SearchBoxScheme = proto.String(getDiscoverDefaultSearchBoxScheme(searchTitle, resourceSearchTitle))
					discoverRecList = append(discoverRecList, tmpDiscoverRecInfo)
				}
			}
			recDiscoverInfo = &discover.DiscoverInfo{
				Type:    proto.String("rec"),
				RecList: discoverRecList,
			}
			// 请求来源首页，返回推荐数据
		} else {
			// 请求来源首页，返回热门话题数据
			discoverTopicList := make([]*discover.DiscoverTopicInfo, 0)
			forumRes, err := multi.GetResult(ctx, "getSearchRecommendWithSort")
			if err != nil {
				tbcontext.WarningF(ctx, "call hottopic::GetSearchRecommendWithSortRes fail: %v", err)
				return tiebaerror.ERR_CALL_SERVICE_FAIL
			}
			var res *hottopic.GetSearchRecommendWithSortRes
			var ok bool
			if res, ok = forumRes.(*hottopic.GetSearchRecommendWithSortRes); !ok {
				tbcontext.WarningF(ctx, "call hottopic::GetSearchRecommendWithSortRes fail: %v", err)
				return tiebaerror.ERR_CALL_SERVICE_FAIL
			}
			// 获取热门话题榜单取前10条数据作为搜索词条
			if len(res.Ret) > 10 {
				res.Ret = res.Ret[:10]
			}
			for _, topic := range res.Ret {
				if topic == nil {
					continue
				}
				topicID, err := strconv.Atoi(topic.GetTopicId())
				if err != nil {
					tbcontext.WarningF(ctx, "topicId Atoi err: %v", err)
					continue
				}
				searchTitle := fmt.Sprintf(HotTopicTitlePrefix, topic.GetTopicName())
				discoverTopicList = append(discoverTopicList, &discover.DiscoverTopicInfo{
					TopicId:      proto.Uint64(uint64(topicID)),
					TopicName:    proto.String(topic.GetTopicName()),
					IsVideoTopic: proto.String(topic.GetIsVideoTopic()),
					SearchTitle:  proto.String(searchTitle),
				})
			}
			topicDiscoverInfo = &discover.DiscoverInfo{
				Type:      proto.String("topic"),
				TopicList: discoverTopicList,
			}
		}

		// S卡 A卡逻辑
		hotCardResInter, err := multi.GetResult(ctx, "getSearchBoxNewHotCard")
		hotCardRes, _ := hotCardResInter.(*hottopic.GetSearchBoxNewHotCardRes)
		if err != nil || hotCardRes == nil || hotCardRes.Errno == nil ||
			hotCardRes.GetErrno() != tiebaerror.ERR_SUCCESS || hotCardRes.GetData().GetSearchBox() == nil {
			tbcontext.WarningF(ctx, "call hottopic::getSearchBoxNewHotCardRes fail: %v, %+v", err, hotCardRes)
		} else {
			titlePic, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_hottopic_conf", "discover_title_pic")
			if err != nil {
				tbcontext.WarningF(ctx, "call wordserver::QueryKey fail: %v", err)
			} else {
				topicInfos := make([]*discover.DiscoverTopicInfo, 0)
				data := hotCardRes.GetData().GetSearchBox()
				topicInfos = append(topicInfos, &discover.DiscoverTopicInfo{
					TopicId:       proto.Uint64(common.Tvttt(data.GetTopicId(), common.TTT_UINT64).(uint64)),
					TopicName:     proto.String(data.GetTopicName()),
					SearchTitle:   proto.String(data.GetTopicName()),
					IsVideoTopic:  proto.String(common.Tvttt(data.GetIsVideoTopic(), common.TTT_STRING).(string)),
					ShowFrequency: proto.Int64(int64(data.GetShowMax())),
					SearchBoxScheme: proto.String("tiebaapp://router/portal?callback=$callback&params=" +
						strings.ReplaceAll(url.QueryEscape(fmt.Sprintf(`{"page":"h5/openWebView",`+
							`"pageParams":{"url":"https://tieba.baidu.com/mo/q/hybrid-usergrow-search/searchGlobal/history?nonavigationbar=1&customfullscreen=1",`+
							`"initData":{"topicInfo":"{\"is_video_topic\":%d,\"search_title\":\"%s\",\"topic_id\":%d,\"topic_name\":\"%s\"}"}}}`,
							data.GetIsVideoTopic(), "实时热点 "+data.GetTopicName(), data.GetTopicId(), data.GetTopicName())), "+", "%20")),
					SearchScheme: proto.String(fmt.Sprintf(`com.baidu.tieba://unidispatch/topicdetail?callback=$callback&topic_id=%d&topic_name=%s&is_video_topic=%d`,
						data.GetTopicId(), strings.ReplaceAll(url.QueryEscape(data.GetTopicName()), "+", "%20"), data.GetIsVideoTopic())),
					TopicScheme: proto.String(fmt.Sprintf(
						`com.baidu.tieba://unidispatch/topicdetail?callback=$callback&topic_id=%d`+
							`&topic_name=%s&is_video_topic=%d&topic_source=%d`,
						data.GetTopicId(), strings.ReplaceAll(url.QueryEscape(data.GetTopicName()), "+", "%20"), data.GetIsVideoTopic(), 12)),
				})
				newHotPicInfo = &discover.DiscoverInfo{
					Type: proto.String("new_hot_topic"),
					HotTopic: &discover.DiscoverHotTopics{
						Title:  proto.String(titlePic),
						Topics: topicInfos,
					},
				}
			}
		}
	}

	// 封装不同来源的搜索内容
	discoverInfoList := make([]*discover.DiscoverInfo, 0)
	if sceneMap[HomePage] {
		if isHarmony {
			var discoverInfo *discover.DiscoverInfo
			if forumEntryDiscoverInfo != nil {
				discoverInfo = forumEntryDiscoverInfo
				discoverInfo.Scene = proto.String(HomePage)
				discoverInfoList = append(discoverInfoList, discoverInfo)
			}

		} else {
			if isDiscoverByRec {
				if recDiscoverInfo != nil {
					recDiscoverInfo.Scene = proto.String(HomePage)
					discoverInfoList = append(discoverInfoList, recDiscoverInfo)
				}
			} else {
				if topicDiscoverInfo != nil {
					topicDiscoverInfo.Scene = proto.String(HomePage)
					discoverInfoList = append(discoverInfoList, topicDiscoverInfo)
				}
			}

			if newHotPicInfo != nil {
				newHotPicInfo.Scene = proto.String(HomePage)
				discoverInfoList = append(discoverInfoList, newHotPicInfo)
			}
		}
	}

	if sceneMap[ForumEntry] {
		discoverInfoList = append(discoverInfoList, forumEntryDiscoverInfo)
	}
	if sceneMap[FrsPage] {
		discoverInfoList = append(discoverInfoList, frsPageDiscoverInfo)
	}
	response.Data.DiscoverList = discoverInfoList

	return tiebaerror.ERR_SUCCESS
}

// buildMGetBtxInfoParam 构建请求参数
func buildMGetBtxInfoParam(ctx context.Context) (*tbservice.Parameter, bool) {
	redisOut, err := resource.CacheTwLive.Get(ctx, RankForumRedisKey)
	if err != nil {
		tbcontext.FatalF(ctx, "call redis failed. key: %s, err: %v", RankForumRedisKey, err)
		return nil, false
	}
	if redisOut == "" {
		tbcontext.FatalF(ctx, "redis key %s, redisOut is nil", RankForumRedisKey)
		return nil, false
	}
	redisOutStr, err := php.Unserialize([]byte(redisOut))
	if err != nil {
		tbcontext.WarningF(ctx, "redis twlive get %s unserialize fail! err:%v", RankForumRedisKey, err)
		return nil, false
	}
	hotSearchFids := make([]int64, 0)
	if fidList, ok := redisOutStr.([]interface{}); ok {
		for _, fid := range fidList {
			hotSearchFids = append(hotSearchFids, fid.(int64))
		}
	} else {
		tbcontext.WarningF(ctx, "redis twlive get %s assert fail!", RankForumRedisKey)
		return nil, false
	}

	/**
	 * 原神 & 原神内鬼特殊处理 by yuzhenlei
	 *
	 * 紧急需求 by liuyang129，这里先写死，如果有原神内鬼fid过滤掉，同时将原神fid插入到5位
	 * 搜索起始页热榜干预：
	 * 1、进吧搜索页 热吧榜tab 原神内鬼吧降权至12位以后，原神吧排到第5位
	 * 2、进吧搜索页 游戏榜tab 原神吧排到第5位
	 * 原神内鬼:27497591
	 * 原神:3193775
	 */
	interveneHotFids := make([]int64, 0) // 对热门吧进行干预
	for _, fid := range hotSearchFids {
		if fid == 27497591 || fid == 3193775 {
			continue
		}
		interveneHotFids = append(interveneHotFids, fid)
	}
	interveneHotFids = insert(interveneHotFids, 5, 3193775)
	// 最多取10个热吧tid
	if len(interveneHotFids) > HotSearchDisplayNum {
		interveneHotFids = interveneHotFids[1 : HotSearchDisplayNum+1]
	}
	fids = interveneHotFids
	// 根据热吧fid获取吧信息
	return &tbservice.Parameter{
		Service: "forum",
		Method:  "mgetBtxInfo",
		Input: map[string]interface{}{
			"forum_id": fids,
		},
		Output: &forum.MgetBtxInfoRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}, true
}

// buildGetSearchRecommendWithSortParam 构建请求参数
func buildGetSearchRecommendWithSortParam(ctx context.Context) *tbservice.Parameter {
	return &tbservice.Parameter{
		Service: "hottopic",
		Method:  "getSearchRecommendWithSort",
		Input: &hottopic.GetSearchRecommendWithSortReq{
			Req: &hottopic.SortReq{
				ReLocate:  proto.Int64(4),
				Num:       proto.Int64(30),
				NeedCache: proto.Int64(1),
			},
		},
		Output: &hottopic.GetSearchRecommendWithSortRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
}

func buildHotTopicParam(ctx context.Context, uid int64) *tbservice.Parameter {
	return &tbservice.Parameter{
		Service: "hottopic",
		Method:  "getSearchBoxNewHotCard",
		Input: map[string]interface{}{
			"req": map[string]interface{}{"user_id": uid},
		},
		Output: &hottopic.GetSearchBoxNewHotCardRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
}

// buildGetSearchDiscoverByRecParam 构建请求参数
func buildGetSearchDiscoverByRecParam(ctx context.Context, uid int64, cuid string) *tbservice.Parameter {
	return &tbservice.Parameter{
		Service: "related-rank",
		Method:  PATH_REC_SEARCH_SERVICE_RANK,
		Input: map[string]any{
			"uid":    uid,
			"cuid":   cuid,
			"log_id": tbcontext.GetLogidString(ctx),
		},

		Output: &recom.GetRecSearchServiceRes{},
		Option: []tbservice.Option{
			tbservice.WithHttpUrl(PATH_REC_SEARCH_SERVICE_RANK),
			tbservice.WithHttpMethod("POST"),
			tbservice.WithConverter(tbservice.JSON),
		},
	}
}

func insert(slice []int64, index int, value int64) []int64 {
	if index < 0 || index > len(slice) {
		return slice
	}
	newSlice := make([]int64, len(slice)+1)
	copy(newSlice, slice[:index])
	newSlice[index] = value
	copy(newSlice[index+1:], slice[index:])
	return newSlice
}

// 搜索发现点击搜索按钮跳转话题scheme
func getDiscoverTopicSearchScheme(topicID string, topicName string) string {
	return fmt.Sprintf(`com.baidu.tieba://unidispatch/topicdetail?callback=$callback&topic_id=%s&topic_name=%s`,
		topicID, strings.ReplaceAll(url.QueryEscape(topicName), "+", "%20"))
}

// 搜索发现点击搜索框跳转搜索起始页二跳跳转话题scheme
func getDiscoverTopicSearchBoxScheme(topicID string, topicName string, searchTitle string) string {
	return "tiebaapp://router/portal?callback=$callback&params=" +
		strings.ReplaceAll(url.QueryEscape(fmt.Sprintf(`{"page":"h5/openWebView",`+
			`"pageParams":{"url":"https://tieba.baidu.com/mo/q/hybrid-usergrow-search/searchGlobal/history?nonavigationbar=1&customfullscreen=1",`+
			`"initData":{"topicInfo":"{\"search_title\":\"%s\",\"topic_id\":%s,\"topic_name\":\"%s\"}"}}}`,
			searchTitle, topicID, topicName)), "+", "%20")
}

// 搜索发现点击搜索按钮跳转搜索结果页scheme
func getDiscoverDefaultSearchScheme(resourceSearchTitle string) string {
	return "tiebaapp://router/portal?callback=$callback&params=" +
		strings.ReplaceAll(url.QueryEscape(fmt.Sprintf(`{"page":"h5/openWebView",`+
			`"pageParams":{"url":"https://tieba.baidu.com/mo/q/hybrid-usergrow-search/searchGlobal/result?`+
			`pageType=result&nonavigationbar=1&customfullscreen=1&keyword=%s"}}`,
			resourceSearchTitle)), "+", "%20")
}

// 搜索发现点击搜索框跳转搜索起始页二跳跳转搜索结果页scheme
func getDiscoverDefaultSearchBoxScheme(searchTitle string, resourceSearchTitle string) string {
	return "tiebaapp://router/portal?callback=$callback&params=" +
		strings.ReplaceAll(url.QueryEscape(fmt.Sprintf(`{"page":"h5/openWebView",`+
			`"pageParams":{"url":"https://tieba.baidu.com/mo/q/hybrid-usergrow-search/searchGlobal/history?nonavigationbar=1&customfullscreen=1",`+
			`"initData":{"searchInfo":"{\"search_title\":\"%s\",\"query\":\"%s\"}"}}}`,
			searchTitle, resourceSearchTitle)), "+", "%20")
}
