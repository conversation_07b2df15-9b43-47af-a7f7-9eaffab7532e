package buildoutput

import (
	"context"
	"fmt"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/constants"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/userAgent"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agent"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"

	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentuids"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentwhitelist"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/useragent/privateparams"
)

// opName 算子名称
const opName = "build_output"

type ProcessBuildOutput struct {
	CommonParams  commonparams.CommonParams
	PrivateParams privateparams.PrivateParams
	AgentUIDs     agentuids.AgentUIDs
	Agent         agent.Agent
	Whitelist     agentwhitelist.AgentWhitelist

	output *userAgent.UserAgentRes // 接口返回
}

func NewProcessBuildOutput() *ProcessBuildOutput {
	p := new(ProcessBuildOutput)
	p.output = &userAgent.UserAgentRes{}
	return p
}

func (p *ProcessBuildOutput) Process(ctx context.Context) error {
	// 不可丢失核心数据校验
	if err := p.checkCoreData(); err != nil {
		return err
	}

	// 构建返回字段
	if err := p.buildOutput(); err != nil {
		return err
	}

	return nil
}

// checkCoreData 不可丢失核心数据校验
func (p *ProcessBuildOutput) checkCoreData() error {
	if p.PrivateParams == nil || p.PrivateParams.GetUserType() == 0 {
		return fmt.Errorf("PrivateParams is nil or userType is empty")
	}

	if p.CommonParams == nil {
		return fmt.Errorf("CommonParams is nil")
	}

	if p.AgentUIDs == nil {
		return fmt.Errorf("AgentUIDs is nil")
	}

	if p.Agent == nil {
		return fmt.Errorf("agent is nil")
	}
	return nil
}

// buildOutput 构建返回数据
func (p *ProcessBuildOutput) buildOutput() error {
	p.output.AgentList = make([]*client.AibBot, 0)
	p.output.Page = p.AgentUIDs.GetPage()
	// 是否客态
	isObjective := p.PrivateParams.GetUserType() == 2
	agentMap := p.Agent.GetLegalAgentInfo(isObjective, isObjective, true)
	for _, uid := range p.AgentUIDs.GetAgentUIDs() {
		if a, ok := agentMap[uid]; ok {
			a.AuditStatus = proto.Int32(int32(constants.AuditStatusMap[int64(a.GetAuditStatus())]))
			p.output.AgentList = append(p.output.AgentList, a)
		}
	}
	// 用户是否有创建权限
	if p.Whitelist != nil {
		canCreate := int32(0)
		if p.Whitelist.GetCanCreateBot() {
			canCreate = 1
		}
		p.output.CanCreateBot = proto.Int32(canCreate)
	}
	return nil
}
