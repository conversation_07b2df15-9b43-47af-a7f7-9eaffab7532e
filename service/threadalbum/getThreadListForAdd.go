package threadalbum

import (
	"context"
	"errors"
	"time"

	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/god"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	userpostproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/userpost"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/threadalbum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/threadtype"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	threadalbumProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/threadalbum/getThreadListForAdd"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	// SourceCollection 我的收藏
	SourceCollection = 1

	// SourceUserPost 我的发帖
	SourceUserPost = 2

	// SourceViewHistory 浏览历史
	SourceViewHistory = 3
)

// GetThreadListForAdd 获取帖子列表
func GetThreadListForAdd(ctx context.Context, baseData *types.ThreadalbumForAddBaseData,
	response *threadalbumProto.ThreadListForAddrResIdl) int {

	if baseData == nil || baseData.Request == nil {
		return tiebaerror.ERR_HOME_UI_PARAM
	}
	uID, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)

	if uID <= 0 {
		return tiebaerror.ERR_HOME_UI_PARAM
	}
	hasMore, tidList, errNum := getTidList(ctx, uID, baseData.Request)
	if errNum != tiebaerror.ERR_SUCCESS {
		return errNum
	}
	if len(tidList) == 0 {
		tbcontext.WarningF(ctx, "tidList is empty")
		return tiebaerror.ERR_MODATA_EMPTY_DATA
	}

	mgetThreadRes, err := getThreadListForAdd(ctx, tidList, baseData.Request.GetFid())
	if err != nil {
		tbcontext.WarningF(ctx, "call getThreadListForAdd failed,input:%s, "+
			"output:%s, err: %v", common.ToString(baseData.Request), common.ToString(mgetThreadRes), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	var userIDs []int64
	for _, thread := range mgetThreadRes.GetOutput().GetThreadUserList() {
		if thread != nil && thread.GetUserId() > 0 {
			userIDs = append(userIDs, thread.GetUserId())
		}
	}

	if len(userIDs) == 0 {
		tbcontext.WarningF(ctx, "userIDs is empty")
		return tiebaerror.ERR_MODATA_EMPTY_DATA
	}

	threadListForAdd := make([]*threadalbumProto.ThreadListForAddItem, 0)
	userDataRes, userForumLevelRes, err := getUserDataAndUserForumLevel(ctx, userIDs, baseData.Request.GetFid())
	if err != nil {
		tbcontext.WarningF(ctx, "call getUserDataAndUserForumLevel failed,input:%s, "+
			"output:%v,%v, err: %v", common.ToString(userIDs), userDataRes, userForumLevelRes, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	if userDataRes == nil || userForumLevelRes == nil {
		tbcontext.WarningF(ctx, "userDataRes or userForumLevelRes is nil")
		return tiebaerror.ERR_MODATA_EMPTY_DATA
	}
	userForumLevelMap := make(map[uint64]*perm.UserOutput)
	for _, permData := range userForumLevelRes.GetScoreInfo() {
		userForumLevelMap[permData.GetUserId()] = permData
	}

	var threadAlbumIDs []uint64
	threadAlbumIDsMap := make(map[uint64]bool)
	for _, thread := range mgetThreadRes.GetOutput().GetThreadList() {
		if thread != nil && thread.GetThreadAlbumId() > 0 && !threadAlbumIDsMap[thread.GetThreadAlbumId()] {
			threadAlbumIDs = append(threadAlbumIDs, thread.GetThreadAlbumId())
			threadAlbumIDsMap[thread.GetThreadAlbumId()] = true
		}
	}
	albumInfoMap := map[uint64]*threadalbum.AlbumInfo{}
	if len(threadAlbumIDs) > 0 {
		req := map[string]any{
			"album_ids":  threadAlbumIDs,
			"is_manager": proto.Uint32(1),
		}
		res := &threadalbum.MgetAlbumInfoByIdsRes{}
		// 获取合辑列表
		err = tbservice.Call(ctx, "common", "mgetAlbumInfoByIds", req, res, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.GetErrno() != 0 {
			tbcontext.WarningF(ctx, "call mgetAlbumInfoByIds failed,input:%v, output:%v, err: %v", req, res, err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		albumInfoMap = res.GetData()
	}

	for _, threadID := range tidList {
		thread, ok := mgetThreadRes.GetOutput().GetThreadList()[threadID]
		if !ok || thread == nil {
			tbcontext.WarningF(ctx, "thread is %d nil", threadID)
			continue
		}
		image := getImage(thread)
		threadTypeMap := threadtype.GetThreadType(thread.GetThreadTypes())
		if value, ok := threadTypeMap["is_movideo"]; ok && value {
			image = getImageByMoVideo(thread.GetVideoInfo())
		}

		userData, ok := userDataRes.GetUserInfo()[thread.GetUserId()]
		if !ok || userData == nil {
			tbcontext.WarningF(ctx, "userData is nil")
			return tiebaerror.ERR_MODATA_EMPTY_DATA
		}

		authorName := userData.GetUserInfoNameShowV2(ctx)

		tmp := &threadalbumProto.ThreadListForAddItem{
			Tid:           proto.Uint64(thread.GetThreadId()),
			Title:         proto.String(thread.GetTitle()),
			Text:          proto.String(thread.GetAbstract()),
			Image:         proto.String(image),
			LikeNum:       proto.Uint64(uint64(thread.GetAgreeNum())),
			CommentNum:    proto.Uint64(uint64(thread.GetPostNum())),
			AuthorName:    proto.String(authorName),
			Portrait:      proto.String(tbportrait.Encode(int64(userData.GetUserId()), userData.GetUserName(), time.Now().Unix())),
			Level:         proto.Uint64(uint64(userData.GetUserGrowthLevel())),
			IsDashen:      proto.Uint64(uint64(getIsGod(userData))),
			TbLevel:       proto.Uint64(uint64(getTbLevel(thread, userForumLevelMap))),
			ThreadTypes:   getThreadTypes(threadTypeMap),
			ThreadAlbumId: thread.ThreadAlbumId,
			AlbumTitle:    proto.String(getAlbumTitle(thread, albumInfoMap)),
			VideoInfo:     getVideoInfo(ctx, thread),
		}
		threadListForAdd = append(threadListForAdd, tmp)
	}
	// 构建返回数据
	response.Data = &threadalbumProto.ThreadListForAddrRes{
		Errno:  proto.Uint32(0),
		Errmsg: proto.String("success"),
		Data: &threadalbumProto.DspData{
			HasMore:    proto.Bool(hasMore),
			ThreadList: threadListForAdd,
		},
	}
	return tiebaerror.ERR_SUCCESS
}

// getIsGod 是否是大神
func getIsGod(userData *user.UserInfo) int {
	isGod := 0
	godInfoSturct, err := userData.GetGodInfoStruct()
	if err == nil && godInfoSturct != nil {
		godInfo := god.GetUserGodInfo(godInfoSturct)
		if godInfo.GetFieldId() > 0 && godInfo.GetStatus() == god.STATUS_RECEIVE {
			isGod = 1
		}
	}
	return isGod
}

// getThreadTypes 获取帖子类型
func getThreadTypes(threadTypeMap map[string]bool) []string {
	var threadTypes []string
	for threadTypeStr := range threadTypeMap {
		threadTypes = append(threadTypes, threadTypeStr)
	}
	return threadTypes
}

// getTbLevel 获取帖子级别
func getTbLevel(thread *post.ThreadInfo, userForumLevelMap map[uint64]*perm.UserOutput) int {
	tbLevel := 1
	if value, ok := userForumLevelMap[uint64(thread.GetUserId())]; ok {
		tbLevel = int(value.GetLevelId())
	}
	return tbLevel
}

// getAlbumTitle 获取合辑信息
func getAlbumTitle(thread *post.ThreadInfo, albumInfoMap map[uint64]*threadalbum.AlbumInfo) string {
	threadAlbumTitle := ""
	threadAlbumInfo, ok := albumInfoMap[thread.GetThreadAlbumId()]
	if ok && threadAlbumInfo != nil {
		threadAlbumTitle = threadAlbumInfo.GetTitle()
	}
	return threadAlbumTitle
}

// getVideoInfo 获取视频信息
func getVideoInfo(ctx context.Context, thread *post.ThreadInfo) *threadalbumProto.VideoInfoItem {
	videoInfo := &threadalbumProto.VideoInfoItem{}
	oriVideoInfo, _ := thread.GetVideoInfoStruct(ctx)
	if oriVideoInfo == nil {
		return nil
	}

	videoInfo.ThumbnailUrl = oriVideoInfo.ThumbnailUrl
	videoInfo.VideoHeight = proto.Uint64(uint64(oriVideoInfo.GetVideoHeight()))
	videoInfo.VideoWidth = proto.Uint64(uint64(oriVideoInfo.GetVideoWidth()))
	videoInfo.VideoDuration = proto.Uint64(uint64(oriVideoInfo.GetVideoDuration()))
	videoInfo.VideoUrl = oriVideoInfo.VideoUrl

	return videoInfo
}

type VideoInfo struct {
	ThumbnailURL string `json:"thumbnail_url"`
}

// getImageByMoVideo 获取视频贴的封面图
func getImageByMoVideo(videoInfo any) string {
	if videoInfo == nil {
		return ""
	}
	video, ok := videoInfo.(*VideoInfo)
	if !ok {
		return ""
	}
	if video.ThumbnailURL != "" {
		return video.ThumbnailURL
	}
	return ""
}

// getImage 获取图片
func getImage(threadInfo *post.ThreadInfo) string {
	if threadInfo == nil {
		return ""
	}
	if threadInfo.GetMedia() != nil {
		for _, media := range threadInfo.GetMedia() {
			if media.GetType() != "pic" {
				continue
			}
			return media.GetSmallPic()
		}
	}
	return ""
}

// getUserDataAndUserForumLevel 获取用户数据和用户等级
func getUserDataAndUserForumLevel(ctx context.Context, userIDs []int64, fID uint32) (*user.MgetUserDataRes, *perm.MgetUserForumLevelRes, error) {
	multi := tbservice.Multi()
	userDataParam := &tbservice.Parameter{
		Service: "user",
		Method:  "mgetUserData",
		Input: &user.MgetUserDataReq{
			UserId:          userIDs,
			NeedGrowthLevel: proto.Uint32(1),
		},
		Output: &user.MgetUserDataRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "user::mgetUserDataEx", userDataParam)

	type UserForumLevelReqItem struct {
		UserID  int64 `json:"user_id"`
		ForumID int64 `json:"forum_id"`
	}

	userForumLevelInput := make([]UserForumLevelReqItem, len(userIDs))
	for idx, userID := range userIDs {
		userForumLevelInput[idx] = UserForumLevelReqItem{
			UserID:  userID,
			ForumID: int64(fID),
		}
	}
	permParam := &tbservice.Parameter{
		Service: "perm",
		Method:  "mgetUserForumLevel",
		Input: map[string]any{
			"req": userForumLevelInput,
		},
		Output: &perm.MgetUserForumLevelRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	tbcontext.WarningF(ctx, "call perm::mgetUserForumLevel input:%s", common.ToString(userForumLevelInput))
	multi.Register(ctx, "mgetUserForumLevel", permParam)
	multi.Call(ctx)

	userDataRes, err := multi.GetResult(ctx, "user::mgetUserDataEx")
	if err != nil {
		tbcontext.WarningF(ctx, "call mgetUserDataEx failed, err: %v", err)
		return nil, nil, err
	}

	userDataRet, ok := userDataRes.(*user.MgetUserDataRes)
	if !ok || userDataRet == nil || userDataRet.GetErrno() != tiebaerror.ERR_SUCCESS {
		return nil, nil, errors.New("call user service fail")
	}

	permRes, err := multi.GetResult(ctx, "mgetUserForumLevel")
	if err != nil {
		tbcontext.WarningF(ctx, "call perm::mgetUserForumLevel fail: %v", err)
		return nil, nil, err
	}
	permOut, ok := permRes.(*perm.MgetUserForumLevelRes)
	if !ok || permOut == nil || permOut.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call perm service fail:output:%s, err: %v",
			common.ToString(permRes), err)
		return nil, nil, errors.New("call perm service fail")
	}
	return userDataRet, permOut, nil
}

// getThreadListForAdd 获取帖子列表
func getThreadListForAdd(ctx context.Context, tIDs []uint64, fid uint32) (*frs.MgetThreadRes, error) {
	multi := tbservice.Multi()
	threadMaskInfoParam := &tbservice.Parameter{
		Service: "post",
		Method:  "getThreadMaskInfo",
		Input: &frs.GetThreadMaskInfoReq{
			ThreadIds: tIDs,
		},
		Output: &frs.GetThreadMaskInfoRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}

	multi.Register(ctx, "post::getThreadMaskInfo", threadMaskInfoParam)

	mGetThreadReq := &tbservice.Parameter{
		Service: "post",
		Method:  "mgetThread",
		Input: &frs.MgetThreadReq{
			ThreadIds:    tIDs,
			ForumId:      proto.Uint32(fid),
			NeedAbstract: proto.Uint32(1),
			CallFrom:     proto.String("client_frs"),
		},
		Output: &frs.MgetThreadRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}

	multi.Register(ctx, "post::mgetThread", mGetThreadReq)

	multi.Call(ctx)

	threadMaskInfoRes, err := multi.GetResult(ctx, "post::getThreadMaskInfo")
	if err != nil {
		tbcontext.WarningF(ctx, "call post::getThreadMaskInfo failed, err: %v", err)
		return &frs.MgetThreadRes{}, err
	}
	threadMaskInfo, ok := threadMaskInfoRes.(*frs.GetThreadMaskInfoRes)
	if !ok || threadMaskInfo == nil || threadMaskInfo.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call post::getThreadMaskInfo fail: %v", err)
		return &frs.MgetThreadRes{}, err
	}

	threadListMap := make(map[uint64]*post.ThreadMaskInfo)
	for _, thread := range threadMaskInfo.GetOutput().GetThreadInfo() {
		threadListMap[thread.GetThreadId()] = thread
	}

	threadList, err := multi.GetResult(ctx, "post::mgetThread")
	if err != nil || threadList == nil {
		tbcontext.WarningF(ctx, "call post::mgetThread failed,output:%s, "+
			"err: %v", common.ToString(threadList), err)
		return &frs.MgetThreadRes{}, err
	}
	mgetThreadRes, ok := threadList.(*frs.MgetThreadRes)
	if !ok || mgetThreadRes == nil || mgetThreadRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call post::mgetThread fail: %v", err)
		return &frs.MgetThreadRes{}, err
	}

	for _, thread := range mgetThreadRes.GetOutput().GetThreadList() {

		threadMaskInfoItem, ok := threadListMap[thread.GetThreadId()]
		if !ok {
			continue
		}
		if thread.GetIsDeleted() == 1 || threadMaskInfoItem.GetIsDeleted() == 1 || threadMaskInfoItem.GetIsUserBlocked() == 1 {
			delete(mgetThreadRes.Output.ThreadList, thread.GetThreadId())
		}

	}

	return mgetThreadRes, nil
}

// getTidList 获取tid列表
func getTidList(ctx context.Context, uID int64, request *threadalbumProto.GetThreadListForAddrReq) (bool, []uint64, int) {
	switch request.GetSource() {
	case SourceCollection:
		return getMyCollection(ctx, uID, request)
	case SourceUserPost:
		return getMyPost(ctx, uID, request)
	case SourceViewHistory:
		return getViewHistory(ctx, request)
	default:
		tbcontext.WarningF(ctx, "source %d not support", request.GetSource())
		return false, nil, tiebaerror.ERR_PARAM_ERROR
	}
}

// getMyCollection 获取我的收藏
func getMyCollection(ctx context.Context, uID int64, request *threadalbumProto.GetThreadListForAddrReq) (bool, []uint64, int) {

	// return false, []uint64{7550656340, 7550656341, 7634826667}, 0
	req := map[string]any{
		"user_id":  uID,
		"forum_id": request.GetFid(),
		"pn":       request.Pn,
		"rn":       request.Rn,
	}
	res := &userpostproto.QueryUserStoreByFidRes{}
	err := tbservice.Call(ctx, "post", "queryUserStoreByFid", req, res, tbservice.WithConverter(tbservice.JSONITER))

	if err != nil || int(res.GetErrno()) != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call post::queryUserStoreByFid failed,input:%s, output:%s, err: %v", common.ToString(req), common.ToString(res), err)
		return false, nil, tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	data := res.GetData()

	threadList := data.GetThreadList()
	return data.GetHasMore(), threadList, 0
}

// getMyPost 获取我的发帖
func getMyPost(ctx context.Context, uID int64, request *threadalbumProto.GetThreadListForAddrReq) (bool, []uint64, int) {

	offSet := (request.GetPn() - 1) * request.GetRn()
	reqUserPost := &userpostproto.QueryUserPostReq{
		Input: &userpostproto.UserPostInput{
			UserId:     proto.Int64(uID),
			Offset:     proto.Uint32(uint32(offSet)),
			ResNum:     request.Rn,
			OrderType:  proto.Uint32(uint32(1)),
			DeleteType: proto.Uint32(uint32(0)),
			IsThread:   proto.Uint32(1),
			ForumId:    request.Fid,
		},
	}
	userPostRes := &userpostproto.QueryUserPostRes{}

	err := tbservice.Call(ctx, "post", "queryUserPost", reqUserPost, userPostRes, tbservice.WithConverter(tbservice.JSONITER))

	if err != nil || int(userPostRes.GetErrno()) != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call post::queryUserPost failed,input:%s, output:%s, err: %v", common.ToString(reqUserPost), common.ToString(userPostRes), err)
		return false, nil, tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	var tidList []uint64
	for _, post := range userPostRes.GetPost().GetPost() {
		if post.GetThreadId() == 0 {
			continue
		}
		tidList = append(tidList, post.GetThreadId())
	}
	return false, tidList, tiebaerror.ERR_SUCCESS
}

// getViewHistory 获取浏览历史
func getViewHistory(ctx context.Context, request *threadalbumProto.GetThreadListForAddrReq) (bool, []uint64, int) {
	if request.GetSource() != SourceViewHistory || request.GetTidList() == nil || len(request.GetTidList()) == 0 {
		return false, nil, tiebaerror.ERR_PARAM_ERROR
	}
	return false, request.GetTidList(), tiebaerror.ERR_SUCCESS
}
