package threadalbum

import (
	"context"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/threadalbum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	threadalbumProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/threadalbum/getAlbumListForManager"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// GetAlbumListForManager 获取合辑列表
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：baseData：输入数据
// @Param：response：输出数据
// @Return：返回值是int类型，表示执行是否成功
func GetAlbumListForManager(ctx context.Context, baseData *types.ThreadalbumBaseData,
	response *threadalbumProto.AlbumListForManagerResIdl) int {

	if baseData == nil || baseData.Request == nil {
		return tiebaerror.ERR_HOME_UI_PARAM
	}
	getAlbumResult, err := getThreadAlbumList(ctx, baseData)

	if err != nil || getAlbumResult == nil || getAlbumResult.GetErrno() != 0 {
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	threadAlbumList := getAlbumResult.Data.GetAlbumList()

	albumIDs := make([]uint64, 0)
	for _, album := range threadAlbumList {
		albumIDs = append(albumIDs, album.GetId())
	}

	multi1 := tbservice.Multi()
	// 获取合辑帖子
	albumTidReq := &commonProto.MgetAlbumTidsReq{
		AlbumIds: albumIDs,
		Pn:       proto.Uint64(0),
		Rn:       proto.Uint64(1),
		Order:    proto.Uint64(1),
	}
	albumTidRes := &commonProto.MgetAlbumTidsRes{}
	getAlbumTidOpt := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
	}
	multi1.Register(ctx, "get_album_tids", &tbservice.Parameter{
		Service: "common",
		Method:  "mgetAlbumTids",
		Input:   albumTidReq,
		Output:  albumTidRes,
		Option:  getAlbumTidOpt,
	})

	// 调用common::getAlbumListCount 获取合辑列表数量
	getAlbumListCountReq := &commonProto.GetAlbumListCountReq{
		ForumId: proto.Uint64(uint64(baseData.Request.GetFid())),
		Status:  []uint64{1, 3},
	}
	getAlbumListCountRes := &commonProto.GetAlbumListCountRes{}
	getAlbumListCountOpt := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
	}
	multi1.Register(ctx, "getAlbumListCount", &tbservice.Parameter{
		Service: "common",
		Method:  "getAlbumListCount",
		Input:   getAlbumListCountReq,
		Output:  getAlbumListCountRes,
		Option:  getAlbumListCountOpt,
	})

	// 调用common::mgetAlbumUids 获取合辑作者
	albumUIDReq := &commonProto.MgetAlbumUidsReq{
		AlbumIds: albumIDs,
	}
	albumUIDRes := &commonProto.MgetAlbumUidsRes{}
	getAlbumUIDOpt := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
	}
	multi1.Register(ctx, "get_album_uids", &tbservice.Parameter{
		Service: "common",
		Method:  "mgetAlbumUids",
		Input:   albumUIDReq,
		Output:  albumUIDRes,
		Option:  getAlbumUIDOpt,
	})

	multi1.Call(ctx)

	getAlbumListCountRet := &commonProto.GetAlbumListCountRes{}
	getAlbumListCountResult, err := multi1.GetResult(ctx, "getAlbumListCount")
	if err != nil {
		tbcontext.WarningF(ctx, "getAlbumListCount output:%s, error: %v", common.ToString(getAlbumListCountResult), err)
	}
	getAlbumListCountRet, ok := getAlbumListCountResult.(*commonProto.GetAlbumListCountRes)
	if !ok || getAlbumListCountRet == nil || getAlbumListCountRet.GetErrno() != 0 {
		tbcontext.WarningF(ctx, "getAlbumListCount output:%s, error: %v", common.ToString(getAlbumListCountRet), err)
	}

	var onlineAlbumNum, waitAuditAlbumNum uint64
	if value, ok := getAlbumListCountRet.GetData().GetStatusCount()["online"]; ok {
		onlineAlbumNum = uint64(value)
	}

	if value, ok := getAlbumListCountRet.GetData().GetStatusCount()["no_audit"]; ok {
		waitAuditAlbumNum = uint64(value)
	}

	getAlbumUIDsRet := &commonProto.MgetAlbumUidsRes{}
	getAlbumUIDsResult, err := multi1.GetResult(ctx, "get_album_uids")
	if err != nil {
		tbcontext.WarningF(ctx, "getAlbumUids output:%s, error: %v", common.ToString(getAlbumUIDsResult), err)
	}
	getAlbumUIDsRet, ok = getAlbumUIDsResult.(*commonProto.MgetAlbumUidsRes)
	if !ok || getAlbumUIDsRet == nil || getAlbumUIDsRet.GetErrno() != 0 {
		tbcontext.WarningF(ctx, "getAlbumUids output:%s, error: %v", common.ToString(getAlbumUIDsRet), err)
	}

	uidList, err := getUserListBTIDs(ctx, getAlbumUIDsRet)
	if err != nil {
		tbcontext.WarningF(ctx, "getUserListBTIDs input:%s, error: %v", common.ToString(getAlbumUIDsRet), err)
	}
	uIDListMap := uidList.GetUserInfo()

	getAlbumTIDsRet := &commonProto.MgetAlbumTidsRes{}
	getAlbumTIDsResult, err := multi1.GetResult(ctx, "get_album_tids")
	if err != nil {
		tbcontext.WarningF(ctx, "getAlbumTIDs output:%s, error: %v", common.ToString(getAlbumTIDsResult), err)
	}
	getAlbumTIDsRet, ok = getAlbumTIDsResult.(*commonProto.MgetAlbumTidsRes)

	if !ok || getAlbumTIDsRet == nil || getAlbumTIDsRet.GetErrno() != 0 {
		tbcontext.WarningF(ctx, "getAlbumTIDs output:%s, error: %v", common.ToString(getAlbumTIDsRet), err)
	}

	threadList, err := getThreadListBTIDs(ctx, getAlbumTIDsRet)
	if err != nil {
		tbcontext.WarningF(ctx, "getAlbumListBTIDs input:%s, error: %v", common.ToString(getAlbumTIDsRet), err)
	}
	albumThreadList := threadList.GetOutput()

	threadListMap := map[uint64]*post.ThreadInfo{}

	if albumThreadList == nil {
		tbcontext.WarningF(ctx, "getAlbumListBTIDs output:%s", common.ToString(albumThreadList))
	} else {
		threadListMap = albumThreadList.GetThreadList()
	}

	albumList := make([]*threadalbumProto.AlbumList, 0)
	for _, album := range threadAlbumList {

		isOfficial := getIsOfficial(album.GetSource())

		threadTitle, tidCount := getThreadData(getAlbumTIDsRet, album.GetId(), threadListMap)

		authorCount, authorAvatar := getAuthorData(getAlbumUIDsRet, album.GetId(), uIDListMap)

		albumList = append(albumList, &threadalbumProto.AlbumList{
			Id:           proto.Uint64(album.GetId()),
			Title:        proto.String(album.GetTitle()),
			Image:        proto.String(album.GetImage()),
			Type:         proto.Uint64(uint64(album.GetType())),
			IsOfficial:   proto.Uint64(isOfficial),
			TopSortValue: proto.Uint64(album.GetTopSortValue()),
			ThreadCount:  proto.Uint64(tidCount),
			ThreadTitle:  proto.String(threadTitle),
			AuthorCount:  proto.Uint64(authorCount),
			AuthorAvatar: authorAvatar,
			Sort:         proto.Int32(album.GetSort()),
		})
	}

	// 构建返回数据
	response.Data = &threadalbumProto.AlbumListForManagerRes{
		Errno:  proto.Uint32(0),
		Errmsg: proto.String("success"),
		Data: &threadalbumProto.DspData{
			HasMore:           proto.Bool(getAlbumResult.Data.GetHasMore()),
			AlbumList:         albumList,
			OnlineAlbumNum:    proto.Uint64(onlineAlbumNum),    // 需要根据实际业务逻辑填充
			WaitAuditAlbumNum: proto.Uint64(waitAuditAlbumNum), // 需要根据实际业务逻辑填充
		},
	}

	return tiebaerror.ERR_SUCCESS
}

// getIsOfficial 获取是否是运营创建
func getIsOfficial(source string) uint64 {
	var isOfficial uint64
	if source != "吧主创建" {
		isOfficial = uint64(1)
	}
	return isOfficial
}

// getThreadData 获取帖子数据
func getThreadData(getAlbumTIDsRet *commonProto.MgetAlbumTidsRes, albumID uint64, threadListMap map[uint64]*post.ThreadInfo) (string, uint64) {
	threadTitle := ""
	tidCount := uint64(0)

	if tid, ok := getAlbumTIDsRet.GetData()[albumID]; ok {
		tidCount = tid.GetTidNum()
		albumTidsArr := tid.GetTids()
		if len(albumTidsArr) > 0 && threadListMap != nil {
			value, ok1 := threadListMap[albumTidsArr[0]]
			if ok1 {
				threadTitle = value.GetTitle()
			}
		}
	}
	return threadTitle, tidCount
}

// getAuthorData 获取作者数据
func getAuthorData(getAlbumUIDsRet *commonProto.MgetAlbumUidsRes, albumID uint64, uIDListMap map[int64]*user.UserInfo) (uint64, []string) {
	authorCount := uint64(0)
	authorAvatar := []string{}
	if uID, ok := getAlbumUIDsRet.GetData()[albumID]; ok {
		authorCount = uID.GetUserCount()

		for index, uIDVal := range uID.GetUids() {
			if index >= 5 {
				break
			}
			if value, ok1 := uIDListMap[int64(uIDVal)]; ok1 {

				authorAvatar = append(authorAvatar, tbportrait.Encode(value.GetUserId(), value.GetUserName(), time.Now().Unix()))
			}
		}
	}
	return authorCount, authorAvatar
}

// getThreadAlbumList 获取合辑列表
func getThreadAlbumList(ctx context.Context, baseData *types.ThreadalbumBaseData) (*commonProto.GetThreadAlbumByFidRes, error) {
	// 调用common::getThreadAlbumByFid 获取合辑列表
	getAlbumReq := &commonProto.GetThreadAlbumByFidReq{
		ForumId:     proto.Uint64(uint64(baseData.Request.GetFid())),
		AlbumStatus: proto.Int32(int32(baseData.Request.GetAlbumStatus())),
		Pn:          proto.Int32(int32(baseData.Request.GetPn())),
		Rn:          proto.Int32(int32(baseData.Request.GetRn())),
	}
	getAlbumRes := &commonProto.GetThreadAlbumByFidRes{}
	getAlbumOpt := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("common_go"),
	}

	err := tbservice.Call(ctx, "common", "getThreadAlbumByFid", getAlbumReq, getAlbumRes, getAlbumOpt...)
	if err != nil {
		tbcontext.WarningF(ctx, "call getThreadAlbumByFid input:%s, ouput:%s, err:%v", common.ToString(getAlbumReq), common.ToString(getAlbumRes), err)
		return &commonProto.GetThreadAlbumByFidRes{}, err
	}
	return getAlbumRes, nil
}

// getThreadListBTIDs 获取帖子列表
func getThreadListBTIDs(ctx context.Context, param *commonProto.MgetAlbumTidsRes) (*frs.MgetThreadRes, error) {
	var tIDs []uint64
	for _, album := range param.GetData() {
		if album == nil || album.GetTids() == nil || len(album.GetTids()) == 0 {
			continue
		}
		tIDs = append(tIDs, album.GetTids()...)
	}
	if tIDs == nil || len(tIDs) == 0 {
		return &frs.MgetThreadRes{}, nil
	}
	threadInput := map[string]any{
		"thread_ids":         tIDs,
		"need_abstract":      1,
		"forum_id":           0,
		"need_photo_pic":     1,
		"need_user_data":     0,
		"call_from":          "client_frs",
		"icon_size":          0,
		"need_forum_name":    0,
		"need_post_content":  0,
		"structured_content": 0,
	}
	opt := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
	}
	output := &frs.MgetThreadRes{}
	err := tbservice.Call(ctx, "post", "mgetThread", threadInput, output, opt...)

	if err != nil {
		tbcontext.WarningF(ctx, "call thread input:%s, ouput:%s, err:%v",
			common.ToString(threadInput), common.ToString(output), err)
		return &frs.MgetThreadRes{}, err
	}
	return output, nil
}

// getUserListBTIDs 获取帖子列表
func getUserListBTIDs(ctx context.Context, param *commonProto.MgetAlbumUidsRes) (*user.MgetUserDataRes, error) {
	var uIDs []int64
	for _, album := range param.GetData() {
		if album == nil || album.GetUids() == nil || len(album.GetUids()) == 0 {
			continue
		}
		albumUids := album.GetUids()
		index := 0
		for userIDKey := range albumUids {
			if index >= 5 {
				break
			}
			index++
			uIDs = append(uIDs, int64(userIDKey))
		}
	}
	if uIDs == nil || len(uIDs) == 0 {
		return &user.MgetUserDataRes{}, nil
	}
	Input := &user.MgetUserDataReq{
		UserId: uIDs,
	}
	opt := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
	}
	output := &user.MgetUserDataRes{}
	err := tbservice.Call(ctx, "user", "mgetUserData", Input, output, opt...)

	if err != nil {
		tbcontext.WarningF(ctx, "call thread input:%s, ouput:%s, err:%v",
			common.ToString(Input), common.ToString(output), err)
		return &user.MgetUserDataRes{}, err
	}
	return output, nil
}
