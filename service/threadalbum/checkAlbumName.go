package threadalbum

import (
	"context"

	"google.golang.org/protobuf/proto"
	threadalbumProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/threadalbum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/threadalbum/checkAlbumName"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func CheckAlbumName(ctx context.Context, baseData *types.CheckAlbumNameData, response *checkAlbumName.CheckAlbumNameResIdl) int {
	if baseData == nil || baseData.AlbumTitle == "" || baseData.ForumID <= 0 {
		tbcontext.WarningF(ctx, "GetThreadScoreItemList params error")
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 调用common方法
	input := map[string]interface{}{
		"forum_id": baseData.ForumID,
		"title":    baseData.AlbumTitle,
	}
	res := new(threadalbumProto.CheckAlbumTitleRes)
	err := tbservice.Call(ctx, "common", "checkAlbumTitle", input, res,
		tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service common:checkAlbumTitle, err = %v, input = %s, output = %s", err, common.ToString(input), common.ToString(res))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	// tbcontext.NoticeF(ctx, "call service common:checkAlbumTitle success, input = %v, output = %s", input, common.ToString(res))

	// 赋值
	response.Data.IsExist = proto.Int32(res.GetData().GetIsExist())
	return tiebaerror.ERR_SUCCESS
}
