package matchactivity

import (
	"context"
	"encoding/json"
	"fmt"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	commonproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/matchActivityQuiz"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/quiz"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	// 词表
	wordList  = "tb_wordlist_redis_activity_scene_config_%d"
	matchCard = "match_card"
)

func GetQuiz(ctx context.Context, baseData *types.MatchActivityQuizBaseData,
	response *matchActivityQuiz.MatchActivityQuizResIdl) int {
	// 获取竞猜数据
	quizID := baseData.Request.GetQuizId()
	product := baseData.Request.GetProduct()
	optionID := baseData.Request.GetQuizOptionId()
	if quizID == 0 || product == 0 || optionID == 0 {
		tbcontext.WarningF(ctx, "quiz param invalid, quizid[%d], product[%d], optionId[%d]", quizID, product, optionID)
		return tiebaerror.ERR_PARAM_ERROR
	}

	multi := tbservice.Multi()

	// 竞猜数据
	getQuizByIds := &tbservice.Parameter{
		Service: "common",
		Method:  "getQuizByIds",
		Input: map[string]interface{}{
			"quiz_id": quizID,
			"user_id": common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64),
		},
		Output: &types.GetQuizByIdsRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getQuizByIds", getQuizByIds)

	// 贴贝余额
	getUserTmoney := &tbservice.Parameter{
		Service: "common",
		Method:  "getUserTmoney",
		Input: map[string]interface{}{
			"user_id": common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64),
		},
		Output: &commonproto.GetUserTmoneyRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getUserTmoney", getUserTmoney)

	multi.Call(ctx)

	getUserTMoneyOutput, err := multi.GetResult(ctx, "getUserTmoney")
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service getUserTmoney, err = %v, input = %v, output = %v", err, common.ToString(getUserTmoney), common.ToString(getUserTMoneyOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	getQuizByIdsOutput, err := multi.GetResult(ctx, "getQuizByIds")
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service getQuizByIds, err = %v, input = %v, output = %v", err, common.ToString(getQuizByIds), common.ToString(getQuizByIdsOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	quiz := getQuizByIdsOutput.(*types.GetQuizByIdsRes).Data
	if quiz != nil {
		optionName := ""
		optionNum := int64(-1)
		for _, option := range quiz.GetOptions() {
			if option.GetQuizOptionId() == optionID {
				optionName = option.GetQuizOption()
				optionNum = option.GetTotalUserCount()
			}
		}

		// 选项不存在，报错
		if optionName == "" || optionNum == -1 {
			tbcontext.WarningF(ctx, "option id invalid")
			return tiebaerror.ERR_PARAM_ERROR
		}

		if optionNum <= 0 {
			optionNum = 1
		}

		toastContent := []*client.ToastContent{
			{
				Text: proto.String("确认支持"),
			},
			{
				Text: proto.String(fmt.Sprintf("是否下注%d贴贝支持%s？\n猜中预计赢%d贴贝\n您的贴贝余额：",
					quiz.GetMinPourCount(), optionName, quiz.GetTotalCount()/optionNum)),
			},
			{
				Text:          proto.String(fmt.Sprintf(" %d贴贝", int64(getUserTMoneyOutput.(*commonproto.GetUserTmoneyRes).GetData().GetRemainingNum()))),
				HasColor:      proto.Int32(1),
				TextColor:     proto.String("#ff392b"),
				TextColorDark: proto.String("#FFAC40"),
			},
		}
		matchCardConf, err := wordListConf(ctx, product)
		if err != nil {
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		response.Data.Quiz = quizAdapter(quiz, baseData, matchCardConf)
		response.Data.PopupDialog = &client.Toast{
			Content: toastContent,
		}
	} else {
		response.Data = new(matchActivityQuiz.DataRes)
	}

	return tiebaerror.ERR_SUCCESS
}

func wordListConf(ctx context.Context, product int64) (*matchCardWordListConf, error) {
	// 词表获取跳转地址和文案
	values, err := wordserver.QueryKey(ctx, fmt.Sprintf(wordList, product), matchCard)
	if err != nil {
		err = fmt.Errorf("wordserver QueryKey err %v, out :%+v", err, values)
		tbcontext.WarningF(ctx, "wordserver QueryKey err %v", err)
		return nil, err
	}

	matchConf := new(matchCardWordListConf)
	err = json.Unmarshal([]byte(values), matchConf)
	if err != nil {
		tbcontext.WarningF(ctx, "json.Unmarshal err %v", err)
		return nil, err
	}
	return matchConf, nil
}

// 赛前竞猜卡，词表配置
type matchCardWordListConf struct {
	TopImg struct {
		Day struct {
			ImgAndroid string `json:"img_android"`
			ImgIos     string `json:"img_ios"`
		} `json:"day"`
		Night struct {
			ImgAndroid string `json:"img_android"`
			ImgIos     string `json:"img_ios"`
		} `json:"night"`
	} `json:"top_img"`
	Jump struct {
		Url  string `json:"url"`
		Text string `json:"text"`
	} `json:"jump"`
	TotalCount struct {
		Type        int    `json:"type"`
		IconAndroid string `json:"icon_android"`
		IconIos     string `json:"icon_ios"`
		Text        string `json:"text"`
	} `json:"total_count"`
	BgImg struct {
		Android string `json:"android"`
		Ios     string `json:"ios"`
	} `json:"bg_img"`
	Button struct {
		Support    string `json:"support"`
		Supportted string `json:"supportted"`
	} `json:"button"`
}

func quizAdapter(quizInfo *quiz.QuizInfo, baseData *types.MatchActivityQuizBaseData, matchCardConf *matchCardWordListConf) *client.QuizInfo {

	moneyIcon := matchCardConf.TotalCount.IconAndroid
	if common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int) == clientvers.CLIENT_TYPE_IPHONE {
		moneyIcon = matchCardConf.TotalCount.IconIos
	}

	totalCountText := ""
	totalCountIcon := moneyIcon
	if matchCardConf.TotalCount.Type == 0 { // icon + 奖池数量
		totalCountText = fmt.Sprintf("%d", quizInfo.TotalCount)
	} else if matchCardConf.TotalCount.Type == 1 { // 奖池数量 + 文案
		totalCountIcon = ""
		totalCountText = fmt.Sprintf("%d%s", quizInfo.TotalCount, matchCardConf.TotalCount.Text)
	} else if matchCardConf.TotalCount.Type == 2 { // icon + 奖池数量 + 文案
		totalCountText = fmt.Sprintf("%d%s", quizInfo.TotalCount, matchCardConf.TotalCount.Text)
	}

	boundsType := 0
	if quizInfo.GetBounusType() == 1 || quizInfo.GetBounusType() == 4 { // 有下注
		boundsType = 1
	} else if quizInfo.GetBounusType() == 2 || quizInfo.GetBounusType() == 3 { // 无下注
		boundsType = 2
	}

	quizRes := &client.QuizInfo{
		BonusType:        proto.Int64(int64(boundsType)),
		Title:            proto.String(quizInfo.GetTitle()),
		TotalCountIcon:   proto.String(totalCountIcon),
		TotalCountText:   proto.String(totalCountText),
		TotalCount:       proto.Int64(quizInfo.GetTotalCount()),
		TotalUserCount:   proto.Int64(quizInfo.GetTotalUserCount()),
		MinPourCount:     proto.Int64(quizInfo.GetMinPourCount()),
		QuizId:           proto.Int64(quizInfo.GetQuizId()),
		BrowseUserOption: proto.Int64(quizInfo.GetBrowseUserOptional()),
		Product:          proto.Int64(quizInfo.GetProduct()),
		Options:          []*client.QuizOption{},
		Type:             proto.Int64(quizInfo.GetType()),
	}

	for _, option := range quizInfo.Options {
		quizRes.Options = append(quizRes.Options, &client.QuizOption{
			QuizOptionId:   proto.Int64(option.GetQuizOptionId()),
			QuizOption:     proto.String(option.GetQuizOption()),
			Icon:           proto.String(option.GetIcon()),
			TotalUserCount: proto.Int64(option.GetTotalUserCount()),
			ButtonImg:      proto.String(moneyIcon),
			ButtonTextSup:  proto.String("已支持"),
			ButtonTextNo:   proto.String("支持TA"),
		})
	}

	return quizRes
}
