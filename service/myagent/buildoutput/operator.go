package buildoutput

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agent"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentuids"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentwhitelist"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/myagent/privateparams"
)

// OperatorBuildOutput 算子定义
type OperatorBuildOutput struct {
	CommonParams  commonparams.CommonParams     `inject:"canLost=false,canNil=false"`
	PrivateParams privateparams.PrivateParams   `inject:"canLost=false,canNil=false"`
	AgentUIDs     agentuids.AgentUIDs           `inject:"canLost=false,canNil=false"`
	Agent         agent.Agent                   `inject:"canLost=false,canNil=false"`
	Whitelist     agentwhitelist.AgentWhitelist `inject:"canLost=true,canNil=true"`
}

func (o *OperatorBuildOutput) DoImpl(ctx *engine.Context) error {
	p := NewProcessBuildOutput()
	p.CommonParams = o.CommonParams
	p.PrivateParams = o.PrivateParams
	p.AgentUIDs = o.AgentUIDs
	p.Agent = o.Agent
	p.Whitelist = o.Whitelist

	err := p.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	// 导出数据，供controller输出返回值使用
	ctx.MustRegisterInstance(p.output)
	return nil
}
