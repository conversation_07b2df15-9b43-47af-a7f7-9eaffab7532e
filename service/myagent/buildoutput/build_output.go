package buildoutput

import (
	"context"
	"fmt"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/myAgent"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agent"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentuids"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentwhitelist"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/constants"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/myagent/privateparams"
	"strconv"
)

// opName 算子名称
const opName = "build_output"

type ProcessBuildOutput struct {
	CommonParams  commonparams.CommonParams
	PrivateParams privateparams.PrivateParams
	AgentUIDs     agentuids.AgentUIDs
	Agent         agent.Agent
	Whitelist     agentwhitelist.AgentWhitelist
	output        *myAgent.MyAgentRes // 接口返回
}

func NewProcessBuildOutput() *ProcessBuildOutput {
	p := new(ProcessBuildOutput)
	p.output = &myAgent.MyAgentRes{}
	return p
}

func (p *ProcessBuildOutput) Process(ctx context.Context) error {
	// 不可丢失核心数据校验
	if err := p.checkCoreData(); err != nil {
		return err
	}

	// 构建返回字段
	if err := p.buildOutput(); err != nil {
		return err
	}

	return nil
}

// checkCoreData 不可丢失核心数据校验
func (p *ProcessBuildOutput) checkCoreData() error {
	if p.PrivateParams == nil || p.PrivateParams.GetUserType() == 0 {
		return fmt.Errorf("PrivateParams is nil or userType is empty")
	}

	if p.CommonParams == nil {
		return fmt.Errorf("CommonParams is nil")
	}

	if p.AgentUIDs == nil {
		return fmt.Errorf("AgentUIDs is nil")
	}

	if p.Agent == nil {
		return fmt.Errorf("agent is nil")
	}
	return nil
}

// buildOutput 构建返回数据
func (p *ProcessBuildOutput) buildOutput() error {
	if tabs := p.AgentUIDs.GetAgentTabs(); len(tabs) > 0 {
		p.output.TabList = make([]*myAgent.MyAgentTab, len(tabs))
		getShowNumString := func(num int64) *string {
			if num <= 0 {
				return nil
			}
			if num < 10000 {
				return proto.String(strconv.FormatInt(num, 10))
			}
			numw := num / 10000
			numt := (num % 10000) / 1000
			if numt > 0 {
				return proto.String(fmt.Sprintf("%d.%dw", numw, numt))
			}
			return proto.String(fmt.Sprintf("%dw", numw))
		}
		for i, t := range tabs {
			p.output.TabList[i] = &myAgent.MyAgentTab{
				Name:      proto.String(t.Name),
				Scene:     proto.Uint32(uint32(t.Scene)),
				ActiveTab: proto.Bool(t.Active),
			}
			if t.Scene == agentuids.AgentTabSceneMine || t.Scene == agentuids.AgentTabSceneFollow {
				p.output.TabList[i].ShowNum = getShowNumString(t.TotalNum)
			}
		}
	}

	p.output.AgentList = make([]*client.AibBot, 0)
	p.output.Page = p.AgentUIDs.GetPage()
	// 是否客态
	isObjective := p.PrivateParams.GetUserType() == 2
	agentMap := p.Agent.GetLegalAgentInfo(isObjective, isObjective, true)
	for _, uid := range p.AgentUIDs.GetAgentUIDs() {
		if a, ok := agentMap[uid]; ok {
			a.AuditStatus = proto.Int32(int32(constants.AuditStatusMap[int64(a.GetAuditStatus())]))
			p.output.AgentList = append(p.output.AgentList, a)
		}
	}
	// 用户是否有创建权限
	if p.Whitelist != nil {
		canCreateBot := uint32(0)
		if p.Whitelist.GetCanCreateBot() {
			canCreateBot = 1
		}
		p.output.CanCreateBot = proto.Uint32(canCreateBot)
		canCreateGame := uint32(0)
		if p.Whitelist.GetCanCreateGame() {
			canCreateGame = 1
		}
		p.output.CanCreateGame = proto.Uint32(canCreateGame)
	}
	return nil
}
