package privateparams

import (
	"context"
	"fmt"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
)

// opName 算子名称
const opName = "privateparams"

type Processor struct {
	baseAction *client.UIBaseAction

	pn       int32
	rn       int32
	scene    int32
	userType int32
	portrait string
	paType   int32 // 0-智能体 1-游戏
	botUk    []string
}

func NewProcessor() *Processor {
	p := new(Processor)
	return p
}

func (p *Processor) Process(ctx context.Context) error {
	p.pn = cast.ToInt32(p.baseAction.GetOriginalParam("pn"))
	p.rn = cast.ToInt32(p.baseAction.GetOriginalParam("rn"))
	p.scene = cast.ToInt32(p.baseAction.GetOriginalParam("scene"))
	p.botUk = cast.ToStringSlice(p.baseAction.GetOriginalParam("bot_uk"))

	p.userType = ActiveMode
	p.portrait = cast.ToString(p.baseAction.GetOriginalParam("portrait"))
	p.paType = -1
	if p.baseAction.GetOriginalParam("pa_type") != nil {
		p.paType = cast.ToInt32(p.baseAction.GetOriginalParam("pa_type"))
	}
	if p.pn <= 0 {
		p.baseAction.Error(tiebaerror.ERR_PARAM_ERROR, stcdefine.GetErrMsg(tiebaerror.ERR_PARAM_ERROR), true)
		return fmt.Errorf("pn[%d] is err", p.pn)
	}
	return nil
}

func (p *Processor) GetUserType() int32 {
	return p.userType
}

func (p *Processor) GetPn() int32 {
	return p.pn
}

func (p *Processor) GetRn() int32 {
	return p.rn
}

func (p *Processor) GetScene() int32 {
	return p.scene
}

func (p *Processor) GetBotUk() []string {
	return p.botUk
}

func (p *Processor) GetPortrait() string {
	return p.portrait
}

func (p *Processor) GetPaType() int32 {
	return p.paType
}
