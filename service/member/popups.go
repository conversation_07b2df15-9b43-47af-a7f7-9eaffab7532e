package member

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/member"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/tbmall"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/member/popups"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	_MemberCenter     = "https://tieba.baidu.com/mo/q/hybrid-business-vip/tbvip?customfullscreen=1&nonavigationbar=1&noshare=1&user_skin_overlay=0"
	_MemberRecharge   = "https://tieba.baidu.com/mo/q/hybrid-business-vip/payPanel?customfullscreen=1&nonavigationbar=1&noshare=1&user_skin_overlay=0"
	PopupTypeTarget   = 1 // 定向下发弹窗
	PopupTypeRemind   = 2 // 续费提醒弹窗
	PopupTypeRecall   = 3 // 召回弹窗
	PopupTypeDiscount = 4 // 优惠弹窗

	memberWordTable = "tb_wordlist_redis_member"
	memberPopUpsURL = "popups_url_info"
)

type ExtFiledOut struct {
	ExtFiled map[string]interface{} `json:"ext_filed"`
}

type URLInfo struct {
	PicURL    string `json:"pic_url"`
	SubPicURL string `json:"sub_pic_url"`
}

func GetMemberPopups(ctx context.Context, baseData *types.GetMemberPopupsBaseData,
	response *popups.GetMemberPopupsResIdl) int {

	did := baseData.Request.GetDialogId()

	// 查弹窗信息
	popupInfo, err := getMemberPopupsByID(ctx, did)
	if err != nil {
		tbcontext.WarningF(ctx, err.Error())
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	rechargeURL := _MemberRecharge + "&dialogType=" + strconv.Itoa(int(popupInfo.GetPopupType()))

	// 不同端类型取不同字段
	sceneName := ""
	clientType := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	if clientType == clientvers.CLIENT_TYPE_IPHONE {
		sceneName = "ios_scene_id"
	} else if clientType == clientvers.CLIENT_TYPE_ANDROID {
		sceneName = "android_scene_id"
	}

	if sceneName != "" {
		// 解析ext_filed json结构
		extFiledOutput := ExtFiledOut{}
		err = json.Unmarshal([]byte(popupInfo.GetExtFiled()), &extFiledOutput)
		if err != nil {
			tbcontext.WarningF(ctx, "unmarshal popupInfo.GetExtFiled info error, err = %v", err)
		}
		extFiledInfo := extFiledOutput.ExtFiled
		if senceID, ok := extFiledInfo[sceneName]; ok {
			intSenceID := int(senceID.(float64))
			if intSenceID > 0 {
				rechargeURL = rechargeURL + "&scene_id=" + strconv.Itoa(intSenceID)
			}
		}
	}

	var activityID uint32 = 0
	// 判断弹窗类型，然后查优惠服务
	// 查活动id
	if popupInfo.GetPopupType() == PopupTypeDiscount || popupInfo.GetPopupType() == PopupTypeRemind || popupInfo.GetPopupType() == PopupTypeRecall {
		ActivityData, err := getActivityByPopupIDs(ctx, did)
		if err != nil {
			tbcontext.WarningF(ctx, err.Error())
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		if val, ok := ActivityData.GetActivityList()[uint64(did)]; ok {
			rechargeURL = rechargeURL + "&activityId=" + strconv.Itoa(int(val.GetId()))
			activityID = uint32(val.GetId())
		}
	}

	// 修改为从词表查询
	confStr, err := wordserver.QueryKey(ctx, memberWordTable, memberPopUpsURL)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to query wordlist %v, key = %v, err = %v", memberWordTable, memberPopUpsURL, err)
	}
	picURL := rechargeURL
	subPicURL := rechargeURL
	if confStr != "" {
		u := make(map[string]URLInfo)
		err = json.Unmarshal([]byte(confStr), &u)
		if err != nil {
			tbcontext.WarningF(ctx, "json to wordlist %v, key = %v, err = %v", memberWordTable, memberPopUpsURL, err)
		} else {
			didStr := strconv.Itoa(int(did))
			if info, ok := u[didStr]; ok {
				if info.PicURL != "" {
					picURL = info.PicURL
				}
				if info.SubPicURL != "" {
					subPicURL = info.SubPicURL
				}
			}
		}
	}

	response.Data = &popups.GetMemberPopupsRes{
		Pic:           proto.String(popupInfo.GetPopupPic()),
		SubPic:        proto.String(popupInfo.GetSubPic()),
		PicJumpUrl:    proto.String(picURL), //
		SubPicJumpUrl: proto.String(subPicURL),
		PicScheme:     proto.String(""),
		SubPicScheme:  proto.String(""),
		Type:          proto.Uint32(uint32(popupInfo.GetPopupType())),
		ActivityId:    proto.Uint32(activityID),
	}
	return tiebaerror.ERR_SUCCESS
}

func getMemberPopupsByID(ctx context.Context, did uint32) (*member.MemberPopupsDetail, error) {
	req := new(member.GetMemberPopupsByIDReq)
	req.Id = proto.Uint32(did)
	res := new(member.GetMemberPopupsByIDRes)
	err := tbservice.Call(ctx, "member", "getMemberPopupsByID", req, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call member::getMemberPopupsByID fail, err=%v, input=%s, output=%s",
			err, common.ToString(req), common.ToString(res))
		return nil, errors.New("meme::getPackageDetailById fail")
	}
	return res.GetData().GetPopup(), nil
}

func getActivityByPopupIDs(ctx context.Context, did uint32) (*tbmall.ActivityData, error) {
	req := new(tbmall.GetActivityByPopupIDsReq)
	req.PopupIds = []uint64{uint64(did)}
	res := new(tbmall.GetActivityByPopupIDsResp)
	err := tbservice.Call(ctx, "tbmall", "getActivityByPopupIDs", req, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call member::getMemberPopupsByID fail, err=%v, input=%s, output=%s",
			err, common.ToString(req), common.ToString(res))
		return nil, errors.New("meme::getPackageDetailById fail")
	}
	return res.GetData(), nil
}
