package raffle

import (
	"context"
	"encoding/json"
	"math/rand"
	"strconv"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/raffle/showPrize"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	_CheckUserRedisKey = "forum:raffle:check:"
	_ShowPVRedisKey    = "forum:raffle:pv:show:"
)

type BrandInfo struct {
	BrandName          string `json:"brand_name"`           // 品牌方名字
	BrandAvatar        string `json:"brand_avatar"`         // 品牌方logo
	BrandBanner        string `json:"brand_banner"`         // 品牌方logo
	Text               string `json:"text"`                 // 文字链文案
	PrizeLink          string `json:"prize_link"`           // 跳转链接
	ButtonText         string `json:"button_text"`          // 按钮文案
	PrizeText          string `json:"prize_text"`           // 祝福语
	BrandID            string `json:"brand_id"`             // 当前内容的hash 值，翻卡时透传回Server
	LimitNum           int    `json:"limit_num"`            // 每日出卡上限
	ViewStatisticsUrl  string `json:"view_statistics_url"`  //第三方曝光监测链接
	ClickStatisticsUrl string `json:"click_statistics_url"` //第三方点击监测链接
	PrizeDeeplink      string `json:"prize_deeplink"`       // 深度链接跳转APP
}

func ShowPrize(ctx context.Context, baseData *types.ShowPrize2024BaseData,
	response *showPrize.ShowPrizeResIdl) int {
	userID, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT).(int)
	clientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	productID := int(baseData.Request.GetProductId())
	source := baseData.Request.GetSource()
	if source == "" || userID <= 0 || productID <= 0 {
		tbcontext.WarningF(ctx, "user_id=%d, product_id=%d, source=%s", userID, productID, source)
		return tiebaerror.ERR_PARAM_ERROR
	}
	// 版控
	if clientvers.Compare("12.54.0.0", clientVersion) < 0 {
		tbcontext.WarningF(ctx, "showPrize input param error, clientVersion = %v", clientVersion)
		return tiebaerror.ERR_PARAM_ERROR
	}

	redisKeys := []string{"brand_info"}
	tableName := "tb_wordlist_redis_activity_raffle_config_" + strconv.Itoa(productID)
	data, err := wordserver.QueryItems(ctx, tableName, redisKeys)
	if err != nil {
		tbcontext.WarningF(ctx, "load word list err: %s", err.Error())
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	hasBrand := false
	// 存在且有值
	if val, ok := data["brand_info"]; ok && val != "" {
		BrandList := make([]*BrandInfo, 0)
		err = json.Unmarshal([]byte(val), &BrandList)
		if err != nil {
			tbcontext.WarningF(ctx, "un marshal  brand info err: %s", err.Error())
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		// 查询各品牌方当日 pv num值
		pvKey := _ShowPVRedisKey
		result, err := resource.RedisUserGrowth.HGetAll(ctx, pvKey).Result()
		if err != nil {
			return 0
		}

		// 找到第一个pv 没有超出限制品牌方信息
		brand := new(BrandInfo)
		randomlyShuffle(BrandList)
		for _, b := range BrandList {
			if val, ok := result[b.BrandID]; ok {
				intVal, _ := strconv.Atoi(val)
				if intVal < b.LimitNum {
					brand = b
					hasBrand = true
					break
				}
			} else {
				// key 不存在时说明当天第一次出卡
				brand = b
				hasBrand = true
				break
			}
		}

		response.Data = &showPrize.ShowPrizeRes{
			BrandName:          proto.String(brand.BrandName),
			BrandAvatar:        proto.String(brand.BrandBanner), // 这里有点怪，但就是这样的
			Text:               proto.String(brand.Text),
			PrizeLink:          proto.String(brand.PrizeLink),
			ButtonText:         proto.String(brand.ButtonText),
			PrizeText:          proto.String(brand.PrizeText),
			HashVal:            proto.String(brand.BrandID),
			BrandId:            proto.String(brand.BrandID),
			ViewStatisticsUrl:  proto.String(brand.ViewStatisticsUrl),
			ClickStatisticsUrl: proto.String(brand.ClickStatisticsUrl),
			PrizeDeeplink:      proto.String(brand.PrizeDeeplink),
		}

		// 记录出卡pv 活动维度
		_, err = resource.RedisUserGrowth.HIncrBy(ctx, pvKey, brand.BrandID, 1).Result()
		if err != nil {
			tbcontext.WarningF(ctx, "log pv err")
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
	}
	if hasBrand == false {
		response.Data = &showPrize.ShowPrizeRes{
			BrandName:   proto.String(""),
			BrandAvatar: proto.String(""),
			Text:        proto.String(""),
			PrizeLink:   proto.String(""),
			ButtonText:  proto.String(""),
			PrizeText:   proto.String(""),
			HashVal:     proto.String(""),
		}
	}

	// 记录用户出卡信息，用于翻卡时校验
	userShowKey := _CheckUserRedisKey + strconv.Itoa(userID) + ":" + source
	_, err = resource.RedisUserGrowth.IncrBy(ctx, userShowKey, 1).Result()
	if err != nil {
		tbcontext.WarningF(ctx, "set check user err: %s", err.Error())
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	resource.RedisUserGrowth.Expire(ctx, userShowKey, time.Minute*5)
	return tiebaerror.ERR_SUCCESS
}

func randomlyShuffle(list []*BrandInfo) {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	// 使用 Fisher-Yates 算法对数组进行随机排序
	for i := len(list) - 1; i > 0; i-- {
		j := r.Intn(i + 1)                  // 生成 0 到 i 的随机数
		list[i], list[j] = list[j], list[i] // 交换元素
	}
}
