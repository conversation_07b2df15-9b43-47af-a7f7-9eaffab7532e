package inspiration

import (
	"context"
	"errors"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"strconv"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/threadlist"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/metadata"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/bawu"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/hottopic"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/search"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/cmap"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/inspiration/getInspirationList"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"sync"
)

func DoGetInspirationList(ctx context.Context, baseData *types.GetInspirationListBaseData, output *getInspirationList.GetInspirationListResIdl) int {
	baseData.ClientType, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 1), common.TTT_INT).(int)
	baseData.ClientVersion, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	baseData.UserID, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	baseData.ForumID, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("forum_id", 0), common.TTT_INT64).(int64)
	baseData.Pn, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("pn", 0), common.TTT_UINT32).(uint32)
	baseData.Rn, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("rn", 0), common.TTT_UINT32).(uint32)

	// 初始化返回值
	output.Data = &getInspirationList.GetInspirationListRes{
		InspirationList: nil,
		HasMore:         proto.Uint32(0),
	}

	if baseData.ForumID <= 0 {
		tbcontext.WarningF(ctx, "DoGetInspirationList param invalid, input=[%s]", common.ToString(baseData))
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 根据fid获取吧兴趣标签
	forumTags, err := getForumTags(ctx, baseData.ForumID)
	if err != nil {
		tbcontext.WarningF(ctx, "DoGetInspirationList getForumTags failed, err=[%v]", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 根据标签查话题
	topicInfo, err := getTopicInfoByTags(ctx, forumTags, baseData.Pn, baseData.Rn, baseData.ForumID)
	if err != nil {
		tbcontext.WarningF(ctx, "DoGetInspirationList topicInfo failed, err=[%v]", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 根据话题查tid列表
	threadIdsMap, err := getThreadIdsByTopic(ctx, topicInfo)
	if err != nil {
		tbcontext.WarningF(ctx, "DoGetInspirationList getThreadIdsByTopic failed, err=[%v]", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 根据tid列表查帖子信息
	threadInfo, err := getThreadInfo(ctx, baseData, threadIdsMap)
	if err != nil {
		tbcontext.WarningF(ctx, "DoGetInspirationList getThreadIdsByTopic failed, err=[%v]", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 取词表配置的当前页多少条不可展 下一页不再请求
	noShowNum := uint32(5)
	keys := []string{"no_show_num"}
	arrResults, err := wordserver.QueryKeys(ctx, "tb_wordlist_redis_bashengtai", keys)
	if err != nil || len(arrResults) <= 0 || len(arrResults[0]) == 0 {
		tbcontext.WarningF(ctx, "call tb_wordlist_redis_client_sync_switch fail, or has no key, the input is %v,err is %v", keys, err)
	} else {
		val, _ := strconv.Atoi(arrResults[0])
		if val < 1 {
			tbcontext.WarningF(ctx, "the no_showcard_num is below to 1")
		} else {
			noShowNum = uint32(val)
		}
	}

	// 构建灵感列表
	inspirationList, err := buildInspirationInfo(topicInfo, threadIdsMap, threadInfo)
	if err != nil {
		tbcontext.WarningF(ctx, "DoGetInspirationList buildInspirationInfo failed, err=[%v]", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	//no_show_num 赋值
	inspirationList.NoShowNum = &noShowNum
	output.Data = inspirationList
	return tiebaerror.ERR_SUCCESS
}

// 根据fid获取吧兴趣标签
func getForumTags(ctx context.Context, forumId int64) ([]string, error) {
	multi := tbservice.Multi()
	// 从建吧记录里获取
	getCreateRecordInput := map[string]interface{}{
		"forum_id": forumId,
		"latest":   true,
		"order_by": "create_time",
		"order":    "desc",
	}
	getCreateRecordParmas := &tbservice.Parameter{
		Service: "bawu",
		Method:  "getCreateRecord",
		Input:   getCreateRecordInput,
		Output:  &bawu.GetCreateRecordRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getCreateRecord", getCreateRecordParmas)

	// 从吧属性里获取
	getBtxInfoInput := map[string]interface{}{
		"forum_id": forumId,
	}
	getBtxInfoParams := &tbservice.Parameter{
		Service: "forum",
		Method:  "getBtxInfo",
		Input:   getBtxInfoInput,
		Output:  &forum.GetBtxInfoRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getBtxInfo", getBtxInfoParams)
	multi.Call(ctx)

	// 处理结果
	getCreateRecordResInf, err := multi.GetResult(ctx, "getCreateRecord")
	if err != nil {
		tbcontext.WarningF(ctx, "call bawu::getCreateRecord failed fail, forum_id=[%d], err=[%v]", forumId, err)
		return nil, errors.New("call bawu::getCreateRecord failed")
	}
	getCreateRecordRes, ok := getCreateRecordResInf.(*bawu.GetCreateRecordRes)
	if !ok || getCreateRecordRes == nil || getCreateRecordRes.Errno == nil || getCreateRecordRes.GetErrno() != errno.CodeSuccess {
		tbcontext.WarningF(ctx, "call getHotTabThread fail, forum_id=[%d], output=[%v]", forumId, getCreateRecordResInf)
		return nil, errors.New("call bawu::getCreateRecord failed")
	}

	tags := make([]string, 0, 10)
	forumTags := &types.ForumTags{}
	if len(getCreateRecordRes.GetData().GetData()) > 0 {
		forumTagsDbStr := getCreateRecordRes.GetData().GetData()[0].GetTagExt()
		if len(forumTagsDbStr) > 0 {
			err = jsoniter.UnmarshalFromString(forumTagsDbStr, forumTags)
			if err != nil {
				tbcontext.WarningF(ctx, "getForumTags UnmarshalFromString failed, err:[%v], string:[%s]", err, forumTagsDbStr)
				return nil, err
			}
			tags = append(tags, forumTags.InterestTags.Recom...)
			tags = append(tags, forumTags.InterestTags.Self...)
		}
	}

	getBtxInfoResInf, err := multi.GetResult(ctx, "getBtxInfo")
	if err != nil {
		tbcontext.WarningF(ctx, "call forum::getBtxInfo failed fail, forum_id=[%d], err=[%v]", forumId, err)
		return nil, errors.New("call forum::getBtxInfo failed")
	}
	getBtxInfoRes, ok := getBtxInfoResInf.(*forum.GetBtxInfoRes)
	if !ok || getBtxInfoRes == nil || getBtxInfoRes.Errno == nil || getBtxInfoRes.GetErrno() != errno.CodeSuccess {
		tbcontext.WarningF(ctx, "call getHotTabThread fail, forum_id=[%d], output=[%v]", forumId, getBtxInfoResInf)
		return nil, errors.New("call forum::getBtxInfo failed")
	}
	forumTagsAttrStr := getBtxInfoRes.GetAttrs().GetForumTags()
	if len(forumTagsAttrStr) > 0 {
		err = jsoniter.UnmarshalFromString(forumTagsAttrStr, forumTags)
		if err != nil {
			tbcontext.WarningF(ctx, "getForumTags UnmarshalFromString failed, err:[%v], string:[%s]", err, forumTagsAttrStr)
			return nil, err
		}
		tags = append(tags, forumTags.InterestTags.Recom...)
		tags = append(tags, forumTags.InterestTags.Self...)
	}

	// 去重
	res := make([]string, 0, len(tags))
	tagExist := make(map[string]struct{})
	for _, tag := range tags {
		if _, ok = tagExist[tag]; ok {
			continue
		}
		res = append(res, tag)
		tagExist[tag] = struct{}{}
	}

	return res, nil
}

// 根据标签查话题
func getTopicInfoByTags(ctx context.Context, tags []string, pn, rn uint32, forumID int64) (*search.SearchTopicByTagsData, error) {
	input := map[string]interface{}{
		"words": tags,
		"pn":    fmt.Sprintf("%d", pn),
		"rn":    fmt.Sprintf("%d", rn),
		// 新增策略
		"forum_id":    forumID,
		"search_type": proto.Int32(1),
	}
	output := &search.SearchTopicByTagsRes{}
	err := tbservice.Call(ctx, "search", "searchTopicByTags", input, output, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "getForumTags call service search::searchTopicByTags failed, err = %v, input = %v, output = %v", err, input, output)
		return nil, errors.New("call search::searchTopicByTags failed")
	}
	return output.GetData(), nil
}

// 根据话题查tid列表
func getThreadIdsByTopic(ctx context.Context, topicInfo *search.SearchTopicByTagsData) (map[int64][]uint64, error) {
	multi := tbservice.Multi()
	// 遍历topicInfo拼接参数列表
	for _, topic := range topicInfo.GetList() {
		getHotTabThreadInput := map[string]interface{}{
			"topic_id":       fmt.Sprintf("%d", topic.GetTopicId()),
			"pn":             1,
			"rn":             50,
			"is_only_thread": 1,
		}

		//判断是否有showcard_topic_card字段
		if topic.ShowcardTopicType != nil {
			getHotTabThreadInput["showcard_topic_type"] = topic.GetShowcardTopicType()
		}

		getHotTabThreadParmas := &tbservice.Parameter{
			Service: "hottopic",
			Method:  "getHotTabThread",
			Input:   getHotTabThreadInput,
			Output:  &hottopic.GetHotTabThreadRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, fmt.Sprintf("getHotTabThread_%d", topic.GetTopicId()), getHotTabThreadParmas)
	}
	multi.Call(ctx)

	// 处理结果
	res := make(map[int64][]uint64)
	for _, topic := range topicInfo.GetList() {
		getHotTabThreadResInf, err := multi.GetResult(ctx, fmt.Sprintf("getHotTabThread_%d", topic.GetTopicId()))
		if err != nil {
			tbcontext.WarningF(ctx, "call getHotTabThread fail, topic_id=[%d], err=[%v]", topic.GetTopicId(), err)
			continue
		}
		getHotTabThreadRes, ok := getHotTabThreadResInf.(*hottopic.GetHotTabThreadRes)
		if !ok || getHotTabThreadRes == nil || getHotTabThreadRes.Errno == nil || getHotTabThreadRes.GetErrno() != errno.CodeSuccess {
			tbcontext.WarningF(ctx, "call getHotTabThread fail, topic_id=[%d], output=[%v]", topic.GetTopicId(), getHotTabThreadResInf)
			continue
		}
		threadList := getHotTabThreadRes.GetData().GetThreadList()
		threadIds := make([]uint64, 0, len(threadList))
		for _, thread := range threadList {
			threadIds = append(threadIds, thread.GetThreadId())
		}
		res[topic.GetTopicId()] = threadIds
	}
	return res, nil
}

// 根据tid列表查帖子信息
func getThreadInfo(ctx context.Context, baseData *types.GetInspirationListBaseData, threadIdsMap map[int64][]uint64) (map[uint64]*client.ThreadInfo, error) {
	threadIds := make([]uint64, 0, 300)
	threadIdsExist := make(map[uint64]struct{})
	for _, ids := range threadIdsMap {
		for _, id := range ids {
			// 已记录过，不重复记录
			if _, ok := threadIdsExist[id]; ok {
				continue
			}
			// 未记录过，记录
			threadIds = append(threadIds, id)
			threadIdsExist[id] = struct{}{}
		}
	}

	threadIdChunks := chunkThreadIds(threadIds, 100)
	threadListArr := make([][]*metadata.ThreadInfoOutWithThreadTypes, len(threadIdChunks))

	var wg sync.WaitGroup
	wg.Add(len(threadIdChunks))
	for i, threadIdChunk := range threadIdChunks {
		go func(i int, threadIdChunk []uint64) {
			defer wg.Done()
			threadListParam := threadlist.ThreadListParam{
				ArrTid:        threadIdChunk,
				Fid:           uint32(baseData.ForumID),
				ClientType:    baseData.ClientType,
				ClientVersion: baseData.ClientVersion,
				UserId:        int64(baseData.UserID),
				ArrStrategy:   cmap.New(),
			}
			threadListArr[i] = threadListParam.GetThreadList(ctx, baseData.BaseObj)
		}(i, threadIdChunk)
	}
	wg.Wait()

	res := map[uint64]*client.ThreadInfo{}
	for _, threadList := range threadListArr {
		for _, threadInfo := range threadList {
			// 过滤被屏蔽的贴子
			if threadInfo.ClientThreadInfo.GetIsDeleted() == 1 ||
				threadInfo.ClientThreadInfo.GetIsFrsMask() == 1 ||
				threadInfo.ClientThreadInfo.GetIsPartialVisible() == 1 {
				tbcontext.WarningF(ctx, "thread is masked, tid=[%d]", threadInfo.GetThreadId())
				continue
			}
			// 过滤纯文字贴
			if len(threadInfo.ClientThreadInfo.GetMedia()) == 0 && threadInfo.ClientThreadInfo.GetVideoInfo() == nil {
				tbcontext.WarningF(ctx, "thread media is nil, tid=[%d]", threadInfo.GetThreadId())
				continue
			}
			res[uint64(threadInfo.GetThreadId())] = threadInfo.ClientThreadInfo
		}
	}
	return res, nil
}

// 构建灵感列表
func buildInspirationInfo(
	topicInfo *search.SearchTopicByTagsData,
	threadIdsMap map[int64][]uint64,
	threadInfoMap map[uint64]*client.ThreadInfo,
) (*getInspirationList.GetInspirationListRes, error) {
	inspirationList := make([]*getInspirationList.InspirationInfo, 0, len(topicInfo.GetList()))
	for _, topic := range topicInfo.GetList() {
		resTopicInfo := &getInspirationList.TopicInfo{
			TopicId:      proto.String(fmt.Sprintf("%d", topic.GetTopicId())),
			TopicName:    proto.String(topic.GetTopicName()),
			TopicImage:   proto.String(topic.GetAvater()),
			PublisherNum: proto.Uint64(uint64(topic.GetUserCount())),
			BrowseNum:    proto.Uint64(uint64(topic.GetShowPv())),
		}
		resThreadList := make([]*client.ThreadInfo, 0, len(threadIdsMap[topic.GetTopicId()]))
		for _, threadID := range threadIdsMap[topic.GetTopicId()] {
			if threadInfo, ok := threadInfoMap[threadID]; ok {
				resThreadList = append(resThreadList, threadInfo)
			}
		}
		inspirationList = append(inspirationList, &getInspirationList.InspirationInfo{
			TopicInfo:  resTopicInfo,
			ThreadList: resThreadList,
		})
	}
	res := &getInspirationList.GetInspirationListRes{
		InspirationList: inspirationList,
		HasMore:         proto.Uint32(topicInfo.GetHasMore()),
	}
	return res, nil
}

func chunkThreadIds(threadIds []uint64, batchSize int) [][]uint64 {
	var chunks [][]uint64
	if len(threadIds) <= 0 || batchSize <= 0 {
		return chunks
	}
	for batchSize < len(threadIds) {
		threadIds, chunks = threadIds[batchSize:], append(chunks, threadIds[0:batchSize:batchSize])
	}
	chunks = append(chunks, threadIds)
	return chunks
}
