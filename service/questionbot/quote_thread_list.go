package questionbot

import (
	"context"
	"errors"
	"fmt"

	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/redis"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/feeddomain"
	postproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	pbproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/pb"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	quoteproto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/questionbot/quoteThreadList"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/sse/aigc/bdtAigcAnswer"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
)

func QuoteThreadList(ctx context.Context, uiBase *uiclient.UIBaseAction, request *quoteproto.QuoteThreadListReqIdl,
	response *quoteproto.QuoteThreadListResIdl) int {
	threadID := request.GetData().GetTid()
	postID := request.GetData().GetPid()
	if threadID == 0 {
		tbcontext.WarningF(ctx, "param error: threadID[%d]", threadID)
		return tiebaerror.ERR_PARAM_ERROR
	}

	var quoteList []uint64
	if postID != 0 {
		// 帖子ID不为0，pb评论区引用帖子
		rawPost, err := getPostInfo(ctx, postID)
		if err != nil {
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		bdt, err := rawPost.GetBdtSearchInfoStruct()
		if err != nil {
			tbcontext.WarningF(ctx, "getBdtSearchInfoStruct error: [%v]", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		quoteList = bdt.GetBdtThreadList()
	} else {
		// 帖子ID为0，包打听浮层
		content, err := getReplyContent(ctx, threadID)
		if err != nil {
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		quoteList = content.GetRecomInfo().GetThreadList()
	}

	var threads []*client.ThreadInfo
	var threadIDs map[uint64]bool

	g := &gtask.Group{
		AllowSomeFail: true,
	}
	g.Go(func() (err error) {
		threads, err = queryFeedByTIDs(ctx, uiBase, quoteList)
		return err
	})
	g.Go(func() (err error) {
		threadIDs, err = getThreadMaskInfo(ctx, quoteList)
		return err
	})
	_, err := g.Wait()
	if err != nil {
		tbcontext.WarningF(ctx, "call service err:%v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	threadList := make([]*client.ThreadInfo, 0, len(threads))
	for _, v := range threads {
		if threadIDs[uint64(v.GetTid())] {
			threadList = append(threadList, v)
		}
	}

	response.Data = &quoteproto.QuoteThreadListRes{
		ThreadList: threadList,
	}

	return tiebaerror.ERR_SUCCESS
}

// queryFeedByTIDs 调用feed-domain服务获取帖子信息
func queryFeedByTIDs(ctx context.Context, uiBase *uiclient.UIBaseAction, threadIDs []uint64) ([]*client.ThreadInfo, error) {
	if len(threadIDs) == 0 {
		return []*client.ThreadInfo{}, nil
	}
	rawReq := uiBase.ObjRequest
	qInput := &feeddomain.QueryFeedByTidsReq{
		Tids:          threadIDs,
		UserId:        proto.Uint64(cast.ToUint64(rawReq.GetCommonAttr("user_id", 0))),
		ClientType:    proto.Uint32(cast.ToUint32(rawReq.GetCommonAttr("client_type", 0))),
		ClientVersion: proto.String(cast.ToString(rawReq.GetCommonAttr("client_version", ""))),
		Model:         proto.String(cast.ToString(rawReq.GetCommonAttr("model", ""))),
		SampleId:      proto.String(cast.ToString(rawReq.GetCommonAttr("sample_id", ""))),
		OsVersion:     proto.String(cast.ToString(rawReq.GetCommonAttr("os_version", ""))),
		Idfa:          proto.String(cast.ToString(rawReq.GetCommonAttr("idfa", ""))),
		PhoneImei:     proto.String(cast.ToString(rawReq.GetCommonAttr("phone_imei", ""))),
		AndroidId:     proto.String(cast.ToString(rawReq.GetCommonAttr("android_id", ""))),
		NetType:       proto.String(cast.ToString(rawReq.GetCommonAttr("net_type", ""))),
		UserAgent:     proto.String(cast.ToString(rawReq.GetCommonAttr("user_agent", ""))),
		Cuid:          proto.String(cast.ToString(rawReq.GetCommonAttr("cuid", ""))),
	}
	qOutput := &feeddomain.QueryFeedByTidsRes{}
	err := tbservice.Call(ctx, "feed-domain", "queryFeedByTids", qInput, qOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || qOutput.Errno == nil || *qOutput.Errno != tiebaerror.ERR_SUCCESS || qOutput.Data == nil || len(qOutput.Data.FeedList) == 0 {
		tbcontext.WarningF(ctx, "call feed-domain::queryFeedByTids fail err:[%v], input:[%v], output:[%v]",
			err, common.ToString(qInput), common.ToString(qOutput))
		return nil, err
	}
	ret := make([]*client.ThreadInfo, 0, len(qOutput.Data.FeedList))
	for _, v := range qOutput.Data.FeedList {
		ret = append(ret, v.ThreadInfo)
	}
	return ret, nil
}

// getPostInfo 调用帖子服务获取帖子信息
func getPostInfo(ctx context.Context, postID uint64) (*postproto.Post, error) {
	if postID == 0 {
		return nil, errors.New("postID is zero")
	}
	pInput := &pbproto.GetPostInfoReq{
		PostIds: []uint64{postID},
	}
	pOutput := &pbproto.GetPostInfoRes{}
	err := tbservice.Call(ctx, "post", "getPostInfo", pInput, pOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || pOutput.GetErrno() != tiebaerror.ERR_SUCCESS || len(pOutput.GetOutput()) == 0 {
		tbcontext.WarningF(ctx, "call post::getPostInfo fail err:[%v], input:[%v], output:[%v]",
			err, common.ToString(pInput), common.ToString(pOutput))
		return nil, err
	}
	postInfo := pOutput.GetOutput()[0]
	return postInfo, nil
}

// getReplyContent 获取回复内容
func getReplyContent(ctx context.Context, threadID uint64) (*bdtAigcAnswer.BdtAigcAnswerData, error) {
	infoKey := fmt.Sprintf("bdt_aigc_answer_%d", threadID)
	infoRes, err := resource.RedisMsglogic.Get(ctx, infoKey).Result()
	if err != nil && !errors.Is(err, redis.ErrNil) {
		tbcontext.WarningF(ctx, "redis get error:[%v], key:[%s]", err, infoKey)
		return nil, err
	} else if errors.Is(err, redis.ErrNil) {
		tbcontext.WarningF(ctx, "redis get nil, key:[%s]", infoKey)
		return nil, err
	}
	content := &bdtAigcAnswer.BdtAigcAnswerRes{}
	err = jsoniter.UnmarshalFromString(infoRes, content)
	if err != nil {
		tbcontext.WarningF(ctx, "json unmarshal error: %v, infoRes: %s", err, infoRes)
		return nil, err
	}
	return content.GetData(), nil
}

// getThreadMaskInfo 过滤不可展示的贴子
func getThreadMaskInfo(ctx context.Context, threadIDs []uint64) (map[uint64]bool, error) {
	res := make(map[uint64]bool, len(threadIDs))
	tInput := &frs.GetThreadMaskInfoReq{
		ThreadIds: threadIDs,
	}
	tOutput := &frs.GetThreadMaskInfoRes{}
	err := tbservice.Call(ctx, "post", "getThreadMaskInfo", tInput, tOutput,
		tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || tOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call post::getThreadMaskInfo fail err:[%v], input:[%v], output:[%v]",
			err, common.ToString(tInput), common.ToString(tOutput))
		return nil, err
	}
	for _, v := range tOutput.GetOutput().GetThreadInfo() {
		if v.GetIsExist() == 0 || v.GetIsDeleted() == 1 || v.GetIsPartialVisible() == 1 || v.GetIsUserFiltered() == 1 {
			continue
		}
		res[v.GetThreadId()] = true
	}
	return res, nil
}
