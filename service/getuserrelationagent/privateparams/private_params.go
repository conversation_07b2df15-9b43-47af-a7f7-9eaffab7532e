/**
 * @Author: gongruiyang
 * @Description:
 * @File:  private_params
 * @Date: 2025/02/10 16:14
 */

package privateparams

import (
	"context"
	"fmt"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
)

// opName 算子名称
const opName = "private_params"

type Processor struct {
	baseAction *client.UIBaseAction

	pn uint64
	rn uint64
}

func NewProcessor() *Processor {
	p := new(Processor)
	return p
}

func (p *Processor) Process(ctx context.Context) error {

	p.pn = cast.ToUint64(p.baseAction.GetOriginalParam("pn"))
	p.rn = cast.ToUint64(p.baseAction.GetOriginalParam("rn"))

	if p.pn <= 0 || p.rn <= 0 {
		p.baseAction.Error(tiebaerror.ERR_PARAM_ERROR, stcdefine.GetErrMsg(tiebaerror.ERR_PARAM_ERROR), true)
		return fmt.Errorf("pn[%d] or rn[%d] is err", p.pn, p.rn)
	}

	return nil
}

func (p *Processor) GetPn() uint64 {
	return p.pn
}

func (p *Processor) GetRn() uint64 {
	return p.rn
}
