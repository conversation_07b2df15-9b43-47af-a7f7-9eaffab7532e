package wordlist

import (
	"context"

	"github.com/spf13/cast"

	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
)

// opName 算子名称
const opName = "wordlist"

const (
	wordlistTable         = "tb_wordlist_redis_aichat_uidlist_conf"
	addThreadBotMaxNumKey = "add_thread_max_bot_num"
)

type ProcessWordlist struct {
	WordlistConf *Config
}

func NewProcessWordlist() *ProcessWordlist {
	p := &ProcessWordlist{
		WordlistConf: &Config{
			AddThreadMaxBotNum: 10,
		},
	}
	return p
}

func (p *ProcessWordlist) Process(ctx context.Context) error {
	maxBotNumStr, err := wordserver.QueryKey(ctx, wordlistTable, addThreadBotMaxNumKey)
	if err != nil {
		tbcontext.WarningF(ctx, "wordlist server query fail: %v", err)
		return err
	}
	// 默认发帖可挂载智能体为10个
	var addThreadMaxBotNum uint64
	if maxBotNumStr != "" {
		botMaxNum := cast.ToUint64(maxBotNumStr)
		if botMaxNum > 0 {
			addThreadMaxBotNum = botMaxNum
		}
	}
	if addThreadMaxBotNum > 0 {
		p.WordlistConf.AddThreadMaxBotNum = addThreadMaxBotNum
	}
	return nil
}

// GetWordlistConf 获取是否能创建智能体
func (p *ProcessWordlist) GetWordlistConf() *Config {
	return p.WordlistConf
}
