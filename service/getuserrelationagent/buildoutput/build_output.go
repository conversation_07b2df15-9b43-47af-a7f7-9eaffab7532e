/**
 * @Author: gong<PERSON>iyang
 * @Description:
 * @File:  build_output
 * @Date: 2025/02/10 16:48
 */

package buildoutput

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getuserrelationagent/wordlist"

	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getUserRelationAgent"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agent"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentuids"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
)

// opName 算子名称
const opName = "build_output"

type ProcessBuildOutput struct {
	CommonParams commonparams.CommonParams
	//PrivateParams privateparams.PrivateParams

	AgentUIDs agentuids.AgentUIDs
	Agent     agent.Agent
	WordList  wordlist.Wordlist

	output *getUserRelationAgent.GetUserRelationAgentRes // 接口返回
}

func NewProcessBuildOutput() *ProcessBuildOutput {
	p := new(ProcessBuildOutput)
	p.output = &getUserRelationAgent.GetUserRelationAgentRes{}
	return p
}

func (p *ProcessBuildOutput) Process(ctx context.Context) error {
	// 不可丢失核心数据校验
	if err := p.checkCoreData(); err != nil {
		return err
	}

	// 构建返回字段
	if err := p.buildOutput(); err != nil {
		return err
	}

	return nil
}

// checkCoreData 不可丢失核心数据校验
func (p *ProcessBuildOutput) checkCoreData() error {
	//if p.PrivateParams == nil {
	//	return fmt.Errorf("PrivateParams is nil or userType is empty")
	//}

	if p.CommonParams == nil {
		return fmt.Errorf("CommonParams is nil")
	}

	if p.AgentUIDs == nil {
		return fmt.Errorf("AgentUIDs is nil")
	}

	if p.Agent == nil {
		return fmt.Errorf("agent is nil")
	}

	return nil
}

// buildOutput 构建返回数据
func (p *ProcessBuildOutput) buildOutput() error {
	p.output.List = make([]*client.AibBot, 0)
	p.output.Page = &getUserRelationAgent.PageInfo{}

	agentUids := p.AgentUIDs.GetAgentUIDs()
	agentInfo := p.Agent.GetLegalAgentInfo(true, true, true)
	page := p.AgentUIDs.GetPage()

	for _, uid := range agentUids {
		aibBot := agentInfo[uid]
		if aibBot != nil {
			p.output.List = append(p.output.List, aibBot)
		}
	}

	p.output.Page.TotalPage = proto.Uint64(uint64(page.GetTotalPage()))
	p.output.Page.CurPage = proto.Uint64(uint64(page.GetCurrentPage()))

	if p.WordList != nil && p.WordList.GetWordlistConf() != nil {
		p.output.Config = &getUserRelationAgent.Config{
			AddThreadMaxBotNum: proto.Uint64(p.WordList.GetWordlistConf().AddThreadMaxBotNum),
		}
	}

	return nil
}
