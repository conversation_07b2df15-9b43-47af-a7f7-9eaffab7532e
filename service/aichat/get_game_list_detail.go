package aichat

import (
	"context"
	"fmt"
	"strconv"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/base"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getGameListDetail"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// DoGetGameProcess 获取游戏剧情信息和游戏进度信息
func DoGetGameListDetail(ctx context.Context, baseData *types.GetGameListDetailBaseData, response *getGameListDetail.GetGameListDetailResIdl) int {
	baseData.UserID, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	baseData.ChatUk, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("chat_uk", ""), common.TTT_STRING).(string)

	// 校验入参role_type是否合法
	if baseData.UserID <= 0 || baseData.ChatUk == "" {
		tbcontext.WarningF(ctx, "getGameProcess check user_id and chat_uk error")
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 根据chat_uk获取基础信息 getAiInteractiveGamePlotByChatUk
	getBotGameInfoByUkInput := &chat.GetAiInteractiveGamePlotByChatUKReq{
		Uid:    proto.Int64(int64(baseData.UserID)),
		ChatUk: []string{baseData.ChatUk},
	}
	getBotGameInfoByUkOutput := &chat.GetAiInteractiveGamePlotByChatUKRes{}
	err := tbservice.Call(ctx, "chat", "getAiInteractiveGamePlotByChatUk",
		getBotGameInfoByUkInput,
		&getBotGameInfoByUkOutput,
		tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getBotGameInfoByUkOutput.Errno == nil || getBotGameInfoByUkOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:getAiInteractiveGamePlotByChatUk fail, err:[%v], output:[%s]", err, common.ToString(getBotGameInfoByUkOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 先用这个uk看一下是否有相关数据，没有数据的直接返回
	if _, ok := getBotGameInfoByUkOutput.Data.GetAiInteractiveGamePlot()[baseData.ChatUk]; !ok {
		response.Data.GameList = make([]*base.AiBotGameInfo, 0)
		return tiebaerror.ERR_SUCCESS
	}
	//有数据的话把数据依次拿出来遍历,填充回response的字段
	gameList := make([]*base.AiBotGameInfo, 0, len(getBotGameInfoByUkOutput.Data.GetAiInteractiveGamePlot()[baseData.ChatUk].Plot))

	plotInfoList := getBotGameInfoByUkOutput.Data.GetAiInteractiveGamePlot()[baseData.ChatUk].Plot
	for _, plotInfo := range plotInfoList {
		//获取游戏进度信息
		//用户剧情最新记录数据查询 chat::mgetUserPlotRecord
		plotIDTmp := plotInfo.GetId()
		getBotPlotRecordInput := &chat.MgetUserPlotRecordReq{
			UserId:  proto.Int64(int64(baseData.UserID)),
			PlotIds: []int64{plotIDTmp},
		}
		getBotPlotRecordOutput := &chat.MgetUserPlotRecordRes{}
		err = tbservice.Call(ctx, "chat", "mgetUserPlotRecord",
			getBotPlotRecordInput,
			&getBotPlotRecordOutput,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || getBotPlotRecordOutput.Errno == nil || getBotPlotRecordOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call chat:mgetUserPlotRecord fail, err:[%v], output:[%s]", err, common.ToString(getBotPlotRecordOutput))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		//判断是否玩过的字段
		isPlaygameTmp := proto.Int32(0)
		GameProcessTmp := &base.GameProcessInfo{}
		if _, ok := getBotPlotRecordOutput.Data.GetRecord()[plotInfo.GetId()]; ok {
			//有进度数据，则说明用户玩过这个剧情
			isPlaygameTmp = proto.Int32(1)
			//游戏进度信息需要塞一下
			GameProcessTmp = &base.GameProcessInfo{
				GameRounds:    getBotPlotRecordOutput.Data.GetRecord()[plotIDTmp].GameRounds,
				Score:         getBotPlotRecordOutput.Data.GetRecord()[plotIDTmp].Score,
				SessionRounds: getBotPlotRecordOutput.Data.GetRecord()[plotIDTmp].SessionRounds,
				IsPass:        getBotPlotRecordOutput.Data.GetRecord()[plotIDTmp].IsPass,
				IsEnd:         getBotPlotRecordOutput.Data.GetRecord()[plotIDTmp].IsEnd,
				SurpassRatio:  getBotPlotRecordOutput.Data.GetRecord()[plotIDTmp].SurpassRatio,
			}
			//历史是否通关查询 chat::mgetUserPlotPass
			getBotGameIsPlayInput := &chat.MgetUserPlotPassReq{
				UserId:  proto.Int64(int64(baseData.UserID)),
				PlotIds: []int64{plotIDTmp},
			}
			getBotGameIsPlayOutput := &chat.MgetUserPlotPassRes{}
			err = tbservice.Call(ctx, "chat", "mgetUserPlotPass", getBotGameIsPlayInput, &getBotGameIsPlayOutput, tbservice.WithConverter(tbservice.JSONITER))
			if err != nil || getBotGameIsPlayOutput.Errno == nil || getBotGameIsPlayOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "call chat:mgetUserPlotPass fail, err:[%v], output:[%s]", err, common.ToString(getBotGameIsPlayOutput))
				return tiebaerror.ERR_CALL_SERVICE_FAIL
			}
			if isPassHistoryValue, ok := getBotGameIsPlayOutput.Data.GetRecord()[plotIDTmp]; ok {
				GameProcessTmp.IsPassHistory = proto.Int32(isPassHistoryValue)
			}
		} else {
			//没有进度数据，则说明用户没玩这个剧情给一些初始值
			GameProcessTmp = &base.GameProcessInfo{
				GameRounds:    proto.Int32(0),
				Score:         plotInfo.InitScore,
				SessionRounds: proto.Int32(0),
				IsPass:        proto.Int32(0),
				IsPassHistory: proto.Int32(0),
				IsEnd:         proto.Int32(0),
			}
		}

		// 计算游戏胜率
		var rate float64 = 0
		if plotInfo.GetChallengeCnt() != 0 {
			rate, _ = strconv.ParseFloat(fmt.Sprintf("%.4f", float32(plotInfo.GetPassCnt())/(float32(plotInfo.GetChallengeCnt()))), 64)
		}

		gameList = append(gameList, &base.AiBotGameInfo{
			PlotId:             plotInfo.Id,
			BotUk:              plotInfo.BotUk,
			BotPa:              plotInfo.BotPa,
			PlotTitle:          plotInfo.PlotTitle,
			PlotDescription:    plotInfo.PlotDescription,
			GreetingInfo:       plotInfo.GreetingInfo,
			PreDependency:      plotInfo.PreDependency,
			InitScore:          plotInfo.InitScore,
			PassScore:          plotInfo.PassScore,
			RoundLimit:         plotInfo.RoundLimit,
			SuccessInfo:        plotInfo.SuccessInfo,
			RoundLimitExceeded: plotInfo.RoundLimitExceeded,
			ProcessNegative:    plotInfo.ProcessNegative,
			IsPlaygame:         isPlaygameTmp,
			GameProcess:        GameProcessTmp,
			BotType:            proto.Int32(plotInfo.GetBotType()),
			RobotVersion:       proto.Int32(plotInfo.GetRobotVersion()),
			GameType:           plotInfo.GameType,
			Rate:               proto.Float64(rate),
		})
	}

	//拿到的数据 拼接回response
	response.Data.GameList = gameList

	return tiebaerror.ERR_SUCCESS
}
