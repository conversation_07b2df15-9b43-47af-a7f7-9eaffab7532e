package aichat

import (
	"context"
	"errors"
	"fmt"
	"math/rand"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/meme"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getRandomMemeInfo"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func DoGetRandomMeme(ctx context.Context, baseData *types.GetRandomMemeInfoBaseData,
	response *getRandomMemeInfo.GetRandomMemeInfoResIdl) int {

	info, err := getRandMeme(ctx, baseData.Request.GetMemePackageId())
	if err != nil {
		tbcontext.WarningF(ctx, "getRandMeme err: %v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	response.Data = info
	return tiebaerror.ERR_SUCCESS
}

func getRandMeme(ctx context.Context, pid uint32) (*getRandomMemeInfo.GetRandomMemeInfoRes, error) {
	req := new(meme.GetPackageDetailByIDReq)
	req.Id = proto.Uint32(pid)
	res := new(meme.GetPackageDetailByIDRes)
	err := tbservice.Call(ctx, "meme", "getPackageDetailById", req, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call meme::getPackageDetailById fail, err=%v, input=%s, output=%s",
			err, common.ToString(req), common.ToString(res))
		return nil, errors.New("meme::getPackageDetailById fail")
	}
	picList := res.GetData().GetPics()
	pInfo := picList[rand.Int()%len(picList)]
	data := &getRandomMemeInfo.GetRandomMemeInfoRes{
		Src:       proto.String(pInfo.GetUrl()),
		BigSrc:    proto.String(pInfo.GetUrl()),
		Bsize:     proto.String(fmt.Sprintf("%d,%d", pInfo.GetWidth(), pInfo.GetHeight())),
		BigSize:   proto.String(fmt.Sprintf("%d,%d", pInfo.GetWidth(), pInfo.GetHeight())),
		MemeId:    proto.Uint64(uint64(pInfo.GetId())),
		PackageId: proto.Uint64(uint64(pid)),
	}
	return data, nil
}
