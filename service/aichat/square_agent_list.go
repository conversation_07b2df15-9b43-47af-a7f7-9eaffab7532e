package aichat

import (
	"context"
	"errors"
	"fmt"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getAgentList"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/squareAgentList"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	agentGlobalSwitch = "agent_global_switch" // agent全局开关

	BotRoleTypeRecommend      = "recommend"
	BotRoleTypeRecommendValue = "推荐"
)

func SquareAgentList(ctx context.Context, baseData *types.SquareAgentListBaseData, response *squareAgentList.SquareAgentListResIdl) int {
	if baseData == nil || baseData.Request == nil {
		tbcontext.FatalF(ctx, "baseData.Request is nil")
		return tiebaerror.ERR_PARAM_ERROR
	}
	input := baseData.Request
	objRequest := baseData.BaseObj.ObjRequest
	uid := common.Tvttt(objRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	pureMode := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("pure_mode", 0), common.TTT_INT).(int)
	clientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	pn, rn := input.GetPn(), input.GetRn()
	if pn == 0 {
		pn = 1
	}
	if rn == 0 {
		rn = 10
	}

	//创建智能体入口可见 默认是0 吸底展示（用户uid命中白名单或者全部用户可见）
	response.Data.CanCreateBot = proto.Int32(0)
	//创建智能体入口可见  默认是0 导航栏（用户uid命中白名单或者用户有创建的智能体或者全部用户可见）
	response.Data.HasOrCreateBot = proto.Int32(0)
	if pureMode == 0 { // 非审核态才能下发创建入口
		canCreateBot, hasOrCreateBot := checkUserCreateWhiteList(ctx, int64(uid)) // 获取用户是否有创建智能体白名单
		if canCreateBot {
			response.Data.CanCreateBot = proto.Int32(1)
		}
		if hasOrCreateBot {
			response.Data.HasOrCreateBot = proto.Int32(1)
		}
	}

	// 获取智能体广场tab
	response.Data.TabList = getSquareTab(ctx, uid, clientVersion)

	// 获取智能体uid列表
	var (
		botUIDs      = make([]int64, 0)
		recomInfoMap = make(map[int64]*chat.RecommendBotInfo)
		totalPage    int32
	)
	if input.GetSource() == BotRoleTypeRecommend { // 推荐
		recomInfos, count, err := getRecommendTabBotUid(ctx, uid, input.GetPn(), input.GetRn())
		if err != nil {
			tbcontext.FatalF(ctx, "getRecommendTabBotUid failed, input=[%v], err=[%v]", common.ToString(input), err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		totalPage = count
		for _, info := range recomInfos {
			recomInfoMap[info.GetBotUid()] = info
			botUIDs = append(botUIDs, info.GetBotUid())
		}
	} else {
		normalBotUIDs, count, err := getNormalTabBotUid(ctx, uid, input.GetSource(), input.GetPn(), input.GetRn())
		if err != nil {
			tbcontext.FatalF(ctx, "getNormalTabBotUid failed, input=[%v], err=[%v]", common.ToString(input), err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		totalPage = count
		botUIDs = normalBotUIDs
	}

	// 翻页信息
	response.Data.Page = &getAgentList.Page{
		CurPage:   proto.Int32(input.GetPn()),
		TotalPage: proto.Int32(totalPage),
	}

	// 没有智能体 返回数据
	if len(botUIDs) <= 0 {
		return tiebaerror.ERR_SUCCESS
	}

	// 根据智能体uid 获取详细数据
	infoInput := &chat.MgetAiBotUserInfoByUidReq{
		BotUids:                botUIDs,
		NeedFilterOnlineStatus: proto.Int32(1),
	}
	infoOutput := &chat.MgetAiBotUserInfoByUidRes{}
	err := tbservice.Call(ctx, "chat", "mgetAiBotUserInfoByUid", infoInput, infoOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || infoOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(
			ctx, "fail to call service chat:mgetAiBotUserInfoByUid, input=[%v], output=[%v], err=[%v]",
			common.ToString(input), common.ToString(infoOutput), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	botList := make([]*getAgentList.RotList, 0)
	botMap := infoOutput.GetData().GetBotList()
	for _, uid := range botUIDs {
		if i, ok := botMap[uid]; ok {
			// AI游戏无游戏名称、游戏描述、游戏头像、背景图 过滤不展示
			if i.GetBotType() == 1 && (i.GetAiGamePlot().GetPlotTitle() == "" || i.GetAiGamePlot().GetPlotDescription() == "" ||
				i.GetAiGamePlot().GetPortraitUrl() == "" || i.GetAiGamePlot().GetBotBackgroundUrl() == "") {
				continue
			}
			mostPvForum := ""
			if i.GetMostPvForum() != "" {
				mostPvForum = fmt.Sprintf("%s吧", i.GetMostPvForum())
			}
			bot := &getAgentList.RotList{
				BotType:      proto.Int32(i.GetBotType()),
				RobotVersion: proto.Int32(i.GetBotVersion()),
				AiGamePlot: &getAgentList.AiGamePlot{
					PlotTitle:        proto.String(i.GetAiGamePlot().GetPlotTitle()),
					PlotDescription:  proto.String(i.GetAiGamePlot().GetPlotDescription()),
					Rate:             proto.Float64(float64(i.GetAiGamePlot().GetRate())),
					PortraitUrl:      proto.String(i.GetAiGamePlot().GetPortraitUrl()),
					PlotId:           proto.Int32(int32(i.GetAiGamePlot().GetPlotId())),
					BotUk:            proto.String(i.GetChatUk()),
					BotPa:            proto.Int64(i.GetPa()),
					BotBackgroundUrl: proto.String(i.GetAiGamePlot().GetBotBackgroundUrl()),
					CreateUser:       proto.String(i.GetCreateUser()),
					CreateUserAvatar: proto.String(i.GetCreateUserAvatar()),
					DialogueUserNum:  proto.Int32(i.GetDialogueUserNum()),
					RoleType:         proto.String(i.GetRoleType()),
					MostPvForum:      proto.String(mostPvForum),
				},
				BotInfo: &getAgentList.BotInfo{
					Name:             proto.String(i.GetName()),
					Portrait:         proto.String(i.GetPortrait()),
					BackgroundUrl:    proto.String(i.GetBackgroundUrl()),
					Prologue:         proto.String(i.GetPrologue()),
					Uk:               proto.String(i.GetChatUk()),
					Pa:               proto.Int64(i.GetPa()),
					CreateUser:       proto.String(i.GetCreateUser()),
					CreateUserAvatar: proto.String(i.GetCreateUserAvatar()),
					DialogueUserNum:  proto.Int32(i.GetDialogueUserNum()),
					RoleType:         proto.String(i.GetRoleType()),
					MostPvForum:      proto.String(mostPvForum),
					ThemeColor: &client.ChatThemeColor{
						ThemeColor:    proto.String(i.GetThemeColor().GetThemeColor()),
						BubbleBgColor: proto.String(i.GetThemeColor().GetBubbleBgColor()),
						SugTextColor:  proto.String(i.GetThemeColor().GetSugTextColor()),
						TagColor:      proto.String(i.GetThemeColor().GetTagColor()),
					},
				},
			}
			// 推荐tab额外数据
			if recomInfo, isRecom := recomInfoMap[i.GetBotUid()]; isRecom {
				bot.RecomTag = proto.String(recomInfo.GetTag())
				bot.RecomTime = proto.Int64(recomInfo.GetRecommendTime())
			}
			botList = append(botList, bot)
		}
	}

	// 角色信息
	response.Data.RotList = botList
	return tiebaerror.ERR_SUCCESS
}

// getSquareTab 获取智能体广场分类tab
func getSquareTab(ctx context.Context, uid uint64, clientType string) []*squareAgentList.SquareRoleType {
	squareRoleType := make([]*squareAgentList.SquareRoleType, 0) // 返回结构体

	isValidVersion := false
	if clientvers.CompareV2(clientType, ">=", "12.76") {
		isValidVersion = true // 12.76以上版本 才需要下发推荐tab
	}

	// 并发请求
	multi := tbservice.Multi()
	classifyParams := &tbservice.Parameter{
		Service: "chat",
		Method:  "getAiSquareClassifyConf",
		Input:   &chat.GetAiSquareClassifyConfReq{},
		Output:  &chat.GetAiSquareClassifyConfRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getAiSquareClassifyConf", classifyParams)
	if isValidVersion {
		recommendParams := &tbservice.Parameter{
			Service: "chat",
			Method:  "getSquareRecomBot",
			Input: &chat.GetSquareRecommBotReq{
				Uid: proto.Uint64(uid),
				Pn:  proto.Int32(1),
				Rn:  proto.Int32(5),
			},
			Output: &chat.GetSquareRecommBotRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getSquareRecomBot", recommendParams)
	}
	multi.Call(ctx)
	// 获取推荐智能体
	if isValidVersion {
		recommendOutput, err := multi.GetResult(ctx, "getSquareRecomBot")
		if err != nil {
			tbcontext.WarningF(ctx, "call chat::getSquareRecomBot fail: %v", err)
		}
		recommendRes := recommendOutput.(*chat.GetSquareRecommBotRes)
		if recommendRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(
				ctx, "fail to call service chat:getSquareRecomBot, uid=[%v], output=[%v], err=[%v]",
				common.ToString(uid), common.ToString(recommendRes), err)
		}
		if len(recommendRes.GetData().GetRecommendBots()) != 0 {
			squareRoleType = append(squareRoleType, &squareAgentList.SquareRoleType{
				Name:  proto.String(BotRoleTypeRecommend),
				Value: proto.String(BotRoleTypeRecommendValue),
			})
		}
	}
	// 获取默认tab
	classifyOutput, err := multi.GetResult(ctx, "getAiSquareClassifyConf")
	if err != nil {
		tbcontext.WarningF(ctx, "call chat::getAiSquareClassifyConf fail: %v", err)
	}
	classifyRes := classifyOutput.(*chat.GetAiSquareClassifyConfRes)
	if classifyRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(
			ctx, "fail to call service chat:getAiSquareClassifyConf, uid=[%v], output=[%v], err=[%v]",
			common.ToString(uid), common.ToString(classifyRes), err)
	}
	for _, tab := range classifyRes.GetData().GetConf() {
		squareRoleType = append(squareRoleType, &squareAgentList.SquareRoleType{
			Name:  proto.String(tab.GetRoleType()),
			Value: proto.String(tab.GetRoleName()),
		})
	}
	return squareRoleType
}

// checkUserCreateWhiteList 获取用户是否创建智能体白名单
func checkUserCreateWhiteList(ctx context.Context, uid int64) (canCreateBot, hasOrCreateBot bool) {
	multi := tbservice.Multi()
	// 只在未命中ios审核态时查白名单
	if uid > 0 {
		whiteListParams := &tbservice.Parameter{
			Service: "chat",
			Method:  "isHitAgentBlackWhiteList",
			Input: &chat.IsHitAgentBlackWhiteListReq{
				Uid:                proto.Int64(int64(uid)),
				NeedCheckUserAgent: proto.Int32(1),
			},
			Output: &chat.IsHitAgentBlackWhiteListRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "isHitAgentBlackWhiteList", whiteListParams)
		whiteListParamsA := &tbservice.Parameter{
			Service: "chat",
			Method:  "isHitAgentBlackWhiteList",
			Input: &chat.IsHitAgentBlackWhiteListReq{
				Uid: proto.Int64(int64(uid)),
			},
			Output: &chat.IsHitAgentBlackWhiteListRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "isHitAgentBlackWhiteListA", whiteListParamsA)
	}
	multi.Call(ctx)

	if uid > 0 {
		whiteListOutput, err := multi.GetResult(ctx, "isHitAgentBlackWhiteList")
		if err != nil {
			tbcontext.WarningF(ctx, "call chat::isHitAgentBlackWhiteList fail: %v", err)
		} else {
			whiteListRes := whiteListOutput.(*chat.IsHitAgentBlackWhiteListRes)
			if whiteListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(
					ctx, "fail to call service chat:isHitAgentBlackWhiteList, uid=[%v], output=[%v], err=[%v]",
					common.ToString(uid), common.ToString(whiteListRes), err)
			} else if whiteListRes.GetData().GetIsHitWhiteList() == 1 {
				hasOrCreateBot = true
			}
		}
		whiteListOutputA, err := multi.GetResult(ctx, "isHitAgentBlackWhiteListA")
		if err != nil {
			tbcontext.WarningF(ctx, "call chat::isHitAgentBlackWhiteList fail: %v", err)
		} else {
			whiteListResA := whiteListOutputA.(*chat.IsHitAgentBlackWhiteListRes)
			if whiteListResA.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(
					ctx, "fail to call service chat:isHitAgentBlackWhiteList, uid=[%v], output=[%v], err=[%v]",
					common.ToString(uid), common.ToString(whiteListResA), err)
			} else if whiteListResA.GetData().GetIsHitWhiteList() == 1 {
				canCreateBot = true
			}
		}
	} else {
		//用户未登录判断词表配置
		res, err := wordserver.QueryKey(ctx, aichatFrsConf, agentGlobalSwitch)
		if err != nil {
			tbcontext.WarningF(ctx, "checkGlobalSwitch QueryKey failed, err=%v", err)
		} else {
			globalSwitch := common.TransforValueToTargetType(res, common.TTT_INT32).(int32)
			if globalSwitch == 1 {
				canCreateBot = true
				hasOrCreateBot = true
			}
		}
	}
	return canCreateBot, hasOrCreateBot
}

// getRecommendTabUID 获取推荐tab的智能体uid列表
func getRecommendTabBotUid(ctx context.Context, uid uint64, pn, rn int32) ([]*chat.RecommendBotInfo, int32, error) {
	recommendBotInfo := make([]*chat.RecommendBotInfo, 0)
	req := &chat.GetSquareRecommBotReq{
		Uid: proto.Uint64(uid),
		Pn:  proto.Int32(pn),
		Rn:  proto.Int32(rn),
	}
	res := &chat.GetSquareRecommBotRes{}
	err := tbservice.Call(ctx, "chat", "getSquareRecomBot", req, res)
	if err != nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "chat::getSquareRecomBot fail. err=[%v], input=[%v], output=[%v]",
			err, common.ToString(req), common.ToString(res))
		return recommendBotInfo, 0, errors.New("fail to call service chat:getSquareRecomBot")
	}
	recommendBot := res.GetData().GetRecommendBots()
	totalPage := res.GetData().GetTotalPage()
	return recommendBot, totalPage, nil
}

// getNormalTabBotUid 获取运营tab的智能体uid列表
func getNormalTabBotUid(ctx context.Context, uid uint64, source string, pn, rn int32) ([]int64, int32, error) {
	// 获取智能体广场智能体uid列表
	req := &chat.GetSquareShowBotReq{
		RoleType: proto.String(source),
		Pn:       proto.Uint32(uint32(pn)),
		Rn:       proto.Uint32(uint32(rn)),
		Uid:      proto.Uint64(uid),
	}
	res := &chat.GetSquareShowBotRes{}
	err := tbservice.Call(ctx, "chat", "getSquareShowBot", req, res)
	if err != nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "chat::getSquareShowBot fail. err=[%v], input=[%v], output=[%v]",
			err, common.ToString(req), common.ToString(res))
		return []int64{}, 0, errors.New("fail to call service chat:getSquareShowBot")
	}
	uidList := res.GetData().GetBotUid()
	totalPage := res.GetData().GetTotalPage()
	return uidList, totalPage, nil
}
