package aichat

import (
	"context"
	"errors"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/baidu/uidxuk"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getUserBotSet"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

var setMapMeata = map[string]uint32{
	"set_background": 1,
	"set_note":       2,
	"set_name":       3,
	"set_setting":    4,
	"set_trigger":    5,
}

var setKeysMeta = []string{
	"set_background",
	"set_note",
	"set_name",
	"set_setting",
	"set_trigger",
}

// DoGetUserBotSet 吧二楼 获取游戏剧情信息和游戏进度信息
func DoGetUserBotSet(ctx context.Context, baseData *types.GetUserBotSetBaseData, response *getUserBotSet.GetUserBotSetResIdl) int {
	userID := baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0).(int64)
	botUID, _ := uidxuk.UK2UID(baseData.Request.GetBotUk())

	tbcontext.WarningF(ctx, "input uid[%d] botuid[%d]", userID, botUID)

	// 校验入参是否合法
	if botUID <= 0 || userID <= 0 {
		tbcontext.WarningF(ctx, "DoGetUserBotSet check botuid and userID error")
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 提前init返回值
	response.Data = &getUserBotSet.GetUserBotSetData{
		BotSet: &getUserBotSet.GetUserBotSetMeta{},
	}

	// 构建所有设置项并配置默认文案
	defalutContent, err := getDefaultContent(ctx)
	if err != nil {
		tbcontext.WarningF(ctx, "query defalut content failed! err[%s]", err.Error())
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	retSetInfo := []*client.UserBotSetItem{}
	for _, v := range setMapMeata {
		retSetInfo = append(retSetInfo, &client.UserBotSetItem{
			Type:           proto.Int32(int32(v)),
			DefaultContent: proto.String(defalutContent[v]),
			AuditStatus:    proto.Uint32(1), // 默认是审核通过的
			TrggierTime:    proto.Uint64(0), // 默认没有定时设置
			Content:        proto.String(""),
		})
	}

	// 查询用户提交内容并更新回返回值中
	mgetUserBotSetByCondInput := &chat.MgetUserBotSetByCondReq{
		BotUid:       &botUID,
		UserId:       proto.Uint64(uint64(userID)),
		Status:       proto.Uint32(1),
		OnlineStatus: proto.Uint32(1),
	}
	mgetUserBotSetByCondOutput := &chat.MgetUserBotSetByCondRes{}

	err = tbservice.Call(ctx, "chat", "mgetUserBotSetByCond", mgetUserBotSetByCondInput, &mgetUserBotSetByCondOutput, tbservice.WithConverter(tbservice.JSONITER))

	tbcontext.WarningF(ctx, "call chat:mgetUserBotSetByCond fail, err:[%v], input[%v], output:[%s]", err,
		mgetUserBotSetByCondInput, common.ToString(mgetUserBotSetByCondOutput))
	if err != nil || mgetUserBotSetByCondOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:mgetUserBotSetByCond fail, err:[%v], input[%v], output:[%s]", err,
			mgetUserBotSetByCondInput, common.ToString(mgetUserBotSetByCondOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 开始填充文案、审核状态和定时时间
	for _, v := range mgetUserBotSetByCondOutput.GetData().GetInfo() {
		if v.GetBotUid() != botUID || v.GetStatus() != 1 || v.GetOnlineStatus() != 1 {
			continue
		}

		for k, item := range retSetInfo {
			if item.GetType() == int32(v.GetSetType()) {
				retSetInfo[k].Content = v.Content
				retSetInfo[k].AuditStatus = v.AuditStatus
				if v.GetExt() != nil {
					retSetInfo[k].TrggierTime = v.GetExt().TriggerTime
					if v.GetExt().GetTriggerTime() > 0 && v.GetExt().GetTriggerTime() < uint64(time.Now().Unix()) && v.GetAuditStatus() == 1 {
						retSetInfo[k].AuditStatus = proto.Uint32(3) // 设定过期
					}
				}
			}
		}
	}

	response.Data.BotSet.SetInfo = retSetInfo
	return tiebaerror.ERR_SUCCESS
}

// getDefaultContent 获取多个个性化设置的默认兜底文案
func getDefaultContent(ctx context.Context) (map[uint32]string, error) {
	ret := map[uint32]string{}

	res, err := wordserver.QueryKeys(ctx, "tb_wordlist_redis_aichat_uidlist_conf", setKeysMeta)
	if err != nil {
		tbcontext.WarningF(ctx, "get tb_wordlist_redis_aichat_uidlist_conf fail: %v", err)
		return nil, err
	} else if len(res) != len(setKeysMeta) {
		tbcontext.WarningF(ctx, "the query out len is not right: %d", len(res))
		return nil, errors.New("out len not right")
	}

	for k, v := range setKeysMeta {
		ret[setMapMeata[v]] = res[k]
	}

	return ret, nil
}
