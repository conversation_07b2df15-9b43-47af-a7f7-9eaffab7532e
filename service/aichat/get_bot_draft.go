package aichat

import (
	"context"
	"encoding/json"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/constants"
	"strconv"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/baidu/uidxuk"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	aichatProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getBotDraft"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// GetBotDraft 获取bot草稿信息
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：baseData：输入数据
// @Param：response：输出数据
// @Return：int：错误码
func GetBotDraft(ctx context.Context, baseData *types.GetBotDraftBaseData, response *aichatProto.GetBotDraftResIdl) int {
	req := baseData.Request
	if req == nil {
		return tiebaerror.ERR_PARAM_ERROR
	}
	userID, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)

	var botInfo *client.CreateBotInfo
	var plotInfo *client.CreatePlotInfo
	var createBotConf *aichatProto.CreateBotConf
	var createPlotConf *aichatProto.CreatePlotConf
	var auditInfo []*aichatProto.AuditInfo

	if req.GetBotUk() != "" {
		var botUid int64
		if req.GetAgentId() != "" {
			uid, err := strconv.Atoi(req.GetBotUk())
			if err != nil {
				tbcontext.WarningF(ctx, "GetBotDraft param err, input:%v", common.ToString(req))
				return tiebaerror.ERR_PARAM_ERROR
			}
			botUid = int64(uid)
		} else {
			uid, err := uidxuk.UK2UID(req.GetBotUk())
			if err != nil {
				tbcontext.WarningF(ctx, "GetBotDraft param err, input::%v", common.ToString(req))
				return tiebaerror.ERR_PARAM_ERROR
			}
			botUid = int64(uid)
		}
		// 调用chat服务的mgetAiBotUserInfoByUid方法
		botInfo, plotInfo, auditInfo = getBotInfoByUid(ctx, botUid)
		if botInfo != nil {
			botInfo.BotUk = proto.String(req.GetBotUk())
		}
	} else {
		// 从redis取数据
		botInfo, plotInfo = getBotInfoFromRedis(ctx, req.GetScene(), userID)
	}

	// 根据入参取词表
	createBotConf, createPlotConf = getConfFromWordlist(ctx, req.GetScene())

	// 获取隐私协议
	privacyAgreement := &aichatProto.AgreementInfo{}
	resData, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_aichat_frs_conf", "create_bot_privacy_agreement")
	if err != nil || resData == "" {
		tbcontext.WarningF(ctx, "word server query key error: %v", err)
	}
	err = json.Unmarshal([]byte(resData), &privacyAgreement)
	if err != nil {
		tbcontext.WarningF(ctx, "word server unmarshal error: %v", err)
	}

	response.Data = &aichatProto.GetBotDraftRes{
		BotInfo:          botInfo,
		PlotInfo:         plotInfo,
		CreateBotConf:    createBotConf,
		CreatePlotConf:   createPlotConf,
		AuditInfo:        auditInfo,
		PrivacyAgreement: privacyAgreement,
	}

	return tiebaerror.ERR_SUCCESS
}

// getBotInfoByUid 调用chat服务的mgetAiBotUserInfoByUid方法
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：botUid：bot的uid
// @Return：*aichatProto.BotInfo：bot信息
// @Return：*aichatProto.PlotInfo：plot信息
// @Return：[]*aichatProto.AuditInfo：审核信息
func getBotInfoByUid(ctx context.Context, botUid int64) (*client.CreateBotInfo, *client.CreatePlotInfo, []*aichatProto.AuditInfo) {
	req := &chat.MgetAiBotUserInfoByUidReq{
		BotUids: []int64{botUid},
	}
	res := &chat.MgetAiBotUserInfoByUidRes{}

	err := tbservice.Call(ctx, "chat", "mgetAiBotUserInfoByUid", req, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:mgetAiBotUserInfoByUid fail, err:[%v], output:[%s]", err, common.ToString(res))
		return nil, nil, nil
	}

	botData := res.GetData().GetBotList()[botUid]
	if botData == nil {
		tbcontext.WarningF(ctx, "bot data is nil")
		return nil, nil, nil
	}
	sugContent := make([]string, 0)
	if botData.GetDefaultSugContent() != "" {
		_ = jsoniter.Unmarshal([]byte(botData.GetDefaultSugContent()), &sugContent)
	}
	botInfo := &client.CreateBotInfo{
		BotAvatar:      proto.String(botData.GetPortrait()),
		BotBackground:  proto.String(botData.GetBackgroundUrl()),
		BotName:        proto.String(botData.GetName()),
		BotGender:      proto.Int32(botData.GetGender()),
		BotNickname:    proto.String(botData.GetNickname()),
		BotDescription: proto.String(botData.GetOriginDescription()),
		BotGreeting:    proto.String(botData.GetPrologue()),
		DefaultSug:     sugContent,
		VisibleStatus:  proto.Int32(botData.GetVisibleStatus()),
		AuditStatus:    proto.Int32(constants.AuditStatusOpSuccess),
	}
	if status, ok := constants.BotAuditStatusMap[int64(botData.GetAuditStatus())]; ok {
		botInfo.AuditStatus = proto.Int32(int32(status))
	}
	plotInfo := &client.CreatePlotInfo{}
	if botData.GetPaType() == 1 {
		plotInfo = &client.CreatePlotInfo{
			Title:         proto.String(botData.GetAiGamePlot().GetPlotTitle()),
			Description:   proto.String(botData.GetAiGamePlot().GetPlotSetting()),
			Task:          proto.String(botData.GetAiGamePlot().GetPlotTask()),
			AddScore:      proto.String(botData.GetAiGamePlot().GetPlotAddScore()),
			DecreaseScore: proto.String(botData.GetAiGamePlot().GetPlotDecreaseScore()),
			Introduce:     proto.String(botData.GetAiGamePlot().GetPlotDescription()),
			Greeting:      proto.String(botData.GetPrologue()),
			DefaultSug:    sugContent,
		}
	}

	auditInfo := make([]*aichatProto.AuditInfo, 0)
	for _, reason := range botData.GetAuditReason() {
		if reason != nil {
			auditInfo = append(auditInfo, &aichatProto.AuditInfo{
				FailedField:  proto.String(reason.GetField()),
				FailedReason: proto.String(reason.GetReason()),
			})
		}
	}
	return botInfo, plotInfo, auditInfo
}

// getBotInfoFromRedis 从redis取数据
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：scene：场景
// @Param：botUid：bot的uid
// @Return：*aichatProto.BotInfo：bot信息
// @Return：*aichatProto.PlotInfo：plot信息
func getBotInfoFromRedis(ctx context.Context, scene string, botUid uint64) (*client.CreateBotInfo, *client.CreatePlotInfo) {
	agentType := 1
	if scene == "create_plot" {
		agentType = 2
	}

	req := &chat.GetBotInfoReq{
		Type:   proto.Uint32(uint32(agentType)),
		UserId: proto.Uint64(botUid),
	}
	res := &chat.GetBotInfoRes{}

	err := tbservice.Call(ctx, "chat", "getBotInfo", req, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:getAiInteractiveGamePlotByIDs fail, err:[%v], output:[%s]", err, common.ToString(res))
		return nil, nil
	}
	result := res.GetData()

	var botInfo *client.CreateBotInfo
	var plotInfo *client.CreatePlotInfo
	var botPlotMixInfo *client.CreateBotPlotMixInfo

	if scene == "create_bot" {
		botInfo = &client.CreateBotInfo{}
		err = jsoniter.Unmarshal([]byte(result), botInfo)
		if err != nil {
			tbcontext.WarningF(ctx, "unmarshal bot info error: %v", err)
			return nil, nil
		}
	} else if scene == "create_plot" {
		botPlotMixInfo = &client.CreateBotPlotMixInfo{}
		err = jsoniter.Unmarshal([]byte(result), botPlotMixInfo)
		if err != nil {
			tbcontext.WarningF(ctx, "unmarshal botPlotMixInfo info error: %v", err)
			return nil, nil
		}
		botInfo = botPlotMixInfo.GetBotInfo()
		plotInfo = botPlotMixInfo.GetPlotInfo()
	}

	return botInfo, plotInfo
}

// getConfFromWordlist 根据入参取词表
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：scene：场景
// @Return：*aichatProto.CreateBotConf：创建bot的配置信息
// @Return：*aichatProto.CreatePlotConf：创建plot的配置信息
func getConfFromWordlist(ctx context.Context, scene string) (*aichatProto.CreateBotConf, *aichatProto.CreatePlotConf) {
	var key string
	if scene == "create_bot" {
		key = "create_bot_conf"
	} else if scene == "create_plot" {
		key = "create_plot_conf"
	} else {
		tbcontext.WarningF(ctx, "scene is invalid")
		return nil, nil
	}

	result, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_aichat_frs_conf", key)
	if err != nil || result == "" {
		tbcontext.WarningF(ctx, "word server query key error: %v", err)
		return nil, nil
	}

	var createBotConf *aichatProto.CreateBotConf
	var createPlotConf *aichatProto.CreatePlotConf

	if scene == "create_bot" {
		createBotConf = &aichatProto.CreateBotConf{}
		err = jsoniter.Unmarshal([]byte(result), createBotConf)
		if err != nil {
			tbcontext.WarningF(ctx, "unmarshal create bot conf error: %v", err)
			return nil, nil
		}
	} else if scene == "create_plot" {
		createPlotConf = &aichatProto.CreatePlotConf{}
		err = jsoniter.Unmarshal([]byte(result), createPlotConf)
		if err != nil {
			tbcontext.WarningF(ctx, "unmarshal create bot conf error: %v", err)
			return nil, nil
		}
	}

	return createBotConf, createPlotConf
}
