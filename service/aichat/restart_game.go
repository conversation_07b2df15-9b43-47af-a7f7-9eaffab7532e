package aichat

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/restartGame"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// DoRestartGame 重新开始ai游戏
func DoRestartGame(ctx context.Context, baseData *types.RestartGameBaseData, response *restartGame.RestartGameResIdl) int {
	baseData.UserID, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	baseData.PlotID, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("plot_id", 0), common.TTT_INT64).(int64)

	// 校验入参role_type是否合法
	if baseData.UserID <= 0 || baseData.PlotID <= 0 {
		tbcontext.WarningF(ctx, "getGameProcess check user_id and plot_id error, plot_id=[%v]", baseData.PlotID)
		return tiebaerror.ERR_PARAM_ERROR
	}

	//重新开始 resetUserPlotRecord
	resetUserPlotInput := &chat.ResetUserPlotRecordReq{
		UserId: proto.Int64(int64(baseData.UserID)),
		PlotId: proto.Int64(baseData.PlotID),
	}

	resetUserPlotOutput := &chat.ResetUserPlotRecordRes{}
	err := tbservice.Call(ctx, "chat", "resetUserPlotRecord", resetUserPlotInput, &resetUserPlotOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || resetUserPlotOutput.Errno == nil || resetUserPlotOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:resetUserPlotRecord fail, err:[%v], output:[%s]", err, common.ToString(resetUserPlotOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	response.Data.GameRounds = proto.Int32(resetUserPlotOutput.GetData().GetGameRounds())
	response.Data.ConversationId = proto.String(resetUserPlotOutput.GetData().GetConversationId())

	//拿到的数据 拼接回response
	return tiebaerror.ERR_SUCCESS
}
