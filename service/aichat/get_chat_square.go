package aichat

import (
	"context"
	"math/rand"
	"strconv"
	"time"

	"github.com/golang/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getChatSquare"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

var roleNameList = []string{"全部", "主播", "动漫", "小说", "情感", "数码", "游戏", "名人"} // 角色类型列表，和roleTypeList一一对应
var roleTypeList = []string{"all", "anchor", "anime", "novel", "emotion", "digital", "game", "weblebrity"}

func DoGetChatSquare(ctx context.Context, baseData *types.GetChatSquareBaseData, output *getChatSquare.GetChatSquareResIdl) int {
	baseData.UserID, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	baseData.Pn, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("pn", 0), common.TTT_UINT32).(uint32)
	baseData.Rn, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("rn", 0), common.TTT_UINT32).(uint32)
	baseData.RoleType, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("role_type", 0), common.TTT_STRING).(string)
	// 校验入参role_type是否合法
	if baseData.RoleType != "" {
		if !php2go.InArrayString(baseData.RoleType, roleTypeList) {
			tbcontext.WarningF(ctx, "getChatSqure check role_type error, input_type=[%s], need_type=[%v]", baseData.RoleType, roleTypeList)
			return tiebaerror.ERR_PARAM_ERROR
		}
	}

	// 获取机器人列表
	err := getBotListForSquare(ctx, baseData, output)
	if err != nil {
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 获取机器人话题列表
	err = getTopicConfForSquare(ctx, baseData, output)
	if err != nil {
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	return tiebaerror.ERR_SUCCESS
}

// 调用chat::getAichatBotInfoByRoleType 获取机器人列表
func getBotListForSquare(ctx context.Context, baseData *types.GetChatSquareBaseData, output *getChatSquare.GetChatSquareResIdl) error {
	getBotInput := &chat.GetAichatBotInfoByRoleTypeReq{
		Pn:        proto.Uint32(baseData.Pn),
		Rn:        proto.Uint32(baseData.Rn),
		CreateUid: proto.Uint64(baseData.UserID),
		BotType:   proto.String(baseData.RoleType),
		PaType:    proto.Int32(0), // 只取bot人设
	}
	// 如果是获取全部，service接口入参BotType需要传空字符串
	if baseData.RoleType == "all" {
		getBotInput.BotType = proto.String("")
	}
	getBotOutput := &chat.GetAichatBotInfoByRoleTypeRes{}
	err := tbservice.Call(ctx, "chat", "getAichatBotInfoByRoleType", getBotInput, &getBotOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getBotOutput.Errno == nil || getBotOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:getAichatBotInfoByRoleType fail, err:[%v], output:[%s]", err, common.ToString(getBotOutput))
		return errno.ErrCallServiceFail
	}
	output.Data.HasMore = getBotOutput.Data.HasMore
	output.Data.TotalNum = getBotOutput.Data.TotalNum
	output.Data.AichatList = make([]*getChatSquare.BotInfo, 0, len(getBotOutput.Data.BotList))
	err = common.StructAToStructBCtx(ctx, getBotOutput.Data.BotList, &output.Data.AichatList)
	if err != nil {
		tbcontext.WarningF(ctx, "StructAToStructB fail, err:[%v], input:[%s]", err, common.ToString(getBotOutput.Data.BotList))
		return errno.ErrCallServiceFail
	}

	// 构建机器人类型列表
	botTypeList := make([]*getChatSquare.RoleTypeSt, 0, len(roleNameList))
	for index, roleNmae := range roleNameList {
		tempBotType := &getChatSquare.RoleTypeSt{
			RoleType: proto.String(roleTypeList[index]),
			RoleName: proto.String(roleNmae),
		}
		botTypeList = append(botTypeList, tempBotType)
	}
	output.Data.RoleTypeList = botTypeList

	// 处理对话轮数
	dialogNumCoefficient := 0
	dialogNumCoefficientStr, err := wordserver.QueryKey(ctx, aiChatConfWordList, "dialog_num_coefficient")
	if err != nil {
		// 对话数系数获取失败，打日志，不阻塞；相当于本次请求对话数系数为1；
		tbcontext.WarningF(ctx, "fail to query wordlist %v, err = %v", aiChatConfWordList, err)
		dialogNumCoefficient = 1
	} else {
		dialogNumCoefficient, err = strconv.Atoi(dialogNumCoefficientStr)
		if err != nil {
			tbcontext.WarningF(ctx, "parse dialog_num_coefficient fail, err=[%v], wordlist_res=[%s]", err, dialogNumCoefficientStr)
			dialogNumCoefficient = 1
		}
	}

	baseData.BotUIDs = make([]int64, 0, len(output.Data.AichatList))
	baseData.TopicMap = make(map[int64]int64, len(output.Data.AichatList))
	for _, bot := range output.Data.AichatList {
		botUserInfo := bot.UserInfo
		if botUserInfo == nil {
			continue
		}
		// 对话数计算规则：兜底+系数*实际对话数
		defaultBialogNum := int64(0)
		if bot.Config != nil {
			defaultBialogNum = bot.GetConfig().GetDefaultDialogNum()
		}
		tempNum := uint64(defaultBialogNum + int64(botUserInfo.GetDialogNum())*int64(dialogNumCoefficient))
		botUserInfo.DialogNum = proto.Uint64(tempNum)
		// 记录bot的uid和topic_id
		baseData.BotUIDs = append(baseData.BotUIDs, botUserInfo.GetUid())
		if len(botUserInfo.TopicIdList) > 0 {
			randTopicID := ArrayRandInt64(botUserInfo.TopicIdList)
			baseData.TopicMap[botUserInfo.GetUid()] = randTopicID
		}
	}

	return nil
}

// 获取机器人话题信息
func getTopicConfForSquare(ctx context.Context, baseData *types.GetChatSquareBaseData, output *getChatSquare.GetChatSquareResIdl) error {
	topicIDs := make([]int64, 0, len(baseData.TopicMap))
	for _, topicID := range baseData.TopicMap {
		topicIDs = append(topicIDs, topicID)
	}
	// 没有话题id，调用chat会报错，直接返回
	if len(topicIDs) == 0 {
		return nil
	}

	topicConfInput := map[string]any{
		"topic_ids": topicIDs,
	}
	topicConfOut := &chat.MgetTopicConfByTopicIDRes{}
	err := tbservice.Call(ctx, "chat", "mgetTopicConfByTopicID", topicConfInput, &topicConfOut, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || topicConfOut.Errno == nil || topicConfOut.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:mgetTopicConfByTopicID fail, err:[%v], output:[%s]", err, common.ToString(topicConfOut))
		return errno.ErrCallServiceFail
	}
	topicConfMap := topicConfOut.GetData()
	for _, bot := range output.Data.AichatList {
		botUserInfo := bot.UserInfo
		if botUserInfo == nil {
			continue
		}
		topicConf := topicConfMap[int32(baseData.TopicMap[botUserInfo.GetUid()])]
		if topicConf == nil {
			continue
		}
		err := common.StructAToStructBCtx(ctx, topicConf, &bot.TopicInfo)
		if err != nil {
			tbcontext.WarningF(ctx, "StructAToStructB fail, err:[%v], input:[%s]", err, common.ToString(topicConf))
		}
	}

	return nil
}

// 获取当前用户与bot的关注关系
func getBotsFollowInfo(ctx context.Context, baseData *types.GetChatSquareBaseData, output *getChatSquare.GetChatSquareResIdl) error {
	// 调用user:getUserFollowInfo 获取当前用户与bot的关注关系
	followInfoInput := map[string]any{
		"user_id":     baseData.UserID,
		"req_user_id": baseData.BotUIDs,
	}
	followInfoOutput := &user.GetUserFollowInfoRes{}
	err := tbservice.Call(ctx, "user", "getUserFollowInfo", followInfoInput, &followInfoOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || followInfoOutput.Errno == nil || followInfoOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call user:getUserFollowInfo fail, err:[%v], output:[%s]", err, common.ToString(followInfoOutput))
		return errno.ErrCallServiceFail
	}

	// user返回值中，IsFollowed=1时，表示用户已关注该bot
	hasFollowMap := make(map[int64]bool, len(followInfoOutput.ResUserInfos))
	for _, followInfo := range followInfoOutput.ResUserInfos {
		if followInfo.GetIsFollowed() == 1 {
			hasFollowMap[followInfo.GetUserId()] = true
		}
	}

	for _, bot := range output.Data.AichatList {
		botUserInfo := bot.UserInfo
		if botUserInfo == nil {
			continue
		}
		botUID := botUserInfo.GetUid()
		if _, ok := hasFollowMap[botUID]; ok {
			bot.HasFollow = proto.Int64(1)
		} else {
			bot.HasFollow = proto.Int64(0)
		}
	}

	return nil
}

// 从int64数组中随机取一个元素
func ArrayRandInt64(elements []int64) int64 {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	index := r.Intn(len(elements))
	return elements[index]
}
