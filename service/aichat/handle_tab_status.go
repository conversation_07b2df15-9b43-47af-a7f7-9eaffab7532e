package aichat

import (
	"context"
	"google.golang.org/protobuf/proto"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	aichatProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/handleTabStatus"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// HandleTabStatus @Description 处理Tab状态的业务逻辑
// @Param：ctx：上下文对象
// @Param：baseData：输入数据
// @Param：response：响应数据
// @Return：int：错误码
func HandleTabStatus(ctx context.Context, baseData *types.HandleTabStatusBaseData, response *aichatProto.HandleTabStatusResIdl) int {
	req := baseData.Request
	if req == nil || req.Common == nil || req.BotUid == nil || (req.GetOperateType() != 1 && req.GetOperateType() != 2 && req.GetOperateType() != 3) {
		return tiebaerror.ERR_HOME_UI_PARAM
	}
	userID, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)

	updateReq := &commonProto.UpdateUserChatAibotReq{
		Uid:     proto.Uint64(userID),
		RobotId: proto.Uint64(uint64(req.GetBotUid())),
	}

	switch req.GetOperateType() {
	case 1:
		updateReq.IsHide = proto.Uint32(1)
	case 2:
		updateReq.IsTop = proto.Uint32(1)
	case 3:
		updateReq.IsTop = proto.Uint32(0)
	}

	updateRes := &commonProto.UpdateUserChatAibotRes{}
	err := tbservice.Call(ctx, "chat", "updateUserChatAibot", updateReq, updateRes)
	if err != nil || updateRes.GetErrno() != 0 {
		response.Data.Result = proto.Int32(0)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	response.Data.Result = proto.Int32(1)
	return tiebaerror.ERR_SUCCESS
}
