package aichat

import (
	"context"
	"encoding/json"
	"errors"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/extension/gtask"

	chatProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/baidu/uidxuk"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	fetchAiBotProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/fetchAiBot"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	// fetchAiBotAuditStatusPass 用户设置审核状态 - 通过
	fetchAiBotAuditStatusPass = 1
	// fetchAiBotAuditSetTypeName 用户设置类型 - 名称
	fetchAiBotAuditSetTypeName = 2

	fetchAiBotMaxNum = 50
)

func FetchAiBot(ctx context.Context, baseData *types.FetchAiBotBaseData, response *fetchAiBotProto.FetchAiBotResIdl) int {
	// 参数校验
	userID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	strBotUkList := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("bot_uk_list", ""), common.TTT_STRING).(string)
	if userID <= 0 || len(strBotUkList) == 0 {
		tbcontext.WarningF(ctx, "input param empty")
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 参数解析
	botUks := make([]string, 0)
	err := json.Unmarshal([]byte(strBotUkList), &botUks)
	if err != nil {
		tbcontext.WarningF(ctx, "input param empty")
		return tiebaerror.ERR_PARAM_ERROR
	}
	if len(botUks) == 0 {
		tbcontext.WarningF(ctx, "input param empty")
		return tiebaerror.ERR_PARAM_ERROR
	}
	if len(botUks) > fetchAiBotMaxNum {
		botUks = botUks[:fetchAiBotMaxNum]
	}

	// 数据去重
	botIDs := make([]int64, 0, len(botUks))
	botIDsU64 := make([]uint64, 0, len(botUks))
	botIDHits := make(map[int64]struct{})
	for _, botUk := range botUks {
		uid, err := uidxuk.UK2UID(botUk)
		if err != nil {
			tbcontext.WarningF(ctx, "uk2uid fail, err=%v, uk=%s", err, botUk)
			continue
		}
		uid64 := int64(uid)
		if _, ok := botIDHits[uid64]; !ok {
			botIDHits[uid64] = struct{}{}
			botIDs = append(botIDs, uid64)
			botIDsU64 = append(botIDsU64, uid)
		}
	}
	if len(botIDs) == 0 {
		tbcontext.WarningF(ctx, "input param invalid")
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 并行请求
	gp := &gtask.Group{
		AllowSomeFail: true,
	}

	userInfo := make(map[int64]*chatProto.BotAndPlotData)
	userBotSet := make(map[uint64]*chatProto.UserBotSets)

	// 获取智能体信息
	gp.Go(func() error {
		input := &chatProto.MgetAiBotUserInfoByUidReq{
			BotUids:                 botIDs,
			NeedFilterAuditStatus:   proto.Int32(1),
			NeedFilterVisibleStatus: proto.Int32(1),
			NeedFilterOnlineStatus:  proto.Int32(1),
		}
		output := &chatProto.MgetAiBotUserInfoByUidRes{}
		err := tbservice.Call(ctx, "chat", "mgetAiBotUserInfoByUid", input, output, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || output == nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.FatalF(ctx, "call chat::mgetAiBotUserInfoByUid fail, err=%v, input=%s, output=%s", err, common.ToString(input), common.ToString(output))
			return errors.New("call chat::mgetAiBotUserInfoByUid fail")
		}
		userInfo = output.GetData().GetBotList()
		return nil
	})

	// 获取用户自定义设置
	gp.Go(func() error {
		input := &chatProto.MgetUserBotSetByUIDReq{
			UserId:  proto.Uint64(userID),
			BotUids: botIDsU64,
		}
		output := &chatProto.MgetUserBotSetByUIDRes{}
		err := tbservice.Call(ctx, "chat", "mgetUserBotSetByUID", input, output, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || output == nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
			// 用户自定义设置允许调用失败
			tbcontext.WarningF(ctx, "call chat::mgetUserBotSetByUID fail, err=%v, input=%s, output=%s", err, common.ToString(input), common.ToString(output))
			return nil
		}
		userBotSet = output.GetData().GetInfo()
		return nil
	})

	_, err = gp.Wait()
	if err != nil {
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 构造返回值
	botInfo := make([]*fetchAiBotProto.FetchAiBotInfo, 0, len(userInfo))
	for _, info := range userInfo {
		if info == nil {
			continue
		}
		botName := info.GetName()
		if userSetInfo, ok := userBotSet[uint64(info.GetBotUid())]; ok && userSetInfo != nil {
			for _, settings := range userSetInfo.GetUserBotSets() {
				if settings == nil {
					continue
				}
				if settings.GetSetType() != fetchAiBotAuditSetTypeName {
					continue
				}
				if settings.GetAuditStatus() != fetchAiBotAuditStatusPass {
					continue
				}
				if len(settings.GetContent()) > 0 {
					botName = settings.GetContent()
					break
				}
			}
		}
		userCreate := 0
		if uint64(info.GetCreateUid()) == userID {
			userCreate = 1
		}
		botInfo = append(botInfo, &fetchAiBotProto.FetchAiBotInfo{
			BotUk:      proto.String(info.GetChatUk()),
			BotName:    proto.String(botName),
			Avatar:     proto.String(info.GetPortrait()),
			UserCreate: proto.Uint32(uint32(userCreate)),
			Pa:         info.Pa,
		})
	}
	response.Data = &fetchAiBotProto.FetchAiBotRes{
		BotInfo: botInfo,
	}
	return tiebaerror.ERR_SUCCESS
}
