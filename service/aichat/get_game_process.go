package aichat

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/base"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getGameProcess"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// DoGetGameProcess 获取游戏剧情信息和游戏进度信息
func DoGetGameProcess(ctx context.Context, baseData *types.GetGameProcessBaseData, response *getGameProcess.GetGameProcessResIdl) int {
	baseData.UserID, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	baseData.PlotID, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("plot_id", 0), common.TTT_INT64).(int64)

	// 校验入参role_type是否合法
	if baseData.UserID <= 0 || baseData.PlotID <= 0 {
		tbcontext.WarningF(ctx, "getGameProcess check user_id and plot_id error, plot_id=[%v]", baseData.PlotID)
		return tiebaerror.ERR_PARAM_ERROR
	}

	//根据剧情ID获取基础信息 getAiInteractiveGamePlotByIDs
	getBotGameInfoInput := &chat.GetAiInteractiveGamePlotByIDsReq{
		Uid:        proto.Int64(int64(baseData.UserID)),
		PlotIds:    []int64{baseData.PlotID},
	}
	getBotGameInfoOutput := &chat.GetAiInteractiveGamePlotByIDsRes{}
	err := tbservice.Call(ctx, "chat", "getAiInteractiveGamePlotByIDs", getBotGameInfoInput, &getBotGameInfoOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getBotGameInfoOutput.Errno == nil || getBotGameInfoOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:getAiInteractiveGamePlotByIDs fail, err:[%v], output:[%s]", err, common.ToString(getBotGameInfoOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	//错误的剧情id是查不到数据
	if len(getBotGameInfoOutput.Data.GetAiInteractiveGamePlot()) == 0 {
		tbcontext.WarningF(ctx, "call chat:getAiInteractiveGamePlotByIDs fail, err:plot_id is not exist, plot_id:[%v]", baseData.PlotID)
		return tiebaerror.ERR_PARAM_ERROR
	}

	response.Data.PlotId = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].Id
	response.Data.BotUk = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].BotUk
	response.Data.BotPa = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].BotPa
	response.Data.PlotTitle = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].PlotTitle
	response.Data.PlotDescription = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].PlotDescription
	response.Data.PreDependency = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].PreDependency
	response.Data.GreetingInfo = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].GreetingInfo
	response.Data.InitScore = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].InitScore
	response.Data.PassScore = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].PassScore
	response.Data.RoundLimit = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].RoundLimit
	response.Data.SuccessInfo = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].SuccessInfo
	response.Data.RoundLimitExceeded = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].RoundLimitExceeded
	response.Data.ProcessNegative = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].ProcessNegative

	//用户剧情最新记录数据查询 chat::mgetUserPlotRecord
	//"user_id": 123456, // 必传
    //"plot_ids": [11], // 对应剧情id数组
	getBotPlotRecordInput := &chat.MgetUserPlotRecordReq{
		UserId:		proto.Int64(int64(baseData.UserID)),
		PlotIds:	[]int64{baseData.PlotID},
	}
	getBotPlotRecordOutput := &chat.MgetUserPlotRecordRes{}
	err = tbservice.Call(ctx, "chat", "mgetUserPlotRecord", getBotPlotRecordInput, &getBotPlotRecordOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getBotPlotRecordOutput.Errno == nil || getBotPlotRecordOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:mgetUserPlotRecord fail, err:[%v], output:[%s]", err, common.ToString(getBotPlotRecordOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	if _, ok := getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID]; ok {
		//有进度数据，则说明用户玩过这个剧情
		response.Data.IsPlaygame = proto.Int32(1)
		//游戏进度信息需要塞一下
		response.Data.GameProcess = &base.GameProcessInfo{
			GameRounds:	getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID].GameRounds,
			Score:		getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID].Score,
			SessionRounds: 	getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID].SessionRounds,
			IsPass:	getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID].IsPass,
			IsEnd:	getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID].IsEnd,
			SurpassRatio: 	getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID].SurpassRatio,
		}
		//历史是否通关查询 chat::mgetUserPlotPass
		getBotGameIsPlayInput := &chat.MgetUserPlotPassReq{
			UserId:		proto.Int64(int64(baseData.UserID)),
			PlotIds:	[]int64{baseData.PlotID},
		}
		getBotGameIsPlayOutput := &chat.MgetUserPlotPassRes{}
		err = tbservice.Call(ctx, "chat", "mgetUserPlotPass", getBotGameIsPlayInput, &getBotGameIsPlayOutput, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || getBotGameIsPlayOutput.Errno == nil || getBotGameIsPlayOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call chat:mgetUserPlotPass fail, err:[%v], output:[%s]", err, common.ToString(getBotGameIsPlayOutput))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		if isPassHistoryValue, ok := getBotGameIsPlayOutput.Data.GetRecord()[baseData.PlotID]; ok {
			response.Data.GameProcess.IsPassHistory = proto.Int32(isPassHistoryValue)
		}
	} else {
		//没有数据，则说明用户没有玩过这个剧情
		response.Data.IsPlaygame = proto.Int32(0)
		//游戏进度信息需要塞一下
		response.Data.GameProcess = &base.GameProcessInfo{
			GameRounds:	proto.Int32(0),
			Score:		response.Data.InitScore,
			SessionRounds: 	proto.Int32(0),
			IsPass:	proto.Int32(0),
			IsPassHistory: 	proto.Int32(0),
			IsEnd:	proto.Int32(0),
		}
	}

	//拿到的数据 拼接回response
	return tiebaerror.ERR_SUCCESS
}
