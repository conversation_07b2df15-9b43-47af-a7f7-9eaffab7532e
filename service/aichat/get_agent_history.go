package aichat

import (
	"context"
	"encoding/json"
	"strconv"

	"github.com/chzyer/readline/runes"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/aigcprocesser"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	p "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getAgentHistory"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	Markdown = "markdown"
	UiMeta   = "uiMeta"
	UiData   = "uiData"

	SelectOptionPre = "【"
	SelectOptionSuf = "】"
	ScorePre        = "["
	ScoreSuf        = "]"
)

// DoGetAgentHistory 获取历史消息
func DoGetAgentHistory(ctx context.Context, baseData *types.GetAgentHistoryBaseData, response *p.GetAgentHistoryResIdl) int {
	agentId, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("agent_id", ""), common.TTT_STRING).(string)
	conversationId, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("conversation_id", ""), common.TTT_STRING).(string)
	userID, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	size, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("size", 0), common.TTT_INT64).(int64)
	pageToken, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("page_token", ""), common.TTT_STRING).(string)
	plotId, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("plot_id", ""), common.TTT_INT64).(int64)
	cuidIos, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("shoubai_cuid", ""), common.TTT_STRING).(string)
	cuidAndroid, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid_galaxy2", ""), common.TTT_STRING).(string)
	baiduID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("baiduid", ""), common.TTT_STRING).(string)
	clientType := baseData.Request.GetCommon().GetXClientType()
	cuid := cuidAndroid
	if clientType == clientvers.CLIENT_TYPE_IPHONE {
		cuid = cuidIos
	}
	// 校验入参是否合法
	if agentId == "" || userID <= 0 {
		tbcontext.WarningF(ctx, "getAgentHistory check param error")
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 如果是剧情游戏 获取游戏开始时间 用于获取历史消息
	var plotCreateTime int64 = 0
	if plotId != 0 {
		mGetUserPlotRecordReq := &chat.MgetUserPlotRecordReq{
			UserId:  proto.Int64(int64(userID)),
			PlotIds: []int64{plotId},
		}
		mGetUserPlotRecordOutput := &chat.MgetUserPlotRecordRes{}
		err := tbservice.Call(ctx, "chat", "mgetUserPlotRecord", mGetUserPlotRecordReq, mGetUserPlotRecordOutput, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || mGetUserPlotRecordOutput.Errno == nil || mGetUserPlotRecordOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call chat::mgetUserPlotRecord fail, err:[%v], output:[%s]", err, common.ToString(mGetUserPlotRecordOutput))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		if mGetUserPlotRecordOutput.GetData() != nil && mGetUserPlotRecordOutput.GetData().GetRecord() != nil {
			if plotRecord, ok := mGetUserPlotRecordOutput.GetData().GetRecord()[plotId]; ok {
				plotCreateTime = plotRecord.GetCreateTime()
			}
		}
	}
	// 获取历史消息记录
	agentHistoryReq := &aigcprocesser.AgentHistoryReq{
		AgentId:             proto.String(agentId),
		UserId:              proto.Uint64(userID),
		ConversationId:      proto.String(conversationId),
		Size:                proto.Int64(size),
		PageToken:           proto.String(pageToken),
		HistoryMsgStartTime: proto.Int64(plotCreateTime),
		Cuid:                proto.String(cuid),
		BaiduId:             proto.String(baiduID),
	}
	agentHistoryOutput := &aigcprocesser.AgentHistoryRes{}
	err := tbservice.Call(ctx, "aigc_processer", "agentHistory", agentHistoryReq, agentHistoryOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || agentHistoryOutput.Errno == nil || agentHistoryOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call aigc_processer::agentHistory fail, err:[%v], output:[%s]", err, common.ToString(agentHistoryOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	agentHistoryRes := agentHistoryOutput.GetData()
	if agentHistoryRes.GetPage() != nil {
		response.Data.Page = &p.Page{
			HasMore:   agentHistoryRes.GetPage().HasMore,
			PageToken: agentHistoryRes.GetPage().PageToken,
		}
	}
	historyMessage := make([]*p.HistoryMessage, 0)
	for _, message := range agentHistoryRes.GetHistoryMessage() {
		queryInfo := &p.QueryInfo{}
		for _, query := range message.GetQuery() {
			queryInfo = &p.QueryInfo{
				Type:  query.Type,
				Value: query.Value,
			}
		}
		answer := &p.Answer{}
		components := make([]*p.Component, 0)
		ui := &p.Component{
			Type: proto.String("ui"),
			Ui:   &p.UiInfo{},
		}
		var scoreResult int32 = 0
		for _, component := range message.GetAnswer().GetComponents() {
			switch component.GetType() {
			case Markdown:
				data := &types.Markdown{}
				if err = json.Unmarshal([]byte(component.GetData()), data); err != nil {
					tbcontext.WarningF(ctx, "getAgentHistory markdown json unmarshal fail, err:[%v]", err)
					continue
				}
				matchResults := make([]string, 0)
				if plotId != 0 {
					// 匹配选项
					data.Value, matchResults = cutSMatchString(data.Value, SelectOptionPre, SelectOptionSuf)
					if len(matchResults) != 0 {
						selectOptions := make([]*p.SelectOptions, 0)
						for _, option := range matchResults {
							selectOptions = append(selectOptions, &p.SelectOptions{
								Type:  proto.String("text"),
								Value: proto.String(option),
							})
						}
						componentRes := &p.Component{
							Type: proto.String("select"),
							Select: &p.Select{
								Name:    proto.String("select"),
								Options: selectOptions,
							},
						}
						components = append(components, componentRes)
					}
					// 匹配分数
					data.Value, matchResults = cutSMatchString(data.Value, ScorePre, ScoreSuf)
					if len(matchResults) != 0 {
						score := 0
						score, err = strconv.Atoi(matchResults[0])
						if err != nil {
							tbcontext.WarningF(ctx, "getAgentHistory score invalid, err:[%v]", err)
						} else {
							scoreResult = int32(score)
						}
					}
				}
				componentRes := &p.Component{
					Type:  proto.String(Markdown),
					Props: &p.Props{Content: proto.String(data.Value)},
				}
				components = append(components, componentRes)
			case UiMeta:
				ui.Ui.UiMeta = component.Data
			case UiData:
				ui.Ui.Data = component.Data
			}
		}
		if ui.GetUi().GetUiMeta() != "" && ui.GetUi().GetData() != "" {
			components = append(components, ui)
		}
		answer.Components = components
		if plotId != 0 {
			answer.Score = proto.Int32(int32(scoreResult))
		}
		historyMessage = append(historyMessage, &p.HistoryMessage{
			Query:  queryInfo,
			Answer: answer,
		})
	}
	response.Data.HistoryMsg = historyMessage
	return tiebaerror.ERR_SUCCESS
}

// cutSMatchString 截取字符串
func cutSMatchString(oriStr string, start, end string) (string, []string) {
	ori := []rune(oriStr)
	match := make([]string, 0)
	for {
		startIdx := runes.Index([]rune(start)[0], ori)
		if startIdx == -1 {
			break
		}
		endIdx := runes.Index([]rune(end)[0], ori[startIdx+1:])
		if endIdx == -1 {
			break
		}
		endIdx += startIdx + 1
		if startIdx+1 > len(ori) || endIdx > len(ori) || startIdx+1 > endIdx {
			break
		}
		match = append(match, string(ori[startIdx+1:endIdx]))
		ori = append(ori[:startIdx], ori[endIdx+1:]...)
	}
	return string(ori), match
}
