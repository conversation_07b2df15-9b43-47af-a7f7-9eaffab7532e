package aichat

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	aigc "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/aigcprocesser"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/impusher"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/usertask"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/activity"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/baidu/uidxuk"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/base"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getChatDetail"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/go-cache/cache"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/constants"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	AigcWhiteListTypeCommon           = "common"                                     // aigc通用白名单类型
	AigcWhiteListTypeAIChat           = "aichat"                                     // aichat白名单类型
	FirstGreetKeyTPL                  = "aichat_first_%d_%d"                         // 判断是否第一次打招呼的redis key
	FirstGreetKeyExpireTime           = 7776000                                      // 90天过期，判定为首次
	questionBotUid                    = 6421022725                                   // 包打听uid
	aiChatConfWordList                = "tb_wordlist_redis_aichat_uidlist_conf"      // 一些其他的配置
	autoAddAichatWhitelistMaxNum      = "auto_add_aichat_whitelist_max_num"          // 每日自动加入人设对话白名单上限数
	autoAddAichatWhitelistNum         = "auto_add_aichat_whitelist_num"              // 每日自动加入人设对话白名单数
	aichatUidListConf                 = "tb_wordlist_redis_aichat_uidlist_conf"      // 一些其余配置词表名
	aichatFrsConf                     = "tb_wordlist_redis_aichat_frs_conf"          // ai角色对话，frs相关配置
	aichatFromFrs                     = 1                                            // 对话落地页的跳转来源：frs
	aichatFromMessageTab              = 2                                            // 对话落地页的跳转来源：消息tab
	aichatFromSquareEntrance          = 3                                            // 对话落地页的跳转来源：广场入口
	aichatFromBotHome                 = 4                                            // 对话落地页的跳转来源：bot主页
	AigcWhiteListTypePrivateChatShare = "private_chat_bot_share"                     // 包打听分享白名单
	PrivateChatShareWordList          = "tb_wordlist_redis_private_chat_share"       // 包打听分享相关词表配置
	aichatActivityConfPre             = "aichat_activity_conf_%v"                    // ai人设对话活动配置
	aichatActivityTableName           = "tb_wordlist_redis_aichat_activity"          // ai人设对话活动词表
	aichatActivityEntryUidBotUid      = "aichat:activity:entry:uid:%d:botuid:%d"     // 角色维度用户触发次数redis KEY（进入触发）
	aichatActivityCurrentChooseType   = "aichat_activity_current_choose_type"        // ai人设对话活动当前选择的数据返回类型
	aichatActivityPopupInfo           = "aichat_activity_popup_info"                 // ai人设对话活动弹窗信息配置
	aichatActivityImgInfo             = "aichat_activity_img_info"                   // ai人设对话活动图片信息配置
	aiInteractiveGamePlotTable        = "tb_wordlist_redis_ai_interactive_game_plot" // Ai游戏剧情词表配置
	aiInteractiveGamePlotChatUkConfig = "ai_interactive_game_plot_uk_config"         // Ai游戏剧情chatUkToGameUk配置
	aiChatSugIcon                     = "ai_chat_sug_icon"                           // sug icon配置，目前仅在游戏模式下使用
	paTypeAiChat                      = 0                                            // pa_type类型，0表示对话
	paTypeGamePlot                    = 1                                            // pa_type类型，1表示游戏剧情
	agreeGuideInfo                    = "agree_guide_info"                           // 点赞引导信息
	followGuideInfo                   = "follow_guide_info"                          // 关注引导信息
	shareGuideInfo                    = "share_guide_info"                           // 分享引导信息

	MsgExtraTypePopup     = 1001 // 对话触发弹窗
	MsgExtraTypeActiveImg = 1002 // 通用活动图片消息体

	AgentTest = "12_66_agent_a" // agent实验
)

// 词表配置的数据格式
type ActivityConfOnlyBot struct {
	KeyWords     []string          `json:"key_words"`
	EntryTrigger uint32            `json:"entry_trigger"` // 是否进入触发
	InputText    string            `json:"input_text"`
	StartTime    int64             `json:"start_time"`
	EndTime      int64             `json:"end_time"`
	MsgType      int32             `json:"msg_type"`   // 单角色消息类型
	PopupInfo    map[string]string `json:"popup_info"` // 单角色消息内容
}

type KeyWordConf struct {
	KeyWord   string `json:"key_word"`
	StartTime int64  `json:"start_time"`
	EndTime   int64  `json:"end_time"`
}

type ActivityConfAll struct {
	KeyWords     []KeyWordConf `json:"key_words"`
	EntryTrigger uint32        `json:"entry_trigger"` // 是否进入触发
	InputText    string        `json:"input_text"`
	StartTime    int64         `json:"start_time"`
	EndTime      int64         `json:"end_time"`
}

type AiInteractiveGameUKConfig struct {
	ChatUkToBotUKs map[string][]string `json:"chat_uk_to_bot_uks"`
}

func DoGetChatDetail(ctx context.Context, request *getChatDetail.GetChatDetailReqIdl,
	baseData *types.GetChatDetailBaseData,
	response *getChatDetail.GetChatDetailResIdl) int {
	clientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)

	// 获取实验数据
	sampleId := baseData.Request.GetCommon().GetSampleId()
	sids := UbsAbtest.GetUbsAbtestSid(ctx, sampleId, cast.ToString(baseData.UserID), "")

	// 前置校验
	errno := preCheck(ctx, baseData)
	if errno != tiebaerror.ERR_SUCCESS {
		return errno
	}
	// 获取点赞、关注、分享引导数据
	guideInfo := getGuideInfo(ctx)
	response.Data.ChatGuide = &getChatDetail.ChatGuide{
		GuideInfo: guideInfo,
	}

	// 安卓12.58 版本控制，ios12.61版本控制
	gamePlotEnable := false
	intClientType := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 1), common.TTT_INT).(int)
	strClientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	if (intClientType == clientvers.CLIENT_TYPE_ANDROID && clientvers.Compare("*********", strClientVersion) >= 0) ||
		(intClientType == clientvers.CLIENT_TYPE_IPHONE && clientvers.Compare("*********", strClientVersion) >= 0) {
		gamePlotEnable = true
	}

	multi := tbservice.Multi()

	// rpc调用，获取数据
	// 调用mgetAichatUserInfoByUid 获取机器人基础数据
	aiChatParam := &tbservice.Parameter{
		Service: "chat",
		Method:  "mgetAichatUserInfoByUid",
		Input: map[string]interface{}{
			"uids":      []uint64{baseData.RobotUID},
			"need_list": false,
			"user_id":   baseData.UserID,
		},
		Output: &chat.MgetAichatUserInfoByUidRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetAichatUserInfoByUid", aiChatParam)

	mgetUserDataParam := &tbservice.Parameter{
		Service: "user",
		Method:  "mgetUserData",
		Input: &user.MgetUserDataReq{
			UserId: []int64{baseData.UserID},
		},
		Output: &user.MgetUserDataRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetUserData", mgetUserDataParam)

	// 获取 dialog_num
	dialogNumParam := &tbservice.Parameter{
		Service: "chat",
		Method:  "getDialogNumsByRobotIDs",
		Input: map[string]interface{}{
			"robot_ids": []uint64{baseData.RobotUID},
		},
		Output: &chat.GetDialogNumsByRobotIDsRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getDialogNumsByRobotIDs", dialogNumParam)

	// 获取 AIChatRelation
	getAIChatRelationParam := &tbservice.Parameter{
		Service: "chat",
		Method:  "getAIChatRelation",
		Input: &chat.GetAIChatRelationReq{
			UserId:   proto.Int64(baseData.UserID),
			RobotId:  proto.Int64(int64(baseData.RobotUID)),
			Category: proto.String("relation"),
		},
		Output: &chat.GetAIChatRelationRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getAIChatRelation", getAIChatRelationParam)

	// 获取 AIChatLabel
	getAIChatLabelParam := &tbservice.Parameter{
		Service: "chat",
		Method:  "getAIChatRelation",
		Input: &chat.GetAIChatRelationReq{
			UserId:   proto.Int64(baseData.UserID),
			RobotId:  proto.Int64(int64(baseData.RobotUID)),
			Category: proto.String("label"),
		},
		Output: &chat.GetAIChatRelationRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getAIChatLabel", getAIChatLabelParam)

	// 获取Relation默认配置
	getRobotRelationConfigsParam := &tbservice.Parameter{
		Service: "chat",
		Method:  "getRobotRelationConfigs",
		Input: &chat.GetRobotRelationConfigsReq{
			RobotId: proto.Uint64(baseData.RobotUID),
		},
		Output: &chat.GetRobotRelationConfigsRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getRobotRelationConfigs", getRobotRelationConfigsParam)

	// 获取审核状态
	getAigcWhiteListParam := &tbservice.Parameter{
		Service: "aigc_processer",
		Method:  "getAigcWhiteListApply",
		Input: &aigc.GetAigcWhiteListApplyReq{
			UserId:   proto.Int64(baseData.UserID),
			Category: proto.String("aichat"),
		},
		Output: &aigc.GetAigcWhiteListApplyRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getAigcWhiteListApply", getAigcWhiteListParam)

	// 判断是否命中aigc通用白名单
	checkAigcWhiteListParam := &tbservice.Parameter{
		Service: "aigc_processer",
		Method:  "checkAigcWhiteList",
		Input: &aigc.CheckAigcWhiteListReq{
			UserIds: []int64{
				baseData.UserID,
			},
			WhiteListTypes: []string{
				AigcWhiteListTypeAIChat,
				AigcWhiteListTypePrivateChatShare,
			},
		},
		Output: &aigc.CheckAigcWhiteListRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "checkAigcWhiteList", checkAigcWhiteListParam)

	// 获取吧内角色信息
	getHotAichatBotParam := &tbservice.Parameter{
		Service: "chat",
		Method:  "getHotAichatBotInfoByFid",
		Input: &chat.GetHotAichatBotInfoByFidReq{
			Fid:     proto.Uint64(uint64(baseData.Request.GetFid())),
			Uid:     proto.Uint64(uint64(baseData.UserID)),
			NeedAll: proto.Uint32(1),
		},
		Output: &chat.GetHotAichatBotInfoByFidRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}

	// 获取吧信息
	getForumInfoParam := &tbservice.Parameter{
		Service: "forum",
		Method:  "getBtxInfo",
		Input: &forum.GetBtxInfoReq{
			ForumId: proto.Uint32(uint32(baseData.Request.GetFid())),
		},
		Output: &forum.GetBtxInfoRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}

	// 当aichat的来源为frs时
	if baseData.Request.GetFromPage() == aichatFromFrs {
		multi.Register(ctx, "getHotAichatBotInfoByFid", getHotAichatBotParam)
		multi.Register(ctx, "getForumInfo", getForumInfoParam)
	}

	// 获取智能体点赞和关注数据
	getBotAgreeAndFollowParam := &tbservice.Parameter{
		Service: "chat",
		Method:  "getBotAgreeAndFollow",
		Input: &chat.GetBotAgreeAndFollowReq{
			UserId: proto.Uint64(uint64(baseData.UserID)),
			BotUid: []uint64{baseData.RobotUID},
		},
		Output: &chat.GetBotAgreeAndFollowRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getBotAgreeAndFollow", getBotAgreeAndFollowParam)

	// 获取聊过轮次数据
	mgetBotChatUidByCondParam := &tbservice.Parameter{
		Service: "chat",
		Method:  "mgetBotChatUidByCond",
		Input: &chat.MgetBotChatUidByCondReq{
			UserId: proto.Uint64(uint64(baseData.UserID)),
			BotUid: proto.Uint64(baseData.RobotUID),
		},
		Output: &chat.MgetBotChatUidByCondRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetBotChatUidByCond", mgetBotChatUidByCondParam)

	// 获取个性化配置数据
	mgetUserBotSetInfoByCondParam := &tbservice.Parameter{
		Service: "chat",
		Method:  "mgetUserBotSetInfoByCond",
		Input: &chat.MgetUserBotSetInfoByCondReq{
			UserId: proto.Uint64(uint64(baseData.UserID)),
			BotUid: proto.Uint64(baseData.RobotUID),
		},
		Output: &chat.MgetUserBotSetInfoByCondRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetUserBotSetInfoByCond", mgetUserBotSetInfoByCondParam)

	setIDs := make([]uint64, 0)
	setIDStr := request.GetData().GetUserAgentSet()
	err := json.Unmarshal([]byte(setIDStr), &setIDs)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to json unmarshal userAgentSet err = %v, userAgentSet = %v", err, common.ToString(setIDStr))
	}
	if len(setIDs) > 0 {
		mgetUserBotSetByIDsParam := &tbservice.Parameter{
			Service: "chat",
			Method:  "mgetUserBotSetByIDs",
			Input: &chat.MgetUserBotSetByIDsReq{
				Ids: setIDs,
			},
			Output: &chat.MgetUserBotSetByIDsRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetUserBotSetByIDs", mgetUserBotSetByIDsParam)
	}

	multi.Call(ctx)

	aiChatOutInf, err := multi.GetResult(ctx, "mgetAichatUserInfoByUid")
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service chat:mgetAichatUserInfoByUid, err = %v, input = %v, output = %v", err, common.ToString(aiChatParam.Input), common.ToString(aiChatOutInf))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	aiChatOut := aiChatOutInf.(*chat.MgetAichatUserInfoByUidRes)
	if aiChatOut == nil || aiChatOut.Errno == nil || aiChatOut.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service chat:mgetAichatUserInfoByUid, err = %v, input = %v, output = %v", err, common.ToString(aiChatParam.Input), common.ToString(aiChatOut))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	mgetUserDataInter, err := multi.GetResult(ctx, "mgetUserData")
	if err != nil || mgetUserDataInter == nil {
		tbcontext.WarningF(ctx, "fail to call service user:mgetUserData, err = %v, input = %v,output = %v", err, common.ToString(mgetUserDataParam.Input), common.ToString(mgetUserDataInter))
	}
	mgetUserDataRes, ok := mgetUserDataInter.(*user.MgetUserDataRes)
	if !ok || mgetUserDataRes == nil || mgetUserDataRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service user:mgetUserData, err = %v, input = %v,output = %v", err, common.ToString(mgetUserDataParam.Input), common.ToString(mgetUserDataRes))
	}

	dialogNumOutInf, err := multi.GetResult(ctx, "getDialogNumsByRobotIDs")
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service chat:getDialogNumsByRobotID, err = %v, input = %v,output = %v", err, common.ToString(dialogNumParam.Input), common.ToString(dialogNumOutInf))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	dialogNumOut := dialogNumOutInf.(*chat.GetDialogNumsByRobotIDsRes)
	if dialogNumOut == nil || dialogNumOut.Errno == nil || dialogNumOut.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service chat:getDialogNumsByRobotID, err = %v, input = %v,output = %v", err, common.ToString(dialogNumParam.Input), common.ToString(dialogNumOut))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	dialogNum := dialogNumOut.GetData()[int64(baseData.RobotUID)]
	// mock dialogNum
	// var dialogNum int64
	// dialogNum = 10

	aiChatRelationInter, err := multi.GetResult(ctx, "getAIChatRelation")
	if err != nil || aiChatRelationInter == nil {
		tbcontext.WarningF(ctx, "fail to call service chat:getAIChatRelation, err = %v, input = %v,output = %v", err, common.ToString(getAIChatRelationParam.Input), common.ToString(aiChatRelationInter))
	}
	aiChatRelation, ok := aiChatRelationInter.(*chat.GetAIChatRelationRes)
	if !ok || aiChatRelation == nil || aiChatRelation.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service chat:getAIChatRelation, err = %v, input = %v,output = %v", err, common.ToString(getAIChatRelationParam.Input), common.ToString(aiChatRelation))
	}

	aiChatLabelInter, err := multi.GetResult(ctx, "getAIChatLabel")
	if err != nil || aiChatLabelInter == nil {
		tbcontext.WarningF(ctx, "fail to call service chat:getAIChatRelation, err = %v, input = %v,output = %v", err, common.ToString(getAIChatLabelParam.Input), common.ToString(aiChatLabelInter))
	}
	aiChatLabel, ok := aiChatLabelInter.(*chat.GetAIChatRelationRes)
	if !ok || aiChatLabel == nil || aiChatLabel.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service chat:getAIChatRelation, err = %v, input = %v,output = %v", err, common.ToString(getAIChatLabelParam.Input), common.ToString(aiChatLabel))
	}

	robotRelationConfigsInter, err := multi.GetResult(ctx, "getRobotRelationConfigs")
	if err != nil || robotRelationConfigsInter == nil {
		tbcontext.WarningF(ctx, "fail to call service chat:getRobotRelationConfigs, err = %v, input = %v,output = %v", err, common.ToString(getRobotRelationConfigsParam.Input), common.ToString(robotRelationConfigsInter))
	}
	robotRelationConfigs, ok := robotRelationConfigsInter.(*chat.GetRobotRelationConfigsRes)
	if !ok || robotRelationConfigs == nil || robotRelationConfigs.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service chat:getRobotRelationConfigs, err = %v, input = %v,output = %v", err, common.ToString(getRobotRelationConfigsParam.Input), common.ToString(robotRelationConfigs))
	}

	AigcWhiteListInter, err := multi.GetResult(ctx, "getAigcWhiteListApply")
	if err != nil || AigcWhiteListInter == nil {
		tbcontext.WarningF(ctx, "fail to call service aigc_processer:getAigcWhiteListApply, err = %v, input = %v,output = %v", err, common.ToString(getAigcWhiteListParam.Input), common.ToString(AigcWhiteListInter))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	AigcWhiteList, ok := AigcWhiteListInter.(*aigc.GetAigcWhiteListApplyRes)
	if !ok || AigcWhiteList == nil || AigcWhiteList.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service aigc_processer:getAigcWhiteListApply, err = %v, input = %v,output = %v", err, common.ToString(getAigcWhiteListParam.Input), common.ToString(AigcWhiteList))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	checkAigcWhiteInter, err := multi.GetResult(ctx, "checkAigcWhiteList")
	if err != nil || checkAigcWhiteInter == nil {
		tbcontext.WarningF(ctx, "fail to call service aigc_processer:checkAigcWhiteList, err = %v, input = %v,output = %v", err, common.ToString(checkAigcWhiteListParam.Input), common.ToString(checkAigcWhiteInter))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	checkAigcWhite, ok := checkAigcWhiteInter.(*aigc.CheckAigcWhiteListRes)
	if !ok || checkAigcWhite == nil || checkAigcWhite.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service aigc_processer:checkAigcWhiteList, err = %v, input = %v,output = %v", err, common.ToString(checkAigcWhiteListParam.Input), common.ToString(checkAigcWhite))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	getBotAgreeAndFollowInter, err := multi.GetResult(ctx, "getBotAgreeAndFollow")
	if err != nil || getBotAgreeAndFollowInter == nil {
		tbcontext.WarningF(ctx, "fail to call service chat:getBotAgreeAndFollow, err = %v, input = %v,output = %v",
			err, common.ToString(getBotAgreeAndFollowParam.Input), common.ToString(getBotAgreeAndFollowInter))
	}
	getBotAgreeAndFollow, ok := getBotAgreeAndFollowInter.(*chat.GetBotAgreeAndFollowRes)
	if !ok || getBotAgreeAndFollow == nil || getBotAgreeAndFollow.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service chat:getBotAgreeAndFollow, err = %v, input = %v,output = %v",
			err, common.ToString(getBotAgreeAndFollowParam.Input), common.ToString(getBotAgreeAndFollowInter))
	}

	mgetBotChatUidByCondInter, err := multi.GetResult(ctx, "mgetBotChatUidByCond")
	if err != nil || mgetBotChatUidByCondInter == nil {
		tbcontext.WarningF(ctx, "fail to call service chat:mgetBotChatUidByCond, err = %v, input = %v,output = %v",
			err, common.ToString(mgetBotChatUidByCondParam.Input), common.ToString(getBotAgreeAndFollowInter))
	}
	mgetBotChatUidByCond, ok := mgetBotChatUidByCondInter.(*chat.MgetBotChatUidByCondRes)
	if !ok || getBotAgreeAndFollow == nil || getBotAgreeAndFollow.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service chat:mgetBotChatUidByCond, err = %v, input = %v,output = %v",
			err, common.ToString(mgetBotChatUidByCondParam.Input), common.ToString(mgetBotChatUidByCondInter))
	}

	mgetUserBotSetInfoByCondInter, err := multi.GetResult(ctx, "mgetUserBotSetInfoByCond")
	if err != nil || mgetUserBotSetInfoByCondInter == nil {
		tbcontext.WarningF(ctx, "fail to call service chat:mgetUserBotSetInfoByCond, err = %v, input = %v,output = %v",
			err, common.ToString(mgetUserBotSetInfoByCondParam.Input), common.ToString(mgetUserBotSetInfoByCondInter))
	}
	mgetUserBotSetInfoByCond, ok := mgetUserBotSetInfoByCondInter.(*chat.MgetUserBotSetInfoByCondRes)
	if !ok || mgetUserBotSetInfoByCond == nil || mgetUserBotSetInfoByCond.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service chat:mgetUserBotSetInfoByCond, err = %v, input = %v,output = %v",
			err, common.ToString(mgetUserBotSetInfoByCondParam.Input), common.ToString(mgetUserBotSetInfoByCondInter))
	}

	var mgetUserBotSetByIDs *chat.MgetUserBotSetByIDsRes
	if len(setIDs) > 0 {
		mgetUserBotSetByIDsInter, err := multi.GetResult(ctx, "mgetUserBotSetByIDs")
		if err != nil || mgetUserBotSetByIDsInter == nil {
			tbcontext.WarningF(ctx, "fail to call service chat:mgetUserBotSetByIDs, err = %v, output = %v", err, common.ToString(mgetUserBotSetByIDsInter))
		}
		mgetUserBotSetByIDs, ok = mgetUserBotSetByIDsInter.(*chat.MgetUserBotSetByIDsRes)
		if !ok || mgetUserBotSetByIDs == nil || mgetUserBotSetByIDs.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service chat:mgetUserBotSetByIDs, err = %v, output = %v", err, common.ToString(mgetUserBotSetByIDsInter))
		}
	}

	// 查redis，获取是否第一次打招呼
	IsFirstGreet := false
	key := fmt.Sprintf(FirstGreetKeyTPL, baseData.UserID, baseData.RobotUID)
	_, err = resource.CacheMsglogic.Get(ctx, key)
	if err != nil {
		if err == cache.ErrorMiss {
			// 如果是空，表示是第一次打招呼
			IsFirstGreet = true
		} else {
			// 读取失败，不阻塞整体流程，按照非首次处理
			tbcontext.WarningF(ctx, "fail to call redis Get, key = %v, err = %v", key, err)
		}
	}

	// 记录首次对话redis
	err = resource.CacheMsglogic.AsyncSet(ctx, key, "1", FirstGreetKeyExpireTime*time.Second)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to set first chat redis, key = %v, err = %v", key, err)
	}

	// 处理返回结果
	robotList := aiChatOut.GetData().GetAichatUserinfoList()
	response.Data.IsDelete = proto.Int32(0)
	robotInfo, ok := robotList[int64(baseData.RobotUID)]
	if !ok {
		// 没查到数据，返回空
		response.Data.UserInfo = &getChatDetail.BotInfo{
			Uk: proto.String(request.GetData().GetChatUk()),
		}
		response.Data.IsDelete = proto.Int32(1)
		return tiebaerror.ERR_SUCCESS
	}

	if len(robotInfo.GetUserInfo().GetTopicIdList()) > 0 {
		topicIds := make([]int32, len(robotInfo.GetUserInfo().GetTopicIdList()))
		for i, v := range robotInfo.GetUserInfo().GetTopicIdList() {
			topicIds[i] = int32(v)
		}
		// 获取 TopicConf
		tConfInput := &chat.MgetTopicConfByTopicIDReq{
			TopicIds: topicIds,
		}
		tConfOutput := &chat.MgetTopicConfByTopicIDRes{}
		outputErr := tbservice.Call(ctx, "chat", "mgetTopicConfByTopicID", tConfInput, tConfOutput)
		if outputErr != nil || tConfOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service chat:mgetTopicConfByTopicID, err = %v, input = %v,output = %v", outputErr, common.ToString(tConfInput), common.ToString(tConfOutput))
		}
		for _, v := range topicIds {
			c, ok := tConfOutput.GetData()[v]
			if !ok {
				continue
			}
			tConf := new(getChatDetail.AIChatTopicConf)
			err := common.StructAToStructBCtx(ctx, c, tConf)
			if err != nil {
				continue
			}
			tConf.Id = nil
			response.Data.TopicInfo = append(response.Data.TopicInfo, tConf)
		}
	} else if len(robotInfo.GetDefaultSugContent()) > 0 {
		// 使用bot的default sug 生成sug选项
		sugList := make([]*base.AbilityInfo, 0)
		for _, sug := range robotInfo.GetDefaultSugContent() {
			sugList = append(sugList, &base.AbilityInfo{
				AbilityType: proto.String("send_msg"),
				AbilityConf: &base.AbilityInfoAbilityConf{
					Type: proto.Int32(1),
					Content: &base.AbilityConfSendMsgContent{
						Text: proto.String(sug),
					},
				},
				StyleConf: &base.AbilityInfoStyleConf{
					Scene:   proto.String("message_buttons"),
					Content: proto.String(sug),
				},
			})
		}
		response.Data.TopicInfo = append(response.Data.TopicInfo, &getChatDetail.AIChatTopicConf{
			SugList: sugList,
		})
	}

	// 拼 user_info
	response.Data.UserInfo = &getChatDetail.BotInfo{
		Uk:                    robotInfo.UserInfo.Uk,
		Pa:                    robotInfo.UserInfo.Pa,
		Name:                  robotInfo.UserInfo.Name,
		Portrait:              robotInfo.UserInfo.Portrait,
		Gender:                proto.Int64(int64(robotInfo.UserInfo.GetGender())),
		CreateUname:           robotInfo.UserInfo.CreateUname,
		DialogNum:             proto.Int64(dialogNum),
		Description:           robotInfo.UserInfo.Description,
		CreatePortrait:        robotInfo.UserInfo.CreatePortrait,
		IntroduceCardSugTitle: robotInfo.UserInfo.IntroduceCardSugTitle,
		IntroduceCardContent:  robotInfo.UserInfo.IntroduceCardContent,
		IntroduceVoice:        robotInfo.UserInfo.IntroduceVoice,
		Prologue:              robotInfo.GreetingInfo.Prologue,
		BotType:               proto.Int32(robotInfo.UserInfo.GetBotType()),
		RobotVersion:          proto.Int32(robotInfo.UserInfo.GetRobotVersion()),
		RoleType:              proto.String(robotInfo.GetUserInfo().GetRoleType()),
		DialogUserNum:         proto.Uint64(robotInfo.GetUserInfo().GetDialogUserNum()),
		IsForumBot:            proto.Int32(robotInfo.GetUserInfo().GetIsForumBot()),
	}

	errno = formatUserInfo(ctx, baseData.UserID, robotInfo, request, response)
	if errno != 0 {
		tbcontext.WarningF(ctx, "Fail to add plot info to userInfo")
	}

	if len(aiChatRelation.GetData().GetRelationList()) > 0 {
		response.Data.UserInfo.Relation = proto.String(aiChatRelation.GetData().GetRelationList()[0].GetName())
	} else if len(robotRelationConfigs.GetData().GetRelation()) > 0 {
		response.Data.UserInfo.Relation = proto.String(robotRelationConfigs.GetData().GetRelation()[0].GetName())
	}
	if len(aiChatLabel.GetData().GetLabelList()) > 0 {
		response.Data.UserInfo.Label = proto.String(aiChatLabel.GetData().GetLabelList()[0])
	} else if v, ok := mgetUserDataRes.GetUserInfo()[baseData.UserID]; ok {
		response.Data.UserInfo.Label = proto.String(v.GetUserInfoNameShowV2(ctx))
	}

	response.Data.UserInfo.Tags = make([]*getChatDetail.Tag, 0, len(robotInfo.UserInfo.Tag))
	for _, tagInfo := range robotInfo.UserInfo.Tag {
		response.Data.UserInfo.Tags = append(response.Data.UserInfo.Tags, &getChatDetail.Tag{
			Name:  tagInfo.Name,
			Value: tagInfo.Value,
		})
	}
	response.Data.UserInfo.IntroduceCardTitleInfo = make([]*getChatDetail.TitleInfo, 0, len(robotInfo.UserInfo.GetIntroduceCardTitleInfo()))
	for _, titleInfo := range robotInfo.UserInfo.IntroduceCardTitleInfo {
		response.Data.UserInfo.IntroduceCardTitleInfo = append(response.Data.UserInfo.IntroduceCardTitleInfo, &getChatDetail.TitleInfo{
			Content:  titleInfo.Content,
			Icon:     titleInfo.Icon,
			IconType: titleInfo.IconType,
		})
	}

	// 拼 default_sug
	response.Data.DefaultSug = make([]*base.AbilityInfo, len(robotInfo.DefaultSug))
	common.StructAToStructBCtx(ctx, robotInfo.GetDefaultSug(), &response.Data.DefaultSug)

	// 拼 greeting_info
	response.Data.GreetingInfo = &getChatDetail.GreetingInfo{}
	firstNum := len(robotInfo.GreetingInfo.FirstGreeting)
	notFirstNum := len(robotInfo.GreetingInfo.NotFirstGreeting)
	if IsFirstGreet && firstNum > 0 {
		// 首次打招呼，并且rpc调用获取到了首次打招呼的数据
		index := rand.Intn(firstNum) // 随机取一条
		response.Data.GreetingInfo.Type = proto.String("first")
		response.Data.GreetingInfo.Title = robotInfo.GreetingInfo.FirstGreeting[index].Title
		response.Data.GreetingInfo.Content = robotInfo.GreetingInfo.FirstGreeting[index].Content
		common.StructAToStructBCtx(ctx, robotInfo.GreetingInfo.FirstGreeting[index].GetSugList(), &response.Data.GreetingInfo.SugList)
	} else if !IsFirstGreet && notFirstNum > 0 {
		// 非首次打招呼，并且rpc调用获取到了非首次打招呼的数据
		index := rand.Intn(notFirstNum) // 随机取一条
		response.Data.GreetingInfo.Type = proto.String("not_first")
		response.Data.GreetingInfo.Title = robotInfo.GreetingInfo.NotFirstGreeting[index].Title
		response.Data.GreetingInfo.Content = robotInfo.GreetingInfo.NotFirstGreeting[index].Content
		common.StructAToStructBCtx(ctx, robotInfo.GreetingInfo.NotFirstGreeting[index].GetSugList(), &response.Data.GreetingInfo.SugList)
	} else {
		tbcontext.WarningF(ctx, "no greeting data available")
	}

	// 拼 config
	response.Data.Config = &getChatDetail.Config{
		InputboxPromptText:       robotInfo.Config.InputboxPromptText,
		LoadingText:              robotInfo.Config.LoadingText,
		NotFirstGreetingInterval: robotInfo.Config.NotFirstGreetingInterval,
		RestartDialogueInterval:  robotInfo.Config.RestartDialogueInterval,
		ThemeColor:               robotInfo.Config.ThemeColor,
		DefaultAnswer:            robotInfo.Config.DefaultAnswer,
		Timeout:                  robotInfo.Config.Timeout,
		MsgNumForSug:             robotInfo.Config.MsgNumForSug,
		ChatBackground:           robotInfo.Config.ChatBackground,
		BubbleBgColor:            robotInfo.Config.BubbleBgColor,
		SugTextColor:             robotInfo.Config.SugTextColor,
		TagColor:                 robotInfo.Config.TagColor,
		PersonPic:                robotInfo.Config.PersonPic,
		PersonBackground:         robotInfo.Config.PersonBackground,
		SpeakId:                  robotInfo.Config.SpeakId,
		FollowButtonColor:        robotInfo.Config.FollowButtonColor,
		Avatar:                   robotInfo.Config.Avatar,
	}
	// 版控
	if clientvers.Compare("12.54.0.0", clientVersion) >= 0 {
		// 获取活动期间的输入框默认文案
		inputText := getInputboxText(ctx, baseData)
		if inputText != "" {
			response.Data.Config.InputboxPromptText = proto.String(inputText)
		}
	}

	// 拼 switch
	response.Data.Switch = &getChatDetail.Switch{
		EnableSug:                  robotInfo.Switch.EnableLoading,
		EnableShowNotFirstGreeting: robotInfo.Switch.EnableShowNotFirstGreeting,
		EnableRestartDialogue:      robotInfo.Switch.EnableRestartDialogue,
		EnableLoading:              robotInfo.Switch.EnableLoading,
		EnableTypewriter:           robotInfo.Switch.EnableTypewriter,
		EnableIntervalGreeting:     robotInfo.Switch.EnableIntervalGreeting,
		ShowHot:                    robotInfo.Switch.ShowHot,
		EnableNewHomepage:          robotInfo.Switch.EnableNewHomepage,
		EnableHideKeyboard:         robotInfo.Switch.EnableHideKeyboard,
	}

	if baseData.Request.GetFromPage() == aichatFromFrs {

		// 获取吧信息
		forumInfoInter, err := multi.GetResult(ctx, "getForumInfo")
		if err != nil || forumInfoInter == nil {
			tbcontext.WarningF(ctx, "fail to call service forum:getBtxInfo, err = %v, input = %v,output = %v", err, common.ToString(getForumInfoParam.Input), common.ToString(forumInfoInter))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		forumInfo, ok := forumInfoInter.(*forum.GetBtxInfoRes)
		if !ok || forumInfo == nil || forumInfo.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service forum:getBtxInfo, err = %v, input = %v,output = %v", err, common.ToString(getForumInfoParam.Input), common.ToString(forumInfo))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		// 获取吧内角色
		hotAichatBotInfoByFidInter, err := multi.GetResult(ctx, "getHotAichatBotInfoByFid")
		if err != nil || hotAichatBotInfoByFidInter == nil {
			tbcontext.WarningF(ctx, "fail to call service chat:getHotAichatBotInfoByFid, err = %v, input = %v,output = %v", err, common.ToString(getHotAichatBotParam.Input), common.ToString(hotAichatBotInfoByFidInter))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		hotAichatBotInfoByFid, ok := hotAichatBotInfoByFidInter.(*chat.GetHotAichatBotInfoByFidRes)
		if !ok || hotAichatBotInfoByFid == nil || hotAichatBotInfoByFid.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service chat:getHotAichatBotInfoByFid, err = %v, input = %v,output = %v", err, common.ToString(getHotAichatBotParam.Input), common.ToString(hotAichatBotInfoByFid))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		botList := []*getChatDetail.BotInfo{}
		uids := make([]int64, 0)

		if len(hotAichatBotInfoByFid.Data.GetBotList()) > 0 {
			for _, botInfo := range hotAichatBotInfoByFid.Data.BotList {
				if botInfo == nil {
					tbcontext.WarningF(ctx, "botInfo is nil")
					continue
				}

				bot := &getChatDetail.BotInfo{
					Uk:                botInfo.UserInfo.Uk,
					Pa:                botInfo.UserInfo.Pa,
					Name:              botInfo.UserInfo.Name,
					Portrait:          botInfo.UserInfo.Portrait,
					Gender:            proto.Int64(int64(botInfo.UserInfo.GetGender())),
					DialogNum:         proto.Int64(int64(botInfo.UserInfo.GetDialogNum())),
					CreateUname:       botInfo.UserInfo.CreateUname,
					Description:       botInfo.UserInfo.Description,
					CreatePortrait:    botInfo.UserInfo.CreatePortrait,
					Background:        botInfo.Config.ChatBackground,
					FollowButtonColor: botInfo.Config.FollowButtonColor,
				}

				// bot_list中的bot添加plot信息
				if gamePlotEnable && len(botInfo.Plot) > 0 {
					bot.Plot = PlotBasicToPlot(botInfo.Plot)
				}

				botList = append(botList, bot)
				uids = append(uids, botInfo.GetUserInfo().GetUid())
			}
		} else if clientvers.Compare("12.66", clientVersion) < 0 && !php2go.InArrayString(AgentTest, sids) {
			// 12.66版本，agent实验，吧内无挂载bot也可以会话
			// 如果吧内没角色时， 1个词表配置uid+另一个词表前3个uid，用4个uid取到对应的bot_list
			// 词表获取1个uid
			uidOneStr, err := wordserver.QueryKey(ctx, aichatFrsConf, "default_bot_uid")
			if err != nil {
				tbcontext.WarningF(ctx, "fail to get aichat uidlist word list, error = %v", err)
				return tiebaerror.ERR_CALL_SERVICE_FAIL
			}
			uidOneInt, err := strconv.Atoi(uidOneStr)
			if err != nil {
				tbcontext.WarningF(ctx, "Atoi fail, uidOneStr = %s, err = %v", uidOneStr, err)
				return tiebaerror.ERR_PARAM_ERROR
			}
			if uidOneInt <= 0 {
				tbcontext.WarningF(ctx, "bot uid is wrong! table = %s, key = default_bot_uid", aichatFrsConf)
				return tiebaerror.ERR_PARAM_ERROR
			}
			uids = append(uids, int64(uidOneInt))

			// 增加去重逻辑，防止第一个uid和另外3个uid有重复，若重复则少展示
			uidOneMap := map[int]struct{}{}
			uidOneMap[uidOneInt] = struct{}{}

			// 读词表获取前3的数据
			uidThreeStr, err := wordserver.QueryKey(ctx, aichatUidListConf, "chat_square_uids")
			if err != nil {
				tbcontext.WarningF(ctx, "fail to get aichat uidlist word list, error = %v", err)
				return tiebaerror.ERR_CALL_SERVICE_FAIL
			}
			uidThreeRaw := strings.Split(uidThreeStr, ",")
			if len(uidThreeRaw) > 3 {
				uidThreeRaw = uidThreeRaw[:3]
			}
			for _, uid := range uidThreeRaw {
				idInt, err := strconv.Atoi(uid)
				if err != nil {
					tbcontext.WarningF(ctx, "Atoi fail, uid = %s, err = %v", uid, err)
					return tiebaerror.ERR_PARAM_ERROR
				}
				if _, ok := uidOneMap[idInt]; ok {
					tbcontext.WarningF(ctx, "uid already exists")
					continue
				}
				uids = append(uids, int64(idInt))
			}

			aiChatUserInfoFourParam := &tbservice.Parameter{
				Service: "chat",
				Method:  "mgetAichatUserInfoByUid",
				Input: &chat.MgetAichatUserInfoByUidReq{
					Uids:     uids,
					NeedList: proto.Bool(false),
					UserId:   proto.Int64(baseData.UserID),
				},
				Output: &chat.MgetAichatUserInfoByUidRes{},
				Option: []tbservice.Option{
					tbservice.WithConverter(tbservice.JSONITER),
				},
			}
			multi.Register(ctx, "mgetAichatUserInfoByUidFour", aiChatUserInfoFourParam)
			multi.Call(ctx)

			AichatUserInfoFourInter, err := multi.GetResult(ctx, "mgetAichatUserInfoByUidFour")
			if err != nil || AichatUserInfoFourInter == nil {
				tbcontext.WarningF(ctx, "fail to call service chat:mgetAichatUserInfoByUid, err = %v, input = %v,output = %v", err, common.ToString(aiChatUserInfoFourParam.Input), common.ToString(AichatUserInfoFourInter))
				return tiebaerror.ERR_CALL_SERVICE_FAIL
			}
			AichatUserInfoFour, ok := AichatUserInfoFourInter.(*chat.MgetAichatUserInfoByUidRes)
			if !ok || AichatUserInfoFour == nil || AichatUserInfoFour.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "fail to call service chat:mgetAichatUserInfoByUid, err = %v, input = %v,output = %v", err, common.ToString(aiChatUserInfoFourParam.Input), common.ToString(AichatUserInfoFour))
				return tiebaerror.ERR_CALL_SERVICE_FAIL
			}

			// map会乱序，需要根据uids中的顺序进行排序
			botListMap := AichatUserInfoFour.Data.AichatUserinfoList
			for _, uid := range uids {
				aichatInfo := botListMap[int64(uid)]

				bot := &getChatDetail.BotInfo{
					Uk:                aichatInfo.UserInfo.Uk,
					Pa:                aichatInfo.UserInfo.Pa,
					Name:              aichatInfo.UserInfo.Name,
					Portrait:          aichatInfo.UserInfo.Portrait,
					Gender:            proto.Int64(int64(robotInfo.UserInfo.GetGender())),
					DialogNum:         proto.Int64(dialogNum),
					CreateUname:       aichatInfo.UserInfo.CreateUname,
					Description:       aichatInfo.UserInfo.Description,
					CreatePortrait:    aichatInfo.UserInfo.CreatePortrait,
					Background:        aichatInfo.Config.ChatBackground,
					FollowButtonColor: aichatInfo.Config.FollowButtonColor,
				}

				if gamePlotEnable && len(aichatInfo.GetPlot()) > 0 {
					bot.Plot = PlotBasicToPlot(aichatInfo.GetPlot())
				}

				botList = append(botList, bot)
			}
		}

		// 获取对话bot信息
		hasCurBot := false // 如果吧未挂载当前聊天的游戏、bot 需要补齐
		for _, bot := range botList {
			if bot.GetUk() == robotInfo.GetUserInfo().GetUk() {
				hasCurBot = true
			}
			for _, plot := range bot.GetPlot() {
				if plot.GetGameUk() == robotInfo.GetUserInfo().GetUk() {
					hasCurBot = true
				}
			}
		}
		if !hasCurBot {
			var robotPlot []*getChatDetail.Plot
			for _, plot := range robotInfo.Plot {
				robotPlot = append(robotPlot, &getChatDetail.Plot{
					GameUk: proto.String(plot.GetGameUk()),
					PlotId: proto.Int64(plot.GetPlotId()),
					GamePa: proto.Int64(plot.GetGamePa()),
				})
			}
			botList = append(botList, &getChatDetail.BotInfo{
				Uk:                proto.String(robotInfo.GetUserInfo().GetUk()),
				Pa:                proto.Int64(robotInfo.GetUserInfo().GetPa()),
				Name:              proto.String(robotInfo.GetUserInfo().GetName()),
				Portrait:          proto.String(robotInfo.GetUserInfo().GetPortrait()),
				Gender:            proto.Int64(int64(robotInfo.UserInfo.GetGender())),
				DialogNum:         proto.Int64(dialogNum),
				CreateUname:       proto.String(robotInfo.GetUserInfo().GetCreateUname()),
				Description:       proto.String(robotInfo.GetUserInfo().GetDescription()),
				CreatePortrait:    proto.String(robotInfo.GetUserInfo().GetCreatePortrait()),
				Background:        proto.String(robotInfo.GetConfig().GetChatBackground()),
				FollowButtonColor: robotInfo.Config.FollowButtonColor,
				Plot:              robotPlot,
			})
			uids = append(uids, robotInfo.GetUserInfo().GetUid())
		}

		// 获取用户对角色的关注信息
		var getUserFollowInfoParam *tbservice.Parameter
		if len(uids) != 0 {
			getUserFollowInfoParam = &tbservice.Parameter{
				Service: "user",
				Method:  "getUserRelation",
				Input: &user.GetUserRelationReq{
					UserId:    proto.Int64(baseData.UserID),
					ReqUserId: uids,
				},
				Output: &user.GetUserRelationRes{},
				Option: []tbservice.Option{
					tbservice.WithConverter(tbservice.JSONITER),
				},
			}
			multi.Register(ctx, "getUserFollowInfo", getUserFollowInfoParam)
		}
		multi.Call(ctx)

		// 获取用户对角色的关注信息
		if len(uids) != 0 {
			userFollowInfoInter, err := multi.GetResult(ctx, "getUserFollowInfo")
			if err != nil || userFollowInfoInter == nil {
				tbcontext.WarningF(ctx, "fail to call service user:getUserRelation, err = %v, input = %v,output = %v",
					err, common.ToString(getUserFollowInfoParam.Input), common.ToString(userFollowInfoInter))
				return tiebaerror.ERR_CALL_SERVICE_FAIL
			}
			userFollowInfo, ok := userFollowInfoInter.(*user.GetUserRelationRes)
			if !ok || userFollowInfo == nil || userFollowInfo.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "fail to call service user:getUserRelation, err = %v, input = %v,output = %v", err,
					common.ToString(getUserFollowInfoParam.Input), common.ToString(userFollowInfo))
				return tiebaerror.ERR_CALL_SERVICE_FAIL
			}

			for id, followInfo := range userFollowInfo.GetResUserInfos() {
				if followInfo == nil {
					tbcontext.WarningF(ctx, "followInfo is nil")
					continue
				}
				botList[id].HasFollow = proto.Int32(int32(followInfo.GetIsFollowed()))
			}
		}

		// 拼 bot_num
		botNum := len(hotAichatBotInfoByFid.Data.GetBotList())
		if !hasCurBot {
			botNum++
			baseData.Request.RecentBots = proto.String(fmt.Sprintf("%s,%s", robotInfo.GetUserInfo().GetUk(), baseData.Request.GetRecentBots()))
		}
		response.Data.BotNum = proto.Int64(int64(botNum))

		// 按传参的uk对吧内角色排序，如果吧内角色的数量为0，不排序
		botListRes, errno := botInfoOderByUk(ctx, baseData.Request.GetRecentBots(), botList, botNum)
		if botListRes == nil || errno != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "botInfoOderByUk fail, input: recent_bots = %s, botList = %s", common.ToString(baseData.Request.RecentBots), common.ToString(botList))
			return tiebaerror.ERR_PARAM_ERROR
		}

		// 拼 bot_list
		response.Data.BotList = botListRes

		// 拼 forum_info
		response.Data.ForumInfo = &getChatDetail.ForumInfo{
			Name:    forumInfo.ForumName.ForumName,
			Avatar:  forumInfo.Card.Avatar,
			ForumId: proto.Uint64(uint64(baseData.Request.GetFid())),
		}
	}

	// 处理审核状态
	var status int32
	isWhite, _ := checkAigcWhite.GetData()[baseData.UserID].GetHitWhiteList()[AigcWhiteListTypeAIChat]
	if isWhite {
		status = 1
	} else if AigcWhiteList.GetData() == nil || AigcWhiteList.GetData().AuditStatus == nil {
		status = 0
	} else {
		switch AigcWhiteList.GetData().GetAuditStatus() {
		case 0:
			status = 2
			break
		case 1:
			status = 3
			break
		case 2:
			status = 1
			break
		}
	}
	// 包打听或每日自动添加白名单数小于上限时，不展示申请入口
	if status != 1 && (baseData.RobotUID == questionBotUid || checkAutoAddAichatWhiteListNum(ctx)) {
		status = 1
	}
	response.Data.AuditStatus = proto.Int32(status)

	// 包打听分享字数限制
	res, err := wordserver.QueryKeys(ctx, PrivateChatShareWordList, []string{"share_dialog_limit", "share_word_limit"})
	if err != nil || len(res) != 2 {
		tbcontext.WarningF(ctx, "get tb_wordlist_redis_private_chat_share fail: %v", err)
		response.Data.Config.ShareMsgItemCount = proto.Uint32(50)
		response.Data.Config.ShareMsgWordNum = proto.Uint32(2000)
	} else {
		shareMsgMaxCnt := common.TransforValueToTargetType(res[0], common.TTT_UINT32).(uint32)
		shareWordMaxCnt := common.TransforValueToTargetType(res[1], common.TTT_UINT32).(uint32)
		// 不允许超过硬限
		if shareMsgMaxCnt > 100 {
			shareMsgMaxCnt = 100
		}
		if shareWordMaxCnt > 10000 {
			shareWordMaxCnt = 10000
		}
		response.Data.Config.ShareMsgItemCount = proto.Uint32(shareMsgMaxCnt)
		response.Data.Config.ShareMsgWordNum = proto.Uint32(shareWordMaxCnt)
	}

	// 包打听分享功能白名单
	if clientvers.Compare("12.52.5.0", clientVersion) >= 0 {
		isPrivateChatShareWithe := checkAigcWhite.GetData()[baseData.UserID].GetHitWhiteList()[AigcWhiteListTypePrivateChatShare]
		if isPrivateChatShareWithe {
			response.Data.Switch.EnablePrivateChatShare = proto.Int32(1)
		} else {
			response.Data.Switch.EnablePrivateChatShare = proto.Int32(0)
		}
	}

	response.Data.BotAuditStatus = proto.Int32(int32(constants.AuditStatusMap[int64(robotInfo.GetAuditStatus())]))
	response.Data.VisibleStatus = proto.Int32(int32(constants.AuditStatusMap[int64(robotInfo.GetVisibleStatus())]))

	// 给端兜底
	if clientvers.CompareV2(clientVersion, ">=", "12.73") {
		if response.GetData().GetConfig().GetAvatar() == "" {
			response.Data.Config.Avatar =
				proto.String(fmt.Sprintf("https://himg.bdimg.com/sys/portrait/item/%s", response.GetData().GetUserInfo().GetPortrait()))
		}
	}

	// 拼接智能体互动数据
	interactInfo := &getChatDetail.InteractInfo{}
	if getBotAgreeAndFollow != nil && getBotAgreeAndFollow.GetData() != nil {
		if info, ok := getBotAgreeAndFollow.GetData().GetInfo()[baseData.RobotUID]; ok {
			interactInfo.IsAgree = proto.Uint32(info.GetIsAgree())
			interactInfo.IsFollow = proto.Uint32(info.GetIsFollow())
			interactInfo.FollowNum = proto.Uint64(info.GetFollowNum())
			interactInfo.AgreeNum = proto.Uint64(info.GetAgreeNum())
		}
	}

	if mgetBotChatUidByCond != nil && mgetBotChatUidByCond.GetData() != nil && len(mgetBotChatUidByCond.GetData().GetInfo()) > 0 {
		chatList := mgetBotChatUidByCond.GetData().GetInfo()
		for _, chatInfo := range chatList {
			if chatInfo.GetUserId() == uint64(baseData.UserID) && chatInfo.GetBotUid() == baseData.RobotUID {
				interactInfo.SessionRounds = proto.Uint32(chatInfo.GetConversationRound())
			}
		}

		// 利用上次一的sug来作为这次的默认sug
		// 仅处理非游戏智能体
		if robotInfo.GetPaType() == paTypeAiChat {
			if len(chatList) > 0 && chatList[0].GetLastSug() != "" {
				lastSugList := []string{}
				err := json.Unmarshal([]byte(chatList[0].GetLastSug()), &lastSugList)
				if err != nil {
					tbcontext.WarningF(ctx, "sug string json decode to arr failed! err[%s], input[%s]", err.Error(), chatList[0].GetLastSug())
				} else {
					for k, v := range lastSugList {
						if response.GetData().GetDefaultSug() != nil && len(response.GetData().GetDefaultSug()) > k {
							response.Data.DefaultSug[k].AbilityConf.Content.Text = proto.String(string(v))
							response.Data.DefaultSug[k].StyleConf.Content = proto.String(string(v))
						}
					}
				}
			}
		}
	}

	// 拼接智能体配置状态数据
	if len(setIDs) == 0 && mgetUserBotSetInfoByCond != nil && mgetUserBotSetInfoByCond.GetData() != nil &&
		mgetUserBotSetInfoByCond.GetData().GetInfo() != nil {
		botSetData := mgetUserBotSetInfoByCond.GetData().GetInfo()[0]
		if botSetData.GetExt() != "" {
			extData := &chat.UserBotSetExt{}
			err := json.Unmarshal([]byte(botSetData.GetExt()), &extData)
			if err == nil {
				if botSetData.GetCron() != "" && extData.GetTriggerTime() < uint64(time.Now().Unix()) {
					interactInfo.BotCronStatus = proto.Uint32(types.BotSetInvalid)
				}
				if extData.GetSetPassInfo() != nil && len(extData.GetSetPassInfo().GetSetIds()) > 0 {
					interactInfo.UserAgentSet = proto.String(common.ToString(extData.GetSetPassInfo().GetSetIds()))
				}
			}
		}
		if botSetData.GetName() != "" {
			response.Data.UserInfo.Name = proto.String(botSetData.GetName())
			titleInfos := response.GetData().GetUserInfo().GetIntroduceCardTitleInfo()
			for idx, info := range titleInfos {
				if info.GetIconType() == 2 {
					titleInfos[idx].Content = proto.String(botSetData.GetName())
				}
			}
			response.Data.UserInfo.IntroduceCardTitleInfo = titleInfos
		}

		if botSetData.GetBackground() != "" {
			response.Data.Config.ChatBackground = proto.String(botSetData.GetBackground())
			response.Data.Config.PersonBackground = proto.String(botSetData.GetBackground())
		}
		interactInfo.BotSetStatus = proto.Uint32(types.BotSetAuditPass)
	}

	// 请求传了配置id 需要覆盖数据
	if mgetUserBotSetByIDs != nil && mgetUserBotSetByIDs.GetData() != nil && mgetUserBotSetByIDs.GetData().GetInfo() != nil {
		setMap := mgetUserBotSetByIDs.GetData().GetInfo()
		var setUserID uint64
		for _, set := range setMap {
			setUserID = set.GetUserId()
			if set.GetContent() == "" {
				continue
			}
			if set.GetSetType() == types.BotSetTypeName && set.GetAuditStatus() == types.BotSetAuditPass {
				response.Data.UserInfo.Name = proto.String(set.GetContent())
				titleInfos := response.GetData().GetUserInfo().GetIntroduceCardTitleInfo()
				for idx, info := range titleInfos {
					if info.GetIconType() == 2 {
						titleInfos[idx].Content = proto.String(set.GetContent())
					}
				}
				response.Data.UserInfo.IntroduceCardTitleInfo = titleInfos
			}
			if set.GetSetType() == types.BotSetTypeBackground && set.GetAuditStatus() == types.BotSetAuditPass {
				response.Data.Config.ChatBackground = proto.String(set.GetContent())
				response.Data.Config.PersonBackground = proto.String(set.GetContent())
			}
		}
		// 获取用户名称
		getUserNameReq := &user.GetUnameByUidsReq{
			UserId: []int64{int64(setUserID)},
		}
		getUserNameRes := &user.GetUnameByUidsRes{}
		err = tbservice.Call(ctx, "user", "getUnameByUids", getUserNameReq, getUserNameRes, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || getUserNameRes.Errno == nil || getUserNameRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call user:getUnameByUids fail, err:[%v], output:[%s]", err, common.ToString(res))
		}
		if getUserNameRes.GetOutput() != nil && len(getUserNameRes.GetOutput().GetUnames()) > 0 {
			unames := getUserNameRes.GetOutput().GetUnames()
			for _, uName := range unames {
				if uName.GetUserId() == int64(setUserID) && setUserID != uint64(baseData.UserID) {
					interactInfo.SetAuthorName = proto.String(uName.GetUserName())
				}
			}
		}
	}
	response.Data.InteractInfo = interactInfo

	// 主客态
	response.Data.IsPartialVisible = proto.Uint32(types.PartialVisibleGuest)
	if robotInfo.GetUserInfo().GetCreateUid() == baseData.UserID {
		response.Data.IsPartialVisible = proto.Uint32(types.PartialVisibleMaster)
	}

	// 异步完成人设对话任务
	go func(ctx context.Context) {
		doAiChatActivityTask(ctx, uint64(baseData.UserID), baseData.RobotUID, uint64(robotInfo.UserInfo.GetPa()), baseData.Request.GetCommon().GetCuid())
	}(logit.CopyAllFields(context.Background(), ctx))

	return tiebaerror.ERR_SUCCESS
}

// 前置校验
func preCheck(ctx context.Context, baseData *types.GetChatDetailBaseData) int {
	baseData.UserID, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	chatUk := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("chat_uk", ""), common.TTT_STRING).(string)
	clientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	// 机器人id校验
	robotUID, err := uidxuk.UK2UID(chatUk)
	if err != nil || robotUID <= 0 {
		tbcontext.WarningF(ctx, "GetChatDetail input param error, chatUk = %v", chatUk)
		return tiebaerror.ERR_PARAM_ERROR
	}
	baseData.RobotUID = robotUID

	// 版控
	if clientvers.Compare("12.46.0.0", clientVersion) < 0 {
		tbcontext.WarningF(ctx, "GetChatDetail input param error, clientVersion = %v", clientVersion)
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 用户ID校验
	if baseData.UserID == 0 {
		tbcontext.WarningF(ctx, "GetChatDetail input param error, uid = %v", baseData.UserID)
		return tiebaerror.ERR_USER_NOT_LOGIN
	}
	// 落地页不做白名单校验了
	// isWhite := isHitAigcCommonWhiteList(ctx, baseData.UserID)
	// if !isWhite {
	//	 tbcontext.WarningF(ctx, "GetChatDetail uid is not white user, uid = %v", baseData.UserID)
	//	 return tiebaerror.ERR_AICHAT_NOT_WHITE_LIST
	// }

	// 当aichat来源为frs，需要传入fid
	if baseData.Request.GetFromPage() == aichatFromFrs && baseData.Request.GetFid() <= 0 {
		tbcontext.WarningF(ctx, "from_page is frs, but fid is null")
		return tiebaerror.ERR_PARAM_ERROR
	}
	return tiebaerror.ERR_SUCCESS
}

// checkAutoAddAichatWhiteListNum 检查每日自动添加人设对话白名单数量
func checkAutoAddAichatWhiteListNum(ctx context.Context) bool {
	maxNum := 0
	num := math.MaxInt // 默认设为最大，防止没有取到时，返回true

	var wg sync.WaitGroup
	wg.Add(2)
	go func() {
		// 查上限配置
		defer wg.Done()
		res, err := wordserver.QueryKey(ctx, aiChatConfWordList, autoAddAichatWhitelistMaxNum)
		if err != nil {
			tbcontext.WarningF(ctx, "fail to query wordlist %v, key = %v, err = %v", aiChatConfWordList, autoAddAichatWhitelistMaxNum, err)
			return
		}
		maxNum, _ = strconv.Atoi(res)
	}()
	go func() {
		// 查当日已添加用户
		defer wg.Done()
		redisRes, err := resource.RedisMsglogic.Get(ctx, autoAddAichatWhitelistNum).Result()
		// redis为空，特殊处理
		if err == redis.ErrNil {
			err = nil
			redisRes = "0"
		}
		// 网络错误
		if err != nil {
			tbcontext.WarningF(ctx, "checkAutoAddAichatWhiteListNum get redis failed. err=%v, key=%s", err, autoAddAichatWhitelistNum)
			return
		}
		num, _ = strconv.Atoi(redisRes)
	}()
	wg.Wait()

	return num < maxNum
}

// 根据传入的recent_bots 对 bot_list 进行排序
func botInfoOderByUk(ctx context.Context, recentBotsStr string, botList []*getChatDetail.BotInfo, botNum int) ([]*getChatDetail.BotInfo, int) {

	// 吧内角色数为0时，不根据uk进行排序
	if botNum == 0 {
		return botList, tiebaerror.ERR_SUCCESS
	}

	if len(recentBotsStr) == 0 {
		tbcontext.WarningF(ctx, "recent_bots is empty")
		return botList, tiebaerror.ERR_SUCCESS
	}

	recentBots := strings.Split(recentBotsStr, ",")

	if botList == nil {
		tbcontext.WarningF(ctx, "input wrong: botList = %s", common.ToString(botList))
		return nil, tiebaerror.ERR_PARAM_ERROR
	}

	botListMapRecent := map[string]struct{}{}
	botListMapAll := map[string]*getChatDetail.BotInfo{}
	botListRes := []*getChatDetail.BotInfo{}

	for _, botinfo := range botList {
		botListMapAll[botinfo.GetUk()] = botinfo
	}

	for _, uk := range recentBots {
		botInfo := botListMapAll[uk]
		if botInfo == nil {
			continue
		}
		// 防止传参recent_bots中有重复
		if _, ok := botListMapRecent[botInfo.GetUk()]; ok {
			continue
		}
		botListMapRecent[uk] = struct{}{}
		botListRes = append(botListRes, botInfo)
	}

	for _, botInfo := range botList {
		if botInfo == nil {
			continue
		}
		if _, ok := botListMapRecent[botInfo.GetUk()]; !ok {
			botListRes = append(botListRes, botInfo)
		}
	}

	return botListRes, tiebaerror.ERR_SUCCESS
}
func getGuideInfo(ctx context.Context) []*getChatDetail.GuideInfo {
	redisKeys := []string{agreeGuideInfo, followGuideInfo, shareGuideInfo}
	tableName := aichatActivityTableName
	redisRes, err := wordserver.QueryKeys(ctx, tableName, redisKeys)
	guideInfoAll := make([]*getChatDetail.GuideInfo, 0)
	if err != nil {
		tbcontext.WarningF(ctx, "wordserver query keys fail, input=%s, err=%v", common.ToString(redisKeys), err)
		return guideInfoAll
	}
	for _, info := range redisRes {
		if info == "" {
			continue
		}
		guideInfo := getChatDetail.GuideInfo{}
		err := jsoniter.Unmarshal([]byte(info), &guideInfo)
		if err != nil {
			tbcontext.WarningF(ctx, "getGuideInfo fail to decode raw input, input = %s, err = %v", info, err)
			continue
		}
		guideInfoAll = append(guideInfoAll, &guideInfo)
	}
	return guideInfoAll
}

func getInputboxText(ctx context.Context, baseData *types.GetChatDetailBaseData) string {
	if baseData == nil {
		tbcontext.WarningF(ctx, "param error, input = %s", common.ToString(baseData))
		return ""
	}

	// 接入内测和众测
	switchConf, _ := activity.GetActivitySceneConfig(ctx, baseData.UserID, 0, 0, "")
	if springActivity, isExist := switchConf[10]; !isExist {
		tbcontext.WarningF(ctx, "the spring activity is not exist")
		return ""
	} else if activitySwitch, ok := springActivity["activity_switch"]; !ok {
		tbcontext.WarningF(ctx, "the spring activity switch is not exist")
		return ""
	} else if !activitySwitch {
		tbcontext.WarningF(ctx, "the spring activity switch is off")
		return ""
	}

	// 根据uid查cuid
	var cuid string
	mgetCidsByUidsInput := map[string]interface{}{
		"user_ids": []uint64{uint64(baseData.UserID)},
	}
	mgetCidsByUidsOutput := &impusher.MgetCidsByUidsRes{}
	err := tbservice.Call(ctx, "impusher", "mgetCidsByUids", mgetCidsByUidsInput, mgetCidsByUidsOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || mgetCidsByUidsOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "getInputboxText call impusher::mgetCidsByUids failed, input:[%s], output:[%s], err=[%v]", common.ToString(mgetCidsByUidsInput), common.ToString(mgetCidsByUidsOutput), err)
	}
	cuidMap := map[uint64][]*impusher.UidInfoRes{}
	err = common.StructAToStructBCtx(ctx, mgetCidsByUidsOutput.GetData(), &cuidMap)
	if err != nil {
		tbcontext.WarningF(ctx, "getInputboxText Unmarshal fail, err:[%v], data:[%s]", err, mgetCidsByUidsOutput.GetData())
	}
	if value, ok := cuidMap[uint64(baseData.UserID)]; ok && len(value) > 0 {
		cuid = value[0].GetCuid()
	} else {
		tbcontext.WarningF(ctx, "getInputboxText get cuid fail, cuid is empty")
	}

	// 查任务中台配置的当前活动的开始和结束时间，如果不在此时间区间内，直接返回
	getUserTaskListByActTypeInput := map[string]interface{}{
		"user_id":           baseData.UserID,
		"act_type":          "aichat_hit_words",
		"cuid":              cuid,
		"call_from":         "client",
		"is_filter_valid":   1,
		"need_check_switch": true,
		"data": map[string]interface{}{
			"robot_id": baseData.RobotUID,
		},
	}
	getUserTaskListByActTypeOut := &usertask.GetUserTaskListByActTypeRes{}
	err = tbservice.Call(ctx, "usertask", "getUserTaskListByActType", getUserTaskListByActTypeInput, getUserTaskListByActTypeOut,
		tbservice.WithConverter(tbservice.JSONITER), tbservice.WithTimeout(1500*time.Millisecond))
	if err != nil || getUserTaskListByActTypeOut.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call service usertask::getUserTaskListByActType failed, input = %s, output = %s", common.ToString(getUserTaskListByActTypeInput), common.ToString(getUserTaskListByActTypeOut))
		return ""
	}
	if getUserTaskListByActTypeOut.GetData().GetTaskList() == nil {
		tbcontext.WarningF(ctx, "task list empty, input = %s, output = %s", common.ToString(getUserTaskListByActTypeInput), common.ToString(getUserTaskListByActTypeOut))
		return ""
	}

	var startTime int64
	var endTime int64
	if getUserTaskListByActTypeOut.GetData() != nil && getUserTaskListByActTypeOut.GetData().GetTaskList() != nil &&
		getUserTaskListByActTypeOut.GetData().GetTaskList()[0] != nil {
		startTime = getUserTaskListByActTypeOut.GetData().GetTaskList()[0].GetStartTime()
		endTime = getUserTaskListByActTypeOut.GetData().GetTaskList()[0].GetEndTime()
	}

	if startTime < 0 || endTime < 0 {
		tbcontext.WarningF(ctx, "the activity time range is wrong, startTime = %d, endTime = %d", startTime, endTime)
		return ""
	}

	if time.Now().Unix() < startTime {
		tbcontext.WarningF(ctx, "Not in the activity time")
		return ""
	}

	activityConfOnlyBot := fmt.Sprintf(aichatActivityConfPre, baseData.RobotUID)
	activityConfAll := fmt.Sprintf(aichatActivityConfPre, "all")

	redisKeys := []string{activityConfOnlyBot, activityConfAll}
	tableName := aichatActivityTableName
	redisRes, err := wordserver.QueryKeys(ctx, tableName, redisKeys)
	if err != nil {
		tbcontext.WarningF(ctx, "wordserver query keys fail, input=%s, err=%v", common.ToString(redisKeys), err)
		return ""
	}

	if redisRes[1] != "" {
		activityConfAll := ActivityConfAll{}
		err := jsoniter.Unmarshal([]byte(redisRes[1]), &activityConfAll)
		if err != nil {
			tbcontext.WarningF(ctx, "getInputboxText fail to decode raw input, input = %s, err = %v", redisRes[1], err)
			return ""
		}
		// 如果此时处于全局活动时间范围，判断是否处于单个角色的时间范围
		if time.Now().Unix() < activityConfAll.EndTime && time.Now().Unix() > activityConfAll.StartTime {
			if redisRes[0] != "" {
				activityConfOnlyBot := ActivityConfOnlyBot{}
				err := jsoniter.Unmarshal([]byte(redisRes[0]), &activityConfOnlyBot)
				if err != nil {
					tbcontext.WarningF(ctx, "getInputboxText fail to decode raw input, input = %s, err = %v", redisRes[0], err)
				}
				if time.Now().Unix() < activityConfOnlyBot.EndTime && time.Now().Unix() > activityConfOnlyBot.StartTime {
					if activityConfOnlyBot.InputText != "" {
						return activityConfOnlyBot.InputText
					}
				}
			}
			// 如果不在单角色时间范围，采用全局的输入框配置内容
			if activityConfAll.InputText != "" {
				return activityConfAll.InputText
			}
		}
	}
	return ""
}

func doAiChatActivityTask(ctx context.Context, uid, botId, pa uint64, cuid string) {
	if uid <= 0 || botId <= 0 || pa <= 0 {
		tbcontext.WarningF(ctx, "param error, uid = %d, bot_id = %d, pa = %d", uid, botId, pa)
		return
	}

	// 查任务中台配置的当前活动的开始和结束时间，如果不在此时间区间内或者参与的次数达到活动上限，直接返回
	getUserTaskListByActTypeInput := map[string]interface{}{
		"user_id":           uid,
		"act_type":          "aichat_hit_words",
		"cuid":              cuid,
		"call_from":         "client",
		"is_filter_valid":   1,
		"need_check_switch": true,
		"data": map[string]interface{}{
			"robot_id": botId,
		},
	}
	getUserTaskListByActTypeOut := &usertask.GetUserTaskListByActTypeRes{}
	err := tbservice.Call(ctx, "usertask", "getUserTaskListByActType", getUserTaskListByActTypeInput, getUserTaskListByActTypeOut,
		tbservice.WithConverter(tbservice.JSONITER), tbservice.WithTimeout(1500))
	if err != nil || getUserTaskListByActTypeOut.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call service usertask::getUserTaskListByActType failed, input = %s, output = %s",
			common.ToString(getUserTaskListByActTypeInput), common.ToString(getUserTaskListByActTypeOut))
		return
	}
	if getUserTaskListByActTypeOut.GetData().GetTaskList() == nil {
		tbcontext.WarningF(ctx, "task list empty, input = %s, output = %s",
			common.ToString(getUserTaskListByActTypeInput), common.ToString(getUserTaskListByActTypeOut))
		return
	}

	var startTime, endTime, current, total int64
	if getUserTaskListByActTypeOut.GetData() != nil && getUserTaskListByActTypeOut.GetData().GetTaskList() != nil &&
		getUserTaskListByActTypeOut.GetData().GetTaskList()[0] != nil {
		startTime = getUserTaskListByActTypeOut.GetData().GetTaskList()[0].GetStartTime()
		endTime = getUserTaskListByActTypeOut.GetData().GetTaskList()[0].GetEndTime()
		current = getUserTaskListByActTypeOut.GetData().GetTaskList()[0].GetTaskProgress().GetCurrent()
		total = getUserTaskListByActTypeOut.GetData().GetTaskList()[0].GetTaskProgress().GetTotal()
	}

	if startTime < 0 || endTime < 0 {
		tbcontext.WarningF(ctx, "the activity time range is wrong, startTime = %d, endTime = %d", startTime, endTime)
		return
	}

	if time.Now().Unix() < startTime {
		tbcontext.WarningF(ctx, "Not in the activity time")
		return
	}

	if current >= total {
		tbcontext.NoticeF(ctx, "The number of times the user participates in the activity has reached the limit")
		return
	}

	// 限制每个角色每天只触发一次任务
	redisCheckKey := fmt.Sprintf(aichatActivityEntryUidBotUid, uid, botId)
	redisCheckRes, err := resource.RedisMsglogic.Get(ctx, redisCheckKey).Result()
	if err != nil && err != redis.ErrNil {
		tbcontext.WarningF(ctx, "doAiChatActivityTask get redis failed. err=%v, key=%s", err, redisCheckKey)
		return
	}
	if redisCheckRes == "1" {
		tbcontext.NoticeF(ctx, "the user already hit this bot's keyword, uid=%d, botid=%d", uid, botId)
		return
	}

	activityConfOnlyBotKey := fmt.Sprintf(aichatActivityConfPre, botId)
	activityConfAllKey := fmt.Sprintf(aichatActivityConfPre, "all")
	redisKeys := []string{activityConfOnlyBotKey, activityConfAllKey, aichatActivityCurrentChooseType, aichatActivityPopupInfo, aichatActivityImgInfo}
	tableName := aichatActivityTableName
	redisRes, err := wordserver.QueryKeys(ctx, tableName, redisKeys)
	if err != nil {
		tbcontext.WarningF(ctx, "wordserver query keys fail, input=%s, err=%v", common.ToString(redisKeys), err)
		return
	}

	// 不触发，直接返回
	if !doAiChatActivityTaskIsActive(ctx, redisRes) {
		return
	}

	// 延迟两秒进行做任务和发消息，保证fe在收到接口返回并渲染后才收到消息
	time.Sleep(2 * time.Second)

	// 做任务
	doUserTaskByActTypeInput := map[string]interface{}{
		"user_id":           uid,
		"act_type":          "aichat_hit_words",
		"call_from":         "client",
		"cuid":              cuid,
		"is_filter_valid":   1,
		"need_check_switch": true,
		"data": map[string]interface{}{
			"robot_id": botId,
		},
	}
	doUserTaskByActTypeOutput := &usertask.DoUserTaskByActTypeRes{}
	err = tbservice.Call(ctx, "usertask", "doUserTaskByActType", doUserTaskByActTypeInput, doUserTaskByActTypeOutput,
		tbservice.WithConverter(tbservice.JSONITER), tbservice.WithTimeout(1500))
	if err != nil || doUserTaskByActTypeOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "doAiChatActivityTask call usertask::doUserTaskByActType failed, input:[%s], output:[%s], err=[%v]",
			common.ToString(doUserTaskByActTypeInput), common.ToString(doUserTaskByActTypeOutput), err)
	}

	popupInfoMap := make(map[string]string)
	extraType := int32(MsgExtraTypePopup)
	if redisRes[2] != "" {
		extraType = common.Tvttt(redisRes[2], common.TTT_INT32).(int32)
		switch extraType {
		case MsgExtraTypePopup:
			// 文本触发弹窗
			err = jsoniter.Unmarshal([]byte(redisRes[3]), &popupInfoMap)
			if err != nil {
				tbcontext.WarningF(ctx, "fail to decode raw input, input = %s, err = %v", redisRes[3], err)
				return
			}
			break
		case MsgExtraTypeActiveImg:
			// 文本触发红包图片（自定义图片）
			err = jsoniter.Unmarshal([]byte(redisRes[4]), &popupInfoMap)
			if err != nil {
				tbcontext.WarningF(ctx, "fail to decode raw input, input = %s, err = %v", redisRes[5], err)
				return
			}
			break
		default:
			tbcontext.WarningF(ctx, " extraType invalid %d", extraType)
			return
		}
	}

	// 如果配置了单角色的消息配置，用单角色的消息配置替换
	if redisRes[0] != "" {
		activityConfOnlyBot := ActivityConfOnlyBot{}
		err = jsoniter.Unmarshal([]byte(redisRes[0]), &activityConfOnlyBot)
		if err != nil {
			tbcontext.WarningF(ctx, "doAiChatActivityTask fail to decode raw input, input = %s, err = %v", redisRes[0], err)
		}
		if activityConfOnlyBot.MsgType > 0 && activityConfOnlyBot.PopupInfo != nil {
			extraType = activityConfOnlyBot.MsgType
			popupInfoMap = activityConfOnlyBot.PopupInfo
		}
	}

	// 发送B2C消息
	msgKey := strconv.FormatInt(time.Now().UnixNano(), 16)
	sendInput := &chat.SendTextMsgForPersonalChatReq{
		Pa:        proto.String(strconv.FormatUint(pa, 10)),
		Content:   proto.String("活动消息"), // 该场景前端不读该字段，传值跳过下游校验
		ToUid:     proto.Int64(int64(uid)),
		MsgKey:    proto.String(msgKey),
		PopupInfo: popupInfoMap,
		ExtraType: proto.Int32(extraType),
	}
	sendOutput := new(chat.SendTextMsgForPersonalChatRes)
	err = tbservice.Call(ctx, "chat", "sendTextMsgForPersonalChat", sendInput, sendOutput,
		tbservice.WithConverter(tbservice.JSONITER), tbservice.WithTimeout(1500))
	if err != nil || sendOutput == nil || sendOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service chat:sendTextMsgForPersonalChat, err = %v, input = %v, output = %v",
			err, common.ToString(sendInput), common.ToString(sendOutput))
		return
	}

	if text, ok := popupInfoMap["text"]; ok && text != "" {
		sendInput = &chat.SendTextMsgForPersonalChatReq{
			Pa:      proto.String(strconv.FormatUint(pa, 10)),
			Content: proto.String(popupInfoMap["text"]),
			ToUid:   proto.Int64(int64(uid)),
			MsgKey:  proto.String(strconv.FormatInt(time.Now().UnixNano(), 16)),
		}
		sendOutput = new(chat.SendTextMsgForPersonalChatRes)
		err = tbservice.Call(ctx, "chat", "sendTextMsgForPersonalChat", sendInput, sendOutput,
			tbservice.WithConverter(tbservice.JSONITER), tbservice.WithTimeout(1500))
		if err != nil || sendOutput.Errno == nil || sendOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service chat:sendTextMsgForPersonalChat, err = %v, input = %v, output = %v",
				err, common.ToString(sendInput), common.ToString(sendOutput))
		}
	}

	// 获取当前时间
	currentTime := time.Now()
	// 计算当天24点的时间
	expiryTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()+1, 0, 0, 0, 0, currentTime.Location())
	// 获取时间戳（以秒为单位）
	expireTime := expiryTime.Unix() - currentTime.Unix()
	_, err = resource.RedisMsglogic.Set(ctx, redisCheckKey, 1, time.Duration(expireTime)*time.Second).Result()
	if err != nil {
		tbcontext.WarningF(ctx, "doAiChatActivityTask set redis failed. err=%v, key=%s", err, redisCheckKey)
		return
	}

	return
}

func doAiChatActivityTaskIsActive(ctx context.Context, redisRes []string) bool {
	if redisRes[1] == "" {
		return false
	}

	activityConfAll := ActivityConfAll{}
	err := jsoniter.Unmarshal([]byte(redisRes[1]), &activityConfAll)
	if err != nil {
		tbcontext.WarningF(ctx, "doAiChatActivityTask fail to decode raw input, input = %s, err = %v", redisRes[1], err)
		return false
	}
	// 先判断全局活动时间范围
	if time.Now().Unix() < activityConfAll.EndTime && time.Now().Unix() > activityConfAll.StartTime {
		if redisRes[0] != "" {
			// 有单角色配置，优先判断单角色配置
			activityConfOnlyBot := ActivityConfOnlyBot{}
			err = jsoniter.Unmarshal([]byte(redisRes[0]), &activityConfOnlyBot)
			if err != nil {
				tbcontext.WarningF(ctx, "doAiChatActivityTask fail to decode raw input, input = %s, err = %v", redisRes[0], err)
			}
			if time.Now().Unix() < activityConfOnlyBot.EndTime && time.Now().Unix() > activityConfOnlyBot.StartTime && activityConfOnlyBot.EntryTrigger == 1 {
				return true
			}
		} else {
			// 没有单角色配置，判断全局配置
			if activityConfAll.EntryTrigger == 1 {
				return true
			}
		}
	}
	return false
}

func formatUserInfo(ctx context.Context, uid int64, robotInfo *chat.AichatUserInfo,
	request *getChatDetail.GetChatDetailReqIdl, response *getChatDetail.GetChatDetailResIdl) int {
	// 读取词表
	queryKeys := []string{
		aiChatSugIcon,
	}
	res, err := wordserver.QueryItemsNoPHPSerialized(ctx, aiInteractiveGamePlotTable, queryKeys)
	if err != nil || len(res) <= 0 {
		tbcontext.WarningF(ctx, "Fail to get ai_interactive_game_plot word list, error = %v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 目前仅在游戏剧情模式下下发该字段
	if sugStr, ok := res[aiChatSugIcon].(string); ok {
		if robotInfo.GetPaType() == paTypeGamePlot {
			response.Data.SugIcon = common.GetStringPtr(sugStr)
		}
	}

	if robotInfo.GetPaType() == paTypeAiChat {
		response.Data.UserInfo.ChatUk = common.GetStringPtr(request.GetData().GetChatUk())
		// 根据chat_uk获取基础信息 getAiInteractiveGamePlotByChatUk
		getBotGameInfoByUkInput := &chat.GetAiInteractiveGamePlotByChatUKReq{
			Uid:    proto.Int64(uid),
			ChatUk: []string{request.GetData().GetChatUk()},
		}
		getBotGameInfoByUkOutput := &chat.GetAiInteractiveGamePlotByChatUKRes{}

		err := tbservice.Call(ctx, "chat", "getAiInteractiveGamePlotByChatUk",
			getBotGameInfoByUkInput,
			&getBotGameInfoByUkOutput,
			tbservice.WithConverter(tbservice.JSONITER))

		if err != nil || getBotGameInfoByUkOutput.Errno == nil ||
			getBotGameInfoByUkOutput.GetErrno() != tiebaerror.ERR_SUCCESS ||
			getBotGameInfoByUkOutput.GetData() == nil {
			tbcontext.WarningF(ctx, "call chat:getAiInteractiveGamePlotByChatUk fail, err:[%v], output:[%s]", err, common.ToString(getBotGameInfoByUkOutput))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		if _, ok := getBotGameInfoByUkOutput.GetData().GetAiInteractiveGamePlot()[request.GetData().GetChatUk()]; !ok {
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		plotList := make([]*getChatDetail.Plot, 0)
		for _, plot := range getBotGameInfoByUkOutput.GetData().GetAiInteractiveGamePlot()[request.GetData().GetChatUk()].GetPlot() {
			plotList = append(plotList, &getChatDetail.Plot{
				GameUk: common.GetStringPtr(plot.GetBotUk()),
				PlotId: common.GetInt64Ptr(plot.GetId()),
			})
		}
		response.Data.UserInfo.Plot = plotList
	} else if robotInfo.GetPaType() == paTypeGamePlot {
		//读词表改为读db rpc调用获取数据
		multi := tbservice.Multi()
		aiChatBindParam := &tbservice.Parameter{
			Service: "chat",
			Method:  "mgetAichatBotUidBind",
			Input: map[string]interface{}{
				"is_deleted": 0,
			},
			Output: &chat.MgetAichatBotUIDBindRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetAichatBotUidBind", aiChatBindParam)
		multi.Call(ctx)

		aiChatOutBindInfo, err := multi.GetResult(ctx, "mgetAichatBotUidBind")
		if err != nil {
			tbcontext.WarningF(ctx, "fail to call service chat:mgetAichatBotUidBind, err = %v, input = %v, output = %v", err,
				common.ToString(aiChatBindParam.Input), common.ToString(aiChatOutBindInfo))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		aiChatOutBind := aiChatOutBindInfo.(*chat.MgetAichatBotUIDBindRes)
		if aiChatOutBind == nil || aiChatOutBind.Errno == nil || aiChatOutBind.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service chat:mgetAichatBotUidBind, err = %v, input = %v, output = %v", err,
				common.ToString(aiChatBindParam.Input), common.ToString(aiChatOutBind))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		// 	更新chat_uk
		uk := ""
		if aiChatOutBind.GetData() != nil && aiChatOutBind.GetData().Data != nil && len(aiChatOutBind.GetData().Data) > 0 {
			for _, bind := range aiChatOutBind.GetData().Data {
				if bind != nil && bind.BotUid != nil && bind.RelatedUid != nil {
					chatUk, _ := uidxuk.UID2UK(uint64(*bind.BotUid))
					gameUk, _ := uidxuk.UID2UK(uint64(*bind.RelatedUid))
					if gameUk == request.GetData().GetChatUk() {
						uk = chatUk
						break
					}
				}
			}
		}

		if uk != "" {
			response.Data.UserInfo.ChatUk = common.GetStringPtr(uk)
		}

		response.Data.UserInfo.Plot = PlotBasicToPlot(robotInfo.GetPlot())

		// chat_uk 转成chat_uid
		chatUID, err := uidxuk.UK2UID(uk)

		// 用chat_uid查机器人信息，拿到chat_pa
		aiChatParam := &tbservice.Parameter{
			Service: "chat",
			Method:  "mgetAichatUserInfoByUid",
			Input: map[string]interface{}{
				"uids":      []uint64{chatUID},
				"need_list": false,
				"user_id":   uid,
			},
			Output: &chat.MgetAichatUserInfoByUidRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetAichatUserInfoByUid", aiChatParam)
		multi.Call(ctx)

		aiChatOutInf, err := multi.GetResult(ctx, "mgetAichatUserInfoByUid")
		if err != nil {
			tbcontext.WarningF(ctx, "fail to call service chat:mgetAichatUserInfoByUid, err = %v, input = %v, output = %v", err,
				common.ToString(aiChatParam.Input), common.ToString(aiChatOutInf))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		aiChatOut := aiChatOutInf.(*chat.MgetAichatUserInfoByUidRes)
		if aiChatOut == nil || aiChatOut.Errno == nil || aiChatOut.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service chat:mgetAichatUserInfoByUid, err = %v, input = %v, output = %v", err,
				common.ToString(aiChatParam.Input), common.ToString(aiChatOut))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		// 添加到返回值
		response.Data.UserInfo.ChatPa = proto.Int64(aiChatOut.GetData().AichatUserinfoList[int64(chatUID)].UserInfo.GetPa())
	}
	return 0
}

func PlotBasicToPlot(plots []*chat.AiGamePlotBasic) []*getChatDetail.Plot {
	gamePlots := make([]*getChatDetail.Plot, 0)
	for _, plot := range plots {
		gamePlots = append(gamePlots, &getChatDetail.Plot{
			GameUk: plot.GameUk,
			PlotId: plot.PlotId,
			GamePa: plot.GamePa,
		})
	}
	return gamePlots
}
