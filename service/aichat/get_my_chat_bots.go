package aichat

import (
	"context"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/constants"
	"strconv"

	"github.com/golang/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getMyChatBots"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func DoGetMyChatBots(ctx context.Context, baseData *types.GetMyChatBotsBaseData, response *getMyChatBots.GetMyChatBotsResIdl) int {
	baseData.UserID, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)

	// 1 读词表获取创建角色最大数量
	wordlistRes, err := wordserver.QueryKey(ctx, aiChatConfWordList, "aichat_create_bot_user_limit")
	if err != nil {
		tbcontext.WarningF(ctx, "fail to query wordlist %v, err = %v", aiChatConfWordList, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	maxNum, err := strconv.ParseInt(wordlistRes, 10, 64)
	if err != nil {
		tbcontext.WarningF(ctx, "parse max_num fail, err=[%v], wordlist_res=[%s]", err, wordlistRes)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	response.Data.MaxAichatBotNum = proto.Int64(maxNum)

	// 2 调用go-chat获取用户创建的机器人列表
	chatInput := map[string]any{
		"uid": baseData.UserID,
	}
	chatOutput := &chat.GetAichatBotInfoByCreateUidRes{}

	err = tbservice.Call(ctx, "chat", "getAichatBotInfoByCreateUid", chatInput, &chatOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || chatOutput.Errno == nil || chatOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:getAichatBotInfoByCreateUid fail, err:[%v], output:[%s]", err, common.ToString(chatOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	botList := chatOutput.GetData()
	if len(botList) == 0 {
		response.Data.AichatBotList = make([]*getMyChatBots.BotInfo, 0)
		return tiebaerror.ERR_SUCCESS
	}

	// 3 调用user获取每个机器人的关注数量
	botUIDs := make([]int64, len(botList))
	for _, botInfo := range botList {
		botUIDs = append(botUIDs, botInfo.GetUserInfo().GetUid())
	}
	userInput := map[string]any{
		"user_id":          botUIDs,
		"need_follow_info": 1,
	}
	userOutput := &user.MgetUserDataExRes{}
	err = tbservice.Call(ctx, "user", "mgetUserDataEx", userInput, &userOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || userOutput.Errno == nil || userOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call user:mgetUserDataEx fail, err:[%v], output:[%s]", err, common.ToString(userOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 4 组装数据
	resBotList := make([]*getMyChatBots.BotInfo, 0, len(botList))
	for _, botInfo := range botList {
		// bot基础信息
		botUserInfo := botInfo.GetUserInfo()
		if botUserInfo == nil {
			continue
		}

		resBotInfo := &getMyChatBots.BotInfo{
			BotUk:         botUserInfo.Uk,
			Portrait:      botUserInfo.Portrait,
			BotName:       botUserInfo.Name,
			Paid:          botUserInfo.Pa,
			VisibleStatus: proto.Int64(int64(botInfo.GetVisibleStatus())),
			DialogueNum:   proto.Int64(int64(botUserInfo.GetDialogNum())),
			UpdateTime:    botInfo.UpdateTime,
			AuditStatus:   proto.Int64(constants.AuditStatusMap[int64(botInfo.GetAuditStatus())]),
		}

		if botInfo.Config != nil {
			resBotInfo.ChatBackground = botInfo.Config.ChatBackground
		}
		// bot被关注数量
		if userData, ok := userOutput.UserInfo[uint64(botUserInfo.GetUid())]; ok {
			resBotInfo.FollowNum = userData.FollowedCount
		}
		resBotList = append(resBotList, resBotInfo)
	}
	response.Data.AichatBotList = resBotList

	return tiebaerror.ERR_SUCCESS
}
