package aichat

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/aigcprocesser"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	userProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/baidu/uidxuk"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	p "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getAgentDetail"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	AgentDetailTipsKey     = "agent_detail_tips_pic"
	AgentChatDetailConfKey = "agent_chat_detail_conf"

	SourcePageFrs    = "frs"
	SourcePageSearch = "search"
	SourcePageTopic  = "topic"
)

// DoGetAgentDetail 获取游戏剧情信息和游戏进度信息
func DoGetAgentDetail(ctx context.Context, baseData *types.GetAgentDetailBaseData, response *p.GetAgentDetailResIdl) int {
	botKey, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("bot_uid", ""), common.TTT_STRING).(string)
	userID, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	sourcePage, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("source_page", 0), common.TTT_STRING).(string)
	// 获取uk
	botUid, err := uidxuk.UK2UID(botKey)
	if err != nil {
		botUid = uint64(cast.ToInt64(botKey))
	}
	// 校验入参是否合法
	if botUid <= 0 || userID < 0 {
		tbcontext.WarningF(ctx, "getAgentHistory check fid and userID error")
		return tiebaerror.ERR_PARAM_ERROR
	}
	multi := tbservice.Multi()
	// 获取bot信息
	mgetAiBotUserInfoByUidParam := &tbservice.Parameter{
		Service: "chat",
		Method:  "mgetAiBotUserInfoByUid",
		Input: &chat.MgetAiBotUserInfoByUidReq{
			BotUids:                []int64{int64(botUid)},
			NeedFilterOnlineStatus: proto.Int32(1),
		},
		Output: &chat.MgetAiBotUserInfoByUidRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetAiBotUserInfoByUid", mgetAiBotUserInfoByUidParam)
	// 根据 uid-bot_uid 获取会话记录
	mgetUserChatAibotByCondParam := &tbservice.Parameter{
		Service: "chat",
		Method:  "mgetUserChatAibotByCond",
		Input: &chat.MgetUserChatAibotByCondReq{
			Uid:     proto.Uint64(uint64(userID)),
			RobotId: proto.Uint64(uint64(botUid)),
		},
		Output: &chat.MgetUserChatAibotByCondRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetUserChatAibotByCond", mgetUserChatAibotByCondParam)
	userParams := &tbservice.Parameter{
		Service: "user",
		Method:  "mgetUserDataEx",
		Input: &userProto.MgetUserDataExReq{
			UserId: []int64{userID},
		},
		Output: &userProto.MgetUserDataExRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	// 获取当前智能体的绑定基础智能体
	multi.Register(ctx, "mgetUserDataEx", userParams)
	aiChatBindParam := &tbservice.Parameter{
		Service: "chat",
		Method:  "mgetAichatBotUidBind",
		Input: map[string]interface{}{
			"is_deleted": 0,
		},
		Output: &chat.MgetAichatBotUIDBindRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetAichatBotUidBind", aiChatBindParam)
	multi.Call(ctx)

	// bot 信息
	aiBotInfoOutput, err := multi.GetResult(ctx, "mgetAiBotUserInfoByUid")
	if err != nil {
		tbcontext.WarningF(ctx, "call chat::mgetAiBotUserInfoByUid fail: %v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	var aiBotInfoRes *chat.MgetAiBotUserInfoByUidRes
	var ok bool
	if aiBotInfoRes, ok = aiBotInfoOutput.(*chat.MgetAiBotUserInfoByUidRes); !ok {
		tbcontext.WarningF(ctx, "call chat::mgetAiBotUserInfoByUid fail: %v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	botInfo := &chat.BotAndPlotData{}
	if botInfo, ok = aiBotInfoRes.GetData().GetBotList()[int64(botUid)]; !ok {
		tbcontext.WarningF(ctx, "call chat::mgetAiBotUserInfoByUid bot info is Nil")
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	// 封装bot信息
	var defaultSug []string
	if err = json.Unmarshal([]byte(botInfo.GetDefaultSugContent()), &defaultSug); err != nil {
		tbcontext.WarningF(ctx, "json unmarshal fail: %v", err)
	}
	// 获取对话页sug前的icon
	botDetail := &p.BotInfo{
		AgentId:           botInfo.AgentId,
		BotUid:            botInfo.BotUid,
		BotName:           botInfo.Name,
		BotAvatar:         botInfo.Portrait,
		Greeting:          botInfo.Prologue,
		DefaultSugTipsPic: proto.String(""),
		DefaultSugContent: defaultSug,
		IsGame:            proto.Int32(0),
		BotBackgroundUrl:  botInfo.BackgroundUrl,
		BotType:           proto.Int32(botInfo.GetBotType()),
		RobotVersion:      proto.Int32(botInfo.GetBotVersion()),
	}
	botUK, _ := uidxuk.UID2UK(botUid)
	botDetail.Uk = proto.String(botUK)
	// 获取tips图片
	arrResults, err := wordserver.QueryItemsNoPHPSerialized(ctx, "tb_wordlist_redis_aichat_frs_conf",
		[]string{AgentDetailTipsKey, AgentChatDetailConfKey})
	if err != nil || arrResults == nil {
		tbcontext.WarningF(ctx, "getAgentDetail fail to get wordlist, err:[%v]", err)
	}
	agentDetailTips, _ := common.Tvttt(arrResults[AgentDetailTipsKey], common.TTT_STRING).(string)
	// 从词表读取详情页配置文案
	agentChatDetailConfWordlist, _ := common.Tvttt(arrResults[AgentChatDetailConfKey], common.TTT_STRING).(string)
	if sourcePage != SourcePageFrs {
		agentChatDetailConf := &types.AgentChatDetailConf{}
		err = json.Unmarshal([]byte(agentChatDetailConfWordlist), &agentChatDetailConf)
		if err != nil {
			tbcontext.WarningF(ctx, "getAgentDetail Unmarshal fail, err:[%v]", err)
		}
		sourceText := agentChatDetailConf.BotSourceText
		if botInfo.GetAiGamePlot() != nil && botInfo.GetPaType() == 1 {
			sourceText = agentChatDetailConf.GameSourceText
		}
		response.Data.ConfContent = &p.ConfContent{
			SourceText: proto.String(sourceText),
		}
	}
	chatUID := botUid
	var gamePlotId int64 = 0
	if botInfo.GetAiGamePlot() != nil && botInfo.GetPaType() == 1 {
		botDetail.DefaultSugTipsPic = proto.String(agentDetailTips)
		botDetail.IsGame = proto.Int32(1)
		gamePlot := botInfo.GetAiGamePlot()
		botDetail.GamePlot = &p.Plot{
			PlotId:             gamePlot.PlotId,
			PlotTitle:          gamePlot.PlotTitle,
			PlotDescription:    gamePlot.PlotDescription,
			InitScore:          gamePlot.InitScore,
			PassScore:          gamePlot.PassScore,
			RoundLimit:         gamePlot.RoundLimit,
			SuccessInfo:        gamePlot.SuccessInfo,
			RoundLimitExceeded: gamePlot.RoundLimitExceeded,
			ProcessNegative:    gamePlot.ProcessNegative,
			HasRecord:          proto.Int32(0),
		}
		gamePlotId = gamePlot.GetPlotId()
		// 智能体绑定关系
		aiChatOutBindInfo, err := multi.GetResult(ctx, "mgetAichatBotUidBind")
		if err != nil {
			tbcontext.WarningF(ctx, "fail to call service chat:mgetAichatBotUidBind, err = %v, input = %v, output = %v", err,
				common.ToString(aiChatBindParam.Input), common.ToString(aiChatOutBindInfo))
		}
		aiChatOutBind := aiChatOutBindInfo.(*chat.MgetAichatBotUIDBindRes)
		if aiChatOutBind == nil || aiChatOutBind.Errno == nil || aiChatOutBind.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service chat:mgetAichatBotUidBind, err = %v, input = %v, output = %v", err,
				common.ToString(aiChatBindParam.Input), common.ToString(aiChatOutBind))
		}
		if aiChatOutBind.GetData() != nil && aiChatOutBind.GetData().Data != nil && len(aiChatOutBind.GetData().Data) > 0 {
			for _, bind := range aiChatOutBind.GetData().Data {
				if bind != nil && bind.BotUid != nil && bind.RelatedUid != nil {
					if bind.GetRelatedUid() == int64(botUid) {
						chatUID = uint64(bind.GetBotUid())
						break
					}
				}
			}
		}
	}
	chatUk, _ := uidxuk.UID2UK(chatUID)
	botDetail.ChatUk = proto.String(chatUk)

	//补充12.74的字段
	simplePlots := make([]*p.SimplePlot, 0)
	needQueryUKs := []string{chatUk}
	// 根据chat_uk获取基础信息 getAiInteractiveGamePlotByChatUk
	getBotGameInfoByUkInput := &chat.GetAiInteractiveGamePlotByChatUKReq{
		Uid:    proto.Int64(userID),
		ChatUk: needQueryUKs,
	}
	getBotGameInfoByUkOutput := &chat.GetAiInteractiveGamePlotByChatUKRes{}
	err = tbservice.Call(ctx, "chat", "getAiInteractiveGamePlotByChatUk",
		getBotGameInfoByUkInput,
		&getBotGameInfoByUkOutput,
		tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getBotGameInfoByUkOutput.Errno == nil ||
		getBotGameInfoByUkOutput.GetErrno() != tiebaerror.ERR_SUCCESS ||
		getBotGameInfoByUkOutput.GetData() == nil {
		tbcontext.WarningF(ctx, "call chat:getAiInteractiveGamePlotByChatUk fail, err:[%v], output:[%s]", err, common.ToString(getBotGameInfoByUkOutput))
	} else {
		if len(getBotGameInfoByUkOutput.GetData().GetAiInteractiveGamePlot()) > 0 {
			for _, oneUK := range needQueryUKs {
				if _, ok := getBotGameInfoByUkOutput.GetData().GetAiInteractiveGamePlot()[oneUK]; !ok {
					continue
				}
				for _, plot := range getBotGameInfoByUkOutput.GetData().GetAiInteractiveGamePlot()[oneUK].GetPlot() {
					simplePlots = append(simplePlots, &p.SimplePlot{
						GameUk: proto.String(plot.GetBotUk()),
						PlotId: proto.Int64(plot.GetId()),
					})
				}
			}
		}
	}
	botDetail.Plot = simplePlots

	response.Data.BotInfo = botDetail
	// 获取用户游戏记录 mgetUserPlotRecord
	mGetUserPlotReq := &chat.MgetUserPlotRecordReq{
		UserId:  proto.Int64(userID),
		PlotIds: []int64{gamePlotId},
	}
	mGetUserPlotRes := &chat.MgetUserPlotRecordRes{}
	err = tbservice.Call(ctx, "chat", "mgetUserPlotRecord", mGetUserPlotReq, mGetUserPlotRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || mGetUserPlotRes.Errno == nil || mGetUserPlotRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat::mgetUserPlotRecord failed, err = %v, input = %v, output = %v", err, mGetUserPlotReq, mGetUserPlotRes)
	}
	gameRecord := &chat.AiUserPlotGameRecord{}
	var historyMsgStartTime int64 // 游戏开始时间 即获取历史消息的开始时间
	if gameRecord, ok = mGetUserPlotRes.GetData().GetRecord()[gamePlotId]; ok {
		//历史是否通关查询
		isPassHistory := 0
		getBotGameIsPlayInput := &chat.MgetUserPlotPassReq{
			UserId:  proto.Int64(userID),
			PlotIds: []int64{gamePlotId},
		}
		getBotGameIsPlayOutput := &chat.MgetUserPlotPassRes{}
		err = tbservice.Call(ctx, "chat", "mgetUserPlotPass", getBotGameIsPlayInput, &getBotGameIsPlayOutput, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || getBotGameIsPlayOutput.Errno == nil || getBotGameIsPlayOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call chat:mgetUserPlotPass fail, err:[%v], output:[%s]", err, common.ToString(getBotGameIsPlayOutput))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		if isPassHistoryValue, ok := getBotGameIsPlayOutput.Data.GetRecord()[gamePlotId]; ok {
			isPassHistory = int(isPassHistoryValue)
		}
		botDetail.GamePlot.HasRecord = proto.Int32(1)
		botDetail.GamePlot.GameProcess = &p.GameProcess{
			SessionRounds: gameRecord.SessionRounds,
			Score:         gameRecord.Score,
			IsPass:        gameRecord.IsPass,
			IsEnd:         gameRecord.IsEnd,
			GameRounds:    gameRecord.GameRounds,
			SurpassRatio:  gameRecord.SurpassRatio,
			IsPassHistory: proto.Int32(int32(isPassHistory)),
		}
		historyMsgStartTime = gameRecord.GetCreateTime()
	}
	response.Data.BotInfo = botDetail

	// 主题色
	response.Data.ThemeColor = &p.ThemeColor{
		Underline:     proto.String(botInfo.GetThemeColor().GetUnderline()),
		SugBackground: proto.String(botInfo.GetThemeColor().GetSugBackground()),
		Icon:          proto.String(botInfo.GetThemeColor().GetIcon()),
		Bubble:        proto.String(botInfo.GetThemeColor().GetBubble()),
	}

	// 会话记录
	conversationOutput, err := multi.GetResult(ctx, "mgetUserChatAibotByCond")
	if err != nil {
		tbcontext.WarningF(ctx, "call chat::mgetUserChatAibotByCond fail: %v", err)
	}
	var conversationRes *chat.MgetUserChatAibotByCondRes
	if conversationRes, ok = conversationOutput.(*chat.MgetUserChatAibotByCondRes); !ok {
		tbcontext.WarningF(ctx, "call chat::mgetUserChatAibotByCond fail: %v", err)
	}
	response.Data.HasHistoryMsg = proto.Uint32(0)
	if len(conversationRes.GetData().GetInfo()) > 0 {
		conversationInfo := conversationRes.GetData().GetInfo()[0]
		response.Data.ConversationId = conversationInfo.SessionId
		// 如果是游戏 则获取真正的历史记录 因为重新开始游戏 会有会话记录 但是没历史消息
		if botInfo.GetAiGamePlot() != nil && botInfo.GetPaType() == 1 {
			// 获取历史消息记录
			agentHistoryReq := &aigcprocesser.AgentHistoryReq{
				AgentId:        proto.String(botInfo.GetAgentId()),
				UserId:         proto.Uint64(uint64(userID)),
				ConversationId: proto.String(conversationInfo.GetSessionId()),
				Size:           proto.Int64(5),
				PageToken:      proto.String("-1"),
			}
			if historyMsgStartTime != 0 {
				agentHistoryReq.HistoryMsgStartTime = proto.Int64(historyMsgStartTime)
			}
			agentHistoryRes := &aigcprocesser.AgentHistoryRes{}
			err := tbservice.Call(ctx, "aigc_processer", "agentHistory", agentHistoryReq, agentHistoryRes, tbservice.WithConverter(tbservice.JSONITER))
			if err != nil || agentHistoryRes.Errno == nil || agentHistoryRes.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "call aigc_processer::agentHistory fail, err:[%v], output:[%s]", err, common.ToString(agentHistoryRes))
			}
			if len(agentHistoryRes.GetData().GetHistoryMessage()) > 0 {
				response.Data.HasHistoryMsg = proto.Uint32(1)
			}
		} else { // 非游戏 存在会话记录 则必然有历史消息
			response.Data.HasHistoryMsg = proto.Uint32(1)
		}
	}
	if response.GetData().GetConversationId() == "" {
		response.Data.ConversationId = proto.String(fmt.Sprintf("%d_%d_%d", userID, botUid, time.Now().Unix()))
	}

	// 用户头像
	mGetUserDataExOutput, err := multi.GetResult(ctx, "mgetUserDataEx")
	if err != nil {
		tbcontext.WarningF(ctx, "call user::mgetUserDataEx fail: %v", err)
	}
	var mGetUserDataExRes *userProto.MgetUserDataExRes
	if mGetUserDataExRes, ok = mGetUserDataExOutput.(*userProto.MgetUserDataExRes); !ok {
		tbcontext.WarningF(ctx, "call user::mgetUserDataEx fail: %v", err)
	}
	response.Data.UserInfo = &p.UserInfo{
		Avatar: proto.String(""),
	}
	userInfo := &userProto.UserInfoEx{}
	if userInfo, ok = mGetUserDataExRes.GetUserInfo()[uint64(userID)]; ok {
		response.Data.UserInfo.Avatar = proto.String(tbportrait.Encode(int64(userInfo.GetUserId()), userInfo.GetUserName(), time.Now().Unix()))
	}
	return tiebaerror.ERR_SUCCESS
}
