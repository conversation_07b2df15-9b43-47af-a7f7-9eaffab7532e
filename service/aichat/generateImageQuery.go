package aichat

import (
	"context"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/aigcprocesser"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/generateImageQuery"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// DoGenerateImageQuery 查询生图结果
func DoGenerateImageQuery(ctx context.Context, baseData *types.GenerateImageQueryBaseData, response *generateImageQuery.GenerateImageQueryResIdl) int {
	baseData.TaskID = common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("task_id", ""), common.TTT_STRING).(string)
	// 获取生图任务结果
	getGenerateImageTaskInput := &aigcprocesser.GetGenerateImageTaskReq{
		QueryId: proto.String(baseData.TaskID),
	}
	getGenerateImageTaskOutput := &aigcprocesser.GetGenerateImageTaskRes{}
	err := tbservice.Call(ctx, "aigc_processer", "getGenerateImageTask", getGenerateImageTaskInput,
		getGenerateImageTaskOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getGenerateImageTaskOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "DoGenerateImageQuery call aigc_processer::getGenerateImageTask failed,input:[%s], output:[%s], err=[%v]",
			common.ToString(getGenerateImageTaskInput), common.ToString(getGenerateImageTaskOutput), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	if getGenerateImageTaskOutput.GetData() == nil || getGenerateImageTaskOutput.GetData().GetTaskInfo() == nil {
		tbcontext.WarningF(ctx, "DoGenerateImageQuery call aigc_processer::getGenerateImageTask res is Nil,input:[%s], output:[%s]",
			common.ToString(getGenerateImageTaskInput), common.ToString(getGenerateImageTaskOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	generateImageTaskInfo := getGenerateImageTaskOutput.GetData().GetTaskInfo()

	// 判断生图超时 超时需要设置为生图失败
	if time.Now().Unix()-int64(generateImageTaskInfo.GetCreateTime()) > generateImageTaskInfo.GetTimeout() &&
		generateImageTaskInfo.GetStatus() != aigcprocesser.GenerateImageTaskStatus_value[aigcprocesser.GenerateImageTaskStatus_DONE.String()] {
		response.Data.Status = proto.Int32(aigcprocesser.GenerateImageTaskStatus_value[aigcprocesser.GenerateImageTaskStatus_FAILED.String()])
		// 更新生图任务状态
		generateImageTaskInfo.Status = response.Data.Status
		setGenerateImageTaskInput := &aigcprocesser.SetGenerateImageTaskReq{
			QueryId:  proto.String(baseData.TaskID),
			TaskInfo: generateImageTaskInfo,
		}
		setGenerateImageTaskOutput := &aigcprocesser.GetGenerateImageTaskRes{}
		err = tbservice.Call(ctx, "aigc_processer", "setGenerateImageTask", setGenerateImageTaskInput,
			setGenerateImageTaskOutput, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || setGenerateImageTaskOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.FatalF(ctx, "DoGenerateImageQuery setGenerateImageTask error, queryID:[%s] taskInfo:[%v], err=%v",
				baseData.TaskID, common.ToString(generateImageTaskInfo), err)
		}
		return tiebaerror.ERR_SUCCESS
	}
	// 设置任务状态
	response.Data.Status = proto.Int32(generateImageTaskInfo.GetStatus())
	// 如果生图成功 返回对应图
	if generateImageTaskInfo.GetStatus() == aigcprocesser.GenerateImageTaskStatus_value[aigcprocesser.GenerateImageTaskStatus_DONE.String()] {
		for _, img := range generateImageTaskInfo.GetGenerateImageInfo() {
			response.Data.ImgList = append(response.Data.ImgList, &generateImageQuery.GenerateImageInfo{
				ImgUrl: proto.String(img.GetUrl()),
				Width:  proto.Int64(img.GetWidth()),
				Height: proto.Int64(img.GetHeight()),
				PicId:  proto.Uint64(img.GetPicId()),
			})
		}
	}
	return tiebaerror.ERR_SUCCESS
}
