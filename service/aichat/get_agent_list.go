package aichat

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/proto"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/bawu"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getAgentList"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// DoGetAgentList 吧二楼 获取游戏剧情信息和游戏进度信息
func DoGetAgentList(ctx context.Context, baseData *types.GetAgentListBaseData, response *getAgentList.GetAgentListIdl) int {

	fid, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("fid", 0), common.TTT_INT64).(int64)
	source, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("source", 0), common.TTT_INT64).(int64)
	userID, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	pn, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("pn", 1), common.TTT_INT32).(int32)
	rn, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("rn", 10), common.TTT_INT32).(int32)
	sampleId := baseData.Request.GetCommon().GetSampleId()
	pureMode := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("pure_mode", 0), common.TTT_INT).(int)

	// 校验入参是否合法
	if fid <= 0 || userID <= 0 {
		tbcontext.WarningF(ctx, "getAgentList check fid and userID error")
		return tiebaerror.ERR_PARAM_ERROR
	}

	getSecondFloorShowBotByFidInput := &chat.GetSecondFloorShowBotByFidReq{
		Fid:     proto.Int64(fid),
		Uid:     proto.Int64(int64(userID)),
		Source:  proto.Int32(int32(source)),
		SidInfo: proto.String(sampleId),
		Pn:      proto.Int32(pn),
		Rn:      proto.Int32(rn),
	}

	//MgetAiBotUserInfoByUidInput = chat.MgetAiBotUserInfoByUidReq
	getSecondFloorShowBotByFidOutput := &chat.GetSecondFloorShowBotByFidRes{}
	err := tbservice.Call(ctx, "chat", "getSecondFloorShowBotByFid",
		getSecondFloorShowBotByFidInput, &getSecondFloorShowBotByFidOutput,
		tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getSecondFloorShowBotByFidOutput.Errno == nil || getSecondFloorShowBotByFidOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:getSecondFloorShowBotByFid fail, err:[%v], output:[%s]", err, common.ToString(getSecondFloorShowBotByFidOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	response.Data.CurTab = proto.Int64(int64(getSecondFloorShowBotByFidOutput.GetData().GetCurTab()))
	getFrsBottomBGSmartColorInput := &bawu.GetFrsBottomBGSmartColorReq{
		ForumId: proto.Int64(fid),
	}
	getFrsBottomBGSmartColorOutput := &bawu.GetFrsBottomBGSmartColorRes{}
	err = tbservice.Call(ctx, "bawu", "getFrsBottomBGSmartColor",
		getFrsBottomBGSmartColorInput, &getFrsBottomBGSmartColorOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getFrsBottomBGSmartColorOutput.Errno == nil || getFrsBottomBGSmartColorOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call bawu:getFrsBottomBGSmartColorOutput fail, err:[%v], output:[%s]", err, common.ToString(getFrsBottomBGSmartColorOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	response.Data.ThemeColor = &getAgentList.ThemeColor{}
	response.Data.Page = &getAgentList.Page{}

	if getFrsBottomBGSmartColorOutput.GetData().GetBallBgColor() != "" {
		response.Data.ThemeColor.BackgroundColor = proto.String(getFrsBottomBGSmartColorOutput.GetData().GetThemeColor())
		response.Data.ThemeColor.FontColor = proto.String(getFrsBottomBGSmartColorOutput.GetData().GetAgentTagColor())
		response.Data.ThemeColor.BallBgColor = proto.String(getFrsBottomBGSmartColorOutput.GetData().GetBallBgColor())
		response.Data.ThemeColor.CreateBotButtonLeft = proto.String(getFrsBottomBGSmartColorOutput.GetData().GetCreateBotButtonLeft())
		response.Data.ThemeColor.CreateBotButtonRight = proto.String(getFrsBottomBGSmartColorOutput.GetData().GetCreateBotButtonRight())
	}

	// 获取吧数据
	getBtxInfoReq := &forum.GetBtxInfoReq{
		ForumId: proto.Uint32(uint32(fid)),
	}
	getBtxInfoRes := &forum.GetBtxInfoRes{}
	err = tbservice.Call(ctx, "forum", "getBtxInfo", getBtxInfoReq, getBtxInfoRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getBtxInfoRes.Errno == nil || getBtxInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call forum::getBtxInfo failed, err = %v, input = %v, output = %v", err, getBtxInfoReq, getBtxInfoRes)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	response.Data.Forum = &getAgentList.Forum{
		Avatar:    proto.String(getBtxInfoRes.GetCard().GetAvatar()),
		ForumName: proto.String(fmt.Sprintf("%s吧", getBtxInfoRes.GetForumName().GetForumName())),
	}

	// 拼接数据
	if getSecondFloorShowBotByFidOutput.GetData().GetTabs() != nil {
		response.Data.TabList = getSecondFloorShowBotByFidOutput.GetData().GetTabs()
	}

	if getSecondFloorShowBotByFidOutput.GetData().GetCurPage() != 0 {
		response.Data.Page.CurPage = proto.Int32(getSecondFloorShowBotByFidOutput.GetData().GetCurPage())
	}

	if getSecondFloorShowBotByFidOutput.GetData().GetTotalPage() != 0 {
		response.Data.Page.TotalPage = proto.Int32(getSecondFloorShowBotByFidOutput.GetData().GetTotalPage())
	}
	response.Data.CanCreateBot = proto.Int64(0)
	// 只在未命中ios审核态时查白名单
	if pureMode == 0 {
		//获取一下白名单
		isHitAgentBlackWhiteListInput := &chat.IsHitAgentBlackWhiteListReq{
			Fid:                proto.Int64(fid),
			Uid:                proto.Int64(int64(userID)),
			NeedCheckUserAgent: proto.Int32(0),
		}
		isHitAgentBlackWhiteListOutput := &chat.IsHitAgentBlackWhiteListRes{}
		err = tbservice.Call(ctx, "chat", "isHitAgentBlackWhiteList",
			isHitAgentBlackWhiteListInput, &isHitAgentBlackWhiteListOutput,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || isHitAgentBlackWhiteListOutput.Errno == nil || isHitAgentBlackWhiteListOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call chat:isHitAgentBlackWhiteList fail, err:[%v], output:[%s]", err, common.ToString(isHitAgentBlackWhiteListOutput))
		}
		response.Data.CanCreateBot = proto.Int64(int64(isHitAgentBlackWhiteListOutput.GetData().GetIsHitWhiteList()))
	}

	botUids := getSecondFloorShowBotByFidOutput.GetData().GetBotUid()
	if len(botUids) == 0 {
		return tiebaerror.ERR_SUCCESS
	}
	mgetAiBotUserInfoByUidInput := &chat.MgetAiBotUserInfoByUidReq{
		BotUids:                botUids,
		Fid:                    proto.Int64(fid),
		NeedFilterOnlineStatus: proto.Int32(1),
	}
	mgetAiBotUserInfoByUidOutput := &chat.MgetAiBotUserInfoByUidRes{}

	err = tbservice.Call(ctx, "chat", "mgetAiBotUserInfoByUid",
		mgetAiBotUserInfoByUidInput, &mgetAiBotUserInfoByUidOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || mgetAiBotUserInfoByUidOutput.Errno == nil || mgetAiBotUserInfoByUidOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:mgetAiBotUserInfoByUid fail, err:[%v], output:[%s]", err, common.ToString(mgetAiBotUserInfoByUidOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	for _, botUid := range botUids {
		if botList, ok := mgetAiBotUserInfoByUidOutput.GetData().GetBotList()[botUid]; ok {
			botInfo := &getAgentList.BotInfo{
				Name:             proto.String(botList.GetName()),
				Portrait:         proto.String(botList.GetPortrait()),
				BackgroundUrl:    proto.String(botList.GetBackgroundUrl()),
				Prologue:         proto.String(botList.GetPrologue()),
				Uk:               proto.String(botList.GetChatUk()),
				Pa:               proto.Int64(botList.GetPa()),
				CreateUser:       proto.String(botList.GetCreateUser()),
				CreateUserLevel:  proto.Int32(int32(botList.GetCreateUserForumLevel())),
				CreateUserAvatar: proto.String(botList.GetCreateUserAvatar()),
				DialogueUserNum:  proto.Int32(botList.GetDialogueUserNum()),
				RoleType:         proto.String(botList.GetRoleType()),
				ThemeColor: &client.ChatThemeColor{
					ThemeColor:    proto.String(botList.GetThemeColor().GetThemeColor()),
					BubbleBgColor: proto.String(botList.GetThemeColor().GetBubbleBgColor()),
					SugTextColor:  proto.String(botList.GetThemeColor().GetSugTextColor()),
					TagColor:      proto.String(botList.GetThemeColor().GetTagColor()),
				},
			}

			AiGamePlot := &getAgentList.AiGamePlot{}
			if botList.GetAiGamePlot() != nil {
				AiGamePlot = &getAgentList.AiGamePlot{
					PlotTitle:        proto.String(botList.GetAiGamePlot().GetPlotTitle()),
					PlotDescription:  proto.String(botList.GetAiGamePlot().GetPlotDescription()),
					Rate:             proto.Float64(float64(botList.GetAiGamePlot().GetRate())),
					PortraitUrl:      proto.String(botList.GetAiGamePlot().GetPortraitUrl()),
					PlotId:           proto.Int32(int32(botList.GetAiGamePlot().GetPlotId())),
					BotUk:            proto.String(botList.GetChatUk()),
					BotPa:            proto.Int64(botList.GetPa()),
					BotBackgroundUrl: proto.String(botList.GetBackgroundUrl()),
					CreateUser:       proto.String(botList.GetCreateUser()),
					CreateUserLevel:  proto.Int32(int32(botList.GetCreateUserForumLevel())),
					CreateUserAvatar: proto.String(botList.GetCreateUserAvatar()),
					DialogueUserNum:  proto.Int32(botList.GetDialogueUserNum()),
					RoleType:         proto.String(botList.GetRoleType()),
				}
			}

			// 兜底处理，如果game plot为空，bot type 是1，代表badcase需要过滤
			if botList.GetBotType() == 1 && AiGamePlot.GetBotUk() == "" {
				continue
			}

			response.Data.RotList = append(response.Data.RotList, &getAgentList.RotList{
				BotType:      proto.Int32(botList.GetBotType()),
				RobotVersion: proto.Int32(botList.GetBotVersion()),
				AiGamePlot:   AiGamePlot,
				BotInfo:      botInfo,
			})
		}
	}

	return tiebaerror.ERR_SUCCESS
}
