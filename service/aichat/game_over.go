package aichat

import (
	"context"
	"fmt"
	"strconv"
	"time"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/gameOver"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	GaoKaoConfTableName  = "tb_wordlist_redis_activity_scene_config_13"                      // 高考配置词表
	GaoKaoBotUkKey       = "campus_game_plot_bot_uk"                                         // 校园认证官AI剧情挑战的uk列表
	GaoKaoBotUkTableName = "tb_wordlist_redis_activity_2024_gaokao_ai_certification_officer" // 校园认证官AI剧情挑战的uk->fid列表
	GameOverConf         = "game_over_conf"                                                  // 游戏结束默认配置文案

	completeRedisKey = "ai_plot_complete_uid_plotid"

	fromGaokao = "gaokao"

	productIDGaokao = 13

	prizeTypeIcon         = "icon"  // 印记
	prizeTypeVirtualGoods = "goods" // 虚拟物品

	gaokaoIconName = "gaokao_2025"

	// 奖励类型，tmoney-贴贝，pendant-头像挂件，background-个性背景，themecard-个性卡片，postbubble-发贴气泡，icon-印记
	awardTypeTMoney     = "tmoney"
	awardTypePendant    = "pendant"
	awardTypeBackground = "background"
	awardTypeThemeCard  = "themecard"
	awardTypePostBubble = "postbubble"
	awardTypeIcon       = "icon"

	goodsCategoryBackground = 3  // 虚拟商品类型 - 个性背景
	goodsCategoryPendant    = 4  // 虚拟商品类型 - 头像框
	goodsCategoryThemeCard  = 14 // 虚拟商品类型 - 个性卡片
	goodsCategoryTmoney     = 18 // 虚拟商品类型 - 贴贝
	goodsCategoryPostBubble = 13 // 虚拟商品类型 - 发帖气泡

	SourceFromClient   = 1
	SourceFromSmallApp = 2
)

// DogameOver 重新开始ai游戏
func DoGameOver(ctx context.Context, baseData *types.GameOverBaseData, response *gameOver.GameOverResIdl) int {
	baseData.UserID, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	baseData.PlotID, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("plot_id", 0), common.TTT_INT64).(int64)
	baseData.SourceFrom, _ = common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("source_from", 0), common.TTT_INT32).(int32)
	// 校验入参role_type是否合法
	if baseData.UserID <= 0 || baseData.PlotID <= 0 {
		tbcontext.WarningF(ctx, "getGameProcess check user_id and plot_id error, plot_id=[%v]", baseData.PlotID)
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 根据剧情ID获取基础信息 getAiInteractiveGamePlotByIDs
	getBotGameInfoInput := &chat.GetAiInteractiveGamePlotByIDsReq{
		Uid:     proto.Int64(int64(baseData.UserID)),
		PlotIds: []int64{baseData.PlotID},
	}
	getBotGameInfoOutput := &chat.GetAiInteractiveGamePlotByIDsRes{}
	err := tbservice.Call(ctx, "chat", "getAiInteractiveGamePlotByIDs", getBotGameInfoInput, &getBotGameInfoOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getBotGameInfoOutput.Errno == nil || getBotGameInfoOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:getAiInteractiveGamePlotByIDs fail, err:[%v], output:[%s]", err, common.ToString(getBotGameInfoOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 错误的剧情id是查不到数据
	if len(getBotGameInfoOutput.Data.GetAiInteractiveGamePlot()) == 0 {
		tbcontext.WarningF(ctx, "call chat:getAiInteractiveGamePlotByIDs fail, err:plot_id is not exist, plot_id:[%v]", baseData.PlotID)
		return tiebaerror.ERR_PARAM_ERROR
	}
	response.Data.PassScore = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].PassScore
	response.Data.RoundLimit = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].RoundLimit
	response.Data.SuccessInfo = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].SuccessInfo
	response.Data.RoundLimitExceeded = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].RoundLimitExceeded
	response.Data.ProcessNegative = getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].ProcessNegative
	awardInfo := getBotGameInfoOutput.Data.AiInteractiveGamePlot[0].AwardInfo

	//如果这三个字段有一个为空，则去配置表里取
	if len(*response.Data.SuccessInfo) <= 0 || len(*response.Data.RoundLimitExceeded) <= 0 || len(*response.Data.ProcessNegative) <= 0 {
		redisKeys := []string{GameOverConf}
		tableName := aiChatConfWordList
		redisRes, err := wordserver.QueryKeys(ctx, tableName, redisKeys)
		if err != nil {
			tbcontext.WarningF(ctx, "wordserver query keys fail, input=%s, err=%v", common.ToString(redisKeys), err)
		} else {
			confList := make(map[string]string)
			err = jsoniter.UnmarshalFromString(redisRes[0], &confList)
			if err != nil {
				tbcontext.WarningF(ctx, "json unmarshal fail, input=%s, err=%v", common.ToString(redisRes), err)
			} else {
				if len(*response.Data.SuccessInfo) <= 0 {
					if SuccessInfo, ok := confList["success_info"]; ok {
						response.Data.SuccessInfo = proto.String(SuccessInfo)
					}
				}
				if len(*response.Data.RoundLimitExceeded) <= 0 {
					if RoundLimitExceeded, ok := confList["round_limit_exceeded"]; ok {
						response.Data.RoundLimitExceeded = proto.String(RoundLimitExceeded)
					}
				}
				if len(*response.Data.ProcessNegative) <= 0 {
					if ProcessNegative, ok := confList["process_negative"]; ok {
						response.Data.ProcessNegative = proto.String(ProcessNegative)
					}
				}
			}
		}
	}

	// 用户剧情最新记录数据查询 chat::mgetUserPlotRecord
	getBotPlotRecordInput := &chat.MgetUserPlotRecordReq{
		UserId:  proto.Int64(int64(baseData.UserID)),
		PlotIds: []int64{baseData.PlotID},
	}
	getBotPlotRecordOutput := &chat.MgetUserPlotRecordRes{}
	err = tbservice.Call(ctx, "chat", "mgetUserPlotRecord", getBotPlotRecordInput, &getBotPlotRecordOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getBotPlotRecordOutput.Errno == nil || getBotPlotRecordOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:mgetUserPlotRecord fail, err:[%v], output:[%s]", err, common.ToString(getBotPlotRecordOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	if _, ok := getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID]; ok {
		// 有进度数据，则说明用户玩过这个剧情
		response.Data.IsPlaygame = proto.Int32(1)
		// 游戏进度信息需要塞一下
		response.Data.GameRounds = getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID].GameRounds
		response.Data.Score = getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID].Score
		response.Data.SessionRounds = getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID].SessionRounds
		response.Data.IsPass = getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID].IsPass
		response.Data.IsEnd = getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID].IsEnd
		response.Data.SurpassRatio = getBotPlotRecordOutput.Data.GetRecord()[baseData.PlotID].SurpassRatio
	} else {
		// 没有数据，则说明用户没有玩过这个剧情
		response.Data.IsPlaygame = proto.Int32(0)
	}

	// 判定当前是否为首次通过
	// 本次通关
	if getBotPlotRecordOutput.GetData().GetRecord()[baseData.PlotID].GetIsPass() == int32(1) {
		redisKey := fmt.Sprintf("%s_%d_%d", completeRedisKey, baseData.UserID, baseData.PlotID)
		success, err := resource.RedisSign.SetNX(ctx, redisKey, "1", 365*24*60*time.Minute).Result()
		if err != nil {
			tbcontext.WarningF(ctx, "resource::cacheSign::set fail, err=%v, key=%v", err, redisKey)
			return tiebaerror.ERR_SUCCESS
		}

		// 小程序通关 不需要下发奖励 直接返回
		if baseData.SourceFrom == SourceFromSmallApp {
			tbcontext.NoticeF(ctx, "game over source from small app, uid:%v, plotId:%v", baseData.UserID, baseData.PlotID)
			return tiebaerror.ERR_SUCCESS
		}

		//如果已经通过一次，则直接返回
		if !success {
			return tiebaerror.ERR_SUCCESS
		}

		botUk := getBotGameInfoOutput.GetData().GetAiInteractiveGamePlot()[0].GetBotUk()
		stuAuthRes := stuAuth(ctx, int64(baseData.UserID), botUk)
		if !stuAuthRes {
			tbcontext.WarningF(ctx, "no need stuAuthRes ")
		}

		//高考活动使用：只有首次弹窗返回相关信息
		//check是否有挂载相关奖品配置 4 5
		if getBotGameInfoOutput.GetData().GetAiInteractiveGamePlot()[0].GetAwardInfo() != nil {
			// 直接发奖
			awardType := awardInfo.GetType()
			getRaffleReq := &commonProto.GetRaffleReq{
				UserId:    proto.Uint64(baseData.UserID),
				From:      proto.String(fromGaokao),
				ProductId: proto.Int32(productIDGaokao),
				PrizeInfo: &commonProto.DirectPrizeInfo{
					PrizeType:    proto.String(prizeTypeVirtualGoods),
					SendLimitNum: proto.Uint64(1),
				},
				DirectSend: proto.Uint64(1),
			}
			if awardType == awardTypeTMoney {
				getRaffleReq.PrizeInfo.CategoryType = proto.Uint64(goodsCategoryTmoney)
				getRaffleReq.PrizeInfo.PrizeNum = proto.Uint64(uint64(awardInfo.GetTmoneyNum()))
			} else if awardType == awardTypePendant {
				getRaffleReq.PrizeInfo.CategoryType = proto.Uint64(goodsCategoryPendant)
				getRaffleReq.PrizeInfo.PrizeId = proto.Uint64(uint64(awardInfo.GetPendantId()))
			} else if awardType == awardTypeBackground {
				getRaffleReq.PrizeInfo.CategoryType = proto.Uint64(goodsCategoryBackground)
				getRaffleReq.PrizeInfo.PrizeId = proto.Uint64(uint64(awardInfo.GetBackgroundId()))
			} else if awardType == awardTypeThemeCard {
				getRaffleReq.PrizeInfo.CategoryType = proto.Uint64(goodsCategoryThemeCard)
				getRaffleReq.PrizeInfo.PrizeId = proto.Uint64(uint64(awardInfo.GetThemecardId()))
			} else if awardType == awardTypePostBubble {
				getRaffleReq.PrizeInfo.CategoryType = proto.Uint64(goodsCategoryPostBubble)
				getRaffleReq.PrizeInfo.PrizeId = proto.Uint64(uint64(awardInfo.GetPostbubbleId()))
			} else if awardType == awardTypeIcon {
				getRaffleReq.PrizeInfo.PrizeName = proto.String(awardInfo.GetIconName())
				getRaffleReq.PrizeInfo.PrizeType = proto.String(prizeTypeIcon)
			}
			getRaffleRes := &commonProto.GetRaffleRes{}

			mgetStuAuthByUIDsOption := []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
				tbservice.WithRalName("common_go"),
			}

			err = tbservice.Call(ctx, "common", "getRaffle", getRaffleReq, &getRaffleRes, mgetStuAuthByUIDsOption...)
			if err != nil || getRaffleRes == nil || getRaffleRes.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "call common_go:getRaffle fail, err:[%v], output:[%s] input:[%s]",
					err, common.ToString(getRaffleRes), common.ToString(getRaffleReq))
				return tiebaerror.ERR_SUCCESS
			}

			response.Data.AwardInfo = &gameOver.AwardInfo{}
			response.Data.AwardInfo.Img = proto.String(getBotGameInfoOutput.GetData().GetAiInteractiveGamePlot()[0].GetAwardInfo().GetImg())
			response.Data.AwardInfo.BtnLink = proto.String(getBotGameInfoOutput.GetData().GetAiInteractiveGamePlot()[0].GetAwardInfo().GetBtnLink())
			response.Data.AwardInfo.BtnText = proto.String(getBotGameInfoOutput.GetData().GetAiInteractiveGamePlot()[0].GetAwardInfo().GetBtnText())
			response.Data.AwardInfo.SubTitle = proto.String(getBotGameInfoOutput.GetData().GetAiInteractiveGamePlot()[0].GetAwardInfo().GetSubTitle())
			response.Data.AwardInfo.Title = proto.String(getBotGameInfoOutput.GetData().GetAiInteractiveGamePlot()[0].GetAwardInfo().GetTitle())
			response.Data.AwardInfo.Type = proto.String(getBotGameInfoOutput.GetData().GetAiInteractiveGamePlot()[0].GetAwardInfo().GetType())
		}

	}

	// 拿到的数据 拼接回response
	return tiebaerror.ERR_SUCCESS
}

// 认证方法
func stuAuth(ctx context.Context, uid int64, botUk string) bool {
	// 进行高考认证流程：
	/*
	**  1 查询该剧情是否为高考认证官 2、查询此吧是否有对应高校的映射 3、查询高考认证状态， 失败return 4 如果非已完成认证，则进行认证流程+发印记 失败return 5 进行发奖逻辑，失败return 6 返回新的窗口数据
	 */

	// 1 查询该剧情是否为高考认证官
	redisKeys := []string{GaoKaoBotUkKey}
	tableName := GaoKaoConfTableName
	redisRes, err := wordserver.QueryKeys(ctx, tableName, redisKeys)
	if err != nil {
		tbcontext.WarningF(ctx, "wordserver query keys fail, input=%s, err=%v", common.ToString(redisKeys), err)
		return false
	}
	isAuthOfficial := false
	ukList := []string{}
	err = jsoniter.UnmarshalFromString(redisRes[0], &ukList)
	if err != nil {
		tbcontext.WarningF(ctx, "json unmarshal fail, input=%s, err=%v", common.ToString(redisKeys), err)
		return false
	}

	for _, item := range ukList {
		if item == botUk {
			// 是认证官，进行认证流程
			isAuthOfficial = true
			break
		}
	}

	if !isAuthOfficial {
		// 不是认证官，直接返回
		return false
	}

	// 2 查该认证官是否有对应吧的映射
	redisKeys = []string{botUk}
	tableName = GaoKaoBotUkTableName
	redisRes, err = wordserver.QueryKeys(ctx, tableName, redisKeys)
	if err != nil {
		tbcontext.WarningF(ctx, "wordserver query keys fail, input=%s, err=%v", common.ToString(redisKeys), err)
		return false
	}
	bindFid := redisRes[0]
	if bindFid == "" {
		// 无对应高校的映射
		tbcontext.WarningF(ctx, "no uk =》 fid map, input=%s", common.ToString(redisKeys))
		return false
	}

	fid, err := strconv.Atoi(bindFid)
	if err != nil {
		tbcontext.WarningF(ctx, "strconv fid fail, input=%s", common.ToString(redisKeys))
		return false
	}

	// 3 查该吧是否有对应高校的映射
	// common:mgetForumCollegeByFIDs
	mgetForumCollegeByFIDsReq := &commonProto.MgetForumCollegeByFIDsReq{
		ForumId: []int64{int64(fid)},
	}

	mgetForumCollegeByFIDsOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("common_go"),
	}
	mgetForumCollegeByFIDsRes := &commonProto.MgetForumCollegeByFIDsRes{}
	err = tbservice.Call(ctx, "common", "mgetForumCollegeByFIDs", mgetForumCollegeByFIDsReq, &mgetForumCollegeByFIDsRes, mgetForumCollegeByFIDsOption...)

	// 异常了就不往下走了
	if err != nil || mgetForumCollegeByFIDsRes.Errno == nil || mgetForumCollegeByFIDsRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call common_go:mgetForumCollegeByFIDs fail, err:[%v], output:[%s]", err, common.ToString(mgetForumCollegeByFIDsRes))
		return false
	}

	college := mgetForumCollegeByFIDsRes.GetData()[int64(fid)]
	if college == "" {
		// 该吧没有对应的高考学院
		tbcontext.WarningF(ctx, "no college , input=%v", fid)
		return false
	}

	// 4 查询高考认证状态
	mgetStuAuthByUIDsReq := &commonProto.MgetStuAuthByUIDsReq{
		UserIds: []int64{int64(uid)},
	}

	mgetStuAuthByUIDsOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("common_go"),
	}

	mgetStuAuthByUIDsRes := &commonProto.MgetStuAuthByUIDsRes{}
	err = tbservice.Call(ctx, "common", "mgetStuAuthByUIDs", mgetStuAuthByUIDsReq, &mgetStuAuthByUIDsRes, mgetStuAuthByUIDsOption...)

	// 异常了就不往下走了
	if err != nil || mgetStuAuthByUIDsRes.Errno == nil || mgetStuAuthByUIDsRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call common_go:mgetStuAuthByUIDsRes fail, err:[%v], output:[%s]", err, common.ToString(mgetStuAuthByUIDsRes))
		return false
	}
	if mgetStuAuthByUIDsRes.GetData().GetAuthInfo()[uid].GetStatus() != 1 {
		// 进入认证流程
		addStuAuthReq := &commonProto.AddStuAuthReq{
			UserId:       proto.Int64(uid),
			ForumId:      proto.Int64(int64(fid)),
			College:      proto.String(college),
			Status:       proto.Uint32(1),
			CreateOrigin: proto.Uint32(1),
			PassOrigin:   proto.Uint32(1),
		}
		addStuAuthRes := &commonProto.AddStuAuthRes{}
		err = tbservice.Call(ctx, "common", "addStuAuth", addStuAuthReq, &addStuAuthRes, mgetStuAuthByUIDsOption...)

		// 异常了就不往下走了
		if err != nil || addStuAuthRes.Errno == nil || addStuAuthRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call common_go:addStuAuth fail, err:[%v], output:[%s]", err, common.ToString(mgetStuAuthByUIDsRes))
			return false
		}

		// 发高考印记
		getRaffleReq := &commonProto.GetRaffleReq{
			UserId:    proto.Uint64(uint64(uid)),
			From:      proto.String(fromGaokao),
			ProductId: proto.Int32(productIDGaokao),
			PrizeInfo: &commonProto.DirectPrizeInfo{
				PrizeType:    proto.String(prizeTypeIcon),
				PrizeName:    proto.String(gaokaoIconName),
				SendLimitNum: proto.Uint64(1),
			},
			DirectSend: proto.Uint64(1),
		}
		getRaffleRes := &commonProto.GetRaffleRes{}
		err = tbservice.Call(ctx, "common", "getRaffle", getRaffleReq, &getRaffleRes, mgetStuAuthByUIDsOption...)
		if err != nil || getRaffleRes == nil || getRaffleRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call common_go:getRaffle fail, err:[%v], output:[%s] input:[%s]",
				err, common.ToString(getRaffleRes), common.ToString(getRaffleReq))
		}
	}
	return true
}
