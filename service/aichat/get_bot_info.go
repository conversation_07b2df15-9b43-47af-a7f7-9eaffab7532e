package aichat

import (
	"context"
	"encoding/json"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/baidu/uidxuk"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getAichatBotInfo"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	aiChatBotStyleInfos = "aichat_botStyle_conf" // 机器人风格信息
)

// DoGetBotInfo 获取机器人信息
func DoGetBotInfo(ctx context.Context, baseData *types.GetAiChatBotInfoBaseData, response *getAichatBotInfo.GetAichatBotInfoResIdl) int {

	// 通过词表获取风格信息
	styleInfos := []*getAichatBotInfo.StyleInfo{}
	confStr, err := wordserver.QueryKey(ctx, aiChatConfWordList, aiChatBotStyleInfos)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to query wordlist %v, key = %v, err = %v", aiChatConfWordList, aiChatBotStyleInfos, err)
	}
	if confStr != "" {
		err = json.Unmarshal([]byte(confStr), &styleInfos)
		if err != nil {
			tbcontext.WarningF(ctx, "json to wordlist %v, key = %v, err = %v", aiChatConfWordList, aiChatBotStyleInfos, err)
		}
	}

	errno := parseParams(ctx, baseData)
	if errno != tiebaerror.ERR_SUCCESS {
		return errno
	}
	if baseData.UserID == 0 {
		response.Data = &getAichatBotInfo.GetAichatBotInfoRes{
			StyleInfo: styleInfos,
			Tbs:       proto.String(tbs.CreateTbs(ctx, baseData.BaseObj.Req).Gene(true)),
		}
		return tiebaerror.ERR_SUCCESS
	}

	multi := tbservice.Multi()
	// rpc调用，获取数据
	// 调用mgetAichatUserInfoByUid 获取机器人基础数据
	aiChatParam := &tbservice.Parameter{
		Service: "chat",
		Method:  "mgetAichatUserInfoByUid",
		Input: map[string]interface{}{
			"uids":      []uint64{baseData.UserID},
			"need_list": false,
		},
		Output: &chat.MgetAichatUserInfoByUidRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetAichatUserInfoByUid", aiChatParam)
	// 获取关联吧id
	aiChatBotForumReq := &tbservice.Parameter{
		Service: "chat",
		Method:  "getBotForumRelationByUid",
		Input: map[string]interface{}{
			"user_id": baseData.UserID,
		},
		Output: &chat.GetBotForumRelationByUidResp{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getBotForumRelationByUid", aiChatBotForumReq)

	multi.Call(ctx)

	// 机器人信息
	aiChatUserInfoRes, err := multi.GetResult(ctx, "mgetAichatUserInfoByUid")
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service chat:mgetAichatUserInfoByUid, err = %v, input = %v,output = %v", err, common.ToString(aiChatParam.Input), common.ToString(aiChatUserInfoRes))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	aiChatUserInfoOut := aiChatUserInfoRes.(*chat.MgetAichatUserInfoByUidRes)
	if aiChatUserInfoOut.GetData() == nil || len(aiChatUserInfoOut.GetData().GetAichatUserinfoList()) == 0 {
		return tiebaerror.ERR_MODATA_EMPTY_DATA
	}
	aiChatUserInfo := aiChatUserInfoOut.GetData().GetAichatUserinfoList()[int64(baseData.UserID)]
	if aiChatUserInfo == nil || aiChatUserInfo.GetUserInfo() == nil {
		return tiebaerror.ERR_MODATA_EMPTY_DATA
	}

	// 吧id
	aiChatBotForumRes, err := multi.GetResult(ctx, "getBotForumRelationByUid")
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service chat:getBotForumRelationByUid, err = %v, input = %v,output = %v", err, common.ToString(aiChatBotForumReq.Input), common.ToString(aiChatBotForumRes))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	aiChatBotForumOut := aiChatBotForumRes.(*chat.GetBotForumRelationByUidResp)
	forumIds := []int64{}
	if aiChatBotForumOut.GetData() != nil && aiChatBotForumOut.GetData().GetForumId() != nil {
		for _, forumId := range aiChatBotForumOut.GetData().GetForumId() {
			forumIds = append(forumIds, forumId)
		}
	}

	// 根据吧id获取吧信息
	forumExtInput := map[string]interface{}{
		"forum_id": forumIds,
	}
	forumRes := &forum.MgetBtxInfoExRes{}
	forumExtOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
	}
	err = tbservice.Call(ctx, "forum", "mgetBtxInfoEx", forumExtInput, forumRes, forumExtOption...)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service forum:mgetBtxInfoEx, err = %v, input = %v,output = %v", err, common.ToString(forumExtInput), common.ToString(forumRes))
	}
	// 处理返回的关联吧信息
	forumInfos := make([]*getAichatBotInfo.ForumInfo, 0, len(forumIds))
	if forumRes.GetOutput() != nil {
		for _, forumInfo := range forumRes.GetOutput() {
			if forumInfo != nil && forumInfo.GetForumName() != nil && forumInfo.GetCard() != nil {
				forumInfos = append(forumInfos, &getAichatBotInfo.ForumInfo{
					ForumId:   proto.Int64(int64(forumInfo.GetForumName().GetForumId())),
					ForumName: proto.String(forumInfo.GetForumName().GetForumName()),
					ForumPic:  proto.String(forumInfo.GetCard().GetAvatar()),
				})
			}
		}
	}

	prologue := ""
	if aiChatUserInfo.GetGreetingInfo() != nil {
		prologue = aiChatUserInfo.GetGreetingInfo().GetPrologue()
	}
	backgroundImg := ""
	if aiChatUserInfo.GetConfig() != nil {
		backgroundImg = aiChatUserInfo.GetConfig().GetPersonBackground()
	}
	response.Data = &getAichatBotInfo.GetAichatBotInfoRes{
		Name:          proto.String(aiChatUserInfo.GetUserInfo().GetName()),
		Desc:          proto.String(aiChatUserInfo.GetUserInfo().GetDescription()),
		Greet:         proto.String(prologue),
		Gender:        proto.Int32(aiChatUserInfo.GetUserInfo().GetGender()),
		BackgroundImg: proto.String(backgroundImg),
		Portrait:      proto.String(aiChatUserInfo.GetUserInfo().GetPortrait()),
		VisibleStatus: proto.Int32(aiChatUserInfo.GetVisibleStatus()),
		ForumInfos:    forumInfos,
		StyleInfo:     styleInfos,
		Tbs:           proto.String(tbs.CreateTbs(ctx, baseData.BaseObj.Req).Gene(true)),
	}

	return tiebaerror.ERR_SUCCESS
}

// parseParams 参数处理
func parseParams(ctx context.Context, baseData *types.GetAiChatBotInfoBaseData) int {
	botUk := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("bot_uk", ""), common.TTT_STRING).(string)

	if botUk != "" {
		// 机器人uk处理
		botUid, err := uidxuk.UK2UID(botUk)
		if err != nil || botUid <= 0 {
			tbcontext.WarningF(ctx, "getAiChatBotInfo input param error, botUk = %v", botUk)
			return tiebaerror.ERR_PARAM_ERROR
		}
		baseData.UserID = botUid
	}

	return tiebaerror.ERR_SUCCESS
}
