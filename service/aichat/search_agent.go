package aichat

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/getAgentList"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/searchAgent"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// SearchAgent 搜索智能体
func SearchAgent(ctx context.Context, baseData *types.SearchAgentBaseData, response *searchAgent.SearchAgentResIdl) int {
	if baseData == nil || baseData.Request == nil {
		tbcontext.FatalF(ctx, "baseData.Request is nil")
		return tiebaerror.ERR_PARAM_ERROR
	}
	userID, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)

	var pn, rn int32 = 1, 10
	if baseData.Request.GetPn() > 0 {
		pn = baseData.Request.GetPn()
	}
	if baseData.Request.GetRn() >= 0 {
		rn = baseData.Request.GetRn()
	}
	searchByEsInput := &chat.SearchAgentEsReq{
		Pn:   proto.Int32(pn),
		Rn:   proto.Int32(rn),
		Key:  proto.String(baseData.Request.GetKey()),
		Type: proto.Int32(2), // 模糊匹配
	}
	searchByEsOutput := &chat.SearchAgentUidsRes{}
	err := tbservice.Call(ctx, "chat", "searchAgentByEs", searchByEsInput, searchByEsOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || searchByEsOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "fail to call service chat:searchAgentByEs, input=[%v], output=[%v], err=[%v]",
			common.ToString(searchByEsInput), common.ToString(searchByEsOutput), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	if searchByEsOutput == nil || searchByEsOutput.GetData() == nil {
		tbcontext.FatalF(ctx, "searchAgentByEs output is Nil")
		return tiebaerror.ERR_SUCCESS
	}
	// 空结果直接返回
	if len(searchByEsOutput.GetData().GetBotInfos()) == 0 {
		return tiebaerror.ERR_SUCCESS
	}
	// 查询到到智能体uid列表
	uidList := make([]int64, 0)
	for _, uid := range searchByEsOutput.GetData().GetBotInfos() {
		uidList = append(uidList, uid.GetBotUid())
	}
	// 查询详细数据
	mgetAiBotUserInfoByUidInput := &chat.MgetAiBotUserInfoByUidReq{
		BotUids:                 uidList,
		NeedFilterAuditStatus:   proto.Int32(1),
		NeedFilterVisibleStatus: proto.Int32(1),
		NeedFilterOnlineStatus:  proto.Int32(1),
		Uid:                     proto.Uint64(userID),
		CallForm:                proto.String("square_search"),
	}
	mgetAiBotUserInfoByUidOutput := &chat.MgetAiBotUserInfoByUidRes{}
	err = tbservice.Call(ctx, "chat", "mgetAiBotUserInfoByUid",
		mgetAiBotUserInfoByUidInput, &mgetAiBotUserInfoByUidOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || mgetAiBotUserInfoByUidOutput.Errno == nil || mgetAiBotUserInfoByUidOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:mgetAiBotUserInfoByUid fail, err:[%v], output:[%s]", err, common.ToString(mgetAiBotUserInfoByUidOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	if mgetAiBotUserInfoByUidOutput == nil || mgetAiBotUserInfoByUidOutput.GetData() == nil {
		tbcontext.FatalF(ctx, "searchAgentByEs output is Nil")
		return tiebaerror.ERR_SUCCESS
	}

	resBotList := make([]*getAgentList.RotList, 0)
	botInfoMap := mgetAiBotUserInfoByUidOutput.GetData().GetBotList()
	for _, uid := range uidList {
		if botInfoMap[uid] == nil {
			continue
		}
		bot := botInfoMap[uid]
		botInfo := &getAgentList.BotInfo{
			Name:             proto.String(bot.GetName()),
			Portrait:         proto.String(bot.GetPortrait()),
			BackgroundUrl:    proto.String(bot.GetBackgroundUrl()),
			Prologue:         proto.String(bot.GetPrologue()),
			Uk:               proto.String(bot.GetChatUk()),
			Pa:               proto.Int64(bot.GetPa()),
			CreateUser:       proto.String(bot.GetCreateUser()),
			CreateUserLevel:  proto.Int32(int32(bot.GetCreateUserForumLevel())),
			CreateUserAvatar: proto.String(bot.GetCreateUserAvatar()),
			DialogueUserNum:  proto.Int32(bot.GetDialogueUserNum()),
			RoleType:         proto.String(bot.GetRoleType()),
			Tags:             make([]*clientProto.AibBotTag, 0),
		}

		// 组装标签信息
		if bot.Tags != nil && len(bot.GetTags()) > 0 {
			for _, tagInfos := range bot.GetTags() {
				newTagInfos := make([]*clientProto.AgentRichText, 0)
				for _, tagInfo := range tagInfos.GetTagInfo() {
					newTagInfos = append(newTagInfos, &clientProto.AgentRichText{
						SubType: proto.Uint32(tagInfo.GetSubType()),
						Content: proto.String(tagInfo.GetContent()),
						ImgUrl:  proto.String(tagInfo.GetImgUrl()),
						Width:   proto.String(tagInfo.GetWidth()),
						Height:  proto.String(tagInfo.GetHeight()),
					})
				}
				if len(newTagInfos) > 0 {
					botInfo.Tags = append(botInfo.Tags, &clientProto.AibBotTag{
						TagInfo: newTagInfos,
					})
				}
			}
		}

		AiGamePlot := &getAgentList.AiGamePlot{}
		if bot.GetAiGamePlot() != nil {
			AiGamePlot = &getAgentList.AiGamePlot{
				PlotTitle:        proto.String(bot.GetAiGamePlot().GetPlotTitle()),
				PlotDescription:  proto.String(bot.GetAiGamePlot().GetPlotDescription()),
				Rate:             proto.Float64(float64(bot.GetAiGamePlot().GetRate())),
				PortraitUrl:      proto.String(bot.GetAiGamePlot().GetPortraitUrl()),
				PlotId:           proto.Int32(int32(bot.GetAiGamePlot().GetPlotId())),
				BotUk:            proto.String(bot.GetChatUk()),
				BotPa:            proto.Int64(bot.GetPa()),
				BotBackgroundUrl: proto.String(bot.GetBackgroundUrl()),
				CreateUser:       proto.String(bot.GetCreateUser()),
				CreateUserLevel:  proto.Int32(int32(bot.GetCreateUserForumLevel())),
				CreateUserAvatar: proto.String(bot.GetCreateUserAvatar()),
				DialogueUserNum:  proto.Int32(bot.GetDialogueUserNum()),
				RoleType:         proto.String(bot.GetRoleType()),
			}
		}
		userSetIDsStr := ""
		if len(bot.GetUserSetIds()) > 0 {
			userSetIDsStr = common.ToString(bot.GetUserSetIds())
		}
		resBotList = append(resBotList, &getAgentList.RotList{
			BotType:      proto.Int32(bot.GetBotType()),
			RobotVersion: proto.Int32(bot.GetBotVersion()),
			AiGamePlot:   AiGamePlot,
			BotInfo:      botInfo,
			UserSet:      proto.String(userSetIDsStr),
		})
	}
	// 封装返回
	total := searchByEsOutput.GetData().GetTotal()
	res := &searchAgent.SearchAgentRes{
		Page: &getAgentList.Page{
			TotalPage: proto.Int32(total/rn + 1),
			CurPage:   proto.Int32(pn),
		},
		List: resBotList,
	}
	response.Data = res
	return tiebaerror.ERR_SUCCESS
}
