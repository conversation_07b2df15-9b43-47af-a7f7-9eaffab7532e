package privateparams

import (
	"context"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
)

// opName 算子名称
const opName = "privateparams"

type Processor struct {
	baseAction *client.UIBaseAction

	lastMsgID int64
}

func NewProcessor() *Processor {
	p := new(Processor)
	return p
}

func (p *Processor) Process(ctx context.Context) error {
	p.lastMsgID = cast.ToInt64(p.baseAction.GetOriginalParam("last_msg_id"))
	return nil
}

func (p *Processor) GetRn() int32 {
	return 15
}

func (p *Processor) GetLastMsgID() int64 {
	return p.lastMsgID
}
