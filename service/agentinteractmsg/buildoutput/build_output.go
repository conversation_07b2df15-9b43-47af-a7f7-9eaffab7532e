package buildoutput

import (
	"context"
	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/agentInteractMsg"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/aichat/base"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentbase"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentinteractmsgs"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/userinfo"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/agentinteractmsg/privateparams"
	"net/url"
	"time"
)

// opName 算子名称
const opName = "buildoutput"

type ProcessBuildOutput struct {
	CommonParams      commonparams.CommonParams
	PrivateParams     privateparams.PrivateParams
	AgentInteractMsgs agentinteractmsgs.AgentInteractMsgs
	AgentBase         agentbase.AgentBase
	UserInfo          userinfo.UserInfo
	output            *agentInteractMsg.AgentInteractMsgRes // 接口返回
}

func NewProcessBuildOutput() *ProcessBuildOutput {
	p := new(ProcessBuildOutput)
	p.output = &agentInteractMsg.AgentInteractMsgRes{}
	return p
}

const (
	msgTypeAgree uint32 = iota + 1
	msgTypeFollow
	msgTypeChat
	msgTypeAchievement
)

const (
	avatarIconAgree   = "https://tieba-ares.cdn.bcebos.com/mis/2025-2/1740569649927/94b6c8ab767d.png"
	avatarIconFollow  = "https://tieba-ares.cdn.bcebos.com/mis/2025-2/1740569650242/58c147afec3a.png"
	avatarAchievement = "https://tieba-ares.cdn.bcebos.com/mis/2025-2/1740558471604/6b044ea2eaa2.png"
)

func (p *ProcessBuildOutput) Process(ctx context.Context) error {
	userInfoMap := p.UserInfo.GetRawUsers()
	agentInfoMap := p.AgentBase.GetBotList()
	messages := p.AgentInteractMsgs.GetMessages()

	p.output.HasMore = proto.Uint32(0)
	if p.AgentInteractMsgs.HasMore() {
		p.output.HasMore = proto.Uint32(1)
	}
	p.output.MsgList = make([]*agentInteractMsg.AgentInteractMsgResMsg, 0, len(messages))
	for _, msg := range messages {
		userInfo := userInfoMap[msg.GetUserId()]
		agentInfo := agentInfoMap[int64(msg.GetBotUid())]
		if userInfo == nil || agentInfo == nil {
			tbcontext.WarningF(ctx, "failed to get user or agent info: user_id %d, bot_uid %d", msg.GetUserId(), msg.GetBotUid())
			continue
		}

		msgRes := &agentInteractMsg.AgentInteractMsgResMsg{
			MsgId:      msg.Id,
			Type:       msg.Type,
			Title:      proto.String(userInfo.GetUserInfoNameShowV2(ctx)),
			CreateTime: msg.CreateTime,
			UserInfo: &agentInteractMsg.AgentInteractMsgUserInfo{
				Portrait: proto.String(tbportrait.Encode(userInfo.GetUserId(), userInfo.GetUserInfoNameShowV2(ctx), time.Now().Unix())),
			},
			AgentInfo: &agentInteractMsg.AgentInteractMsgAgentInfo{
				Name:              agentInfo.Name,
				Portrait:          agentInfo.Portrait,
				PaType:            agentInfo.PaType,
				ChatBackgroundUrl: agentInfo.ChatBackgroundUrl,
				Prologue:          agentInfo.Prologue,
				BotUk:             agentInfo.ChatUk,
			},
		}
		for _, cardTitle := range agentInfo.IntroduceCardTitleInfo {
			msgRes.AgentInfo.IntroduceCardTitleInfo = append(msgRes.AgentInfo.IntroduceCardTitleInfo, &base.TitleInfo{
				Content:  cardTitle.Content,
				Icon:     cardTitle.Icon,
				IconType: cardTitle.IconType,
			})
		}
		if agentInfo.ThemeColor != nil {
			msgRes.AgentInfo.ThemeColor = &base.BotThemeColor{
				BubbleBgColor: agentInfo.ThemeColor.BubbleBgColor,
				ThemeColor:    agentInfo.ThemeColor.ThemeColor,
			}
		}
		if agentInfo.AiGamePlot != nil {
			msgRes.AgentInfo.AiGamePlot = &base.PlotBaseInfo{
				PlotTitle:        agentInfo.AiGamePlot.PlotTitle,
				PlotId:           agentInfo.AiGamePlot.PlotId,
				BotBackgroundUrl: agentInfo.AiGamePlot.BotBackgroundUrl,
			}
		}

		msgRes.Schema = p.buildSchema(agentInfo)

		switch msg.GetType() {
		case msgTypeAgree:
			msgRes.UserInfo.AvatarIcon = proto.String(avatarIconAgree)
			if agentInfo.GetPaType() == 1 {
				msgRes.Content = proto.String("喜欢了你的AI游戏：" + agentInfo.GetName())
			} else {
				msgRes.Content = proto.String("喜欢了你的智能体：" + agentInfo.GetName())
			}
		case msgTypeFollow:
			msgRes.UserInfo.AvatarIcon = proto.String(avatarIconFollow)
			msgRes.Content = proto.String("关注了你的智能体：" + agentInfo.GetName())
		case msgTypeAchievement:
			msgRes.Title = proto.String("智能体成就")
			msgRes.Content = msg.Content
			msgRes.Button = &agentInteractMsg.AgentInteractMsgButton{
				Content: proto.String(msg.GetShareContent()),
			}
			msgRes.UserInfo.Avatar = proto.String(avatarAchievement)
		default:
			tbcontext.WarningF(ctx, "unknown msg type: %d", msg.GetType())
			continue
		}

		p.output.MsgList = append(p.output.MsgList, msgRes)
	}
	return nil
}

func (p *ProcessBuildOutput) buildSchema(botInfo *chat.BotBaseInfo) *string {
	h5URL := "https://tieba.baidu.com/mo/q/hybrid-main-chatgroup/ai-chat?nonavigationbar=1&customfullscreen=1&loadingSignal=1"
	pageType := "aibot_chat"
	// agent
	if botInfo.GetAgentId() != "" {
		h5URL = "https://tieba.baidu.com/mo/q/hybrid-main-chatgroup/agent-chat?nonavigationbar=1&customfullscreen=1&loadingSignal=1"
		pageType = "agentbot_chat"
	}
	params := map[string]any{
		"page": "im/AISingleChat",
		"pageParams": map[string]any{
			"h5Url":      h5URL,
			"uk":         botInfo.GetChatUk(),
			"pageType":   pageType,
			"paid":       botInfo.GetPa(),
			"hintText":   "想跟我聊点什么～",
			"pageSource": "agent_message",
		},
	}
	paramJSON, _ := jsoniter.MarshalToString(params)
	return proto.String("tiebaapp://router/portal?params=" + url.QueryEscape(paramJSON))
}
