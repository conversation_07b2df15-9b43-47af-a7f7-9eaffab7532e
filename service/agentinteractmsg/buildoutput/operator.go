package buildoutput

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentbase"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/agentinteractmsgs"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/userinfo"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/agentinteractmsg/privateparams"
)

// OperatorBuildOutput 算子定义
type OperatorBuildOutput struct {
	CommonParams      commonparams.CommonParams           `inject:"canLost=false,canNil=false"`
	PrivateParams     privateparams.PrivateParams         `inject:"canLost=false,canNil=false"`
	AgentInteractMsgs agentinteractmsgs.AgentInteractMsgs `inject:"canLost=false,canNil=false"`
	AgentBase         agentbase.AgentBase                 `inject:"canLost=false,canNil=false"`
	UserInfo          userinfo.UserInfo                   `inject:"canLost=false,canNil=false"`
}

func (o *OperatorBuildOutput) DoImpl(ctx *engine.Context) error {
	p := NewProcessBuildOutput()
	p.CommonParams = o.CommonParams
	p.PrivateParams = o.PrivateParams
	p.AgentInteractMsgs = o.AgentInteractMsgs
	p.AgentBase = o.AgentBase
	p.UserInfo = o.UserInfo

	err := p.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	// 导出数据，供controller输出返回值使用
	ctx.MustRegisterInstance(p.output)
	return nil
}
