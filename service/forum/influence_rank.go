package forum

import (
	"context"
	"fmt"
	"net/url"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/baidu/uidxuk"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/influenceRank"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type getForumInfluenceRes struct {
	Errno  int                             `json:"errno"`
	Errmsg string                          `json:"errmsg"`
	Data   *influenceRank.InfluenceRankRes `json:"data"`
	Ie     string                          `json:"ie"`
}

func InfluenceRank(ctx context.Context, baseData *types.InfluenceRankBaseData,
	response *influenceRank.InfluenceRankResIdl) int {
	forumId := baseData.Request.GetForumId()

	if forumId == 0 {
		tbcontext.WarningF(ctx, "InfluenceRank input param error, forumId = %v", forumId)
		return tiebaerror.ERR_PARAM_ERROR
	}

	uid, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	// uid = 853727249
	input := map[string]any{
		"forum_id": forumId,
		"user_id":  uid,
	}

	res := &getForumInfluenceRes{}
	err := tbservice.Call(ctx, "common", "getForumInfluence", input, res,
		tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service common:getForumInfluence, err = %v, input = %v, output = %v", err, input, res)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	subappType, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("subapp_type", ""), common.TTT_STRING).(string)
	if subappType == stcdefine.SUB_APP_SHOUBAI_UGC {
		if res.Data != nil && len(res.Data.GetList()) > 0 {
			for _, v := range res.Data.GetList() {
				// 手百ugc下发单独的schema
				v.Schema = common.GetStringPtr(GetCenterShoubaiUGC(v.GetUserId(), "tiezi"))
			}
		}
		if res.Data != nil && res.Data.GetCurrentUser() != nil {
			res.Data.GetCurrentUser().Schema = common.GetStringPtr(GetCenterShoubaiUGC(res.Data.GetCurrentUser().GetUserId(), "tiezi"))
		}
	}

	response.Data = res.Data
	return tiebaerror.ERR_SUCCESS
}

func GetCenterShoubaiUGC(uid int64, tab string) string {
	uk, _ := uidxuk.UID2UK(uint64(uid))
	paramsMap := map[string]any{
		"context": map[string]any{
			"from":  "tieba", // 固定值
			"scene": "tieba", // 固定值
			"tab":   tab,
			"id":    uk,
			"type":  "uk", // 固定值
		},
	}
	jsonStr, _ := jsoniter.MarshalToString(paramsMap)
	encodedParams := url.QueryEscape(jsonStr)
	targetURL := fmt.Sprintf("baiduboxapp://personalPage/entry?callback=callback&params=%s", encodedParams)
	return targetURL
}
