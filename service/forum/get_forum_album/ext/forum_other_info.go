package ext

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	commonproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbum"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/util"
)

// 昨日进吧，吧热度数据查询
type ForumOtherInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_album_fourm_other_info", func() engine.Job {
		return &ForumOtherInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewForumOtherInfo(ctx *engine.Context) *ForumOtherInfo {
	return &ForumOtherInfo{
		ctx: ctx,
	}
}
func (a *ForumOtherInfo) IsValid(ctx context.Context, baseData *types.GetForumAlbumBaseData) bool {
	return true
}
func (a *ForumOtherInfo) Execute(ctx context.Context, outData *getForumAlbum.GetForumAlbumResData, baseData *types.GetForumAlbumBaseData) error {
	eg := new(gtask.Group)
	// 获取吧热度信息
	eg.Go(func() error {
		return a.mgetHotByForumID(ctx, baseData)
	})

	// 获取昨日进吧数据
	eg.Go(func() error {
		return a.mgetForumEnterNum(ctx, baseData)
	})

	// 获取垂类topN吧信息
	eg.Go(func() error {
		return a.getForumIsTopN(ctx, baseData)
	})

	// 结果等待
	_, err := eg.Wait()

	if err != nil {
		return err
	}

	return nil
}

type ForumOtherInfoOperator struct {
}

func (rdop *ForumOtherInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *getForumAlbum.GetForumAlbumResData
	var baseData *types.GetForumAlbumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewForumOtherInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum_album_fourm_other_info execute fail: %v", err)
		return err
	}

	return nil
}

// 调用mgetHotByForumId获取吧热度信息
func (a *ForumOtherInfo) mgetHotByForumID(ctx context.Context, baseData *types.GetForumAlbumBaseData) error {
	static := baseData.StaticField
	// 如果没有fid，则直接返回
	if len(static.FidInfList) == 0 {
		return nil
	}
	multi := tbservice.Multi()

	for index, fourmIds := range static.FidInfList {
		commonParam := &tbservice.Parameter{
			Service: "common",
			Method:  "mgetHotByForumId",
			Input: map[string]interface{}{
				"forum_ids": fourmIds,
			},
			Output: &commonproto.MgetHotByForumIdRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, fmt.Sprintf("mgetHotByForumId_%v", index), commonParam)
	}

	multi.Call(ctx)
	for index, fourmIds := range static.FidInfList {
		resInf, err := multi.GetResult(ctx, fmt.Sprintf("mgetHotByForumId_%v", index))
		if err != nil {
			tbcontext.WarningF(ctx, "call mgetHotByForumId fail, fids=[%v], err=[%v]", fourmIds, err)
			return errno.ErrCallServiceFail
		}
		res, ok := resInf.(*commonproto.MgetHotByForumIdRes)
		if !ok || res == nil || res.Errno == nil || res.GetErrno() != errno.CodeSuccess {
			tbcontext.WarningF(ctx, "call mgetHotByForumId fail, fids=[%v], output=[%v]", fourmIds, resInf)
			return errno.ErrCallServiceFail
		}

		for _, forumInfo := range res.GetOutput() {
			intFid := forumInfo.GetForumId()
			static.ForumHotValueMap[intFid] = forumInfo.GetHotValue()
		}
	}
	return nil
}

// 获取昨日进吧数据
func (a *ForumOtherInfo) mgetForumEnterNum(ctx context.Context, baseData *types.GetForumAlbumBaseData) error {
	static := baseData.StaticField
	// 如果没有fid，或者用户未登录则直接返回
	if len(static.FidInfList) == 0 {
		return nil
	}
	multi := tbservice.Multi()

	for index, fourmIds := range static.FidInfList {

		tempParmas := &tbservice.Parameter{
			Service: "forumenter",
			Method:  "mgetForumEnterNum",
			Input: map[string]interface{}{
				"forum_ids": fourmIds,
				"duration":  "day",
			},
			Output: &commonproto.MgetForumEnterNumRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
				tbservice.WithServiceName("common"),
			},
		}
		multi.Register(ctx, fmt.Sprintf("mgetForumEnterNum_%v", index), tempParmas)
	}

	multi.Call(ctx)
	for index, fourmIds := range static.FidInfList {
		resInf, err := multi.GetResult(ctx, fmt.Sprintf("mgetForumEnterNum_%v", index))
		if err != nil {
			tbcontext.WarningF(ctx, "call mgetForumEnterNum fail, fids=[%v], uid=[%v], err=[%v]", fourmIds, static.UserID, err)
			return errno.ErrCallServiceFail
		}
		res, ok := resInf.(*commonproto.MgetForumEnterNumRes)
		if !ok || res == nil || res.Errno == nil || res.GetErrno() != errno.CodeSuccess {
			tbcontext.WarningF(ctx, "call mgetForumEnterNum fail, fids=[%v], uid=[%v], output=[%v]", fourmIds, static.UserID, resInf)
			return errno.ErrCallServiceFail
		}

		for _, forumInfo := range res.GetData() {
			fid := forumInfo.GetForumId()
			static.ForumEnterNumMap[fid] = forumInfo.GetEnterNum()
		}
	}
	return nil
}

// 判断是否是热门topN吧
func (a *ForumOtherInfo) getForumIsTopN(ctx context.Context, baseData *types.GetForumAlbumBaseData) error {
	top5ForumMap, err := util.GetTopNForum(ctx)
	if err != nil {
		return err
	}

	static := baseData.StaticField
	for _, fid := range static.ForumIDList {
		if class, ok := top5ForumMap[fid]; ok {
			static.ForumIsTop5Map[fid] = class
		}
	}
	return nil
}
