package core

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	commonproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/yuelao2"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbum"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/util"
)

// 构建返回值

type BuildOutput struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_album_build_output", func() engine.Job {
		return &BuildOutputOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewBuildOutput(ctx *engine.Context) *BuildOutput {
	return &BuildOutput{
		ctx: ctx,
	}
}
func (a *BuildOutput) IsValid(ctx context.Context, baseData *types.GetForumAlbumBaseData) bool {
	return true
}
func (a *BuildOutput) Execute(ctx context.Context, outData *getForumAlbum.GetForumAlbumResData, baseData *types.GetForumAlbumBaseData) error {
	// 构建运营卡信息返回值
	a.buildForumCard(ctx, outData, baseData)
	// 构建新热吧单返回值
	a.buildHotForumAlbum(ctx, baseData)
	// 构建推荐吧单返回值
	a.buildRecomForumAlbum(ctx, baseData)
	// 合并新热吧单和推荐吧单
	a.mergeForumAlbum(ctx, outData, baseData)
	// 构建conf返回值
	a.buildConfRes(ctx, outData, baseData)

	return nil
}

type BuildOutputOperator struct {
}

func (rdop *BuildOutputOperator) DoImpl(ctx *engine.Context) error {
	var outData *getForumAlbum.GetForumAlbumResData
	var baseData *types.GetForumAlbumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewBuildOutput(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum_album_build_output execute fail: %v", err)
		return err
	}

	return nil
}

// 根据用户所属垂类对新热吧单进行排序，如果用户属于A，新热吧单为 B1,B2,A1,A2  排序后为 A1,B1,B2,A2
func (a *BuildOutput) sortHotForumAlbum(ctx context.Context, userVertical string, albumList []*commonproto.HotForumAlbumInfo) []*commonproto.HotForumAlbumInfo {
	if userVertical == "" || len(albumList) == 0 {
		return albumList
	}

	// 构造排序链表
	head := new(types.HotForumAlbumSortSt)
	pre := head
	for _, album := range albumList {
		albumSort := &types.HotForumAlbumSortSt{
			AlbumInfo:     album,
			VerticalField: album.GetVerticalFields(),
		}
		pre.Next = albumSort
		pre = albumSort
	}

	pre = head
	for pre.Next != nil {
		now := pre.Next
		if now.HasSorted || now.VerticalField == userVertical || now.Next == nil {
			// 已经排过序、与用户垂类相同或者已经是最后一个了，就不需要进行排序了
			pre = pre.Next
			continue
		}

		// 摘掉now节点，将pre的next指向now的next
		pre.Next = now.Next
		// 寻找now的插入位置，如果now后面还有两位就插入到后两位的后面，如果不足两位，就插入到后一位的后面
		temp := now.Next
		if temp.Next != nil {
			temp = temp.Next
		}
		now.Next = temp.Next
		temp.Next = now
		// now节点标记为已经排过序
		now.HasSorted = true
	}

	// 记录新热吧单的插入楼层
	insertIndexs := make([]uint32, 0, len(albumList))
	for _, album := range albumList {
		insertIndexs = append(insertIndexs, album.GetInsertIndex())
	}
	newHotForumAlbumList := make([]*commonproto.HotForumAlbumInfo, 0, len(albumList))
	for i := 0; head.Next != nil; head = head.Next {
		if head.Next.AlbumInfo != nil {
			head.Next.AlbumInfo.InsertIndex = proto.Uint32(insertIndexs[i])
		}
		newHotForumAlbumList = append(newHotForumAlbumList, head.Next.AlbumInfo)
		i += 1
	}
	return newHotForumAlbumList
}

// 构造运营卡返回值
func (a *BuildOutput) buildForumCard(ctx context.Context, outData *getForumAlbum.GetForumAlbumResData, baseData *types.GetForumAlbumBaseData) {
	static := baseData.StaticField
	if len(static.OpCardMeta) == 0 {
		return
	}

	outData.ForumCardList = make([]*getForumAlbum.ForumCardInfo, 0, len(static.OpCardMeta))
	for _, card := range static.OpCardMeta {
		forumInfo, ok := static.ForumMetaInfoMap[card.GetForumId()]
		strThemeColor := card.GetThemeColor()
		colors := strings.Split(strThemeColor, "@@")
		if !ok || len(colors) != 6 {
			// 如果forumInfo不存在，说明吧信息获取失败，跳过这张卡片
			// 如果主题色配置不合法，也跳过这张卡片
			tbcontext.SetDealSuccess(ctx, false)
			tbcontext.WarningF(ctx, "forum_album_build_output: forumInfo not found or color illegal for card %v", card)
			continue
		}
		forumCard := &getForumAlbum.ForumCardInfo{
			HeadImg:     card.HeadImg,
			RecomTag:    card.RecomTag,
			RecomType:   card.RecomType,
			RecomTitle:  card.RecomTitle,
			InsertIndex: card.InsertIndex,
			ForumInfo: &getForumAlbum.ForumMetaInfo{
				Id:          card.ForumId,
				Name:        proto.String(forumInfo.GetForumName().GetForumName()),
				Avatar:      proto.String(forumInfo.GetCard().GetAvatar()),
				Intro:       card.ForumIntro,
				MemberCount: proto.Int64(int64(forumInfo.GetStatistics().GetMemberCount())),
				HotNum:      proto.Int64(static.ForumHotValueMap[card.GetForumId()]),
			},
		}
		colorSt := &getForumAlbum.ThemeColorInfo{
			Day:   colors[0:2],
			Night: colors[2:4],
			Dark:  colors[4:],
		}
		forumCard.ThemeColor = colorSt
		outData.ForumCardList = append(outData.ForumCardList, forumCard)
	}
	return
}

// 构造新热吧单的返回值
// 因为吧单需求迭代变更，这个方法已经不适用了，留作备用
// func (a *BuildOutput) buildHotForumAlbum(ctx context.Context, baseData *types.GetForumAlbumBaseData) {
// 	static := baseData.StaticField
// 	if len(static.HotForumAlbumList) == 0 {
// 		return
// 	}

// 	// 新热吧单列表按照插入楼层顺序排序
// 	sort.Slice(static.HotForumAlbumList, func(i, j int) bool {
// 		return static.HotForumAlbumList[i].GetInsertIndex() < static.HotForumAlbumList[j].GetInsertIndex()
// 	})

// 	userStaticField := ""
// 	if len(static.UserVerticalField) > 0 {
// 		userStaticField = static.UserVerticalField[0]
// 	}
// 	// 根据用户所属垂类对新热吧单进行排序
// 	static.HotForumAlbumList = a.sortHotForumAlbum(ctx, userStaticField, static.HotForumAlbumList)

// 	// 构建基础信息
// 	for _, album := range static.HotForumAlbumList {
// 		resAlbumInfo := &getForumAlbum.ForumAlbumInfo{
// 			AlbumInfo: &getForumAlbum.ForumAlbumMetaInfo{
// 				Id:       album.Id,
// 				Name:     album.Name,
// 				Type:     proto.Int64(2), // 2表示新热吧单
// 				Tags:     album.Tags,
// 				ForumNum: proto.Int64(int64(len(album.ForumInfo))),
// 				HotNum:   proto.Int64(static.AlbumShowInfoMap[album.GetId()]),
// 				Icon:     proto.String(static.HotForumAlbumIcon),
// 			},
// 		}
// 		if resAlbumInfo.GetAlbumInfo().GetForumNum() > 4 {
// 			resAlbumInfo.GuideText = proto.String(static.ExpandGuideWordHasMore)
// 		} else {
// 			resAlbumInfo.GuideText = proto.String(static.ExpandGuideWordNoMore)
// 		}

// 		var err error
// 		resAlbumInfo.ForumList, err = a.transForumAndThread(ctx, album.ForumInfo, "hot", static)
// 		if err != nil {
// 			tbcontext.WarningF(ctx, "build_output: this album has invisible thread, album:[%s]", common.ToString(album))
// 			continue
// 		}

// 		static.ResHotForumAlbumList[album.GetId()] = resAlbumInfo
// 	}

// 	return
// }

// 构造推荐吧单的返回值
func (a *BuildOutput) buildRecomForumAlbum(ctx context.Context, baseData *types.GetForumAlbumBaseData) {
	static := baseData.StaticField
	if len(static.RecomForumAlbumList) == 0 {
		return
	}

	for _, album := range static.RecomForumAlbumList {
		resAlbumInfo := &getForumAlbum.ForumAlbumInfo{
			AlbumInfo: &getForumAlbum.ForumAlbumMetaInfo{
				Id:       album.TopicId,
				Name:     album.Name,
				Type:     proto.Int64(types.RecomAlbumType), // 1表示推荐吧单(兴趣吧单)
				Tags:     album.Tag,
				ForumNum: proto.Int64(int64(len(album.ForumList))),
				HotNum:   proto.Int64(static.AlbumShowInfoMap[album.GetTopicId()]),
				Icon:     proto.String(static.RecommendForumAlbumIcon),
				Style:    proto.String(static.ShowStyle),
			},
		}
		if resAlbumInfo.GetAlbumInfo().GetForumNum() > int64(static.ThreadNum) {
			resAlbumInfo.GuideText = proto.String(static.ExpandGuideWordHasMore)
		} else {
			resAlbumInfo.GuideText = proto.String(static.ExpandGuideWordNoMore)
		}

		// 当前简化版帖子信息构建逻辑不一样，分两个函数实现，到时候完整版直接切换函数方法就行
		// var err error
		// resAlbumInfo.ForumList, err = a.transForumAndThread(ctx, album.ForumList, "recom", static)
		err := a.transForumAndThreadForMini(ctx, resAlbumInfo, album, static)
		if err != nil {
			tbcontext.WarningF(ctx, "build_output: this album has invisible thread, album:[%s]", common.ToString(album))
			continue
		}

		static.ResRecomForumAlbumList = append(static.ResRecomForumAlbumList, resAlbumInfo)
	}

	return
}

// 简化版ui接口时，构造返回吧信息和帖子信息
func (a *BuildOutput) transForumAndThreadForMini(ctx context.Context, resAlbumInfo *getForumAlbum.ForumAlbumInfo,
	rawData *yuelao2.RecommForumAlbumInfo, static *types.GetForumAlbumStaticField) error {
	// 1 从原始数据中提取出吧id
	fidList := make([]int64, 0)
	for _, forumInfo := range rawData.GetForumList() {
		fidList = append(fidList, forumInfo.GetFid())
	}

	// 2 拼每个吧的基础数据
	forumInfoMap := make(map[int64]*getForumAlbum.ForumMetaInfo, 0)
	for _, fid := range fidList {
		resForumInfo := new(getForumAlbum.ForumInfo)
		forumRawInfo := static.ForumMetaInfoMap[fid]
		forumMetaInfo := &getForumAlbum.ForumMetaInfo{
			Id:           proto.Int64(fid),
			Name:         proto.String(forumRawInfo.GetForumName().GetForumName()),
			Avatar:       proto.String(forumRawInfo.GetCard().GetAvatar()),
			MemberCount:  proto.Int64(int64(forumRawInfo.GetStatistics().GetMemberCount())),
			HotNum:       proto.Int64(static.ForumHotValueMap[fid]),
			ThreadNum:    proto.Int64(int64(forumRawInfo.GetStatistics().GetThreadNum())),
			LastVisitNum: proto.Int64(static.ForumEnterNumMap[fid]),
		}
		// 如果该吧是某个垂类的热度top5，就下发该吧的垂类
		if verticalField, ok := static.ForumIsTop5Map[fid]; ok {
			forumMetaInfo.VerticalField = proto.String(verticalField)
		}
		resForumInfo.ForumInfo = forumMetaInfo
		forumInfoMap[fid] = forumMetaInfo
	}
	resAlbumInfo.ThreadIdList = make([]*getForumAlbum.TidFidSt, 0)
	resAlbumInfo.ThreadList = make([]*getForumAlbum.ThreadInfo, 0)

	// 3 拼外露贴信息
	usedTidMap := make(map[int64]bool, 0)
	for fidIndex, forumInfo := range rawData.GetForumList() {
		// 前x个吧，取第一个帖子或者第二个帖子
		if fidIndex < static.ThreadNum {
			tidSt := forumInfo.ThreadList[0]
			usedTidMap[tidSt.GetTid()] = true
			if _, ok := static.ThreadMetaInfoMap[tidSt.GetTid()]; !ok {
				tidSt = forumInfo.ThreadList[1]
				usedTidMap[tidSt.GetTid()] = true
			}

			if threadMeta, ok := static.ThreadMetaInfoMap[tidSt.GetTid()]; ok {
				threadInfo := buildAlbumThreadInfo(ctx, threadMeta, tidSt, forumInfoMap)
				if threadInfo == nil {
					continue
				}
				resAlbumInfo.ThreadList = append(resAlbumInfo.ThreadList, threadInfo)
			}
		} else {
			break
		}
	}
	// 推荐返回的吧不足6个时需要补充帖子
	needPaddingThreadNum := static.TopicNeedPaddingThreadNumMap[rawData.GetTopicId()]
	if needPaddingThreadNum > 0 {
		// 补全帖子列表, 第二个帖子或者第三个帖子
		for fidIndex, forumInfo := range rawData.GetForumList() {
			// 前NeedPaddingThreadNum个吧
			if len(forumInfo.GetThreadList()) < 2 || fidIndex >= needPaddingThreadNum {
				continue
			}

			tidSt := forumInfo.ThreadList[1]
			usedTidMap[tidSt.GetTid()] = true
			_, ok := static.ThreadMetaInfoMap[tidSt.GetTid()]
			// used或者不可见取第三个
			if usedTidMap[tidSt.GetTid()] || !ok {
				if len(forumInfo.GetThreadList()) >= 3 {
					tidSt = forumInfo.ThreadList[2]
					usedTidMap[tidSt.GetTid()] = true
				}
			}
			if threadMeta, ok := static.ThreadMetaInfoMap[tidSt.GetTid()]; ok {
				threadInfo := buildAlbumThreadInfo(ctx, threadMeta, tidSt, forumInfoMap)
				if threadInfo == nil {
					continue
				}
				resAlbumInfo.ThreadList = append(resAlbumInfo.ThreadList, threadInfo)
			}

		}
	}

	if len(resAlbumInfo.ThreadList) < static.ThreadNum {
		// 如果没有找到外露贴信息，说明这个帖子被封禁了，就不展示这个吧单
		return errors.New("thread info not found")
	}

	// 4 拼帖子id列表信息
	for _, tidInfo := range static.AlbumID2TidsMap[rawData.GetTopicId()] {
		if !usedTidMap[tidInfo.GetTid()] {
			resAlbumInfo.ThreadIdList = append(resAlbumInfo.ThreadIdList, &getForumAlbum.TidFidSt{
				Tid: proto.Int64(tidInfo.GetTid()),
				Fid: proto.Int64(tidInfo.GetFid()),
			})
		}
		// 超过50个帖子截断
		if len(resAlbumInfo.ThreadIdList) > 50 {
			break
		}
	}

	return nil
}

func buildAlbumThreadInfo(ctx context.Context, threadMeta *post.ThreadInfo, tidSt *yuelao2.ThreadInfo,
	forumInfoMap map[int64]*getForumAlbum.ForumMetaInfo) *getForumAlbum.ThreadInfo {

	threadInfo := new(getForumAlbum.ThreadInfo)
	threadInfo.ThreadId = proto.Int64(tidSt.GetTid())
	threadInfo.Title = threadMeta.Title
	threadInfo.FirstFloorText = threadMeta.Abstract
	threadInfo.PostNum = proto.Int64(int64(threadMeta.GetPostNum()))
	if len(threadMeta.GetMedia()) > 0 {
		for _, media := range threadMeta.GetMedia() {
			if media.GetType() != "pic" {
				continue
			} else {
				url := util.GenPicURLForAlbum(media.GetBigPic())
				size := media.GetPicInfo().GetBig()
				threadInfo.ImgInfo = &getForumAlbum.ImgInfo{
					Url:   proto.String(url),
					Bsize: proto.String(fmt.Sprintf("%d,%d", size.GetWidth(), size.GetHeight())),
				}
				break
			}
		}
	}
	// 没拿到图片信息就不返回，避免前端显示问题
	if threadInfo.ImgInfo == nil {
		tbcontext.WarningF(ctx, "build_output: thread has no image info, thread:[%s]", common.ToString(threadMeta))
		return nil
	}
	threadInfo.ForumInfo = forumInfoMap[tidSt.GetFid()]

	return threadInfo
}

// 完整版ui接口时，构造返回吧信息和帖子信息
// 因为吧单需求迭代变更，这个转换方法已经不适用了，留作备用
// func (a *BuildOutput) transForumAndThread(ctx context.Context, rawData any, dataFrom string,
// 	static *types.GetForumAlbumStaticField) ([]*getForumAlbum.ForumInfo, error) {
// 	// 1 从原始数据中提取出吧id列表和对应的帖子id列表
// 	fidList := make([]int64, 0)
// 	tidListMap := make(map[int64][]int64)
// 	if dataFrom == "hot" {
// 		rawForumList := rawData.([]*commonproto.HotForumAlbumForumInfo)
// 		for _, forumInfo := range rawForumList {
// 			fidList = append(fidList, forumInfo.GetForumId())
// 			tidListMap[forumInfo.GetForumId()] = forumInfo.GetThreadIds()
// 		}
// 	} else if dataFrom == "recom" {
// 		rawForumList := rawData.([]*yuelao2.ForumInfo)
// 		for _, forumInfo := range rawForumList {
// 			fidList = append(fidList, forumInfo.GetFid())
// 			tidList := make([]int64, 0, len(forumInfo.ThreadList))
// 			for _, threadInfo := range forumInfo.ThreadList {
// 				tidList = append(tidList, threadInfo.GetTid())
// 			}
// 			tidListMap[forumInfo.GetFid()] = tidList
// 		}
// 	}

// 	// 2 拼每个吧的基础数据及帖子基础数据
// 	resForumList := make([]*getForumAlbum.ForumInfo, 0, len(fidList))
// 	for findex, fid := range fidList {
// 		resForumInfo := new(getForumAlbum.ForumInfo)
// 		forumRawInfo := static.ForumMetaInfoMap[fid]
// 		forumMetaInfo := &getForumAlbum.ForumMetaInfo{
// 			Id:           proto.Int64(fid),
// 			Name:         proto.String(forumRawInfo.GetForumName().GetForumName()),
// 			Avatar:       proto.String(forumRawInfo.GetCard().GetAvatar()),
// 			MemberCount:  proto.Int64(int64(forumRawInfo.GetStatistics().GetMemberCount())),
// 			HotNum:       proto.Int64(static.ForumHotValueMap[fid]),
// 			IsLike:       proto.Bool(static.UserLikeForumMap[fid] == 1),
// 			ThreadNum:    proto.Int64(int64(forumRawInfo.GetStatistics().GetThreadNum())),
// 			LastVisitNum: proto.Int64(static.ForumEnterNumMap[fid]),
// 		}
// 		// 如果该吧是某个垂类的热度top5，就下发该吧的垂类
// 		if verticalField, ok := static.ForumIsTop5Map[fid]; ok {
// 			forumMetaInfo.VerticalField = proto.String(verticalField)
// 		}
// 		threadInfo := new(getForumAlbum.ThreadInfo)
// 		threadList := make([]int64, 0)
// 		findFirstThread := false
// 		for tindex, tid := range tidListMap[fid] {
// 			if findex < 4 && tindex < 2 && !findFirstThread { // 前4个吧要获取外露贴信息，外露贴是每个吧的第一个或者第二个帖子
// 				if threadMeta, ok := static.ThreadMetaInfoMap[tid]; ok {
// 					threadInfo.ThreadId = proto.Int64(tid)
// 					threadInfo.Title = threadMeta.Title
// 					threadInfo.FirstFloorText = threadMeta.Abstract
// 					if len(threadMeta.GetMedia()) > 0 {
// 						for _, media := range threadMeta.GetMedia() {
// 							if media.GetType() != "pic" {
// 								continue
// 							}
// 							url := util.GenPicURLForAlbum(media.GetBigPic())
// 							size := media.GetPicInfo().GetBig()
// 							threadInfo.ImgInfo = &getForumAlbum.ImgInfo{
// 								Url:   proto.String(url),
// 								Bsize: proto.String(fmt.Sprintf("%d,%d", size.GetWidth(), size.GetHeight())),
// 							}
// 						}
// 					}
// 					findFirstThread = true
// 				} else {
// 					tbcontext.WarningF(ctx, "build_output: thread info not found, tid:[%d]", tid)
// 				}
// 			} else {
// 				threadList = append(threadList, tid)
// 			}
// 		}
// 		if !findFirstThread {
// 			// 如果没有找到外露贴信息，说明这个帖子被封禁了，就不展示这个吧单
// 			return nil, errors.New("thread info not found")
// 		}

// 		// todo 图片顺序兜底

// 		resForumInfo.ForumInfo = forumMetaInfo
// 		resForumInfo.ThreadInfo = threadInfo
// 		resForumInfo.ThreadList = threadList
// 		resForumList = append(resForumList, resForumInfo)
// 	}

// 	return resForumList, nil
// }

// 将新热吧单插入到推荐吧单中，形成最终的吧单列表
// 因为吧单需求迭代变更，这个方法已经不适用了，留作备用
// func (a *BuildOutput) mergeForumAlbum(ctx context.Context, outData *getForumAlbum.GetForumAlbumResData, baseData *types.GetForumAlbumBaseData) {
// 	static := baseData.StaticField
// 	// 如果没有可以展示的新热吧单，直接将推荐吧单放入结果集中
// 	if len(static.ResHotForumAlbumList) == 0 {
// 		outData.ForumAlbum = static.ResRecomForumAlbumList
// 		return
// 	}

// 	// 遍历推荐吧单，将新热吧单插入到合适的位置
// 	for index, forumAlbum := range static.ResRecomForumAlbumList {
// 		// HotForumAlbumList 中的新热吧单是按插入顺序排序的，所以这里直接取第一个
// 		if len(static.HotForumAlbumList) > 0 && int(static.HotForumAlbumList[0].GetInsertIndex()) == index+1 {
// 			// 如果这个热门吧单没有被过滤掉，就插入到结果集中
// 			if hotForumAlbum, ok := static.ResHotForumAlbumList[static.HotForumAlbumList[0].GetId()]; ok {
// 				outData.ForumAlbum = append(outData.ForumAlbum, hotForumAlbum)
// 			}
// 			if len(static.HotForumAlbumList) >= 2 {
// 				static.HotForumAlbumList = static.HotForumAlbumList[1:]
// 			} else {
// 				static.HotForumAlbumList = make([]*commonproto.HotForumAlbumInfo, 0)
// 			}
// 		}
// 		outData.ForumAlbum = append(outData.ForumAlbum, forumAlbum)
// 	}

// 	// 如果遍历完，新热吧单还有没有插入的，就放在最后（这种情况仅存在于推荐吧单被过滤后，不满足新热吧单的插入条件）
// 	for _, item := range static.HotForumAlbumList {
// 		if hotForumAlbum, ok := static.ResHotForumAlbumList[item.GetId()]; ok {
// 			outData.ForumAlbum = append(outData.ForumAlbum, hotForumAlbum)
// 		}
// 	}
// 	return
// }

// 构建conf返回值
func (a *BuildOutput) buildConfRes(ctx context.Context, outData *getForumAlbum.GetForumAlbumResData, baseData *types.GetForumAlbumBaseData) {
	static := baseData.StaticField
	outData.Config = &getForumAlbum.ForumAlbumConfig{
		ForumEntryIconForeDay:   proto.String(static.ForumEntryIconForeDay),
		ForumEntryIconForeNight: proto.String(static.ForumEntryIconForeNight),
		ForumEnterNumThreshold:  proto.Int64(static.ForumEnterNumThreshold),
	}
}

// 构建新热吧单数据
func (a *BuildOutput) buildHotForumAlbum(ctx context.Context, baseData *types.GetForumAlbumBaseData) {
	static := baseData.StaticField
	if len(static.HotForumAlbumList) == 0 {
		return
	}

	// 新热吧单列表按照插入楼层顺序排序
	sort.Slice(static.HotForumAlbumList, func(i, j int) bool {
		return static.HotForumAlbumList[i].GetInsertIndex() < static.HotForumAlbumList[j].GetInsertIndex()
	})

	// 构建新热吧单信息
	for _, album := range static.HotForumAlbumList {
		// 吧单基础信息
		resAlbumInfo := &getForumAlbum.ForumAlbumInfo{
			AlbumInfo: &getForumAlbum.ForumAlbumMetaInfo{
				Id:     album.Id,
				Name:   album.Name,
				Type:   proto.Int64(types.HotAlbumType), // 2表示新热吧单
				Tags:   album.Tags,
				HotNum: proto.Int64(static.AlbumShowInfoMap[album.GetId()]),
				Icon:   proto.String(static.HotForumAlbumIcon),
			},
		}
		// 收录吧数量
		forumMap := make(map[int64]bool)
		for _, tidSt := range album.GetThreadInfo() {
			forumMap[tidSt.GetForumId()] = true
		}
		resAlbumInfo.GetAlbumInfo().ForumNum = proto.Int64(int64(len(forumMap)))

		// 颜色配置
		resAlbumInfo.GetAlbumInfo().ThemeColor = &getForumAlbum.ThemeColorInfo{
			Day:   album.GetThemeColor().GetDay(),
			Night: album.GetThemeColor().GetNight(),
			Dark:  album.GetThemeColor().GetDark(),
		}

		// 构建外露帖子列表
		usedTidMap := make(map[int64]bool, 0)
		for index, tidSt := range album.GetThreadInfo() {
			if index >= 12 {
				// 每个新热吧单最多只取了12个帖子的详细信息
				break
			}
			usedTidMap[tidSt.GetThreadId()] = true
			threadInfo := a.buildThreadInfoAndForumInfo(ctx, tidSt.GetThreadId(), tidSt.GetForumId(), static)
			if threadInfo == nil {
				continue
			}
			resAlbumInfo.ThreadList = append(resAlbumInfo.ThreadList, threadInfo)
			if len(resAlbumInfo.ThreadList) == 6 {
				// 找到6个可见贴后就退出循环
				break
			}
		}

		if len(resAlbumInfo.ThreadList) < 6 {
			// 如果可见贴不足6个，就跳过这个新热吧单
			tbcontext.WarningF(ctx, "buildHotForumAlbum: album has less than 6 visible threads, album:[%s]", common.ToString(album))
			continue
		}
		// 构建帖子id列表
		for _, tidSt := range album.GetThreadInfo() {
			if !usedTidMap[tidSt.GetThreadId()] {
				resAlbumInfo.ThreadIdList = append(resAlbumInfo.ThreadIdList, &getForumAlbum.TidFidSt{
					Tid: tidSt.ThreadId,
					Fid: tidSt.ForumId,
				})
			}

			// 超过50个帖子截断
			if len(resAlbumInfo.ThreadIdList) > 50 {
				break
			}
		}
		static.ResHotForumAlbumList[album.GetId()] = resAlbumInfo
	}
}

// 将原始的贴信息和吧信息构建为吧单需要的结构
func (a *BuildOutput) buildThreadInfoAndForumInfo(ctx context.Context, tid, fid int64, static *types.GetForumAlbumStaticField) *getForumAlbum.ThreadInfo {
	// 如果没有原始的帖子信息，说明这个帖子被封禁了，就返回空
	threadMeta, ok := static.ThreadMetaInfoMap[tid]
	if !ok {
		tbcontext.WarningF(ctx, "buildThreadInfoAndForumInfo: thread is not visible, thread:[%d]", tid)
		return nil
	}
	threadInfo := new(getForumAlbum.ThreadInfo)
	threadInfo.ThreadId = proto.Int64(tid)
	threadInfo.Title = threadMeta.Title
	threadInfo.FirstFloorText = threadMeta.Abstract
	threadInfo.PostNum = proto.Int64(int64(threadMeta.GetPostNum()))
	if len(threadMeta.GetMedia()) > 0 {
		for _, media := range threadMeta.GetMedia() {
			if media.GetType() != "pic" {
				continue
			} else {
				url := util.GenPicURLForAlbum(media.GetBigPic())
				size := media.GetPicInfo().GetBig()
				threadInfo.ImgInfo = &getForumAlbum.ImgInfo{
					Url:   proto.String(url),
					Bsize: proto.String(fmt.Sprintf("%d,%d", size.GetWidth(), size.GetHeight())),
				}
				break
			}
		}
	}
	// 没拿到图片信息就不返回这个贴信息，避免前端显示问题
	if threadInfo.ImgInfo == nil {
		tbcontext.WarningF(ctx, "buildThreadInfoAndForumInfo: thread has no image info, thread:[%s]", common.ToString(threadMeta))
		return nil
	}
	// 构建吧信息
	forumRawInfo, ok := static.ForumMetaInfoMap[fid]
	if !ok {
		tbcontext.WarningF(ctx, "buildThreadInfoAndForumInfo: forum is not visible, fid:[%d]", fid)
		return nil
	}
	forumMetaInfo := &getForumAlbum.ForumMetaInfo{
		Id:           proto.Int64(fid),
		Name:         proto.String(forumRawInfo.GetForumName().GetForumName()),
		Avatar:       proto.String(forumRawInfo.GetCard().GetAvatar()),
		MemberCount:  proto.Int64(int64(forumRawInfo.GetStatistics().GetMemberCount())),
		HotNum:       proto.Int64(static.ForumHotValueMap[fid]),
		ThreadNum:    proto.Int64(int64(forumRawInfo.GetStatistics().GetThreadNum())),
		LastVisitNum: proto.Int64(static.ForumEnterNumMap[fid]),
	}
	// 如果该吧是某个垂类的热度top5，就下发该吧的垂类
	if verticalField, ok := static.ForumIsTop5Map[fid]; ok {
		forumMetaInfo.VerticalField = proto.String(verticalField)
	}
	threadInfo.ForumInfo = forumMetaInfo

	return threadInfo
}

// 合并推荐吧单和新热吧单
func (a *BuildOutput) mergeForumAlbum(ctx context.Context, outData *getForumAlbum.GetForumAlbumResData, baseData *types.GetForumAlbumBaseData) {
	static := baseData.StaticField
	// 如果没有可以展示的新热吧单，直接将推荐吧单放入结果集中
	if len(static.ResHotForumAlbumList) == 0 {
		outData.ForumAlbum = static.ResRecomForumAlbumList
		return
	}
	// 1 推荐吧单放入结果集
	resAlbumList := static.ResRecomForumAlbumList

	// 2 插入新热吧单
	hotAlbumNum := 0
	for _, hotAlbum := range static.HotForumAlbumList {
		resHotForumInfo, ok := static.ResHotForumAlbumList[hotAlbum.GetId()]
		if !ok {
			// 如果ResHotForumAlbumList没有这个新热吧单，说明这个新热吧单因为可见贴数量问题被过滤了，跳过
			continue
		}
		if hotAlbumNum == 2 {
			// 加一次校验，保证每次下发两个运营吧单
			break
		}

		// 如果用户没有垂类，新热吧单没有垂类，用户垂类和新热吧单匹配，就按照新热吧单配置的插入位置进行插入
		if len(static.UserVerticalField) == 0 || hotAlbum.GetVerticalFields() == "" ||
			php2go.InArrayString(hotAlbum.GetVerticalFields(), static.UserVerticalField) {
			position := int(hotAlbum.GetInsertIndex())
			if position > len(resAlbumList) {
				// 如果插入位置大于结果集长度，就直接插到最后一个
				position = len(resAlbumList) + 1
			}
			resAlbumList = append(resAlbumList[:position-1], append([]*getForumAlbum.ForumAlbumInfo{resHotForumInfo}, resAlbumList[position-1:]...)...)
		} else {
			// 如果用户垂类和新热吧单垂类不匹配，新热吧单直接插到最后一个
			resAlbumList = append(resAlbumList, resHotForumInfo)
		}
		hotAlbumNum += 1
	}

	// 3 保证结果集中没有两个相邻的新热吧单
	if hotAlbumNum == 2 {
		for i := 0; i < len(resAlbumList)-1; i++ {
			if resAlbumList[i].GetAlbumInfo().GetType() == types.HotAlbumType &&
				resAlbumList[i].GetAlbumInfo().GetType() == resAlbumList[i+1].GetAlbumInfo().GetType() {
				// 如果两个相邻的是新热吧单就调整一下
				if i+1 < len(resAlbumList)-1 {
					// 如果第二个新热吧单后面还有吧单，就将它和后面的进行交换
					resAlbumList[i+1], resAlbumList[i+2] = resAlbumList[i+2], resAlbumList[i+1]
				} else if i > 0 {
					// 如果第一个新热吧单前面还有吧单，就将它后前面的进行交换
					resAlbumList[i-1], resAlbumList[i] = resAlbumList[i], resAlbumList[i-1]
				}
			}
		}
	}

	outData.ForumAlbum = resAlbumList
	return
}
