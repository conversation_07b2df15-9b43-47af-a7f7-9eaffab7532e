package core

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/threadtype"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbum"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 获取帖子信息

type GetThreadInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_album_get_thread_info", func() engine.Job {
		return &GetThreadInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewGetThreadInfo(ctx *engine.Context) *GetThreadInfo {
	return &GetThreadInfo{
		ctx: ctx,
	}
}
func (a *GetThreadInfo) IsValid(ctx context.Context, baseData *types.GetForumAlbumBaseData) bool {
	return true
}
func (a *GetThreadInfo) Execute(ctx context.Context, outData *getForumAlbum.GetForumAlbumResData, baseData *types.GetForumAlbumBaseData) error {
	static := baseData.StaticField
	if len(static.TidInfList) == 0 {
		return nil
	}

	// 调用mgetThread获取帖子信息，并将可见的帖子信息存入static.ThreadMetaInfoMap
	err := a.getVisibleThread(ctx, static.TidInfList, static)
	if err != nil {
		return err
	}

	// 简化版每个吧单一次性传入了多个帖子，可见贴小于4个的概率很小，不用再额外取一次

	// // 检查是不是每个帖子都成功获取到了并且没有被mask,如果有遗漏的，使用该tid的下一个tid去获取帖子信息
	// nextTIDList := []interface{}{}
	// for _, tid := range static.ThreadIDList {
	// 	if _, ok := static.ThreadMetaInfoMap[tid]; !ok {
	// 		if nextTID, ok := static.NextTheadIDMap[tid]; ok {
	// 			nextTIDList = append(nextTIDList, nextTID)
	// 		}
	// 	}
	// }
	// if len(nextTIDList) == 0 {
	// 	return nil
	// }

	// // 使用推荐吧单中的第二个tid再去拉一次帖子信息
	// tidInfList := php2go.ArrayChunk(nextTIDList, 30)
	// err = a.getVisibleThread(ctx, tidInfList, static)
	// if err != nil {
	// 	return err
	// }

	return nil
}

// 根据传入的tid获取可见贴的帖子基础信息，放入static.ThreadMetaInfoMap中
func (a *GetThreadInfo) getVisibleThread(ctx context.Context, tidInfList [][]interface{}, static *types.GetForumAlbumStaticField) error {
	multi := tbservice.Multi()
	for index, tids := range tidInfList {
		threadInput := map[string]interface{}{
			"thread_ids":     tids,
			"forum_id":       0,
			"need_abstract":  1,
			"need_photo_pic": 1,
			"call_from":      "client_frs",
			"icon_size":      1,
		}
		threadParmas := &tbservice.Parameter{
			Service: "post",
			Method:  "mgetThread",
			Input:   threadInput,
			Output:  &frs.MgetThreadRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, fmt.Sprintf("mgetThread_%v", index), threadParmas)
		maskInput := map[string]interface{}{
			"thread_ids": tids,
		}
		maskParmas := &tbservice.Parameter{
			Service: "post",
			Method:  "getThreadMaskInfo",
			Input:   maskInput,
			Output:  &frs.GetThreadMaskInfoRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, fmt.Sprintf("getThreadMaskInfo_%v", index), maskParmas)
	}
	multi.Call(ctx)
	maskedThreadMap := make(map[uint64]bool) // 记录被mask的帖子
	// 处理帖子mask信息
	for index, tids := range tidInfList {
		resInf, err := multi.GetResult(ctx, fmt.Sprintf("getThreadMaskInfo_%v", index))
		if err != nil {
			tbcontext.WarningF(ctx, "call getThreadMaskInfo fail, tids=[%v], err=[%v]", tids, err)
			return errno.ErrCallServiceFail
		}
		res, ok := resInf.(*frs.GetThreadMaskInfoRes)
		if !ok || res == nil || res.Errno == nil || res.GetErrno() != errno.CodeSuccess {
			tbcontext.WarningF(ctx, "call getThreadMaskInfo fail, tids=[%v], output=[%v]", tids, resInf)
			return errno.ErrCallServiceFail
		}

		for _, threadInfo := range res.GetOutput().GetThreadInfo() {
			if threadInfo.GetIsDeleted() == 1 || threadInfo.GetIsFrsMask() == 1 || threadInfo.GetIsPartialVisible() == 1 {
				tbcontext.WarningF(ctx, "thread is masked, tid=[%v]", threadInfo.GetThreadId())
				maskedThreadMap[threadInfo.GetThreadId()] = true
			}
		}
	}

	for index, tids := range tidInfList {
		resInf, err := multi.GetResult(ctx, fmt.Sprintf("mgetThread_%v", index))
		if err != nil {
			tbcontext.WarningF(ctx, "call mgetThread fail, tids=[%v], err=[%v]", tids, err)
			return errno.ErrCallServiceFail
		}
		res, ok := resInf.(*frs.MgetThreadRes)
		if !ok || res == nil || res.Errno == nil || res.GetErrno() != errno.CodeSuccess {
			tbcontext.WarningF(ctx, "call mgetThread fail, tids=[%v], output=[%v]", tids, resInf)
			return errno.ErrCallServiceFail
		}

		resThreadList := res.GetOutput().GetThreadList()

		for tid, thread := range resThreadList {
			if maskedThreadMap[tid] {
				continue
			}
			// 鸿蒙版贴子过滤
			harmonyFilterOption := threadtype.HarmonyFilterOption{
				ThreadTypes: thread.GetThreadTypes(),
			}
			if static.IntClientType == stcdefine.CLIENT_TYPE_HARMONY && !threadtype.HarmonyFilter(harmonyFilterOption) {
				continue
			}
			intTID := common.Tvttt(tid, common.TTT_INT64).(int64)
			static.ThreadMetaInfoMap[intTID] = thread
		}
	}
	return nil
}

type GetThreadInfoOperator struct {
}

func (rdop *GetThreadInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *getForumAlbum.GetForumAlbumResData
	var baseData *types.GetForumAlbumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewGetThreadInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum_album_get_thread_info execute fail: %v", err)
		return err
	}

	return nil
}
