package core

import (
	"context"
	"strings"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbum"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 获取吧单所需的基础信息，包括吧单曝光量、吧基础信息、用户是否关注、吧单展开时引导文案、用户所属垂类
type WordlistInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_album_album_wordlist", func() engine.Job {
		return &WordlistInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewWordlistInfo(ctx *engine.Context) *WordlistInfo {
	return &WordlistInfo{
		ctx: ctx,
	}
}
func (a *WordlistInfo) IsValid(ctx context.Context, baseData *types.GetForumAlbumBaseData) bool {
	return true
}
func (a *WordlistInfo) Execute(ctx context.Context, outData *getForumAlbum.GetForumAlbumResData, baseData *types.GetForumAlbumBaseData) error {

	// 获取词表配置
	err := a.getWordListConf(ctx, baseData)
	if err != nil {
		tbcontext.WarningF(ctx, "getWordListConf err: %v", err)
		return err
	}

	return nil
}

type WordlistInfoOperator struct {
}

func (rdop *WordlistInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *getForumAlbum.GetForumAlbumResData
	var baseData *types.GetForumAlbumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewWordlistInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum_album_album_meta_info execute fail: %v", err)
		return err
	}

	return nil
}

// 从词表获取配置信息
func (a *WordlistInfo) getWordListConf(ctx context.Context, baseData *types.GetForumAlbumBaseData) error {
	keys := []string{
		"expand_forum_album_guid_word_have_more",    // 展开吧单的引导语（还有更多吧时）
		"expand_forum_album_guid_word_no_more",      // 展开吧单的引导语（没有更多吧时）
		"recommend_forum_album_icon",                // 推荐兴趣吧单的图标
		"hot_forum_album_icon",                      // 新热吧单的图标
		"forum_enter_num_threshold",                 // 进吧数量阈值，如果大于此阈值就展示
		"forum_entry_icon_fore_day",                 // 进吧运营位图片，前面大图(明)
		"forum_entry_icon_fore_night",               // 进吧运营位图片，前面大图(暗)
		"recommend_forum_album_style_exp",           // 推荐吧样式实验
		"recommend_forum_album_style_exp_whitelist", // 推荐吧样式白名单
	}

	values, err := wordserver.QueryKeys(ctx, types.ForumAlbumConfTableName, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], err=[%v]", types.ForumAlbumConfTableName, keys, err)
		return errno.ErrCallServiceFail
	}

	// 如果数量不对，也认为失败返回错误
	if len(values) != len(keys) {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], values=[%v]", types.ForumAlbumConfTableName, keys, values)
		return errno.ErrCallServiceFail
	}

	static := baseData.StaticField
	static.ExpandGuideWordHasMore = values[0]
	static.ExpandGuideWordNoMore = values[1]
	static.RecommendForumAlbumIcon = values[2]
	static.HotForumAlbumIcon = values[3]
	static.ForumEnterNumThreshold = common.Tvttt(values[4], common.TTT_INT64).(int64)
	static.ForumEntryIconForeDay = values[5]
	static.ForumEntryIconForeNight = values[6]
	static.RecommendForumAlbumStyleExp = values[7]
	static.RecommendFourmExpWhiteList = strings.Split(values[8], ",")
	return nil
}
