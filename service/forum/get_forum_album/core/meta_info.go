package core

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	commonproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	forumproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbum"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 获取吧单所需的基础信息，包括吧单曝光量、吧基础信息、用户是否关注、吧单展开时引导文案、用户所属垂类
type ForumAlbumMetaInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_album_album_meta_info", func() engine.Job {
		return &ForumAlbumMetaInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewForumAlbumMetaInfo(ctx *engine.Context) *ForumAlbumMetaInfo {
	return &ForumAlbumMetaInfo{
		ctx: ctx,
	}
}
func (a *ForumAlbumMetaInfo) IsValid(ctx context.Context, baseData *types.GetForumAlbumBaseData) bool {
	return true
}
func (a *ForumAlbumMetaInfo) Execute(ctx context.Context, outData *getForumAlbum.GetForumAlbumResData, baseData *types.GetForumAlbumBaseData) error {
	eg := new(gtask.Group)
	// 获取吧信息
	eg.Go(func() error {
		return a.mgetForumBtxInfo(ctx, baseData)
	})

	// 简化版不需要用户是否关注吧信息
	// 获取用户关注状态
	// eg.Go(func() error {
	// 	return a.mgetUserLevel(ctx, baseData)
	// })

	// 简化版不需要用户垂类信息
	// 获取用户所属垂类
	eg.Go(func() error {
		return a.getUserVerticalField(ctx, baseData)
	})

	// 获取吧单曝光量
	eg.Go(func() error {
		return a.mgetForumAlbumShowInfo(ctx, baseData)
	})

	// 结果等待
	_, err := eg.Wait()

	if err != nil {
		return err
	}

	return nil
}

type ForumAlbumMetaInfoOperator struct {
}

func (rdop *ForumAlbumMetaInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *getForumAlbum.GetForumAlbumResData
	var baseData *types.GetForumAlbumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewForumAlbumMetaInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum_album_album_meta_info execute fail: %v", err)
		return err
	}

	return nil
}

// 调用mgetForumBtxInfo获取吧信息
func (a *ForumAlbumMetaInfo) mgetForumBtxInfo(ctx context.Context, baseData *types.GetForumAlbumBaseData) error {
	static := baseData.StaticField
	// 如果没有fid，则直接返回
	if len(static.FidInfList) == 0 {
		return nil
	}
	multi := tbservice.Multi()

	for index, fourmIds := range static.FidInfList {
		tempParmas := &tbservice.Parameter{
			Service: "forum",
			Method:  "mgetBtxInfoEx",
			Input:   map[string]any{"forum_id": fourmIds},
			Output:  &forumproto.MgetBtxInfoExRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, fmt.Sprintf("mgetBtxInfoEx_%v", index), tempParmas)
	}

	multi.Call(ctx)
	for index, fourmIds := range static.FidInfList {
		resInf, err := multi.GetResult(ctx, fmt.Sprintf("mgetBtxInfoEx_%v", index))
		if err != nil {
			tbcontext.WarningF(ctx, "call mgetBtxInfoEx fail, fids=[%v], err=[%v]", fourmIds, err)
			return errno.ErrCallServiceFail
		}
		res, ok := resInf.(*forumproto.MgetBtxInfoExRes)
		if !ok || res == nil || res.Errno == nil || res.GetErrno() != errno.CodeSuccess {
			tbcontext.WarningF(ctx, "call mgetBtxInfoEx fail, fids=[%v], output=[%v]", fourmIds, resInf)
			return errno.ErrCallServiceFail
		}

		for fid, forumInfo := range res.GetOutput() {
			intFid := common.Tvttt(fid, common.TTT_INT64).(int64)
			static.ForumMetaInfoMap[intFid] = forumInfo
		}
	}
	return nil
}

// 调用mgetUserLevel获取用户关注吧状态
func (a *ForumAlbumMetaInfo) mgetUserLevel(ctx context.Context, baseData *types.GetForumAlbumBaseData) error {
	static := baseData.StaticField
	// 如果没有fid，或者用户未登录则直接返回
	if len(static.FidInfList) == 0 || static.UserID == 0 {
		return nil
	}
	multi := tbservice.Multi()

	for index, fourmIds := range static.FidInfList {

		tempParmas := &tbservice.Parameter{
			Service: "perm",
			Method:  "mgetUserLevel",
			Input: map[string]interface{}{
				"user_id":   static.UserID,
				"forum_ids": fourmIds,
			},
			Output: &perm.MgetUserLevelRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, fmt.Sprintf("mgetUserLevel_%v", index), tempParmas)
	}

	multi.Call(ctx)
	for index, fourmIds := range static.FidInfList {
		resInf, err := multi.GetResult(ctx, fmt.Sprintf("mgetUserLevel_%v", index))
		if err != nil {
			tbcontext.WarningF(ctx, "call mgetUserLevel fail, fids=[%v], uid=[%v], err=[%v]", fourmIds, static.UserID, err)
			return errno.ErrCallServiceFail
		}
		res, ok := resInf.(*perm.MgetUserLevelRes)
		if !ok || res == nil || res.Errno == nil || res.GetErrno() != errno.CodeSuccess {
			tbcontext.WarningF(ctx, "call mgetUserLevel fail, fids=[%v], uid=[%v], output=[%v]", fourmIds, static.UserID, resInf)
			return errno.ErrCallServiceFail
		}

		for _, userForumItem := range res.GetScoreInfo() {
			fid := common.Tvttt(userForumItem.GetForumId(), common.TTT_INT64).(int64)
			static.UserLikeForumMap[fid] = userForumItem.GetIsLike()
		}
	}
	return nil
}

// 获取用户所属垂类
func (a *ForumAlbumMetaInfo) getUserVerticalField(ctx context.Context, baseData *types.GetForumAlbumBaseData) error {
	static := baseData.StaticField
	// 用户未登录则直接返回
	if static.UserID == 0 {
		return nil
	}

	input := map[string]any{
		"user_id": []uint64{static.UserID},
	}
	output := &commonproto.GetUserVerticalClassRes{}

	err := tbservice.Call(ctx, "forumenter", "getUserVerticalClass", input, &output, []tbservice.Option{tbservice.WithServiceName("common"),
		tbservice.WithConverter(tbservice.JSONITER)}...)
	if err != nil || output.Errno == nil || output.GetErrno() != errno.CodeSuccess {
		tbcontext.WarningF(ctx, "call forumenter:getUserVerticalClass fail, err:[%v],intput:[%s], output:[%s]", err, common.ToString(input), common.ToString(output))
		// 用户垂类数据获取失败，当做没有垂类信息，不阻塞整体流程
		tbcontext.SetDealSuccess(ctx, false)
		return nil
	}

	static.UserVerticalField = output.GetData()[int64(static.UserID)].GetClasses()
	return nil
}

// 获取吧单曝光量信息
func (a *ForumAlbumMetaInfo) mgetForumAlbumShowInfo(ctx context.Context, baseData *types.GetForumAlbumBaseData) error {
	static := baseData.StaticField
	// 吧单id列表为空直接返回
	if len(static.ForumAlbumIDList) == 0 {
		return nil
	}

	input := map[string]any{
		"album_ids": static.ForumAlbumIDList,
	}

	output := &commonproto.MgetForumAlbumShowInfoRes{}
	err := tbservice.Call(ctx, "forumenter", "mgetForumAlbumShowInfo", input, &output, []tbservice.Option{tbservice.WithServiceName("common"),
		tbservice.WithConverter(tbservice.JSONITER)}...)
	if err != nil || output.Errno == nil || output.GetErrno() != errno.CodeSuccess {
		tbcontext.WarningF(ctx, "call forumenter:mgetForumAlbumShowInfo fail, err:[%v],intput:[%s], output:[%s]",
			err, common.ToString(input), common.ToString(output))
		return errno.ErrCallServiceFail
	}

	for id, showInfo := range output.GetData() {
		static.AlbumShowInfoMap[id] = showInfo.GetShowNum()
	}
	return nil
}
