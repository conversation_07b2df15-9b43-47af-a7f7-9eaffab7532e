package core

import (
	"context"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	commonproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbum"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 新热吧单信息获取

type GetHotForumAlbum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_album_get_hot_forum_album", func() engine.Job {
		return &GetHotForumAlbumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewGetHotForumAlbum(ctx *engine.Context) *GetHotForumAlbum {
	return &GetHotForumAlbum{
		ctx: ctx,
	}
}
func (a *GetHotForumAlbum) IsValid(ctx context.Context, baseData *types.GetForumAlbumBaseData) bool {
	return true
}
func (a *GetHotForumAlbum) Execute(ctx context.Context, outData *getForumAlbum.GetForumAlbumResData, baseData *types.GetForumAlbumBaseData) error {
	static := baseData.StaticField
	// 调用mgetHotForumAlbumInfo查询新热吧单
	pn := baseData.StaticField.Pn
	input := map[string]any{
		"pn": pn,
	}
	// 如果是新用户，拉取仅新用户可见的新热吧单
	if baseData.Request.GetIsNewUser() == 1 && clientvers.Compare("12.68.0", static.StrClientVersion) >= 0 {
		input["is_new_user"] = 1
	}
	output := &commonproto.MgetHotForumAlbumInfoRes{}
	err := tbservice.Call(ctx, "forumenter", "mgetHotForumAlbumInfo", input, &output, []tbservice.Option{tbservice.WithServiceName("common"),
		tbservice.WithConverter(tbservice.JSONITER)}...)
	if err != nil || output.Errno == nil || output.GetErrno() != errno.CodeSuccess {
		tbcontext.WarningF(ctx, "call forumenter:mgetHotForumAlbumInfo fail, err:[%v], output:[%s]", err, common.ToString(output))
		return errno.ErrCallServiceFail
	}

	static.HotForumAlbumList = output.GetData().GetAlbumInfo()

	if len(static.HotForumAlbumList) == 0 {
		return nil
	}

	// 此处只记录吧单id，吧id和帖子id因为只需要取一部分，放在在prepare扩展处理
	hotForumAlbumIDList := make([]int64, 0)
	for _, hotForumAlbum := range static.HotForumAlbumList {
		if len(hotForumAlbum.ThreadInfo) < 6 {
			// 小于6个帖子，跳过
			tbcontext.WarningF(ctx, "this forum album has less than 6 thread, skip it. album_info=[%v]", hotForumAlbum)
			continue
		}
		hotForumAlbumIDList = append(hotForumAlbumIDList, hotForumAlbum.GetId())
	}

	static.DataLock.Lock()
	defer static.DataLock.Unlock()
	static.ForumAlbumIDList = append(static.ForumAlbumIDList, hotForumAlbumIDList...)

	return nil
}

type GetHotForumAlbumOperator struct {
}

func (rdop *GetHotForumAlbumOperator) DoImpl(ctx *engine.Context) error {
	var outData *getForumAlbum.GetForumAlbumResData
	var baseData *types.GetForumAlbumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewGetHotForumAlbum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum_album_get_hot_forum_album execute fail: %v", err)
		return err
	}

	return nil
}
