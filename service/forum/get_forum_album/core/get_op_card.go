package core

import (
	"context"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	commonproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbum"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 运营卡片信息获取

type GetOpCard struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_album_get_op_card", func() engine.Job {
		return &GetOpCardOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewGetOpCard(ctx *engine.Context) *GetOpCard {
	return &GetOpCard{
		ctx: ctx,
	}
}
func (a *GetOpCard) IsValid(ctx context.Context, baseData *types.GetForumAlbumBaseData) bool {
	return true
}
func (a *GetOpCard) Execute(ctx context.Context, outData *getForumAlbum.GetForumAlbumResData, baseData *types.GetForumAlbumBaseData) error {
	static := baseData.StaticField
	// 调用getForumAlbumCard查询运营卡片
	pn := baseData.StaticField.Pn
	input := map[string]any{
		"pn": pn,
	}
	output := &commonproto.GetForumAlbumCardRes{}
	err := tbservice.Call(ctx, "forumenter", "getForumAlbumCard", input, &output, []tbservice.Option{tbservice.WithServiceName("common"),
		tbservice.WithConverter(tbservice.JSONITER)}...)
	if err != nil || output.Errno == nil || output.GetErrno() != errno.CodeSuccess {
		tbcontext.WarningF(ctx, "call forumenter:getForumAlbumCard fail, err:[%v], output:[%s]", err, common.ToString(output))
		return errno.ErrCallServiceFail
	}
	static.OpCardMeta = output.GetData()
	if len(static.OpCardMeta) == 0 {
		return nil
	}

	// 将forumId记录下来，后续步骤去获取吧信息
	static.DataLock.Lock()
	defer static.DataLock.Unlock()
	for _, opCard := range static.OpCardMeta {
		static.ForumIDList = append(static.ForumIDList, opCard.GetForumId())
	}

	return nil
}

type GetOpCardOperator struct {
}

func (rdop *GetOpCardOperator) DoImpl(ctx *engine.Context) error {
	var outData *getForumAlbum.GetForumAlbumResData
	var baseData *types.GetForumAlbumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewGetOpCard(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum_album_get_op_card execute fail: %v", err)
		return err
	}

	return nil
}
