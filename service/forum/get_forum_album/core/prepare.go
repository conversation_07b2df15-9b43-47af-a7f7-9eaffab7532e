package core

import (
	"context"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbum"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 数据预处理以及检查推荐返回结果

type Prepare struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_album_prepare", func() engine.Job {
		return &PrepareOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPrepare(ctx *engine.Context) *Prepare {
	return &Prepare{
		ctx: ctx,
	}
}
func (a *Prepare) IsValid(ctx context.Context, baseData *types.GetForumAlbumBaseData) bool {
	return true
}
func (a *Prepare) Execute(ctx context.Context, outData *getForumAlbum.GetForumAlbumResData, baseData *types.GetForumAlbumBaseData) error {
	static := baseData.StaticField
	// 从推荐吧单获取需要查帖子信息的tid
	a.getRecomForumAlbumTid(static)

	// 从新热吧单删除推荐吧单已经出现过的tid
	a.delRepeatThreadInHotAlbum(ctx, static)

	// 从新热吧单获取需要查详细信息的tid和fid
	a.getHotForumAlbumFidTid(static)

	// 需要获取具体信息的吧id列表和帖子id列表去重
	a.deduplicationIDList(baseData)

	// 吧id 和 帖子id 分片，避免请求下游时超时或报错
	fidInterfaceList := make([]interface{}, 0, len(static.ForumIDList))
	tidInterfaceList := make([]interface{}, 0, len(static.ThreadIDList))
	for _, fid := range static.ForumIDList {
		fidInterfaceList = append(fidInterfaceList, fid)
	}
	for _, tid := range static.ThreadIDList {
		tidInterfaceList = append(tidInterfaceList, tid)
	}

	static.FidInfList = php2go.ArrayChunk(fidInterfaceList, 30)
	static.TidInfList = php2go.ArrayChunk(tidInterfaceList, 30)
	return nil
}

// 记录推荐吧单需要查帖子信息的tid
func (a *Prepare) getRecomForumAlbumTid(static *types.GetForumAlbumStaticField) {
	// 简单版优先外露前4个吧的首帖，首帖不可见用这个吧的第二个帖子替换，第二个帖子还不可见就丢弃（概率极低），其他帖子都只下发帖子id
	for _, albumInfo := range static.RecomForumAlbumList {
		albumThreadList := make([][]*getForumAlbum.TidFidSt, 0, len(albumInfo.ForumList))
		for forumIndex, forumInfo := range albumInfo.ForumList {
			tempTids := make([]*getForumAlbum.TidFidSt, 0, len(forumInfo.ThreadList))
			for threadIndex, threadInfo := range forumInfo.ThreadList {
				tempTids = append(tempTids, &getForumAlbum.TidFidSt{Tid: threadInfo.Tid, Fid: forumInfo.Fid})
				if forumIndex < static.ThreadNum && threadIndex == 0 {
					// 前x个吧的第一个和第二个帖子需要去查帖子信息
					static.ThreadIDList = append(static.ThreadIDList, threadInfo.GetTid())
					if nextTid, ok := static.NextTheadIDMap[threadInfo.GetTid()]; ok {
						static.ThreadIDList = append(static.ThreadIDList, nextTid)
						// 需要展示的帖子数大于返回的吧数时，可能需要把第三个帖子补位
						// 情景：推荐只返回4个吧，需要补充第一二个吧的第二个帖子，如果帖子不可见，使用第三个帖子兜底, 如果仍不可见，则丢弃
						needPaddingThreadNum := static.TopicNeedPaddingThreadNumMap[albumInfo.GetTopicId()]
						if needPaddingThreadNum > 0 && forumIndex < needPaddingThreadNum {
							if thirdTid, ok := static.NextTheadIDMap[nextTid]; ok {
								static.ThreadIDList = append(static.ThreadIDList, thirdTid)
							}
						}
					}
				}
			}
			albumThreadList = append(albumThreadList, tempTids)
		}

		// 记录当前这个吧单的所有帖子id，并且按照每个吧依次取一个的顺序排序
		static.AlbumID2TidsMap[albumInfo.GetTopicId()] = mergeArrays(albumThreadList)
	}
}

// 记录新热吧单需要查详细信息的tid和fid
func (a *Prepare) getHotForumAlbumFidTid(static *types.GetForumAlbumStaticField) {
	// 新热吧单需要下发6个帖子信息，所以这里查12个帖子信息，避免前6个有不可见的帖子
	for _, albumInfo := range static.HotForumAlbumList {
		threadIDList := make([]int64, 0, 12)
		forumIDList := make([]int64, 0, 12)
		for index, threadInfo := range albumInfo.GetThreadInfo() {
			if index >= 12 {
				break
			}
			threadIDList = append(threadIDList, threadInfo.GetThreadId())
			forumIDList = append(forumIDList, threadInfo.GetForumId())
		}
		static.ThreadIDList = append(static.ThreadIDList, threadIDList...)
		static.ForumIDList = append(static.ForumIDList, forumIDList...)
	}
}

// 吧id列表和帖子id列表去重
func (a *Prepare) deduplicationIDList(baseData *types.GetForumAlbumBaseData) {
	static := baseData.StaticField
	fidMap := make(map[int64]bool, len(static.ForumIDList))
	tidMap := make(map[int64]bool, len(static.ThreadIDList))
	for _, fid := range static.ForumIDList {
		fidMap[fid] = true
	}
	for _, tid := range static.ThreadIDList {
		tidMap[tid] = true
	}

	newFIDList := make([]int64, 0, len(fidMap))
	newTIDList := make([]int64, 0, len(tidMap))
	for key := range fidMap {
		newFIDList = append(newFIDList, key)
	}
	for key := range tidMap {
		newTIDList = append(newTIDList, key)
	}
	static.ForumIDList = newFIDList
	static.ThreadIDList = newTIDList
}

// 去除新热吧单中推荐吧单以包括的帖子
func (a *Prepare) delRepeatThreadInHotAlbum(ctx context.Context, static *types.GetForumAlbumStaticField) {
	tidMap := make(map[int64]bool, 0)
	for _, recomAlbum := range static.RecomForumAlbumList {
		for _, forumInfo := range recomAlbum.GetForumList() {
			for _, threadInfo := range forumInfo.GetThreadList() {
				tidMap[threadInfo.GetTid()] = true
			}
		}
	}

	for index, hotAlbum := range static.HotForumAlbumList {
		newThreadList := make([]*common.HotForumAlbumThreadInfo, 0)
		for _, threadInfo := range hotAlbum.GetThreadInfo() {
			if !tidMap[threadInfo.GetThreadId()] {
				newThreadList = append(newThreadList, threadInfo)
			} else {
				tbcontext.WarningF(ctx, "delRepeatThreadInHotAlbum: tid:%v", threadInfo)
			}
		}
		static.HotForumAlbumList[index].ThreadInfo = newThreadList
	}
}

type PrepareOperator struct {
}

func (rdop *PrepareOperator) DoImpl(ctx *engine.Context) error {
	var outData *getForumAlbum.GetForumAlbumResData
	var baseData *types.GetForumAlbumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewPrepare(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum_album_prepare execute fail: %v", err)
		return err
	}

	return nil
}

func mergeArrays(arrays [][]*getForumAlbum.TidFidSt) []*getForumAlbum.TidFidSt {
	n := len(arrays)
	result := []*getForumAlbum.TidFidSt{}

	// 找到所有数组中最大的长度
	maxLen := 0
	for _, arr := range arrays {
		if len(arr) > maxLen {
			maxLen = len(arr)
		}
	}

	// 按顺序从每个数组中取出一个元素，直到所有数组都取完
	for i := 0; i < maxLen; i++ {
		for j := 0; j < n; j++ {
			if i < len(arrays[j]) {
				result = append(result, arrays[j][i])
			}
		}
	}

	return result
}
