package core

import (
	"context"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/yuelao2"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbum"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 通过yuelaou获取推荐吧单信息
const RecomForumAlbumNum = 5        // 推荐吧单数量
const StyleSixThread = "six_thread" // 展示6个帖子的style
const minForumNum = 4               // 推荐返回的每个吧单中最少的吧数量

type GetYuelaou struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_album_get_yuelaou", func() engine.Job {
		return &GetYuelaouOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewGetYuelaou(ctx *engine.Context) *GetYuelaou {
	return &GetYuelaou{
		ctx: ctx,
	}
}
func (a *GetYuelaou) IsValid(ctx context.Context, baseData *types.GetForumAlbumBaseData) bool {
	return true
}
func (a *GetYuelaou) Execute(ctx context.Context, outData *getForumAlbum.GetForumAlbumResData, baseData *types.GetForumAlbumBaseData) error {
	// 入参
	static := baseData.StaticField
	static.TopicNeedPaddingThreadNumMap = make(map[int64]int, 0)
	userID := static.UserID
	clientVersion := static.StrClientVersion
	clientType := static.IntClientType
	sampleIds := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("sample_id", ""), common.TTT_STRING).(string)
	cuid := static.Cuid
	strHisToryAlbumID := baseData.Request.GetHistoryAlbumIds()
	rn := int64(RecomForumAlbumNum)
	// 如果前端传了rn，就是用前端传过来的值
	if baseData.Request.GetRn() > 0 {
		rn = baseData.Request.GetRn()
	}

	yuelaouInput := map[string]any{
		"user_id":        userID,
		"client_version": clientVersion,
		"net_type":       baseData.BaseObj.ObjRequest.GetCommonAttr("net_type", 0),
		"client_type":    clientType,
		"sample_ids":     sampleIds,
		"cuid":           cuid,
		"call_from":      "recomm_forum",
		"res_num":        rn,
		"ua_str":         baseData.BaseObj.ObjRequest.GetCommonAttr("ua_str", ""), // 屏幕宽_屏幕高_android/iphone_版本号_0
	}
	if len(strHisToryAlbumID) > 0 {
		yuelaouInput["shown_forum"] = strHisToryAlbumID
	}
	// 判断是否为首刷，入参is_first_req是1时，表示首刷
	if baseData.Request.GetIsFirstReq() == 1 {
		yuelaouInput["load_type"] = 1
	} else {
		yuelaouInput["load_type"] = 2
	}

	// 如果是新用户，根据是否有关注的吧决定是否要第一页出吧友必看
	if static.UserID > 0 && baseData.Request.GetIsNewUser() == 1 && baseData.Request.GetPn() == 1 && clientvers.Compare("12.68.0", static.StrClientVersion) >= 0 {
		input := map[string]interface{}{
			"user_id":     static.UserID,
			"check_forum": 1,
			"page_type":   1,
			"page_no":     1,
			"page_size":   1,
		}
		getLikeForumListRes := new(perm.GetLikeForumListRes)
		err := tbservice.Call(ctx, "perm", "getLikeForumList", input, getLikeForumListRes, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || getLikeForumListRes.Errno == nil || getLikeForumListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call perm::getLikeForumList fail, input:%v, output:%v, err: %v", input, getLikeForumListRes, err)
		} else if getLikeForumListRes.GetOutput().GetMemberCount() == 0 {
			// 新用户没有关注吧时，首刷时第一页出必看
			yuelaouInput["is_new_user"] = 1
			yuelaouInput["page_num"] = baseData.Request.GetPn()
		}
	}

	// 新用户未登录，第一页必出吧友必看
	if static.UserID == 0 && baseData.Request.GetIsNewUser() == 1 && baseData.Request.GetPn() == 1 && clientvers.Compare("12.68.0", static.StrClientVersion) >= 0 {
		yuelaouInput["is_new_user"] = 1
		yuelaouInput["page_num"] = baseData.Request.GetPn()
	}

	yuelaouOutput := &yuelao2.RecommForumTabRes{}
	err := tbservice.Call(ctx, "yuelaou2", "recommForumTab", yuelaouInput, yuelaouOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || yuelaouOutput.Errno == nil || yuelaouOutput.GetErrno() != errno.CodeSuccess {
		tbcontext.FatalF(ctx, "call yuelaou2:recommForumTab fail, err:[%v],intput:[%s], output:[%s]",
			err, common.ToString(yuelaouInput), common.ToString(yuelaouOutput))
		return errno.ErrCallServiceFail
	}

	// jsoniter.UnmarshalFromString(getMock(), &yuelaouOutput.Data)
	// 检查返回值
	if len(yuelaouOutput.Data) == 0 {
		// 理论上不会出现返回值为空的问题，兜个底，提前返回，防止后续出现空指针问题
		tbcontext.FatalF(ctx, "yuelaou2:recommForumTab return empty,intput:[%s], output:[%s]", common.ToString(yuelaouInput), common.ToString(yuelaouOutput))
		return errno.ErrCallServiceFail
	}
	static.RecomForumAlbumList = make([]*yuelao2.RecommForumAlbumInfo, 0, len(yuelaouOutput.Data))
	forumIDList := make([]int64, 0)
	// threadIDList := make([]int64, 0)
	topicIDList := make([]int64, 0)
	nextThreadIDMap := make(map[int64]int64)

	isHitNewStyle := a.IsHitNewStyle(ctx, sampleIds, baseData)
	static.ThreadNum = 4
	if isHitNewStyle {
		static.ThreadNum = 6
		static.ShowStyle = StyleSixThread
		tbcontext.WarningF(ctx, "hit new style, uid: %d", userID)
	}

	for _, recomForumAlbum := range yuelaouOutput.Data {
		var needPaddingThreadNum int = 0
		if len(recomForumAlbum.GetForumList()) < minForumNum {
			// 吧数量小于minForumNum个，跳过这个吧单
			tbcontext.WarningF(ctx, "this forum album has less than %d forum, skip it. recom_info=[%v]", static.ThreadNum, recomForumAlbum)
			continue
		}
		if len(recomForumAlbum.GetForumList()) < static.ThreadNum {
			needPaddingThreadNum = static.ThreadNum - len(recomForumAlbum.GetForumList())
		}
		for forumIndex, forumInfo := range recomForumAlbum.GetForumList() {
			forumIDList = append(forumIDList, forumInfo.GetFid()) // 记录吧id
			if forumIndex < static.ThreadNum {
				if len(forumInfo.GetThreadList()) > 1 {
					// 记录第二个帖子id，当第一个tid不可见时外露第二个tid（兜底）
					nextThreadIDMap[forumInfo.GetThreadList()[0].GetTid()] = forumInfo.GetThreadList()[1].GetTid()
				}
			}
			// 吧不足threadNum时，吧单需要补足threadNum个帖子
			if needPaddingThreadNum > 0 && forumIndex < needPaddingThreadNum {
				// 使用第一个吧->第二个吧的第二个->第三个帖子的优先级依次补充
				if len(forumInfo.GetThreadList()) > 2 {
					nextThreadIDMap[forumInfo.GetThreadList()[1].GetTid()] = forumInfo.GetThreadList()[2].GetTid()
				}
			}
		}

		static.TopicNeedPaddingThreadNumMap[recomForumAlbum.GetTopicId()] = needPaddingThreadNum
		static.RecomForumAlbumList = append(static.RecomForumAlbumList, recomForumAlbum)
		topicIDList = append(topicIDList, recomForumAlbum.GetTopicId())
	}

	static.DataLock.Lock()
	defer static.DataLock.Unlock()
	static.ForumIDList = append(static.ForumIDList, forumIDList...)
	static.ForumAlbumIDList = append(static.ForumAlbumIDList, topicIDList...)
	for tid, nextTID := range nextThreadIDMap {
		static.NextTheadIDMap[tid] = nextTID
	}

	return nil
}

// 是否命中实验
func (sa *GetYuelaou) IsHitNewStyle(ctx context.Context, sampleIdsStr string, baseData *types.GetForumAlbumBaseData) bool {
	expSid := baseData.StaticField.RecommendForumAlbumStyleExp
	// 全量
	if expSid == "all" {
		return true
	}

	sampleIDs := strings.Split(sampleIdsStr, "-")
	if len(sampleIDs) <= 0 {
		return false
	}

	// 小流量
	for _, sampleID := range sampleIDs {
		if sampleID == baseData.StaticField.RecommendForumAlbumStyleExp {
			return true
		}
	}

	// 白名单
	uid := baseData.StaticField.UserID
	for _, uidStr := range baseData.StaticField.RecommendFourmExpWhiteList {
		id, _ := strconv.ParseUint(uidStr, 10, 64)
		if uid == id {
			return true
		}
	}

	return false
}

type GetYuelaouOperator struct {
}

func (rdop *GetYuelaouOperator) DoImpl(ctx *engine.Context) error {
	var outData *getForumAlbum.GetForumAlbumResData
	var baseData *types.GetForumAlbumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewGetYuelaou(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum_album_get_yuelaou execute fail: %v", err)
		return err
	}

	return nil
}
