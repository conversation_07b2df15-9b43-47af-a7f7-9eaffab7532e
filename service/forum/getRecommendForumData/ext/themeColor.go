package ext

import (
	"context"
	"encoding/json"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/conf"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/utilconst"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientRecommendData "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getRecommendForumData"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ThemeColor struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_recommend_data_themecolor", func() engine.Job {
		return &ThemeColorOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewThemeColor(ctx *engine.Context) *ThemeColor {
	return &ThemeColor{
		ctx: ctx,
	}
}
func (a *ThemeColor) IsValid(ctx context.Context, baseData *types.CRecommendForumDataBaseData) bool {
	staticField := baseData.StaticField
	if len(staticField.ArrRecForumInfoList) == 0 {
		return false
	}

	return true
}
func (a *ThemeColor) Execute(ctx context.Context, outData *clientRecommendData.GetRecommendForumDataRes, baseData *types.CRecommendForumDataBaseData) error {
	staticField := baseData.StaticField

	for _, arrForum := range staticField.ArrRecForumInfoList {
		if arrForum.GetForumName() == "" {
			continue
		}

		forumId := common.Tvttt(arrForum.GetForumId(), common.TTT_INT64).(int64)
		btx := staticField.BtxForumIdx[forumId]

		//10.0返回吧主题色
		//如果没有返回默认色
		themeColor := &client.ThemeColorInfo{
			Day: &client.ThemeElement{
				CommonColor: proto.String(utilconst.DAY_THEME_COLOR_DEFAULT),
				DarkColor:   proto.String(utilconst.DAY_DARK_COLOR_DEFAULT),
				LightColor:  proto.String(utilconst.DAY_LIGHT_COLOR_DEFAULT),
			},
			Night: &client.ThemeElement{
				CommonColor: proto.String(utilconst.NIGHT_THEME_COLOR_DEFAULT),
				DarkColor:   proto.String(utilconst.NIGHT_DARK_COLOR_DEFAULT),
				LightColor:  proto.String(utilconst.NIGHT_LIGHT_COLOR_DEFAULT),
			},
		}

		themeColorStr := btx.GetAttrs().GetThemeColor()
		if themeColorStr != "" {
			// 因主题色返回格式改变，主要为了兼容第一版
			themeColorAttr := new(client.ThemeColorInfo)
			err := json.Unmarshal([]byte(themeColorStr), themeColorAttr)
			if err != nil {
				tbcontext.WarningF(ctx, "change to ThemeColorInfo fail: %v", err)
			}

			if themeColorAttr.GetDay() != nil && themeColorAttr.GetNight() != nil {
				themeColor = themeColorAttr
			}
		}

		clientStr := "android"
		if staticField.ClientType == clientvers.CLIENT_TYPE_IPHONE {
			clientStr = "ios"
		}

		firstClass := btx.GetDir().GetLevel_1Name()
		var pattern []string
		switch firstClass {
		case utilconst.DIR_CATEGORY_YULE, utilconst.DIR_CATEGORY_ZONGYI, utilconst.DIR_CATEGORY_ZHUIJU, utilconst.DIR_CATEGORY_MOVIE, utilconst.DIR_CATEGORY_FUN:
			pattern = conf.ArrPatternImage[clientStr]["entertainment"]
		case utilconst.DIR_CATEGORY_SPORT:
			pattern = conf.ArrPatternImage[clientStr]["sport"]
		case utilconst.DIR_CATEGORY_NOVEL, utilconst.DIR_CATEGORY_AREA, utilconst.DIR_CATEGORY_NATURE:
			pattern = conf.ArrPatternImage[clientStr]["culture"]
		case utilconst.DIR_CATEGORY_LIFE:
			pattern = conf.ArrPatternImage[clientStr]["life"]
		case utilconst.DIR_CATEGORY_GAME, utilconst.DIR_CATEGORY_CARTOON, utilconst.DIR_CATEGORY_CAMPUS:
			pattern = conf.ArrPatternImage[clientStr]["young"]
		default:
			pattern = conf.ArrPatternImage[clientStr]["default"]
		}

		index := int(forumId) % len(pattern)
		themeColor.GetDay().PatternImage = proto.String(pattern[index])
		themeColor.GetNight().PatternImage = proto.String(pattern[index])

		arrForum.ThemeColor = themeColor
		arrForum.NeedTrans = proto.Bool(true)
	}

	return nil
}

type ThemeColorOperator struct {
}

func (rdop *ThemeColorOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientRecommendData.GetRecommendForumDataRes
	var baseData *types.CRecommendForumDataBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewThemeColor(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "ThemeColor execute fail: %v", err)
		return err
	}

	return nil
}
