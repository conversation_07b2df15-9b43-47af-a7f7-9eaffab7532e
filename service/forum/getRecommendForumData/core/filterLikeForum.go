package core

import (
	"context"
	"errors"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientRecommendData "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getRecommendForumData"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type FilterLikeForum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_recommend_data_filterlikeforum", func() engine.Job {
		return &FilterLikeForumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewFilterLikeForum(ctx *engine.Context) *FilterLikeForum {
	return &FilterLikeForum{
		ctx: ctx,
	}
}
func (a *FilterLikeForum) IsValid(ctx context.Context, baseData *types.CRecommendForumDataBaseData) bool {
	staticField := baseData.StaticField
	if len(staticField.ArrRecForumIds) == 0 {
		return false
	}

	return true
}
func (a *FilterLikeForum) Execute(ctx context.Context, outData *clientRecommendData.GetRecommendForumDataRes, baseData *types.CRecommendForumDataBaseData) error {
	staticField := baseData.StaticField
	if !staticField.Login || staticField.UserId == 0 {
		return nil
	}
	input := map[string]interface{}{
		"user_id":   staticField.UserId,
		"forum_ids": staticField.ArrRecForumIds,
	}
	permRes := new(perm.MgetUserLevelRes)
	err := tbservice.Call(ctx, "perm", "mgetUserLevel", input, permRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || permRes.Errno == nil {
		tbcontext.FatalF(ctx, "call perm::mgetUserLevel fail, input:%v, output:%v, err: %v", input, permRes, err)
		baseData.BaseObj.Error(int(permRes.GetErrno()), tiebaerror.GetErrMsg(int(permRes.GetErrno())), true)
		return errors.New("")
	}

	if permRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call perm::mgetUserLevel fail, input:%v, output:%v, err: %v", input, permRes, err)
		return nil
	}

	arrLikeForumId := make(map[uint32]bool)
	if len(permRes.GetScoreInfo()) > 0 {
		for _, val := range permRes.GetScoreInfo() {
			if val.GetForumId() > 0 && val.GetIsLike() == 1 {
				arrLikeForumId[val.GetForumId()] = true
			}
		}
	}

	newRecForumIds := make([]uint32, 0)
	for _, recFid :=  range staticField.ArrRecForumIds {
		if _, ok := arrLikeForumId[recFid]; !ok {
			newRecForumIds = append(newRecForumIds, recFid)
		} else {
			delete(staticField.ArrRecTids, recFid)
		}
	}

	staticField.ArrRecForumIds = newRecForumIds

	return nil
}

type FilterLikeForumOperator struct {
}

func (rdop *FilterLikeForumOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientRecommendData.GetRecommendForumDataRes
	var baseData *types.CRecommendForumDataBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewFilterLikeForum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "FilterLikeForum execute fail: %v", err)
		return err
	}

	return nil
}
