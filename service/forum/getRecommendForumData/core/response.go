package core

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/stlog"
	clientRecommendData "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getRecommendForumData"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type RecommendDataResp struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_recommend_data_resp", func() engine.Job {
		return &RecommendDataRespOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewRecommendDataResp(ctx *engine.Context) *RecommendDataResp {
	return &RecommendDataResp{
		ctx: ctx,
	}
}
func (a *RecommendDataResp) IsValid(ctx context.Context, baseData *types.CRecommendForumDataBaseData) bool {

	return true
}
func (a *RecommendDataResp) Execute(ctx context.Context, outData *clientRecommendData.GetRecommendForumDataRes, baseData *types.CRecommendForumDataBaseData) error {
	staticField := baseData.StaticField

	newForumList := make([]*clientRecommendData.RecForumInfo, 0)
	for _ ,val := range staticField.ArrRecForumInfoList {
		forumId := common.Tvttt(val.GetForumId(), common.TTT_UINT32).(uint32)
		if _, ok := staticField.ThreadList[forumId]; ok {
			val.Threadlist = make([]*clientRecommendData.ThreadList, 0)
			for _, thread := range staticField.ThreadList[forumId] {
				val.Threadlist = append(val.Threadlist, thread)
			}
			newForumList = append(newForumList, val)
		}
	}

	outData.RecommendForumInfo = make([]*clientRecommendData.RecForumInfo, 0)
	for _, v := range staticField.ArrRecForumInfoList {
		if len(v.GetThreadlist()) > 0 {
			outData.RecommendForumInfo = append(outData.RecommendForumInfo, v)
		}
	}
	stlog.AddLog(ctx, "data_cnt", len(outData.RecommendForumInfo))
	stlog.AddLog(ctx, "source_type", staticField.SourceType)

	return nil
}

type RecommendDataRespOperator struct {
}

func (rdop *RecommendDataRespOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientRecommendData.GetRecommendForumDataRes
	var baseData *types.CRecommendForumDataBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewRecommendDataResp(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "RecommendDataResp execute fail: %v", err)
		return err
	}

	return nil
}
