package core

import (
	"context"
	"html"
	"strings"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/pb"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/hiphoto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientRecommendData "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getRecommendForumData"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ThreadList struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_recommend_data_threadlist", func() engine.Job {
		return &ThreadListOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewThreadList(ctx *engine.Context) *ThreadList {
	return &ThreadList{
		ctx: ctx,
	}
}
func (a *ThreadList) IsValid(ctx context.Context, baseData *types.CRecommendForumDataBaseData) bool {
	staticField := baseData.StaticField
	if len(staticField.ArrRecTids) == 0 {
		return false
	}

	return true
}
func (a *ThreadList) Execute(ctx context.Context, outData *clientRecommendData.GetRecommendForumDataRes, baseData *types.CRecommendForumDataBaseData) error {
	staticField := baseData.StaticField

	//获取帖子数据前，按帖子数过滤一次帖子
	deleteKey := make([]uint32, 0)
	for key, tids := range staticField.ArrRecTids {
		if len(tids) < types.DISPLAY_MIN_THREAD_NUM {
			deleteKey = append(deleteKey, key)
		}
	}

	for _, key := range deleteKey {
		delete(staticField.ArrRecTids, key)
	}

	multi := tbservice.Multi()
	for fid, tids := range staticField.ArrRecTids {

		fidStr := common.Tvttt(fid, common.TTT_STRING).(string)

		arrParam := &tbservice.Parameter{
			Service: "post",
			Method:  "mgetThread",
			Input: map[string]interface{}{
				"thread_ids":      tids,
				"need_abstract":   1,
				"forum_id":        fid,
				"need_photo_pic":  0,
				"need_user_data":  0,
				"icon_size":       0,
				"need_forum_name": 0,            //是否获取吧名
				"call_from":       "client_frs", //上游模块名
			},
			Output: &frs.MgetThreadRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}

		multi.Register(ctx, "mgetThread"+fidStr, arrParam)

		arrParam = &tbservice.Parameter{
			Service: "post",
			Method:  "getMaskInfo",
			Input: map[string]interface{}{
				"input": map[string]interface{}{
					"thread_ids": tids,
				},
			},
			Output: &pb.GetMaskInfoRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}

		multi.Register(ctx, "getMaskInfo"+fidStr, arrParam)
	}

	multi.Call(ctx)

	threadList := make(map[uint32]map[int64]*clientRecommendData.ThreadList)
	for fid := range staticField.ArrRecTids {
		fidStr := common.Tvttt(fid, common.TTT_STRING).(string)

		mgetThreadResInter, err := multi.GetResult(ctx, "mgetThread"+fidStr)
		mgetThreadRes := mgetThreadResInter.(*frs.MgetThreadRes)
		if err != nil || mgetThreadRes.Errno == nil || mgetThreadRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call post::mgetThread fail: %v, res: %v", err, common.ToString(mgetThreadRes))
		} else {
			maskResInter, err := multi.GetResult(ctx, "getMaskInfo"+fidStr)
			maskRes := maskResInter.(*pb.GetMaskInfoRes)
			if err != nil || maskRes.Errno == nil || maskRes.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "call post::getMaskInfo fail: %v, res: %v", err, common.ToString(mgetThreadRes))
				continue
			}

			for tid, thread := range mgetThreadRes.GetOutput().GetThreadList() {
				//过滤删除/主态贴子
				if maskInfo, ok := maskRes.GetOutput().GetThreadsMaskStatus()[tid]; ok {
					if maskInfo.GetIsKeyDeleted() > 0 || maskInfo.GetIsKeyVisible() > 0 {
						continue
					}
				}

				//图片
				var pic string
				if len(thread.GetMedia()) > 0 && thread.GetMedia()[0].GetWaterPic() != "" {
					pic = thread.GetMedia()[0].GetWaterPic()
					centrePoint, err := thread.GetThumbnailCentrePointStruct()
					if err != nil {
						centrePoint, err = thread.GetMapThumbnailCentrePointStruct()
					}
					if err == nil && centrePoint != nil && centrePoint["tag_2"] != nil {
						picSign := hiphoto.GetPicSignFromUrl(pic)
						picId := hiphoto.DecodePicUrlCrypt(picSign)

						var thumbInfo *post.ThumbnailCentrePointTag
						for _, tmp := range centrePoint["tag_2"] {
							if tmp.GetPicId() == picId {
								thumbInfo = tmp
								break
							}
						}
						if thumbInfo != nil {
							cropInput := image.SmartCropInput{
								OriginW:         int(thumbInfo.GetSrcImageWidth()),
								OriginH:         int(thumbInfo.GetSrcImageHeight()),
								CenterRatioW:    thumbInfo.GetCenterSingle()[0],
								CenterRatioH:    thumbInfo.GetCenterSingle()[1],
								SmartAreaLength: int(thumbInfo.GetWidth()),
								ShowPicNum:      1,
								ClientType:      staticField.ClientType,
								ClientVersion:   staticField.ClientVersion,
								InterfaceName:   "c/f/forum/getRecommendForumData",
							}
							tmp, err := image.SmartCropWithUrl(pic, cropInput)
							if err != nil {
								tbcontext.WarningF(ctx, "SmartCropWithUrl fail: %v", err)
							} else {
								pic = tmp
							}
						}

						//cropReq := cropre
					}
				} else if thread.GetVideoInfo() != "" && thread.GetVideoInfo() != nil {
					videoInfo, err := thread.GetVideoInfoStruct(ctx)
					if err == nil && videoInfo != nil && videoInfo.GetVideoMd5() != "" {
						pic = videoInfo.GetThumbnailUrl()
					}
				}

				if pic == "" {
					continue
				}

				//优先标题，没有标题就获取正文
				var title string
				if thread.GetTitle() != "" {
					title = strings.TrimSpace(html.UnescapeString(thread.GetTitle()))
				} else if thread.GetAbstract() != "" {
					title = strings.TrimSpace(html.UnescapeString(thread.GetAbstract()))
				} else {
					continue
				}

				if _, ok := threadList[fid]; !ok {
					threadList[fid] = make(map[int64]*clientRecommendData.ThreadList)
				}

				threadList[fid][int64(tid)] = &clientRecommendData.ThreadList{
					Tid:   proto.Uint64(tid),
					Title: proto.String(title),
					Pic:   proto.String(pic),
				}
			}
		}
	}

	if len(threadList) == 0 {
		return nil
	}

	deleteKey = make([]uint32, 0)
	for fid, threads := range threadList {
		if len(threads) < 3 {
			deleteKey = append(deleteKey, fid)
		}
	}

	for _, key := range deleteKey {
		delete(threadList, key)
	}

	//随机
	for fid, threads := range threadList {
		if len(threads) > types.DISPLAY_MAX_THREAD_NUM {
			newThread := make(map[int64]*clientRecommendData.ThreadList)
			i := 0
			for tid, val := range threads {
				if i > 5 {
					break
				}
				newThread[tid] = val
				i++
			}
			threadList[fid] = newThread
		}
	}

	staticField.ThreadList = threadList

	return nil
}

type ThreadListOperator struct {
}

func (rdop *ThreadListOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientRecommendData.GetRecommendForumDataRes
	var baseData *types.CRecommendForumDataBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewThreadList(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "ThreadList execute fail: %v", err)
		return err
	}

	return nil
}
