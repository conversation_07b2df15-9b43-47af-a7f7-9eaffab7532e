package core

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	clientRecommendData "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getRecommendForumData"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type RecDataInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_recommend_data_recdata", func() engine.Job {
		return &RecDataInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewRecDataInfo(ctx *engine.Context) *RecDataInfo {
	return &RecDataInfo{
		ctx: ctx,
	}
}
func (a *RecDataInfo) IsValid(ctx context.Context, baseData *types.CRecommendForumDataBaseData) bool {

	return true
}
func (a *RecDataInfo) Execute(ctx context.Context, outData *clientRecommendData.GetRecommendForumDataRes, baseData *types.CRecommendForumDataBaseData) error {
	staticField := baseData.StaticField

	input := map[string]interface{}{
		"from": types.REC_FROM_LABEL,
		"user_id": staticField.UserId,
		"num": types.REC_FORUM_NUM,
		"page": staticField.Page,
	}
	res := new(common.GetRecomForumByUidRes)
	err := tbservice.Call(ctx, "common", "getRecomForumByUid", input, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != types.REC_RAL_SUCCESS_CODE {
		tbcontext.WarningF(ctx, "call common::getRecomForumByUid fail, input:%v, output:%v, err: %v", input, res, err)
		return nil
	}

	if len(res.GetOutput().GetForumList()) == 0 {
		tbcontext.WarningF(ctx, "common_getRecomForumByUid get no data.")
		return nil
	}

	for _, arrForum := range res.GetOutput().GetForumList() {
		if arrForum.GetForumId() != 0 {
			forumId := uint32(arrForum.GetForumId())
			staticField.ArrRecForumIds = append(staticField.ArrRecForumIds, forumId)
			staticField.ArrRecTids[forumId] = arrForum.GetTids()
			staticField.ArrRecFrom[forumId] = arrForum.GetFrom()
		}
	}

	return nil
}

type RecDataInfoOperator struct {
}

func (rdop *RecDataInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientRecommendData.GetRecommendForumDataRes
	var baseData *types.CRecommendForumDataBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewRecDataInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "RecDataInfo execute fail: %v", err)
		return err
	}

	return nil
}
