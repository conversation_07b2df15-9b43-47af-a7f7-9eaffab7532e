package core

import (
	"context"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientRecommendData "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getRecommendForumData"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"strconv"
)

type ForumList struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_recommend_data_forumlist", func() engine.Job {
		return &ForumListOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewForumList(ctx *engine.Context) *ForumList {
	return &ForumList{
		ctx: ctx,
	}
}
func (a *ForumList) IsValid(ctx context.Context, baseData *types.CRecommendForumDataBaseData) bool {
	staticField := baseData.StaticField
	if len(staticField.ArrRecForumIds) == 0 {
		return false
	}

	return true
}
func (a *ForumList) Execute(ctx context.Context, outData *clientRecommendData.GetRecommendForumDataRes, baseData *types.CRecommendForumDataBaseData) error {
	staticField := baseData.StaticField

	multi := tbservice.Multi()
	for i := 0; len(staticField.ArrRecForumIds) > i*types.BTX_PER_CALL; i++ {
		var fids []uint32
		if len(staticField.ArrRecForumIds) > (i+1)*types.BTX_PER_CALL {
			fids = staticField.ArrRecForumIds[i*types.BTX_PER_CALL : (i+1)*types.BTX_PER_CALL]
		} else {
			fids = staticField.ArrRecForumIds[i*types.BTX_PER_CALL:]
		}

		arrParam := &tbservice.Parameter{
			Service: "forum",
			Method:  "mgetBtxInfoEx",
			Input: map[string]interface{}{
				"forum_id": fids,
			},
			Output: &forum.MgetBtxInfoExRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetBtxInfoEx"+strconv.Itoa(i), arrParam)
	}

	multi.Call(ctx)

	forumInfoList := make(map[uint32]*clientRecommendData.RecForumInfo)
	for i := 0; len(staticField.ArrRecForumIds) > i*types.BTX_PER_CALL; i++ {
		resInter, err := multi.GetResult(ctx, "mgetBtxInfoEx"+strconv.Itoa(i))
		res := resInter.(*forum.MgetBtxInfoExRes)
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call forum::mgetBtxInfoEx fail: %v, res: %v", err, common.ToString(res))
			continue
		}
		for fid, fInfo := range res.GetOutput() {
			//推荐出的数据可能有问题，对未创建的吧需要屏蔽
			if fInfo.GetForumName() == nil || fInfo.GetForumName().GetForumId() == 0 ||
				fInfo.GetForumName().GetForumName() == "" {
				continue
			}

			recFrom := staticField.ArrRecFrom[fid]
			forumInfoList[fid] =  &clientRecommendData.RecForumInfo{
				ForumId:     proto.String(common.Tvttt(fInfo.GetForumName().GetForumId(), common.TTT_STRING).(string)),
				ForumName:   proto.String(fInfo.GetForumName().GetForumName()),
				Avatar:      proto.String(fInfo.GetCard().GetAvatar()),
				MemberCount: proto.Uint32(fInfo.GetStatistics().GetMemberCount()),
				ThreadCount: proto.Uint32(fInfo.GetStatistics().GetPostNum()),
				From:        proto.String(recFrom),
			}

			staticField.BtxForumIdx[int64(fid)] = fInfo
		}
	}

	//重置排序
	for _, fid := range staticField.ArrRecForumIds {
		if _, ok := forumInfoList[fid]; ok {
			staticField.ArrRecForumInfoList = append(staticField.ArrRecForumInfoList, forumInfoList[fid])
		}
	}

	return nil
}

type ForumListOperator struct {
}

func (rdop *ForumListOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientRecommendData.GetRecommendForumDataRes
	var baseData *types.CRecommendForumDataBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewForumList(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "ForumList execute fail: %v", err)
		return err
	}

	return nil
}
