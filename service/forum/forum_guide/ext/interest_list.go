package ext

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	commonproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	clientForumGuide "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	"icode.baidu.com/baidu/tieba-server-go/golib2/php"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type InterestList struct {
	ctx       *engine.Context
	onlineMap map[string]struct{}
	classList []string
}

func init() {
	err := engine.RegisterOperator("guide_interest_list", func() engine.Job {
		return &InterestListOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewInterestList(ctx *engine.Context) *InterestList {
	return &InterestList{
		ctx: ctx,
	}
}
func (a *InterestList) IsValid(ctx context.Context, baseData *types.CForumGuideBaseData) bool {
	static := baseData.StaticField
	// 12.68版本后，新用户登录后，没有关注的吧才会下发兴趣面板信息
	if baseData.Request.GetIsNewUser() == 1 && len(static.ArrLikeForumList) == 0 && static.UserID > 0 &&
		clientvers.Compare("12.68.0", static.StrClientVersion) >= 0 {
		return true
	}
	// todo 改成false
	return false
}
func (a *InterestList) Execute(ctx context.Context, outData *clientForumGuide.ForumGuideRes, baseData *types.CForumGuideBaseData) error {
	static := baseData.StaticField
	// 查询该用户近期是否提交过兴趣
	commonInput := map[string]int64{
		"user_id":     static.UserID,
		"check_forum": 1,
	}

	commonRes := new(commonproto.GetInterestCommitRes)
	err := tbservice.Call(ctx, "common", "getInterestCommit", commonInput, &commonRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || commonRes.Errno == nil || commonRes.GetErrno() != errno.CodeSuccess {
		tbcontext.WarningF(ctx, "call common::getInterestCommit getInterestCommitRes failed, ret: %v", commonRes)
		return errno.ErrCallServiceFail
	}

	// 14 天没有提交过才展示
	if time.Now().Unix()-commonRes.GetData().GetTagCommitTime() < 60*60*24*14 {
		tbcontext.WarningF(ctx, "user[%d] interest commit time is too near, commit time:[%d]", static.UserID, commonRes.GetData().GetTagCommitTime())
		return nil
	}

	err = a.getNewInterestBoard(ctx)
	if err != nil {
		tbcontext.WarningF(ctx, "getNewInterestBoard err %v", err.Error())
		return err
	}

	allClassList := []*clientForumGuide.InterestClassList{}
	userInterestingClassList := []*clientForumGuide.InterestClassList{}

	for _, value := range a.classList {
		classValue := strings.Split(value, "@@")
		if len(classValue) != 2 {
			continue
		}

		index, err := strconv.Atoi(classValue[0])
		if err != nil {
			continue
		}

		if _, ok := a.onlineMap[classValue[1]]; !ok {
			continue
		}

		single := &clientForumGuide.InterestClassList{
			ClassId:   proto.Int64(int64(index)),
			ClassName: proto.String(classValue[1]),
			HasChoose: proto.Int32(0),
		}
		if !php2go.InArray(classValue[1], commonRes.GetData().GetInterestTag()) {
			allClassList = append(allClassList, single)
		} else {
			single.HasChoose = proto.Int32(1)
			userInterestingClassList = append(userInterestingClassList, single)
		}
	}

	allClassList = append(allClassList, userInterestingClassList...)

	outData.InterestClassList = allClassList
	return nil
}

func (a *InterestList) getNewInterestBoard(ctx context.Context) error {
	// 词表获取跳转地址和文案
	wordValues, err := wordserver.QueryKeys(ctx, "tb_wordlist_redis_new_interest_board",
		[]string{"class_id2name_map",
			"online_class_name",
		})

	if err != nil || len(wordValues) != 2 {
		err = fmt.Errorf("wordserver QueryKey err %w, out :%v", err, wordValues)
		tbcontext.WarningF(ctx, "wordserver QueryKey err %v %v", err, wordValues)
		return err
	}

	values := wordValues[0]
	wordOnlineClassList := wordValues[1]

	classList := make([]string, 0)
	tmp, err := php.Unserialize([]byte(values))
	if err != nil {
		tbcontext.WarningF(ctx, "php Unserialize fail. input:%v, err:%v", values, err)
		return err
	}
	err = common.StructAToStructBCtx(ctx, tmp, &classList)
	if err != nil {
		tbcontext.WarningF(ctx, "change to []string fail. tmp:%v, err:%v", tmp, err)
		return err
	}
	a.classList = classList

	onlineClassList := make([]string, 0)
	res, err := php.Unserialize([]byte(wordOnlineClassList))
	if err != nil {
		tbcontext.WarningF(ctx, "php Unserialize fail. input:%v, err:%v", wordOnlineClassList, err)
		return err
	}
	err = common.StructAToStructBCtx(ctx, res, &onlineClassList)
	if err != nil {
		tbcontext.WarningF(ctx, "change to []string fail. res:%v, err:%v", res, err)
		return err
	}

	onlineMap := make(map[string]struct{})
	for _, classValue := range onlineClassList {
		onlineMap[classValue] = struct{}{}
	}
	a.onlineMap = onlineMap

	return nil
}

type InterestListOperator struct {
}

func (rdop *InterestListOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientForumGuide.ForumGuideRes
	var baseData *types.CForumGuideBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewInterestList(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "InterestList execute fail: %v", err)
		return err
	}

	return nil
}
