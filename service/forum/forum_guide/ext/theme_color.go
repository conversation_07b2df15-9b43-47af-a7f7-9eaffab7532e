package ext

import (
	"context"
	"encoding/json"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/conf"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/utilconst"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientForumGuide "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ThemeColor struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("guide_forum_themecolor", func() engine.Job {
		return &ThemeColorOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewThemeColor(ctx *engine.Context) *ThemeColor {
	return &ThemeColor{
		ctx: ctx,
	}
}
func (a *ThemeColor) IsValid(ctx context.Context, baseData *types.CForumGuideBaseData) bool {

	return true
}
func (a *ThemeColor) Execute(ctx context.Context, outData *clientForumGuide.ForumGuideRes, baseData *types.CForumGuideBaseData) error {
	staticField := baseData.StaticField

	for _, arrForum := range staticField.ArrLikeForumList {
		if arrForum.GetForumName() == "" {
			continue
		}

		btx := staticField.BtxForumIdx[int64(arrForum.GetForumId())]
		a.setColorInfo(ctx, btx, arrForum, staticField)
	}

	for _, arrForum := range staticField.ArrTopLikeForumList {
		if arrForum.GetForumName() == "" {
			continue
		}

		btx := staticField.BtxTopForumIdx[int64(arrForum.GetForumId())]
		a.setColorInfo(ctx, btx, arrForum, staticField)
	}

	return nil
}

func (a *ThemeColor) setColorInfo(ctx context.Context, btx *forum.BtxInfo, arrForum *clientForumGuide.LikeForum, staticField *types.ForumGuideStaticField) {
	//10.0返回吧主题色
	//如果没有返回默认色
	themeColor := &client.ThemeColorInfo{
		Day: &client.ThemeElement{
			CommonColor: proto.String(utilconst.DAY_THEME_COLOR_DEFAULT),
			DarkColor:   proto.String(utilconst.DAY_DARK_COLOR_DEFAULT),
			LightColor:  proto.String(utilconst.DAY_LIGHT_COLOR_DEFAULT),
		},
		Night: &client.ThemeElement{
			CommonColor: proto.String(utilconst.NIGHT_THEME_COLOR_DEFAULT),
			DarkColor:   proto.String(utilconst.NIGHT_DARK_COLOR_DEFAULT),
			LightColor:  proto.String(utilconst.NIGHT_LIGHT_COLOR_DEFAULT),
		},
	}

	themeColorStr := btx.GetAttrs().GetThemeColor()
	if themeColorStr != "" {
		// 因主题色返回格式改变，主要为了兼容第一版
		themeColorAttr := new(client.ThemeColorInfo)
		err := json.Unmarshal([]byte(themeColorStr), themeColorAttr)
		if err != nil {
			tbcontext.WarningF(ctx, "change to ThemeColorInfo fail: %v", err)
		}

		if themeColorAttr.GetDay() != nil && themeColorAttr.GetNight() != nil {
			themeColor = themeColorAttr
		}
	}

	clientStr := "android"
	if staticField.IntClientType == clientvers.CLIENT_TYPE_IPHONE {
		clientStr = "ios"
	} else if staticField.IntClientType == clientvers.CLIENT_TYPE_HARMONY {
		clientStr = "harmony"
	}

	firstClass := btx.GetDir().GetLevel_1Name()
	var pattern []string
	switch firstClass {
	case utilconst.DIR_CATEGORY_YULE, utilconst.DIR_CATEGORY_ZONGYI, utilconst.DIR_CATEGORY_ZHUIJU, utilconst.DIR_CATEGORY_MOVIE, utilconst.DIR_CATEGORY_FUN:
		pattern = conf.ArrPatternImage[clientStr]["entertainment"]
	case utilconst.DIR_CATEGORY_SPORT:
		pattern = conf.ArrPatternImage[clientStr]["sport"]
	case utilconst.DIR_CATEGORY_NOVEL, utilconst.DIR_CATEGORY_AREA, utilconst.DIR_CATEGORY_NATURE:
		pattern = conf.ArrPatternImage[clientStr]["culture"]
	case utilconst.DIR_CATEGORY_LIFE:
		pattern = conf.ArrPatternImage[clientStr]["life"]
	case utilconst.DIR_CATEGORY_GAME, utilconst.DIR_CATEGORY_CARTOON, utilconst.DIR_CATEGORY_CAMPUS:
		pattern = conf.ArrPatternImage[clientStr]["young"]
	default:
		pattern = conf.ArrPatternImage[clientStr]["default"]
	}

	if len(pattern) > 0 {
		index := common.Tvttt(arrForum.GetForumId(), common.TTT_INT).(int) % len(pattern)
		if themeColor.GetDay() != nil {
			themeColor.GetDay().PatternImage = proto.String(pattern[index])
		}
		if themeColor.GetNight() != nil {
			themeColor.GetNight().PatternImage = proto.String(pattern[index])
		}
	}

	arrForum.ThemeColor = themeColor
	arrForum.NeedTrans = proto.Bool(true)
}

type ThemeColorOperator struct {
}

func (rdop *ThemeColorOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientForumGuide.ForumGuideRes
	var baseData *types.CForumGuideBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewThemeColor(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "ThemeColor execute fail: %v", err)
		return err
	}

	return nil
}
