package ext

import (
	"context"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	clientForumGuide "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ForumSquare struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("guide_forum_square", func() engine.Job {
		return &ForumSquareOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewForumSquare(ctx *engine.Context) *ForumSquare {
	return &ForumSquare{
		ctx: ctx,
	}
}
func (a *ForumSquare) IsValid(ctx context.Context, baseData *types.CForumGuideBaseData) bool {
	static := baseData.StaticField
	if baseData.Request.GetIsNewUser() == 1 && clientvers.Compare("12.68.0", static.StrClientVersion) >= 0 {
		return true
	}
	// todo 改成false
	return false
}
func (a *ForumSquare) Execute(ctx context.Context, outData *clientForumGuide.ForumGuideRes, baseData *types.CForumGuideBaseData) error {
	//读取词表配置
	rawConf, err := wordserver.QueryKey(ctx, types.WordlistForumLikeName, types.ForumSquareIconListKey)
	if err != nil || len(rawConf) == 0 {
		tbcontext.WarningF(ctx, "query wordlist fail, err=%v, wordlist=%s, key=%s", err, types.WordlistForumLikeName, types.ForumSquareIconListKey)
		return nil
	}

	if rawConf == "" {
		return nil
	}

	err = jsoniter.UnmarshalFromString(rawConf, &outData.ForumSquareIconList)
	if err != nil {
		tbcontext.WarningF(ctx, "unmarshal from string fail, str:%s, err:%v", rawConf, err)
	}
	return nil
}

type ForumSquareOperator struct {
}

func (rdop *ForumSquareOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientForumGuide.ForumGuideRes
	var baseData *types.CForumGuideBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewForumSquare(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "ForumSquare execute fail: %v", err)
		return err
	}

	return nil
}
