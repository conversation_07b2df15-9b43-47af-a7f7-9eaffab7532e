package ext

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/voiceroom"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientForumGuide "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type VoiceRoom struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("guide_forum_voiceroom", func() engine.Job {
		return &VoiceRoomOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewVoiceRoom(ctx *engine.Context) *VoiceRoom {
	return &VoiceRoom{
		ctx: ctx,
	}
}
func (a *VoiceRoom) IsValid(ctx context.Context, baseData *types.CForumGuideBaseData) bool {
	staticField := baseData.StaticField
	if staticField.UserID <= 0 {
		return false
	}
	if baseData.Request.GetCallFrom() == types.LikeForumTabCallForm {
		// 我关注的吧页面不需要这个逻辑
		return false
	}

	// 鸿蒙版无语音房信息
	if staticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
		return false
	}
	return true
}
func (a *VoiceRoom) Execute(ctx context.Context, outData *clientForumGuide.ForumGuideRes, baseData *types.CForumGuideBaseData) error {
	staticField := baseData.StaticField
	if !clientvers.IsLegalVersion("12_14_0", staticField.IntClientType, staticField.StrClientVersion) {
		return nil
	}

	uidKey := fmt.Sprintf("uid_%d", staticField.UserID)
	keys := []string{"visible_rate", "need_num", uidKey, "open_switch"}
	info, err := wordserver.QueryKeys(ctx, types.TBWordlistRedisVoiceroom, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "call wordlist getValueByKeys fail")
		return nil
	}

	visibleRate := 0
	sw := 0
	isInWhiteList := 0
	if len(info) >= 4 {
		tmpRate := common.Tvttt(info[0], common.TTT_INT).(int)
		if tmpRate > 0 {
			visibleRate = tmpRate
		}

		tmpSw := common.Tvttt(info[3], common.TTT_INT).(int)
		if tmpSw > 0 {
			sw = tmpSw
		}

		if sw == 1 || sw == 0 {
			if info[2] == "1" {
				isInWhiteList = 1
			}
		}
	}

	if hash(staticField.Cuid) < int64(visibleRate) || isInWhiteList == 1 {
		needNum := common.Tvttt(info[1], common.TTT_INT).(int)
		if needNum == 0 {
			needNum = 6
		}
		input := map[string]interface{}{
			"client_type":    staticField.IntClientType,
			"client_version": staticField.StrClientVersion,
			"user_id":        staticField.UserID,
			"need_num":       needNum,
			"call_from":      "intoBarPage",
		}
		getSortVoiceRoomListRes := new(voiceroom.GetSortVoiceRoomListRes)
		err = tbservice.Call(ctx, "voiceroom", "getSortVoiceRoomList", input, getSortVoiceRoomListRes, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || getSortVoiceRoomListRes.Errno == nil || getSortVoiceRoomListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call voiceroom::getSortVoiceRoomList fail, input:%v, output:%v, err: %v", input, getSortVoiceRoomListRes, err)
			return nil
		}

		staticField.VoiceRoomList = make([]*client.ThreadInfo, 0)
		err = common.StructAToStructBCtx(ctx, getSortVoiceRoomListRes.GetData(), &staticField.VoiceRoomList)
		if err != nil {
			tbcontext.WarningF(ctx, "getSortVoiceRoomList data change fail: %v", err)
		}
	}

	return nil
}

type VoiceRoomOperator struct {
}

func (rdop *VoiceRoomOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientForumGuide.ForumGuideRes
	var baseData *types.CForumGuideBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewVoiceRoom(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "VoiceRoom execute fail: %v", err)
		return err
	}

	return nil
}

func hash(value string) int64 {
	hash := ""
	if len(value) >= 15 {
		hash = value[len(value)-15:]
	}
	if 0 < len(hash) {
		ihash, err := php2go.HexdecUint(hash) //php2go.Hexdec(hash)
		if nil == err {
			return int64(ihash) % 100
		}
	}
	return 0
}
