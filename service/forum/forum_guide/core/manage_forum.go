package core

import (
	"context"
	"errors"
	"sort"
	"time"

	"github.com/golang/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	clientForumGuide "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type UserForumLevelReqItem struct {
	UserID  int64 `json:"user_id"`
	ForumID int64 `json:"forum_id"`
}

type ManageForum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_guide_manage_forum", func() engine.Job {
		return &ManageForumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewManageForum(ctx *engine.Context) *ManageForum {
	return &ManageForum{
		ctx: ctx,
	}
}

func (a *ManageForum) IsValid(ctx context.Context, baseData *types.CForumGuideBaseData) bool {
	if baseData.Request.GetCallFrom() == types.LikeForumTabCallForm && baseData.Request.GetPageNo() == 1 && baseData.StaticField.UserID > 0 {
		// 来自新版我关注的吧页面且是第一页才执行此算子获取我管理的吧信息
		return true
	}
	// todo 改成false
	return false
}

func (a *ManageForum) Execute(ctx context.Context, outData *clientForumGuide.ForumGuideRes, baseData *types.CForumGuideBaseData) error {
	staticField := baseData.StaticField
	// 获取用户管理的吧信息
	permInput := map[string]any{
		"user_id": staticField.UserID,
	}

	permOutput := new(perm.GetUserBawuForumRes)
	err := tbservice.Call(ctx, "perm", "getUserBawuForum", permInput, permOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || permOutput.Errno == nil || permOutput.GetErrno() != 0 {
		tbcontext.FatalF(ctx, "call perm::getUserBawuForum fail, input:%v, output:%v, err: %v", permInput, common.ToString(permOutput), err)
		return errors.New("call service fail")
	}

	staticField.ManagerForumIDs = []int64{}
	for _, v := range permOutput.GetForumUserRoles().GetManager() {
		staticField.ManagerForumIDs = append(staticField.ManagerForumIDs, int64(v.GetForumId()))
	}
	staticField.AssistForumIDs = []int64{}
	for _, v := range permOutput.GetForumUserRoles().GetAssist() {
		staticField.AssistForumIDs = append(staticField.AssistForumIDs, int64(v.GetForumId()))
	}

	// 查吧信息，用户信息，用户吧内等级信息
	err = a.getManagerInfo(ctx, outData, staticField)
	if err != nil {
		return err
	}

	return nil
}

func (a *ManageForum) getManagerInfo(ctx context.Context, outData *clientForumGuide.ForumGuideRes, staticField *types.ForumGuideStaticField) error {
	forumIDs := make([]int64, 0, len(staticField.ManagerForumIDs)+len(staticField.AssistForumIDs))
	forumIDs = append(forumIDs, staticField.ManagerForumIDs...)
	forumIDs = append(forumIDs, staticField.AssistForumIDs...)
	multi := tbservice.Multi()
	if len(forumIDs) > 0 {
		forumParam := &tbservice.Parameter{
			Service: "forum",
			Method:  "mgetBtxInfoEx",
			Input: map[string]any{
				"forum_id": forumIDs,
			},
			Output: &forum.MgetBtxInfoExRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetBtxInfoEx", forumParam)
		userForumLevelInput := make([]UserForumLevelReqItem, len(forumIDs))
		for idx, forumIDItem := range forumIDs {
			userForumLevelInput[idx] = UserForumLevelReqItem{
				UserID:  staticField.UserID,
				ForumID: forumIDItem,
			}
		}
		permParam := &tbservice.Parameter{
			Service: "perm",
			Method:  "mgetUserForumLevel",
			Input: map[string]any{
				"req": userForumLevelInput,
			},
			Output: &perm.MgetUserForumLevelRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetUserForumLevel", permParam)
	}

	userParam := &tbservice.Parameter{
		Service: "user",
		Method:  "getUserData",
		Input: map[string]any{
			"user_id": staticField.UserID,
		},
		Output: &user.GetUserDataRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getUserData", userParam)

	multi.Call(ctx)
	// 处理rpc调用信息
	if len(forumIDs) > 0 {
		forumOutInter, err := multi.GetResult(ctx, "mgetBtxInfoEx")
		if err != nil {
			tbcontext.WarningF(ctx, "call forum::mgetBtxInfoEx fail: %v", err)
			return err
		}
		forumOut, ok := forumOutInter.(*forum.MgetBtxInfoExRes)
		if !ok || forumOut.Errno == nil || forumOut.GetErrno() != 0 {
			tbcontext.WarningF(ctx, "call forum::mgetBtxInfoEx fail: %v", forumOut)
			return errors.New("call service fail")
		}

		permOutInter, err := multi.GetResult(ctx, "mgetUserForumLevel")
		if err != nil {
			tbcontext.WarningF(ctx, "call perm::mgetUserForumLevel fail: %v", err)
			return err
		}
		permOut, ok := permOutInter.(*perm.MgetUserForumLevelRes)
		if !ok || permOut.Errno == nil || permOut.GetErrno() != 0 {
			tbcontext.WarningF(ctx, "call perm::mgetUserForumLevel fail: %v", permOut)
			return errors.New("call service fail")
		}
		scoreMap := make(map[int64]*perm.UserOutput)
		for _, score := range permOut.GetScoreInfo() {
			scoreMap[int64(score.GetForumId())] = score
		}

		// 吧主身份的吧信息
		outData.ManageForum = make([]*clientForumGuide.LikeForum, 0, len(staticField.ManagerForumIDs))
		managerForumList := make([]*clientForumGuide.LikeForum, 0, len(staticField.ManagerForumIDs))
		for _, forumID := range staticField.ManagerForumIDs {
			forumInfo := forumOut.GetOutput()[uint32(forumID)]
			scoreInfo := scoreMap[forumID]
			isForbidden := forumInfo.GetForumName().GetForbidden()
			if forumInfo == nil || scoreInfo == nil {
				continue
			}
			// week11 我管理的吧被关后不展示
			if isForbidden == 1 {
				tbcontext.TraceF(ctx, "forum[id:%d name:%s] is forbidden", forumID, forumInfo.GetForumName().GetForumName())
				continue
			}
			managerForumList = append(managerForumList, &clientForumGuide.LikeForum{
				ForumId:         proto.Uint64(uint64(forumID)),
				Avatar:          proto.String(forumInfo.GetCard().GetAvatar()),
				ForumName:       proto.String(forumInfo.GetForumName().GetForumName()),
				LevelId:         proto.Int32(int32(scoreInfo.GetLevelId())),
				LevelName:       proto.String(scoreInfo.GetLevelName()),
				ManagerRoleName: proto.String("吧主"),
			})
			// 排序
			if len(managerForumList) >= 2 {
				sort.Slice(managerForumList, func(i, j int) bool {
					return managerForumList[i].GetLevelId() > managerForumList[j].GetLevelId()
				})
			}
		}
		if len(managerForumList) > 0 {
			outData.ManageForum = append(outData.ManageForum, managerForumList...)
		}

		// 小吧主身份的吧信息
		assistForumList := make([]*clientForumGuide.LikeForum, 0, len(staticField.AssistForumIDs))
		for _, forumID := range staticField.AssistForumIDs {
			forumInfo := forumOut.GetOutput()[uint32(forumID)]
			scoreInfo := scoreMap[forumID]
			if forumInfo == nil || scoreInfo == nil {
				continue
			}
			assistForumList = append(assistForumList, &clientForumGuide.LikeForum{
				ForumId:         proto.Uint64(uint64(forumID)),
				Avatar:          proto.String(forumInfo.GetCard().GetAvatar()),
				ForumName:       proto.String(forumInfo.GetForumName().GetForumName()),
				LevelId:         proto.Int32(int32(scoreInfo.GetLevelId())),
				LevelName:       proto.String(scoreInfo.GetLevelName()),
				ManagerRoleName: proto.String("小吧主"),
			})
			// 排序
			if len(assistForumList) >= 2 {
				sort.Slice(assistForumList, func(i, j int) bool {
					return assistForumList[i].GetLevelId() > assistForumList[j].GetLevelId()
				})
			}
		}
		if len(assistForumList) > 0 {
			outData.ManageForum = append(outData.ManageForum, assistForumList...)
		}
	}

	userOutInter, err := multi.GetResult(ctx, "getUserData")
	if err != nil {
		tbcontext.WarningF(ctx, "call user::getUserData fail: %v", err)
		return err
	}

	userOut, ok := userOutInter.(*user.GetUserDataRes)
	if !ok || userOut.Errno == nil || userOut.GetErrno() != 0 {
		tbcontext.WarningF(ctx, "call user::getUserData fail: %v", userOut)
		return errors.New("call service fail")
	}

	// 用户是否超级vip和用户的关注吧隐私状态
	if len(userOut.GetUserInfo()) > 0 {
		userInfo := userOut.GetUserInfo()[0]
		if userInfo.GetVipInfo().GetVStatus() >= 2 && userInfo.GetVipInfo().GetETime() >= time.Now().Unix() {
			outData.IsSuperVip = proto.Int32(1)
		}

		outData.LikePrivSets = proto.Int32(userInfo.GetPrivSets().GetLike())
	}

	return nil
}

type ManageForumOperator struct {
}

func (rdop *ManageForumOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientForumGuide.ForumGuideRes
	var baseData *types.CForumGuideBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewManageForum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "ManageForum execute fail: %v", err)
		return err
	}

	return nil
}
