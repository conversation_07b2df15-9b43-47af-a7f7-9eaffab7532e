package core

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/bawu"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	clientForumGuide "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ForumCreateInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("guide_forum_createinfo", func() engine.Job {
		return &ForumCreateInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewForumCreateInfo(ctx *engine.Context) *ForumCreateInfo {
	return &ForumCreateInfo{
		ctx: ctx,
	}
}
func (a *ForumCreateInfo) IsValid(ctx context.Context, baseData *types.CForumGuideBaseData) bool {

	return true
}
func (a *ForumCreateInfo) Execute(ctx context.Context, outData *clientForumGuide.ForumGuideRes, baseData *types.CForumGuideBaseData) error {
	staticField := baseData.StaticField
	//私有化吧创建信息
	if clientvers.IsPriforumVersion(staticField.IntClientType, staticField.StrClientVersion) {

		if staticField.UserID == 0 {
			staticField.ForumCreateInfo = &client.ForumCreateInfo{
				IsShowCreate:     proto.Int32(0),
				IsCanCreate:      proto.Int32(0),
				CanCreateErrno:   proto.Int32(0),
				CanCreateErrdata: proto.String(""),
			}
			return nil
		}

		canCreateErrArr := map[int32]string{
			tiebaerror.ERR_CALL_SERVICE_FAIL:                      "页面出了一点小问题，刷新一下吧~",
			tiebaerror.ERR_ANTI_ID_BLOCKED:                        "您的帐号涉及违规操作，现已被贴吧官方管理员封禁，可进行申诉。",
			tiebaerror.ERROR_MIS_FMANAGER_ENOUGH_USER_MANAGER_NUM: "您当前担任吧主数量已达到上限，您需要先辞职才能再创建新吧哦~",
		}
		input := map[string]interface{}{
			"user_id":        staticField.UserID,
			"client_version": staticField.StrClientVersion,
			"client_type":    staticField.IntClientType,
		}
		canUserCreateRes := new(bawu.CanUserCreateRes)
		err := tbservice.Call(ctx, "bawu", "canUserCreate", input, canUserCreateRes, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || canUserCreateRes.Errno == nil || canUserCreateRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call bawu::canUserCreate fail, input:%v, output:%v, err: %v", input, canUserCreateRes, err)
			staticField.ForumCreateInfo = &client.ForumCreateInfo{
				IsShowCreate:     proto.Int32(0),
				IsCanCreate:      proto.Int32(0),
				CanCreateErrno:   proto.Int32(0),
				CanCreateErrdata: proto.String(""),
			}
		} else {
			data := canUserCreateRes.GetData()
			if data != nil {
				staticField.ForumCreateInfo = &client.ForumCreateInfo{
					IsShowCreate:     proto.Int32(data.GetIsShowCreate()),
					IsCanCreate:      proto.Int32(data.GetIsCanCreate()),
					CanCreateErrno:   proto.Int32(data.GetCanCreateErrno()),
					CanCreateErrdata: proto.String(canCreateErrArr[data.GetCanCreateErrno()]),
				}
			}
		}
	}

	return nil
}

type ForumCreateInfoOperator struct {
}

func (rdop *ForumCreateInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientForumGuide.ForumGuideRes
	var baseData *types.CForumGuideBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewForumCreateInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "ForumCreateInfo execute fail: %v", err)
		return err
	}

	return nil
}
