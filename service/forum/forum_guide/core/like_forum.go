package core

import (
	"context"
	"strconv"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	protoCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/sign"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientForumGuide "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type LikeForum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_guide_like_forum", func() engine.Job {
		return &LikeForumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewLikeForum(ctx *engine.Context) *LikeForum {
	return &LikeForum{
		ctx: ctx,
	}
}

func (a *LikeForum) IsValid(ctx context.Context, baseData *types.CForumGuideBaseData) bool {
	staticField := baseData.StaticField
	if staticField.UserID <= 0 {
		return false
	}
	return true
}

// Execute 执行逻辑
func (a *LikeForum) Execute(ctx context.Context, outData *clientForumGuide.ForumGuideRes, baseData *types.CForumGuideBaseData) error {
	isLogin := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("login", false), common.TTT_BOOL).(bool)
	if !isLogin {
		return nil
	}

	staticField := baseData.StaticField
	if staticField == nil {
		return nil
	}

	input := map[string]any{
		"user_id":              staticField.UserID,
		"check_forum":          1, // week11 去掉过滤关吧
		"need_forbidden_forum": 1, ////与check_forum=1结合，不过滤封禁吧但更新吧名
		"page_type":            1,
		"page_no":              1,
		"page_size":            types.LikeForumPageSize,
		"sort_type":            staticField.SortType,
	}

	// 鸿蒙版需要过滤已经关闭的吧
	if staticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
		//input["check_forum"] = 1
		input["need_forbidden_forum"] = 0 //鸿蒙版不需要更新封禁吧信息和下发
	}

	// 吧等级排序时，sort_type 为 0
	if types.FlistSortForumLevel == staticField.SortType {
		input["sort_type"] = 0
	}

	// 我关注的吧页面，传参需要特殊处理
	if baseData.Request.GetCallFrom() == types.LikeForumTabCallForm {
		if staticField.SortType == 0 { // 逻辑兜底
			staticField.SortType = types.FlistSortForumTop
		}
		input["sort_type"] = staticField.SortType // 置顶的吧吧排序
		// 翻页参数使用入参的数据
		input["page_no"] = baseData.Request.GetPageNo()
		input["page_size"] = baseData.Request.GetResNum()
	}

	getLikeForumListRes := new(perm.GetLikeForumListRes)
	err := tbservice.Call(ctx, "perm", "getLikeForumList", input, getLikeForumListRes, tbservice.WithConverter(tbservice.JSONITER))

	if err != nil || getLikeForumListRes.Errno == nil {
		tbcontext.FatalF(ctx, "call perm::getLikeForumList fail, input:%v, output:%v, err: %v", input, getLikeForumListRes, err)
		return nil
	}
	if getLikeForumListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call perm::getLikeForumList fail, input:%v, output:%v, err: %v", input, getLikeForumListRes, err)
		baseData.BaseObj.Error(int(getLikeForumListRes.GetErrno()), tiebaerror.GetErrMsg(int(getLikeForumListRes.GetErrno())), true)
		return nil
	}

	// 判断关注吧列表是否存在更多
	if getLikeForumListRes.GetOutput().GetMemberCount() > types.LikeForumPageSize {
		staticField.BoolLikeForumHasMore = true
	}

	// 我关注的吧页面，hasmore需要单独判断
	if baseData.Request.GetCallFrom() == types.LikeForumTabCallForm {
		if getLikeForumListRes.GetOutput().GetMemberCount() > baseData.Request.GetPageNo()*baseData.Request.GetResNum() {
			staticField.BoolLikeForumHasMore = true
		} else {
			staticField.BoolLikeForumHasMore = false
		}
	}

	likeForum := make([]*perm.UserOutput, 0)
	for _, v := range getLikeForumListRes.GetOutput().GetMemberList() {
		if v != nil && v.GetForumName() != "" {
			likeForum = append(likeForum, v)
		}
	}

	staticField.ArrLikeForumTmp = likeForum

	getLikeForumBuild(ctx, staticField)

	if staticField.IsRefactor {
		// 进吧tab重构，额外下发外露展示的数量
		staticField.FoldDisplayNum = getFoldDisplayNum(ctx)
	}

	return nil
}

type LikeForumOperator struct {
}

func (rdop *LikeForumOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientForumGuide.ForumGuideRes
	var baseData *types.CForumGuideBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewLikeForum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "LikeForum execute fail: %v", err)
		return err
	}

	return nil
}

func getLikeForumBuild(ctx context.Context, staticField *types.ForumGuideStaticField) {
	if len(staticField.ArrLikeForumTmp) == 0 {
		return
	}

	likeForumId := make([]uint64, 0)
	likeForumList := make([]*clientForumGuide.LikeForum, 0)
	for _, arrForum := range staticField.ArrLikeForumTmp {
		// 如果吧名为空直接过滤，有吧已经被回收的情况
		if arrForum.GetForumName() == "" {
			continue
		}

		likeForum := &clientForumGuide.LikeForum{
			ForumName:    proto.String(arrForum.GetForumName()),
			ForumId:      proto.Uint64(uint64(arrForum.GetForumId())),
			SortValue:    proto.Uint64(uint64(arrForum.GetSortValue())),
			LevelId:      proto.Int32(int32(arrForum.GetLevelId())),
			LevelName:    proto.String(arrForum.GetLevelName()),
			TopSortValue: proto.Uint64(arrForum.GetTopSortValue()),
		}
		likeForumList = append(likeForumList, likeForum)
		likeForumId = append(likeForumId, uint64(arrForum.GetForumId()))
	}

	if len(likeForumList) == 0 {
		return
	}

	getForumInfoByForumIds(ctx, likeForumId, staticField)

	for _, item := range likeForumList {
		item.HotNum = proto.Uint32(0)
		item.Avatar = proto.String("")
		item.MemberCount = proto.Uint32(0)
		item.ThreadNum = proto.Uint32(0)
		item.IsSign = proto.Int32(0)
		item.IsForbidden = proto.Uint32(0)
		if forumHotInfo, ok := staticField.ArrForumHotNumList[int64(item.GetForumId())]; ok {
			item.HotNum = proto.Uint32(uint32(forumHotInfo.GetHotValue()))
		}
		if forumInfo, ok := staticField.ArrForumBaseInfoList[int64(item.GetForumId())]; ok {
			item.Avatar = proto.String(forumInfo.GetCard().GetAvatar())
			item.MemberCount = proto.Uint32(forumInfo.GetStatistics().GetMemberCount())
			item.ThreadNum = proto.Uint32(forumInfo.GetStatistics().GetThreadNum())
			item.DayThreadNum = proto.Uint32(forumInfo.GetStatistics().GetDayThreadNum())
			item.IsForbidden = proto.Uint32(uint32(forumInfo.GetForumName().GetForbidden()))
		}
		if isSign, ok := staticField.ArrForumSignInfo[int64(item.GetForumId())]; ok {
			item.IsSign = proto.Int32(isSign)
		}
	}

	staticField.ArrLikeForumList = likeForumList
}

func getForumInfoByForumIds(ctx context.Context, likeForumId []uint64, staticField *types.ForumGuideStaticField) {
	if len(likeForumId) == 0 {
		return
	}

	multi := tbservice.Multi()
	for i := 0; len(likeForumId) > i*types.MultiGetHotNumNum; i++ {
		var fids []uint64
		if len(likeForumId) > (i+1)*types.MultiGetHotNumNum {
			fids = likeForumId[i*types.MultiGetHotNumNum : (i+1)*types.MultiGetHotNumNum]
		} else {
			fids = likeForumId[i*types.MultiGetHotNumNum:]
		}

		iStr := strconv.Itoa(i)
		commonParam := &tbservice.Parameter{
			Service: "common",
			Method:  "mgetHotByForumId",
			Input: map[string]any{
				"forum_ids": fids,
			},
			Output: &protoCommon.MgetHotByForumIdRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetHotByForumId"+iStr, commonParam)

		forumParam := &tbservice.Parameter{
			Service: "forum",
			Method:  "mgetBtxInfoEx",
			Input: map[string]any{
				"forum_id": fids,
			},
			Output: &forum.MgetBtxInfoExRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetBtxInfoEx"+iStr, forumParam)

		signParam := &tbservice.Parameter{
			Service: "sign",
			Method:  "getUserSignForums",
			Input: map[string]any{
				"forum_id": fids,
				"user_id":  staticField.UserID,
			},
			Output: &sign.GetUserSignForumsRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getUserSignForums"+iStr, signParam)
	}

	multi.Call(ctx)

	forumHotNumList := make(map[int64]*protoCommon.MgetHotByForumId)
	forumBaseInfoList := make(map[int64]*forum.BtxInfo)
	signInfoList := make(map[int64]int32)
	for i := 0; len(likeForumId) > i*types.MultiGetHotNumNum; i++ {
		iStr := strconv.Itoa(i)
		// 并行获取热度值信息
		mgetHotByForumIdInter, err := multi.GetResult(ctx, "mgetHotByForumId"+iStr)
		if err != nil {
			tbcontext.WarningF(ctx, "call common::mgetHotByForumId fail: %v", err)
		} else {
			res := mgetHotByForumIdInter.(*protoCommon.MgetHotByForumIdRes)
			for _, forumInfo := range res.GetOutput() {
				if forumInfo == nil {
					continue
				}
				forumHotNumList[forumInfo.GetForumId()] = forumInfo
			}
		}

		// 获取吧基础信息
		mgetBtxInfoExInter, err := multi.GetResult(ctx, "mgetBtxInfoEx"+iStr)
		if err != nil {
			tbcontext.WarningF(ctx, "call forum::mgetBtxInfoEx fail: %v", err)
		} else {
			res := mgetBtxInfoExInter.(*forum.MgetBtxInfoExRes)
			for fid, forumInfo := range res.GetOutput() {
				if forumInfo == nil {
					continue
				}
				forumBaseInfoList[int64(fid)] = forumInfo
			}
		}

		// 获取吧签到信息
		getUserSignForumsInter, err := multi.GetResult(ctx, "getUserSignForums"+iStr)
		if err != nil {
			tbcontext.WarningF(ctx, "call sign::getUserSignForums fail: %v", err)
		} else {
			res := getUserSignForumsInter.(*sign.GetUserSignForumsRes)
			for fid, signInfo := range res.GetArrUserInfo() {
				if signInfo == nil {
					continue
				}
				signInfoList[int64(fid)] = signInfo.GetIsSignIn()
			}
		}
	}

	staticField.ArrForumSignInfo = signInfoList
	staticField.ArrForumBaseInfoList = forumBaseInfoList
	staticField.ArrForumHotNumList = forumHotNumList
	staticField.BtxForumIdx = forumBaseInfoList
}

// getFoldDisplayNum 获取外露展示的数量
func getFoldDisplayNum(ctx context.Context) (displayNum int) {
	// 设置默认外露展示数量
	displayNum = types.DefaultFoldDisplayNum

	// 查询词表配置
	rawConf, err := wordserver.QueryKey(ctx, types.WordlistForumLikeName, types.FoldDisplayNumKey)
	if err != nil || len(rawConf) == 0 {
		tbcontext.WarningF(ctx, "getFoldDisplayNum query wordlist fail, err=%v, wordlist=%s, key=%s", err, types.WordlistForumLikeName, types.HotSearchKey)
		return
	}

	// 解析词表配置
	num, err := strconv.ParseInt(rawConf, 10, 64)
	if err != nil || num <= 0 {
		tbcontext.WarningF(ctx, "getFoldDisplayNum parse wordlist fail, err=%v, raw config=%s", err, rawConf)
		return
	}
	return int(num)
}
