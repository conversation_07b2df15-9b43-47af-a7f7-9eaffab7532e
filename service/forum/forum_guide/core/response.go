package core

import (
	"context"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/hottopic/ext"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	clientForumGuide "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ForumGuideResp struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_guide_resp", func() engine.Job {
		return &ForumGuideRespOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewForumGuideResp(ctx *engine.Context) *ForumGuideResp {
	return &ForumGuideResp{
		ctx: ctx,
	}
}
func (a *ForumGuideResp) IsValid(ctx context.Context, baseData *types.CForumGuideBaseData) bool {

	return true
}
func (a *ForumGuideResp) Execute(ctx context.Context, outData *clientForumGuide.ForumGuideRes, baseData *types.CForumGuideBaseData) error {
	objReq := baseData.BaseObj.ObjRequest
	staticField := baseData.StaticField

	// 打点记录用户排序方式
	baseData.BaseObj.ObjResponse.AddLog("obj_type", staticField.SortType)

	isLogin := common.Tvttt(objReq.GetCommonAttr("login", false), common.TTT_BOOL).(bool)
	outData.IsLogin = proto.Uint32(0)
	if isLogin {
		outData.IsLogin = proto.Uint32(1)
	}
	outData.MsignValid = proto.Uint32(common.Tvttt(objReq.GetStrategy("m_sign", 0), common.TTT_UINT32).(uint32))
	outData.MsignText = proto.String("")
	if outData.GetMsignValid() == 0 {
		outData.MsignText = proto.String("小贴贴现在压力有点大，暂时无法提供一键签到服务，请稍候再来试试吧~")
	}
	outData.MsignLevel = proto.Uint32(common.Tvttt(objReq.GetStrategy("m_sign_member_level", 0), common.TTT_UINT32).(uint32))

	// 私有化吧创建信息
	outData.ForumCreateInfo = staticField.ForumCreateInfo
	// 热搜数据
	outData.HotSearch = staticField.ArrHotSearch
	// 关注吧数据，置顶吧放在前面
	outData.LikeForum = []*clientForumGuide.LikeForum{}
	if len(staticField.ArrTopLikeForumList) > 0 {
		outData.LikeForum = append(outData.LikeForum, staticField.ArrTopLikeForumList...)
	}
	if len(staticField.ArrLikeForumList) > 0 {
		outData.LikeForum = append(outData.LikeForum, staticField.ArrLikeForumList...)
	}

	if baseData.Request.GetCallFrom() == types.LikeForumTabCallForm {
		outData.LikeForumHasMore = proto.Bool(staticField.BoolLikeForumHasMore)
	}

	if clientvers.IsLegalVersion("12_14_0", staticField.IntClientType, staticField.StrClientVersion) {
		// 语音房数据
		outData.VoiceRoomList = staticField.VoiceRoomList
	}

	// 进吧tab重构版
	if staticField.IsRefactor {
		// 下发外露展示的数量
		outData.FoldDisplayNum = proto.Uint32(uint32(staticField.FoldDisplayNum))
		// 下发关注列表是否存在更多
		outData.LikeForumHasMore = proto.Bool(staticField.BoolLikeForumHasMore)
	}
	// 添加官方吧标识
	if outData != nil {
		forumMap := ext.GetOfficialForumIds(ctx)
		for _, forum := range outData.LikeForum {
			if _, ok := forumMap[cast.ToUint32(forum.ForumId)]; ok {
				forum.IsOfficialForum = proto.Uint32(1)
			}
		}
		for _, forum := range outData.ManageForum {
			if _, ok := forumMap[cast.ToUint32(forum.ForumId)]; ok {
				forum.IsOfficialForum = proto.Uint32(1)
			}
		}
	}
	return nil
}

type ForumGuideRespOperator struct {
}

func (rdop *ForumGuideRespOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientForumGuide.ForumGuideRes
	var baseData *types.CForumGuideBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewForumGuideResp(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "ForumGuideResp execute fail: %v", err)
		return err
	}

	return nil
}
