package core

import (
	"context"
	"strconv"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	protoCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/sign"
	clientForumGuide "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type TopLikeForum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_guide_top_like_forum", func() engine.Job {
		return &TopLikeForumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewTopLikeForum(ctx *engine.Context) *TopLikeForum {
	return &TopLikeForum{
		ctx: ctx,
	}
}
func (a *TopLikeForum) IsValid(ctx context.Context, baseData *types.CForumGuideBaseData) bool {
	if baseData.Request.GetCallFrom() == types.LikeForumTabCallForm && baseData.Request.GetPageNo() == 1 && baseData.StaticField.UserID > 0 {
		// 来自新版我关注的吧页面且是第一页才执行此算子获取我置顶的吧信息
		return true
	}
	// todo 改成false
	return false
}
func (a *TopLikeForum) Execute(ctx context.Context, outData *clientForumGuide.ForumGuideRes, baseData *types.CForumGuideBaseData) error {
	// 最大置顶吧数量
	outData.MaxTopForumNum = proto.Int32(types.DefaultMaxTopForumNum)

	// 查询词表配置
	rawConf, err := wordserver.QueryKey(ctx, types.WordlistForumLikeName, types.MaxTopForumNumKey)
	if err != nil || len(rawConf) == 0 {
		tbcontext.WarningF(ctx, "query wordlist fail, err=%v, wordlist=%s, key=%s", err, types.WordlistForumLikeName, types.MaxTopForumNumKey)
		return nil
	}

	// 解析词表配置
	num, err := strconv.ParseInt(rawConf, 10, 64)
	if err != nil || num <= 0 {
		tbcontext.WarningF(ctx, "parse wordlist fail, err=%v, raw config=%s", err, rawConf)
		return nil
	}

	outData.MaxTopForumNum = proto.Int32(int32(num))
	return nil
}

func (a *TopLikeForum) getTopLikeForumBuild(ctx context.Context, staticField *types.ForumGuideStaticField) error {
	if len(staticField.ArrTopLikeForumTmp) == 0 {
		return nil
	}

	likeForumIDs := make([]uint64, 0)
	likeForumList := make([]*clientForumGuide.LikeForum, 0)
	for _, arrForum := range staticField.ArrTopLikeForumTmp {
		likeForum := &clientForumGuide.LikeForum{
			ForumName:    proto.String(arrForum.GetForumName()),
			ForumId:      proto.Uint64(uint64(arrForum.GetForumId())),
			SortValue:    proto.Uint64(uint64(arrForum.GetSortValue())),
			LevelId:      proto.Int32(int32(arrForum.GetLevelId())),
			LevelName:    proto.String(arrForum.GetLevelName()),
			TopSortValue: proto.Uint64(arrForum.GetTopSortValue()),
		}
		likeForumList = append(likeForumList, likeForum)
		likeForumIDs = append(likeForumIDs, uint64(arrForum.GetForumId()))
	}

	err := a.getForumInfoByForumIds(ctx, likeForumIDs, staticField)
	if err != nil {
		return err
	}

	for _, item := range likeForumList {
		item.HotNum = proto.Uint32(0)
		item.Avatar = proto.String("")
		item.MemberCount = proto.Uint32(0)
		item.ThreadNum = proto.Uint32(0)
		item.IsSign = proto.Int32(0)
		if forumHotInfo, ok := staticField.ArrTopForumHotNumList[int64(item.GetForumId())]; ok {
			item.HotNum = proto.Uint32(uint32(forumHotInfo.GetHotValue()))
		}
		if forumInfo, ok := staticField.ArrTopForumBaseInfoList[int64(item.GetForumId())]; ok {
			item.Avatar = proto.String(forumInfo.GetCard().GetAvatar())
			item.MemberCount = proto.Uint32(forumInfo.GetStatistics().GetMemberCount())
			item.ThreadNum = proto.Uint32(forumInfo.GetStatistics().GetThreadNum())
		}
		if isSign, ok := staticField.ArrTopForumSignInfo[int64(item.GetForumId())]; ok {
			item.IsSign = proto.Int32(isSign)
		}
	}

	staticField.ArrTopLikeForumList = likeForumList
	return nil
}

func (a *TopLikeForum) getForumInfoByForumIds(ctx context.Context, likeForumIDs []uint64, staticField *types.ForumGuideStaticField) error {
	if len(likeForumIDs) == 0 {
		return nil
	}

	multi := tbservice.Multi()
	for i := 0; len(likeForumIDs) > i*types.MultiGetHotNumNum; i++ {
		var fids []uint64
		if len(likeForumIDs) > (i+1)*types.MultiGetHotNumNum {
			fids = likeForumIDs[i*types.MultiGetHotNumNum : (i+1)*types.MultiGetHotNumNum]
		} else {
			fids = likeForumIDs[i*types.MultiGetHotNumNum:]
		}

		iStr := strconv.Itoa(i)
		commonParam := &tbservice.Parameter{
			Service: "common",
			Method:  "mgetHotByForumId",
			Input: map[string]interface{}{
				"forum_ids": fids,
			},
			Output: &protoCommon.MgetHotByForumIdRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetHotByForumId"+iStr, commonParam)

		forumParam := &tbservice.Parameter{
			Service: "forum",
			Method:  "mgetBtxInfoEx",
			Input: map[string]interface{}{
				"forum_id": fids,
			},
			Output: &forum.MgetBtxInfoExRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetBtxInfoEx"+iStr, forumParam)

		signParam := &tbservice.Parameter{
			Service: "sign",
			Method:  "getUserSignForums",
			Input: map[string]interface{}{
				"forum_id": fids,
				"user_id":  staticField.UserID,
			},
			Output: &sign.GetUserSignForumsRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getUserSignForums"+iStr, signParam)
	}

	multi.Call(ctx)

	forumHotNumList := make(map[int64]*protoCommon.MgetHotByForumId)
	forumBaseInfoList := make(map[int64]*forum.BtxInfo)
	signInfoList := make(map[int64]int32)
	for i := 0; len(likeForumIDs) > i*types.MultiGetHotNumNum; i++ {
		iStr := strconv.Itoa(i)
		// 并行获取热度值信息
		mgetHotByForumIdInter, err := multi.GetResult(ctx, "mgetHotByForumId"+iStr)
		if err != nil {
			tbcontext.WarningF(ctx, "call common::mgetHotByForumId fail: %v", err)
		} else {
			res := mgetHotByForumIdInter.(*protoCommon.MgetHotByForumIdRes)
			for _, forumInfo := range res.GetOutput() {
				if forumInfo == nil {
					continue
				}
				forumHotNumList[forumInfo.GetForumId()] = forumInfo
			}
		}

		// 获取吧基础信息
		mgetBtxInfoExInter, err := multi.GetResult(ctx, "mgetBtxInfoEx"+iStr)
		if err != nil {
			tbcontext.WarningF(ctx, "call forum::mgetBtxInfoEx fail: %v", err)
		} else {
			res := mgetBtxInfoExInter.(*forum.MgetBtxInfoExRes)
			for fid, forumInfo := range res.GetOutput() {
				if forumInfo == nil {
					continue
				}
				forumBaseInfoList[int64(fid)] = forumInfo
			}
		}

		// 获取吧签到信息
		getUserSignForumsInter, err := multi.GetResult(ctx, "getUserSignForums"+iStr)
		if err != nil {
			tbcontext.WarningF(ctx, "call sign::getUserSignForums fail: %v", err)
		} else {
			res := getUserSignForumsInter.(*sign.GetUserSignForumsRes)
			for fid, signInfo := range res.GetArrUserInfo() {
				if signInfo == nil {
					continue
				}
				signInfoList[int64(fid)] = signInfo.GetIsSignIn()
			}
		}
	}

	staticField.ArrTopForumSignInfo = signInfoList
	staticField.ArrTopForumBaseInfoList = forumBaseInfoList
	staticField.ArrTopForumHotNumList = forumHotNumList
	staticField.BtxTopForumIdx = forumBaseInfoList
	return nil
}

type TopLikeForumOperator struct {
}

func (rdop *TopLikeForumOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientForumGuide.ForumGuideRes
	var baseData *types.CForumGuideBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewTopLikeForum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "TopLikeForum execute fail: %v", err)
		return err
	}

	return nil
}
