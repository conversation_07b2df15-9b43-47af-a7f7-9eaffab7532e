package core

import (
	"context"
	"strconv"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	clientForumGuide "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Prepare struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_guide_prepare", func() engine.Job {
		return &PrepareOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPrepare(ctx *engine.Context) *Prepare {
	return &Prepare{
		ctx: ctx,
	}
}

func (p *Prepare) IsValid(ctx context.Context, baseData *types.CForumGuideBaseData) bool {
	return true
}

func (p *Prepare) Execute(ctx context.Context, outData *clientForumGuide.ForumGuideRes, baseData *types.CForumGuideBaseData) error {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	staticField.UserID = common.Tvttt(objReq.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	staticField.IntClientType = common.Tvttt(objReq.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	staticField.StrClientVersion = common.Tvttt(objReq.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	staticField.Cuid = common.Tvttt(objReq.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)

	sampleID := common.Tvttt(objReq.GetCommonAttr("sample_id", ""), common.TTT_STRING).(string)
	staticField.SampleIDs = UbsAbtest.GetUbsAbtestSid(ctx, sampleID, strconv.FormatInt(staticField.UserID, 10), "")

	// 12.56.5及以上，适用进吧tab重构
	if clientvers.Compare("12.56.4", staticField.StrClientVersion) >= 0 {
		staticField.IsRefactor = true
	}

	return nil
}

type PrepareOperator struct {
}

func (op *PrepareOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientForumGuide.ForumGuideRes
	var baseData *types.CForumGuideBaseData
	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)

	webCtx := ctx.CallerCtx()
	obj := NewPrepare(ctx)
	if !obj.IsValid(webCtx, baseData) {
		return nil
	}

	err := obj.Execute(webCtx, outData, baseData)
	if err != nil {
		tbcontext.WarningF(webCtx, "prepare execute fail, err=%v", err)
		return err
	}
	return nil
}
