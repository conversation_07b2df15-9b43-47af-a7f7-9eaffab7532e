package core

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	clientForumGuide "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/forumGuide"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type HotSearch struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("forum_guide_hot_search", func() engine.Job {
		return &HotSearchOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewHotSearch(ctx *engine.Context) *HotSearch {
	return &HotSearch{
		ctx: ctx,
	}
}
func (a *HotSearch) IsValid(ctx context.Context, baseData *types.CForumGuideBaseData) bool {
	if baseData.Request.GetCallFrom() == types.LikeForumTabCallForm {
		// 我关注的吧页面不需要这个逻辑
		return false
	}
	return true
}
func (a *HotSearch) Execute(ctx context.Context, outData *clientForumGuide.ForumGuideRes, baseData *types.CForumGuideBaseData) error {

	staticField := baseData.StaticField

	redisOut, err := resource.CacheSign.Get(ctx, types.HotSearchKey)
	if err != nil {
		tbcontext.FatalF(ctx, "call redis failed. key: %s, err: %v", types.HotSearchKey, err)
		baseData.BaseObj.Error(tiebaerror.ERR_MO_INTERNAL_ERROR, tiebaerror.GetErrMsg(tiebaerror.ERR_MO_INTERNAL_ERROR), true)
		return errors.New("get hot search failed")
	}

	if redisOut == "" {
		return nil
	}

	hotSearchFids := make([]int64, 0)
	err = jsoniter.Unmarshal([]byte(redisOut), &hotSearchFids)
	if err != nil {
		tbcontext.WarningF(ctx, "json unmarshal fail: %v", err)
		return nil
	}

	staticField.ArrHotSearch = getForumList(ctx, hotSearchFids)

	return nil
}

type HotSearchOperator struct {
}

func (rdop *HotSearchOperator) DoImpl(ctx *engine.Context) error {
	var outData *clientForumGuide.ForumGuideRes
	var baseData *types.CForumGuideBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewHotSearch(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "HotSearch execute fail: %v", err)
		return err
	}

	return nil
}

func getForumList(ctx context.Context, hotSearchFids []int64) []*clientForumGuide.HotSearch {
	multi := tbservice.Multi()
	for i := 0; len(hotSearchFids) > i*types.BtxHotSearchCall; i++ {
		var fids []int64
		if len(hotSearchFids) > (i+1)*types.BtxHotSearchCall {
			fids = hotSearchFids[i*types.BtxHotSearchCall : (i+1)*types.BtxHotSearchCall]
		} else {
			fids = hotSearchFids[i*types.BtxHotSearchCall:]
		}

		arrParam := &tbservice.Parameter{
			Service: "forum",
			Method:  "mgetBtxInfoEx",
			Input: map[string]interface{}{
				"forum_id": fids,
			},
			Output: &forum.MgetBtxInfoExRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetBtxInfoEx"+strconv.Itoa(i), arrParam)
	}

	multi.Call(ctx)

	arrForumInfoList := make(map[uint32]*clientForumGuide.HotSearch)
	for i := 0; len(hotSearchFids) > i*types.BtxHotSearchCall; i++ {
		resInter, err := multi.GetResult(ctx, "mgetBtxInfoEx"+strconv.Itoa(i))
		if err != nil {
			tbcontext.WarningF(ctx, "call forum::mgetBtxInfoEx fail: %v", err)
			continue
		}
		res := resInter.(*forum.MgetBtxInfoExRes)
		for fid, forumInfo := range res.GetOutput() {
			if forumInfo == nil {
				continue
			}
			arrForumInfoList[fid] = &clientForumGuide.HotSearch{
				ForumId:   proto.Uint64(uint64(forumInfo.GetForumName().GetForumId())),
				ForumName: proto.String(forumInfo.GetForumName().GetForumName()),
				Slogan:    proto.String(forumInfo.GetCard().GetSlogan()),
			}
		}
	}

	if len(arrForumInfoList) == 0 {
		return nil
	}

	sliceForumList := make([]*clientForumGuide.HotSearch, 0)
	i := 0
	for len(sliceForumList) < 10 {
		if i >= len(hotSearchFids) {
			break
		}

		fid := hotSearchFids[i]
		i++
		if arrForumInfoList[uint32(fid)] == nil {
			continue
		}

		sliceForumList = append(sliceForumList, arrForumInfoList[uint32(fid)])
	}

	for k, v := range sliceForumList {
		sliceForumList[k].SearchTitle = proto.String(fmt.Sprintf(types.HotSearchTitlePrefix, k+1, v.GetForumName()))
	}

	return sliceForumList
}
