package core

import (
	"context"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/stlog"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbrichtext"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/itemPage"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Response struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("item_page_response", func() engine.Job {
		return &ResponseOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewResponse(ctx *engine.Context) *Response {
	return &Response{
		ctx: ctx,
	}
}
func (a *Response) IsValid(ctx context.Context, baseData *types.CItemPageBaseData) bool {

	return true
}
func (a *Response) Execute(ctx context.Context, outData *itemPage.ItemPageRes, baseData *types.CItemPageBaseData) error {
	staticField := baseData.StaticField

	if staticField.ItemGameInfo != nil {
		outData.ItemGameInfo = staticField.ItemGameInfo
	}

	outData.HasTornado = proto.Int32(staticField.HasTornado)

	if staticField.ItemInfo.GetApkName() != "" && staticField.ItemInfo.GetApkDetail() != nil {
		stlog.AddLog(ctx, "item_pkg_source", staticField.ItemInfo.GetApkDetail().GetPkgSource())
		stlog.AddLog(ctx, "item_pkg_name", staticField.ItemInfo.GetApkName())
	}

	outData.ItemInfo = staticField.ItemInfo
	outData.Link = proto.String(staticField.Link)
	outData.AlbumList = staticField.AlbumList
	outData.ThreadList = make([]*client.ThreadInfo, 0)
	if len(staticField.RecommendItem) > staticField.IntMinRecomItem {
		outData.RecommendItem = staticField.RecommendItem
	}

	for _, threadInfo := range staticField.ThreadList {
		if threadInfo.ClientThreadInfo != nil {
			// 图片origin_src加上图片鉴权串
			for _, abs := range threadInfo.ClientThreadInfo.GetRichAbstract() {
				if abs.GetType() == tbrichtext.SLOT_TYPE_IMG && abs.GetOriginSrc() != "" {
					tmpSrc, err := image.GenAuthUrl(abs.GetSrc())
					if err == nil {
						abs.Src = proto.String(tmpSrc)
					}
				}
			}

			for _, fc := range threadInfo.ClientThreadInfo.GetFirstPostContent() {
				if fc.GetType() == tbrichtext.SLOT_TYPE_IMG && fc.GetOriginSrc() != "" {
					tmpSrc, err := image.GenAuthUrl(fc.GetSrc())
					if err == nil {
						fc.Src = proto.String(tmpSrc)
					}
				}
			}
			outData.ThreadList = append(outData.ThreadList, threadInfo.ClientThreadInfo)
		}
	}
	outData.DiscussionList = make([]*client.ThreadInfo, 0)
	for _, threadInfo := range staticField.DiscussionList {
		if threadInfo.ClientThreadInfo != nil {
			// 图片origin_src加上图片鉴权串
			for _, abs := range threadInfo.ClientThreadInfo.GetRichAbstract() {
				if abs.GetType() == tbrichtext.SLOT_TYPE_IMG && abs.GetOriginSrc() != "" {
					tmpSrc, err := image.GenAuthUrl(abs.GetSrc())
					if err == nil {
						abs.Src = proto.String(tmpSrc)
					}
				}
			}

			for _, fc := range threadInfo.ClientThreadInfo.GetFirstPostContent() {
				if fc.GetType() == tbrichtext.SLOT_TYPE_IMG && fc.GetOriginSrc() != "" {
					tmpSrc, err := image.GenAuthUrl(fc.GetSrc())
					if err == nil {
						fc.Src = proto.String(tmpSrc)
					}
				}
			}
			outData.DiscussionList = append(outData.DiscussionList, threadInfo.ClientThreadInfo)
		}
	}
	outData.DiscussionId = proto.Uint32(uint32(staticField.DiscussId))

	if len(staticField.RecommmendForum) > staticField.IntMinRecomForum {
		outData.RecommendForum = staticField.RecommmendForum
	}
	if clientvers.Is12_9Version(staticField.IntClientType, staticField.StrClientVersion) {
		if outData.ItemGameInfo == nil {
			outData.ItemGameInfo = new(client.ItemGameInfo)
		}
		common.StructAToStructBCtx(ctx, staticField.HotVideo, &outData.ItemGameInfo.HotVideos)
	}

	if clientvers.Is12_13Version(staticField.IntClientType, staticField.StrClientVersion) {
		outData.ItemGameCode = &client.ItemGameCode{
			UnclaimedNum: proto.Int32(staticField.UnclaimedNum),
			GameCodeList: staticField.GameCodeList,
		}
	}


	return nil
}

type ResponseOperator struct {
}

func (rdop *ResponseOperator) DoImpl(ctx *engine.Context) error {
	var outData *itemPage.ItemPageRes
	var baseData *types.CItemPageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewResponse(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "response execute fail: %v", err)
		return err
	}

	return nil
}
