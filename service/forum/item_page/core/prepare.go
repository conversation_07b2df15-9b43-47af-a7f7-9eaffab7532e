package core

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/itemPage"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Prepare struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("item_page_prepare", func() engine.Job {
		return &PrepareOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPrepare(ctx *engine.Context) *Prepare {
	return &Prepare{
		ctx: ctx,
	}
}
func (a *Prepare) IsValid(ctx context.Context, baseData *types.CItemPageBaseData) bool {

	return true
}
func (a *Prepare) Execute(ctx context.Context, outData *itemPage.ItemPageRes, baseData *types.CItemPageBaseData) error {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest
	staticField.IntUid = common.Tvttt(objReq.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	staticField.IntClientType = common.Tvttt(objReq.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	staticField.StrClientVersion = common.Tvttt(objReq.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	staticField.ItemId = common.Tvttt(objReq.GetPrivateAttr("item_id", 0), common.TTT_UINT32).(uint32)
	staticField.IntScreenWidth = common.Tvttt(objReq.GetPrivateAttr("scr_w", 1280), common.TTT_INT32).(int32)
	staticField.IntScreenHeight = common.Tvttt(objReq.GetPrivateAttr("scr_h", 1920), common.TTT_INT32).(int32)
	staticField.IntScreenDip = common.Tvttt(objReq.GetPrivateAttr("scr_dip", 3), common.TTT_FLOAT64).(float64)
	staticField.IntQType = common.Tvttt(objReq.GetPrivateAttr("q_type", 0), common.TTT_INT32).(int32)

	return nil
}

type PrepareOperator struct {
}

func (rdop *PrepareOperator) DoImpl(ctx *engine.Context) error {
	var outData *itemPage.ItemPageRes
	var baseData *types.CItemPageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewPrepare(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "prepare execute fail: %v", err)
		return err
	}

	return nil
}
