package core

import (
	"context"
	"errors"
	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/threadlist"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	utilCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/itemPage"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"math"
)

type ItemInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("item_page_item_info", func() engine.Job {
		return &ItemInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewItemInfo(ctx *engine.Context) *ItemInfo {
	return &ItemInfo{
		ctx: ctx,
	}
}
func (a *ItemInfo) IsValid(ctx context.Context, baseData *types.CItemPageBaseData) bool {

	return true
}
func (a *ItemInfo) Execute(ctx context.Context, outData *itemPage.ItemPageRes, baseData *types.CItemPageBaseData) error {
	staticField := baseData.StaticField

	multi := tbservice.Multi()

	mgetItemInfoByIDParam := &tbservice.Parameter{
		Service: "common",
		Method:  "mgetItemInfoByID",
		Input: map[string]interface{}{
			"item_ids":       []uint32{staticField.ItemId},
			"score_detail":   1,
			"sort_tag":       1,
			"need_album_wh":  1, //需要宽/高信息
			"client_type":    staticField.IntClientType,
			"client_version": staticField.StrClientVersion,
			"user_id":        staticField.IntUid,
			"call_from":      "item_detail", //来源 详情tab 展示button
		},
		Output: &common.MgetItemInfoByIDRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetItemInfoByID", mgetItemInfoByIDParam)

	if staticField.IntUid > 0 {
		getCommentParam := &tbservice.Parameter{
			Service: "common",
			Method:  "getComment",
			Input: map[string]interface{}{
				"uid":     staticField.IntUid,
				"item_id": staticField.ItemId,
			},
			Output: &common.GetCommentRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getComment", getCommentParam)
	}

	getItemRankNumParam := &tbservice.Parameter{
		Service: "common",
		Method:  "getItemRankNum",
		Input: map[string]interface{}{
			"item_id": staticField.ItemId,
		},
		Output: &common.GetItemRankNumRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getItemRankNum", getItemRankNumParam)

	//最近更新注册
	if clientvers.Is12_10Version(staticField.IntClientType, staticField.StrClientVersion) {
		getLastUpdateInfoParam := &tbservice.Parameter{
			Service: "common",
			Method:  "getLastUpdateInfo",
			Input: map[string]interface{}{
				"item_id": staticField.ItemId,
				"from":    staticField.IntClientType,
			},
			Output: &common.GetLastUpdateInfoRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getLastUpdateInfo", getLastUpdateInfoParam)
	}

	getItemTornadoStatusParam := &tbservice.Parameter{
		Service: "common",
		Method:  "getItemTornadoStatus",
		Input: map[string]interface{}{
			"item_id": staticField.ItemId,
		},
		Output: &common.GetItemTornadoStatusRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getItemTornadoStatus", getItemTornadoStatusParam)

	multi.Call(ctx)

	getItemInfoInter, err := multi.GetResult(ctx, "mgetItemInfoByID")
	getItemInfoRes := getItemInfoInter.(*common.MgetItemInfoByIDRes)
	if err != nil || getItemInfoRes.Errno == nil || getItemInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call common::mgetItemInfoByID fail, output: %v, err: %v", utilCommon.ToString(getItemInfoRes), err)
		baseData.BaseObj.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, tiebaerror.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return errors.New("call common::mgetItemInfoByID fail")
	}

	staticField.ArrItemInfo = getItemInfoRes.GetOutput()[int64(staticField.ItemId)]
	arrItemOutput := getItemInfoRes.GetOutput()[int64(staticField.ItemId)]

	var arrCommentOutput common.GetComment
	if staticField.IntUid > 0 {
		getCommentInter, err := multi.GetResult(ctx, "getComment")
		getCommentRes := getCommentInter.(*common.GetCommentRes)
		if err != nil || getCommentRes.Errno != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call common::getComment fail, output: %v, err: %v", utilCommon.ToString(getCommentRes), err)
		} else {
			arrCommentOutput = getCommentRes.Output
		}
	}

	itemConfigStr := arrItemOutput.GetConfig()
	itemConfig := new(common.ItemConfig)
	err = jsoniter.Unmarshal([]byte(itemConfigStr), itemConfig)
	if err != nil {
		tbcontext.WarningF(ctx, "json unmarshal fail, err: %v", err)
	}

	arrOutput := threadlist.FormatItemInfo(ctx, arrItemOutput, arrCommentOutput)
	if arrOutput.TagInfo == nil {
		arrOutput.TagInfo = make([]*client.TagInfo, 0)
	}
	//跳榜单页的参数
	for _, v := range arrItemOutput.GetTagInfo() {
		arrOutput.TagInfo = append(arrOutput.TagInfo, &client.TagInfo{
			RankType: proto.Int32(3),
			ClassId:  proto.Int32(arrItemOutput.GetClassId()),
			TabId:    proto.Int32(int32(v.GetTagId())),
			TagName:  proto.String(v.GetTagName()),
		})
	}

	arrOutput.TemplateName = proto.String(arrItemOutput.GetTemplateName())
	arrOutput.Category1 = proto.String(arrItemOutput.GetCategory1())
	arrOutput.Category2 = proto.String(arrItemOutput.GetCategory2())

	/* 填充默认值，Molib_Util_ThreadList_Tools::formatItemInfo没有填充默认值
	 *  当前版本v11.8中/c/f/forum/itemPage为短连接，故json没有默认值；
	 *  其他使用Molib_Util_ThreadList_Tools::formatItemInfo的ui接口为proto，
	 *  会自动填充默认值；
	 *  可选方案：可更改Molib_Util_ThreadList_Tools::formatItemInfo，自动填充默认值；
	 */
	if arrOutput.Score == nil || arrOutput.Score.IsCommented == nil {
		if arrOutput.Score == nil {
			arrOutput.Score = new(client.ItemTable)
		}
		arrOutput.Score.IsCommented = proto.Int32(0)
		arrOutput.Score.CommentStar = proto.Int32(0)
	}

	//智能取色
	arrColor := make([]*common.SmartColor, 0)
	err = jsoniter.Unmarshal([]byte(arrItemOutput.GetSmartColor()), &arrColor)
	if err != nil {
		tbcontext.WarningF(ctx, "json unmarshal fail, err: %v", err)
	} else {
		if len(arrColor) > 0 {
			arrOutput.ThemeColor = &client.ItemThemeColor{
				Day: &client.ItemThemeColorElement{
					TopColor:        proto.String(arrColor[0].GetGradientColor().GetValue()),
					BottomColor:     proto.String(arrColor[0].GetGradientColorDark().GetValue()),
					EditButtonColor: proto.String(arrColor[0].GetButtonColor().GetValue()),
				},
			}
		}
	}

	//榜单挂载
	getItemRankNumInter, err := multi.GetResult(ctx, "getItemRankNum")
	getItemRankNumRes := getItemRankNumInter.(*common.GetItemRankNumRes)
	if err != nil || getItemRankNumRes.Errno == nil || getItemRankNumRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call common::getItemRankNum fail, output: %v, err: %v", utilCommon.ToString(getItemRankNumRes), err)
	} else {
		if getItemRankNumRes.Data != nil && getItemRankNumRes.GetData().RankNum != nil {
			arrOutput.Ranking = &client.Ranking{
				Name:    proto.String(getItemRankNumRes.GetData().GetName()),
				RankNum: proto.Int32(getItemRankNumRes.GetData().GetRankNum()),
				RankingParam: &client.RankingParam{
					RankType: proto.Int32(getItemRankNumRes.GetData().GetConfType()),
					RankCode: proto.Int32(int32(getItemRankNumRes.GetData().GetConfId())),
					SortType: proto.String(getItemRankNumRes.GetData().GetSortType()),
					TabId:    proto.Int32(int32(getItemRankNumRes.GetData().GetTabId())),
					Pn:       proto.Int32(int32(math.Ceil(float64(getItemRankNumRes.GetData().GetRankNum()) / 20))),
					Rn:       proto.Int32(20),
				},
			}

			// >12.6.5使用长链接，key不能用驼峰 todo 需要确认
			//if clientvers.Is12_6_BVersion(staticField.IntClientType, staticField.StrClientVersion) {
			//
			//}
		}
	}

	//可选字段,
	itemOptions := arrItemOutput.GetExtra()
	if clientvers.Compare("12.3.0", staticField.StrClientVersion) >= 0 {
		if itemConfig.GetTemplateFields() != nil && itemConfig.GetTemplateFields().Intro != nil && itemOptions.GetIntro() != "" {
			for k := range itemConfig.GetTemplateFields().GetIntro() {
				if optionMap, ok := itemOptions.GetIntro().(map[string]interface{}); ok {
					if _, ok := optionMap[k]; ok {
						staticField.Intro = append(staticField.Intro, optionMap[k])
					}
				}
			}
		}
	}

	// 详情
	if itemConfig.GetTemplateFields() != nil && itemConfig.GetTemplateFields().Detail != nil && itemOptions.GetDetail() != nil {
		detailMap := make(map[string]interface{})
		utilCommon.StructAToStructBCtx(ctx, itemOptions.GetDetail(), &detailMap)
		for k, v := range detailMap {
			vStr := utilCommon.Tvttt(v, utilCommon.TTT_STRING).(string)
			if vStr != "" && itemConfig.GetTemplateFields().GetDetail()[k] != "" {
				staticField.Details = append(staticField.Details, &client.ItemDetail{
					Name:  proto.String(itemConfig.GetTemplateFields().GetDetail()[k]),
					Value: proto.String(vStr),
				})
			}
		}
	}

	for _, v := range itemOptions.GetService() {
		if int32(staticField.IntClientType) != v.GetClientType() && v.GetClientType() != 0 {
			continue
		}
		if clientvers.Is12_6_BVersion(staticField.IntClientType, staticField.StrClientVersion) {
			if v.GetName() == "预约" && v.GetUrl() == "" {
				continue
			}
		}

		staticField.Services = append(staticField.Services, &client.ItemService{
			Name:       proto.String(v.GetName()),
			Icon:       proto.String(v.GetIcon()),
			ClientType: proto.Int32(v.GetClientType()),
			Type:       proto.Int32(v.GetType()),
			Url:        proto.String(v.GetUrl()),
			AppId:      proto.Int32(v.GetAppId()),
		})
	}

	staticField.Link = itemOptions.GetLink()
	ablumList := make([]*client.AlbumElement, 0)
	ablum := itemOptions.GetAlbum()
	if videoMap, ok := ablum.GetVideo().([]interface{}); ok {
		for _, v := range videoMap {
			if vMap, ok := v.(map[string]interface{}); ok {
				thumbUrl, _ := image.GenAuthUrl(utilCommon.Tvttt(vMap["thumb_url"], utilCommon.TTT_STRING).(string))
				ablumList = append(ablumList, &client.AlbumElement{
					AlbumUrl:      proto.String(utilCommon.Tvttt(vMap["url"], utilCommon.TTT_STRING).(string)),
					AlbumType:     proto.Uint32(1),
					AlbumThumbUrl: proto.String(thumbUrl),
				})
			}
		}
	}

	if imageMap, ok := ablum.GetImage().([]interface{}); ok {
		for _, v := range imageMap {
			if vMap, ok := v.(map[string]interface{}); ok {
				ablumList = append(ablumList, &client.AlbumElement{
					AlbumUrl:      proto.String(utilCommon.Tvttt(vMap["url"], utilCommon.TTT_STRING).(string)),
					AlbumType:     proto.Uint32(0),
					AlbumThumbUrl: proto.String(utilCommon.Tvttt(vMap["thumb_url"], utilCommon.TTT_STRING).(string)),
					AlbumHeight:   proto.Uint32(utilCommon.Tvttt(vMap["height"], utilCommon.TTT_UINT32).(uint32)),
					AlbumWidth:    proto.Uint32(utilCommon.Tvttt(vMap["width"], utilCommon.TTT_UINT32).(uint32)),
				})
			}
		}
	}

	staticField.AlbumList = ablumList

	//最近更新获取
	utilCommon.StructAToStructBCtx(ctx, arrItemOutput.GetItemGameInfo(), staticField.ItemGameInfo)
	if clientvers.Is12_10Version(staticField.IntClientType, staticField.StrClientVersion) {
		getLastUpdateInfoInter, err := multi.GetResult(ctx, "getLastUpdateInfo")
		getLastUpdateInfoRes := getLastUpdateInfoInter.(*common.GetLastUpdateInfoRes)
		if err != nil || getLastUpdateInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call common::getLastUpdateInfo fail, output: %v, err: %v", utilCommon.ToString(getLastUpdateInfoRes), err)
		} else {
			staticField.ItemGameInfo.RecentUpdate = &client.RecentUpdate{
				Version:    proto.String(getLastUpdateInfoRes.GetOutput().GetVersion()),
				Log:        proto.String(getLastUpdateInfoRes.GetOutput().GetLog()),
				UpdateTime: proto.String(getLastUpdateInfoRes.GetOutput().GetUpdateTime()),
			}
		}
	}

	// 查询当前item是否可被加速
	getItemTornadoStatusInter, err := multi.GetResult(ctx, "getItemTornadoStatus")
	getItemTornadoStatusRes := getItemTornadoStatusInter.(*common.GetItemTornadoStatusRes)
	if err != nil || getItemTornadoStatusRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "call common::getItemTornadoStatus fail, output: %v, err: %v", utilCommon.ToString(getItemTornadoStatusRes), err)
		baseData.BaseObj.Error(tiebaerror.ERR_CALL_SERVICE_FAIL, tiebaerror.GetErrMsg(tiebaerror.ERR_CALL_SERVICE_FAIL), false)
		return nil
	}

	if getItemTornadoStatusRes.GetData().GetItemTornadoStatus() == 1 {
		staticField.HasTornado = 1
	}

	staticField.ItemInfo = arrOutput

	return nil
}

type ItemInfoOperator struct {
}

func (rdop *ItemInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *itemPage.ItemPageRes
	var baseData *types.CItemPageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewItemInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "item_info execute fail: %v", err)
		return err
	}

	return nil
}
