package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	protoCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/itemPage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"reflect"
)

type HotVideo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("item_page_hot_video", func() engine.Job {
		return &HotVideoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewHotVideo(ctx *engine.Context) *HotVideo {
	return &HotVideo{
		ctx: ctx,
	}
}
func (a *HotVideo) IsValid(ctx context.Context, baseData *types.CItemPageBaseData) bool {
	return true
}
func (a *HotVideo) Execute(ctx context.Context, outData *itemPage.ItemPageRes, baseData *types.CItemPageBaseData) error {
	staticField := baseData.StaticField
	if clientvers.Is12_9Version(staticField.IntClientType, staticField.StrClientVersion) {
		input := map[string]interface{}{
			"name": staticField.ArrItemInfo.GetItemName(),
			"ie":   "utf-8",
		}
		res := new(protoCommon.GetHotVideoByNameRes)
		err := tbservice.Call(ctx, "common", "getHotVideosByName", input, res, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service common:getHotVideosByName, err = %v, input = %v, output = %v", err, input, res)
			return nil
		}

		changeFieldType := []string{"rich_title", "voice_info", "video_channel_info", "poll_info", "livecover_src", "pb_link_info",
			"yule_post_activity", "ala_info", "rich_abstract", "animation_info", "zan", "app_code", "item_star", "location", "skin_info",
			"voice_room", "cartoon_info", "pb_goods_info", "media"}

		if threadList, ok := res.GetOutput().GetThreadList().([]interface{}); ok {
			for _, v := range threadList {
				if vMap, ok := v.(map[string]interface{}); ok {
					for _, field := range changeFieldType {
						if reflect.TypeOf(vMap[field]).Kind() == reflect.String {
							vMap[field] = nil
						}
					}
					if _, ok := vMap["thread_types"]; ok {
						vMap["thread_types"] = common.Tvttt(vMap["thread_types"], common.TTT_UINT32)
					}
					if author, ok := vMap["author"]; ok {
						if authorMap, ok := author.(map[string]interface{}); ok {
							if _, ok := authorMap["vipInfo"]; ok {
								if vipInfoMap, ok := authorMap["vipInfo"].(map[string]interface{}); ok {
									vLevel := common.Tvttt(vipInfoMap["v_level"], common.TTT_INT32).(int32)
									if vLevel < 0 {
										vipInfoMap["v_level"] = 0
									}
								}
							}
						}
					}

				}
			}
		}

		staticField.HotVideo = res.GetOutput().GetThreadList()
	}

	return nil
}

type HotVideoOperator struct {
}

func (rdop *HotVideoOperator) DoImpl(ctx *engine.Context) error {
	var outData *itemPage.ItemPageRes
	var baseData *types.CItemPageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewHotVideo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "hot_video execute fail: %v", err)
		return err
	}

	return nil
}
