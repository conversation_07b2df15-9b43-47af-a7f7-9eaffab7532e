package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	protoCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/itemPage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Discuss struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("item_page_discuss", func() engine.Job {
		return &DiscussOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewDiscuss(ctx *engine.Context) *Discuss {
	return &Discuss{
		ctx: ctx,
	}
}
func (a *Discuss) IsValid(ctx context.Context, baseData *types.CItemPageBaseData) bool {
	return true
}
func (a *Discuss) Execute(ctx context.Context, outData *itemPage.ItemPageRes, baseData *types.CItemPageBaseData) error {
	staticField := baseData.StaticField
	input := map[string]interface{}{
		"item_id": staticField.ItemId,
	}
	getDiscussionByIdRes := new(protoCommon.GetDiscussionByIdRes)
	err := tbservice.Call(ctx, "common", "getDiscussionById", input, getDiscussionByIdRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getDiscussionByIdRes.Errno == nil || getDiscussionByIdRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service common:getDiscussionById, err = %v, input = %v, output = %v", err, input, getDiscussionByIdRes)
		return nil
	}

	if getDiscussionByIdRes.GetData() > 0 {
		staticField.DiscussId = getDiscussionByIdRes.GetData()
		staticField.DiscussionList = getHotThread(ctx, getDiscussionByIdRes.GetData(), baseData)
	}

	return nil
}

type DiscussOperator struct {
}

func (rdop *DiscussOperator) DoImpl(ctx *engine.Context) error {
	var outData *itemPage.ItemPageRes
	var baseData *types.CItemPageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewDiscuss(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "discuss execute fail: %v", err)
		return err
	}

	return nil
}
