package ext

import (
	"context"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	protoCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/itemPage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

//激活码领取状态
const NOT_CLAIMED_STATUS = 1  //可领取
const CLAIMED_STATUS = 2      //已领取
const NOT_REMAINED_STATUS = 3 //已领完

//码类型
const CODE_TYPE_ACTIVE = 1 //激活码
const CODE_TYPE_GIFT = 2   //礼包码

type GameCode struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("item_page_game_code", func() engine.Job {
		return &GameCodeOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewGameCode(ctx *engine.Context) *GameCode {
	return &GameCode{
		ctx: ctx,
	}
}
func (a *GameCode) IsValid(ctx context.Context, baseData *types.CItemPageBaseData) bool {
	return true
}
func (a *GameCode) Execute(ctx context.Context, outData *itemPage.ItemPageRes, baseData *types.CItemPageBaseData) error {
	staticField := baseData.StaticField
	if clientvers.Is12_13Version(staticField.IntClientType, staticField.StrClientVersion) {
		input := map[string]interface{}{
			"item_id": staticField.ItemId,
			"user_id": staticField.IntUid,
		}
		res := new(protoCommon.GetOnlineGameCodeRes)
		err := tbservice.Call(ctx, "common", "getOnlineGameCode", input, res, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service common:getOnlineGameCode, err = %v, input = %v, output = %v", err, input, res)
			return nil
		}

		if len(res.GetOutput()) == 0 {
			return nil
		}

		//遍历 分组排序 可领取的1>已领取2>已领完3
		notClaim := make([]*client.GameCodeList, 0)
		claimed := make([]*client.GameCodeList, 0)
		notRemain := make([]*client.GameCodeList, 0)

		intUnclaimedNum := 0
		for listId, item := range res.GetOutput() {
			temp := new(client.GameCodeList)
			temp.ListId = proto.Int32(int32(listId))
			temp.Type = proto.Int32(item.GetType())
			temp.Label = proto.String(item.GetLabel())

			//如果type=1 激活码 且label为 / 的话 label默认为"激活码"
			if temp.GetType() == CODE_TYPE_ACTIVE && temp.GetLabel() == "/" {

			}
			temp.BeginTime = proto.Int32(int32(item.GetBeginTime()))
			temp.EndTime = proto.Int32(int32(item.GetEndTime()))
			temp.ItemId = proto.Int32(int32(item.GetItemId()))
			temp.ClaimedStatus = proto.Int32(item.GetClaimedStatus())

			if temp.GetClaimedStatus() == NOT_CLAIMED_STATUS {
				intUnclaimedNum++
				notClaim = append(notClaim, temp)
			}

			if temp.GetClaimedStatus() == CLAIMED_STATUS {
				temp.ClaimedValue = proto.String(item.GetClaimedValue())
				claimed = append(claimed, temp)
			}

			if temp.GetClaimedStatus() == NOT_REMAINED_STATUS {
				notRemain = append(notRemain, temp)
			}
		}

		staticField.UnclaimedNum = int32(intUnclaimedNum)
		staticField.GameCodeList = append(staticField.GameCodeList, notClaim...)
		staticField.GameCodeList = append(staticField.GameCodeList, claimed...)
		staticField.GameCodeList = append(staticField.GameCodeList, notRemain...)
	}

	return nil
}

type GameCodeOperator struct {
}

func (rdop *GameCodeOperator) DoImpl(ctx *engine.Context) error {
	var outData *itemPage.ItemPageRes
	var baseData *types.CItemPageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewGameCode(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "game_code execute fail: %v", err)
		return err
	}

	return nil
}
