package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	protoCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/itemPage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type RecomForum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("item_page_recom_forum", func() engine.Job {
		return &RecomForumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewRecomForum(ctx *engine.Context) *RecomForum {
	return &RecomForum{
		ctx: ctx,
	}
}
func (a *RecomForum) IsValid(ctx context.Context, baseData *types.CItemPageBaseData) bool {
	return true
}
func (a *RecomForum) Execute(ctx context.Context, outData *itemPage.ItemPageRes, baseData *types.CItemPageBaseData) error {
	staticField := baseData.StaticField
	input := map[string]interface{}{
		"item_id":   staticField.ItemId,
		"item_name": staticField.ArrItemInfo.GetItemName(),
		"user_id":   staticField.IntUid,
	}
	res := new(protoCommon.RecomForumByItemRes)
	err := tbservice.Call(ctx, "common", "recomForumByItem", input, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service common:recomForumByItem, err = %v, input = %v, output = %v", err, input, res)
		return nil
	}

	common.StructAToStructBCtx(ctx, res.GetData(), &staticField.RecommmendForum)

	return nil
}

type RecomForumOperator struct {
}

func (rdop *RecomForumOperator) DoImpl(ctx *engine.Context) error {
	var outData *itemPage.ItemPageRes
	var baseData *types.CItemPageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewRecomForum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "recom_forum execute fail: %v", err)
		return err
	}

	return nil
}
