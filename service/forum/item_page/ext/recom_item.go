package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/threadlist"
	protoCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/itemPage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type RecomItem struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("item_page_recom_item", func() engine.Job {
		return &RecomItemOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewRecomItem(ctx *engine.Context) *RecomItem {
	return &RecomItem{
		ctx: ctx,
	}
}
func (a *RecomItem) IsValid(ctx context.Context, baseData *types.CItemPageBaseData) bool {
	staticField := baseData.StaticField
	if staticField.ItemInfo.GetTemplateName() == "" {
		return false
	}
	return true
}
func (a *RecomItem) Execute(ctx context.Context, outData *itemPage.ItemPageRes, baseData *types.CItemPageBaseData) error {
	staticField := baseData.StaticField
	input := map[string]interface{}{
		"item_id":       staticField.ItemId,
		"template_name": staticField.ItemInfo.GetTemplateName(),
	}

	res := new(protoCommon.RecomItemByTplNameRes)
	err := tbservice.Call(ctx, "common", "recomItemByUtf8TplName", input, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service common:recomItemByUtf8TplName, err = %v, input = %v, output = %v", err, input, res)
		return nil
	}

	for _, v := range res.GetData() {
		staticField.RecommendItem = append(staticField.RecommendItem, threadlist.FormatItemInfo(ctx, v, protoCommon.GetComment{}))
	}

	return nil
}

type RecomItemOperator struct {
}

func (rdop *RecomItemOperator) DoImpl(ctx *engine.Context) error {
	var outData *itemPage.ItemPageRes
	var baseData *types.CItemPageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewRecomItem(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "recom_item execute fail: %v", err)
		return err
	}

	return nil
}
