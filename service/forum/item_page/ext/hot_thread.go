package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/img"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/threadlist"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/metadata"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/cmap"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbrichtext"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/itemPage"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type HotThread struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("item_page_hot_thread", func() engine.Job {
		return &HotThreadOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewHotThread(ctx *engine.Context) *HotThread {
	return &HotThread{
		ctx: ctx,
	}
}
func (a *HotThread) IsValid(ctx context.Context, baseData *types.CItemPageBaseData) bool {
	return true
}
func (a *HotThread) Execute(ctx context.Context, outData *itemPage.ItemPageRes, baseData *types.CItemPageBaseData) error {
	staticField := baseData.StaticField
	staticField.ThreadList = getHotThread(ctx, int64(staticField.ItemId), baseData)
	return nil
}

type HotThreadOperator struct {
}

func (rdop *HotThreadOperator) DoImpl(ctx *engine.Context) error {
	var outData *itemPage.ItemPageRes
	var baseData *types.CItemPageBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewHotThread(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "hot_thread execute fail: %v", err)
		return err
	}

	return nil
}


func getHotThread(ctx context.Context, itemId int64, baseData *types.CItemPageBaseData) []*metadata.ThreadInfoOutWithThreadTypes {
	staticField := baseData.StaticField
	input := map[string]interface{}{
		"tab_id":   itemId,
		"forum_id": itemId,
		"offset":   0,
		"res_num":  staticField.IntGetMFrsResNum,
		"order_by": staticField.SortType,
		"order":    "desc",
	}
	res := new(frs.GetMFrsRes)
	err := tbservice.Call(ctx, "post", "getMFrs", input, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service post:getMFrs, err = %v, input = %v, output = %v", err, input, res)
		return nil
	}

	threadIds := make([]uint64, 0)
	for i, threadId := range res.GetOutput().GetTidList() {
		if i >= staticField.IntThreadNum * 2 {
			break
		}
		if res.GetOutput().GetThreadList()[threadId] != nil {
			threadIds = append(threadIds, threadId)
		}
	}

	if len(threadIds) == 0 {
		tbcontext.WarningF(ctx, "hot thread list empty!")
		return nil
	}

	threadListParam := threadlist.ThreadListParam{
		ArrTid:        threadIds,
		ClientType:    staticField.IntClientType,
		ClientVersion: staticField.StrClientVersion,
		UserId:        staticField.IntUid,
		ArrStrategy:   cmap.New(),
	}
	threadInfos := threadListParam.GetThreadList(ctx, baseData.BaseObj)
	if len(threadInfos) == 0 {
		tbcontext.WarningF(ctx, "Molib_Util_ThreadList_GetThreadList::getThreadList empty!  thread_ids: %v", threadIds)
		return nil
	}

	threadList := make([]*metadata.ThreadInfoOutWithThreadTypes, 0)
	for i, threadId := range threadIds {
		if i >= staticField.IntThreadNum {
			break
		}
		for _, threadInfo := range threadInfos {
			if uint64(threadInfo.GetThreadId()) == threadId {
				threadList = append(threadList, threadInfo)
			}
		}
	}


	for _, threadInfo := range threadList {

		if threadInfo.ThumbnailCentrePoint["tag_2"] == nil {
			continue
		}

		centerInfo := make(map[uint64]*post.ThumbnailCentrePointTag, 0)
		for _, v := range threadInfo.ThumbnailCentrePoint["tag_2"] {
			centerInfo[v.GetPicId()] = v
		}

		numMedia := 0
		for _, media := range threadInfo.ClientThreadInfo.GetMedia() {
			if media.GetType() == tbrichtext.SLOT_TYPE_IMG {
				numMedia++
			}
		}

		//智能裁图，下发智能点位
		for _, media := range threadInfo.ClientThreadInfo.GetMedia() {
			if media.GetType() == tbrichtext.SLOT_TYPE_IMG {
				img.PutPicCenterIntoMedia(media, centerInfo, numMedia)
			}
		}

	}

	return threadList
}