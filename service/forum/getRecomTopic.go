package forum

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/bawu"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/search"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getRecomTopic"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tools/encode"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const DefaultTopicAvatar = "https://tieba-ares.cdn.bcebos.com/mis/2024-4/1712542770843/6a2ef2b38c37.png"
const frsInspirationGlobalSwitch = "frs_inspiration_global_switch"
const frsInspirationWhiteList = "frs_inspiration"

// GetRecomTopic 获取吧推荐话题
func GetRecomTopic(ctx context.Context, baseData *types.GetRecomTopicBaseData, response *getRecomTopic.GetRecomTopicResIdl) int {

	// 判断是否显示创作灵感
	if !isShowTopic(ctx, int32(baseData.Request.GetFid())) {
		response.Data.RecomTopicList = make([]*getRecomTopic.RecomTopic, 0)
		return tiebaerror.ERR_SUCCESS
	}
	// 获取吧标签
	tags, err := getForumTag(ctx, int64(baseData.Request.GetFid()))
	if err != nil {
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	// 调用增长接口获取话题数据
	recomTopicList, errno := searchTopicByTags(ctx, tags, baseData.Request.GetFid())
	if errno != tiebaerror.ERR_SUCCESS {
		return errno
	}
	response.Data.RecomTopicList = recomTopicList
	return tiebaerror.ERR_SUCCESS
}

// isShow 判断是否显示创作灵感
func isShowTopic(ctx context.Context, forumID int32) bool {
	// 从词表读取开关
	gs, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_pri_forum_config", frsInspirationGlobalSwitch)
	if err != nil {
		tbcontext.WarningF(ctx, "call wordserver.QueryKey err, key:%v, err:%v", frsInspirationGlobalSwitch, err)
	}
	if gs == "1" {
		tbcontext.TraceF(ctx, "%s is true", frsInspirationGlobalSwitch)
		return true
	}

	// 获取该吧是否在白名单
	req := &commonProto.IsInCommonWhiteListReq{
		Id: proto.String(fmt.Sprintf("%d", forumID)),
		WhiteListTypes: []string{
			frsInspirationWhiteList,
		},
	}
	res := &commonProto.IsInCommonWhiteListRes{}
	err = tbservice.Call(ctx, "common", "isInCommonWhiteList", req, res,
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("common_go"),
	)
	if err != nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call common_go::isInCommonWhiteList failed, err = %v, req = %v, res = %v", err, req, res)
	}
	if res.GetData() != nil {
		if list, ok := res.GetData()[fmt.Sprintf("%d", forumID)]; ok && list != nil {
			if b, ok := list.HitWhiteList[frsInspirationWhiteList]; ok && b {
				tbcontext.TraceF(ctx, "%d is in whitelist %s", forumID, frsInspirationWhiteList)
				return true
			}
		}
	}
	tbcontext.TraceF(ctx, "global_switch close and not in whitelist")
	return false
}

func searchTopicByTags(ctx context.Context, words []string, forumId uint64) ([]*getRecomTopic.RecomTopic, int) {
	searchTopicByTagsReq := &search.SearchTopicByTagsReq{
		Words:  words,
		Rn:     proto.String("4"),
		Pn:     proto.String("1"),
		Format: proto.String(tbservice.JSON),
		Ie:     proto.String(encode.UTF8),
		// 新增策略, 按吧id从取策略数据
		ForumId: proto.Int64(int64(forumId)),
	}
	searchTopicByTagsRes := &search.SearchTopicByTagsRes{}
	err := tbservice.Call(ctx, "search", "searchTopicByTags", searchTopicByTagsReq, searchTopicByTagsRes)
	if err != nil {
		tbcontext.WarningF(ctx, "call search::searchTopicByTags fail: %v", err)
		return nil, tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	if searchTopicByTagsRes.GetData() == nil {
		tbcontext.WarningF(ctx, "searchTopicByTagsRes Data is Nil")
		return nil, tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	recomTopicList := make([]*getRecomTopic.RecomTopic, 0)
	for _, topic := range searchTopicByTagsRes.GetData().GetList() {
		avatar := topic.GetAvater()
		if avatar == "" {
			avatar = DefaultTopicAvatar
		}
		recomTopicList = append(recomTopicList, &getRecomTopic.RecomTopic{
			TopicId:      topic.TopicId,
			TopicName:    topic.TopicName,
			TopicImage:   proto.String(avatar),
			PublisherNum: topic.UserCount,
			BrowseNum:    topic.ShowPv,
		})
	}
	return recomTopicList, tiebaerror.ERR_SUCCESS
}

// getForumTag 获取吧标签
func getForumTag(ctx context.Context, fid int64) ([]string, error) {
	// 从吧属性里取已经有的标签
	getBtxInfoReq := &forum.GetBtxInfoReq{
		ForumId: proto.Uint32(uint32(fid)),
	}
	getBtxInfoRes := &forum.GetBtxInfoRes{}
	err := tbservice.Call(ctx, "forum", "getBtxInfo", getBtxInfoReq, getBtxInfoRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getBtxInfoRes.Errno == nil || getBtxInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "getForumTags call service forum::getBtxInfo failed, err = %v, input = %v, output = %v", err, getBtxInfoReq, getBtxInfoRes)
		return nil, errors.New("call forum::getBtxInfo failed")
	}
	var attrTags []string
	if getBtxInfoRes.GetAttrs() != nil && getBtxInfoRes.GetAttrs().GetForumTags() != "" {
		forumAttrTag := &types.ForumTags{}
		forumTagsStr := getBtxInfoRes.GetAttrs().GetForumTags()
		err = json.Unmarshal([]byte(forumTagsStr), forumAttrTag)
		if err != nil {
			tbcontext.WarningF(ctx, "getForumTags UnmarshalFromString failed, err:[%v]", err)
			return nil, err
		}
		attrTags = append(attrTags, forumAttrTag.InterestTags.Recom...)
		attrTags = append(attrTags, forumAttrTag.InterestTags.Self...)
	}

	// 获取建吧时不需要过审的标签
	getCreateRecordReq := &bawu.GetCreateRecordReq{
		ForumId: proto.Int64(fid),
		Latest:  proto.Bool(true),
		OrderBy: proto.String("create_time"),
		Order:   proto.String("desc"),
	}
	getCreateRecordRes := &bawu.GetCreateRecordRes{}
	err = tbservice.Call(ctx, "bawu", "getCreateRecord", getCreateRecordReq, getCreateRecordRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getBtxInfoRes.Errno == nil || getBtxInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "getForumTags call service bawu::getCreateRecord failed, err = %v, input = %v, output = %v", err, getBtxInfoReq, getBtxInfoRes)
		return nil, errors.New("call bawu::getCreateRecord failed")
	}
	if getCreateRecordRes == nil || getCreateRecordRes.GetData() == nil || getCreateRecordRes.GetData().GetData() == nil ||
		len(getCreateRecordRes.GetData().GetData()) == 0 {
		tbcontext.WarningF(ctx, "call bawu::getCreateRecord res is Nil")
		return nil, errors.New("res is Nil")
	}
	forumTags := &types.ForumTags{}
	if getCreateRecordRes.GetData().GetData()[0].GetTagExt() != "" {
		err = json.Unmarshal([]byte(getCreateRecordRes.GetData().GetData()[0].GetTagExt()), forumTags)
		if err != nil {
			tbcontext.WarningF(ctx, "tagExt unmarshal err:%v, tagExt:%v", err, getCreateRecordRes.GetData().GetData()[0].GetTagExt())
			return nil, err
		}
	}
	// 标签取并集
	forumAttrTags := forumTags.InterestTags.Recom
	forumAttrTags = append(forumAttrTags, forumTags.InterestTags.Self...)
	tags := stringArrMerge(attrTags, forumAttrTags)
	return tags, nil
}

// stringArrMerge 返回两个字符串切片的并集
func stringArrMerge(slice1, slice2 []string) []string {
	m := make(map[string]bool)
	for _, item := range slice1 {
		m[item] = true
	}
	for _, item := range slice2 {
		m[item] = true
	}
	merge := make([]string, 0, len(m))
	for key := range m {
		merge = append(merge, key)
	}
	return merge
}
