package core

import (
	"context"
	"encoding/json"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/conf"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	utilForum "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/utilconst"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/ala"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbapi/midlib/cupid"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tools/encode"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Response struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("get_history_forum_response", func() engine.Job {
		return &ResponseOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewResponse(ctx *engine.Context) *Response {
	return &Response{
		ctx: ctx,
	}
}

func (a *Response) IsValid(ctx context.Context, baseData *types.CHistoryForumBaseData) bool {
	return true
}

func (a *Response) Execute(ctx context.Context, outData *getHistoryForum.GetHistoryForumRes, baseData *types.CHistoryForumBaseData) error {
	staticField := baseData.StaticField

	getForumInfo(ctx, staticField)

	// 对用户进行权限校验
	genBlockPopInfo(ctx, baseData)

	// 11.9 过滤不在访问吧列表的最近发帖的吧
	filterForumNotHisotry(ctx, staticField)

	// 过滤官方吧发帖分区tab
	filterOfficialForumTab(ctx, staticField)

	outData.HistoryForum = staticField.ArrHistoryForumInfo
	outData.ThisWeekForums = staticField.UserWeekForumInfos

	return nil
}

type ResponseOperator struct {
}

func (rdop *ResponseOperator) DoImpl(ctx *engine.Context) error {
	var outData *getHistoryForum.GetHistoryForumRes
	var baseData *types.CHistoryForumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewResponse(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "response execute fail: %v", err)
		return err
	}

	return nil
}

func getForumInfo(ctx context.Context, staticField *types.HistoryForumStaticField) {
	if staticField == nil {
		return
	}
	for _, forumInfo := range staticField.ArrHistoryInfo {
		forumIdInt := common.Tvttt(forumInfo["forum_id"], common.TTT_UINT32).(uint32)
		forumNameInfo := staticField.ForumInfo[forumIdInt]
		if forumNameInfo != nil {
			forumName := forumNameInfo.GetForumName().GetForumName()
			if forumName == "" {
				continue
			}

			avatar := forumNameInfo.GetCard().GetAvatar()
			isNowLive := 0
			// 鸿蒙无直播信息
			if forumIdInt == types.LIVE_FORUM_ID && staticField.IntClientType != clientvers.CLIENT_TYPE_HARMONY {
				input := map[string]any{
					"pn": 0,
					"ps": 5,
				}
				res := new(ala.GetLiveBarRecommendRes)
				err := tbservice.Call(ctx, "ala", "getLiveBarRecommend", input, res,
					tbservice.WithConverter(tbservice.JSONITER))
				if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
					tbcontext.WarningF(ctx, "fail to call service ala:getLiveBarRecommend, err = %v, input = %v, output = %v", err, input, res)
					return
				}

				if len(res.GetData().GetThreadList()) > 0 {
					isNowLive = 1
				}
			}

			// 10.0返回吧主题色
			// 如果没有返回默认色
			themeColor := &client.ThemeColorInfo{
				Day: &client.ThemeElement{
					CommonColor: proto.String(utilconst.DAY_THEME_COLOR_DEFAULT),
					DarkColor:   proto.String(utilconst.DAY_DARK_COLOR_DEFAULT),
					LightColor:  proto.String(utilconst.DAY_LIGHT_COLOR_DEFAULT),
					FontColor:   proto.String(utilconst.DAY_FONT_COLOR_DEFAULT),
				},
				Night: &client.ThemeElement{
					CommonColor: proto.String(utilconst.NIGHT_THEME_COLOR_DEFAULT),
					DarkColor:   proto.String(utilconst.NIGHT_DARK_COLOR_DEFAULT),
					LightColor:  proto.String(utilconst.NIGHT_LIGHT_COLOR_DEFAULT),
					FontColor:   proto.String(utilconst.NIGHT_FONT_COLOR_DEFAULT),
				},
				Dark: &client.ThemeElement{
					CommonColor: proto.String(utilconst.DARK_THEME_COLOR_DEFAULT),
					DarkColor:   proto.String(utilconst.DARK_DARK_COLOR_DEFAULT),
					LightColor:  proto.String(utilconst.DARK_LIGHT_COLOR_DEFAULT),
					FontColor:   proto.String(utilconst.DARK_FONT_COLOR_DEFAULT),
				},
			}

			themeColorStr := forumNameInfo.GetAttrs().GetThemeColor()
			if themeColorStr != "" {
				// 因主题色返回格式改变，主要为了兼容第一版
				themeColorAttr := new(client.ThemeColorInfo)
				err := json.Unmarshal([]byte(themeColorStr), themeColorAttr)
				if err != nil {
					tbcontext.WarningF(ctx, "change to ThemeColorInfo fail: %v", err)
				}

				if themeColorAttr.GetDay() != nil && themeColorAttr.GetNight() != nil {
					themeColor = themeColorAttr
				}
			}

			firstClass := forumNameInfo.GetDir().GetLevel_1Name()
			// 根据端类型及吧目录返回主题模板图片
			// 周年版去掉进吧页的问题图片
			if !clientvers.IsAnnualServerVersion(staticField.IntClientType, staticField.StrClientVersion) {
				clientStr := "android"
				if staticField.IntClientType == clientvers.CLIENT_TYPE_IPHONE {
					clientStr = "ios"
				} else if staticField.IntClientType == clientvers.CLIENT_TYPE_HARMONY {
					clientStr = "harmony"
				}

				var pattern []string
				switch firstClass {
				case utilconst.DIR_CATEGORY_YULE, utilconst.DIR_CATEGORY_ZONGYI, utilconst.DIR_CATEGORY_ZHUIJU, utilconst.DIR_CATEGORY_MOVIE, utilconst.DIR_CATEGORY_FUN:
					pattern = conf.ArrPatternImage[clientStr]["entertainment"]
				case utilconst.DIR_CATEGORY_SPORT:
					pattern = conf.ArrPatternImage[clientStr]["sport"]
				case utilconst.DIR_CATEGORY_NOVEL, utilconst.DIR_CATEGORY_AREA, utilconst.DIR_CATEGORY_NATURE:
					pattern = conf.ArrPatternImage[clientStr]["culture"]
				case utilconst.DIR_CATEGORY_LIFE:
					pattern = conf.ArrPatternImage[clientStr]["life"]
				case utilconst.DIR_CATEGORY_GAME, utilconst.DIR_CATEGORY_CARTOON, utilconst.DIR_CATEGORY_CAMPUS:
					pattern = conf.ArrPatternImage[clientStr]["young"]
				default:
					pattern = conf.ArrPatternImage[clientStr]["default"]
				}

				index := int(forumIdInt) % len(pattern)
				if themeColor.GetDay() != nil {
					themeColor.GetDay().PatternImage = proto.String(pattern[index])
				}
				if themeColor.GetNight() != nil {
					themeColor.GetNight().PatternImage = proto.String(pattern[index])
				}
				if themeColor.GetDark() != nil {
					themeColor.GetDark().PatternImage = proto.String(pattern[index])
				}
			}

			dayThreadNum := uint32(0)
			threadNum := uint32(0)
			postNum := uint32(0)

			if forumNameInfo.GetStatistics() != nil {
				// 日新数
				dayThreadNum = forumNameInfo.GetStatistics().GetDayThreadNum()

				// thread_num
				threadNum = forumNameInfo.GetStatistics().GetThreadNum()

				// post_num
				postNum = forumNameInfo.GetStatistics().GetPostNum()
			}

			staticField.ArrHistoryForumInfo = append(staticField.ArrHistoryForumInfo, &client.HistoryForumInfo{
				ForumId:       proto.Int64(int64(forumIdInt)),
				ForumName:     proto.String(forumName),
				Avatar:        proto.String(avatar),
				IsLiveforum:   proto.Int32(int32(isNowLive)),
				VisitTime:     proto.String(common.Tvttt(forumInfo["visit_time"], common.TTT_STRING).(string)),
				ThemeColor:    themeColor,
				NeedTrans:     proto.Bool(true),
				FirstCategory: proto.String(utilForum.AddCategoryInfo(firstClass)),
				DayThreadNum:  proto.Int32(int32(dayThreadNum)),
				ThreadNum:     proto.Int32(int32(threadNum)),
				PostNum:       proto.Int32(int32(postNum)),
			})
		}
	}

	for _, v := range staticField.ArrHistoryForumInfo {
		forumIdStr := common.Tvttt(v.GetForumId(), common.TTT_STRING).(string)
		if _, ok := staticField.HotThread[forumIdStr]; ok {
			v.UnreadNum = proto.Int32(int32(len(staticField.HotThread[forumIdStr])))
		}
	}

	if staticField.ForumMember != nil {
		for _, v := range staticField.ArrHistoryForumInfo {
			if staticField.ForumMember[uint32(v.GetForumId())] != nil {
				v.FollowNum = proto.Int32(int32(staticField.ForumMember[uint32(v.GetForumId())].GetMemberCount()))
			}
		}
	}

	for _, value := range staticField.ArrHistoryForumInfo {
		if staticField.HotNum[value.GetForumId()] != nil {
			value.HotNum = proto.Uint32(uint32(staticField.HotNum[value.GetForumId()].GetHotValue()))
		}
	}

	if staticField.IntUserId > 0 && staticField.LiveBarRecom != nil && staticField.LiveForumInfo != nil {
		isNowLive := 0
		if len(staticField.LiveBarRecom.GetThreadList()) > 0 {
			isNowLive = 1
		}

		tmp := &client.HistoryForumInfo{
			ForumId:     proto.Int64(types.LIVE_FORUM_ID),
			ForumName:   proto.String(staticField.LiveForumInfo.GetForumName().GetForumName()),
			Avatar:      proto.String(staticField.LiveForumInfo.GetCard().GetAvatar()),
			IsLiveforum: proto.Int32(int32(isNowLive)),
			VisitTime:   proto.String(common.Tvttt(time.Now().Unix(), common.TTT_STRING).(string)),
		}

		staticField.ArrHistoryForumInfo = append(staticField.ArrHistoryForumInfo, tmp)
	}

	if len(staticField.ForbiddenInfo) > 0 {
		newHistoryInfo := make([]*client.HistoryForumInfo, 0)
		for _, v := range staticField.ArrHistoryForumInfo {
			if _, ok := staticField.ForbiddenInfo[v.GetForumName()]; ok {
				if staticField.ForbiddenInfo[v.GetForumName()] != 1 {
					newHistoryInfo = append(newHistoryInfo, v)
				} else {
					tbcontext.TraceF(ctx, "forum[id:%d name:%s] is forbidden", v.GetForumId(), v.GetForumName())
				}
			} else {
				newHistoryInfo = append(newHistoryInfo, v)
			}
		}

		staticField.ArrHistoryForumInfo = newHistoryInfo
	}

	if len(staticField.ForumLevel) > 0 {
		for _, v := range staticField.ArrHistoryForumInfo {
			v.LevelId = proto.Int32(int32(staticField.ForumLevel[uint32(v.GetForumId())]))
		}
	}

	if len(staticField.ForumIsLikeForum) > 0 {
		for _, v := range staticField.ArrHistoryForumInfo {
			v.IsLike = proto.Int32(int32(staticField.ForumIsLikeForum[uint32(v.GetForumId())]))
		}
	}

	if len(staticField.ForumTag) > 0 {
		for _, v := range staticField.ArrHistoryForumInfo {
			if tagInfo, ok := staticField.ForumTag[v.GetForumId()]; ok {
				authUrl, _ := image.GenAuthUrl(tagInfo.GetIconUrl())
				v.TagInfo = &client.RecomTagInfo{
					Id:   proto.Int32(tagInfo.GetTagId()),
					Name: proto.String(tagInfo.GetTagName()),
					Pic:  proto.String(authUrl),
				}
			}
		}
	}
}

func genBlockPopInfo(ctx context.Context, baseData *types.CHistoryForumBaseData) {
	staticField := baseData.StaticField

	cupidInput := make(map[int64]map[string]any)
	for _, v := range staticField.ArrHistoryForumInfo {
		firstClass := ""
		secondClass := ""
		if staticField.ForumInfo[uint32(v.GetForumId())] != nil {
			firstClass = staticField.ForumInfo[uint32(v.GetForumId())].GetDir().GetLevel_1Name()
			secondClass = staticField.ForumInfo[uint32(v.GetForumId())].GetDir().GetLevel_2Name()
			if utf8.ValidString(firstClass) {
				firstClass = encode.Utf8ToGbkString(firstClass)
			}
			if utf8.ValidString(secondClass) {
				secondClass = encode.Utf8ToGbkString(secondClass)
			}
		}
		cupidInput[v.GetForumId()] = map[string]any{
			"type":             "frs",
			"forum_id":         v.GetForumId(),
			"forum_name":       encode.Utf8ToGbkString(v.GetForumName()),
			"forum_dir":        firstClass,
			"forum_second_dir": secondClass,
		}
	}

	cupidRes := cupid.MultiQuery(ctx, cupidInput)

	for _, v := range staticField.ArrHistoryForumInfo {
		arrAnti, tabInfo, postPrefix, hasPrefix := utilForum.BuildEx(ctx, staticField.ForumInfo[uint32(v.GetForumId())], staticField.PermData[v.GetForumId()], staticField.BlockData[v.GetForumId()],
			staticField.UserState[v.GetForumId()], cupidRes[v.GetForumId()], baseData.BaseObj)
		v.BlockPopInfo = arrAnti.GetBlockPopInfo()

		if len(staticField.UserData) > 0 && staticField.UserData[0].GetBusinessAccount().GetStatus() == 1 {
			fids := staticField.UserData[0].GetBusinessAccount().GetFid()
			if len(fids) > 0 && php2go.InArray(v.GetForumId(), fids) {
				v.IsForumBusinessAccount = proto.Int32(1)
			}
		}

		v.TabInfo = tabInfo
		v.PostPrefix = postPrefix
		v.HasPostpre = proto.Bool(hasPrefix)
	}
}

func filterForumNotHisotry(ctx context.Context, staticField *types.HistoryForumStaticField) {
	for _, v := range staticField.ArrHistoryForumInfo {
		if _, ok := staticField.UserWeekForums[v.GetForumId()]; ok {
			// 这里不能对历史列表进行修改，避免影响进吧页接口
			staticField.UserWeekForumInfos = append(staticField.UserWeekForumInfos, v)
		}
	}
}

// 过滤官方吧发帖分区tab
func filterOfficialForumTab(ctx context.Context, staticField *types.HistoryForumStaticField) {
	for _, v := range staticField.ArrHistoryForumInfo {
		if _, ok := staticField.OfficialForum[uint32(v.GetForumId())]; ok {
			v.TabInfo = make([]*client.FrsTabInfo, 0)
			tbcontext.TraceF(ctx, "filter official forum tab, forum_id:[%v]", v.GetForumId())

			// 设置官方吧字段值
			v.IsOfficialForum = proto.Int32(1)
		} else {
			v.IsOfficialForum = proto.Int32(0)
		}
	}
}
