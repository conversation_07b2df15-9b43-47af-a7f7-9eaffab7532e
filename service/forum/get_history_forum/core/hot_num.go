package core

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type HotNum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("get_history_forum_hot_num", func() engine.Job {
		return &HotNumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewHotNum(ctx *engine.Context) *HotNum {
	return &HotNum{
		ctx: ctx,
	}
}
func (a *HotNum) IsValid(ctx context.Context, baseData *types.CHistoryForumBaseData) bool {

	return true
}
func (a *HotNum) Execute(ctx context.Context, outData *getHistoryForum.GetHistoryForumRes, baseData *types.CHistoryForumBaseData) error {
	staticField := baseData.StaticField
	if len(staticField.ArrHistoryInfo) > 0 {
		input := map[string]interface{}{
			"forum_ids": staticField.ForumIds,
		}
		res := new(common.MgetHotByForumIdRes)
		err := tbservice.Call(ctx, "common", "mgetHotByForumId", input, res,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service common:mgetHotByForumId, err = %v, input = %v, output = %v", err, input, res)
			return nil
		}

		forumDict := make(map[int64]*common.MgetHotByForumId)
		for _, item := range res.GetOutput() {
			forumDict[item.GetForumId()] = item
		}

		staticField.HotNum = forumDict
	}

	return nil
}

type HotNumOperator struct {
}

func (rdop *HotNumOperator) DoImpl(ctx *engine.Context) error {
	var outData *getHistoryForum.GetHistoryForumRes
	var baseData *types.CHistoryForumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewHotNum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "hot_num execute fail: %v", err)
		return err
	}

	return nil
}
