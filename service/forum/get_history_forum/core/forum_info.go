package core

import (
	"context"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ForumInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("get_history_forum_forum_info", func() engine.Job {
		return &ForumInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewForumInfo(ctx *engine.Context) *ForumInfo {
	return &ForumInfo{
		ctx: ctx,
	}
}
func (a *ForumInfo) IsValid(ctx context.Context, baseData *types.CHistoryForumBaseData) bool {
	staticField := baseData.StaticField
	if !staticField.BolIsCallForum {
		return false
	}

	return true
}
func (a *ForumInfo) Execute(ctx context.Context, outData *getHistoryForum.GetHistoryForumRes, baseData *types.CHistoryForumBaseData) error {
	staticField := baseData.StaticField

	input := map[string]interface{}{
		"forum_id": staticField.ForumIds,
	}
	res := new(forum.MgetBtxInfoExRes)
	err := tbservice.Call(ctx, "forum", "mgetBtxInfoEx", input, res,
		tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service forum:mgetBtxInfoEx, err = %v, input = %v, output = %v", err, input, res)
		return nil
	}

	staticField.ForumInfo = res.GetOutput()

	return nil
}

type ForumInfoOperator struct {
}

func (rdop *ForumInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *getHistoryForum.GetHistoryForumRes
	var baseData *types.CHistoryForumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewForumInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum_info execute fail: %v", err)
		return err
	}

	return nil
}
