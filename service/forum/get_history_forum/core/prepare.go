package core

import (
	"context"
	"errors"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type Prepare struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("get_history_forum_prepare", func() engine.Job {
		return &PrepareOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewPrepare(ctx *engine.Context) *Prepare {
	return &Prepare{
		ctx: ctx,
	}
}
func (a *Prepare) IsValid(ctx context.Context, baseData *types.CHistoryForumBaseData) bool {

	return true
}
func (a *Prepare) Execute(ctx context.Context, outData *getHistoryForum.GetHistoryForumRes, baseData *types.CHistoryForumBaseData) error {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest
	staticField.IntUserId = common.Tvttt(objReq.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	staticField.IntClientType = common.Tvttt(objReq.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	staticField.StrClientVersion = common.Tvttt(objReq.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	staticField.ArrHistoryInfo, _ = objReq.GetPrivateAttr("history_info", []map[string]interface{}{}).([]map[string]interface{})
	staticField.BoolLogin = common.Tvttt(objReq.GetCommonAttr("login", false), common.TTT_BOOL).(bool)

	newHistoryInfo := make([]map[string]interface{}, 0)
	forumIds := make([]uint32, 0)
	for _, forumInfo := range staticField.ArrHistoryInfo {
		if forumId, ok := forumInfo["forum_id"]; ok {
			forumIdInt := common.Tvttt(forumId, common.TTT_UINT32).(uint32)
			if forumIdInt > 0 {
				newHistoryInfo = append(newHistoryInfo, forumInfo)
				forumIds = append(forumIds, forumIdInt)
			}
		}
	}

	staticField.ForumIds = forumIds

	if len(newHistoryInfo) == 0 {
		tbcontext.WarningF(ctx, "get history info fail: %v", newHistoryInfo)
		// bugfix: http://newicafe.baidu.com/v5/issue/CASETIEBA-36541/show?source=profile
		// 如用户刚启动贴吧，我的吧列表为空时，请求该 ui 时传输的 history_info 为空，此时不应该设置为错误状态。
		baseData.BaseObj.Error(tiebaerror.ERR_SUCCESS, tiebaerror.GetErrMsg(tiebaerror.ERR_SUCCESS), false)
		return errors.New("history info len is 0")
	}

	staticField.ArrHistoryInfo = newHistoryInfo

	if len(staticField.ArrHistoryInfo) > 0 {
		staticField.BolIsCallForum = true
	}

	return nil
}

type PrepareOperator struct {
}

func (rdop *PrepareOperator) DoImpl(ctx *engine.Context) error {
	var outData *getHistoryForum.GetHistoryForumRes
	var baseData *types.CHistoryForumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewPrepare(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "prepare execute fail: %v", err)
		return err
	}

	return nil
}
