package core

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	utilCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ThreadInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("get_history_forum_thread_info", func() engine.Job {
		return &ThreadInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewThreadInfo(ctx *engine.Context) *ThreadInfo {
	return &ThreadInfo{
		ctx: ctx,
	}
}
func (a *ThreadInfo) IsValid(ctx context.Context, baseData *types.CHistoryForumBaseData) bool {

	return true
}
func (a *ThreadInfo) Execute(ctx context.Context, outData *getHistoryForum.GetHistoryForumRes, baseData *types.CHistoryForumBaseData) error {
	staticField := baseData.StaticField

	if len(staticField.ArrHistoryInfo) > 0 {
		input := map[string]interface{}{
			"history_info": staticField.ArrHistoryInfo,
		}
		res := new(common.GetHotThreadRes)
		err := tbservice.Call(ctx, "common", "getHotThread", input, res,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service common:getHotThread, err = %v, input = %v, output = %v", err, input, utilCommon.ToString(res))
			return nil
		}

		data := make(map[string][]*common.GetHotThread)
		utilCommon.StructAToStructBCtx(ctx, res.GetData(), &data)

		staticField.HotThread = data
	}

	return nil
}

type ThreadInfoOperator struct {
}

func (rdop *ThreadInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *getHistoryForum.GetHistoryForumRes
	var baseData *types.CHistoryForumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewThreadInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "thread_info execute fail: %v", err)
		return err
	}

	return nil
}
