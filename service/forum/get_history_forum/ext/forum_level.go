package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ForumLevel struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("get_history_forum_forum_level", func() engine.Job {
		return &ForumLevelOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewForumLevel(ctx *engine.Context) *ForumLevel {
	return &ForumLevel{
		ctx: ctx,
	}
}
func (a *ForumLevel) IsValid(ctx context.Context, baseData *types.CHistoryForumBaseData) bool {

	return true
}
func (a *ForumLevel) Execute(ctx context.Context, outData *getHistoryForum.GetHistoryForumRes, baseData *types.CHistoryForumBaseData) error {
	staticField := baseData.StaticField
	// 11.9版本首页发帖，需要获取用户对应的level_id
	if staticField.BolIsCallForum {

		input := map[string]interface{}{
			"forum_ids": staticField.ForumIds,
			"user_id":   staticField.IntUserId,
		}
		res := new(perm.MgetUserLevelRes)
		err := tbservice.Call(ctx, "perm", "mgetUserLevel", input, res,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service perm:mgetUserLevel, err = %v, input = %v, output = %v", err, input, res)
			return nil
		}

		userLevelDict := make(map[uint32]uint32)
		islikeForum := make(map[uint32]uint32)
		for _, item := range res.GetScoreInfo() {
			userLevelDict[item.GetForumId()] = item.GetLevelId()
			islikeForum[item.GetForumId()] = item.GetIsLike()
		}

		staticField.ForumLevel = userLevelDict
		staticField.ForumIsLikeForum = islikeForum

	}

	return nil
}

type ForumLevelOperator struct {
}

func (rdop *ForumLevelOperator) DoImpl(ctx *engine.Context) error {
	var outData *getHistoryForum.GetHistoryForumRes
	var baseData *types.CHistoryForumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewForumLevel(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum_level execute fail: %v", err)
		return err
	}

	return nil
}
