package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ForbiddenInfo struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("get_history_forum_forbidden_info", func() engine.Job {
		return &ForbiddenInfoOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewForbiddenInfo(ctx *engine.Context) *ForbiddenInfo {
	return &ForbiddenInfo{
		ctx: ctx,
	}
}
func (a *ForbiddenInfo) IsValid(ctx context.Context, baseData *types.CHistoryForumBaseData) bool {

	return true
}
func (a *ForbiddenInfo) Execute(ctx context.Context, outData *getHistoryForum.GetHistoryForumRes, baseData *types.CHistoryForumBaseData) error {
	staticField := baseData.StaticField
	// 关吧过滤
	if staticField.BolIsCallForum {
		fnames := make([]string, 0)
		for _, history := range staticField.ForumInfo {
			fnames = append(fnames, history.GetForumName().GetForumName())
		}

		input := map[string]interface{}{
			"query_words": fnames,
		}
		res := new(forum.GetFidByFnameRes)
		err := tbservice.Call(ctx, "forum", "getFidByFname", input, res,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service forum:getFidByFname, err = %v, input = %v, output = %v", err, input, res)
			return nil
		}

		forbiddenInfo := make(map[string]uint32)
		for _, arrForumInfo := range res.GetForumId() {
			forbiddenInfo[arrForumInfo.GetQword()] = arrForumInfo.GetIsForbidden()
		}

		staticField.ForbiddenInfo = forbiddenInfo
	}

	return nil
}

type ForbiddenInfoOperator struct {
}

func (rdop *ForbiddenInfoOperator) DoImpl(ctx *engine.Context) error {
	var outData *getHistoryForum.GetHistoryForumRes
	var baseData *types.CHistoryForumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewForbiddenInfo(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forbidden_info execute fail: %v", err)
		return err
	}

	return nil
}
