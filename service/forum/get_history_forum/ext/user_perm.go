package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/convert"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/userstate"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/ip"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type UserPerm struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("get_history_forum_user_perm", func() engine.Job {
		return &UserPermOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewUserPerm(ctx *engine.Context) *UserPerm {
	return &UserPerm{
		ctx: ctx,
	}
}
func (a *UserPerm) IsValid(ctx context.Context, baseData *types.CHistoryForumBaseData) bool {

	return true
}
func (a *UserPerm) Execute(ctx context.Context, outData *getHistoryForum.GetHistoryForumRes, baseData *types.CHistoryForumBaseData) error {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	// 对用户发帖权限进行校验，复用frs页权限校验
	if staticField.BolIsCallForum && staticField.IntUserId > 0 {

		ipInt := common.Tvttt(objReq.GetCommonAttr("ip_int", 0), common.TTT_UINT32).(uint32)
		ipVersion := ip.GetIPVersion(ip.Long2IP(ipInt))
		theIp := uint32(0)
		theIpv6 := ""
		if ipVersion == ip.IPV4 {
			theIp = ipInt
		} else {
			theIpv6 = common.Tvttt(ipInt, common.TTT_STRING).(string)
		}

		multi := tbservice.Multi()

		getUserDataParam := &tbservice.Parameter{
			Service: "user",
			Method:  "getUserData",
			Input: map[string]interface{}{
				"user_id":   staticField.IntUserId,
				"call_from": "client_frs",
			},
			Output: &user.GetUserDataRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getUserData", getUserDataParam)

		for _, forumId := range staticField.ForumIds {
			forumIdStr := common.Tvttt(forumId, common.TTT_STRING).(string)
			getPermParam := &tbservice.Parameter{
				Service: "perm",
				Method:  "getPerm",
				Input: map[string]interface{}{
					"user_id":  staticField.IntUserId,
					"forum_id": forumId,
					"user_ip":  theIp,
					"user_ip6": theIpv6,
				},
				Output: &perm.GetPermRes{},
				Option: []tbservice.Option{
					tbservice.WithConverter(tbservice.JSONITER),
				},
			}
			multi.Register(ctx, "getPerm"+forumIdStr, getPermParam)

			if staticField.BoolLogin {

				input := map[string]map[string]interface{}{
					"reqs": {
						"check_vcode": map[string]interface{}{
							"service_type": "vcode",
							"forum_id":     forumId,
							"key":          staticField.IntUserId,
							"opgroup":      "system",
						},
						"check_block": map[string]interface{}{
							"service_type": "blockid",
							"forum_id":     forumId,
							"key":          staticField.IntUserId,
						},
						"client_type":    staticField.IntClientType,
						"client_version": staticField.StrClientVersion,
					},
				}

				if clientvers.Compare("9.2.0", staticField.StrClientVersion) >= 0 {
					input["reqs"]["extra"] = []string{"phoneblock"}
					input["reqs"]["req_source"] = []string{"frspre"}
				}

				var userState *tbservice.Parameter
				if clientvers.Compare("9.7.0", staticField.StrClientVersion) >= 0 {
					userState = &tbservice.Parameter{
						Service: "userstate",
						Method:  "queryBlockAndAppealInfo",
						Input:   input,
						Output:  &userstate.QueryBlockAndAppealInfoRes{},
						Option: []tbservice.Option{
							tbservice.WithConverter(tbservice.JSONITER),
						},
					}
					multi.Register(ctx, "queryBlockAndAppealInfo"+forumIdStr, userState)
				} else {
					userState = &tbservice.Parameter{
						Service: "userstate",
						Method:  "queryUserStates",
						Input:   input,
						Output:  &userstate.QueryUserStatesRes{},
						Option: []tbservice.Option{
							tbservice.WithConverter(tbservice.JSONITER),
							tbservice.WithResponseConverter(convert.UserStateConvert),
						},
					}
					multi.Register(ctx, "queryUserStates"+forumIdStr, userState)
				}
			}

		}
		multi.Call(ctx)

		userDataInter, err := multi.GetResult(ctx, "getUserData")
		userData := userDataInter.(*user.GetUserDataRes)
		if err != nil || userData.Errno == nil || userData.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service user:getUserData, err = %v, output = %v", err, userData)
		} else {
			staticField.UserData = userData.GetUserInfo()
		}

		for _, forumId := range staticField.ForumIds {
			forumIdStr := common.Tvttt(forumId, common.TTT_STRING).(string)
			getPermInter, err := multi.GetResult(ctx, "getPerm"+forumIdStr)
			getPerm := getPermInter.(*perm.GetPermRes)
			if err != nil || getPerm.Errno == nil || getPerm.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "fail to call service perm:getPerm, err = %v, output = %v", err, getPerm)
			} else {
				staticField.PermData[int64(forumId)] = getPerm.GetOutput()
			}

			if staticField.BoolLogin {
				if clientvers.Compare("9.7.0", staticField.StrClientVersion) >= 0 {
					queryBlockAndAppealInfoInter, err := multi.GetResult(ctx, "queryBlockAndAppealInfo"+forumIdStr)
					queryBlockAndAppealInfo := queryBlockAndAppealInfoInter.(*userstate.QueryBlockAndAppealInfoRes)
					if err != nil || queryBlockAndAppealInfo.Errno == nil || queryBlockAndAppealInfo.GetErrno() != tiebaerror.ERR_SUCCESS {
						tbcontext.WarningF(ctx, "fail to call service userstate:queryBlockAndAppealInfo, err = %v, output = %v", err, queryBlockAndAppealInfo)
					} else {
						staticField.BlockData[int64(forumId)] = queryBlockAndAppealInfo.GetRes()
					}
				} else {
					queryUserStatesInter, err := multi.GetResult(ctx, "queryUserStates"+forumIdStr)
					queryUserStates := queryUserStatesInter.(*userstate.QueryUserStatesRes)
					if err != nil || queryUserStates.Errno == nil || queryUserStates.GetErrno() != tiebaerror.ERR_SUCCESS {
						tbcontext.WarningF(ctx, "fail to call service userstate:queryUserStates, err = %v, output = %v", err, queryUserStates)
					} else {
						staticField.UserState[int64(forumId)] = queryUserStates.GetRes()
					}
				}
			}
		}
	}

	return nil
}

type UserPermOperator struct {
}

func (rdop *UserPermOperator) DoImpl(ctx *engine.Context) error {
	var outData *getHistoryForum.GetHistoryForumRes
	var baseData *types.CHistoryForumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewUserPerm(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "user_perm execute fail: %v", err)
		return err
	}

	return nil
}
