package ext

import (
	"context"

	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type OfficialForum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("get_history_forum_official_forum", func() engine.Job {
		return &OfficialForumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewOfficialForum(ctx *engine.Context) *OfficialForum {
	return &OfficialForum{
		ctx: ctx,
	}
}
func (a *OfficialForum) IsValid(ctx context.Context, baseData *types.CHistoryForumBaseData) bool {
	return true
}
func (a *OfficialForum) Execute(ctx context.Context, outData *getHistoryForum.GetHistoryForumRes, baseData *types.CHistoryForumBaseData) error {
	staticField := baseData.StaticField
	req := map[string]interface{}{
		"pn":          1,
		"rn":          1000,
		"is_form_mis": 1,
	}
	res := &common.GetOfficialForumIDListRes{}
	err := tbservice.Call(ctx, "common", "getOfficialForumIDList", req, res)
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service common:getOfficialForumIDList, err=[%v], input=[%v], output=[%v]", err, req, res)
		return nil
	}

	forumIDMap := make(map[uint32]struct{})
	for _, v := range res.GetData().GetForumIds() {
		forumIDMap[v] = struct{}{}
	}

	staticField.OfficialForum = forumIDMap

	return nil
}

type OfficialForumOperator struct {
}

func (ofop *OfficialForumOperator) DoImpl(ctx *engine.Context) error {
	var outData *getHistoryForum.GetHistoryForumRes
	var baseData *types.CHistoryForumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewOfficialForum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "get_history_forum_official_forum execute fail: %v", err)
		return err
	}

	return nil
}
