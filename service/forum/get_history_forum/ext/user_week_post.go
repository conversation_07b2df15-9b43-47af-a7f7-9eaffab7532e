package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/userpost"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"time"
)

type UserWeekPost struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("get_history_forum_user_week_post", func() engine.Job {
		return &UserWeekPostOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewUserWeekPost(ctx *engine.Context) *UserWeekPost {
	return &UserWeekPost{
		ctx: ctx,
	}
}
func (a *UserWeekPost) IsValid(ctx context.Context, baseData *types.CHistoryForumBaseData) bool {

	return true
}
func (a *UserWeekPost) Execute(ctx context.Context, outData *getHistoryForum.GetHistoryForumRes, baseData *types.CHistoryForumBaseData) error {
	staticField := baseData.StaticField
	// 11.9 首页发帖，获取用户一周内发帖的吧
	if staticField.BolIsCallForum && staticField.IntUserId > 0 {

		input := map[string]interface{}{
			"input": map[string]interface{}{
				"user_id":     staticField.IntUserId,
				"offset":      0,
				"res_num":     1000,
				"order_type":  1,
				"delete_type": 0,
				"is_thread":   1,
				"begin_time":  time.Now().Unix() - 7*24*60*60,
			},
		}
		res := new(userpost.QueryUserPostRes)
		err := tbservice.Call(ctx, "post", "queryUserPost", input, res,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service post:queryUserPost, err = %v, input = %v, output = %v", err, input, res)
			return nil
		}

		res.GetPost().GetPost()
		forumIdMap := make(map[int64]bool)
		for _, postInfo := range res.GetPost().GetPost() {
			forumIdMap[int64(postInfo.GetForumId())] = true
		}

		staticField.UserWeekForums = forumIdMap
	}

	return nil
}

type UserWeekPostOperator struct {
}

func (rdop *UserWeekPostOperator) DoImpl(ctx *engine.Context) error {
	var outData *getHistoryForum.GetHistoryForumRes
	var baseData *types.CHistoryForumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewUserWeekPost(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "user_week_post execute fail: %v", err)
		return err
	}

	return nil
}
