package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ForumTag struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("get_history_forum_forum_tag", func() engine.Job {
		return &ForumTagOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewForumTag(ctx *engine.Context) *ForumTag {
	return &ForumTag{
		ctx: ctx,
	}
}
func (a *ForumTag) IsValid(ctx context.Context, baseData *types.CHistoryForumBaseData) bool {

	return true
}
func (a *ForumTag) Execute(ctx context.Context, outData *getHistoryForum.GetHistoryForumRes, baseData *types.CHistoryForumBaseData) error {
	staticField := baseData.StaticField
	// 12.27版本，查询吧的推荐标签
	if staticField.BolIsCallForum {

		input := map[string]interface{}{
			"fids": staticField.ForumIds,
		}
		res := new(common.MQueryTagForumRes)
		err := tbservice.Call(ctx, "common", "mqueryTagForum", input, res,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service common:mqueryTagForum, err = %v, input = %v, output = %v", err, input, res)
			return nil
		}

		staticField.ForumTag = res.GetData()

	}

	return nil
}

type ForumTagOperator struct {
}

func (rdop *ForumTagOperator) DoImpl(ctx *engine.Context) error {
	var outData *getHistoryForum.GetHistoryForumRes
	var baseData *types.CHistoryForumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewForumTag(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum_tag execute fail: %v", err)
		return err
	}

	return nil
}
