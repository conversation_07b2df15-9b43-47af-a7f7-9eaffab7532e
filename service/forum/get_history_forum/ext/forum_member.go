package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type ForumMember struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("get_history_forum_forum_member", func() engine.Job {
		return &ForumMemberOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewForumMember(ctx *engine.Context) *ForumMember {
	return &ForumMember{
		ctx: ctx,
	}
}
func (a *ForumMember) IsValid(ctx context.Context, baseData *types.CHistoryForumBaseData) bool {

	return true
}
func (a *ForumMember) Execute(ctx context.Context, outData *getHistoryForum.GetHistoryForumRes, baseData *types.CHistoryForumBaseData) error {
	staticField := baseData.StaticField
	if staticField == nil {
		return nil
	}
	//10.0版本增加吧关注数和吧主题色
	version := clientvers.IsMajorServerVersion(staticField.IntClientType, staticField.StrClientVersion)
	if version && ((clientvers.Compare("*******", staticField.StrClientVersion) >= 0 && clientvers.CLIENT_TYPE_ANDROID == staticField.IntClientType) ||
		(clientvers.Compare("10.0.0", staticField.StrClientVersion) >= 0 && clientvers.CLIENT_TYPE_IPHONE == staticField.IntClientType) ||
		(clientvers.CLIENT_TYPE_HARMONY == staticField.IntClientType)) {
		input := map[string]interface{}{
			"forum_ids": staticField.ForumIds,
		}
		res := new(perm.MgetForumMemberInfoRes)
		err := tbservice.Call(ctx, "perm", "mgetForumMemberInfo", input, res,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service perm:mgetForumMemberInfo, err = %v, input = %v, output = %v", err, input, res)
			return nil
		}

		if len(res.GetOutput()) == 0 {
			return nil
		}

		staticField.ForumMember = res.GetOutput()
	}

	return nil
}

type ForumMemberOperator struct {
}

func (rdop *ForumMemberOperator) DoImpl(ctx *engine.Context) error {
	var outData *getHistoryForum.GetHistoryForumRes
	var baseData *types.CHistoryForumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewForumMember(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "forum_member execute fail: %v", err)
		return err
	}

	return nil
}
