package ext

import (
	"context"
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/ala"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getHistoryForum"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type LiveForum struct {
	ctx *engine.Context
}

func init() {
	err := engine.RegisterOperator("get_history_forum_live_forum", func() engine.Job {
		return &LiveForumOperator{}
	})
	if err != nil {
		panic(err)
	}
}

func NewLiveForum(ctx *engine.Context) *LiveForum {
	return &LiveForum{
		ctx: ctx,
	}
}
func (a *LiveForum) IsValid(ctx context.Context, baseData *types.CHistoryForumBaseData) bool {
	if baseData == nil || baseData.StaticField == nil {
		return false
	}
	// 鸿蒙暂不支持直播
	if baseData.StaticField.IntClientType == stcdefine.CLIENT_TYPE_HARMONY {
		return false
	}
	return true
}
func (a *LiveForum) Execute(ctx context.Context, outData *getHistoryForum.GetHistoryForumRes, baseData *types.CHistoryForumBaseData) error {
	staticField := baseData.StaticField

	if !staticField.BolIsCallForum && staticField.IntUserId > 0 {
		input := map[string]interface{}{
			"forum_ids": staticField.ForumIds,
		}
		res := new(common.GetLiveForumRes)
		err := tbservice.Call(ctx, "common", "getLiveForum", input, res,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.FatalF(ctx, "fail to call service common:getLiveForum, err = %v, input = %v, output = %v", err, input, res)
			return nil
		}

		if len(res.GetData()) > 0 && res.GetData()[0].IsShow != nil && res.GetData()[0].GetIsShow() == 0 {
			multi := tbservice.Multi()

			arrParam := &tbservice.Parameter{
				Service: "forum",
				Method:  "getBtxInfo",
				Input: map[string]interface{}{
					"forum_id": types.LIVE_FORUM_ID,
				},
				Output: &forum.GetBtxInfoRes{},
				Option: []tbservice.Option{
					tbservice.WithConverter(tbservice.JSONITER),
				},
			}

			multi.Register(ctx, "getBtxInfo", arrParam)

			arrParam = &tbservice.Parameter{
				Service: "ala",
				Method:  "getLiveBarRecommend",
				Input: map[string]interface{}{
					"forum_id": types.LIVE_FORUM_ID,
				},
				Output: &ala.GetLiveBarRecommendRes{},
				Option: []tbservice.Option{
					tbservice.WithConverter(tbservice.JSONITER),
				},
			}

			multi.Register(ctx, "getLiveBarRecommend", arrParam)

			multi.Call(ctx)

			mgetBtxInfoExInter, err := multi.GetResult(ctx, "getBtxInfo")
			mgetBtxInfoEx := mgetBtxInfoExInter.(*forum.GetBtxInfoRes)
			if err != nil || mgetBtxInfoEx.Errno == nil || mgetBtxInfoEx.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "call forum::mgetBtxInfoEx fail: %v, res: %+v", err, mgetBtxInfoEx)
			}

			getLiveBarRecommendInter, err := multi.GetResult(ctx, "getLiveBarRecommend")
			getLiveBarRecommend := getLiveBarRecommendInter.(*ala.GetLiveBarRecommendRes)
			if err != nil || getLiveBarRecommend.Errno == nil || getLiveBarRecommend.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.WarningF(ctx, "call ala::getLiveBarRecommend fail: %v, res: %+v", err, getLiveBarRecommend)
			}

			staticField.LiveBarRecom = getLiveBarRecommend.GetData()
			staticField.LiveForumInfo = mgetBtxInfoEx
		}

	}

	return nil
}

type LiveForumOperator struct {
}

func (rdop *LiveForumOperator) DoImpl(ctx *engine.Context) error {
	var outData *getHistoryForum.GetHistoryForumRes
	var baseData *types.CHistoryForumBaseData

	ctx.MutableInstance(&outData)
	ctx.MutableInstance(&baseData)
	sa := NewLiveForum(ctx)
	if !sa.IsValid(ctx.CallerCtx(), baseData) {
		return nil
	}

	err := sa.Execute(ctx.CallerCtx(), outData, baseData)
	if err != nil {
		tbcontext.WarningF(ctx.CallerCtx(), "live_forum execute fail: %v", err)
		return err
	}

	return nil
}
