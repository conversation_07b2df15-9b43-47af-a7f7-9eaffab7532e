package forum

import (
	"context"
	"unicode/utf8"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/userstate"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/queryBlockAndAppealInfo"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tools/encode"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func QueryBlockAndAppealInfo(ctx context.Context, baseData *types.CQueryBlockAndAppealInfoBaseData,
	response *queryBlockAndAppealInfo.QueryBlockAndAppealInfoResIdl) int {
	objRequest := baseData.BaseObj.ObjRequest

	uid := common.Tvttt(objRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	if uid <= 0 {
		tbcontext.WarningF(ctx, "uid is invalid, uid=%d", uid)
		return tiebaerror.ERR_USER_NOT_LOGIN
	}

	input := map[string]interface{}{
		"reqs": map[string]interface{}{
			"check_vcode": map[string]interface{}{
				"service_type": "vcode",
				"forum_id":     0,
				"key":          uid,
				"opgroup":      "system",
			},
			"check_block": map[string]interface{}{
				"service_type": "blockid",
				"forum_id":     0,
				"key":          uid,
			},
			"query_type": "hotswap",
		},
	}

	output := new(userstate.QueryBlockAndAppealInfoRes)
	err := tbservice.Call(ctx, "userstate", "queryBlockAndAppealInfo", input, output, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || output.Errno == nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call userstate::queryBlockAndAppealInfo fail, res: %+v, err: %v", output, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	respData := response.GetData()
	if output.GetRes() == nil || output.GetRes().GetBlockPopInfo() == nil {
		respData.BlockInfo = proto.String("")
		respData.AheadInfo = proto.String("")
		respData.AheadUrl = proto.String("")
		respData.OkInfo = proto.String("")
		respData.WinType = proto.Uint32(0)
		respData.BlockIdCode = proto.String("0")
	} else {
		respData.BlockInfo = proto.String(output.GetRes().GetBlockPopInfo().GetBlockInfo())
		respData.AheadInfo = proto.String(output.GetRes().GetBlockPopInfo().GetAheadInfo())
		respData.AheadUrl = proto.String(output.GetRes().GetBlockPopInfo().GetAheadUrl())
		respData.OkInfo = proto.String(output.GetRes().GetBlockPopInfo().GetOkInfo())

		if !utf8.ValidString(respData.GetBlockInfo()) {
			respData.BlockInfo = proto.String(encode.GbkToUtf8String(respData.GetBlockInfo()))
		}

		if !utf8.ValidString(respData.GetOkInfo()) {
			respData.OkInfo = proto.String(encode.GbkToUtf8String(respData.GetOkInfo()))
		}

		if !utf8.ValidString(respData.GetAheadInfo()) {
			respData.AheadInfo = proto.String(encode.GbkToUtf8String(respData.GetAheadInfo()))
		}

		respData.WinType = proto.Uint32(uint32(output.GetRes().GetAppealTimes()) + 1) // 用户已被封禁
		respData.BlockIdCode = proto.String(output.GetRes().GetBlockIdCode())
		if output.GetRes().GetBlockIdCode() == "0" || output.GetRes().GetBlockIdCode() == "" {
			respData.WinType = proto.Uint32(0)
		}
	}

	// 打印是否修改
	tbcontext.AddNotice(ctx, "before set: res=", respData.String())
	respData.WinType = proto.Uint32(0)
	respData.BlockIdCode = proto.String("0")
	tbcontext.AddNotice(ctx, "after set: res=", respData.String())
	return tiebaerror.ERR_SUCCESS
}
