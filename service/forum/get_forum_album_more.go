package forum

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/threadtype"

	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	forumProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	postFrsProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	gfamProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumAlbumMore"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/util"
)

// getForumAlbumMoreSeparator 入参使用的分隔符
const getForumAlbumMoreSeparator = ","

const (
	// getThreadChunkNum 获取贴信息时每组请求的数量
	getThreadChunkNum = 30

	// getForumChunkNum 获取吧信息时每组请求的数量
	getForumChunkNum = 30
)

// forumEnterNumDuration 查询
const forumEnterNumDuration = "day"

type getForumAlbumMoreProcessor struct {
	ctx      context.Context
	baseData *types.GetForumAlbumMoreBaseData
}

// GetForumAlbumMore 进吧页吧单列表翻页接口
func GetForumAlbumMore(ctx context.Context, baseData *types.GetForumAlbumMoreBaseData, response *gfamProto.GetForumAlbumMoreResIdl) int {
	obj := getForumAlbumMoreProcessor{
		ctx:      ctx,
		baseData: baseData,
	}

	// 前置校验及参数解析
	err := obj.preCheck()
	if err != nil {
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 获取基础信息
	err = obj.getBasicData()
	if err != nil {
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 数据格式化
	response.Data.ThreadList = obj.buildThreadList()

	return tiebaerror.ERR_SUCCESS
}

// preCheck 前置校验及参数解析
func (obj *getForumAlbumMoreProcessor) preCheck() error {
	objRequest := obj.baseData.BaseObj.ObjRequest

	// 参数校验
	strThreadIDs := common.Tvttt(objRequest.GetPrivateAttr("thread_ids", ""), common.TTT_STRING).(string)
	strForumIDs := common.Tvttt(objRequest.GetPrivateAttr("forum_ids", ""), common.TTT_STRING).(string)
	if len(strThreadIDs) == 0 || len(strForumIDs) == 0 {
		tbcontext.WarningF(obj.ctx, "preCheck input empty")
		return errors.New("input empty")
	}

	// 参数解析
	threadIDs := strings.Split(strThreadIDs, getForumAlbumMoreSeparator)
	forumIDs := strings.Split(strForumIDs, getForumAlbumMoreSeparator)
	if len(threadIDs) == 0 || len(forumIDs) == 0 || len(threadIDs) != len(forumIDs) {
		tbcontext.WarningF(obj.ctx, "preCheck input invalid, thread_ids=%s, forum_ids=%s",
			common.ToString(&threadIDs), common.ToString(&forumIDs))
		return errors.New("input invalid")
	}

	// 数据处理、去重、分组
	arrThreadIDs := make([]uint64, 0, len(threadIDs))
	arrForumIDs := make([]uint32, 0, len(forumIDs))
	threadIDHits := make(map[uint64]struct{})
	forumIDHits := make(map[uint32]struct{})
	for index, strTid := range threadIDs {
		rawTid, err := strconv.ParseInt(strTid, 10, 64)
		if err != nil || rawTid <= 0 {
			continue
		}
		tid := uint64(rawTid)

		rawFid, err := strconv.ParseInt(forumIDs[index], 10, 64)
		if err != nil || rawFid <= 0 {
			continue
		}
		fid := uint32(rawFid)

		if _, ok := threadIDHits[tid]; !ok {
			arrThreadIDs = append(arrThreadIDs, tid)
			threadIDHits[tid] = struct{}{}
		}
		if _, ok := forumIDHits[fid]; !ok {
			arrForumIDs = append(arrForumIDs, fid)
			forumIDHits[fid] = struct{}{}
		}

		obj.baseData.StaticField.ThreadForumMap[tid] = fid
	}

	for i := 0; i < len(arrThreadIDs); i += getThreadChunkNum {
		start := i
		end := i + getThreadChunkNum
		if end >= len(arrThreadIDs) {
			end = len(arrThreadIDs)
		}
		obj.baseData.StaticField.ThreadIDs = append(obj.baseData.StaticField.ThreadIDs, arrThreadIDs[start:end])
	}

	for i := 0; i < len(arrForumIDs); i += getForumChunkNum {
		start := i
		end := i + getForumChunkNum
		if end >= len(arrForumIDs) {
			end = len(arrForumIDs)
		}
		obj.baseData.StaticField.ForumIDs = append(obj.baseData.StaticField.ForumIDs, arrForumIDs[start:end])
	}

	return nil
}

// getBasicData 获取基础信息
func (obj *getForumAlbumMoreProcessor) getBasicData() error {
	eg := gtask.Group{
		// 不允许部分失败，核心数据方法会返回error，扩展数据方法不会返回error
		AllowSomeFail: false,
	}

	// 获取贴信息
	eg.Go(func() error {
		return obj.getThreadInfo()
	})

	// 获取贴屏蔽状态
	eg.Go(func() error {
		return obj.getThreadMaskInfo()
	})

	// 获取吧信息
	eg.Go(func() error {
		return obj.getForumInfo()
	})

	// 获取垂类信息
	eg.Go(func() error {
		return obj.getForumVerticalInfo()
	})

	// 获取昨日进吧量
	eg.Go(func() error {
		return obj.getForumEnterNum()
	})

	// 获取吧热度信息
	eg.Go(func() error {
		return obj.getForumHotNum()
	})

	// 核心数据失败阻塞执行
	_, err := eg.Wait()
	if err != nil {
		tbcontext.FatalF(obj.ctx, "getBasicData process fail, err=%v", err)
		return err
	}
	return nil
}

// getThreadInfo 获取贴信息
func (obj *getForumAlbumMoreProcessor) getThreadInfo() error {
	multi := tbservice.Multi()

	for index, tids := range obj.baseData.StaticField.ThreadIDs {
		threadReq := &tbservice.Parameter{
			Service: "post",
			Method:  "mgetThread",
			Input: &postFrsProto.MgetThreadReq{
				ThreadIds:    tids,
				NeedAbstract: proto.Uint32(1),
				NeedPhotoPic: proto.Uint32(1),
				IconSize:     proto.Uint32(1),
				CallFrom:     proto.String("client_frs"),
				ForumId:      proto.Uint32(0), // 不传这个参数不返回pic_info
			},
			Output: &postFrsProto.MgetThreadRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(obj.ctx, fmt.Sprintf("mgetThread_%d", index), threadReq)
	}

	multi.Call(obj.ctx)

	for index, tids := range obj.baseData.StaticField.ThreadIDs {
		res, err := multi.GetResult(obj.ctx, fmt.Sprintf("mgetThread_%d", index))
		if err != nil {
			tbcontext.WarningF(obj.ctx, "getThreadInfo call post::mgetThread fail, err=%v, input=%s", err, common.ToString(&tids))
			return errors.New("call post::mgetThread fail")
		}
		threadRes, ok := res.(*postFrsProto.MgetThreadRes)
		if !ok || threadRes == nil || threadRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(obj.ctx, "getThreadInfo call post::mgetThread fail, err=%v, input=%s, output=%s",
				err, common.ToString(&tids), common.ToString(threadRes))
			return errors.New("call post::mgetThread fail")
		}

		for tid, thread := range threadRes.GetOutput().GetThreadList() {
			obj.baseData.StaticField.RawTheadInfo[tid] = thread
		}
	}

	return nil
}

// getThreadMaskInfo 获取贴屏蔽状态
func (obj *getForumAlbumMoreProcessor) getThreadMaskInfo() error {
	multi := tbservice.Multi()

	for index, tids := range obj.baseData.StaticField.ThreadIDs {
		maskReq := &tbservice.Parameter{
			Service: "post",
			Method:  "getThreadMaskInfo",
			Input: &postFrsProto.GetThreadMaskInfoReq{
				ThreadIds: tids,
			},
			Output: &postFrsProto.GetThreadMaskInfoRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(obj.ctx, fmt.Sprintf("getThreadMaskInfo_%d", index), maskReq)
	}

	multi.Call(obj.ctx)

	for index, tids := range obj.baseData.StaticField.ThreadIDs {
		res, err := multi.GetResult(obj.ctx, fmt.Sprintf("getThreadMaskInfo_%d", index))
		if err != nil {
			tbcontext.WarningF(obj.ctx, "getThreadMaskInfo call post::getThreadMaskInfo fail, err=%v, input=%s", err, common.ToString(&tids))
			return errors.New("call post::getThreadMaskInfo fail")
		}
		maskRes, ok := res.(*postFrsProto.GetThreadMaskInfoRes)
		if !ok || maskRes == nil || maskRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(obj.ctx, "getThreadMaskInfo call post::getThreadMaskInfo fail, err=%v, input=%s, output=%s",
				err, common.ToString(&tids), common.ToString(maskRes))
			return errors.New("call post::getThreadMaskInfo fail")
		}

		for _, mask := range maskRes.GetOutput().GetThreadInfo() {
			if mask.GetIsDeleted() == 1 || mask.GetIsPartialVisible() == 1 || mask.GetIsFrsMask() == 1 {
				obj.baseData.StaticField.MaskThreadID[mask.GetThreadId()] = struct{}{}
			}
		}
	}

	return nil
}

// getForumInfo 获取吧信息
func (obj *getForumAlbumMoreProcessor) getForumInfo() error {
	multi := tbservice.Multi()

	for index, fids := range obj.baseData.StaticField.ForumIDs {
		forumReq := &tbservice.Parameter{
			Service: "forum",
			Method:  "mgetBtxInfoEx",
			Input: &forumProto.MgetBtxInfoExReq{
				ForumId: fids,
			},
			Output: &forumProto.MgetBtxInfoExRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(obj.ctx, fmt.Sprintf("mgetBtxInfoEx_%d", index), forumReq)
	}

	multi.Call(obj.ctx)

	for index, fids := range obj.baseData.StaticField.ForumIDs {
		res, err := multi.GetResult(obj.ctx, fmt.Sprintf("mgetBtxInfoEx_%d", index))
		if err != nil {
			tbcontext.WarningF(obj.ctx, "getForumInfo call forum::mgetBtxInfoEx fail, err=%v, input=%s", err, common.ToString(&fids))
			return errors.New("call forum::mgetBtxInfoEx fail")
		}
		forumRes, ok := res.(*forumProto.MgetBtxInfoExRes)
		if !ok || forumRes == nil || forumRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(obj.ctx, "getForumInfo call forum::mgetBtxInfoEx fail, err=%v, input=%s, output=%s",
				err, common.ToString(&fids), common.ToString(forumRes))
			return errors.New("call forum::mgetBtxInfoEx fail")
		}

		for fid, forumInfo := range forumRes.GetOutput() {
			obj.baseData.StaticField.RawForumInfo[fid] = forumInfo
		}
	}

	return nil
}

// getForumVerticalInfo 获取垂类信息
func (obj *getForumAlbumMoreProcessor) getForumVerticalInfo() error {
	forumField, err := util.GetTopNForum(obj.ctx)
	if err != nil {
		// 获取失败不阻塞
		tbcontext.WarningF(obj.ctx, "getForumVerticalInfo GetTopNForum fail, err=%v", err)
		return nil
	}
	for fid, field := range forumField {
		obj.baseData.StaticField.ForumTopVerticalField[uint32(fid)] = field
	}
	return nil
}

// getForumEnterNum 获取昨日进吧量
func (obj *getForumAlbumMoreProcessor) getForumEnterNum() error {
	multi := tbservice.Multi()

	for index, fids := range obj.baseData.StaticField.ForumIDs {
		fidList := make([]int64, 0, len(fids))
		for _, fid := range fids {
			fidList = append(fidList, int64(fid))
		}

		enterReq := &tbservice.Parameter{
			Service: "forumenter",
			Method:  "mgetForumEnterNum",
			Input: &commonProto.MgetForumEnterNumReq{
				ForumIds: fidList,
				Duration: proto.String(forumEnterNumDuration),
			},
			Output: &commonProto.MgetForumEnterNumRes{},
			Option: []tbservice.Option{
				tbservice.WithServiceName("common"),
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(obj.ctx, fmt.Sprintf("mgetForumEnterNum_%d", index), enterReq)
	}

	multi.Call(obj.ctx)

	for index, fids := range obj.baseData.StaticField.ForumIDs {
		res, err := multi.GetResult(obj.ctx, fmt.Sprintf("mgetForumEnterNum_%d", index))
		if err != nil {
			tbcontext.WarningF(obj.ctx, "getForumEnterNum call forumenter::mgetForumEnterNum fail, err=%v, input=%s", err, common.ToString(&fids))
			continue
		}
		enterRes, ok := res.(*commonProto.MgetForumEnterNumRes)
		if !ok || enterRes == nil || enterRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(obj.ctx, "getForumEnterNum call forumenter::mgetForumEnterNum fail, err=%v, input=%s, output=%s",
				err, common.ToString(&fids), common.ToString(enterRes))
			continue
		}

		for fid, info := range enterRes.GetData() {
			if fid <= 0 || info == nil {
				continue
			}
			obj.baseData.StaticField.ForumEnterNum[uint32(fid)] = info.GetEnterNum()
		}
	}

	return nil
}

// getForumHotNum 获取吧热度信息
func (obj *getForumAlbumMoreProcessor) getForumHotNum() error {
	multi := tbservice.Multi()

	for index, fids := range obj.baseData.StaticField.ForumIDs {
		hotReq := &tbservice.Parameter{
			Service: "common",
			Method:  "mgetHotByForumId",
			Input: map[string]any{
				"forum_ids": fids,
			},
			Output: &commonProto.MgetHotByForumIdRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(obj.ctx, fmt.Sprintf("mgetHotByForumId_%d", index), hotReq)
	}

	multi.Call(obj.ctx)

	for index, fids := range obj.baseData.StaticField.ForumIDs {
		res, err := multi.GetResult(obj.ctx, fmt.Sprintf("mgetHotByForumId_%d", index))
		if err != nil {
			tbcontext.WarningF(obj.ctx, "getForumHotNum call common::mgetHotByForumId fail, err=%v, input=%s", err, common.ToString(&fids))
			continue
		}
		hotRes, ok := res.(*commonProto.MgetHotByForumIdRes)
		if !ok || hotRes == nil || hotRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(obj.ctx, "getForumHotNum call common::mgetHotByForumId fail, err=%v, input=%s, output=%s",
				err, common.ToString(&fids), common.ToString(hotRes))
			continue
		}

		for _, info := range hotRes.GetOutput() {
			if info == nil || info.GetForumId() == 0 {
				continue
			}
			obj.baseData.StaticField.ForumHotNum[uint32(info.GetForumId())] = info.GetHotValue()
		}
	}

	return nil
}

// buildThreadList 数据格式化
func (obj *getForumAlbumMoreProcessor) buildThreadList() []*gfamProto.ThreadInfo {
	staticField := obj.baseData.StaticField
	threadList := make([]*gfamProto.ThreadInfo, 0, len(staticField.ThreadIDs))

	// 数据校验
	if len(staticField.RawTheadInfo) == 0 || len(staticField.RawForumInfo) == 0 {
		tbcontext.WarningF(obj.ctx, "buildThreadList rawData empty")
		return threadList
	}

	for _, tidList := range staticField.ThreadIDs {
		for _, tid := range tidList {

			if _, ok := staticField.MaskThreadID[tid]; ok {
				tbcontext.WarningF(obj.ctx, "buildThreadList mask thread, thread_id=%d", tid)
				continue
			}

			threadInfo, ok := staticField.RawTheadInfo[tid]
			if !ok || threadInfo == nil {
				continue
			}
			fid, ok := staticField.ThreadForumMap[tid]
			if !ok {
				continue
			}
			forumInfo, ok := staticField.RawForumInfo[fid]
			if !ok || forumInfo == nil {
				continue
			}
			// 鸿蒙版贴子过滤
			harmonyFilterOption := threadtype.HarmonyFilterOption{
				ThreadTypes: threadInfo.GetThreadTypes(),
			}
			if staticField.ClientType == stcdefine.CLIENT_TYPE_HARMONY && !threadtype.HarmonyFilter(harmonyFilterOption) {
				continue
			}

			// 基本信息
			thread := &gfamProto.ThreadInfo{
				ThreadId:       proto.Int64(int64(tid)),
				Title:          proto.String(threadInfo.GetTitle()),
				FirstFloorText: proto.String(threadInfo.GetAbstract()),
				PostNum:        proto.Int64(int64(threadInfo.GetPostNum())),
			}

			// 图片信息
			for _, media := range threadInfo.GetMedia() {
				if media == nil || media.GetType() != "pic" {
					continue
				}
				if len(media.GetBigPic()) == 0 || media.GetPicInfo() == nil || media.GetPicInfo().GetBig() == nil {
					continue
				}
				picInfo := media.GetPicInfo().GetBig()
				thread.ImgInfo = &gfamProto.ImgInfo{
					Url:   proto.String(util.GenPicURLForAlbum(media.GetBigPic())),
					Bsize: proto.String(fmt.Sprintf("%d,%d", picInfo.GetWidth(), picInfo.GetHeight())),
				}
				break
			}
			// 没拿到图片信息就不返回，避免前端显示问题
			if thread.ImgInfo == nil {
				continue
			}

			// 吧信息
			forum := &gfamProto.ForumInfo{
				Id:          proto.Int64(int64(fid)),
				Name:        proto.String(forumInfo.GetForumName().GetForumName()),
				Avatar:      proto.String(forumInfo.GetCard().GetAvatar()),
				MemberCount: proto.Int64(int64(forumInfo.GetStatistics().GetMemberCount())),
			}
			if field, ok := staticField.ForumTopVerticalField[fid]; ok && len(field) > 0 {
				forum.VerticalField = proto.String(field)
			}
			if enterNum, ok := staticField.ForumEnterNum[fid]; ok && enterNum > 0 {
				forum.LastVisitNum = proto.Int64(enterNum)
			}
			if hotNum, ok := staticField.ForumHotNum[fid]; ok && hotNum > 0 {
				forum.HotNum = proto.Int64(hotNum)
			}
			thread.ForumInfo = forum

			threadList = append(threadList, thread)
		}
	}

	return threadList
}
