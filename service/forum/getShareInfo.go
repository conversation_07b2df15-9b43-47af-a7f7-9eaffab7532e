package forum

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/tidwall/sjson"
	"google.golang.org/protobuf/proto"
	chatProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	scoreProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/score"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/tbmall"
	userProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/baidu/uidxuk"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getShareInfo"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func GetShareInfo(ctx context.Context, baseData *types.GetShareInfoData, response *getShareInfo.GetShareInfoResIdl) int {

	response.Data = &getShareInfo.GetShareInfo{}

	// 根据不同的Scene 走不同的逻辑
	switch baseData.Scene {
	case "score":
		// 通过tid获取score_list_id、thread_title
		scoreListID, threadTitle, err := getLevelInfo(ctx, baseData)
		if err != nil {
			tbcontext.WarningF(ctx, "getLevelInfo fail, err=[%v]", err)
		}
		if scoreListID <= 0 {
			tbcontext.WarningF(ctx, "getScoreListID fail, thread_id=[%d] scoreListId=[%v]", baseData.ThreadID, scoreListID)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		// 调用接口获取数据
		multi := tbservice.Multi()
		getThreadScoreInfoParam := &tbservice.Parameter{
			Service: "score",
			Method:  "getThreadScoreInfo",
			Input: map[string]interface{}{
				"score_list_id":       scoreListID,
				"user_id":             baseData.UserID,
				"sort_type":           1,
				"need_user_score":     1,
				"need_score_user_num": 1,
				"pn":                  1,
				"rn":                  9,
			},
			Output: &scoreProto.GetThreadScoreInfoRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getThreadScoreInfoParam", getThreadScoreInfoParam)
		multi.Call(ctx)

		// 处理数据
		getThreadScoreInfoRes := &scoreProto.GetThreadScoreInfoRes{}
		getThreadScoreInfoInter, err := multi.GetResult(ctx, "getThreadScoreInfoParam")
		if err != nil || getThreadScoreInfoInter == nil {
			tbcontext.WarningF(ctx, "GetThreadScoreInfo call score::getThreadScoreInfo fail, output: %v, err: %v", common.ToString(getThreadScoreInfoInter), err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		getThreadScoreInfoRes = getThreadScoreInfoInter.(*scoreProto.GetThreadScoreInfoRes)
		if getThreadScoreInfoRes.Errno == nil || getThreadScoreInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "GetThreadScoreInfo call score::getThreadScoreInfo, output: %v, err: %v", common.ToString(getThreadScoreInfoRes), err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		// 赋值
		response.Data.ThreadScore = make([]*getShareInfo.ShareInfo, 0)
		if getThreadScoreInfoRes.GetData().GetTotalCount() > 0 {
			// 拼接规则文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/woEAQB92mj/6VFuVCls3pmwOB
			linkUrl := fmt.Sprintf("https://tieba.baidu.com/p/%d?share=9105&see_lz=0&from=share", baseData.ThreadID)
			ShareTitlePrefix := getThreadScoreInfoRes.GetData().GetShareTitlePrefix()
			ShareSubtitle := getThreadScoreInfoRes.GetData().GetShareSubtitle()
			ShareUserSumMsg := getThreadScoreInfoRes.GetData().GetShareUserSumMsg()

			shareInfo := &getShareInfo.ShareInfo{
				Title:    proto.String(fmt.Sprintf("%s%s", ShareTitlePrefix, threadTitle)),
				Content:  proto.String(fmt.Sprintf("%s，%s", ShareUserSumMsg, ShareSubtitle)),
				ImageUrl: make([]string, 0),
				LinkUrl:  proto.String(linkUrl),
				Entry:    proto.String(""),
			}

			normalPics := make([]string, 0)
			weiboPics := make([]string, 0)
			for _, item := range getThreadScoreInfoRes.GetData().GetItems() {
				if len(normalPics) == 0 {
					normalPics = append(normalPics, item.GetPic())
				}
				if len(weiboPics) < 9 {
					weiboPics = append(weiboPics, item.GetPic())
				}
			}
			Entrys := []string{"qq", "qqzone", "weixin", "weixinzone", "weibo"}
			for _, entry := range Entrys {
				newShareInfo := proto.Clone(shareInfo).(*getShareInfo.ShareInfo)
				if entry == "weibo" {
					newShareInfo.Title = proto.String(fmt.Sprintf("【%s】（分享至@百度贴吧）%s", threadTitle, ShareUserSumMsg))
					newShareInfo.Content = proto.String(fmt.Sprintf("#%s#", ShareSubtitle))
					newShareInfo.ImageUrl = weiboPics
				} else {
					newShareInfo.ImageUrl = normalPics
				}
				newShareInfo.Entry = proto.String(entry)
				response.Data.ThreadScore = append(response.Data.ThreadScore, newShareInfo)
			}
		}
	case "agent":
		// botUK转成uid
		uid, err := uidxuk.UK2UID(baseData.BotUK)
		if err != nil {
			uid = common.Tvttt(baseData.BotUK, common.TTT_UINT64).(uint64)
			tbcontext.WarningF(ctx, "uk2uid fail, err=%v, uk=%s", err, baseData.BotUK)
		}

		// 调用MgetAiBotUserInfoByUid方法 获取agent基础信息
		var uids []int64
		uids = append(uids, int64(uid))

		// 获取智能体信息
		input := &chatProto.MgetAiBotUserInfoByUidReq{
			BotUids: uids,
		}
		output := &chatProto.MgetAiBotUserInfoByUidRes{}
		err = tbservice.Call(ctx, "chat", "mgetAiBotUserInfoByUid", input, output, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || output == nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.FatalF(ctx, "call chat::mgetAiBotUserInfoByUid fail, err=%v, input=%s, output=%s", err, common.ToString(input), common.ToString(output))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		// bot信息
		botUserInfo := output.GetData().GetBotList()[int64(uid)]

		shareInfo := &getShareInfo.ShareInfo{
			Title:    proto.String(""),
			Content:  proto.String(""),
			ImageUrl: make([]string, 0),
			LinkUrl:  proto.String(""),
			Entry:    proto.String(""),
		}

		userTotalMsg := "0"
		if botUserInfo.GetDialogueUserNum() > 9999 {
			userTotalFloat := fmt.Sprintf("%.1f", float64(botUserInfo.GetDialogueUserNum())/float64(10000))
			userTotalMsg = fmt.Sprintf("%sW", userTotalFloat)
		} else {
			userTotalMsg = fmt.Sprintf("%d", botUserInfo.GetDialogueUserNum())
		}

		image := make([]string, 0)
		image = append(image, botUserInfo.GetPortrait())

		linkUrl := "/mo/q/hybrid-main-chatgroup/chat-immerse?loadingSignal=1&nonavigationbar=1&customfullscreen=1"

		if botUserInfo.GetPaType() == 1 {
			// 游戏
			Entrys := []string{"qq", "qqzone", "weixin", "weixinzone", "weibo"}
			for _, entry := range Entrys {

				if entry == "qq" || entry == "weixin" {
					shareInfo = &getShareInfo.ShareInfo{
						Title:    proto.String(botUserInfo.GetAiGamePlot().GetPlotTitle()),
						Content:  proto.String(userTotalMsg + "人玩过" + " \n" + "\"我发现了一个有趣的AI游戏，点击立即开始挑战\""),
						ImageUrl: image,
						LinkUrl:  proto.String(linkUrl),
						Entry:    proto.String(entry),
					}
					response.Data.AgentShare = append(response.Data.AgentShare, shareInfo)
				}

				if entry == "qqzone" || entry == "weixinzone" {
					shareInfo = &getShareInfo.ShareInfo{
						Title:    proto.String("贴吧AI游戏 | " + botUserInfo.GetAiGamePlot().GetPlotTitle()),
						Content:  proto.String(""),
						ImageUrl: image,
						LinkUrl:  proto.String(linkUrl),
						Entry:    proto.String(entry),
					}
					response.Data.AgentShare = append(response.Data.AgentShare, shareInfo)
				}

				if entry == "weibo" {
					shareInfo = &getShareInfo.ShareInfo{
						Title:    proto.String(""),
						Content:  proto.String("【" + botUserInfo.GetAiGamePlot().GetPlotTitle() + "】" + "我发现了一个有趣的AI游戏（分享自@百度贴吧）" + userTotalMsg + "人玩过" + "\n" + "#来百度贴吧，挑战AI游戏#"),
						ImageUrl: image,
						LinkUrl:  proto.String(linkUrl),
						Entry:    proto.String(entry),
					}
					response.Data.AgentShare = append(response.Data.AgentShare, shareInfo)
				}

			}
		} else {
			// 智能体

			Entrys := []string{"qq", "qqzone", "weixin", "weixinzone", "weibo"}
			for _, entry := range Entrys {

				if entry == "qq" || entry == "weixin" {
					shareInfo = &getShareInfo.ShareInfo{
						Title:    proto.String("快来和" + botUserInfo.GetName() + "聊天吧"),
						Content:  proto.String(userTotalMsg + "人聊过" + " \n" + "\"我发现了一个有趣的AI智能体，点击立即开始聊天\""),
						ImageUrl: image,
						LinkUrl:  proto.String(linkUrl),
						Entry:    proto.String(entry),
					}
					response.Data.AgentShare = append(response.Data.AgentShare, shareInfo)
				}

				if entry == "qqzone" || entry == "weixinzone" {
					shareInfo = &getShareInfo.ShareInfo{
						Title:    proto.String("贴吧AI智能体 | 快来和" + botUserInfo.GetName() + "聊天吧"),
						Content:  proto.String(""),
						ImageUrl: image,
						LinkUrl:  proto.String(linkUrl),
						Entry:    proto.String(entry),
					}
					response.Data.AgentShare = append(response.Data.AgentShare, shareInfo)
				}

				if entry == "weibo" {
					shareInfo = &getShareInfo.ShareInfo{
						Title:    proto.String(""),
						Content:  proto.String("【" + botUserInfo.GetName() + "】" + "我发现了一个有趣的AI智能体（分享自@百度贴吧） " + userTotalMsg + "人聊过" + "\n" + "#来百度贴吧，畅聊AI智能体#"),
						ImageUrl: image,
						LinkUrl:  proto.String(linkUrl),
						Entry:    proto.String(entry),
					}
					response.Data.AgentShare = append(response.Data.AgentShare, shareInfo)
				}
			}

		}
	case "shop_goods":
		linkUrl := fmt.Sprintf("https://tieba.baidu.com/p/%d", baseData.ThreadID)
		goodInfo := getGoodsList(ctx, baseData)
		if goodInfo == nil {
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		entry := []string{"qq", "qqzone", "weixin", "weixinzone", "weibo"}
		for _, e := range entry {
			shareInfo := &getShareInfo.ShareInfo{
				Title:    proto.String(goodInfo.GetGoodsName()),
				Content:  proto.String(goodInfo.GetGoodsDesc()),
				ImageUrl: goodInfo.ImgList,
				LinkUrl:  proto.String(linkUrl),
				Entry:    proto.String(e),
			}
			response.Data.ThreadScore = append(response.Data.ThreadScore, shareInfo)
		}
		// 处理分享链
		s := `{"page":"write/writePost","pageParams":{"composeList":[{"composeConfig":{"composeId":"normal",
"composeTitle":"贴子"},"morePanelConfig":{"morePanelItems":[{"name":"TOOL_ID_AT","status":"1","title":"好友"},
{"name":"TOOL_ID_VOTE","status":"1","title":"投票"},{"name":"TOOL_ID_AGENT","status":"1","title":"AI智能体",
"event":"hybrid_deal"}]},"tipsBarConfig":{"tipsBarItems":[]},"toolsBarConfig":{"toolsBarItems":[{"imageAddType":"0",
"name":"TOOL_ID_IMAGE","status":"0","title":"图片"},{"name":"TOOL_ID_VIDEO","status":"1","title":"视频"},{"name":
"TOOL_ID_VOICE","status":"0","title":"语音"},{"name":"TOOL_ID_LINK","status":"0","title":"链接"},{"name":
"TOOL_ID_EMOTION","status":"0","title":"表情"},{"name":"TOOL_ID_MEMBER","status":"0","title":"会员","items":
[{"name":"TOOL_ID_BUBBLE","status":"1","title":"气泡"},{"name":"TOOL_ID_TAIL","status":"1","title":"小尾巴"},
{"name":"TOOL_ID_PERSONALIZED_CARD","status":"1","title":"个性卡片"}]},{"name":"TOOL_ID_MORE","status":"0",
"title":"更多"}]},"webViewConfig":{"url":"https://tieba.baidu.com/mo/q/hybrid-main-post/app-post"}}],
"useH5WritePage":"1","from":"share_panel","write_from":"share_panel","call_source":"share_panel","use_home_style":
"0","not_read_draft":"1","webviewData":{"forumShopProduct":{"shareLink":""}}}}`
		s, _ = sjson.Set(s, "pageParams.webviewData.forumShopProduct.shareLink", linkUrl)
		s = url.QueryEscape(s)
		s = "tiebaapp://router/portal?params=" + s
		response.Data.Schema = proto.String(s)

	default:
		tbcontext.WarningF(ctx, "Scene param_error, Scene=(%s)", baseData.Scene)
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 获取用户头像串
	getUserInput := &userProto.MgetUserDataExReq{
		UserId: []int64{int64(baseData.UserID)},
	}
	getUserOutput := &userProto.MgetUserDataExRes{}
	err := tbservice.Call(ctx, "user", "mgetUserDataEx", getUserInput, getUserOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || getUserOutput == nil || getUserOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call user::mgetUserDataEx fail, err=%v, input=%s, output=%s",
			err, common.ToString(getUserInput), common.ToString(getUserOutput))
	}
	if getUserOutput != nil && getUserOutput.GetUserInfo() != nil {
		if user, ok := getUserOutput.GetUserInfo()[baseData.UserID]; ok {
			response.Data.Portrait = proto.String(tbportrait.Encode(int64(user.GetUserId()), user.GetUserName(), time.Now().Unix()))
		}
	}

	return tiebaerror.ERR_SUCCESS
}

// 获取打分贴id信息及贴子标题
func getLevelInfo(ctx context.Context, baseData *types.GetShareInfoData) (uint64, string, error) {
	multi := tbservice.Multi()

	// 获取打分贴等级信息
	threadInput := &tbservice.Parameter{
		Service: "post",
		Method:  "mgetThread",
		Input: map[string]interface{}{
			"thread_ids": []uint64{baseData.ThreadID},
		},
		Output: &frs.MgetThreadRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "post::mgetThread", threadInput)

	multi.Call(ctx)

	scoreListID := uint64(0)
	threadTitle := string("")
	mgetThreadResInfo, err := multi.GetResult(ctx, "post::mgetThread")
	if err == nil || mgetThreadResInfo != nil {
		mgetThreadRes, ok := mgetThreadResInfo.(*frs.MgetThreadRes)
		if ok && mgetThreadRes != nil && mgetThreadRes.GetErrno() == tiebaerror.ERR_SUCCESS {
			resThreadList := mgetThreadRes.GetOutput().GetThreadList()
			threadInfo := resThreadList[baseData.ThreadID]
			scoreInfo, err := threadInfo.GetScoreInfoStruct()
			threadTitle = threadInfo.GetTitle()
			if err == nil {
				scoreListID = scoreInfo.GetScoreListId()
			} else {
				tbcontext.WarningF(ctx, "get score info error, err:[%s] output=[%s]", err.Error(), common.ToString(threadInfo))
			}
		} else {
			tbcontext.WarningF(ctx, "call mgetThread fail, thread_id=[%d], output=[%v]", baseData.ThreadID, mgetThreadResInfo)
		}
	} else {
		tbcontext.WarningF(ctx, "call mgetThread fail, thread_id=[%d], err=[%v]", baseData.ThreadID, err)
	}

	return scoreListID, threadTitle, nil
}

// 获取商品信息
func getGoodsList(ctx context.Context, baseData *types.GetShareInfoData) *tbmall.ShopGoodsInfo {
	req := &tbmall.GetShopGoodsInfoReq{
		UserId: proto.Uint64(baseData.UserID),
		// UserId:   proto.Uint64(1),
		// ThreadId: proto.Uint64(1),
		ThreadId: proto.Uint64(baseData.ThreadID),
	}

	res := new(tbmall.GetShopGoodsInfoRes)

	tbmallOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("tbmall_go"),
	}
	err := tbservice.Call(ctx, "tbmall", "getShopGoodsInfo", req, res, tbmallOption...)

	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call tbmall:getShopGoodList fail, err:%v,input:%s  output:%s", err, common.ToString(req), common.ToString(res))
		return nil
	}
	return res.GetData()
}
