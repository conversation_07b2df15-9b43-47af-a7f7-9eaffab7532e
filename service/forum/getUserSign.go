package forum

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/golang/protobuf/proto"

	signProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/sign"
	userProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getUserSign"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	signRobotUID           = 6400359422                              // 签到机器人uid
	signRobotUK            = "6XK-hmd9_x0FirQMOzPWAg"                // 签到机器人uk
	wordlistName           = "tb_wordlist_redis_group_chat_activity" // 签到词表
	signOptimizeExperiment = "12_46_sign_optimize_experiment_a"      // 签到优化实验组
	signBotSignSkillID     = 10005                                   // 签到skill id
	signBotReSignSkillID   = 10006                                   // 补签skill id
)

type pageParams struct {
	RoomID            string         `json:"roomId,omitempty"`
	IsShowImGroupList int            `json:"is_show_im_group_list,omitempty"`
	Ability           map[string]any `json:"ability,omitempty"`
}

type jumpScheme struct {
	Page       string     `json:"page,omitempty"`
	Refer      string     `json:"refer,omitempty"`
	PageParams pageParams `json:"pageParams,omitempty"`
}

// GetUserSign 函数用于获取用户签到信息
// ctx 表示上下文内容，类型为 context.Context
// baseData 参数表示基本数据结构，类型为 types.GetUserSignBaseData
// response 参数表示返回数据结构，类型为 getUserSign.GetUserSignResIdl
func GetUserSign(ctx context.Context, baseData *types.GetUserSignBaseData,
	response *getUserSign.GetUserSignResIdl) int {

	from := baseData.Request.GetFrom()
	senceFrom := baseData.Request.GetSceneFrom()
	forumIds := baseData.Request.GetForumIds()

	if senceFrom != "" {
		from = senceFrom
	}

	if forumIds == "" {
		tbcontext.WarningF(ctx, "GetUserSign input param error, forumIds = %v", forumIds)
		return tiebaerror.ERR_PARAM_ERROR
	}

	forumIdsArr := []string{}
	forumIdsArr = strings.Split(forumIds, ",")

	if len(forumIdsArr) == 0 {
		tbcontext.WarningF(ctx, "GetUserSign input param error, forumIds empty")
		return tiebaerror.ERR_PARAM_ERROR
	}

	//if from == "frs" && len(forumIdsArr) != 1 {
	// tbcontext.WarningF(ctx, "GetUserSign input param error, when from == frs, only one forumId can be chooes")
	// return tiebaerror.ERR_PARAM_ERROR
	//}

	if from != "frs" && from != "recom" {
		tbcontext.WarningF(ctx, "GetUserSign input param error, only support from == frs")
		return tiebaerror.ERR_PARAM_ERROR
	}

	uid, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)

	//uid = 13090999
	if from == "frs" {
		//获取漏签次数以及本日是否签到状态

		year := time.Now().Year()
		month := time.Now().Month()

		input := map[string]interface{}{
			"forum_id": forumIdsArr[0],
			"user_id":  uid,
			"year":     year,
			"month":    month,
		}

		res := new(signProto.GetUserMonSignInfoRes)
		err := tbservice.Call(ctx, "sign", "getUserMonSignInfo", input, res,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service sign:getUserMonSignInfo, err = %v, input = %v, output = %v", err, input, res)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		arrMonthSignInfo := res.GetHisInfo()
		intTimeOut := time.Now()
		isSignToday := int32(0)
		hasSignRoom := int32(0)

		for _, value := range arrMonthSignInfo {
			signTime, _ := common.Tvttt(*(value.SignTime), common.TTT_INT64).(int64)
			if time.Unix(signTime, 0).Day() == intTimeOut.Day() {
				isSignToday = int32(1)
			}
		}
		//漏签天数

		intMissDayNum := int32(time.Now().Day() - len(arrMonthSignInfo))
		//jumpSchemeSign := "https://tieba.baidu.com/mo/q/resign/index?forum_id="++"&nonavigationbar=1&customfullscreen=1"
		jumpSchemeSign := fmt.Sprintf("%s%s%s", "https://tieba.baidu.com/mo/q/resign/index?forum_id=", forumIdsArr[0], "&nonavigationbar=1&customfullscreen=1")
		signUserInfo := &getUserSign.SignUser{
			UserId:           nil,
			IsSignIn:         proto.Int32(isSignToday),
			UserSignRank:     nil,
			SignTime:         nil,
			ContSignNum:      proto.Int32(res.GetUserInfo().GetContSignNum()),
			CoutTotalSignNum: proto.Int32(res.GetUserInfo().GetCoutTotalSingNum()),
			IsOrgDisabled:    nil,
			CSignNum:         nil,
			HunSignNum:       nil,
			TotalResignNum:   nil,
			MissSignNum:      proto.Int32(intMissDayNum),
			JumpScheme:       proto.String(jumpSchemeSign),
		}
		signForumInfo := &getUserSign.SignForum{
			IsOn:              nil,
			IsFilter:          nil,
			ForumInfo:         nil,
			CurrentRankInfo:   nil,
			YesterdayRankInfo: nil,
			WeeklyRankInfo:    nil,
			Level1DirName:     nil,
			Level2DirName:     nil,
			MonthlyRankInfo:   nil,
			ForumId:           nil,
		}
		signInfo := &getUserSign.SignInfo{
			UserInfo:        signUserInfo,
			ForumInfo:       signForumInfo,
			HasChatroomSign: proto.Int32(hasSignRoom),
			//ChatroomSignInfo: chatroomSignInfo,
		}
		int64ForumId := common.Tvttt(forumIdsArr[0], common.TTT_INT64).(int64)
		forumInfo := &getUserSign.ForumInfo{
			Id:         proto.Int64(int64ForumId),
			SignInInfo: signInfo,
		}

		response.Data.Forum = append(response.Data.Forum, forumInfo)
	}

	if from == "recom" {
		input := map[string]interface{}{
			"forum_id": forumIdsArr,
			"user_id":  uid,
		}

		res := new(signProto.GetUserSignForumsRes)
		err := tbservice.Call(ctx, "sign", "getUserSignForums", input, res,
			tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "fail to call service sign:getUserSignForums, err = %v, input = %v, output = %v", err, input, res)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		if res.GetArrUserInfo() != nil {
			for fid, value := range res.GetArrUserInfo() {
				signUserInfo := &getUserSign.SignUser{
					UserId:           nil,
					IsSignIn:         proto.Int32(value.GetIsSignIn()),
					UserSignRank:     nil,
					SignTime:         nil,
					ContSignNum:      nil,
					CoutTotalSignNum: nil,
					IsOrgDisabled:    nil,
					CSignNum:         nil,
					HunSignNum:       nil,
					TotalResignNum:   nil,
					MissSignNum:      proto.Int32(0),
				}
				signForumInfo := &getUserSign.SignForum{
					IsOn:              nil,
					IsFilter:          nil,
					ForumInfo:         nil,
					CurrentRankInfo:   nil,
					YesterdayRankInfo: nil,
					WeeklyRankInfo:    nil,
					Level1DirName:     nil,
					Level2DirName:     nil,
					MonthlyRankInfo:   nil,
					ForumId:           nil,
				}
				signInfo := &getUserSign.SignInfo{
					UserInfo:         signUserInfo,
					ForumInfo:        signForumInfo,
					HasChatroomSign:  proto.Int32(0),
					ChatroomSignInfo: nil,
				}

				forumInfo := &getUserSign.ForumInfo{
					Id:         proto.Int64(int64(fid)),
					SignInInfo: signInfo,
				}

				response.Data.Forum = append(response.Data.Forum, forumInfo)
			}
		}

	}

	return tiebaerror.ERR_SUCCESS
}

type Content struct {
	Text string `json:"text,omitempty"`
}

type RobotParams struct {
	Scene string `json:"scene,omitempty"`
	Type  int    `json:"type,omitempty"`
}

type AtData struct {
	AtType     string `json:"at_type,omitempty"`
	AtBaiduUk  string `json:"at_baidu_uk,omitempty"`
	AtName     string `json:"at_name,omitempty"`
	AtPortrait string `json:"at_portrait,omitempty"`
}

type StructContent struct {
	Type        string       `json:"type,omitempty"`
	AtData      *AtData      `json:"at_data,omitempty"`
	Content     *Content     `json:"content,omitempty"`
	RobotParams *RobotParams `json:"robot_params,omitempty"`
}

type AbilityConf struct {
	StructContent []*StructContent `json:"struct_content,omitempty"`
}

func buildAbilityConf(ctx context.Context, isReSign bool) (abilityMap map[string]any, err error) {
	abilityMap = make(map[string]any)
	abilityMap["ability_type"] = "send_struct_msg"
	// 处理@信息
	atRobot := new(StructContent)
	atRobot.Type = "at"
	// 处理机器人信息
	robotInfo := new(AtData)
	robotInfo.AtType = "user"
	robotInfo.AtBaiduUk = signRobotUK // 签到机器人uk
	// 查询机器人信息
	info := getSignBotInfo(ctx)
	if info == nil {
		err = errors.New("get sign bot info err")
		return
	}
	robotInfo.AtName = info.GetUserInfoNameShowV2(ctx)
	robotInfo.AtPortrait = tbportrait.Encode(signRobotUID, *info.UserName, time.Now().Unix())
	atRobot.AtData = robotInfo

	// 处理机器人文案
	text := new(StructContent)
	text.Type = "text"
	// 签到文案
	c := new(Content)
	c.Text = getAbilityConfContentSign(ctx)
	// 补签替换文案
	if isReSign {
		c.Text = getAbilityConfContentReSign(ctx)
	}
	text.Content = c

	// 处理机器人参数 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/ddpRviE1Ce/woOxg3sOU4/JPoNuVyZSHo4Jj
	param := new(StructContent)
	param.Type = "robot_params"
	p := new(RobotParams)
	p.Scene = "tieba_group_chat"
	p.Type = signBotSignSkillID
	// 补签更换签到skill id
	if isReSign {
		p.Type = signBotReSignSkillID
	}
	param.RobotParams = p

	ab := new(AbilityConf)
	ab.StructContent = []*StructContent{atRobot, text, param}
	abilityMap["ability_conf"] = ab

	return
}

// 查询签到机器人的用户信息, 有本地缓存
func getSignBotInfo(ctx context.Context) *userProto.UserInfo {

	req := new(userProto.GetUserDataExReq)
	req.UserId = proto.Int64(signRobotUID)
	outPut := new(userProto.GetUserDataExRes)
	err := tbservice.Call(ctx, "user", "getUserDataEx", req, outPut,
		tbservice.WithConverter(tbservice.JSONITER), tbservice.WithCacheResult())
	if err != nil || outPut.Errno == nil || outPut.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call user::getUserData failed, err:[%s]", err.Error())
		return nil
	}
	return outPut.GetUserInfo()
}

// 获取签到提示文案
func getAbilityConfContentSign(ctx context.Context) string {
	text := "【互动：每日签到】"
	// 词表获取连续签到文案
	queryKeys := []string{
		"sign_bot_ability_conf_sign_content",
	}

	tips, err := wordserver.QueryKeys(ctx, wordlistName, queryKeys)
	if err != nil {
		tbcontext.WarningF(ctx, "getRobot query wordlist failed. err=%v, keys=%s，use default text", err, common.ToString(&queryKeys))
		return text
	}
	text = tips[0]
	return text
}

// 获取补签签到文案
func getAbilityConfContentReSign(ctx context.Context) string {
	text := "【互动：吧内补签】"
	// 词表获取连续签到文案
	queryKeys := []string{
		"sign_bot_ability_conf_resign_content",
	}

	tips, err := wordserver.QueryKeys(ctx, wordlistName, queryKeys)
	if err != nil {
		tbcontext.WarningF(ctx, "getRobot query wordlist failed. err=%v, keys=%s，use default text", err, common.ToString(&queryKeys))
		return text
	}
	text = tips[0]
	return text
}
