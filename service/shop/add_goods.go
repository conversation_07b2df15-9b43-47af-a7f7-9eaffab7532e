package shop

import (
	"context"
	"strings"

	"google.golang.org/protobuf/proto"
	tbmallProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/tbmall"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	goodsProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func AddGoods(
	ctx context.Context, baseData *types.AddForumShopGoodsBaseData, response *goodsProto.AddForumShopGoodsResIdl,
) int {
	req := baseData.Request

	descImg := strings.Split(req.GetGoodsDescList(), ",")
	img := strings.Split(req.GetImgList(), ",")

	goodsInfo := &tbmallProto.ShopGoodsInfo{
		SourceUrl:    proto.String(req.GetSourceUrl()),
		GoodsName:    proto.String(req.GetGoodsName()),
		GoodsDesc:    proto.String(req.GetGoodsDesc()),
		GoodsDescImg: descImg,
		ImgList:      img,
		ChannelId:    proto.Uint32(req.GetChannelId()),
		ForumId:      proto.Uint64(req.GetForumId()), // 商品状态 1  已上架、2 已下架（主动）、3 已下架（被动）、4 审核中、5 审核失败
		OpUid:        proto.Uint64(baseData.UserID),
		CardType:     proto.Uint32(req.GetCardType()),
		Id:           proto.Int64(int64(req.GetId())),
	}

	addReq := &tbmallProto.AddShopGoodsInfoReq{
		GoodsInfo: goodsInfo,
	}
	addRes := &tbmallProto.AddShopGoodsInfoRes{}
	tbmallOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("tbmall_go"),
	}

	// 调用tbmall服务
	err := tbservice.Call(ctx, "tbmall", "addShopGoodsInfo", addReq, addRes, tbmallOption...)
	if err != nil || addRes.GetErrno() != 0 {
		tbcontext.WarningF(ctx, "addShopGoodsInfo input:[%v],output:[%v]", addReq, addRes)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	if addRes.GetData().GetErrText() != "" {
		response.Data.ErrInfo = &goodsProto.AddError{Text: proto.String(addRes.GetData().GetErrText())}
	}

	return tiebaerror.ERR_SUCCESS
}
