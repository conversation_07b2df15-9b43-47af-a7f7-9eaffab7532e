package shop

import (
	"context"
	"errors"

	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/tbmall"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func Convert(
	ctx context.Context, baseData *types.ShopGoodsConvertURLBaseData,
	response *goods.ConvertForumShopLinkResIdl,
) int {

	var err error

	g := gtask.Group{
		Concurrent:    3,
		AllowSomeFail: false,
	}
	var convertInfo *tbmall.ConvertURLData
	g.Go(
		func() error {
			convertInfo = convertURL(ctx, baseData)
			if convertInfo == nil {
				return errors.New("get convertURL failed")
			}
			return nil
		},
	)

	var configData map[string]string
	g.Go(
		func() error {
			configData, err = wordserver.QueryItems(
				ctx, wordlistForumShop, []string{wordlistKeyConvertConf, wordlistKeyPromotionRate},
			)
			if err != nil {
				tbcontext.WarningF(ctx, "Failed to fetch %s: %v", wordlistForumShop, err)
				return err
			}
			return nil
		},
	)

	_, err = g.Wait()
	if err != nil {
		tbcontext.WarningF(ctx, "get convertURL info err")
		return tiebaerror.CALL_DRS_SER_FAIL
	}

	dc := &client.ConvertDefaultConf{}

	convertConf := configData[wordlistKeyConvertConf]

	rate := cast.ToUint32(configData[wordlistKeyPromotionRate])
	if rate == 0 {
		rate = 100
	}

	err = jsoniter.Unmarshal([]byte(convertConf), dc)
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to unmarshal %s: %v", wordlistKeyConvertConf, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	response.Data.DefaultConf = dc
	if convertInfo.GetErrText() != "" {
		response.Data.ErrInfo = &goods.URLError{
			Text: convertInfo.ErrText,
		}
		return tiebaerror.ERR_SUCCESS
	}

	if convertInfo.GetAuthLink() != "" {
		response.Data.AuthLink = convertInfo.AuthLink
		return tiebaerror.ERR_SUCCESS
	}

	goodsInfo := convertInfo.GetGoodsInfo()

	response.Data.GoodsInfo = &client.ShopGoodsInfo{
		GoodsName: goodsInfo.GoodsName,
		Price:     goodsInfo.Price,
		ImgList:   goodsInfo.ImgList,
		// GoodsDesc: goodsInfo.GoodsDesc,
		ListingReason: &client.ListingReason{
			ImgList: goodsInfo.GetGoodsDescImg(),
		},
		EstimatedEarnings: proto.Uint32((goodsInfo.GetPromotionAmount() * rate) / 100),
	}
	return tiebaerror.ERR_SUCCESS
}

func convertURL(ctx context.Context, baseData *types.ShopGoodsConvertURLBaseData) *tbmall.ConvertURLData {
	req := &tbmall.ConvertURLReq{
		SourceUrl: baseData.Request.LinkUrl,
		ChannelId: baseData.Request.ChannelId,
		OpUid:     proto.Uint64(baseData.UserID),
		// FromScene: proto.String("op"),
	}

	res := new(tbmall.ConvertURLRes)

	tbmallOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("tbmall_go"),
	}
	err := tbservice.Call(ctx, "tbmall", "convertURL", req, res, tbmallOption...)

	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call tbmall:convertURL fail, err:[%v], output:[%s]", err, common.ToString(res))
		return nil
	}
	return res.GetData()
}
