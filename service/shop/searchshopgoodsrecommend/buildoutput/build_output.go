package buildoutput

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/foruminfo"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shopgoodssearchrecommend"
)

const opName = "buildoutput"

type Processor struct {
	output    *goods.SearchShopGoodsRes
	recommend shopgoodssearchrecommend.ShopGoodsSearchRecommend
	forumInfo foruminfo.ForumInfo
}

func NewProcess() *Processor {
	p := new(Processor)
	p.output = &goods.SearchShopGoodsRes{}
	return p
}

func (p *Processor) Process(ctx context.Context) error {
	forumInfo := p.forumInfo.GetRawForums()
	for _, g := range p.recommend.GetRecommend() {
		g.ForumName = proto.String(forumInfo[uint32(g.GetForumId())].GetForumName().GetForumName())
	}

	p.output.GoodsList = p.recommend.GetRecommend()

	return nil
}
