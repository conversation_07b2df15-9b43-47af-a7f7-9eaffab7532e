package buildoutput

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/foruminfo"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shopgoodssearchrecommend"
)

type OperatorBuildOutput struct {
	BaseAction *client.UIBaseAction                              `inject:"canLost=false,canNil=false"`
	Recommend  shopgoodssearchrecommend.ShopGoodsSearchRecommend `inject:"canLost=false,canNil=false"`
	ForumInfo  foruminfo.ForumInfo                               `inject:"canLost=false,canNil=false"`
}

func (o *OperatorBuildOutput) DoImpl(ctx *engine.Context) error {
	p := NewProcess()

	p.recommend = o.Recommend
	p.forumInfo = o.ForumInfo

	err := p.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	// 导出数据，供controller输出返回值使用
	ctx.MustRegisterInstance(p.output)
	return nil
}
