package shop

import (
	"context"

	"google.golang.org/protobuf/proto"
	tbmallProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/tbmall"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	goodsProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// UpdateForumShopGoods 更新商品信息
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：baseData：输入数据
// @Param：response：输出数据
// @Return：int：错误码
func UpdateForumShopGoods(
	ctx context.Context, baseData *types.UpdateForumShopGoodsBaseData, response *goodsProto.UpdateForumShopGoodsResIdl,
) int {
	req := baseData.Request
	if req.GetGoodsId() == 0 {
		tbcontext.WarningF(ctx, "UpdateForumShopGoods param error:thread_id=0 or goods_id=0, req:[%v]", req)
		return tiebaerror.ERR_PARAM_ERROR
	}
	if req.GoodsStatus != nil && req.GetGoodsStatus() != 5 {
		tbcontext.WarningF(ctx, "UpdateForumShopGoods param error:goods_status!=2, req:[%v]", req)
		return tiebaerror.ERR_PARAM_ERROR
	}

	goodsInfo := &tbmallProto.ShopGoodsInfo{
		Id:          proto.Int64(req.GetGoodsId()),
		IsDelete:    proto.Uint32(req.GetIsDelete()),
		GoodsStatus: proto.Uint32(req.GetGoodsStatus()), // 商品状态 1  已上架、2 已下架（主动）、3 已下架（被动）、4 审核中、5 审核失败
	}

	updateReq := &tbmallProto.UpdateShopGoodsInfoReq{
		GoodsInfo: goodsInfo,
	}
	updateRes := &tbmallProto.UpdateShopGoodsInfoRes{}
	tbmallOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("tbmall_go"),
	}

	// 调用tbmall服务
	err := tbservice.Call(ctx, "tbmall", "updateShopGoodsInfo", updateReq, updateRes, tbmallOption...)
	if err != nil || updateRes.GetErrno() != 0 {
		tbcontext.WarningF(ctx, "UpdateForumShopGoods input:[%v],output:[%v]", updateReq, updateRes)
		// return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	return tiebaerror.ERR_SUCCESS
}
