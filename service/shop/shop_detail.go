package shop

import (
	"context"
	"errors"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/tbmall"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func Detail(
	ctx context.Context, baseData *types.GetForumShopGoodsDetailBaseData,
	response *goods.GetForumShopDetailResIdl,
) int {

	var err error

	g := gtask.Group{
		Concurrent:    3,
		AllowSomeFail: false,
	}
	var overview *tbmall.ShopGoodsOrderOverview
	g.Go(
		func() error {
			overview = getOrderOverview(ctx, baseData.Request.GetForumId())
			if overview == nil {
				tbcontext.WarningF(ctx, "getOrderOverview err")
				return errors.New("getOrderOverview err")
			}
			return nil
		},
	)

	var shopInfo *tbmall.GetShopData
	g.Go(
		func() error {
			shopInfo = getShopInfo(ctx, baseData.Request.GetForumId(), 1)
			if shopInfo == nil {
				tbcontext.WarningF(ctx, "getShopInfo err")
				return errors.New("getShopInfo err")
			}
			return nil
		},
	)

	var bannerData map[string]string
	g.Go(
		func() error {
			bannerData, err = wordserver.QueryItems(
				ctx, wordlistForumShop, []string{wordlistKeyBannerConf},
			)
			if err != nil {
				tbcontext.WarningF(ctx, "Failed to fetch %s: %v", wordlistForumShop, err)
				return err
			}
			return nil
		},
	)

	_, err = g.Wait()
	if err != nil {
		tbcontext.WarningF(ctx, "get convertURL info err")
		return tiebaerror.CALL_DRS_SER_FAIL
	}

	response.Data.DataOverview = []*client.ShopGoodsOrderOverviewData{
		{
			Title:     proto.String("今日订单量"),
			Today:     proto.Uint32(overview.GetOrders().GetToday()),
			Yesterday: proto.Uint32(overview.GetOrders().GetYesterday()),
			DataType:  proto.String("orders"),
		},
		{
			Title:     proto.String("今日成交额"),
			Today:     proto.Uint32(overview.GetTransactions().GetToday()),
			Yesterday: proto.Uint32(overview.GetTransactions().GetYesterday()),
			DataType:  proto.String("transactions"),
		},
		{
			Title:     proto.String("今日预计收益"),
			Today:     proto.Uint32(overview.GetEstimatedEarnings().GetToday()),
			Yesterday: proto.Uint32(overview.GetEstimatedEarnings().GetYesterday()),
			DataType:  proto.String("estimatedEarnings"),
		},
		{
			Title:    proto.String("累计订单量"),
			Today:    proto.Uint32(overview.GetOrders().GetGrand()),
			IsGrand:  proto.Uint32(1),
			DataType: proto.String("orders"),
		},
		{
			Title:    proto.String("累计成交额"),
			Today:    proto.Uint32(overview.GetTransactions().GetGrand()),
			DataType: proto.String("transactions"),
			IsGrand:  proto.Uint32(1),
		},
		{
			Title:    proto.String("累计总收益"),
			Today:    proto.Uint32(overview.GetEstimatedEarnings().GetGrand()),
			IsGrand:  proto.Uint32(1),
			DataType: proto.String("estimatedEarnings"),
		},
	}

	response.Data.ShopInfo = &goods.ShopGoodsDetailAudit{
		AuditStatus: proto.Uint32(shopInfo.GetShopInfo().GetShopStatus()),
		ShopDesc:    proto.String(shopInfo.GetShopInfo().GetShopDesc()),
	}
	if shopInfo.GetShopInfo().GetShopStatus() == 2 {
		response.Data.ShopInfo.AuditText = proto.String("审核中")
	}

	if shopInfo.GetShopInfo().GetShopStatus() == 3 {
		response.Data.ShopInfo.AuditText = proto.String("审核失败")
	}

	response.Data.DotInfo = &goods.ShopGoodsDetailDot{
		GoodsList: proto.Uint32(shopInfo.GetGoodsDotInfo()),
		ShopDesc:  proto.Uint32(shopInfo.GetShopInfo().GetRedDot()),
	}

	bc := make([]*goods.ShopGoodsDetailBanner, 0)
	bannerConf := bannerData[wordlistKeyBannerConf]
	err = jsoniter.Unmarshal([]byte(bannerConf), &bc)
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to unmarshal %s: %v", wordlistKeyBannerConf, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	response.Data.BannerList = bc

	return tiebaerror.ERR_SUCCESS
}
