package shop

import (
	"context"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/tbmall"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func Trend(
	ctx context.Context, baseData *types.GetForumShopGoodsDataTrendBaseData,
	response *goods.GetForumShopDataTrendResIdl,
) int {
	isGrand := baseData.Request.GetIsGrand()
	trend := getOrderTrend(ctx, baseData)
	if trend == nil {
		tbcontext.WarningF(ctx, "getOrderOverview err")
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	data := make([]*goods.ShopGoodsOrderTrendData, 0)
	grand := uint64(0)
	for _, t := range trend {
		num := t.GetTotalOrderAmount()
		if isGrand == 1 {
			num += grand
			grand = num
		}

		data = append(
			data, &goods.ShopGoodsOrderTrendData{
				Date:             t.Date,
				TotalOrderAmount: proto.Uint64(num),
			},
		)
	}
	response.Data = data
	return tiebaerror.ERR_SUCCESS
}

func getOrderTrend(ctx context.Context, baseData *types.GetForumShopGoodsDataTrendBaseData) []*tbmall.TrendData {
	daySub := int(baseData.Request.GetDay())
	now := time.Now()
	// 提取当前的年、月、日
	year, month, day := now.Date()
	// 获取当前的时区信息
	location := now.Location()
	// 使用年、月、日创建一个今天0点的时间对象
	todayMidnight := time.Date(year, month, day, 0, 0, 0, 0, location)
	endTime := todayMidnight.AddDate(0, 0, 1).Unix()
	startTime := todayMidnight.AddDate(0, 0, -(daySub - 1)).Unix()

	req := &tbmall.GetShopGoodsOrderTrendReq{
		ForumId:   baseData.Request.ForumId,
		StartTime: proto.Uint64(uint64(startTime)),
		EndTime:   proto.Uint64(uint64(endTime)),
		Type:      proto.Uint32(baseData.Request.GetType()),
	}

	res := new(tbmall.GetShopGoodsOrderTrendRes)

	tbmallOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("tbmall_go"),
	}
	err := tbservice.Call(ctx, "tbmall", "getShopGoodsOrderTrend", req, res, tbmallOption...)

	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(
			ctx, "call tbmall:getShopGoodsOrderTrend fail, err:[%v], output:[%s]", err, common.ToString(res),
		)
		return nil
	}
	return res.GetData()
}
