package shop

import (
	"context"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
)

const (
	wordlistForumShop        = "tb_wordlist_redis_forum_shop"
	wordlistKeyChannelInfo   = "channel_info"
	wordlistKeyQuestionInfo  = "question_info"
	wordlistKeyConvertConf   = "convert_conf"
	wordlistKeyPromotionRate = "promotion_rate"
	wordlistKeyBannerConf    = "banner_conf"
	wordlistKeyManualConf    = "manual_conf"
)

func ChannelInfo(
	ctx context.Context,
	response *goods.ForumShopChannelInfoResIdl,
) int {

	configData, err := wordserver.QueryItems(
		ctx, wordlistForumShop, []string{wordlistKeyChannelInfo, wordlistKeyQuestionInfo},
	)
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to fetch %s: %v", wordlistForumShop, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	channel := make([]*client.ShopGoodsChannelInfo, 0)
	channelConf := configData[wordlistKeyChannelInfo]

	err = jsoniter.Unmarshal([]byte(channelConf), &channel)
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to unmarshal %s: %v", wordlistKeyChannelInfo, err)
	}

	question := make([]*goods.ShopGoodsQuestionInfo, 0)
	questionConf := configData[wordlistKeyQuestionInfo]
	err = jsoniter.Unmarshal([]byte(questionConf), &question)
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to unmarshal %s: %v", wordlistKeyChannelInfo, err)
	}

	response.Data.ChannelInfo = channel
	response.Data.QuestionInfo = question
	return tiebaerror.ERR_SUCCESS
}
