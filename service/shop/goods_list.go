package shop

import (
	"context"
	"errors"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/tbmall"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func GoodsList(
	ctx context.Context, baseData *types.ShopGoodsBaseData,
	response *goods.ForumShopResIdl,
) int {

	var goodsList *tbmall.GetShopGoodListData
	var shopInfo *tbmall.GetShopData
	var forumName, forumAvatar string
	var err error
	g := gtask.Group{
		Concurrent:    3,
		AllowSomeFail: false,
	}

	g.Go(
		func() error {
			goodsList = getGoodsListByStatus(ctx, baseData)
			if goodsList == nil {
				return errors.New("get goodsList failed")
			}
			return nil
		},
	)

	g.Go(
		func() error {
			shopInfo = getShopInfo(ctx, baseData.Request.GetForumId(), 1)
			if shopInfo == nil {
				return errors.New("get shop info failed")
			}
			return nil
		},
	)

	channelConfMap := make(map[uint32]*client.ShopGoodsChannelInfo)
	var configData map[string]string
	g.Go(
		func() error {
			configData, err = wordserver.QueryItems(
				ctx, wordlistForumShop, []string{wordlistKeyChannelInfo, wordlistKeyConvertConf},
			)
			if err != nil {
				tbcontext.WarningF(ctx, "Failed to fetch %s: %v", wordlistForumShop, err)
				return err
			}
			channel := make([]*client.ShopGoodsChannelInfo, 0)
			channelConf := configData[wordlistKeyChannelInfo]

			err = jsoniter.Unmarshal([]byte(channelConf), &channel)
			if err != nil {
				tbcontext.WarningF(ctx, "Failed to unmarshal %s: %v", wordlistKeyChannelInfo, err)
				return err
			}
			for _, c := range channel {
				channelConfMap[c.GetId()] = c
			}
			return nil
		},
	)

	g.Go(
		func() error {
			forumName, forumAvatar = getForumNameAndAvatar(ctx, uint32(baseData.Request.GetForumId()))
			if forumName == "" || forumAvatar == "" {
				return errors.New("get forum info failed")
			}
			return nil
		},
	)

	_, err = g.Wait()
	if err != nil {
		tbcontext.WarningF(ctx, "get shop info err")
		return tiebaerror.CALL_DRS_SER_FAIL
	}

	goodsListRes := make([]*client.ShopGoodsInfo, 0, len(goodsList.GetGoodsList()))
	for _, g := range goodsList.GetGoodsList() {

		item := &client.ShopGoodsInfo{
			GoodsName:         g.GoodsName,
			Price:             g.Price,
			Sales:             g.Sales,
			CardStyle:         g.CardType,
			ThreadId:          g.ThreadId,
			EstimatedEarnings: g.PromotionAmountEx,
			GoodsDesc:         g.GoodsDesc,
			AuditReason:       g.AuditReason,
			GoodsStatus:       g.GoodsStatus,
			ImgList:           g.ImgList,
			SourceUrl:         g.SourceUrl,
			GoodsId:           proto.Uint32(uint32(g.GetId())),
			NeedNotice:        g.NeedNotice,
			ListingReason: &client.ListingReason{
				ImgList: g.GoodsDescImg,
			},
		}

		if c, ok := channelConfMap[g.GetChannelId()]; ok {
			item.ChannelInfo = &client.ShopGoodsChannelInfo{
				Id:   c.Id,
				Icon: c.Icon,
				Name: c.Name,
			}
		}
		if len(g.GetImgList()) > 0 {
			item.CoverImg = proto.String(g.GetImgList()[0])
		}

		goodsListRes = append(goodsListRes, item)
	}

	// 商品信息
	response.Data = &client.ForumShopData{
		GoodsList:  goodsListRes,
		TotalCount: goodsList.TotalCount,
		OtherCount: goodsList.OtherCount,
		DotInfo:    shopInfo.GoodsDotInfo,
	}
	// 销量信息
	response.Data.TotalSales = shopInfo.ShopSales
	// 吧名吧头像
	response.Data.ShopName = proto.String(forumName)
	response.Data.ForumPortrait = proto.String(forumAvatar)

	dc := &client.ConvertDefaultConf{}
	convertConf := configData[wordlistKeyConvertConf]
	err = jsoniter.Unmarshal([]byte(convertConf), dc)
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to unmarshal %s: %v", wordlistKeyConvertConf, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	response.Data.ConvertConf = dc
	return tiebaerror.ERR_SUCCESS
}

func getGoodsListByStatus(ctx context.Context, baseData *types.ShopGoodsBaseData) *tbmall.GetShopGoodListData {
	req := &tbmall.GetShopGoodListReq{
		ForumId:     baseData.Request.ForumId,
		SortType:    baseData.Request.SortType,
		Pn:          baseData.Request.Pn,
		Rn:          baseData.Request.Rn,
		GoodsStatus: baseData.Request.GoodsStatus,
	}

	res := new(tbmall.GetShopGoodListRes)

	tbmallOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("tbmall_go"),
	}
	err := tbservice.Call(ctx, "tbmall", "getShopGoodList", req, res, tbmallOption...)

	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call tbmall:getShopGoodList fail, err:[%v], output:[%s]", err, common.ToString(res))
		return nil
	}
	return res.GetData()
}
