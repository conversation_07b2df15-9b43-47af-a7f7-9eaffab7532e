package buildoutput

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/foruminfo"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shopgoodssearch"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shopgoodssearchrecommend"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopgoodssearch/privateparams"
)

const opName = "buildoutput"

type Processor struct {
	CommonParams  commonparams.CommonParams
	PrivateParams privateparams.PrivateParams

	output    *goods.SearchShopGoodsRes
	Search    shopgoodssearch.ShopGoodsSearch
	ForumInfo foruminfo.ForumInfo
	Recommend shopgoodssearchrecommend.ShopGoodsSearchRecommend
}

func NewProcess() *Processor {
	p := new(Processor)
	p.output = &goods.SearchShopGoodsRes{}
	return p
}

func (p *Processor) Process(ctx context.Context) error {
	forumInfo := p.ForumInfo.GetRawForums()
	if p.PrivateParams.GetOnlySug() != 1 {
		for _, g := range p.Search.Search().GetGoodsList() {
			g.ForumName = proto.String(forumInfo[uint32(g.GetForumId())].GetForumName().GetForumName())
		}

		p.output.GoodsList = p.Search.Search().GetGoodsList()
	}
	p.output.TotalCount = proto.Uint32(p.Search.Search().GetTotalCount())
	p.output.SugList = p.Search.Search().GetSugList()
	if p.Search.Search().GetTotalCount() < 2 {
		for _, g := range p.Recommend.GetRecommend() {
			g.ForumName = proto.String(forumInfo[uint32(g.GetForumId())].GetForumName().GetForumName())
		}
		p.output.RecommendList = p.Recommend.GetRecommend()
	}
	return nil
}
