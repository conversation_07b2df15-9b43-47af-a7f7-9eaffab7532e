package privateparams

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
)

// OperatorPrivateParams 算子定义
type OperatorPrivateParams struct {
	BaseAction    *client.UIBaseAction `inject:"canLost=false,canNil=false"`
	PrivateParams `extract:"canLost=false,canNil=false"`
}

func (o *OperatorPrivateParams) DoImpl(ctx *engine.Context) error {
	p := NewProcessor()
	p.baseAction = o.BaseAction

	err := p.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	o.PrivateParams = p
	return nil
}
