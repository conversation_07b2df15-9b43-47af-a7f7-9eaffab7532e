package privateparams

import (
	"context"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
)

// opName 算子名称
const opName = "private_params"

type Processor struct {
	baseAction *client.UIBaseAction

	pn      uint32
	rn      uint32
	sug     string
	onlySug uint32
}

func NewProcessor() *Processor {
	p := new(Processor)
	return p
}

func (p *Processor) Process(ctx context.Context) error {

	p.pn = cast.ToUint32(p.baseAction.GetOriginalParam("pn"))
	p.rn = cast.ToUint32(p.baseAction.GetOriginalParam("rn"))
	p.sug = cast.ToString(p.baseAction.GetOriginalParam("sug"))
	p.onlySug = cast.ToUint32(p.baseAction.GetOriginalParam("only_sug"))

	if p.pn == 0 {
		p.pn = 1
	}
	if p.rn == 0 {
		p.rn = 10
	}

	return nil
}

func (p *Processor) GetPn() uint32 {
	return p.pn
}

func (p *Processor) GetRn() uint32 {
	return p.rn
}

func (p *Processor) GetOnlySug() uint32 {
	return p.onlySug
}

func (p *Processor) GetSug() string {
	return p.sug
}
