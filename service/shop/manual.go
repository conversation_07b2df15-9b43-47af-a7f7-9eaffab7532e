package shop

import (
	"context"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
)

func Manual(
	ctx context.Context,
	response *goods.GetForumShopManualResIdl,
) int {

	var err error

	g := gtask.Group{
		Concurrent:    3,
		AllowSomeFail: false,
	}

	var manualData map[string]string
	g.Go(
		func() error {
			manualData, err = wordserver.QueryItems(
				ctx, wordlistForumShop, []string{wordlistKeyManualConf},
			)
			if err != nil {
				tbcontext.WarningF(ctx, "Failed to fetch %s: %v", wordlistForumShop, err)
				return err
			}
			return nil
		},
	)

	_, err = g.Wait()
	if err != nil {
		tbcontext.WarningF(ctx, "get ManualURL info err")
		return tiebaerror.CALL_DRS_SER_FAIL
	}

	mc := make([]*goods.ShopManualData, 0)

	ManualConf := manualData[wordlistKeyManualConf]

	err = jsoniter.Unmarshal([]byte(ManualConf), &mc)
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to unmarshal %s: %v", wordlistKeyManualConf, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	response.Data = mc
	return tiebaerror.ERR_SUCCESS
}
