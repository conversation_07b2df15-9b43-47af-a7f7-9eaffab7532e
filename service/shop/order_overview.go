package shop

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/tbmall"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func OverView(
	ctx context.Context, baseData *types.GetForumShopGoodsDataOverviewBaseData,
	response *goods.GetForumShopDataOverViewResIdl,
) int {

	overview := getOrderOverview(ctx, baseData.Request.GetForumId())
	if overview == nil {
		tbcontext.WarningF(ctx, "getOrderOverview err")
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	response.Data = []*client.ShopGoodsOrderOverviewData{
		{
			Title:     proto.String("今日订单量"),
			Today:     proto.Uint32(overview.GetOrders().GetToday()),
			Yesterday: proto.Uint32(overview.GetOrders().GetYesterday()),
			DataType:  proto.String("orders"),
		},
		{
			Title:     proto.String("今日成交额"),
			Today:     proto.Uint32(overview.GetTransactions().GetToday()),
			Yesterday: proto.Uint32(overview.GetTransactions().GetYesterday()),
			DataType:  proto.String("transactions"),
		},
		{
			Title:     proto.String("今日预计收益"),
			Today:     proto.Uint32(overview.GetEstimatedEarnings().GetToday()),
			Yesterday: proto.Uint32(overview.GetEstimatedEarnings().GetYesterday()),
			DataType:  proto.String("estimatedEarnings"),
		},
		{
			Title:    proto.String("累计订单量"),
			Today:    proto.Uint32(overview.GetOrders().GetGrand()),
			IsGrand:  proto.Uint32(1),
			DataType: proto.String("orders"),
		},
		{
			Title:    proto.String("累计成交额"),
			Today:    proto.Uint32(overview.GetTransactions().GetGrand()),
			DataType: proto.String("transactions"),
			IsGrand:  proto.Uint32(1),
		},
		{
			Title:    proto.String("累计总收益"),
			Today:    proto.Uint32(overview.GetEstimatedEarnings().GetGrand()),
			IsGrand:  proto.Uint32(1),
			DataType: proto.String("estimatedEarnings"),
		},
	}

	return tiebaerror.ERR_SUCCESS
}

func getOrderOverview(ctx context.Context, forumID uint64) *tbmall.ShopGoodsOrderOverview {
	req := &tbmall.GetShopGoodsOrderOverviewReq{
		ForumId: proto.Uint64(forumID),
	}

	res := new(tbmall.GetShopGoodsOrderOverviewRes)

	tbmallOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("tbmall_go"),
	}
	err := tbservice.Call(ctx, "tbmall", "getShopGoodsOrderOverview", req, res, tbmallOption...)

	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call tbmall:getShopGoodList fail, err:[%v], output:[%s]", err, common.ToString(res))
		return nil
	}
	return res.GetData()
}
