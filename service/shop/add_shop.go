package shop

import (
	"context"

	"google.golang.org/protobuf/proto"
	tbmallProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/tbmall"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	goodsProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func AddShop(ctx context.Context, baseData *types.AddForumShopBaseData, response *goodsProto.AddForumShopResIdl) int {
	req := baseData.Request
	shopInfo := &tbmallProto.AddShopReq{
		ShopDesc: proto.String(req.GetDesc()),
		ForumId:  proto.Uint64(req.GetForumId()),
		OpUid:    proto.Uint64(baseData.UserID),
	}

	addRes := &tbmallProto.AddShopRes{}
	tbmallOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("tbmall_go"),
	}

	// 调用tbmall服务
	err := tbservice.Call(ctx, "tbmall", "addShop", shopInfo, addRes, tbmallOption...)
	if err != nil || addRes.GetErrno() != 0 {
		tbcontext.WarningF(ctx, "addShopGoodsInfo input:[%v],output:[%v]", shopInfo, addRes)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	return tiebaerror.ERR_SUCCESS
}
