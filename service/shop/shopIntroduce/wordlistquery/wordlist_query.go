package wordlistquery

import (
	"context"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/shopIntroduce"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopIntroduce/privateparams"
)

// opName 算子名称
const opName = "wordlistquery"

const (
	wordlistForumShop            = "tb_wordlist_redis_forum_shop"
	wordlistKeyShopIntroduceConf = "shop_introduce_conf"
)

// ShopIntroduceConfigData 词表配置数据结构
// 对应JSON格式：
//
//	{
//	  "key": "shop_introduce",
//	  "link": {
//	    "text": "秘！小卖部官方运营手册",
//	    "url": "/mo/q/hybrid-main-bawu/forumShopManage/operationManual",
//	    "position": {
//	      "left": "35",
//	      "top": "701"
//	    }
//	  },
//	  "img": "https://tieba-ares.cdn.bcebos.com/mis/2025-7/1753066320441/13c9a3cb2e35.png"
//	}
type ShopIntroduceConfigData struct {
	Key  string                 `json:"key"`
	Link *ShopIntroduceLinkData `json:"link"`
	Img  string                 `json:"img"`
}

type ShopIntroduceLinkData struct {
	Text     string                     `json:"text"`
	URL      string                     `json:"url"`
	Position *ShopIntroducePositionData `json:"position"`
}

type ShopIntroducePositionData struct {
	Left string `json:"left"`
	Top  string `json:"top"`
}

type ProcessWordlistQuery struct {
	CommonParams      commonparams.CommonParams
	PrivateParams     privateparams.PrivateParams
	shopIntroduceData []*shopIntroduce.ShopIntroduceData
}

func NewProcessWordlistQuery() *ProcessWordlistQuery {
	return &ProcessWordlistQuery{
		shopIntroduceData: make([]*shopIntroduce.ShopIntroduceData, 0),
	}
}

func (p *ProcessWordlistQuery) Process(ctx context.Context) error {
	// 查询词表
	keys := []string{wordlistKeyShopIntroduceConf}
	values, err := wordserver.QueryItems(ctx, wordlistForumShop, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "query wordlist fail, table=%s, keys=%v, err=%v", wordlistForumShop, keys, err)
		return err
	}

	// 解析词表数据
	introduceConf := values[wordlistKeyShopIntroduceConf]
	if introduceConf == "" {
		tbcontext.WarningF(ctx, "shop introduce conf is empty")
		return nil
	}

	// 先解析为中间结构
	var configData []*ShopIntroduceConfigData
	err = jsoniter.Unmarshal([]byte(introduceConf), &configData)
	if err != nil {
		tbcontext.WarningF(ctx, "unmarshal shop introduce conf fail, conf=%s, err=%v", introduceConf, err)
		return err
	}

	// 转换为proto结构
	p.shopIntroduceData = make([]*shopIntroduce.ShopIntroduceData, 0, len(configData))
	for _, config := range configData {
		protoData := &shopIntroduce.ShopIntroduceData{
			Key: &config.Key,
			Img: &config.Img,
		}

		// 转换Link信息
		if config.Link != nil {
			protoLink := &shopIntroduce.Link{
				Text: &config.Link.Text,
				Url:  &config.Link.URL,
			}

			// 转换Position信息
			if config.Link.Position != nil {
				protoPosition := &shopIntroduce.Position{
					Left: &config.Link.Position.Left,
					Top:  &config.Link.Position.Top,
				}
				protoLink.Position = protoPosition
			}

			protoData.Link = protoLink
		}

		p.shopIntroduceData = append(p.shopIntroduceData, protoData)
	}

	return nil
}

// GetShopIntroduceData 获取小卖部介绍数据
func (p *ProcessWordlistQuery) GetShopIntroduceData() []*shopIntroduce.ShopIntroduceData {
	return p.shopIntroduceData
}
