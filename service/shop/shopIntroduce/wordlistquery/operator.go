package wordlistquery

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopIntroduce/privateparams"
)

// OperatorWordlistQuery 算子定义
type OperatorWordlistQuery struct {
	CommonParams    commonparams.CommonParams     `inject:"canLost=false,canNil=false"`
	PrivateParams   privateparams.PrivateParams   `inject:"canLost=false,canNil=false"`
	WordlistQuery   `extract:"canLost=false,canNil=false"`
}

func (o *OperatorWordlistQuery) DoImpl(ctx *engine.Context) error {
	p := NewProcessWordlistQuery()
	p.CommonParams = o.CommonParams
	p.PrivateParams = o.PrivateParams

	err := p.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	o.WordlistQuery = p
	return nil
}
