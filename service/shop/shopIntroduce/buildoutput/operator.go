package buildoutput

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopIntroduce/privateparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopIntroduce/wordlistquery"
)

type OperatorBuildOutput struct {
	BaseAction    *client.UIBaseAction        `inject:"canLost=false,canNil=false"`
	CommonParams  commonparams.CommonParams   `inject:"canLost=false,canNil=false"`
	PrivateParams privateparams.PrivateParams `inject:"canLost=false,canNil=false"`
	WordlistQuery wordlistquery.WordlistQuery `inject:"canLost=false,canNil=false"`
}

func (o *OperatorBuildOutput) DoImpl(ctx *engine.Context) error {
	p := NewProcess()
	p.CommonParams = o.CommonParams
	p.PrivateParams = o.PrivateParams
	p.WordlistQuery = o.WordlistQuery

	err := p.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	// 注册完整的响应数据，供controller获取
	ctx.MustRegisterInstance(p.output)
	return nil
}
