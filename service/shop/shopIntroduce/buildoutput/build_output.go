package buildoutput

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/shopIntroduce"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopIntroduce/privateparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopIntroduce/wordlistquery"
)

// opName 算子名称
const opName = "buildoutput"

type ProcessBuildOutput struct {
	CommonParams  commonparams.CommonParams
	PrivateParams privateparams.PrivateParams
	WordlistQuery wordlistquery.WordlistQuery

	output *shopIntroduce.GetShopIntroduceResIdl
}

func NewProcess() *ProcessBuildOutput {
	return &ProcessBuildOutput{
		output: &shopIntroduce.GetShopIntroduceResIdl{
			Data: make([]*shopIntroduce.ShopIntroduceData, 0),
		},
	}
}

func (p *ProcessBuildOutput) Process(ctx context.Context) error {
	// 检查依赖是否为nil
	if p.WordlistQuery == nil {
		return errors.New("WordlistQuery is nil")
	}
	if p.output == nil {
		return errors.New("output is nil")
	}

	// 获取词表查询结果
	introduceData := p.WordlistQuery.GetShopIntroduceData()

	if len(introduceData) == 0 {
		tbcontext.TraceF(ctx, "shop introduce data is empty")
		return nil
	}

	// 构建输出数据
	p.output.Data = introduceData
	return nil
}


