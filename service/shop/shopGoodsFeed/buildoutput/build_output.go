package buildoutput

import (
	"context"

	"github.com/golang/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/shopGoodsFeed"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/banner"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/privateparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/recommendgoodslist"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/shoplist"
)

const opName = "buildoutput"

type Processor struct {
	CommonParams       commonparams.CommonParams
	PrivateParams      privateparams.PrivateParams
	Banner             banner.Banner
	ShopList           shoplist.ShopList
	RecommendGoodsList recommendgoodslist.RecommendGoodsList
	output             *shopGoodsFeed.ShopGoodsFeedResData
}

func NewProcess() *Processor {
	p := new(Processor)
	p.output = &shopGoodsFeed.ShopGoodsFeedResData{}
	return p
}

func (p *Processor) Process(ctx context.Context) error {
	p.output.BannerInfo = p.Banner.GetBannerInfo()
	p.output.Position = p.Banner.GetPosition()
	p.output.ShopList = p.ShopList.GetShopList()
	p.output.HasMore = proto.Uint32(p.ShopList.GetHasMore())
	p.output.Recommend = p.RecommendGoodsList.GetRecommendGoodsList()
	return nil
}
