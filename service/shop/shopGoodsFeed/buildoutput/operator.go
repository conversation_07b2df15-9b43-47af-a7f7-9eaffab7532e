package buildoutput

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/banner"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/privateparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/recommendgoodslist"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/shoplist"
)

type OperatorBuildOutput struct {
	BaseAction         *client.UIBaseAction                  `inject:"canLost=false,canNil=false"`
	CommonParams       commonparams.CommonParams             `inject:"canLost=false,canNil=false"`
	PrivateParams      privateparams.PrivateParams           `inject:"canLost=false,canNil=false"`
	Banner             banner.Banner                         `inject:"canLost=false,canNil=false"`
	ShopList           shoplist.ShopList                     `inject:"canLost=false,canNil=false"`
	RecommendGoodsList recommendgoodslist.RecommendGoodsList `inject:"canLost=false,canNil=false"`
}

func (o *OperatorBuildOutput) DoImpl(ctx *engine.Context) error {
	p := NewProcess()
	p.CommonParams = o.CommonParams
	p.PrivateParams = o.PrivateParams
	p.Banner = o.Banner
	p.ShopList = o.ShopList
	p.RecommendGoodsList = o.RecommendGoodsList

	err := p.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	// 导出数据，供controller输出返回值使用
	ctx.MustRegisterInstance(p.output)
	return nil
}
