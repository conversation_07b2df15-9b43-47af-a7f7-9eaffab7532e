package shoplist

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/tbmall"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/privateparams"
)

// opName 算子名称
const opName = "shoplist"

type ProcessShopList struct {
	PrivateParams privateparams.PrivateParams
	CommonParams  commonparams.CommonParams
	shopList      []*client.ForumShopData
	hasMore       uint32
}

func NewProcessShopList() *ProcessShopList {
	return new(ProcessShopList)
}

func (p *ProcessShopList) Process(ctx context.Context) error {
	// 调用service接口获取数据
	input := map[string]any{}
	if p.PrivateParams.GetPn() == 0 {
		input["pn"] = 1
	} else {
		input["pn"] = p.PrivateParams.GetPn()
	}

	if p.PrivateParams.GetRn() == 0 {
		input["rn"] = 20
	} else {
		input["rn"] = p.PrivateParams.GetRn()
	}

	output := new(tbmall.GetShopFeedListRes)
	tbmallOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("tbmall_go"),
	}
	err := tbservice.Call(ctx, "tbmall", "getShopFeedList", input, output, tbmallOption...)
	if err != nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call tbmall:getShopFeedList fail, err:%v, input:[%s], output:[%s]", err, common.ToString(input), common.ToString(output))
		return errno.ErrCallServiceFail
	}

	p.hasMore = output.GetData().GetHasMore()
	p.shopList = []*client.ForumShopData{}
	for _, shop := range output.GetData().GetFeedList() {
		forumShop := &client.ForumShopData{
			ShopName:      proto.String(fmt.Sprintf("%s吧小卖部", shop.GetForumName())),
			ShopDesc:      proto.String(shop.GetShopInfo().GetShopDesc()),
			TotalSales:    proto.Uint32(shop.GetShopSales()),
			ForumName:     proto.String(shop.GetForumName()),
			ForumId:       proto.Uint64(shop.GetShopInfo().GetForumId()),
			ForumPortrait: proto.String(shop.GetForumPortrait()),
			RoleInfo: &client.ForumShopRoleInfo{
				UserName:   proto.String(shop.GetRoleInfo().GetUserName()),
				Portrait:   proto.String(shop.GetRoleInfo().GetPortrait()),
				RoleTag:    proto.String(shop.GetRoleInfo().GetRoleTag()),
				UserId:     proto.Uint64(shop.GetRoleInfo().GetUserId()),
				ForumLevel: proto.Uint32(uint32(shop.GetRoleInfo().GetForumLevel())),
				LevelName:  proto.String(shop.GetRoleInfo().GetLevelName()),
			},
		}
		// 小卖部简介
		if shop.GetShopInfo() != nil {
			forumShop.ShopDesc = shop.GetShopInfo().ShopDesc
			if shop.GetShopInfo().GetShopStatus() != 1 {
				forumShop.ShopDesc = proto.String("吧主忙着进货去了，什么都没有留下，吧友随便逛逛随便看看～")
			}
		}

		for _, goodsInfo := range shop.GetGoodsList() {
			g := &client.ShopGoodsInfo{
				GoodsName: goodsInfo.GoodsName,
				Price:     goodsInfo.Price,
				Sales:     goodsInfo.Sales,
				RecommendInfo: &client.ShopGoodsRecommendInfo{
					Count:           proto.Uint32(goodsInfo.GetRecommendInfo().GetCount()),
					RecommendStatus: proto.Uint32(goodsInfo.GetRecommendInfo().GetRecommendStatus()),
				},
				Url:               goodsInfo.Url,
				CardStyle:         goodsInfo.CardType,
				ThreadId:          goodsInfo.ThreadId,
				EstimatedEarnings: goodsInfo.PromotionAmountEx,
				GoodsDesc:         goodsInfo.GoodsDesc,
				AuditReason:       goodsInfo.AuditReason,
				GoodsStatus:       goodsInfo.GoodsStatus,
				ImgList:           goodsInfo.ImgList,
				SourceUrl:         goodsInfo.SourceUrl,
				GoodsId:           proto.Uint32(uint32(goodsInfo.GetId())),
				ListingReason: &client.ListingReason{
					Text:    goodsInfo.GoodsDesc,
					ImgList: goodsInfo.GoodsDescImg,
				},
				AgreeCount: proto.Uint64(goodsInfo.GetAgreeCount()),
			}
			if len(g.ImgList) > 0 {
				g.CoverImg = proto.String(g.ImgList[0])
			}
			forumShop.GoodsList = append(forumShop.GoodsList, g)
		}
		p.shopList = append(p.shopList, forumShop)
	}
	return nil
}

func (p *ProcessShopList) GetShopList() []*client.ForumShopData {
	return p.shopList
}

func (p *ProcessShopList) GetHasMore() uint32 {
	return p.hasMore
}
