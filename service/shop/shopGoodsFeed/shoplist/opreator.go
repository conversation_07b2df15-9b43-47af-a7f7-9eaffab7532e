package shoplist

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/privateparams"
)

type OperatorShopList struct {
	PrivateParams privateparams.PrivateParams `inject:"canLost=false,canNil=false"`
	CommonParams  commonparams.CommonParams   `inject:"canLost=false,canNil=false"`
	ShopList      `extract:"canLost=false,canNil=false"`
}

func (op *OperatorShopList) DoImpl(ctx *engine.Context) error {
	shopList := NewProcessShopList()
	shopList.PrivateParams = op.PrivateParams
	shopList.CommonParams = op.CommonParams

	err := shopList.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	op.ShopList = shopList
	return nil
}
