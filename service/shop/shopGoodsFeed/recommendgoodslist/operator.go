package recommendgoodslist

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/privateparams"
)

type OperatorRecommendGoodsList struct {
	PrivateParams      privateparams.PrivateParams `inject:"canLost=false,canNil=false"`
	CommonParams       commonparams.CommonParams   `inject:"canLost=false,canNil=false"`
	RecommendGoodsList `extract:"canLost=false,canNil=false"`
}

func (op *OperatorRecommendGoodsList) DoImpl(ctx *engine.Context) error {
	recommendGoodsList := NewProcessRecommendGoodsList()
	recommendGoodsList.PrivateParams = op.PrivateParams
	recommendGoodsList.CommonParams = op.CommonParams

	err := recommendGoodsList.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	op.RecommendGoodsList = recommendGoodsList
	return nil
}
