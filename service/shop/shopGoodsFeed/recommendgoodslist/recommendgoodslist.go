package recommendgoodslist

import (
	"context"
	"errors"
	"net/url"
	"strconv"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	permProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/tbmall"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/shopGoodsFeed"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
)

// opName 算子名称
const opName = "recommendgoodslist"

func (p *ProcessRecommendGoodsList) Process(ctx context.Context) error {
	// 参数校验
	userID := p.CommonParams.GetUserID()
	if userID == 0 {
		tbcontext.WarningF(ctx, "params err input userID is 0")
		return errors.New("params err input userID is 0")
	}
	multi := tbservice.Multi()
	//1. 获取用户关注的吧
	getForumLikeListParam := &tbservice.Parameter{
		Service: "perm",
		Method:  "getLikeForumList",
		Input: &permProto.GetLikeForumListReq{
			UserId: proto.Uint64(userID),
		},
		Output: &permProto.GetLikeForumListRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "perm::getLikeForumList", getForumLikeListParam)

	// 2. 获取小卖部列表
	getShopOpenedListParam := &tbservice.Parameter{
		Service: "tbmall",
		Method:  "getShopOpenedList",
		Input:   map[string]interface{}{},
		Output:  &tbmall.GetShopOpenedListRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
			tbservice.WithRalName("tbmall_go"),
		},
	}
	multi.Register(ctx, "tbmall::getShopOpenedList", getShopOpenedListParam)

	multi.Call(ctx)

	// 处理返回
	getLikeForumListRes := &permProto.GetLikeForumListRes{}
	permRet, err := multi.GetResult(ctx, "perm::getLikeForumList")
	if err != nil || permRet == nil {
		tbcontext.WarningF(ctx, "call perm::getLikeForumList fail, input:%v, output:%v, err: %v", getForumLikeListParam, getLikeForumListRes, err)
		return err
	}
	getLikeForumListRes, ok := permRet.(*permProto.GetLikeForumListRes)
	if !ok || getLikeForumListRes.Errno == nil || getLikeForumListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call perm::getLikeForumList fail, input:%v, output:%v, err: %v", getForumLikeListParam, getLikeForumListRes, err)
		return err
	}
	forumLikeList := make(map[uint64]struct{})
	for _, item := range getLikeForumListRes.GetOutput().GetMemberList() {
		// 只取关注的是黄牌用户的吧
		if item.GetForumId() != 0 && item.GetLevelId() >= 10 {
			forumLikeList[uint64(item.GetForumId())] = struct{}{}
		}
	}

	getShopOpenedListRes := &tbmall.GetShopOpenedListRes{}
	tbmallRet, err := multi.GetResult(ctx, "tbmall::getShopOpenedList")
	if err != nil || tbmallRet == nil {
		tbcontext.WarningF(ctx, "call tbmall::getShopOpenedList fail, input:%v, output:%v, err: %v", getShopOpenedListParam, getShopOpenedListRes, err)
		return err
	}
	getShopOpenedListRes, ok = tbmallRet.(*tbmall.GetShopOpenedListRes)
	if !ok || getShopOpenedListRes.Errno == nil || getShopOpenedListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call tbmall::getShopOpenedList fail, input:%v, output:%v, err: %v", getShopOpenedListParam, getShopOpenedListRes, err)
		return err
	}

	// 3. 筛选出用户关注的有小卖部的吧, 取出关注的吧的thread_id 集合
	userLikeForumList := make(map[uint64]uint64)
	for _, item := range getShopOpenedListRes.GetData().GetGoodsList() {
		if _, ok := forumLikeList[item.GetForumId()]; ok {
			userLikeForumList[item.GetThreadId()] = item.GetForumId()
		}
	}
	if len(userLikeForumList) == 0 {
		tbcontext.WarningF(ctx, "user like shop_forum list is empty, userID: %d", userID)
		return nil
	}

	// 4. 查redis用户点赞tid的记录，然后取20个没有点赞过的商品返回
	userKey := RedisUserAgreePrefix + strconv.FormatUint(userID, 10)
	members, err := resource.RedisMember.SMembers(ctx, userKey).Result()
	if err != nil {
		tbcontext.WarningF(ctx, "RedisMember SMembers failed, userKey:[%s], error:[%v]", userKey, err)
		return err
	}

	for _, member := range members {
		threadID, _ := strconv.ParseUint(member, 10, 64)
		if _, ok := userLikeForumList[threadID]; ok {
			delete(userLikeForumList, threadID)
		}
	}
	// 取差集，将tid添加到数组中
	threadIDs := make([]uint64, 0, len(userLikeForumList))
	for threadID := range userLikeForumList {
		threadIDs = append(threadIDs, threadID)
	}
	if len(threadIDs) == 0 {
		// 用户还没有点赞过的threadID为空
		tbcontext.WarningF(ctx, "user unlike threadIDs is empty, userID: %d", userID)
		return nil
	} else if len(threadIDs) > 20 {
		// 截取前20个
		threadIDs = threadIDs[:20]
	}

	multiobj := tbservice.Multi()
	forumIDs := make(map[uint64]struct{})
	// 获取商品信息
	for _, threadID := range threadIDs {
		getShopGoodsInfoParam := &tbservice.Parameter{
			Service: "tbmall",
			Method:  "getShopGoodsInfo",
			Input: &tbmall.GetShopGoodsInfoReq{
				ThreadId: proto.Uint64(threadID),
			},
			Output: &tbmall.GetShopGoodsInfoRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
				tbservice.WithRalName("tbmall_go"),
			},
		}
		multiobj.Register(ctx, "getShopGoodsInfo_"+strconv.FormatUint(threadID, 10), getShopGoodsInfoParam)

		// 获取商店信息
		// 获取tid对应的fid
		forumID := userLikeForumList[threadID]
		// 过滤，避免重复调用获取某一个吧的小卖部信息和吧头像信息
		if _, ok := forumIDs[forumID]; ok {
			continue
		}
		getShopInfoParam := &tbservice.Parameter{
			Service: "tbmall",
			Method:  "getShopInfo",
			Input: &tbmall.GetShopInfoReq{
				ForumId: proto.Uint64(forumID),
			},
			Output: &tbmall.GetShopInfoRes{},
			Option: []tbservice.Option{
				tbservice.WithLogField(logit.AutoField("ext_name", "shop_op")),
				tbservice.WithConverter(tbservice.JSONITER), tbservice.WithRalName("tbmall_go"),
			},
		}
		multiobj.Register(ctx, "getShopInfo_"+strconv.FormatUint(forumID, 10), getShopInfoParam)

		//获取吧头像
		forumParam := &tbservice.Parameter{
			Service: "forum",
			Method:  "mgetBtxInfo",
			Input: &forum.MgetBtxInfoReq{
				ForumId: []uint32{uint32(forumID)},
			},
			Output: &forum.MgetBtxInfoRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multiobj.Register(ctx, "mgetBtxInfo_"+strconv.FormatUint(forumID, 10), forumParam)

		forumIDs[forumID] = struct{}{}
	}

	multiobj.Call(ctx)

	shopGoodsInfo := make([]*tbmall.GetShopGoodsInfoRes, 0)
	shopInfo := make(map[uint64]*tbmall.GetShopInfoRes)
	btxInfoData := make(map[uint64]*forum.MgetBtxInfoRes)
	for _, threadID := range threadIDs {
		// 获取商品信息
		getShopGoodsInfoRes := &tbmall.GetShopGoodsInfoRes{}
		ret, err := multiobj.GetResult(ctx, "getShopGoodsInfo_"+strconv.FormatUint(threadID, 10))
		getShopGoodsInfoRes, ok := ret.(*tbmall.GetShopGoodsInfoRes)
		if !ok || getShopGoodsInfoRes.Errno == nil || getShopGoodsInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call getShopGoodsInfo_%d fail, output:%v, err: %v", threadID, getShopGoodsInfoRes, err)
			return err
		}
		shopGoodsInfo = append(shopGoodsInfo, getShopGoodsInfoRes)

		// 获取商店信息
		forumID := userLikeForumList[threadID]
		getShopInfoRes := &tbmall.GetShopInfoRes{}
		ret, err = multiobj.GetResult(ctx, "getShopInfo_"+strconv.FormatUint(forumID, 10))
		getShopInfoRes, ok = ret.(*tbmall.GetShopInfoRes)
		if !ok || getShopInfoRes.Errno == nil || getShopInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call getShopInfo_%d fail, output:%v, err: %v", forumID, getShopInfoRes, err)
			return err
		}
		shopInfo[forumID] = getShopInfoRes

		// 获取吧头像
		btxInfoRes := &forum.MgetBtxInfoRes{}
		ret, err = multiobj.GetResult(ctx, "mgetBtxInfo_"+strconv.FormatUint(forumID, 10))
		btxInfoRes, ok = ret.(*forum.MgetBtxInfoRes)
		if !ok || btxInfoRes.Errno == nil || btxInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call mgetBtxInfo_%d fail, output:%v, err: %v", forumID, btxInfoRes, err)
			return err
		}
		btxInfoData[forumID] = btxInfoRes
	}

	// 读取词表
	listingSubTitle := ""
	keys := []string{"listing_reason_subtitle", "recommend_subtitle"}
	items, err := wordserver.QueryItems(ctx, wordlistForumShop, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "call wordserver.QueryItems fail. key:[%v], err:[%v]", keys, err)
		return err
	}
	listingSubTitle = items["listing_reason_subtitle"]

	// 组装返回商品信息
	var output []*shopGoodsFeed.ShopGoodsFeedRecommend
	for _, item := range shopGoodsInfo {
		if item.GetData() == nil {
			continue
		}
		shopGoodsData := item.GetData()

		goodsURL, goodsScheme := p.buildUrlAndScheme(ctx, shopGoodsData.GetChannelId(), shopGoodsData.GetUrl())

		recom := &client.ShopGoodsRecommendInfo{}
		_ = common.StructAToStructBCtx(ctx, shopGoodsData.GetRecommendInfo(), recom)
		// 读redis取有多少人推荐
		now := time.Now()
		date := now.Format("2006-01-02")
		key := RedisShopGoodsPvPrefix + date + "_" + strconv.FormatUint(shopGoodsData.GetThreadId(), 10)
		val, err := resource.RedisMember.Get(ctx, key).Result()
		if err != nil {
			tbcontext.WarningF(ctx, "call redis.Get fail, key:[%v], err:[%v]", key, err)
		} else {
			count, _ := strconv.ParseUint(val, 10, 32)
			recom.Count = proto.Uint32(uint32(count))
		}

		// 取改tid对应吧的小卖部信息的role信息
		forumID := userLikeForumList[shopGoodsData.GetThreadId()]
		role := &client.ForumShopRoleInfo{}
		_ = common.StructAToStructBCtx(ctx, shopInfo[forumID].GetData().GetRoleInfo(), role)
		btxInfo := btxInfoData[forumID].GetOutput()
		goodsInfo := &client.ShopGoodsInfo{
			GoodsName:     proto.String(shopGoodsData.GetGoodsName()),
			ForumName:     proto.String(btxInfo[uint32(forumID)].GetForumName().GetForumName()),
			ForumAvatar:   proto.String(btxInfo[uint32(forumID)].GetCard().GetAvatar()),
			ShopDesc:      proto.String(shopInfo[forumID].GetData().GetShopInfo().GetShopDesc()),
			Price:         proto.Uint32(shopGoodsData.GetPrice()),
			Sales:         proto.Uint64(shopGoodsData.GetSales()),
			RecommendInfo: recom,
			CardStyle:     proto.Uint32(shopGoodsData.GetCardType()),
			ChannelInfo: &client.ShopGoodsChannelInfo{
				Id:   proto.Uint32(shopGoodsData.GetChannelId()),
				Icon: proto.String(channelMap[shopGoodsData.GetChannelId()].icon),
				Name: proto.String(channelMap[shopGoodsData.GetChannelId()].name),
			},
			Url:      proto.String(goodsURL),
			Scheme:   proto.String(goodsScheme),
			ThreadId: proto.Uint64(shopGoodsData.GetThreadId()),
			ListingReason: &client.ListingReason{
				Text:     proto.String(shopGoodsData.GetGoodsDesc()),
				ImgList:  shopGoodsData.GetGoodsDescImg(),
				SubTitle: proto.String(listingSubTitle),
			},
			CommentInfo: &client.ShopGoodsCommentInfo{
				Count: proto.Uint32(uint32(shopGoodsData.GetSales())),
			},
			ImgList: shopGoodsData.GetImgList(),
		}

		// 吧主标签信息
		w := int64(28)
		h := int64(15)
		if p.CommonParams.GetClientType() == clientvers.CLIENT_TYPE_ANDROID {
			w = 73
			h = 39
		}
		role.IconInfo = &client.ThemeColorInfo{
			Day: &client.ThemeElement{
				PatternImage:       proto.String(IdentityIconBazhuDay),
				PatternImageHeight: proto.Int64(h),
				PatternImageWidth:  proto.Int64(w),
			},
			Dark: &client.ThemeElement{
				PatternImage:       proto.String(IdentityIconBazhuDark),
				PatternImageHeight: proto.Int64(h),
				PatternImageWidth:  proto.Int64(w),
			},
			Night: &client.ThemeElement{
				PatternImage:       proto.String(IdentityIconBazhuDark),
				PatternImageHeight: proto.Int64(h),
				PatternImageWidth:  proto.Int64(w),
			},
		}

		output = append(output, &shopGoodsFeed.ShopGoodsFeedRecommend{
			GoodsInfo: goodsInfo,
			RoleInfo:  role,
		})
	}

	p.recommendedGoodsList = output
	return nil
}

func NewProcessRecommendGoodsList() *ProcessRecommendGoodsList {
	return new(ProcessRecommendGoodsList)
}

func (p *ProcessRecommendGoodsList) GetRecommendGoodsList() []*shopGoodsFeed.ShopGoodsFeedRecommend {
	return p.recommendedGoodsList
}

func (p *ProcessRecommendGoodsList) buildUrlAndScheme(ctx context.Context, channelID uint32, sourceURL string) (goodsURL, goodsScheme string) {
	if clientvers.CompareV2(p.CommonParams.GetClientVersion(), "<", "12.82.0.2") {
		goodsURL = sourceURL
		return goodsURL, goodsScheme
	}

	// 通用scheme
	if channelID == 1 {
		scheme := "pinduoduo://com.xunmeng.pinduoduo"
		parsedURL, err := url.Parse(sourceURL)
		if err != nil {
			return
		}
		pathAndQuery := parsedURL.Path + "?" + parsedURL.RawQuery
		// scheme 下发deeplink
		goodsScheme = scheme + pathAndQuery
		// url 直接用 source url
		goodsURL = sourceURL
	}
	if channelID == 2 {
		scheme := "tbopen://m.taobao.com/tbopen/index.html?action=ali.open.nav&module=h5&source=alimama&bc_fl_src=" +
			"tunion_vipmedia_sy&h5Url="
		goodsScheme = scheme + url.QueryEscape(sourceURL)
		goodsURL = sourceURL
	}
	return goodsURL, goodsScheme
}
