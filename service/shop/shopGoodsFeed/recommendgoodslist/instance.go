package recommendgoodslist

import (
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/shopGoodsFeed"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/privateparams"
)

type RecommendGoodsList interface {
	GetRecommendGoodsList() []*shopGoodsFeed.ShopGoodsFeedRecommend
}

type ProcessRecommendGoodsList struct {
	CommonParams         commonparams.CommonParams
	PrivateParams        privateparams.PrivateParams
	recommendedGoodsList []*shopGoodsFeed.ShopGoodsFeedRecommend
}

const (
	RedisUserAgreePrefix   = "shop_goods_user_agree_"
	RedisShopGoodsPvPrefix = "shop_goods_pv_"
	wordlistForumShop      = "tb_wordlist_redis_forum_shop"
	IdentityIconBazhuDay   = "https://tieba-fe.cdn.bcebos.com/tb/8d.png"
	IdentityIconBazhuDark  = "https://tieba-fe.cdn.bcebos.com/tb/8n.png"
)

// channelInfo 渠道信息
type channelInfo struct {
	channelID uint32
	icon      string
	name      string
}

var channelMap = map[uint32]channelInfo{
	1: {
		channelID: 1,
		icon:      "https://tieba-ares.cdn.bcebos.com/mis/2025-4/1744806358616/31126bfff12e.png",
		name:      "拼多多",
	},
	2: {
		channelID: 2,
		icon:      "https://tieba-ares.cdn.bcebos.com/mis/2025-4/1744806359030/a23fb7337525.png",
		name:      "淘宝",
	},
}
