package banner

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/privateparams"
)

type OperatorBanner struct {
	PrivateParams privateparams.PrivateParams `inject:"canLost=false,canNil=false"`
	CommonParams  commonparams.CommonParams   `inject:"canLost=false,canNil=false"`
	Banner        `extract:"canLost=false,canNil=false"`
}

func (op *OperatorBanner) DoImpl(ctx *engine.Context) error {
	banner := NewProcessBanner()
	banner.PrivateParams = op.PrivateParams
	banner.CommonParams = op.CommonParams

	err := banner.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	op.Banner = banner
	return nil
}
