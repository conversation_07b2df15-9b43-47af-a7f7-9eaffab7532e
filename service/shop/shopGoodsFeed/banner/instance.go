package banner

import "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/shopGoodsFeed"

// Banner 小卖铺feed流页面banner数据算子 导出接口定义
type Banner interface {
	// GetBannerInfo 获取banner信息
	GetBannerInfo() []*shopGoodsFeed.ShopGoodsFeedBanner
	// GetPosition 获取插楼位置
	GetPosition() map[uint32]uint32
}

type ConfigBanner struct {
	Img          string   `json:"img,omitempty"`
	URL          string   `json:"url,omitempty"`
	Text         string   `json:"text,omitempty"`
	CardType     uint32   `json:"card_type,omitempty"`
	IntervalTime uint64   `json:"interval_time,omitempty"`
	Index        uint32   `json:"index,omitempty"`
	ShowUserList []uint64 `json:"show_user_list,omitempty"`
}
