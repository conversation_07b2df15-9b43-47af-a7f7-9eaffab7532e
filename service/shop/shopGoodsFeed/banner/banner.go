package banner

import (
	"context"
	"errors"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/shopGoodsFeed"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopGoodsFeed/privateparams"
)

// opName 算子名称
const opName = "banner"

const (
	wordlistTable         = "tb_wordlist_redis_forum_shop"
	addThreadBotMaxNumKey = "shop_feed_banner_conf"
	feedCardPositionKey   = "feed_card_position"
)

type ProcessBanner struct {
	CommonParams  commonparams.CommonParams
	PrivateParams privateparams.PrivateParams
	bannerInfo    []*shopGoodsFeed.ShopGoodsFeedBanner
	position      map[uint32]uint32
}

func NewProcessBanner() *ProcessBanner {
	return new(ProcessBanner)
}

func (p *ProcessBanner) Process(ctx context.Context) error {
	// 第一页才返回数据
	pn := p.PrivateParams.GetPn()
	if pn != 1 {
		return nil
	}
	// 读取配置
	strConfig, err := wordserver.QueryKeys(ctx, wordlistTable, []string{addThreadBotMaxNumKey, feedCardPositionKey})
	if err != nil && !errors.Is(err, redis.ErrNil) {
		tbcontext.WarningF(ctx, "wordlist server query fail: %v", err)
		return err
	}
	if errors.Is(err, redis.ErrNil) || len(strConfig) != 2 {
		return nil
	}

	bannerList := []*ConfigBanner{}
	if len(strConfig[0]) > 0 {
		err = jsoniter.UnmarshalFromString(strConfig[0], &bannerList)
		if err != nil {
			tbcontext.WarningF(ctx, "banner optor unmarshal fail, str:%s, err:%v", strConfig[0], err)
			return err
		}
	}

	p.position = map[uint32]uint32{}
	if len(strConfig[1]) > 0 {
		err = jsoniter.UnmarshalFromString(strConfig[1], &p.position)
		if err != nil {
			tbcontext.WarningF(ctx, "banner optor unmarshal fail, str:%s, err:%v", strConfig[1], err)
			return err
		}
	}

	userID := p.CommonParams.GetUserID()
	for _, banner := range bannerList {
		// 如果配置了白名单，但是当前登录用户没有在白名单里面或者未登录，就跳过不下发
		if len(banner.ShowUserList) > 0 {
			if userID == 0 || !php2go.InArrayUint64(userID, banner.ShowUserList) {
				continue
			}
		}

		img, _ := image.GenAuthUrl(banner.Img)
		bannerInfo := &shopGoodsFeed.ShopGoodsFeedBanner{
			Img:          proto.String(img),
			Url:          proto.String(banner.URL),
			Text:         proto.String(banner.Text),
			CardType:     proto.Uint32(banner.CardType),
			IntervalTime: proto.Uint64(banner.IntervalTime),
		}
		p.bannerInfo = append(p.bannerInfo, bannerInfo)
	}
	return nil
}

func (p *ProcessBanner) GetBannerInfo() []*shopGoodsFeed.ShopGoodsFeedBanner {
	return p.bannerInfo
}

func (p *ProcessBanner) GetPosition() map[uint32]uint32 {
	return p.position
}
