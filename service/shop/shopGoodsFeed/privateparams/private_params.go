package privateparams

import (
	"context"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
)

// opName 算子名称
const opName = "private_params"

type Processor struct {
	baseAction *client.UIBaseAction

	pn uint64
	rn uint64
}

func NewProcessor() *Processor {
	p := new(Processor)
	return p
}

func (p *Processor) Process(ctx context.Context) error {

	p.pn = cast.ToUint64(p.baseAction.GetOriginalParam("pn"))
	p.rn = cast.ToUint64(p.baseAction.GetOriginalParam("rn"))

	if p.pn == 0 {
		p.pn = 1
	}
	if p.rn == 0 {
		p.rn = 10
	}

	return nil
}

func (p *Processor) GetPn() uint64 {
	return p.pn
}

func (p *Processor) GetRn() uint64 {
	return p.rn
}
