package buildoutput

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopExam/privateparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopExam/wordlistquery"
)

// OperatorBuildOutput 算子定义
type OperatorBuildOutput struct {
	BaseAction    *client.UIBaseAction          `inject:"canLost=false,canNil=false"`
	CommonParams  commonparams.CommonParams     `inject:"canLost=false,canNil=false"`
	PrivateParams privateparams.PrivateParams   `inject:"canLost=false,canNil=false"`
	WordlistQuery wordlistquery.WordlistQuery   `inject:"canLost=false,canNil=false"`
}

// DoImpl 算子入口
func (o *OperatorBuildOutput) DoImpl(ctx *engine.Context) error {
	p := NewProcessor()
	p.BaseAction = o.BaseAction
	p.CommonParams = o.CommonParams
	p.PrivateParams = o.PrivateParams
	p.WordlistQuery = o.WordlistQuery

	err := p.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	// 导出数据，供controller输出返回值使用
	ctx.MustRegisterInstance(p.output)
	return nil
}
