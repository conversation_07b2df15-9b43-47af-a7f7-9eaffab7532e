package buildoutput

import (
	"context"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/shopExam"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopExam/privateparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopExam/wordlistquery"
)

// opName 算子名称
const opName = "buildoutput"

type Processor struct {
	BaseAction    *client.UIBaseAction
	CommonParams  commonparams.CommonParams
	PrivateParams privateparams.PrivateParams
	WordlistQuery wordlistquery.WordlistQuery

	output *shopExam.ShopExamData // 接口返回
}

func NewProcessor() *Processor {
	p := new(Processor)
	p.output = &shopExam.ShopExamData{}
	return p
}

func (p *Processor) Process(ctx context.Context) error {
	// 从词表查询算子获取答题数据
	if p.WordlistQuery == nil {
		tbcontext.WarningF(ctx, "wordlist query is nil")
		return nil
	}

	examData := p.WordlistQuery.GetShopExamData()
	if examData == nil {
		tbcontext.WarningF(ctx, "wordlist query result is nil")
		return nil
	}

	// 构造输出数据
	p.output = examData

	return nil
}
