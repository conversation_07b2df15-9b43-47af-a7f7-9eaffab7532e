package wordlistquery

import (
	"context"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/shopExam"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopExam/privateparams"
)

// opName 算子名称
const opName = "wordlistquery"

const (
	wordlistForumShop       = "tb_wordlist_redis_forum_shop"
	wordlistKeyQuestionInfo = "exam_info"
)

type ProcessWordlistQuery struct {
	CommonParams  commonparams.CommonParams
	PrivateParams privateparams.PrivateParams

	shopExamData *shopExam.ShopExamData // 存储答题数据的中间变量
}

func NewProcessor() *ProcessWordlistQuery {
	p := &ProcessWordlistQuery{
		shopExamData: &shopExam.ShopExamData{
			QuestionList: make([]*shopExam.QuestionData, 0),
		},
	}
	return p
}

func (p *ProcessWordlistQuery) Process(ctx context.Context) error {
	// 查询词表获取答题数据
	configData, err := wordserver.QueryItems(
		ctx, wordlistForumShop, []string{wordlistKeyQuestionInfo},
	)
	if err != nil {
		tbcontext.WarningF(ctx, "call wordserver.QueryItems fail. key:[%v], err:[%v]", wordlistKeyQuestionInfo, err)
		return err
	}

	// 解析答题数据
	questionList := make([]*shopExam.QuestionData, 0)
	questionConf := configData[wordlistKeyQuestionInfo]

	if questionConf == "" {
		// 允许空配置，直接返回空列表
		if p.shopExamData != nil {
			p.shopExamData.QuestionList = questionList
		}
		return nil
	}

	err = jsoniter.Unmarshal([]byte(questionConf), &questionList)
	if err != nil {
		tbcontext.WarningF(ctx, "unmarshal wordlist content fail, key=%s, err=%v", wordlistKeyQuestionInfo, err)
		return err
	}

	// 构造返回数据
	if p.shopExamData != nil {
		p.shopExamData.QuestionList = questionList
	}

	return nil
}

// GetShopExamData 获取小卖部答题数据
func (p *ProcessWordlistQuery) GetShopExamData() *shopExam.ShopExamData {
	if p == nil || p.shopExamData == nil {
		return &shopExam.ShopExamData{
			QuestionList: make([]*shopExam.QuestionData, 0),
		}
	}
	return p.shopExamData
}
