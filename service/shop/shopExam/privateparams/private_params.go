package privateparams

import (
	"context"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
)

// opName 算子名称
const opName = "privateparams"

type ProcessPrivateParams struct {
	baseAction *client.UIBaseAction
}

func NewProcessor() *ProcessPrivateParams {
	return &ProcessPrivateParams{}
}

func (p *ProcessPrivateParams) Process(ctx context.Context) error {
	// 空算子实现，保持架构一致性
	// 未来如需私有参数处理，可在此扩展
	return nil
}
