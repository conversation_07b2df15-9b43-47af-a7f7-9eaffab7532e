package shop

import (
	"context"
	"errors"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/tbmall"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/goods"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func Goods(
	ctx context.Context, baseData *types.ShopGoodsBaseData,
	response *goods.ForumShopResIdl,
) int {

	var goodsList *tbmall.GetShopGoodListData
	var shopInfo *tbmall.GetShopData
	var forumName, forumAvatar string
	g := gtask.Group{
		Concurrent:    3,
		AllowSomeFail: false,
	}

	g.Go(
		func() error {
			goodsList = getGoodsList(ctx, baseData)
			if goodsList == nil {
				return errors.New("get goodsList failed")
			}
			return nil
		},
	)

	g.Go(
		func() error {
			shopInfo = getShopInfo(ctx, baseData.Request.GetForumId(), 1)
			if shopInfo == nil {
				return errors.New("get shop info failed")
			}
			return nil
		},
	)

	g.Go(
		func() error {
			forumName, forumAvatar = getForumNameAndAvatar(ctx, uint32(baseData.Request.GetForumId()))
			if forumName == "" || forumAvatar == "" {
				return errors.New("get forum info failed")
			}
			return nil
		},
	)

	_, err := g.Wait()
	if err != nil {
		tbcontext.WarningF(ctx, "get shop info err")
		return tiebaerror.CALL_DRS_SER_FAIL
	}

	goodsListRes := make([]*client.ShopGoodsInfo, 0, len(goodsList.GetGoodsList()))
	for _, g := range goodsList.GetGoodsList() {

		item := &client.ShopGoodsInfo{
			GoodsName: g.GoodsName,
			Price:     g.Price,
			Sales:     g.Sales,
			CardStyle: g.CardType,
			ThreadId:  g.ThreadId,
		}

		item.GoodsDesc = proto.String(g.GetGoodsDesc())

		if len(g.GetImgList()) > 0 {
			item.CoverImg = proto.String(g.GetImgList()[0])
		}

		if g.GetRecommendInfo() != nil {
			recommend := &client.ShopGoodsRecommendInfo{
				Count:    g.GetRecommendInfo().Count,
				UserInfo: []*client.ShopGoodsRecommendUserInfo{},
			}

			userInfo := &client.ShopGoodsRecommendUserInfo{
				UserName:   g.GetRecommendInfo().GetUserInfo()[0].UserName,
				ForumLevel: g.GetRecommendInfo().GetUserInfo()[0].ForumLevel,
				Portrait:   g.GetRecommendInfo().GetUserInfo()[0].Portrait,
			}
			recommend.UserInfo = append(recommend.UserInfo, userInfo)
			item.RecommendInfo = recommend
		}

		goodsListRes = append(goodsListRes, item)
	}

	// 商品信息
	response.Data = &client.ForumShopData{
		GoodsList:  goodsListRes,
		TotalCount: goodsList.TotalCount,
	}
	// 小卖部简介
	if shopInfo.GetShopInfo() != nil {
		response.Data.ShopDesc = shopInfo.GetShopInfo().ShopDesc
		if shopInfo.GetShopInfo().GetShopStatus() != 1 {
			response.Data.ShopDesc = proto.String("吧主忙着进货去了，什么都没有留下，吧友随便逛逛随便看看～")
		}
	}
	// 店小二信息
	if shopInfo.GetRoleInfo() != nil {
		response.Data.RoleInfo = &client.ForumShopRoleInfo{
			UserName: shopInfo.GetRoleInfo().UserName,
			Portrait: shopInfo.GetRoleInfo().Portrait,
			RoleTag:  shopInfo.GetRoleInfo().RoleTag,
			UserId:   shopInfo.GetRoleInfo().UserId,
		}
	}
	// 销量信息
	response.Data.TotalSales = shopInfo.ShopSales
	// 吧名吧头像
	response.Data.ShopName = proto.String(forumName)
	response.Data.ForumPortrait = proto.String(forumAvatar)
	return tiebaerror.ERR_SUCCESS
}

func getGoodsList(ctx context.Context, baseData *types.ShopGoodsBaseData) *tbmall.GetShopGoodListData {
	req := &tbmall.GetShopGoodListReq{
		ForumId:       baseData.Request.ForumId,
		SortType:      baseData.Request.SortType,
		Pn:            baseData.Request.Pn,
		Rn:            baseData.Request.Rn,
		NeedRecommend: proto.Uint32(1),
	}

	res := new(tbmall.GetShopGoodListRes)

	tbmallOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("tbmall_go"),
	}
	err := tbservice.Call(ctx, "tbmall", "getShopGoodList", req, res, tbmallOption...)

	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call tbmall:getShopGoodList fail, err:[%v], output:[%s]", err, common.ToString(res))
		return nil
	}
	return res.GetData()
}

func getShopInfo(ctx context.Context, fid uint64, needDot uint32) *tbmall.GetShopData {
	req := &tbmall.GetShopInfoReq{
		ForumId: proto.Uint64(fid),
		NeedDot: proto.Uint32(needDot),
	}

	res := new(tbmall.GetShopInfoRes)

	tbmallOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("tbmall_go"),
	}
	err := tbservice.Call(ctx, "tbmall", "getShopInfo", req, res, tbmallOption...)

	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call tbmall:getShopGoodList fail, err:[%v], output:[%s]", err, common.ToString(res))
		return nil
	}
	return res.GetData()
}

func getForumNameAndAvatar(ctx context.Context, fid uint32) (string, string) {
	req := &forum.GetBtxInfoReq{
		ForumId: proto.Uint32(fid),
	}
	res := new(forum.GetBtxInfoRes)
	err := tbservice.Call(ctx, "forum", "getBtxInfo", req, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(
			ctx, "getForumTags call service forum::getBtxInfo failed, err = %v, input = %v, output = %v", err, req, res,
		)
		return "", ""
	}
	return res.GetForumName().GetForumName(), res.GetCard().GetAvatar()
}
