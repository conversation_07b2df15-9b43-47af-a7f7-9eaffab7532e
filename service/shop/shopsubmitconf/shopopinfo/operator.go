package shopopinfo

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
)

// OperatorShopOpInfo 算子定义
type OperatorShopOpInfo struct {
	ShopOpInfo `extract:"canLost=true,canNil=true"`
}

// DoImpl 算子入口
func (op *OperatorShopOpInfo) DoImpl(ctx *engine.Context) error {
	shopOpInfo := NewProcessor()

	err := shopOpInfo.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	op.ShopOpInfo = shopOpInfo
	return nil
}
