package shopopinfo

import (
	"context"

	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
)

// opName 算子名称
const opName = "shopopinfo"
const ShopConfigWordList = "tb_wordlist_redis_forum_shop"
const Qrcode = "shop_op_qrcode"

type Processor struct {
	qrCode string
}

func NewProcessor() *Processor {
	return &Processor{}
}

func (p *Processor) Process(ctx context.Context) error {

	keys := []string{Qrcode}
	items, err := wordserver.QueryItems(ctx, ShopConfigWordList, keys)
	if err != nil || items == nil {
		tbcontext.WarningF(ctx, "call word server.QueryItems fail. key:[%v], err:[%v]", keys, err)
		return err
	}

	p.qrCode = items[Qrcode]
	return nil
}

func (p *Processor) GetQrCode() string {
	return p.qrCode
}
