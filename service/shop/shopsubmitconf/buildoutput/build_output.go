package buildoutput

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/shop/shopSubmitConf"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shop"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopsubmitconf/shopopinfo"
)

// opName 算子名称
const opName = "buildoutput"

type Processor struct {
	Shop       shop.Shop
	ShopOpInfo shopopinfo.ShopOpInfo

	Output *shopSubmitConf.ShopSubmitConfRes
}

func NewProcessor() *Processor {
	p := &Processor{}
	p.Output = &shopSubmitConf.ShopSubmitConfRes{}
	return p
}

func (p *Processor) Process(ctx context.Context) error {
	if p.Output == nil {
		p.Output = &shopSubmitConf.ShopSubmitConfRes{}
	}
	if p.ShopOpInfo != nil {
		p.Output.QrCode = proto.String(p.ShopOpInfo.GetQrCode())
	}
	if p.Shop != nil {
		shopInfo := p.Shop.GetShopInfo()
		if shopInfo != nil {
			p.Output.ShopStatus = shopInfo.ShopInfo.ShopStatus
			p.Output.ShopDesc = shopInfo.ShopInfo.ShopDesc
			p.Output.AuditReason = shopInfo.ShopInfo.AuditReason
		}
	}

	return nil
}
