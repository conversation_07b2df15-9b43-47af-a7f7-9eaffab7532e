package buildoutput

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/shop"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/shop/shopsubmitconf/shopopinfo"
)

// OperatorBuildOutput 算子定义
type OperatorBuildOutput struct {
	ShopInfo   shop.Shop             `inject:"canLost=true,canNil=false"`
	ShopOpInfo shopopinfo.ShopOpInfo `inject:"canLost=true,canNil=false"`
	Output     *Processor            `extract:"canLost=true,canNil=true"`
}

// DoImpl 算子入口
func (op *OperatorBuildOutput) DoImpl(ctx *engine.Context) error {
	buildOutput := NewProcessor()
	buildOutput.Shop = op.ShopInfo
	buildOutput.ShopOpInfo = op.ShopOpInfo

	err := buildOutput.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	ctx.MustRegisterInstance(buildOutput.Output)
	return nil
}
