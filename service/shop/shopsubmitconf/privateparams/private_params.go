package privateparams

import (
	"context"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
)

// opName 算子名称
const opName = "privateparams"

type Processor struct {
	BaseAction *client.UIBaseAction
	ForumID    uint32
}

func NewProcessor() *Processor {
	return &Processor{}
}

func (p *Processor) Process(ctx context.Context) error {
	p.ForumID = cast.ToUint32(p.BaseAction.GetOriginalParam("forum_id"))
	if p.ForumID <= 0 {
		tbcontext.TraceF(ctx, "forum_id is invalid")
		return errParamError
	}
	return nil
}

func (p *Processor) GetForumID() uint32 {
	return p.ForumID
}
