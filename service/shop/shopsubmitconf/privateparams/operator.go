package privateparams

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
)

// OperatorPrivateParams 算子定义
type OperatorPrivateParams struct {
	BaseAction    *client.UIBaseAction `inject:"canLost=false,canNil=false"`
	PrivateParams `extract:"canLost=false,canNil=false"`
}

// DoImpl 算子入口
func (op *OperatorPrivateParams) DoImpl(ctx *engine.Context) error {
	privateParams := NewProcessor()

	privateParams.BaseAction = op.BaseAction
	err := privateParams.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	op.PrivateParams = privateParams
	return nil
}
