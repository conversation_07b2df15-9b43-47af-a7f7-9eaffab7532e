package sprite

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	utilchat "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/pbcontent"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/baidu/uidxuk"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/sendSpriteMsg"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	MsgExtraTypeDefault        = 1   // 额外信息里的默认消息类型
	MsgExtraTypeVoice          = 103 // 额外信息里的语音消息类型
	MsgExtraTypeAiBotIntroduce = 104 // 额外信息里的机器人介绍消息类型
)

var spriteUk = "wkMPXvAp1GObWY-Y7dRjbg"

type ImContent struct {
	Type    int    `json:"type"`
	Content string `json:"content"`
}

func SendSpriteMsg(ctx context.Context, baseData *types.SendSpriteMsgBaseData,
	response *sendSpriteMsg.SendSpriteMsgResIdl) int {
	// tbcontext.WarningF(ctx, "SendSpriteMsg input param error, sendToImText = %v", common.ToString(baseData))
	sendToImContent := baseData.Request.GetContent()
	// sendToImText := baseData.Request.GetContent()
	originMsgKey := baseData.Request.GetMsgKey()
	pa := baseData.Request.GetPa()
	uk := baseData.Request.GetUk()
	uid, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	extraType := baseData.Request.GetExtraType()
	titleInfoStr := baseData.Request.GetTitleInfo()
	msgSugInfoStr := baseData.Request.GetMsgSugInfo()
	gameRoundId := baseData.Request.GetGameRoundId()
	sugListStr := baseData.Request.GetSugList()
	isPrologue := baseData.Request.GetIsPrologue()

	// todo 测试数据
	// uid = 13090999
	// uid = 449210960
	if uid == 0 {
		tbcontext.WarningF(ctx, "SendSpriteMsg input param error, uid = %v", uid)
		return tiebaerror.ERR_PARAM_ERROR
	}

	// uid = 13090999
	if len(sendToImContent) == 0 {
		tbcontext.WarningF(ctx, "SendSpriteMsg input param error, sendToImContent = %v", sendToImContent)
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 消息类型为机器人介绍消息时，解析titleInfo，msgSugInfo
	titleInfo := []*chat.TitleInfo{}
	msgSugInfo := &chat.MsgSugInfo{}
	if extraType == MsgExtraTypeAiBotIntroduce {
		if len(titleInfoStr) > 0 {
			err := jsoniter.UnmarshalFromString(titleInfoStr, &titleInfo)
			if err != nil {
				tbcontext.WarningF(ctx, "UnmarshalFromString fail, titleInfoStr = %v", titleInfoStr)
				return tiebaerror.ERR_PARAM_ERROR
			}
		}
		if len(msgSugInfoStr) > 0 {
			err := jsoniter.UnmarshalFromString(msgSugInfoStr, msgSugInfo)
			if err != nil {
				tbcontext.WarningF(ctx, "UnmarshalFromString fail, msgSugInfoStr = %v", msgSugInfo)
				return tiebaerror.ERR_PARAM_ERROR
			}
		}
	}

	var sendToImContentList []ImContent

	clientType := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	// clientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)

	err := json.Unmarshal([]byte(sendToImContent), &sendToImContentList)
	if nil != err {
		sendToImContentList = append(sendToImContentList, ImContent{
			Type:    1,
			Content: sendToImContent,
		})
	}

	// 解析入参里的sug_list
	var sugList []*chat.AbilityInfo
	if len(sugListStr) != 0 {
		err := json.Unmarshal([]byte(sugListStr), &sugList)
		if err != nil {
			tbcontext.WarningF(ctx, "fail to unmarshal input sug_list %v, err = %v", sugListStr, err)
		}
	}

	/**
	//高版本 >= 12.42.0
	if clientvers.IsLegalVersion("12_42_0", clientType, clientVersion) {
		err := json.Unmarshal([]byte(sendToImContent), sendToImContentList)
		if nil != err {
			tbcontext.WarningF(ctx, "SendSpriteMsg input param error, sendToImContent = %v is not a vaild json", sendToImContent)
			return tiebaerror.ERR_PARAM_ERROR
		}
	}else{//低版本 < 12.42.0
		sendToImContentList = append(sendToImContentList, ImContent{
			1,
			sendToImContent,
		})
	}
	*/

	spriteUID := uint64(pa)
	if spriteUID == 0 {
		// 调service给im中台发消息
		spriteUID, err = uidxuk.UK2UID(spriteUk)
		if err != nil {
			tbcontext.WarningF(ctx, "fail to get uid by uk, err = %v", err)
		}
	}

	botUid := uint64(0)
	if uk != "" {
		botUid, err = uidxuk.UK2UID(uk)
		if err != nil {
			tbcontext.WarningF(ctx, "fail to get uid by uk, err = %v", err)
		}
	}

	var msgKeyInt int64
	var msgKey *string
	if spriteUID != 0 && uid != 0 && len(sendToImContentList) > 0 {
		var imgUrlInfoMap = make(map[int]chat.GetBosImgInfoData)
		// 安卓的需要获取宽高
		if clientvers.CLIENT_TYPE_ANDROID == clientType {
			multi := tbservice.Multi()
			var multiInput = make(map[string]*tbservice.Parameter)
			for index, imContent := range sendToImContentList {
				if imContent.Type != 2 {
					continue
				}

				imgUrlInfoMap[index] = chat.GetBosImgInfoData{
					ImgUrl: &imContent.Content,
				} // todo url待判断格式
				servicekey := "chat::getBosImgInfo" + strconv.Itoa(index)
				sendMsgInput := &chat.GetBosImgInfoReq{
					Url: &imContent.Content,
				}
				multiInput[servicekey] = &tbservice.Parameter{
					Service: "chat",
					Method:  "getBosImgInfo",
					Input:   sendMsgInput,
					Output:  &chat.GetBosImgInfoRes{},
					Option: []tbservice.Option{
						tbservice.WithConverter("json"),
						tbservice.WithTimeout(1500 * time.Millisecond), // ms
					},
				}
				multi.Register(ctx, servicekey, multiInput[servicekey])
			}

			if len(multiInput) > 0 {
				multi.Call(ctx)
			}

			for index, imContent := range sendToImContentList {
				if imContent.Type != 2 {
					continue
				}
				servicekey := "chat::getBosImgInfo" + strconv.Itoa(index)
				arrRes, err := multi.GetResult(ctx, servicekey)
				if nil != err {
					// 发送消息失败不返回错误
					tbcontext.WarningF(ctx, "fail to call service chat:getBosImgInfo, err = %v, input = %v, output = %v", err, multiInput[servicekey], arrRes)
					continue
				}

				arrOutPut := arrRes.(*chat.GetBosImgInfoRes)
				if nil == arrOutPut.Errno {
					// 发送消息失败不返回错误
					tbcontext.WarningF(ctx, "fail to call service chat:getBosImgInfo, err = %v, input = %v, output = %v", err, multiInput[servicekey], arrOutPut)
					continue
				}

				if arrOutPut.GetErrno() != tiebaerror.ERR_SUCCESS || nil == arrOutPut.Data {
					// 发送消息失败不返回错误
					tbcontext.WarningF(ctx, "fail to call service chat:getBosImgInfo, err = %v, input = %v, output = %v", err, multiInput[servicekey], arrOutPut)
					continue
				}

				imgUrlInfoMap[index] = chat.GetBosImgInfoData{
					ImgUrl:    arrOutPut.GetData().ImgUrl,
					ImgWidth:  arrOutPut.GetData().ImgHeight,
					ImgHeight: arrOutPut.GetData().ImgHeight,
				}
			}
		}

		for index, imContent := range sendToImContentList {
			answerReply := imContent.Content
			msgRichTxtParserRes := []*pbcontent.PbContent{}
			if imContent.Type == 1 { // 文本
				// servicekey := "chat::sendTextMsgForPersonalChat"+strconv.Itoa(index)
				if msgKeyInt == 0 {
					msgKeyInt = time.Now().UnixNano()
				} else {
					msgKeyInt++
				}

				// 回答是文字类型
				// 添加解析器逻辑，处理content和新字段
				msgRichTxtParserData, err := utilchat.MsgRichTxtParser(ctx, answerReply)
				if err != nil {
					tbcontext.WarningF(ctx, "MsgRichTxtParser  failed, err=%v, reply=%s, MsgRichTxtParserRes=%s", err, answerReply, msgRichTxtParserData)
					continue
				}

				err = common.StructAToStructBCtx(ctx, msgRichTxtParserData, &msgRichTxtParserRes)
				if err != nil {
					tbcontext.WarningF(ctx, "common.StructAToStructBCtx failed, err=%v, msgRichTxtParserData=%s, msgRichTxtParserRes=%s", err, common.ToString(msgRichTxtParserData), common.ToString(msgRichTxtParserRes))
					continue
				}

				if len(msgRichTxtParserRes) != 0 {
					newReply := ""
					for key, val := range msgRichTxtParserRes {
						if val.GetText() != "" {
							newReply += val.GetText()
						}
						if val.GetType() != 0 {
							msgRichTxtParserRes[key].C = proto.String("CAM_X0304")
						}
						msgRichTxtParserRes[key].UrlType = proto.Int32(10000000 + val.GetUrlType())
					}
					answerReply = newReply
				}

				msgKey = proto.String(strconv.FormatInt(msgKeyInt, 16))
				sendMsgInput := &chat.SendTextMsgForPersonalChatReq{
					Pa:         proto.String(cast.ToString(spriteUID)),
					Content:    proto.String(answerReply),
					ToUid:      proto.Int64(uid),
					StructData: msgRichTxtParserRes,
					MsgKey:     msgKey,
					BotUid:     proto.Int64(int64(botUid)),
					ExtraType:  proto.Int32(extraType),
					TitleInfo:  titleInfo,
					MsgSugInfo: msgSugInfo,
					SugList:    sugList,
					IsPrologue: proto.Uint64(isPrologue),
				}

				if gameRoundId != 0 {
					sendMsgInput.GameroundId = proto.Int32(gameRoundId)
					sendMsgInput.GameScore = proto.Int32(-9999) // 魔法数字，用来标识端上不展示
				}

				sendMsgOutput := new(chat.SendTextMsgForPersonalChatRes)
				err = tbservice.Call(ctx, "chat", "sendTextMsgForPersonalChat", sendMsgInput, sendMsgOutput,
					tbservice.WithConverter(tbservice.JSONITER), tbservice.WithTimeout(1500*time.Millisecond))
				if err != nil || sendMsgOutput == nil || sendMsgOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
					// 发送消息失败不返回错误
					tbcontext.WarningF(ctx, "fail to call service chat:sendTextMsgForPersonalChat, err = %v, input = %v, output = %v", err, sendMsgInput, sendMsgOutput)
				}
			} else if imContent.Type == 2 { // 图片
				// servicekey := "chat::sendPicMsgForPersonalChat"+strconv.Itoa(index)
				if msgKeyInt == 0 {
					msgKeyInt = time.Now().UnixNano()
				} else {
					msgKeyInt++
				}

				msgKey = proto.String(strconv.FormatInt(msgKeyInt, 16))
				sendMsgInput := &chat.SendPicMsgForPersonalChatReq{
					Pa:        proto.String(cast.ToString(spriteUID)),
					Url:       proto.String(imContent.Content),
					Thumbnail: proto.String(imContent.Content),
					ToUid:     proto.Int64(uid),
					MsgKey:    msgKey,
				}

				if imgUrlInfo, ok := imgUrlInfoMap[index]; ok {
					if imgUrlInfo.GetImgHeight() > 0 && imgUrlInfo.GetImgWidth() > 0 {
						picSize := strconv.FormatInt(int64(imgUrlInfo.GetImgWidth()), 10) + "," + strconv.FormatInt(int64(imgUrlInfo.GetImgHeight()), 10)
						sendMsgInput.PicSize = &picSize // 目前只传大图的，因为底层是默认值，后面接口好了，这里再调整
					}
				}

				sendMsgOutput := new(chat.SendPicMsgForPersonalChatRes)
				err = tbservice.Call(ctx, "chat", "sendPicMsgForPersonalChat", sendMsgInput, sendMsgOutput,
					tbservice.WithConverter(tbservice.JSONITER), tbservice.WithTimeout(1500*time.Millisecond))
				if err != nil || sendMsgOutput == nil || sendMsgOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
					// 发送消息失败不返回错误
					tbcontext.WarningF(ctx, "fail to call service chat:sendPicMsgForPersonalChat, err = %v, input = %v, output = %v", err, sendMsgInput, sendMsgOutput)
				}
			}
		}

		response.Data.MsgKey = msgKey // 最后一个msgkey
		response.Data.OriginMsgKey = proto.String(originMsgKey)
	}

	//
	// //if needSend {
	// //	response.Data.NeedSend = proto.Uint32(1)
	// //	response.Data.SendText = proto.String(sendText)
	// //}
	// if newVersion != version && newVersion != "" {
	//	response.Data.Version = proto.String(newVersion)
	// }

	return tiebaerror.ERR_SUCCESS
}
