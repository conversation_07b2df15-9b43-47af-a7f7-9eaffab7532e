package sprite

import (
	"context"
	"html"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/processpost"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	metaPost "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/hiphoto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbrichtext"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/typeutil"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	callfrom "icode.baidu.com/baidu/tieba-server-user-base/render/const"
	"icode.baidu.com/baidu/tieba-server-user-base/render/layers/page"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/yuelao2"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	commonMeta "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/common/meta"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/threadRecommend"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	defaultRecomNum = 8 // 一次请求的条数
)

func ThreadRecommend(ctx context.Context, baseData *types.SpriteThreadRecommendBaseData, response *threadRecommend.ThreadRecommendResIdl) int {
	threadId := baseData.Request.GetThreadId()
	// pn := baseData.Request.GetPn()
	userId, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	clientType := baseData.Request.GetCommon().GetXClientType()
	clientVersion := baseData.Request.GetCommon().GetXClientVersion()
	cuid := baseData.Request.GetCommon().GetShoubaiCuid()
	forumId := baseData.Request.GetForumId()
	forumName := baseData.Request.GetForumName()

	if clientType == stcdefine.CLIENT_TYPE_ANDROID {
		cuid = baseData.Request.GetCommon().GetCuidGalaxy2()
	}

	if userId == 0 {
		tbcontext.WarningF(ctx, "param error, user_id is 0")
		return tiebaerror.ERR_USER_NOT_LOGIN
	}

	if threadId == 0 {
		tbcontext.WarningF(ctx, "param error, thread_id is 0")
		return tiebaerror.ERR_PARAM_ERROR
	}

	// leadMsgKey := strconv.FormatInt(time.Now().UnixNano(), 16)
	// sendMsgInput := &chat.SendTextMsgForPersonalChatReq{
	// 	Pa:                proto.String(strconv.Itoa(SpritePa)),
	// 	Content:           proto.String("为您推荐以下内容"),
	// 	ToUid:             proto.Int64(userId),
	// 	MsgKey:            proto.String(leadMsgKey),
	// 	NeedFollowLoading: proto.Uint32(1),
	// }
	//
	// sendMsgRes := &chat.SendTextMsgForPersonalChatRes{}
	// err := tbservice.Call(ctx, "chat", "sendTextMsgForPersonalChat", sendMsgInput, sendMsgRes, tbservice.WithConverter(tbservice.JSONITER))
	// if err != nil || sendMsgRes.GetErrno() != tiebaerror.ERR_SUCCESS {
	// 	tbcontext.FatalF(ctx, "fail to call service chat:sendTextMsgForPersonalChat, input = %v, output = %v, err = %v", common.ToString(sendMsgInput), common.ToString(sendMsgRes), err)
	// 	return tiebaerror.ERR_CALL_SERVICE_FAIL
	// }

	// threadInput := &frs.MgetThreadReq{
	// 	ThreadIds: []uint64{threadId},
	// }
	// threadRes := &frs.MgetThreadRes{}
	// err := tbservice.Call(ctx, "post", "mgetThread", threadInput, threadRes, tbservice.WithConverter(tbservice.JSONITER))
	// if err != nil || threadRes.GetErrno() != tiebaerror.ERR_SUCCESS {
	// 	tbcontext.WarningF(ctx, "fail to call service post:mgetThread, input = %v, output = %v, err = %v", common.ToString(threadInput), common.ToString(threadRes), err)
	// 	return tiebaerror.ERR_CALL_SERVICE_FAIL
	// }
	// threadInfo := threadRes.GetOutput().GetThreadList()[0]
	// fmt.Println(common.ToString(threadInfo))

	var rn uint32 = defaultRecomNum
	res, e := wordserver.QueryKey(ctx, "tb_wordlist_redis_sprite_manual_intervention", "thread_recom_res_num")
	if e != nil {
		tbcontext.WarningF(ctx, "fail to call wordserver:queryKey, tb_wordlist_redis_sprite_manual_intervention:thread_recom_res_num, err = %v", e)
	} else {
		rn = cast.ToUint32(res)
	}

	loadType := 1
	// if pn == 1 {
	// 	loadType = 2
	// }
	recomInput := map[string]any{
		"thread_id":      threadId,
		"user_id":        userId,
		"forum_id":       forumId,
		"forum_name":     forumName,
		"cuid":           cuid,
		"net_type":       baseData.Request.GetCommon().GetNetType(),
		"load_type":      loadType,
		"client_type":    clientType,
		"client_version": clientVersion,
		"res_num":        rn,
		"from":           2,
		"sample_ids":     baseData.Request.GetCommon().GetSampleId(),
		"call_from":      "client_similar",
	}

	recomRes := &yuelao2.RecommSimilarContentsRes{}

	err := tbservice.Call(ctx, "yuelaou2", "recommSimilarContents", recomInput, recomRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call service yuelaou2:recommSimilarContents, input = %v, output = %v, err = %v", common.ToString(recomInput), common.ToString(recomRes), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	if len(recomRes.GetData().GetData().GetThread()) == 0 {
		// tbcontext.WarningF(ctx, "no recommend thread, input = %v", common.ToString(recomInput))
		//
		// // 发送一个兜底消息
		// fallbackMsgKey := strconv.FormatInt(time.Now().UnixNano(), 16)
		// sendMsgInput := &chat.SendTextMsgForPersonalChatReq{
		// 	Pa:           proto.String(strconv.Itoa(SpritePa)),
		// 	Content:      proto.String("这里空空的，什么都没有~"),
		// 	ToUid:        proto.Int64(userId),
		// 	MsgKey:       proto.String(fallbackMsgKey),
		// 	OriginMsgKey: proto.String(leadMsgKey),
		// }
		// sendMsgRes := &chat.SendTextMsgForPersonalChatRes{}
		// err := tbservice.Call(ctx, "chat", "sendTextMsgForPersonalChat", sendMsgInput, sendMsgRes, tbservice.WithConverter(tbservice.JSONITER))
		// if err != nil || sendMsgRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		// 	tbcontext.FatalF(ctx, "fail to call service chat:sendTextMsgForPersonalChat, input = %v, output = %v, err = %v", common.ToString(sendMsgInput), common.ToString(sendMsgRes), err)
		// 	return tiebaerror.ERR_CALL_SERVICE_FAIL
		// }
		return tiebaerror.ERR_SUCCESS
	}

	uniqTids := make(map[uint64]struct{})
	recomTids := make([]uint64, 0, len(recomRes.GetData().GetData().GetThread()))
	uniqFids := make(map[uint32]struct{})
	forumIds := make([]uint32, 0, len(recomRes.GetData().GetData().GetThread()))
	// 去重添加吧id和贴子id
	for _, thread := range recomRes.GetData().GetData().GetThread() {
		if _, ok := uniqTids[uint64(thread.GetTid())]; !ok {
			recomTids = append(recomTids, uint64(thread.GetTid()))
			uniqTids[uint64(thread.GetTid())] = struct{}{}
		}
		if _, ok := uniqFids[uint32(thread.GetFid())]; !ok {
			forumIds = append(forumIds, uint32(thread.GetFid()))
			uniqFids[uint32(thread.GetFid())] = struct{}{}
		}
	}

	// 并行调用
	multi := tbservice.Multi()

	// 获取帖子信息
	threadInput := &tbservice.Parameter{
		Service: "post",
		Method:  "mgetThread",
		Input: &frs.MgetThreadReq{
			ThreadIds:         recomTids,
			NeedAbstract:      proto.Uint32(1),
			NeedUserData:      proto.Uint32(1),
			NeedPostContent:   proto.Uint32(1),
			StructuredContent: proto.Uint32(1),
			NeedForumName:     proto.Uint32(1),
			NeedPhotoPic:      proto.Uint32(1),
			NeedMaskInfo:      proto.Uint32(1),
		},
		Output: &frs.MgetThreadRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetThread", threadInput)

	// 获取吧信息
	forumInput := &tbservice.Parameter{
		Service: "forum",
		Method:  "mgetBtxInfoEx",
		Input: &forum.MgetBtxInfoExReq{
			ForumId: forumIds,
		},
		Output: &forum.MgetBtxInfoExRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetBtxInfo", forumInput)

	multi.Call(ctx)

	threadOut, err := multi.GetResult(ctx, "mgetThread")
	threadRes, ok := threadOut.(*frs.MgetThreadRes)
	if err != nil || !ok || threadRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service post:mgetThread, input = %v, output = %v, err = %v", common.ToString(threadInput.Input), common.ToString(threadRes), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	forumOut, err := multi.GetResult(ctx, "mgetBtxInfo")
	forumRes, ok := forumOut.(*forum.MgetBtxInfoExRes)
	if err != nil || !ok || forumRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service forum:mgetBtxInfo, input = %v, output = %v, err= %v", common.ToString(forumInput.Input), common.ToString(forumRes), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// fmt.Println("mgetThread res\n", common.ToString(threadRes))

	threadList := threadRes.GetOutput().GetThreadList()
	userList := threadRes.GetOutput().GetThreadUserList()
	forumList := forumRes.GetOutput()
	clientThreadInfos := make([]*client.ThreadInfo, 0, len(recomTids))
	for _, tid := range recomTids {
		threadInfo, ok := threadList[tid]
		if !ok {
			tbcontext.WarningF(ctx, "fail to get thread info by tid = %d", tid)
			continue
		}

		clientThreadInfo := &client.ThreadInfo{
			Id:    proto.Int64(int64(tid)),
			Tid:   proto.Int64(int64(tid)),
			Title: proto.String(threadInfo.GetTitle()),
			Abstract: []*client.Abstract{
				{
					Type: proto.Int32(tbrichtext.SLOT_TYPE_TEXT),
					Text: proto.String(html.UnescapeString(threadInfo.GetAbstract())),
				},
			},
			Fid:   proto.Int64(int64(threadInfo.GetForumId())),
			Fname: proto.String(threadInfo.GetForumName()),
		}

		// rich_abstract数据
		clientThreadInfo.RichAbstract = processRichAbstract(ctx, threadInfo, baseData)

		// 解析标题中的话题
		clientThreadInfo.RichTitle = processRichTitle(ctx, threadInfo, baseData)

		// 处理贴子Media数据
		// 参考文献: https://console.cloud.baidu-int.com/devops/icode/repos/baidu/tieba-server-user-base/go-client-frs/blob/master/service/core/threadInfo.go#L1286
		if len(threadInfo.GetMedia()) > 0 {
			var thumbNail map[string][]*metaPost.ThumbnailCentrePointTag
			if threadInfo.GetThumbnailCentrePoint() != "" && threadInfo.GetThumbnailCentrePoint() != nil {
				thumbNail, err = threadInfo.GetThumbnailCentrePointStruct()
				if err != nil {
					thumbNail, err = threadInfo.GetMapThumbnailCentrePointStruct()
					if err != nil {
						tbcontext.WarningF(ctx, "GetThumbnailCentrePointStruct fail: %v", err)
					}
				}
			}
			clientThreadInfo.Media = processMedia(ctx, threadInfo, threadInfo.GetFirstPostId(), thumbNail["tag_2"])
		}

		forumInfo := forumList[threadInfo.GetForumId()]
		if forumInfo == nil {
			tbcontext.WarningF(ctx, "fail to get forum info by fid = %d", threadInfo.GetForumId())
		} else {
			// 贴子吧数据
			clientThreadInfo.ForumInfo = &client.SimpleForum{
				Id:        proto.Int64(int64(threadInfo.GetForumId())),
				Name:      proto.String(threadInfo.GetForumName()),
				Avatar:    proto.String(forumInfo.GetCard().GetAvatar()),
				MemberNum: proto.Int32(int32(forumInfo.GetStatistics().GetMemberCount())),
				PostNum:   proto.Int32(int32(forumInfo.GetStatistics().GetPostNum())),
			}
		}

		// 贴子作者数据
		userInfo := userList[threadInfo.GetUserId()]
		clientThreadInfo.Author = &client.User{
			Id:       proto.Int64(threadInfo.GetUserId()),
			NameShow: proto.String(userInfo.GetUserInfoNameShowV2(ctx)),
			Portrait: proto.String(userInfo.GetPortraitTime()),
		}

		clientThreadInfos = append(clientThreadInfos, clientThreadInfo)
	}

	// 渲染feedlist
	feedlist := &page.FeedlistOp{
		Callfrom: "sprite_recommend",
		Ctx:      ctx,
		CReq: callfrom.CommonReq{
			ClientType:    int(clientType),
			ClientVersion: clientVersion,
		},
		Request:        baseData.BaseObj.ObjRequest,
		ThreadInfoList: clientThreadInfos,
	}

	layoutList := feedlist.Render()

	// threadMsgKey := strconv.FormatInt(time.Now().UnixNano(), 16)
	// threadMsgInput := &chat.SendTextMsgForPersonalChatReq{
	// 	Pa:           proto.String(strconv.Itoa(SpritePa)),
	// 	Content:      proto.String("hello hello hello"),
	// 	ToUid:        proto.Int64(userId),
	// 	OriginMsgKey: proto.String(leadMsgKey),
	// 	MsgKey:       proto.String(threadMsgKey),
	// 	ExtraType:    proto.Int32(105),
	// 	ThreadRecom: &chat.PersonalChatThreadRecom{
	// 		FeedList: &feedList,
	// 		Pn:       proto.Uint32(baseData.Request.GetPn()),
	// 		HasMore:  proto.Uint32(recomRes.GetData().GetHasMore()),
	// 	},
	// }
	// threadMsgOutput := &chat.SendTextMsgForPersonalChatRes{}
	//
	// err = tbservice.Call(ctx, "chat", "sendTextMsgForPersonalChat", threadMsgInput, threadMsgOutput, tbservice.WithConverter(tbservice.JSONITER))
	// if err != nil || threadMsgOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
	// 	tbcontext.WarningF(ctx, "fail to call service chat:sendTextMsgForPersonalChat, input = %v, output = %v, err = %v", common.ToString(threadMsgInput), common.ToString(threadMsgOutput), err)
	// 	return tiebaerror.ERR_CALL_SERVICE_FAIL
	// }

	response.Data.Pn = baseData.Request.Pn
	response.Data.Rn = proto.Uint32(rn)
	response.Data.FeedList = layoutList
	response.Data.HasMore = proto.Uint32(recomRes.GetData().GetHasMore())
	return tiebaerror.ERR_SUCCESS
}

// 处理thread.Media 到 clientThread.Media
// 节选自 https://console.cloud.baidu-int.com/devops/icode/repos/baidu/tieba-server-user-base/go-client-frs/blob/master/service/core/threadInfo.go#L813
func processMedia(ctx context.Context, thread *metaPost.ThreadInfo, postId uint64, tcps []*metaPost.ThumbnailCentrePointTag) []*client.Media {
	media := thread.GetMedia()
	mediaNum := 0
	mediaType := make([]string, len(media))
	for i, m := range media {
		mediaType[i] = m.GetType()
		if m.GetType() == "pic" {
			mediaNum++
		}
		// 避免json化失败
		m.Type = nil
	}

	var (
		res []*client.Media
		err error
	)
	if len(media) == 0 {
		return res
	}
	res, err = thread.MediaToClientMedia()
	if err != nil {
		tbcontext.WarningF(ctx, "MediaToClientMedia fail: %v", err)
	}

	for i, m := range media {
		if mediaType[i] == "pic" {
			res[i].Type = proto.Int32(tbrichtext.SLOT_TYPE_IMG)
			res[i].PostId = proto.Int64(int64(postId))
			res[i].WaterPic = proto.String(m.GetBigPic())

			picId := hiphoto.GetPicIdFromUrl(m.GetBigPic())
			for _, tcp := range tcps {
				if tcp.GetPicId() == picId {
					if mediaNum > 1 {
						res[i].WthMidLoc = proto.Float64(tcp.GetCenterMulti()[0])
						res[i].HthMidLoc = proto.Float64(tcp.GetCenterMulti()[1])
					} else {
						res[i].WthMidLoc = proto.Float64(tcp.GetCenterSingle()[0])
						res[i].HthMidLoc = proto.Float64(tcp.GetCenterSingle()[1])
					}
				}
			}

		} else if mediaType[i] == "abstract" {
			res[i].Type = proto.Int32(tbrichtext.SLOT_TYPE_TEXT)
		} else if mediaType[i] == "flash" {
			res[i].Type = proto.Int32(tbrichtext.SLOT_TYPE_EMBED)
			if m.GetVsrc() == "xiaoying.tv" {
				res[i].Type = proto.Int32(tbrichtext.SLOT_TYPE_EMBED_XIAOYING)
			}
		} else if mediaType[i] == "music" {
			res[i].Type = proto.Int32(tbrichtext.SLOT_TYPE_MUSIC)
		}
	}

	return res
}

func processRichAbstract(ctx context.Context, thread *metaPost.ThreadInfo, baseData *types.SpriteThreadRecommendBaseData) []*client.PbContent {
	pc := &tbrichtext.GParserCondition
	pc.NewLineCount = 1
	pc.BolParseBdhd = true
	pc.BolParsePhone = true
	pc.BolCheckSpamUrl = true
	pc.BolGraffitiToImg = true

	clientType := baseData.Request.GetCommon().GetXClientType()
	clientVersion := baseData.Request.GetCommon().GetXClientVersion()
	forumId := baseData.Request.GetForumId()

	// pb结构化后，解析content方式
	// 对content进行内容处理,包括参数的过滤和调整
	objParserStruct := new(tbrichtext.ParserStructured)
	objParserStruct.SetClientType(int(clientType))
	objParserStruct.SetClientVersion(clientVersion)
	objParserStruct.SetFid(int64(forumId))

	var (
		postContent []*commonMeta.PostStructContent
		err         error
	)
	if thread.GetPostContent() != "" && thread.GetPostContent() != nil {
		postContent, err = thread.GetPostContentStruct()
		if err != nil {
			tbcontext.WarningF(ctx, "GetPostContentStruct fail: %v", err)
			return nil
		}
	}

	if len(postContent) != 0 {
		parserOut, err := objParserStruct.Process(ctx, &tbrichtext.ParserStructProcessInput{
			PObjCondition: pc,
			BolEmoji:      true,
			// ScreenWidth:    int(param.screenWidth),
			// ScreenHeight:   int(param.screenHeigh),
			ArrText:        postContent,
			BolIsAllOrigin: true,
			BolNeedTopic:   true,
		})

		if err != nil {
			tbcontext.WarningF(ctx, "parserStructured fail: %v", err)
			return nil
		}

		param := &processpost.ReqParam{
			ClientType:    common.TransforValueToTargetType(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT).(int),
			ClientVersion: common.TransforValueToTargetType(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string),
			Uid:           common.TransforValueToTargetType(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT).(int),
		}
		processPost := processpost.InitParam(param, baseData.BaseObj.ObjRequest.GetStrategyMap())
		richContent, err := processpost.ProcessContent(ctx, parserOut.ArrSlotContent, processPost)
		if err != nil {
			tbcontext.WarningF(ctx, "ArrSlotContent ProcessContent fail: %v", err)
		}
		return richContent
	}

	return nil
}

func processRichTitle(ctx context.Context, thread *metaPost.ThreadInfo, baseData *types.SpriteThreadRecommendBaseData) []*client.PbContent {
	clientVersion := baseData.Request.GetCommon().GetXClientVersion()
	forumId := baseData.Request.GetForumId()
	parserStruct := new(tbrichtext.ParserStructured)
	parserStruct.SetClientVersion(clientVersion)
	parserStruct.SetFid(int64(forumId))
	topicContents := parserStruct.GetStructFromTopicContent(ctx, thread.GetTitle(), 30, 10)
	content := make([]*client.PbContent, 0)
	for _, tc := range topicContents {
		if tc.GetTag() == tbrichtext.CONTENT_ITEM_TAG_TXT {
			content = append(content, &client.PbContent{
				Type: proto.Uint32(tbrichtext.SLOT_TYPE_TEXT),
				Text: proto.String(typeutil.InterfaceToString(tc.GetValue())),
			})
		}

		if tc.GetTag() == tbrichtext.CONTENT_ITEM_TAG_A {
			href := tc.GetHref()
			var value []map[string]interface{}
			err := common.StructAToStructBCtx(ctx, tc.GetValue(), &value)
			if err != nil {
				tbcontext.WarningF(ctx, "value is not []map[string]interface{}")
				continue
			}
			v, _ := value[0]["value"].(string)
			if v == "" {
				continue
			}

			content = append(content, &client.PbContent{
				Type: proto.Uint32(tbrichtext.SLOT_TYPE_TOPIC),
				Text: proto.String(html.UnescapeString(v)),
				Link: proto.String(html.UnescapeString(href)),
			})
		}
	}
	return content
}
