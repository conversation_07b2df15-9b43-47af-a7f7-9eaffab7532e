package sprite

import (
	"context"
	"strconv"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	stProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/stopThinking"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func StopThinking(ctx context.Context, baseData *types.StopThinkingBaseData, response *stProto.StopThinkingResIdl) int {
	// 前置校验
	errNo := stopThinkingPreCheck(ctx, baseData)
	if errNo != tiebaerror.ERR_SUCCESS {
		return errNo
	}

	// 获取用户ID
	userID, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	if userID <= 0 {
		tbcontext.WarningF(ctx, "input param error, user_id is empty")
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 记录拦截key
	strUserID, _ := common.Tvttt(userID, common.TTT_STRING).(string)
	key := types.StopThinkKeyPrefix + strUserID + "_" + baseData.MsgKey
	expr := time.Duration(30) * time.Second
	success, err := resource.RedisMsglogic.SetNX(ctx, key, "1", expr).Result()
	if err != nil {
		tbcontext.FatalF(ctx, "resource::cacheSign::set fail, err=%v, key=%v, expr=%v", err, key, expr)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	if !success {
		tbcontext.WarningF(ctx, "answer already send, cannot stop thinking, key=%v", key)
		return tiebaerror.ERR_SUCCESS
	}

	// 发送消息
	content, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_sprite_manual_intervention", "sprite_stop_thinking_msg")
	if err != nil {
		tbcontext.WarningF(ctx, "get sprite stop thinking msg from word server failed, err=%v, wordlist=tb_wordlist_redis_sprite_manual_intervention, key=sprite_stop_thinking_msg", err)
		return tiebaerror.ERR_WORDLIST_CONN_WORDSERVER_FAIL
	}

	msgKey := strconv.FormatInt(time.Now().UnixNano(), 16)
	sendInput := &chat.SendTextMsgForPersonalChatReq{
		Pa:           proto.String("17592328994873"),
		Content:      proto.String(content),
		ToUid:        proto.Int64(userID),
		MsgKey:       proto.String(msgKey),
		OriginMsgKey: proto.String(baseData.MsgKey),
	}
	sendOutput := &chat.SendTextMsgForPersonalChatRes{}
	err = tbservice.Call(ctx, "chat", "sendTextMsgForPersonalChat", sendInput, sendOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || sendOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service chat:sendTextMsgForPersonalChat, input = %v, output = %v, err = %v", common.ToString(sendInput), common.ToString(sendOutput), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	return tiebaerror.ERR_SUCCESS
}

// stopThinkingPreCheck 前置校验
func stopThinkingPreCheck(ctx context.Context, baseData *types.StopThinkingBaseData) int {
	// 参数校验
	msgKey := baseData.Request.GetMsgKey()
	if len(msgKey) <= 0 {
		tbcontext.WarningF(ctx, "stopThinkingPreCheck input invalid")
		return tiebaerror.ERR_PARAM_ERROR
	}

	baseData.MsgKey = msgKey
	return tiebaerror.ERR_SUCCESS
}
