package sprite

import (
	"context"

	"google.golang.org/protobuf/proto"

	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	cmsProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/checkMemeStatus"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func CheckMemeStatus(ctx context.Context, baseData *types.CheckMemeStatusBaseData, response *cmsProto.CheckMemeStatusResIdl) int {

	// 前置校验
	errNo := checkMemeStatusPreCheck(ctx, baseData)
	if errNo != tiebaerror.ERR_SUCCESS {
		return errNo
	}

	// 查询生成结果
	statusReq := &commonProto.QueryMemeGenerateResultReq{
		QueryId: proto.Int64(baseData.QueryID),
	}
	statusRes := &commonProto.QueryMemeGenerateResultRes{}
	statusOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("common_go"),
	}
	err := tbservice.Call(ctx, "common", "queryMemeGenerateResult", statusReq, statusRes, statusOption...)
	if err != nil || statusRes == nil || statusRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "CheckMemeStatus call common::queryMemeGenerateResult fail, err=%v, input=%s, output=%s", err, common.ToString(statusReq), common.ToString(statusRes))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	statusData := statusRes.GetData()

	// 构造数据
	memeList := make([]*cmsProto.AigcMemeInfo, 0, len(statusData.GetMemeList()))
	for _, item := range statusData.GetMemeList() {
		memeList = append(memeList, &cmsProto.AigcMemeInfo{
			Id:        proto.Int64(item.GetId()),
			Type:      proto.Uint32(memeTypeNoRealTime),
			Src:       proto.String(item.GetIcon()),
			ObjSource: proto.Uint32(uint32(item.GetObjSource())),
		})
	}

	response.Data.Status = proto.Uint32(statusData.GetStatus())
	response.Data.MemeList = memeList
	return tiebaerror.ERR_SUCCESS
}

// checkMemeStatusPreCheck 前置校验
func checkMemeStatusPreCheck(ctx context.Context, baseData *types.CheckMemeStatusBaseData) int {
	// 参数校验
	queryID := common.Tvttt(baseData.BaseObj.ObjRequest.GetPrivateAttr("query_id", ""), common.TTT_INT64).(int64)
	if queryID <= 0 {
		tbcontext.WarningF(ctx, "checkMemeStatusPreCheck input invalid")
		return tiebaerror.ERR_PARAM_ERROR
	}

	baseData.QueryID = queryID
	return tiebaerror.ERR_SUCCESS
}
