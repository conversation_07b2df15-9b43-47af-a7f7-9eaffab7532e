package sprite

const (
	// sceneClickRecom 直接点击场景，只查询离线表情包
	sceneClickRecom = "click_recom"

	// sceneInputQuery 输入查询场景，查询离线表情包+在线表情包
	sceneInputQuery = "input_query"

	// sceneUseSame 用同款场景，查询同款表情包+离线表情包+在线表情包
	sceneUseSame = "use_same"
)

const (
	// memeStatusInit 表情包状态 - 初始状态
	memeStatusInit = 0

	// memeStatusOnline 表情包状态 - 在线状态
	memeStatusOnline = 1

	// memeStatusOffline 表情包状态 - 离线状态
	memeStatusOffline = 2
)

const (
	// memeTypeNoRealTime 表情包样式 - 离线表情包
	memeTypeNoRealTime = 1

	// memeTypeRealTime 表情包样式 - 在线表情包
	memeTypeRealTime = 2
)

const (
	// memeSourceNoRealTime 表情包来源 - 离线表情包
	memeSourceNoRealTime = 1

	// memeSourceRealTime 表情包来源 - 在线表情包
	memeSourceRealTime = 2
)

const (
	// memeTotalRn 一页总表情包数量
	memeTotalRn = memeNoRealTimeRn

	// ====Critical==== 按照当前业务逻辑 memeNoRealTimeRn 需严格大于 memeRealTimeRn

	// memeNoRealTimeRn 离线表情包每页请求数量
	memeNoRealTimeRn = 5

	// memeRealTimeRn 在线表情包每页请求数量
	memeRealTimeRn = 0
)
