package sprite

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/getSpriteChatDetail"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	nmq "icode.baidu.com/baidu/tieba-server-go/golib2/commit"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	SpritePa = 17592328994873
	SpriteUk = "wkMPXvAp1GObWY-Y7dRjbg"

	// from来源
	FromDefault       = "default"        // 默认
	FromIndex         = "index"          // 首页
	FromSearchSummary = "search_summary" // 搜索总结
	FromCatchphrase   = "catchphrase"    // 热梗
	FromChat          = "chat"           // 聊天页
	FromFrs           = "frs"            // frs页
	FromPb            = "pb"             // pb页

	// scene场景
	SceneDefault         = "default"    // 默认场景
	SceneFirstSet        = "first_set"  // 首次认领
	SceneColdStart       = "cold_start" // 冷启动
	SceneChangeCharacter = "change"     // 更换人设
	SceneSLevel          = "s_level"    // s等级事件
)

type CatchphraseGuideConfig struct {
	MsgContent       string `json:"msg_content"`
	CatchphraseQuote string `json:"catchphrase_quote"`
}

func GetSpriteChatDetail(ctx context.Context, baseData *types.GetSpriteChatDetailBaseData,
	response *getSpriteChatDetail.GetSpriteChatDetailResIdl) int {
	from := baseData.Request.GetSceneFrom()
	scene := baseData.Request.GetScene()
	userId, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	strClientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", 0), common.TTT_STRING).(string)

	if userId <= 0 {
		tbcontext.WarningF(ctx, "input param error, user_id is empty")
		return tiebaerror.ERR_PARAM_ERROR
	}

	guideInfo := &getSpriteChatDetail.GuideInfo{}

	// 版本控制,12.52以上不走原来的逻辑了
	if clientvers.Compare("12.52.0", strClientVersion) >= 0 {
		needLoad := NeedLoading(ctx, scene, from)
		if needLoad {
			guideInfo.NeedLoading = proto.Uint32(1)
		} else {
			guideInfo.NeedLoading = proto.Uint32(0)
		}

		response.Data.GuideInfo = guideInfo
		return tiebaerror.ERR_SUCCESS
	}

	if from == "catchphrase" {
		chatInfoInput := &chat.GetSpriteChatInfoReq{
			Rn:   proto.Int32(3),
			Uk:   proto.String(SpriteUk),
			Type: proto.Int32(12), // 请求热梗库，从热梗库随机取n条
		}
		chatInfoRes := &chat.GetSpriteChatInfoRes{}
		err := tbservice.Call(ctx, "chat", "getSpriteChatInfo", chatInfoInput, chatInfoRes, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || chatInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.FatalF(ctx, "fail to call service chat:getSpriteChatInfo, input = %v, output = %v, err = %v", common.ToString(chatInfoInput), common.ToString(chatInfoRes), err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		guideInfo = &getSpriteChatDetail.GuideInfo{
			NeedLoading: proto.Uint32(1),
		}

		// 读词表，取热梗引导的标题 & sug配置
		res, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_sprite_manual_intervention", "catchphrase_guide_config")
		if err != nil {
			tbcontext.FatalF(ctx, "fail to query wordlist catchphrase config, err = %v", err)
			return tiebaerror.ERR_WORDLIST_CONN_WORDSERVER_FAIL
		}

		config := &CatchphraseGuideConfig{}

		err = json.Unmarshal([]byte(res), config)
		if err != nil {
			tbcontext.FatalF(ctx, "catchphrase config format error, err = %v", err)
			return tiebaerror.ERR_MO_INTERNAL_ERROR
		}

		content := config.MsgContent
		sugList := make([]*chat.AbilityInfo, 0, 3)
		for _, data := range chatInfoRes.GetData().GetInfoList() {
			sugList = append(sugList, &chat.AbilityInfo{
				AbilityType: proto.String("send_msg"),
				AbilityConf: &chat.AbilityInfoAbilityConf{
					Type: proto.Int32(1),
					Content: &chat.AbilityConfSendMsgContent{
						Text: proto.String(fmt.Sprintf(config.CatchphraseQuote, data.GetInfo())),
					},
				},
				StyleConf: &chat.AbilityInfoStyleConf{
					Scene:   proto.String("sprite_guide"),
					Content: proto.String(fmt.Sprintf(config.CatchphraseQuote, data.GetInfo())),
				},
			})
		}

		// 发送消息
		go func(ctx context.Context) {
			msgKey := strconv.FormatInt(time.Now().UnixNano(), 16)
			sendMsgInput := &chat.SendTextMsgForPersonalChatReq{
				Pa:      proto.String(cast.ToString(SpritePa)),
				Content: proto.String(content),
				ToUid:   proto.Int64(userId),
				MsgKey:  proto.String(msgKey),
				MsgSugInfo: &chat.MsgSugInfo{
					SugList: sugList,
				},
			}

			sendMsgOutput := new(chat.SendTextMsgForPersonalChatRes)
			err = tbservice.Call(ctx, "chat", "sendTextMsgForPersonalChat", sendMsgInput, sendMsgOutput,
				tbservice.WithConverter(tbservice.JSONITER), tbservice.WithTimeout(2500*time.Millisecond))
			if err != nil || sendMsgOutput == nil || sendMsgOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
				tbcontext.FatalF(ctx, "fail to call service chat:sendTextMsgForPersonalChat, err = %v, input = %v, output = %v", err, common.ToString(sendMsgInput), common.ToString(sendMsgOutput))
			}
		}(logit.CopyAllFields(context.Background(), ctx))
	} else if from == "search_summary" {
		cmd := "aigcPipeline"
		pipeline := "sprite_searchsummary"

		js, err := json.Marshal(map[string]any{
			"user_id": userId,
		})
		extra := string(js)
		if err != nil {
			tbcontext.FatalF(ctx, "json marshal error, err = %s", err.Error())
		} else {
			extra = fmt.Sprintf("{\"user_id\":%d}", userId)
		}

		nmqReq := map[string]any{
			"pipeline": pipeline,
			"callback_info": map[string]any{
				"service": "chat",
				"method":  "sendSearchSummaryMsg",
			},
			"business_info": map[string]string{
				"content": baseData.Request.GetQuery(),
				"user_id": cast.ToString(userId),
				"from":    "sprite_searchsummary",
			},
			"extra": extra,
		}

		tbcontext.NoticeF(ctx, "commit %v, nmq param: %v", cmd, common.ToString(nmqReq))
		_, err = nmq.Commit(ctx, "common", cmd, nmqReq, 0)
		if err != nil {
			tbcontext.WarningF(ctx, "nmq Commit common::%v failed, err=%s", cmd, err.Error())
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		guideInfo = &getSpriteChatDetail.GuideInfo{
			NeedLoading: proto.Uint32(1),
		}
	}

	response.Data.GuideInfo = guideInfo
	return tiebaerror.ERR_SUCCESS
}

// 是否继续请求接口c/x/sprite/getGuideInfo
func NeedLoading(ctx context.Context, scene string, from string) bool {
	// 1. 首页+首次认领 （库里拿,一定会有人设）
	// 2. 首页+冷启（取3个库，top库取2个，bottom库取一个，可能没有人设，没有的话就取默认人设和称呼）
	// 3. 聊天页+更换人设 （发一个消息，一定会有人设）
	// 4. 搜索总结+默认场景（取文心，不需要人设）
	// 5. 热梗+默认场景（热梗库，不需要人设）
	// 6. 默认来源 + s级事件 (同 首页+冷启)
	// 7. frs + 默认场景（同 首页+冷启）
	// 8. pb + 默认场景（同 首页+冷启）
	// 9. 首页+帖子长按/推荐 （不需要返回引导语）
	if scene == "" {
		scene = SceneDefault
	}
	if from == "" {
		from = FromDefault
	}

	if from == FromIndex && (scene == SceneFirstSet || scene == SceneColdStart) {
		// 首页 + 首次认领/冷启
		return true
	} else if from == FromChat && scene == SceneChangeCharacter {
		// 聊天页+更换人设
		return true
	} else if from == FromDefault && scene == SceneSLevel {
		// 默认来源 + s级事件
		return true
	} else if scene == SceneDefault && (from == FromSearchSummary || from == FromCatchphrase || from == FromFrs || from == FromPb) {
		// 搜索总结+默认场景/热梗+默认场景/frs+默认场景/pb+默认场景
		return true
	}

	return false
}
