package sprite

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/modata"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/baidu/uidxuk"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/getSpriteSpeech"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// 场景
const (
	sceneGuide               = 1 // 播放动画引导语场景
	scenePrologue            = 2 // 开场白场景
	sceneFirstColdStart      = 3 // 首次冷启场景
	sceneColdStart           = 4 // 非首次，但冷启
	sceneStay                = 5 // 停留场景
	sceneExitDialog          = 6 // 认领后从对话页退出，趴在底bar
	sceneCloseClaimGuide     = 7 // 关闭认领半浮层
	sceneDailyFirstCodeStart = 8 // 每日首次冷启
	sceneExposureNoClick     = 9 // 曝光无点击
)

// 发言类型
const (
	speechTypeSelf             = 1  // 自言自语类发言
	speechTypeQA               = 2  // 有问有答类发言
	speechTypeOperation        = 3  // 运营配置类发言
	speechTypeQuestion         = 4  // 预设问题
	speechTypePrologue         = 5  // 开场白
	speechTypeDefaultAnswer    = 6  // 默认答案类型
	speechTypeColdStart        = 7  // 冷启库
	speechTypeBoomerang        = 8  // 热梗科普库
	speechTypeNewUser          = 9  // 新手指南库
	speechTypeNewUserOperation = 11 // 新低用户运营干预
	speechTypeDailyCodeStart   = 16 // 每日首次冷启
)

// 场景的固定文案
const (
	speechGuide = "各位好久不见～\n在下滑稽，AKA 贴吧大聪明\n主打一个 「无所不知」\n不服来战~！"
)

const (
	ManualInterventionWordList         = "tb_wordlist_redis_sprite_manual_intervention"
	FirstColdStartKey                  = "sprite_cold_start_text_default"
	FirstColdStartNewUserKey           = "sprite_cold_start_text_default_new_user"
	StaySpeechTypePercentageKey        = "sprite_stay_speech_type_percentage"
	StaySpeechTypePercentageNewUserKey = "sprite_stay_speech_type_percentage_new_user"
	SpeechTypeIconKeyPrefix            = "sprite_icon_type_"
	SpeechTypePercentagePrefix         = "speech_type_"
	CloseClaimGuideKey                 = "sprite_close_claim_guide_text_default"
	ExitDialogKey                      = "sprite_characters_exit_dialog_text"
	ExposureNoClickKey                 = "sprite_exposure_no_click_text_default"
	DailyFirstCodeStartKey             = "sprite_daily_first_code_start_text_default"

	SpriteDailyFirstCodeStartWordList = "tb_wordlist_redis_sprite_dfcs_library_funny"
)

// ios、 安卓默认下发配置
const (
	iosPicDark     = "https://tieba-client-static-res.cdn.bcebos.com/funny_sprite_bubble_dark%403x.png"
	iosPicDay      = "https://tieba-client-static-res.cdn.bcebos.com/funny_sprite_bubble%403x.png"
	androidPicDark = "https://tieba-ares.cdn.bcebos.com/mis/2023-4/1681974723248/d9639724ea68.png"
	androidPicDay  = "https://tieba-ares.cdn.bcebos.com/mis/2023-4/1681974723608/bfc85917b636.png"

	bubblePicNew    = "https://tieba-ares.cdn.bcebos.com/mis/2023-12/1702393408817/6e74d159537f.png"
	bubblePicNewIOS = "https://tieba-client-static-res.cdn.bcebos.com/new_sprite_bubble%403x.png"

	iosLightColor   = "24_30_24_24"
	commonColorDay  = "FF141414"
	commonColorDark = "FF141414"
)

// IconConfig 词表配置的气泡信息结构
type IconConfig struct {
	AndroidPicDay  string `json:"android_pic_day"`
	AndroidPicDark string `json:"android_pic_dark"`
	IosPicDay      string `json:"ios_pic_day"`
	IosPicDark     string `json:"ios_pic_dark"`
	IosLightColor  string `json:"ios_light_color"`
	TextColorDay   string `json:"text_color_day"`
	TextColorDark  string `json:"text_color_dark"`
	BtnText        string `json:"btn_text"`
}

func GetSpriteSpeech(ctx context.Context, baseData *types.GetSpriteSpeechBaseData,
	response *getSpriteSpeech.GetSpriteSpeechResIdl) int {
	// 根据场景获取发言信息
	scene := baseData.Request.GetScene()
	spriteUk := baseData.Request.GetSpriteUk()
	displayOrder := baseData.Request.GetDisplayOrder()
	uid, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)

	version := baseData.Request.GetVersion()

	clientType := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT32).(int32)
	clientVersion, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)

	// 这里给安卓单端兜底，在 12.44 版本有 bug，启动后会在 5s 内连续请求 2 次这个接口，导致用户看到 2 个气泡弹窗
	// 客户端随版本修复，server 先在这里针对单端进行粒度控制，并写死时间，只在 8.20 号之前生效，之后因为版本发出去了，所以不再兜底了
	if uid != 0 && clientType == clientvers.CLIENT_TYPE_ANDROID && time.Now().Unix() <= 1692460800 {
		isControl := resource.RedisUserGrowth.SetNX(ctx, fmt.Sprintf("android_client_spriterepeate_%d", uid), "1", time.Second*8)
		if isOk, err := isControl.Result(); err == nil {
			if !isOk {
				return tiebaerror.ERR_SUCCESS
			}
		} else {
			// 打日志，然后正常走后面的流程
			// 如果粒度控制出问题了，则不进行兜底
			tbcontext.WarningF(ctx, "call redis usergrowth to get sprite repeat key failed! uid[%d] err[%s]", uid, err.Error())
		}
	}

	newUser := isNewUser(ctx, baseData)

	if scene <= 0 || spriteUk == "" {
		tbcontext.WarningF(ctx, "getSpriteSpeech input param error, scene = %v, sprite_uk = %v", scene, spriteUk)
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 根据不同场景，请求不同类型的发言
	speechType := int32(0)          // 请求的发言类型
	text := ""                      // 返回给端的用于展示的text字段
	needSend := false               // 返回给端的need_send字段
	sendText := ""                  // 返回给端的send_text 字段
	sendToImText := ""              // 发送到中台的文案
	newVersion := ""                // 运营文案的版本
	var disappearSeconds int32 = -1 // 气泡小时秒数
	relatedList := make([]*chat.RelateInfo, 0)
	rn := 1 // 返回给端的默认起始顺序

	if scene == sceneGuide {
		// 引导动画场景，不需要请求service，直接发送固定文案
		speechType = 0
		text = speechGuide
		sendToImText = speechGuide
		relatedList = append(relatedList, &chat.RelateInfo{
			Type:    proto.Int32(1),
			Content: proto.String(speechGuide),
		})
	} else if scene == sceneFirstColdStart {
		// 首次冷启，不需要请求service，直接下发固定文案
		speechType = 0
		// 区分新老用户，请求不同的首次冷启内容
		wordKey := FirstColdStartKey
		if newUser {
			wordKey = FirstColdStartNewUserKey
		}
		wordRes, err := wordserver.QueryKey(ctx, ManualInterventionWordList, wordKey)
		if err != nil || wordRes == "" {
			tbcontext.WarningF(ctx, "fail to get default cold start text, err = %v, wordRes = %v", err, wordRes)
			return tiebaerror.ERR_WORDLIST_CONN_WORDSERVER_FAIL
		}
		text = wordRes
		sendToImText = wordRes
		// 手动构造一个点击后发送的结构
		relatedList = append(relatedList, &chat.RelateInfo{
			Type:    proto.Int32(1),
			Content: proto.String(wordRes),
		})
	} else if scene == scenePrologue {
		// 请求开场白库
		speechType = speechTypePrologue
	} else if scene == sceneColdStart {
		// 冷启请求运营干预发言
		speechType = speechTypeOperation
		if newUser {
			speechType = speechTypeNewUserOperation
		}
	} else if scene == sceneStay {
		var err error
		speechType, err = getStaySpeechType(ctx, baseData)
		if err != nil {
			tbcontext.WarningF(ctx, "fail to call getStaySpeechType, err = %v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
	} else if scene == sceneExitDialog {
		if uid <= 0 {
			// 未登录，直接返回成功
			return tiebaerror.ERR_SUCCESS
		}
		speechType = 0
		// 区分人设
		characterInfo, err := getSpriteCharacterInfo(ctx, spriteUk, uid)
		if err != nil || characterInfo == nil {
			tbcontext.WarningF(ctx, "fail to getSpriteCharacterInfo, err = %v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		character := characterInfo.GetCharacter()
		if character == "" {
			// 该场景不应该有未认领的情况
			tbcontext.WarningF(ctx, "exit dialog and get character empty, spriteUk:%v, uid:%v", spriteUk, uid)
			return tiebaerror.ERROR_MIS_WORDLIST_HOOK_ERROR
		}
		// 从词表中获取固定文案
		appellation := characterInfo.GetAppellation()
		if utf8.RuneCountInString(appellation) > 8 {
			appellation = string([]rune(appellation)[:7]) + "..."
		}
		wordRes, err := wordserver.QueryKey(ctx, ManualInterventionWordList, ExitDialogKey)
		if err != nil || wordRes == "" {
			tbcontext.WarningF(ctx, "fail to get default exit dialog text, err = %v, wordRes = %v", err, wordRes)
			return tiebaerror.ERR_WORDLIST_CONN_WORDSERVER_FAIL
		}
		characterText := &map[string]string{}
		jerr := json.Unmarshal([]byte(wordRes), characterText)
		if jerr != nil {
			tbcontext.WarningF(ctx, "fail to unmarshal characterText, err = %v", jerr)
			return tiebaerror.ERROR_MIS_WORDLIST_HOOK_ERROR
		}
		if textConf, ok := (*characterText)[character]; ok {
			text = strings.ReplaceAll(textConf, "$##$", appellation)
		} else {
			tbcontext.WarningF(ctx, "not found this character, character = %v", character)
			return tiebaerror.ERROR_MIS_WORDLIST_HOOK_ERROR
		}
	} else if scene == sceneCloseClaimGuide {
		// 中途关闭半浮层，下发固定文案
		speechType = 0
		wordRes, err := wordserver.QueryKey(ctx, ManualInterventionWordList, CloseClaimGuideKey)
		if err != nil || wordRes == "" {
			tbcontext.WarningF(ctx, "fail to get default close claim guide text, err = %v, wordRes = %v", err, wordRes)
			return tiebaerror.ERR_WORDLIST_CONN_WORDSERVER_FAIL
		}
		text = wordRes
	} else if scene == sceneDailyFirstCodeStart {
		// 每日首次冷启
		speechType = 0
		key := fmt.Sprintf("%d", speechTypeDailyCodeStart)
		displayOrderMap := map[string]int{}
		if displayOrder != "" {
			if jerr := json.Unmarshal([]byte(displayOrder), &displayOrderMap); jerr != nil {
				tbcontext.WarningF(ctx, "fail to unmarshal displayOrderMap, err = %v", jerr)
				return tiebaerror.ERR_PARAM_ERROR
			}
		}

		appellation := "8u"
		character := ""
		if uid > 0 {
			characterInfo, err := getSpriteCharacterInfo(ctx, spriteUk, uid)
			if err != nil || characterInfo == nil {
				tbcontext.WarningF(ctx, "fail to getSpriteCharacterInfo, err = %v", err)
				return tiebaerror.ERR_CALL_SERVICE_FAIL
			}
			character = characterInfo.GetCharacter()
			appellation = characterInfo.GetAppellation()
			if utf8.RuneCountInString(appellation) > 8 {
				appellation = string([]rune(appellation)[:7]) + "..."
			}
		}
		if character == "" {
			if displayOrder == "" {
				displayOrderMap[key] = 1
			}
			if v, ok := displayOrderMap[key]; ok {
				rn = v
			}
			// 未认领，取词表配置，按顺序下发文案
			wordResMap, err := wordserver.QueryItemsNoPHPSerialized(ctx, ManualInterventionWordList, []string{DailyFirstCodeStartKey})
			if err != nil || len(wordResMap) == 0 {
				tbcontext.WarningF(ctx, "fail to get default daily first code start text, err = %v, wordRes = %v", err, wordResMap)
				return tiebaerror.ERR_WORDLIST_CONN_WORDSERVER_FAIL
			}
			wordRes := wordResMap[DailyFirstCodeStartKey]
			arrText := []string{}
			if err = common.StructAToStructBCtx(ctx, wordRes, &arrText); err != nil {
				tbcontext.WarningF(ctx, "fail to modify struct arrText, err = %v", err)
				return tiebaerror.ERROR_MIS_WORDLIST_HOOK_ERROR
			}
			if rn >= len(arrText) {
				rn = 1
			} else {
				rn++
			}
			// 赋值新的顺序，透传到端
			displayOrderMap[key] = rn
			displayOrderJSON, jerr := json.Marshal(displayOrderMap)
			if jerr != nil {
				tbcontext.WarningF(ctx, "fail to marshal displayOrderMap, err = %v", jerr)
				return tiebaerror.ERROR_MIS_WORDLIST_HOOK_ERROR
			}
			displayOrder = string(displayOrderJSON)
			text = arrText[rn-1]
		} else {
			week := time.Now().Weekday().String()
			keyword := fmt.Sprintf("%s_%s", character, week)
			wordResMap, err := wordserver.QueryItemsNoPHPSerialized(ctx, SpriteDailyFirstCodeStartWordList, []string{keyword})
			if err != nil || len(wordResMap) <= 0 {
				tbcontext.WarningF(ctx, "fail to get daily first code start text, err = %v", err)
				return tiebaerror.ERR_WORDLIST_CONN_WORDSERVER_FAIL
			}
			wordRes := wordResMap[keyword]

			arrText := []string{}
			if err = common.StructAToStructBCtx(ctx, wordRes, &arrText); err != nil {
				tbcontext.WarningF(ctx, "fail to modify struct arrText, err = %v", err)
				return tiebaerror.ERROR_MIS_WORDLIST_HOOK_ERROR
			}

			arrTextLen := len(arrText)
			if arrTextLen <= 0 {
				return tiebaerror.ERROR_MIS_WORDLIST_HOOK_ERROR
			}

			// 随机取一个
			r := rand.New(rand.NewSource(time.Now().UnixNano()))
			randNum := r.Intn(arrTextLen)

			text = arrText[randNum]
			text = strings.ReplaceAll(text, "$##$", appellation)
		}
	} else if scene == sceneExposureNoClick {
		// 连续N次曝光无点击，下发固定文案，本接口不判断点击逻辑(使用其他接口)，只下发文案
		speechType = 0
		// // 判断是否认领，未获取到人设认为没认领
		// characterInfo, err := getSpriteCharacterInfo(ctx, spriteUk, uid)
		// if err != nil || characterInfo == nil {
		// 	tbcontext.WarningF(ctx, "fail to getSpriteCharacterInfo, err = %v", err)
		// 	return tiebaerror.ERR_CALL_SERVICE_FAIL
		// }
		// character := characterInfo.GetCharacter()
		// if character == "" {
		wordRes, err := wordserver.QueryKey(ctx, ManualInterventionWordList, ExposureNoClickKey)
		if err != nil || wordRes == "" {
			tbcontext.WarningF(ctx, "fail to get default close claim guide text, err = %v, wordRes = %v", err, wordRes)
			return tiebaerror.ERR_WORDLIST_CONN_WORDSERVER_FAIL
		}
		text = wordRes
		// } else {
		// 	tbcontext.WarningF(ctx, "exposure no click has claim, err req, uid:%v, spriteUk:%v", uid, spriteUk)
		// 	return tiebaerror.ERR_PARAM_ERROR
		// }
	}

	sids := UbsAbtest.GetUbsAbtestSid(ctx, baseData.Request.Common.GetSampleId(), strconv.FormatInt(uid, 10), types.WLUbsAbtestConfigTab)
	// 判断实验号，命中实验被判定为新用户的人，冷启和轮询场景固定从热梗指南库取内容
	if (scene == sceneColdStart || scene == sceneStay) && php2go.InArrayString("12_45_sprite_library_a", sids) {
		speechType = speechTypeBoomerang
	}

	// 需要调用service获取用户发言
	if speechType != 0 {
		info, err := getSpriteChatInfo(ctx, spriteUk, speechType)
		if err != nil {
			tbcontext.WarningF(ctx, "fail to getSpriteChatInfo, err = %v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		// 自言自语、开场白、热梗科普、新手指南类型结构一致
		text = info.GetInfo()
		sendToImText = info.GetInfo()
		relatedList = info.GetRelatedList()

		// 特殊类型特殊处理
		if speechType == speechTypeQA {
			// 有问有答类，需要端手动发送，将外露精灵消息发给中台
			text = info.GetInfo()
			needSend = true
			sendText = info.GetRelatedInfo()
			sendToImText = info.GetInfo()
			relatedList = nil
		} else if speechType == speechTypeOperation {
			// 冷启，有运营配置时，返回给端简短文案，无运营配置时，走冷启库
			text = info.GetInfo()
			newVersion = info.GetVersion()
			// sendToImText = info.GetRelatedInfo()
			if info.GetInfo() == "" || info.GetRelatedInfo() == "" || info.GetVersion() == version {
				// 无运营配置文案，或运营文案已经展示过后，根据新老用户，取不同的冷启时文案

				// 老用户冷启请求冷启库
				speechType = speechTypeColdStart
				if newUser {
					// 新用户冷启请求新手指南库
					speechType = speechTypeNewUser
				}
				info, err := getSpriteChatInfo(ctx, spriteUk, speechType)
				if err != nil {
					tbcontext.WarningF(ctx, "fail to getSpriteChatInfo, err = %v", err)
					return tiebaerror.ERR_CALL_SERVICE_FAIL
				}

				text = info.GetInfo()
				sendToImText = info.GetInfo()
				relatedList = info.GetRelatedList()
			}
		} else if speechType == speechTypeNewUserOperation {
			// 新低用户冷启，有运营配置时，返回。运营没有配置时再请求全局运营配置
			// 有问有答类，需要端手动发送，将外露精灵消息发给中台
			if info.GetInfo() != "" && info.GetRelatedInfo() != "" {
				if info.GetVersion() != version {
					text = info.GetInfo()
					needSend = true
					sendText = info.GetRelatedInfo()
					sendToImText = info.GetInfo()
					relatedList = nil

					newVersion = info.GetVersion()
				} else {
					speechType = speechTypeNewUser
					info, err := getSpriteChatInfo(ctx, spriteUk, speechType)
					if err != nil {
						tbcontext.WarningF(ctx, "fail to getSpriteChatInfo, err = %v", err)
						return tiebaerror.ERR_CALL_SERVICE_FAIL
					}

					text = info.GetInfo()
					sendToImText = info.GetInfo()
					relatedList = info.GetRelatedList()
				}
			} else {
				// 无新低运营配置文案，拿全局运营配置文案
				speechType = speechTypeOperation
				info, err := getSpriteChatInfo(ctx, spriteUk, speechType)
				if err != nil {
					tbcontext.WarningF(ctx, "fail to getSpriteChatInfo, err = %v", err)
					return tiebaerror.ERR_CALL_SERVICE_FAIL
				}

				text = info.GetInfo()
				sendToImText = info.GetInfo()
				relatedList = info.GetRelatedList()

				if info.GetInfo() == "" || info.GetRelatedInfo() == "" || info.GetVersion() == version {
					speechType = speechTypeNewUser
					info, err := getSpriteChatInfo(ctx, spriteUk, speechType)
					if err != nil {
						tbcontext.WarningF(ctx, "fail to getSpriteChatInfo, err = %v", err)
						return tiebaerror.ERR_CALL_SERVICE_FAIL
					}

					text = info.GetInfo()
					sendToImText = info.GetInfo()
					relatedList = info.GetRelatedList()
				}
			}
		}

		// 获取气泡消失时间，所有气泡都生效
		if info.GetDisappearSeconds() > 0 {
			disappearSeconds = info.GetDisappearSeconds()
		} else {
			wordKey := "disapper_sconds_" + strconv.Itoa(int(speechType))
			wordRes, err := wordserver.QueryKey(ctx, ManualInterventionWordList, wordKey)
			if err != nil {
				tbcontext.WarningF(ctx, "fail to get disappear seconds err[%s]", err.Error())
			}
			if wordRes != "" {
				tmp, err := strconv.Atoi(wordRes)
				if err != nil {
					tbcontext.WarningF(ctx, "fail to convert disappear seconds to int, err[%s]", err.Error())
				} else {
					disappearSeconds = int32(tmp)
				}
			}
		}
	}

	// 调service给im中台发消息
	spriteUID, err := uidxuk.UK2UID(spriteUk)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to get uid by uk, err = %v", err)
	}

	// 12.40以上不再调度im发消息能力
	if clientvers.Compare("12.40.0", clientVersion) >= 0 {
		if scene != scenePrologue && scene != sceneGuide {
			sendToImText = ""
		}
	}

	if spriteUID != 0 && uid != 0 && sendToImText != "" {
		sendMsgInput := &chat.SendTextMsgForPersonalChatReq{
			Pa:      proto.String(cast.ToString(spriteUID)),
			Content: proto.String(sendToImText),
			ToUid:   proto.Int64(uid),
			MsgKey:  proto.String(strconv.FormatInt(time.Now().UnixNano(), 16)),
		}
		sendMsgOutput := new(chat.SendTextMsgForPersonalChatRes)
		err = tbservice.Call(ctx, "chat", "sendTextMsgForPersonalChat", sendMsgInput, sendMsgOutput,
			tbservice.WithConverter(tbservice.JSONITER), tbservice.WithTimeout(1500*time.Millisecond))
		if err != nil || sendMsgOutput == nil || sendMsgOutput.GetErrno() != tiebaerror.ERR_SUCCESS {
			// 发送消息失败不返回错误
			tbcontext.WarningF(ctx, "fail to call service chat:sendTextMsgForPersonalChat, err = %v, input = %v, output = %v", err, sendMsgInput, sendMsgOutput)
		}
	}

	response.Data.Scene = proto.Uint32(scene)
	response.Data.Text = proto.String(text)
	if needSend {
		response.Data.NeedSend = proto.Uint32(1)
		response.Data.SendText = proto.String(sendText)
	}

	if newVersion != version && newVersion != "" {
		response.Data.Version = proto.String(newVersion)
	}

	// 将外露文案关联的内部发送文案json化放入sprite_text字段下发给端
	spriteText, err := json.Marshal(relatedList)
	if err != nil {
		tbcontext.WarningF(ctx, "marshal related list fail, err = %v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	if string(spriteText) == "null" {
		spriteText = []byte("")
	}
	response.Data.SpriteText = proto.String(string(spriteText))

	// 根据不同的发言库类型，取发言库对应的配置的气泡
	icon, textColor, btnInfo, err := getSpriteIconBySpeechType(ctx, speechType, clientType)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call getSpriteIconBySpeechType")
		// 取气泡配置失败兜底
		if clientType == clientvers.CLIENT_TYPE_ANDROID {
			icon = &client.ThemeColorInfo{
				Day: &client.ThemeElement{
					PatternImage: proto.String(androidPicDay),
				},
				Dark: &client.ThemeElement{
					PatternImage: proto.String(androidPicDay),
				},
			}
		} else {
			icon = &client.ThemeColorInfo{
				Day: &client.ThemeElement{
					LightColor:   proto.String(iosLightColor),
					PatternImage: proto.String(iosPicDay),
				},
				Dark: &client.ThemeElement{
					LightColor:   proto.String(iosLightColor),
					PatternImage: proto.String(iosPicDay),
				},
			}
		}

		textColor = &client.ThemeColorInfo{
			Day: &client.ThemeElement{
				FontColor: proto.String(commonColorDay),
			},
			Dark: &client.ThemeElement{
				FontColor: proto.String(commonColorDark),
			},
		}
	}
	// 气泡配置下发
	response.Data.IconUrl = icon
	// 文字颜色下发
	response.Data.TextColor = textColor

	if btnInfo != nil {
		// 按钮信息下发
		btnInfo.Schema = proto.String("")
		response.Data.ButtonInfo = btnInfo
	}

	// 发言类型返回给端，映射到端打点类型
	objTypeMap := map[int32]int32{
		speechTypeSelf:          2, // 轻互动 2
		speechTypeQA:            2, // 轻互动 2
		speechTypeOperation:     5, // 其他  5
		speechTypeQuestion:      4, // 其他 5
		speechTypePrologue:      5, // 其他 5
		speechTypeDefaultAnswer: 5, // 其他 5
		speechTypeColdStart:     1, // 冷启 1
		speechTypeBoomerang:     3, // 热梗科普 3
		speechTypeNewUser:       4, // 新手指南 4
	}

	objType, ok := objTypeMap[speechType]
	if !ok {
		objType = 5
	}
	response.Data.SpeechType = proto.Int32(objType)
	response.Data.DisappearSeconds = proto.Int32(disappearSeconds)
	// 展示顺序透传
	response.Data.DisplayOrder = &displayOrder

	if clientvers.Compare(clientVersion, "12.52") <= 0 {
		response.Data.SpriteText = nil
		// 52版本，统一下发新气泡资源
		bubblePic := bubblePicNewIOS
		if clientType == clientvers.CLIENT_TYPE_ANDROID {
			bubblePic = bubblePicNew
		}
		response.Data.IconUrl = &client.ThemeColorInfo{
			Day: &client.ThemeElement{
				LightColor:   proto.String(iosLightColor),
				PatternImage: proto.String(bubblePic),
			},
			Dark: &client.ThemeElement{
				LightColor:   proto.String(iosLightColor),
				PatternImage: proto.String(bubblePic),
			},
		}
		response.Data.TextColor = &client.ThemeColorInfo{
			Day: &client.ThemeElement{
				FontColor: proto.String(commonColorDay),
			},
			Dark: &client.ThemeElement{
				FontColor: proto.String(commonColorDark),
			},
		}
	}

	return tiebaerror.ERR_SUCCESS
}

func getSpriteChatInfo(ctx context.Context, spriteUk string, speechType int32) (*chat.SpriteChatData, error) {
	chatInfoReq := &chat.GetSpriteChatInfoReq{
		Rn:   proto.Int32(1),
		Uk:   proto.String(spriteUk),
		Type: proto.Int32(speechType),
	}

	chatInfoRes := new(chat.GetSpriteChatInfoRes)
	err := tbservice.Call(ctx, "chat", "getSpriteChatInfo", chatInfoReq, chatInfoRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || chatInfoRes == nil || chatInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service chat:getSpriteChatInfo, err = %v, input = %v, output = %v",
			err, common.ToString(chatInfoReq), common.ToString(chatInfoRes))
		return nil, errors.New("fail to call service")
	}
	var info *chat.SpriteChatData
	if len(chatInfoRes.GetData().GetInfoList()) > 0 {
		info = chatInfoRes.GetData().GetInfoList()[0]
	} else if speechType != speechTypeOperation && speechType != speechTypeNewUserOperation {
		// 除了运营配置外，其他情况返回空列表都是不正确的
		tbcontext.FatalF(ctx, "get sprite chat info return empty, input = %v, output = %v", common.ToString(chatInfoReq), common.ToString(chatInfoRes))
		return nil, errors.New("call service return empty")
	}
	return info, nil
}

// 获取用户状态，判断是否新用户，内部出错默认返回false
// 12.42 新版本，使用端传入的字段，老版本请求modata
func isNewUser(ctx context.Context, baseData *types.GetSpriteSpeechBaseData) bool {
	clientVersion := baseData.Request.GetCommon().GetXClientVersion()
	if clientvers.Compare("12.42.0", clientVersion) >= 0 {
		return baseData.Request.GetIsSpriteNewUser() == 1
	}

	subappMap := map[string]int{
		"tieba":  6,
		"mini":   5,
		"wangsu": 4,
	}

	strBrand := baseData.Request.GetCommon().GetBrand()
	strModel := baseData.Request.GetCommon().GetModel()
	clientType := baseData.Request.GetCommon().GetXClientType()

	var brand string
	if clientType == clientvers.CLIENT_TYPE_IPHONE {
		brand = strBrand
	} else if clientType == clientvers.CLIENT_TYPE_ANDROID {
		brand = strModel
	}
	subappType := baseData.Request.GetCommon().GetSubappType()
	subappTypeInt := 0
	if subappMap[subappType] != 0 {
		subappTypeInt = subappMap[subappType]
	} else {
		subappTypeInt = subappMap["tieba"]
	}

	uid, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	input := map[string]any{
		"imei":    baseData.Request.GetCommon().GetXPhoneImei(),
		"cuid":    baseData.Request.GetCommon().GetCuid(),
		"user_id": uid,
		"brand":   brand,
		"subapp":  subappTypeInt,
	}

	var clientInfoRes modata.GetRecentClientInfoRes
	err := tbservice.Call(ctx, "modata", "getRecentClientInfo", input, &clientInfoRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || clientInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service modata:getRecentClientInfo, input = %v, output = %v", common.ToString(input), common.ToString(&clientInfoRes))
		return false
	}

	// 取词表配置的新老用户的判断标准 N天
	wordRes, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_sprite_manual_intervention", "new_user_threshold")
	if err != nil {
		tbcontext.WarningF(ctx, "query wordserver fail, key = new_user_threshold, err = %v", err)
		return false
	}
	newUserThreshold := cast.ToInt64(wordRes)

	// 新用户，且活跃天数小于N天
	now := time.Now().Unix()
	if clientInfoRes.GetIsNewUser() == 1 && now-int64(clientInfoRes.GetExt().GetLastTime()) < newUserThreshold*86400 {
		return true
	}

	return false
}

// 根据新老用户，获取停留场景下的发言类型
func getStaySpeechType(ctx context.Context, data *types.GetSpriteSpeechBaseData) (int32, error) {
	newUser := isNewUser(ctx, data)
	// 根据新老用户，区分读取不同的词表key
	key := StaySpeechTypePercentageKey
	if newUser {
		key = StaySpeechTypePercentageNewUserKey
	}

	// 读词表，取不同类型发言比例的配置
	wordRes, err := wordserver.QueryKey(ctx, ManualInterventionWordList, key)
	if err != nil || wordRes == "" {
		tbcontext.WarningF(ctx, "fail to get default cold start text, err = %v, wordRes = %v", err, wordRes)
		return 0, err
	}

	// 解析词表配置
	typePercentage := map[string]int{}
	err = json.Unmarshal([]byte(wordRes), &typePercentage)
	if err != nil {
		tbcontext.WarningF(ctx, "unmarshal type percentage config error, err = %v", err)
		return 0, err
	}
	// 保证map遍历的一致性
	keys := make([]string, len(typePercentage))
	for t := range typePercentage {
		keys = append(keys, t)
	}
	sort.Strings(keys)

	// 取随机数，看落在配置的哪个区间
	speechType := int32(speechTypeSelf)
	rand := php2go.Rand(0, 99)
	step := 0
	for _, t := range keys {
		percentage := typePercentage[t]
		if rand >= step && rand < step+percentage {
			// 落在配置区间，则发言类型为该配置
			configType := strings.TrimPrefix(t, SpeechTypePercentagePrefix)
			speechType = cast.ToInt32(configType)
			break
		}
		step += percentage
	}
	return speechType, nil
}

// 根据不同的发言类型（语料库）取配置的对应的外露展示气泡
func getSpriteIconBySpeechType(ctx context.Context, speechType int32, clientType int32) (*client.ThemeColorInfo, *client.ThemeColorInfo, *getSpriteSpeech.ButtonInfo, error) {
	key := SpeechTypeIconKeyPrefix + cast.ToString(speechType)
	res, err := wordserver.QueryKey(ctx, ManualInterventionWordList, key)
	if err != nil {
		tbcontext.WarningF(ctx, "getSpriteIconBySpeechType query wordserver fail, key = %v, err = %v", key, err)
		return nil, nil, nil, err
	}

	// 解析词表配置json结构
	iconConfig := IconConfig{}
	err = json.Unmarshal([]byte(res), &iconConfig)
	if err != nil {
		tbcontext.WarningF(ctx, "unmarshal icon info error, err = %v", err)
		return nil, nil, nil, err
	}

	// 气泡样式配置
	icon := &client.ThemeColorInfo{}
	if clientType == clientvers.CLIENT_TYPE_IPHONE {
		icon = &client.ThemeColorInfo{
			Day: &client.ThemeElement{
				LightColor:   proto.String(iconConfig.IosLightColor),
				PatternImage: proto.String(iconConfig.IosPicDay),
			},
			Dark: &client.ThemeElement{
				LightColor:   proto.String(iconConfig.IosLightColor),
				PatternImage: proto.String(iconConfig.IosPicDark),
			},
		}
	} else {
		icon = &client.ThemeColorInfo{
			Day: &client.ThemeElement{
				PatternImage: proto.String(iconConfig.AndroidPicDay),
			},
			Dark: &client.ThemeElement{
				PatternImage: proto.String(iconConfig.AndroidPicDark),
			},
		}
	}

	// 字体颜色配置
	textColor := &client.ThemeColorInfo{
		Day: &client.ThemeElement{
			FontColor: proto.String(iconConfig.TextColorDay),
		},
		Dark: &client.ThemeElement{
			FontColor: proto.String(iconConfig.TextColorDark),
		},
	}

	// 按钮配置
	var btnInfo *getSpriteSpeech.ButtonInfo
	if iconConfig.BtnText != "" {
		btnInfo = &getSpriteSpeech.ButtonInfo{
			Text:   proto.String(iconConfig.BtnText),
			Schema: nil,
		}
	}

	return icon, textColor, btnInfo, nil
}

func getSpriteCharacterInfo(ctx context.Context, spriteUk string, uid int64) (*chat.GetSpriteCharacterData, error) {
	// 获取人设id
	spriteUID, err := uidxuk.UK2UID(spriteUk)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to get uid by uk, err = %v", err)
	}

	robotId := int64(spriteUID)

	charaterInfoReq := &chat.GetSpriteCharacterReq{
		UserId:  &uid,
		RobotId: &robotId,
	}

	charaterInfoRes := new(chat.GetSpriteCharacterRes)
	err = tbservice.Call(ctx, "chat", "getSpriteCharacter", charaterInfoReq, charaterInfoRes, tbservice.WithConverter(tbservice.JSONITER), tbservice.WithTimeout(1500*time.Millisecond))
	if err != nil || charaterInfoRes == nil || charaterInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service chat:getSpriteCharacter, err = %v, input = %v, output = %v",
			err, common.ToString(charaterInfoReq), common.ToString(charaterInfoRes))
		return nil, errors.New("fail to call service")
	}

	var info *chat.GetSpriteCharacterData
	info = charaterInfoRes.GetData()

	return info, nil
}
