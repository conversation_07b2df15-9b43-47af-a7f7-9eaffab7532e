package sprite

import (
	"context"
	"errors"
	"runtime/debug"
	"sync"
	"time"

	"google.golang.org/protobuf/proto"

	aigcProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/aigcprocesser"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	forumProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	qmProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/queryMeme"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func QueryMeme(ctx context.Context, baseData *types.QueryMemeBaseData, response *qmProto.QueryMemeResIdl) int {

	// 前置校验
	errNo := queryMemePreCheck(ctx, baseData)
	if errNo != tiebaerror.ERR_SUCCESS {
		return errNo
	}

	// 首先根据吧ID查询吧名
	forumName := "孙笑川" // 默认吧名兜底
	if baseData.ForumID != 0 {
		forumReq := &forumProto.GetFnameByFidReq{
			ForumId: []uint32{baseData.ForumID},
		}
		forumRes := &forumProto.GetFnameByFidRes{}
		err := tbservice.Call(ctx, "forum", "getFnameByFid", forumReq, forumRes, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || forumRes == nil || forumRes.GetErrno() != tiebaerror.ERR_SUCCESS || forumRes.GetForumName() == nil {
			tbcontext.WarningF(ctx, "QueryMeme call forum::getFnameByFid fail, err=%v, input=%s", err, common.ToString(forumReq))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		forumName = forumRes.GetForumName()[baseData.ForumID].GetForumName()
	}

	// 根据场景进行不同查询，并拼接返回值
	var taskIDPtr *int64
	memeList := make([]*qmProto.AigcMemeInfo, 0, memeTotalRn)
	switch baseData.Scene {
	case sceneClickRecom:
		// 直接点击场景，只查询离线表情包
		queryRes, err := getMemeInfoByQuery(ctx, baseData, forumName)
		if err != nil {
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		if len(queryRes) > 0 {
			memeList = append(memeList, queryRes...)
		}

	case sceneInputQuery:
		if len(baseData.Query) == 0 {
			// 客户端诉求，scene=input_query时，query为空返回空列表
			break
		}

		// 输入查询场景，查询离线表情包，并提交在线生成表情包
		queryRes, err := getMemeInfoByQuery(ctx, baseData, forumName)
		if err != nil {
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		backupInfo := generateBackupInfo(queryRes, &memeList)
		if backupInfo != nil && len(backupInfo) > 0 {
			taskID, memeListRT, err := commitRealTimeMeme(ctx, baseData, forumName, backupInfo)
			if err == nil {
				taskIDPtr = proto.Int64(taskID)
				memeList = append(memeList, memeListRT...)
			} else {
				// 提交在线生成失败时，需要将backup图片补齐
				for _, info := range backupInfo {
					memeList = append(memeList, &qmProto.AigcMemeInfo{
						Id:        proto.Int64(info.GetId()),
						Type:      proto.Uint32(memeTypeNoRealTime),
						Src:       proto.String(info.GetIcon()),
						ObjSource: proto.Uint32(memeSourceNoRealTime),
					})
				}
			}
		}

	case sceneUseSame:
		// 查询同款表情包场景，查询当前表情包，查询离线表情包，并提交在线生成表情包
		wg := sync.WaitGroup{}
		wg.Add(2)

		var curMeme *qmProto.AigcMemeInfo
		memeListNRT := make([]*qmProto.AigcMemeInfo, 0, memeNoRealTimeRn+1)
		go func() {
			defer func() {
				if err := recover(); err != nil {
					tbcontext.FatalF(ctx, "QueryMeme getMemeInfoByID panic. err=%v, stack=%s", err, string(debug.Stack()))
				}
				wg.Done()
			}()
			info, err := getMemeInfoByID(ctx, baseData.MemeID)
			if err == nil && info != nil {
				curMeme = info
			}
		}()

		memeTmpList := make([]*qmProto.AigcMemeInfo, 0, memeNoRealTimeRn)
		go func() {
			defer func() {
				if err := recover(); err != nil {
					tbcontext.FatalF(ctx, "QueryMeme upload image panic. err=%v, stack=%s", err, string(debug.Stack()))
				}
				wg.Done()
			}()
			queryRes, err := getMemeInfoByQuery(ctx, baseData, forumName)
			if err == nil && len(queryRes) > 0 {
				for _, info := range queryRes {
					if info.GetId() == baseData.MemeID {
						continue
					}
					memeTmpList = append(memeTmpList, info)
				}
			}
		}()

		wg.Wait()

		// 将同款表情包与离线表情包进行合并并去重
		if curMeme != nil {
			memeListNRT = append(memeListNRT, curMeme)
		}
		memeListNRT = append(memeListNRT, memeTmpList...)

		// 如果长度超过总长度，需要截断至所需长度
		if len(memeListNRT) > memeTotalRn {
			memeListNRT = memeListNRT[:memeTotalRn]
		}

		if len(baseData.Query) == 0 {
			// 客户端诉求，scene=use_same时，query为空不请求在线生成表情包
			memeList = append(memeList, memeListNRT...)
			break
		}

		backupInfo := generateBackupInfo(memeListNRT, &memeList)
		if backupInfo != nil && len(backupInfo) > 0 {
			taskID, memeListRT, err := commitRealTimeMeme(ctx, baseData, forumName, backupInfo)
			if err == nil {
				taskIDPtr = proto.Int64(taskID)
				memeList = append(memeList, memeListRT...)
			} else {
				// 提交在线生成失败时，需要将backup图片补齐
				for _, info := range backupInfo {
					memeList = append(memeList, &qmProto.AigcMemeInfo{
						Id:        proto.Int64(info.GetId()),
						Type:      proto.Uint32(memeTypeNoRealTime),
						Src:       proto.String(info.GetIcon()),
						ObjSource: proto.Uint32(memeSourceNoRealTime),
					})
				}
			}
		}
	}

	if taskIDPtr != nil {
		response.Data.QueryId = taskIDPtr
	}
	response.Data.MemeList = memeList

	return tiebaerror.ERR_SUCCESS
}

// queryMemePreCheck 前置校验
func queryMemePreCheck(ctx context.Context, baseData *types.QueryMemeBaseData) int {
	objRequest := baseData.BaseObj.ObjRequest

	userID := common.Tvttt(objRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	query := common.Tvttt(objRequest.GetPrivateAttr("query", ""), common.TTT_STRING).(string)
	scene := common.Tvttt(objRequest.GetPrivateAttr("scene", ""), common.TTT_STRING).(string)
	forumID := common.Tvttt(objRequest.GetPrivateAttr("forum_id", 0), common.TTT_UINT32).(uint32)
	pn := common.Tvttt(objRequest.GetPrivateAttr("pn", 0), common.TTT_UINT32).(uint32)
	memeID := common.Tvttt(objRequest.GetPrivateAttr("meme_id", 0), common.TTT_INT64).(int64)

	if userID <= 0 || len(scene) == 0 || pn == 0 ||
		(scene != sceneClickRecom && scene != sceneInputQuery && scene != sceneUseSame) ||
		scene == sceneUseSame && memeID <= 0 {
		tbcontext.WarningF(ctx, "queryMemePreCheck input invalid")
		return tiebaerror.ERR_PARAM_ERROR
	}

	baseData.UserID = userID
	baseData.Query = query
	baseData.Scene = scene
	baseData.ForumID = forumID
	baseData.Pn = pn
	baseData.MemeID = memeID
	return tiebaerror.ERR_SUCCESS
}

// getMemeInfoByID 根据表情包ID查询离线表情包信息
func getMemeInfoByID(ctx context.Context, memeID int64) (*qmProto.AigcMemeInfo, error) {
	memeInfoReq := &commonProto.MgetAigcMemeByIDReq{
		AigcMemeIds: []int64{memeID},
	}
	memeInfoRes := &commonProto.MgetAigcMemeByIDRes{}
	memeInfoOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("common_go"),
	}
	err := tbservice.Call(ctx, "common", "mgetAigcMemeByID", memeInfoReq, memeInfoRes, memeInfoOption...)
	if err != nil || memeInfoRes == nil || memeInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "getMemeInfoByID call common::mgetAigcMemeByID fail, err=%v, input=%s, output=%s", err, common.ToString(memeInfoReq), common.ToString(memeInfoRes))
		return nil, errors.New("call common::mgetAigcMemeByID fail")
	}

	memeInfo, ok := memeInfoRes.GetData()[memeID]
	if !ok {
		return nil, nil
	}
	data := &qmProto.AigcMemeInfo{
		Id:        proto.Int64(memeID),
		Type:      proto.Uint32(memeTypeNoRealTime),
		Src:       proto.String(memeInfo.GetIcon()),
		ObjSource: proto.Uint32(memeSourceNoRealTime),
	}
	return data, nil
}

// getMemeInfoByQuery 根据query查询离线表情包信息
func getMemeInfoByQuery(ctx context.Context, baseData *types.QueryMemeBaseData, forumName string) ([]*qmProto.AigcMemeInfo, error) {
	memeInfoReq := &aigcProto.QueryAigcMemeInfoReq{
		UserId:    proto.Int64(baseData.UserID),
		ForumName: proto.String(forumName),
		Content:   proto.String(baseData.Query),
		Pn:        proto.Uint32(baseData.Pn),
		Rn:        proto.Uint32(memeNoRealTimeRn),
	}
	memeInfoRes := &aigcProto.QueryAigcMemeInfoRes{}
	err := tbservice.Call(ctx, "aigc_processer", "queryAigcMemeInfo", memeInfoReq, memeInfoRes, tbservice.WithConverter(tbservice.JSONITER), tbservice.WithTimeout(1500*time.Millisecond))
	if err != nil || memeInfoRes == nil || memeInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "getMemeInfoByQuery call aigc_processer::queryAigcMemeInfo fail, err=%v, input=%s, output=%s", err, common.ToString(memeInfoReq), common.ToString(memeInfoRes))
		return nil, errors.New("call aigc_processer::queryAigcMemeInfo fail")
	}

	memeInfo := memeInfoRes.GetData().GetMemeInfo()
	data := make([]*qmProto.AigcMemeInfo, 0, len(memeInfo))
	for _, info := range memeInfo {
		data = append(data, &qmProto.AigcMemeInfo{
			Id:        proto.Int64(info.GetId()),
			Type:      proto.Uint32(memeTypeNoRealTime),
			Src:       proto.String(info.GetUrl()),
			ObjSource: proto.Uint32(memeSourceNoRealTime),
		})
	}
	return data, nil
}

// commitRealTimeMeme 提交实时生成表情包任务
func commitRealTimeMeme(ctx context.Context, baseData *types.QueryMemeBaseData, forumName string, backupInfo []*commonProto.AigcMemeInfo) (
	int64, []*qmProto.AigcMemeInfo, error) {
	commitReq := &commonProto.GenerateAigcMemeReq{
		UserId:    proto.Int64(baseData.UserID),
		ForumName: proto.String(forumName),
		Content:   proto.String(baseData.Query),
		ImgNum:    proto.Uint32(memeRealTimeRn),
		Backup: &commonProto.GenerateAigcMemeBackup{
			Meme: backupInfo,
		},
	}
	commitRes := &commonProto.GenerateAigcMemeRes{}
	commitOption := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("common_go"),
	}
	err := tbservice.Call(ctx, "common", "generateAigcMeme", commitReq, commitRes, commitOption...)
	if err != nil || commitRes == nil || commitRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "commitRealTimeMeme call common::generateAigcMeme fail, err=%v, input=%s, output=%s", err, common.ToString(commitReq), common.ToString(commitRes))
		return 0, nil, errors.New("call common::generateAigcMeme fail")
	}

	taskID := commitRes.GetData().GetTaskId()
	data := make([]*qmProto.AigcMemeInfo, 0, memeRealTimeRn)
	for i := 0; i < memeRealTimeRn; i++ {
		data = append(data, &qmProto.AigcMemeInfo{
			Type: proto.Uint32(memeTypeRealTime), // 此处向客户端下发占位字段
		})
	}
	return taskID, data, nil
}

// generateBackupInfo 生成backup信息
//
//	oriMemeList 暂存的表情包列表
//	destMemeList 最终返回的表情包列表
func generateBackupInfo(oriMemeList []*qmProto.AigcMemeInfo, destMemeList *[]*qmProto.AigcMemeInfo) []*commonProto.AigcMemeInfo {
	if len(oriMemeList) == 0 {
		return nil
	}
	backupInfo := make([]*commonProto.AigcMemeInfo, 0, memeRealTimeRn)

	// 数量不满足时直接返回
	if len(oriMemeList) < memeNoRealTimeRn {
		*destMemeList = append(*destMemeList, oriMemeList...)
		return nil
	}

	// 只有数量符合时才进行backup信息生成
	*destMemeList = append(*destMemeList, oriMemeList[:len(oriMemeList)-memeRealTimeRn]...)
	for _, info := range oriMemeList[len(oriMemeList)-memeRealTimeRn:] {
		backupInfo = append(backupInfo, &commonProto.AigcMemeInfo{
			Id:   proto.Int64(info.GetId()),
			Icon: proto.String(info.GetSrc()),
		})
	}
	return backupInfo
}
