package sprite

/*
 * @Author: ch<PERSON><PERSON><PERSON><PERSON> cheng<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2023-06-21 16:04:27
 * @LastEditors: cheng<PERSON><PERSON><PERSON> chengji<PERSON><EMAIL>
 * @LastEditTime: 2023-08-11 16:39:19
 * @FilePath: /go-client-forum/service/sprite/getSpriteBottomChat.go
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import (
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/UbsAbtest"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/chat"
	commonproto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/modata"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/sprite/getSpriteBottomChat"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	defaultRn = 3 // 默认返回条数
)

func GetSpriteBottomChat(ctx context.Context, baseData *types.GetSpriteBottomChatBaseData,
	response *getSpriteBottomChat.GetSpriteBottomChatResIdl) int {

	query := baseData.Request.GetQuery()
	isNewuser := baseData.Request.GetIsSpriteNewUser()
	query = strings.TrimSpace(query)
	rn := baseData.Request.GetRn()
	uk := baseData.Request.GetUk()
	fid := baseData.Request.GetForumId()
	uid, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)

	if uk == "" {
		tbcontext.WarningF(ctx, "GetSpriteBottomChat input param error, uk[%v]", uk)
		return tiebaerror.ERR_PARAM_ERROR
	}

	if rn <= 0 {
		rn = defaultRn
	}

	// 如果端上没有传入这个参数，为-1的时候手动查一下，非0和1的时候直接使用
	strIsNewuser := baseData.BaseObj.GetReqParamFunc("is_sprite_new_user", "-1")
	sids := UbsAbtest.GetUbsAbtestSid(ctx, baseData.Request.Common.GetSampleId(), strconv.FormatInt(uid, 10), types.WLUbsAbtestConfigTab)

	if strIsNewuser == "-1" {
		if getIsNewUser(ctx, uid, sids) {
			tbcontext.AddNotice(ctx, "hit_newuser", "1")
			isNewuser = 1
		}
	}

	arrQuestionList := make([]*getSpriteBottomChat.QuestionInfo, 0)
	chatInfoReq := &chat.GetSpriteBottomChatReq{
		Rn:        proto.Int32(rn),
		Uk:        proto.String(uk),
		Query:     proto.String(query),
		Fid:       proto.Int64(int64(fid)),
		IsNewuser: proto.Int32(int32(isNewuser)),
	}

	chatInfoRes := new(chat.GetSpriteBottomChatRes)
	err := tbservice.Call(ctx, "chat", "getSpriteBottomChat", chatInfoReq, chatInfoRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || chatInfoRes == nil || chatInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call service chat:getSpriteBottomChat, err = %v, input = %v, output = %v",
			err, common.ToString(chatInfoReq), common.ToString(chatInfoRes))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	if chatInfoRes.Data == nil || chatInfoRes.Data.QuestionList == nil || len(chatInfoRes.Data.QuestionList) == 0 {
		return tiebaerror.ERR_SUCCESS
	}

	for _, value := range chatInfoRes.Data.QuestionList {
		arrQuestionData := getSpriteBottomChat.QuestionInfo{
			Question:   value.Question,
			QuestionBg: value.QuestionBg,
			TextColor:  value.TextColor,
		}
		arrQuestionList = append(arrQuestionList, &arrQuestionData)
	}

	response.Data.QuestionList = arrQuestionList

	return tiebaerror.ERR_SUCCESS
}

/**
 * 查询一个用户是否是新用户和低活用户
 */
func getIsNewUser(ctx context.Context, uid int64, sids []string) bool {
	multi := tbservice.Multi()

	commonParam := &tbservice.Parameter{
		Service: "common",
		Method:  "getUserActivity",
		Input: map[string]interface{}{
			"uid": uid,
		},
		Output: &commonproto.GetUserActivityRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getUserActivity", commonParam)

	modataParam := &tbservice.Parameter{
		Service: "modata",
		Method:  "getRecentClientInfo",
		Input: map[string]interface{}{
			"user_id": uid,
		},
		Output: &modata.GetRecentClientInfoRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getRecentClientInfo", modataParam)

	multi.Call(ctx)

	isNewUser := false

	modataOut, err := multi.GetResult(ctx, "getRecentClientInfo")
	if err != nil {
		tbcontext.WarningF(ctx, "call modata:getRecentClientInfo failed! input[%v] err[%s]", modataParam, err.Error())
	} else {
		modataRes, ok := modataOut.(*modata.GetRecentClientInfoRes)
		// tbcontext.NoticeF(ctx, "modata:getRecentClientInfo out[%v]", modataRes)
		// 取词表配置的新老用户的判断标准 N天
		wordRes, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_sprite_manual_intervention", "new_user_threshold")
		if err != nil {
			tbcontext.WarningF(ctx, "query wordserver fail, key = new_user_threshold, err = %v", err)
		}
		newUserThreshold := cast.ToInt64(wordRes)
		if !ok {
			tbcontext.WarningF(ctx, "convert format to modata.GetRecentClientInfoRes failed!{} input[%v]", modataOut)
		} else {
			lastTime := modataRes.GetExt().GetLastTime()
			if time.Now().Unix()-int64(lastTime) <= newUserThreshold*86400 {
				isNewUser = true
			}
		}
	}

	var actScore int32
	commonOut, err := multi.GetResult(ctx, "getUserActivity")
	if err != nil {
		tbcontext.WarningF(ctx, "call common:getUserActivity failed! input[%v] err[%s]", commonParam, err.Error())
	} else {
		commonRes, ok := commonOut.(*commonproto.GetUserActivityRes)
		// tbcontext.NoticeF(ctx, "common:getUserActivity out[%v]", commonRes)
		if !ok {
			tbcontext.WarningF(ctx, "convert format to commonproto.GetUserActivityRes{} failed! input[%v]", commonOut)
		} else {
			actScore = commonRes.GetOutput().GetActScore()
		}
	}

	/*
	* 实验一：新低用户独立实验——观察新低用户实验数据是否有差异，验证猜测
	* 实验组一：新标准下的新用户可见新用户landing相关功能
	* 实验组二：低活用户（active=0&1） 可见新用户landing相关功能
	* 对照组：没有用户可见landing相关功能
	* 实验二：覆盖中高活用户实验——观察高活用户实验数据与新低的差异
	* 实验组一：实验组内的中活用户按新低用户处理，能看到线上新用户landing相关功能
	* 实验组二：实验组内的高活用户按新低用户处理，能看到线上新用户landing相关功能
	* 对照组：没有用户可见landing相关功能
	 */
	return (isNewUser && php2go.InArrayString("12_45_sprite_new_user_a", sids)) || (actScore <= 1 && php2go.InArrayString("12_45_sprite_new_user_b", sids)) ||
		(actScore == 2 && php2go.InArrayString("12_45_sprite_high_activity_a", sids)) || (actScore == 3 && php2go.InArrayString("12_45_sprite_high_activity_b", sids))
}
