package drawthread

import (
	"context"
	"encoding/json"
	"google.golang.org/protobuf/proto"
	quizProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/quiz"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	drawthreadProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/drawthread"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	PnDefault = 1
	RnDefault = 10
)

// MyDrawList 执行myDrawList的逻辑
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：baseData：输入数据
// @Param：response：输出数据
// @Return：返回值是int类型，表示执行是否成功
func MyDrawList(ctx context.Context, baseData *types.MyDrawListBaseData, response *drawthreadProto.MyDrawListResIdl) int {
	// 获取用户ID
	userID := baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0).(int64)
	if userID <= 0 {
		tbcontext.WarningF(ctx, "input param user_id is invalid. userid=%d", userID)
		return tiebaerror.ERR_PARAM_ERROR
	}
	pn := baseData.Request.GetPn()
	rn := baseData.Request.GetRn()
	if pn == 0 {
		pn = PnDefault
	}
	if rn == 0 {
		rn = RnDefault
	}
	// 定义输入的proto
	myDrawListReq := &quizProto.MyDrawListReq{
		UserId: proto.Uint64(uint64(userID)),
		Pn:     proto.Uint32(pn),
		Rn:     proto.Uint32(rn),
	}

	// 定义输出的proto
	myDrawListRes := &quizProto.MyDrawListRes{}

	// 调用下游接口服务交互，调用Call函数实现
	err := tbservice.Call(ctx, "quiz", "myDrawList", myDrawListReq, myDrawListRes,
		tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || myDrawListRes == nil || myDrawListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call quiz::myDrawList fail. input=%s , err = %v", common.ToString(myDrawListReq), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	if myDrawListRes.GetData() == nil || len(myDrawListRes.GetData().GetDrawList()) == 0 {
		tbcontext.WarningF(ctx, "myDrawList data is null. userid=%d", userID)
		return tiebaerror.ERR_SUCCESS
	}
	// 将下游接口返回的数据赋值给response
	response.Data = &drawthreadProto.MyDrawListDataRes{}
	response.Data.HasMore = proto.Uint32(myDrawListRes.GetData().GetHasMore())
	response.Data.TotalNum = proto.Uint32(myDrawListRes.GetData().GetTotalNum())

	// 获取用户状态信息
	var uIDs []uint64
	for _, v := range myDrawListRes.GetData().GetDrawList() {
		uIDs = append(uIDs, v.GetThreadInfo().GetAuthor().GetUserId())
	}

	userStateMap := make(map[int64]*user.UserState, 0)
	if len(uIDs) > 0 {
		// 获取用户状态信息
		userStateInput := map[string]interface{}{
			"uids": uIDs,
		}
		userStateOutput := &user.MgetUserStateFromPassByUidsRes{}
		err = tbservice.Call(ctx, "user", "mgetUserStateFromPassByUid", userStateInput, userStateOutput, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || userStateOutput.GetErrno() != 0 {
			tbcontext.WarningF(ctx, "call user::mgetUserStateFromPassByUid fail. input=%s ,ouput=%s, err = %v",
				common.ToString(userStateInput), common.ToString(userStateOutput), err)
		}
		userStateMap = userStateOutput.GetData()
	}

	for _, v := range myDrawListRes.GetData().GetDrawList() {
		if v == nil {
			continue
		}
		if v.GetThreadInfo() == nil || v.GetPrizeInfo() == nil {
			tbcontext.WarningF(ctx, "myDrawInfo is empty. userid=%d, data=%s", userID, common.ToString(v))
			continue
		}

		userState := 1
		if userStateMap[int64(v.GetThreadInfo().GetAuthor().GetUserId())].GetUserState() == 2 {
			userState = 2
		}

		response.Data.DrawList = append(response.Data.DrawList, &drawthreadProto.MyDrawInfo{
			ThreadInfo: &drawthreadProto.MyDrawThreadInfo{
				Tid:   proto.Uint64(v.GetThreadInfo().GetTid()),
				Title: proto.String(v.GetThreadInfo().GetTitle()),
				Author: &drawthreadProto.MyDrawAuthorInfo{
					UserState:  proto.Int32(int32(userState)),
					UserEncode: proto.String(v.GetThreadInfo().GetAuthor().GetUserEncode()),
					Portrait:   proto.String(v.GetThreadInfo().GetAuthor().GetPortrait()),
					Nickname:   proto.String(v.GetThreadInfo().GetAuthor().GetNickname()),
					NameShow:   proto.String(v.GetThreadInfo().GetAuthor().GetNameShow()),
					UserName:   proto.String(v.GetThreadInfo().GetAuthor().GetUserName()),
				},
			},
			PrizeInfo: &drawthreadProto.MyDrawPrizeInfo{
				Id:               proto.Uint64(v.GetPrizeInfo().GetId()),
				Pic:              proto.String(v.GetPrizeInfo().GetPic()),
				Name:             proto.String(v.GetPrizeInfo().GetName()),
				LotteryValidTime: proto.Uint32(v.GetPrizeInfo().GetLotteryValidTime()),
			},
		})
	}
	response.Data.OfficialNotice = getOfficialNotice(ctx)
	tbcontext.WarningF(ctx, "response data. userid=%d, data=%s", userID, common.ToString(response))
	return tiebaerror.ERR_SUCCESS
}

func getOfficialNotice(ctx context.Context) []*drawthreadProto.OfficialNotice {
	returnData := make([]*drawthreadProto.OfficialNotice, 0)
	desc, err := wordserver.QueryKey(ctx, types.DrawThreadRedisKey, "draw_my_list_official_notice")
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to query my_draw_list_official_notice from wordserver: %v", err)
		return returnData
	}
	if err := json.Unmarshal([]byte(desc), &returnData); err != nil {
		tbcontext.WarningF(ctx, "Failed to unmarshal getOfficialNotice: %v", err)
		return returnData
	}
	return returnData
}
