package drawthread

import (
	"context"
	"encoding/json"
	"fmt"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"strconv"

	frsProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	quizProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/quiz"
	userProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/threadtype"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	dtProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/drawthread"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// Publicity 执行publicity的逻辑
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：baseData：输入数据
// @Param：response：输出数据
// @Return：返回值是int类型，表示执行是否成功
func Publicity(ctx context.Context, baseData *types.DrawThreadPublicityBaseData, response *dtProto.PublicityResIdl) int {
	// 获取公参
	objRequest := baseData.BaseObj.ObjRequest
	userID := common.Tvttt(objRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	login := common.Tvttt(objRequest.GetCommonAttr("login", ""), common.TTT_BOOL).(bool)

	// 校验登录态
	if !login || userID <= 0 {
		return tiebaerror.ERR_NYZQ_NEEDLOGIN
	}

	// 获取帖子信息
	threadID := baseData.Request.GetThreadId()
	multi := tbservice.Multi()
	mgetThreadReq := &tbservice.Parameter{
		Service: "post",
		Method:  "mgetThread",
		Input: map[string]any{
			"thread_ids":        []uint64{threadID},
			"need_abstract":     1,
			"need_photo_pic":    0,
			"need_forum_name":   0,
			"call_from":         "client_frs",
			"need_mask_info":    0,
			"need_post_content": 0,
			"need_user_data":    1,
		},
		Output: &frsProto.MgetThreadRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetThread", mgetThreadReq)
	multi.Call(ctx)

	mgetThreadInter, err := multi.GetResult(ctx, "mgetThread")

	mgetThreadRes := mgetThreadInter.(*frsProto.MgetThreadRes)
	if err != nil || mgetThreadRes.Errno == nil || mgetThreadRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call post:mgetThread, input = %v, err = %v, resp = %v", threadID, err, mgetThreadInter)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 获取帖子信息
	drawEntityID := int32(0)
	threadInfo := mgetThreadRes.GetOutput().GetThreadList()[threadID]
	if threadInfo == nil {
		tbcontext.WarningF(ctx, "fail to get threadInfo. thread_id = %d", threadID)
		return tiebaerror.ERR_SUCCESS
	}
	ThreadTypes := threadtype.GetThreadType(threadInfo.GetThreadTypes())
	if ThreadTypes["is_draw"] {
		// 抽奖贴处理
		infoStruct, _ := threadInfo.GetDrawInfoStruct()
		if infoStruct.GetDrawId() <= 0 {
			tbcontext.WarningF(ctx, "Thread not have draw info")
			return tiebaerror.ERR_SUCCESS
		}
		drawEntityID = int32(infoStruct.GetDrawId())
	} else {
		tbcontext.WarningF(ctx, "Thread is not draw. thread_id = %d,ThreadTypes = %s", threadID, common.ToString(ThreadTypes))
		return tiebaerror.ERR_SUCCESS
	}

	// 获取中奖记录
	getDrawThreadPublicityReq := &quizProto.GetDrawThreadPublicityReq{
		DrawEntityId: proto.Int32(drawEntityID),
		UserId:       proto.Uint64(uint64(userID)),
	}
	getDrawThreadPublicityRes := &quizProto.GetDrawThreadPublicityRes{}
	err = tbservice.Call(ctx, "quiz", "getDrawThreadPublicity", getDrawThreadPublicityReq, getDrawThreadPublicityRes)
	if err != nil || getDrawThreadPublicityRes.GetErrno() != 0 {
		tbcontext.WarningF(ctx, "call getDrawThreadPublicity fail. input=%s , err = %v", common.ToString(getDrawThreadPublicityReq), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	// 构建返回数据
	userPrizeInfo := getDrawThreadPublicityRes.GetData().GetUserPrize()
	response.Data = &dtProto.PublicityResData{
		UserPrize: &dtProto.UserPrize{
			PrizeInfo: &dtProto.PrizeInfo{
				PrizeName: proto.String(userPrizeInfo.GetPrizeInfo().GetPrizeName()),
				Pic:       proto.String(userPrizeInfo.GetPrizeInfo().GetPic()),
				TiebeiNum: proto.Int32(userPrizeInfo.GetPrizeInfo().GetTiebeiNum()),
			},
			PrizeType: proto.Uint32(getDrawThreadPublicityRes.GetData().GetUserPrize().GetPrizeType()),
		},
	}

	for _, drawRecordList := range getDrawThreadPublicityRes.GetData().GetDrawRecordList() {
		drawRecord := &dtProto.DrawRecordList{}
		drawRecord.PrizeName = proto.String(drawRecordList.GetPrizeName())
		drawRecord.UserList = make([]*dtProto.UserInfo, 0)
		drawRecord.PrizeInfo = &dtProto.DrawPrizeInfo{}
		if drawRecordList.GetPrizeInfo() != nil {
			drawRecord.PrizeInfo = &dtProto.DrawPrizeInfo{
				Id:           proto.Uint64(drawRecordList.GetPrizeInfo().GetId()),
				DrawEntityId: proto.Uint64(drawRecordList.GetPrizeInfo().GetDrawEntityId()),
				Name:         proto.String(drawRecordList.GetPrizeInfo().GetName()),
				Pic:          proto.Int64(drawRecordList.GetPrizeInfo().GetPic()),
				Count:        proto.Int32(drawRecordList.GetPrizeInfo().GetCount()),
				Asset:        proto.String(drawRecordList.GetPrizeInfo().GetAsset()),
				AuditStatus:  proto.Int32(drawRecordList.GetPrizeInfo().GetAuditStatus()),
				AuditReason:  proto.String(drawRecordList.GetPrizeInfo().GetAuditReason()),
				CreateTime:   proto.Uint32(drawRecordList.GetPrizeInfo().GetCreateTime()),
				UpdateTime:   proto.Uint32(drawRecordList.GetPrizeInfo().GetUpdateTime()),
			}
		}
		if drawRecordList.GetUserList() != nil {
			userInfos := drawRecordList.GetUserList()
			for _, userInfo := range userInfos {
				user := &dtProto.UserInfo{
					Portrait: proto.String(userInfo.GetPortrait()),
					Nickname: proto.String(userInfo.GetNickname()),
				}
				drawRecord.UserList = append(drawRecord.UserList, user)
			}
		}
		response.Data.DrawRecordList = append(response.Data.GetDrawRecordList(), drawRecord)
	}

	// 获取用户状态信息
	userStateInput := map[string]interface{}{
		"uids": []int64{threadInfo.GetUserId()},
	}
	userStateOutput := &user.MgetUserStateFromPassByUidsRes{}
	err = tbservice.Call(ctx, "user", "mgetUserStateFromPassByUid", userStateInput, userStateOutput, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || userStateOutput.GetErrno() != 0 {
		tbcontext.WarningF(ctx, "call user::mgetUserStateFromPassByUid fail. input=%s ,ouput=%s, err = %v",
			common.ToString(userStateInput), common.ToString(userStateOutput), err)
	}
	userState := 1
	if len(userStateOutput.GetData()) > 0 {
		if userStateOutput.GetData()[threadInfo.GetUserId()].GetUserState() == 2 {
			userState = 2
		}
	}

	response.Data.DrawInfo = &dtProto.DrawEntity{
		Id:               proto.Uint64(getDrawThreadPublicityRes.GetData().GetDrawInfo().GetId()),
		BusinessId:       proto.Uint64(getDrawThreadPublicityRes.GetData().GetDrawInfo().GetBusinessId()),
		OpenTime:         proto.Uint32(getDrawThreadPublicityRes.GetData().GetDrawInfo().GetOpenTime()),
		OpenStatus:       proto.Uint32(getDrawThreadPublicityRes.GetData().GetDrawInfo().GetOpenStatus()),
		CreateTime:       proto.Uint32(getDrawThreadPublicityRes.GetData().GetDrawInfo().GetCreateTime()),
		UpdateTime:       proto.Uint32(getDrawThreadPublicityRes.GetData().GetDrawInfo().GetUpdateTime()),
		LotteryValidTime: proto.Uint32(getDrawThreadPublicityRes.GetData().GetDrawInfo().GetLotteryValidTime()),
		LotteryType:      proto.Uint32(getDrawThreadPublicityRes.GetData().GetDrawInfo().GetLotteryType()),
	}

	userInfo := mgetThreadRes.GetOutput().GetThreadUserList()[threadInfo.GetUserId()]
	portraitID := tbportrait.Encode(userInfo.GetUserId(), userInfo.GetUserName(), 0)
	portraitStr := fmt.Sprintf("http://tb.himg.baidu.com/sys/portrait/item/%s.jpg", portraitID)

	response.Data.ThreadAuthor = &dtProto.AuthorInfo{
		UserState:  proto.Int32(int32(userState)),
		Portrait:   proto.String(portraitStr),
		Nickname:   proto.String(userInfo.GetUserNicknameV2()),
		NameShow:   proto.String(GetUserNameShow(userInfo)),
		UserName:   proto.String(userInfo.GetUserName()),
		UserEncode: proto.String(php2go.Base64Encode(strconv.FormatInt(userInfo.GetUserId(), 10))),
	}
	response.Data.DrawActDesc = getActDesc(ctx, drawEntityID)

	return tiebaerror.ERR_SUCCESS
}
func GetUserNameShow(userInfo *userProto.UserInfo) string {
	if userInfo == nil {
		return ""
	}

	nickNameV2 := userInfo.GetUserNicknameV2()
	if nickNameV2 != "" {
		return nickNameV2
	}

	nickName := userInfo.GetUserNickname()
	if nickName != "" {
		return nickName
	}

	userName := userInfo.GetUserName()
	return userName
}

func getActDesc(ctx context.Context, drawEntityID int32) []*dtProto.DrawActDesc {
	returnData := make([]*dtProto.DrawActDesc, 0)
	desc, err := wordserver.QueryKey(ctx, types.DrawThreadRedisKey, "draw_act_desc")
	if err != nil {
		tbcontext.WarningF(ctx, "Failed to query draw act desc from wordserver: %v", err)
		return returnData
	}
	if err := json.Unmarshal([]byte(desc), &returnData); err != nil {
		tbcontext.WarningF(ctx, "Failed to unmarshal BlackUserConfig: %v", err)
		return returnData
	}
	return returnData
}
