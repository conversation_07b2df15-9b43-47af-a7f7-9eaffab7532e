package drawthread

import (
	"context"
	"encoding/base64"
	"fmt"
	"strconv"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	quizProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/quiz"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/threadtype"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	dtProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/drawthread"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

const (
	RedisKeyPopupHistory = "draw_thread_user_lottery_popup_"
)

// GetLotteryResult 实现业务逻辑
// @Param：ctx：上下文对象，用于控制函数执行的上下文环境
// @Param：baseData：输入数据
// @Param：response：输出数据
// @Return：int：错误码
func GetLotteryResult(ctx context.Context, baseData *types.GetLotteryResultBaseData, response *dtProto.GetLotteryResultResIdl) int {
	// 业务逻辑实现
	obj := baseData.BaseObj
	if obj == nil {
		return tiebaerror.ERR_PARAM_ERROR
	}
	userID, _ := common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	// userID = 173924562911
	tid := baseData.Request.GetThreadId()
	// tid = 8852776702

	// 判断redis key
	key := RedisKeyPopupHistory + strconv.FormatUint(tid, 10) + "_" + strconv.FormatInt(userID, 10)
	exists, _ := resource.RedisCommonb.Exists(ctx, key).Result()
	if exists == 1 {
		tbcontext.WarningF(ctx, "GetLotteryResult key:%s exists", key)
		return tiebaerror.ERR_PARAM_ERROR
	}

	conf := getConf(ctx)
	if conf == nil || conf.Hit == nil || conf.UnHit == nil || conf.HitTMoney == nil {
		tbcontext.WarningF(ctx, "GetLotteryResult conf is nil")
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	if tid == 0 || userID <= 0 {
		tbcontext.WarningF(ctx, "GetLotteryResult tid:%d userID:%d", tid, userID)
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 查帖子信息

	multi := tbservice.Multi()
	mgetThreadReq := &tbservice.Parameter{
		Service: "post",
		Method:  "mgetThread",
		Input: map[string]any{
			"thread_ids":        []uint64{tid},
			"need_abstract":     1,
			"need_photo_pic":    0,
			"need_forum_name":   0,
			"call_from":         "client_frs",
			"need_mask_info":    0,
			"need_post_content": 0,
			"need_user_data":    1,
		},
		Output: &frs.MgetThreadRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "mgetThread", mgetThreadReq)
	multi.Call(ctx)

	mgetThreadInter, err := multi.GetResult(ctx, "mgetThread")
	if err != nil {
		tbcontext.WarningF(ctx, "fail to call post:mgetThread, input = %v, err = %v", tid, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	mgetThreadRes, _ := mgetThreadInter.(*frs.MgetThreadRes)
	if mgetThreadRes.Errno == nil || mgetThreadRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "fail to call post:mgetThread, input = %v, err = %v, resp = %v", tid, err, mgetThreadInter)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	drawEntityID := 0
	threadInfo := mgetThreadRes.GetOutput().GetThreadList()[tid]
	if threadInfo == nil {
		tbcontext.WarningF(ctx, "fail to get threadInfo. thread_id = %d", tid)
		return tiebaerror.ERR_SUCCESS
	}

	ThreadTypes := threadtype.GetThreadType(threadInfo.GetThreadTypes())
	if ThreadTypes["is_draw"] {
		// 抽奖贴处理
		infoStruct, _ := threadInfo.GetDrawInfoStruct()
		if infoStruct.GetDrawId() <= 0 {
			tbcontext.WarningF(ctx, "Thread not have draw info")
			return tiebaerror.ERR_SUCCESS
		}
		drawEntityID = int(infoStruct.GetDrawId())
	} else {
		tbcontext.WarningF(ctx, "Thread is not draw. thread_id = %d,ThreadTypes = %s", tid, common.ToString(ThreadTypes))
		return tiebaerror.ERR_SUCCESS
	}

	multiUser := tbservice.Multi()
	getDrawUserRecordByCondReq := &tbservice.Parameter{
		Service: "quiz",
		Method:  "getDrawUserRecordByUID",
		Input: &quizProto.GetDrawUserRecordByUidReq{
			DrawEntityId: proto.Uint64(uint64(drawEntityID)),
			UserId:       proto.Uint64(uint64(userID)),
		},
		Output: &quizProto.GetDrawUserRecordByUidRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multiUser.Register(ctx, "getDrawUserRecordByUID", getDrawUserRecordByCondReq)

	userStateInputReq := &tbservice.Parameter{
		Service: "user",
		Method:  "mgetUserStateFromPassByUid",
		Input: map[string]interface{}{
			"uids": []int64{threadInfo.GetUserId()},
		},
		Output: &user.MgetUserStateFromPassByUidsRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multiUser.Register(ctx, "mgetUserStateFromPassByUid", userStateInputReq)
	multiUser.Call(ctx)

	getDrawUserRecordByCondResInter, err := multiUser.GetResult(ctx, "getDrawUserRecordByUID")
	getDrawUserRecordByCondRes := getDrawUserRecordByCondResInter.(*quizProto.GetDrawUserRecordByUidRes)
	tbcontext.NoticeF(ctx, "getDrawUserRecordByUID = %s", common.ToString(getDrawUserRecordByCondRes))
	if err != nil || getDrawUserRecordByCondRes.GetErrno() != 0 {
		tbcontext.WarningF(ctx, "call getDrawThreadPublicity fail. input=%s , err = %v", common.ToString(getDrawUserRecordByCondReq), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	userStateOutputInter, err := multiUser.GetResult(ctx, "mgetUserStateFromPassByUid")
	userStateOutput := userStateOutputInter.(*user.MgetUserStateFromPassByUidsRes)
	tbcontext.NoticeF(ctx, "mgetUserStateFromPassByUid = %s", common.ToString(userStateOutput))
	if err != nil || userStateOutput.GetErrno() != 0 {
		tbcontext.WarningF(ctx, "call user::mgetUserStateFromPassByUid fail. ouput=%s, err = %v", common.ToString(userStateOutput), err)
	}

	author := mgetThreadRes.GetOutput().GetThreadUserList()[threadInfo.GetUserId()]

	userState := 1
	if len(userStateOutput.GetData()) > 0 {
		if userStateOutput.GetData()[threadInfo.GetUserId()].GetUserState() == 2 {
			userState = 2
		}
	}

	// 发帖人信息
	authorInfo := &dtProto.GetLotteryAuthorInfo{
		UserState:  proto.Int32(int32(userState)),
		Portrait:   proto.String(tbportrait.Encode(author.GetUserId(), "", 0)),
		Nickname:   proto.String(author.GetUserNickname()),
		NameShow:   proto.String(author.GetUserInfoNameShowV2(ctx)),
		UserName:   proto.String(author.GetUserName()),
		UserEncode: proto.String(base64.StdEncoding.EncodeToString([]byte(strconv.Itoa(int(author.GetUserId()))))),
	}

	res := &dtProto.GetLotteryResultData{
		ThreadAuthor: authorInfo,
	}

	// 判断用户是否中奖
	if len(getDrawUserRecordByCondRes.GetData().GetInfo()) > 0 {
		prizeInfo := getDrawUserRecordByCondRes.GetData().GetInfo()[0]
		res.PrizeType = proto.Uint32(uint32(1)) // 1 中奖
		prizeID := prizeInfo.GetDrawPrizeId()

		prizeInfoInput := &quizProto.MgetDrawPrizeByIDsReq{
			Ids: []uint64{prizeID},
		}
		prizeInfoOutput := &quizProto.MgetDrawPrizeByIDsRes{}
		err = tbservice.Call(ctx, "quiz", "mgetDrawPrizeByIDs", prizeInfoInput, prizeInfoOutput)
		if err != nil || prizeInfoOutput.GetErrno() != 0 {
			tbcontext.WarningF(ctx, "call getDrawThreadPublicity fail. input=%s , err = %v",
				common.ToString(prizeInfoInput), err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		pi, ok := prizeInfoOutput.GetData().GetInfo()[prizeID]
		if !ok {
			tbcontext.WarningF(ctx, "prizeInfo not found prizeID = %d", prizeID)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		res.PrizeInfo = &dtProto.LotteryPrizeInfo{
			Name:      proto.String(pi.GetName()),
			Pic:       proto.String(convertPicToURL(ctx, uint64(pi.GetPic()))),
			TiebeiNum: proto.Int32(int32(1)),
			Title:     proto.String(conf.Hit.Title),
			SubTitle:  proto.String(conf.Hit.SubTitle),
			Prompt:    proto.String(conf.Hit.Prompt),
			Tips:      proto.String(conf.Hit.Tips),
		}
	} else {
		sendInput := &quizProto.SendQuizUserTMoneyReq{
			DrawEntityId: proto.Uint64(uint64(drawEntityID)),
			UserId:       proto.Uint64(uint64(userID)),
			Fid:          proto.Uint64(uint64(threadInfo.GetForumId())),
			ThreadUid:    proto.Uint64(uint64(author.GetUserId())),
		}

		isHit := true
		sendOutput := &quizProto.SendQuizUserTMoneyRes{}
		err = tbservice.Call(ctx, "quiz", "sendQuizUserTMoney", sendInput, sendOutput)
		if err != nil || sendOutput.GetErrno() != 0 {
			tbcontext.WarningF(ctx, "call getDrawThreadPublicity fail. input=%s , err = %v",
				common.ToString(sendOutput), err)
			isHit = false
		}

		res.PrizeType = proto.Uint32(uint32(2)) // 0: 未中奖，1:中奖；2:中贴贝

		res.PrizeInfo = &dtProto.LotteryPrizeInfo{
			Name:      proto.String("贴贝"),
			Pic:       proto.String(conf.HitTMoney.Pic),
			TiebeiNum: proto.Int32(int32(sendOutput.GetData().GetTmoney())),
			Title:     proto.String(conf.HitTMoney.Title),
			SubTitle:  proto.String(fmt.Sprintf(conf.HitTMoney.SubTitle, sendOutput.GetData().GetTmoney())),
			Prompt:    proto.String(conf.HitTMoney.Prompt),
			Tips:      proto.String(conf.HitTMoney.Tips),
		}

		if res.GetPrizeInfo().GetTiebeiNum() <= 0 || !isHit {
			res.PrizeInfo.Name = proto.String("")
			res.PrizeInfo.Pic = proto.String(conf.UnHit.Pic)
			res.PrizeType = proto.Uint32(uint32(0)) // 0: 未中奖，1:中奖；2:中贴贝
			res.PrizeInfo.Title = proto.String(conf.UnHit.Title)
			res.PrizeInfo.SubTitle = proto.String(conf.UnHit.SubTitle)
			res.PrizeInfo.Prompt = proto.String(conf.UnHit.Prompt)
			res.PrizeInfo.Tips = proto.String(conf.UnHit.Tips)
		}
	}
	response.Data = res
	// 计数
	resource.RedisCommonb.IncrBy(ctx, key, 1)
	return tiebaerror.ERR_SUCCESS
}

// convertPicToURL pic id格式的图片转换为url
func convertPicToURL(ctx context.Context, item uint64) string {
	pid2UrlInput := []image.Pid2UrlInput{
		{
			ProductName: "tieba",
			PicId:       int64(item),
			Domain:      proto.String("tiebapic.baidu.com"),
			AuthStr:     proto.String("noauth"),
		},
	}
	url, err := image.BatPid2Url(pid2UrlInput)
	if err != nil {
		tbcontext.WarningF(ctx, "pic To URL err:%v", err)
		return ""
	}
	authURL, err := image.GenAuthUrl(url[0])
	if err != nil {
		tbcontext.WarningF(ctx, "pic To URL err:%v", err)
		return ""
	}
	return authURL
}

type PrizeInfo struct {
	Pic      string `json:"pic"`       // 奖品url
	Title    string `json:"title"`     // 遗憾，本轮未中奖
	SubTitle string `json:"sub_title"` // 官方送你100贴贝，可以在商城兑换商品哦
	Prompt   string `json:"prompt"`    // 私信抽奖发起人，提供地址信息完成领取
	Tips     string `json:"tips"`      // 官方提示，任何需要中奖人转账的都是诈骗行为
}

type CardInfo struct {
	Hit       *PrizeInfo `json:"hit"`         // 中奖配置
	HitTMoney *PrizeInfo `json:"hit_t_money"` // 中贴贝配置
	UnHit     *PrizeInfo `json:"un_hit"`      // 未中奖配置
}

func getConf(ctx context.Context) *CardInfo {
	items, _ := wordserver.QueryItems(ctx, "tb_wordlist_redis_thread_draw_config",
		[]string{"turn_over_cards_conf"})
	cardInfo := &CardInfo{}
	if conf, ok := items["turn_over_cards_conf"]; ok {
		err := jsoniter.Unmarshal([]byte(conf), cardInfo)
		if err != nil {
			tbcontext.WarningF(ctx, "unmarshal turn_over_cards_conf fail , err = %v", err)
			return nil
		}
		return cardInfo
	}
	return nil
}
