package livetask

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	cancelProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/liveTask/cancel"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type CloseCommentLiveTaskResult struct {
	ErrNo  int32  `json:"errno"`
	ErrMsg string `json:"errmsg"`
}

// Cancel func Cancel(ctx context.Context, baseData *types.CancelBaseData, response *cancelProto.CancelResIdl) int
// 根据传入的基础数据和响应对象，调用取消服务，返回错误码
func CancelTask(ctx context.Context, baseData *types.CancelBaseData, response *cancelProto.CancelResIdl) int {
	userID, _ := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	forumID := baseData.Request.GetForumId()
	taskID := baseData.Request.GetTaskId()
	if userID <= 0 || forumID <= 0 || taskID <= 0 {
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 查询吧主列表
	Input := &perm.GetBawuListReq{
		ForumId: proto.Uint32(uint32(forumID)),
	}
	bawuListRes := &perm.GetBawuListRes{}

	err := tbservice.Call(ctx, "perm", "getBawuList", Input, bawuListRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || bawuListRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "bawu:getBawuList uid: %v output: %v", forumID, common.ToString(bawuListRes))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	isBazhu := false
	// 判断是否是吧主
	if len(bawuListRes.GetOutput().GetManager()) > 0 {
		managers := bawuListRes.GetOutput().GetManager()
		for _, user := range managers {
			if user.GetUser().GetRoleId() == 1 && user.GetUser().GetUserId() == userID {
				isBazhu = true
			}
		}
	}
	// 不是吧主
	if !isBazhu {
		return tiebaerror.ERR_BAWUCHECK_NOT_MANAGER
	}

	// 是吧主，调用取消服务
	cancelInput := &map[string]any{
		"forum_id": forumID,
		"task_id":  taskID,
		"user_id":  userID,
	}
	cancelOutput := &CloseCommentLiveTaskResult{}
	err = tbservice.Call(ctx, "live", "closeCommentLiveTask", cancelInput, cancelOutput)
	if err != nil || cancelOutput.ErrNo != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "live:closeCommentLiveTask uid: %v output: %v", forumID, common.ToString(cancelOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	return tiebaerror.ERR_SUCCESS
}
