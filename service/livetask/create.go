package livetask

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/live"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/stlog"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	createProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/liveTask/create"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// CreateLiveTask 创建直播任务
func CreateLiveTask(ctx context.Context, baseData *types.CreateBaseData) (int, string, *createProto.CreateCommentLiveTaskRes) {
	userID := cast.ToUint64(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0))
	input := baseData.Request
	if input.GetThreadId() <= 0 || input.GetForumId() <= 0 || userID <= 0 {
		tbcontext.WarningF(ctx, "CreateLiveTask param invalid, input=[%s]", common.ToString(input))
		return tiebaerror.ERR_PARAM_ERROR, "", nil
	}

	timeStr := time.Now().Format("20060102")
	redisKey := fmt.Sprintf("commentLiveCount_%d_%s", input.GetForumId(), timeStr)
	if input.GetPreCheck() != 1 {
		// 查询词表配置
		wordListName := "tb_wordlist_redis_commentLive"
		wordListKey := "comment_live_forum_max_num"
		rawConf, err := wordserver.QueryKey(ctx, wordListName, wordListKey)
		maxNum := int64(0)
		if err != nil || len(rawConf) == 0 {
			tbcontext.WarningF(ctx, "CreateLiveTask query wordlist fail, err=%v, wordlist=%s, key=%s", err, wordListName, wordListKey)
		} else {
			// 解析词表配置
			maxNum, err = strconv.ParseInt(rawConf, 10, 64)
			if err != nil {
				tbcontext.WarningF(ctx, "CreateLiveTask parse wordlist fail, err=%v, raw config=%s", err, rawConf)
				maxNum = 0
			}
		}
		if maxNum > 0 {
			redisRes, err := resource.RedisCommonb.Get(ctx, redisKey).Result()
			// redis为空，特殊处理
			if err == redis.ErrNil {
				err = nil
				redisRes = "0"
			}
			// 网络错误
			if err != nil {
				tbcontext.WarningF(ctx, "CreateLiveTask get redis failed. err=%v, key=%s", err, redisKey)
				return tiebaerror.ERR_CALL_SERVICE_FAIL, "", nil
			}
			num, _ := strconv.Atoi(redisRes)
			// 超过最大值，直接返回Toast提示
			if num >= int(maxNum) {
				tbcontext.WarningF(ctx, "CreateLiveTask exceed max num, current num=%d", num)
				submitToastError := fmt.Sprintf("每个吧单日最多设置%d次头条，明天再来吧!", maxNum)
				return 290009, submitToastError, nil
			}
		}
	}

	req := &live.CreateCommentLiveTaskReq{
		UserId:   proto.Uint64(userID),
		ThreadId: proto.Uint64(input.GetThreadId()),
		ForumId:  proto.Uint64(input.GetForumId()),
		Duration: proto.Uint64(input.GetDuration()),
		PreCheck: proto.Uint32(uint32(input.GetPreCheck())),
	}
	res := &live.CreateCommentLiveTaskRes{}
	err := tbservice.Call(ctx, "live", "createCommentLiveTask", req, res)
	if err != nil {
		tbcontext.WarningF(ctx, "call live::createCommentLiveTask fail err:[%v], input:[%v], output:[%v]", err,
			common.ToString(req), common.ToString(res))
		return tiebaerror.ERR_CALL_SERVICE_FAIL, "", nil
	}
	if res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call live::createCommentLiveTask fail err:[%v], input:[%v], output:[%v]", err,
			common.ToString(req), common.ToString(res))
		return int(res.GetErrno()), res.GetErrmsg(), nil
	}

	// 创建并且成功才打点
	if input.GetPreCheck() != 1 && res.GetData().GetTaskId() > 0 {
		stlog.AddLog(ctx, "fid", input.GetForumId())
		stlog.AddLog(ctx, "uid", userID)
		stlog.AddLog(ctx, "task_id", res.GetData().GetTaskId())
		resource.RedisCommonb.IncrBy(ctx, redisKey, 1)
		duration := time.Duration(90000) * time.Second // 记录保存25H
		resource.RedisCommonb.Expire(ctx, redisKey, duration)
	}

	resp := &createProto.CreateCommentLiveTaskRes{
		RemainingTime:     proto.Uint64(res.GetData().GetRemainingTime()),
		IsCover:           proto.Uint64(uint64(res.GetData().GetIsCover())),
		CommentSetMaxTime: proto.Uint64(res.GetData().GetCommentSetMaxTime()),
	}
	return tiebaerror.ERR_SUCCESS, "", resp
}
