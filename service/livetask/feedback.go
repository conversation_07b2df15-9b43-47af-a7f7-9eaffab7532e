package livetask

import (
	"context"
	"strconv"
	"time"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	feedbackProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/liveTask/feedback"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type GetLiveTaskInfoByTidResult struct {
	ErrNo  int32    `json:"errno"`
	ErrMsg string   `json:"errmsg"`
	Data   TaskData `json:"data"`
}

type TaskData struct {
	EndTime string `json:"end_time"`
}

// Cancel func Cancel(ctx context.Context, baseData *types.CancelBaseData, response *cancelProto.CancelResIdl) int
// 根据传入的基础数据和响应对象，调用取消服务，返回错误码
func Feedback(ctx context.Context, baseData *types.FeedbackBaseData, response *feedbackProto.FeedbackResIdl) int {
	// 需要查到任务的结束时间？
	cuid := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
	taskID := baseData.Request.TaskId
	if len(cuid) == 0 {
		tbcontext.WarningF(ctx, "live:feedback cuid: %v taskID is %v", cuid, *taskID)
		return tiebaerror.ERR_PARAM_ERROR
	}
	// 兼容有料头条无taskID的情况
	if *taskID <= 0 {
		tbcontext.WarningF(ctx, "live:feedback task invalid, cuid: %v taskID is %v", cuid, *taskID)
		return tiebaerror.ERR_SUCCESS
	}
	getInput := &map[string]any{
		"id": taskID,
	}
	getOutput := &GetLiveTaskInfoByTidResult{}
	err := tbservice.Call(ctx, "live", "getLiveTaskInfoByTid", getInput, getOutput)
	if err != nil || getOutput.ErrNo != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "live:getLiveTaskInfoByTid uid: %v output: %v", *taskID, common.ToString(getOutput))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	endTime := getOutput.Data.EndTime
	extime, _ := strconv.Atoi(endTime)
	if extime <= int(time.Now().Unix()) {
		tbcontext.NoticeF(ctx, "live:feedback cuid: %v taskID is %v extime: %v", cuid, *taskID, endTime)
		return tiebaerror.ERR_SUCCESS
	}
	extimeSecond := extime - int(time.Now().Unix())

	preKey := "comment_live_"
	strLineHash, _ := php2go.BaseConvert(php2go.Md5(cuid)[0:8], 16, 10)
	intLineHash, _ := strconv.Atoi(strLineHash)

	redisKey := preKey + strconv.Itoa(int(*taskID)) + "_" + strconv.Itoa(intLineHash%100)

	err = resource.RedisCommonb.SAdd(ctx, redisKey, cuid).Err()

	if err != nil {
		tbcontext.WarningF(ctx, "sadd redis fail redisKey: %v cuid: %v", redisKey, cuid)

		return tiebaerror.ERR_REDIS_CALL_FAIL
	}
	duration := time.Duration(extimeSecond+86400) * time.Second

	resource.RedisCommonb.Expire(ctx, redisKey, duration)

	return tiebaerror.ERR_SUCCESS
}
