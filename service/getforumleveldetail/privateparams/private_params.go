package privateparams

import (
	"context"
	"fmt"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
)

// opName 算子名称
const opName = "privateparams"

type Processor struct {
	baseAction *client.UIBaseAction

	forumID uint64
}

func NewProcessor() *Processor {
	p := new(Processor)
	return p
}

func (p *Processor) Process(ctx context.Context) error {
	p.forumID = cast.ToUint64(p.baseAction.GetOriginalParam("forum_id"))

	if p.forumID <= 0 {
		p.baseAction.Error(tiebaerror.ERR_PARAM_ERROR, stcdefine.GetErrMsg(tiebaerror.ERR_PARAM_ERROR), true)
		return fmt.Errorf("forumID[%d] is err", p.forumID)
	}
	return nil
}

func (p *Processor) GetForumID() uint64 {
	return p.forumID
}
