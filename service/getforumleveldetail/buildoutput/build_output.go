package buildoutput

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/forum/getForumLevelDetail"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/forumforumlevel"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/forumforumleveldetail"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/foruminfo"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getforumleveldetail/privateparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getforumleveldetail/wordlist"
)

// opName 算子名称
const opName = "buildoutput"

type ProcessBuildOutput struct {
	PrivateParams privateparams.PrivateParams

	WordList         wordlist.Wordlist
	ForumLevelDetail forumforumleveldetail.ForumForumLevelDetail
	ForumLevelInfo   forumforumlevel.ForumForumLevel
	Foruminfo        foruminfo.ForumInfo

	output *getForumLevelDetail.GetForumLevelDetailRes // 接口返回
}

func NewProcessBuildOutput() *ProcessBuildOutput {
	p := new(ProcessBuildOutput)
	p.output = &getForumLevelDetail.GetForumLevelDetailRes{
		ForumInfo:    &getForumLevelDetail.ForumLevelInfo{},
		Introduction: []string{""},
		Details:      make([]*getForumLevelDetail.ForumLevelDetail, 0),
	}
	return p
}

func (p *ProcessBuildOutput) Process(ctx context.Context) error {
	// 不可丢失核心数据校验
	if err := p.checkCoreData(); err != nil {
		return err
	}

	// 构建返回字段
	if err := p.buildOutput(); err != nil {
		return err
	}

	return nil
}

// checkCoreData 不可丢失核心数据校验
func (p *ProcessBuildOutput) checkCoreData() error {
	if p.PrivateParams == nil {
		return fmt.Errorf("privateParams is nil")
	}

	if p.WordList == nil {
		return fmt.Errorf("wordList is nil")
	}
	if p.ForumLevelDetail == nil {
		return fmt.Errorf("forumLevelDetail is nil")
	}

	if p.ForumLevelInfo == nil {
		return fmt.Errorf("forumLevelInfo is nil")
	}

	if p.Foruminfo == nil {
		return fmt.Errorf("foruminfo is nil")
	}

	return nil
}

// buildOutput 构建返回数据
func (p *ProcessBuildOutput) buildOutput() error {

	forumID := p.PrivateParams.GetForumID()
	p.output.ForumInfo.ForumId = proto.Uint64(forumID)

	wordlistConf := p.WordList.GetWordlistConf()
	p.output.Introduction = wordlistConf.ForumLevelIntroduction
	p.output.ForumInfo.ButtonText = proto.String(wordlistConf.ForumLevelButton.ButtonText)
	p.output.ForumInfo.ButtonJumpUrl = proto.String(wordlistConf.ForumLevelButton.ButtonJumpURL)

	value, ok := p.Foruminfo.GetRawForums()[uint32(forumID)]
	if ok && value != nil {
		p.output.ForumInfo.ForumName = proto.String(value.GetForumName().GetForumName())
		p.output.ForumInfo.FollowNum = proto.Uint64(uint64(value.GetStatistics().GetMemberCount()))
	}

	forumLevel := p.ForumLevelInfo.GetForumForumLevelData().ForumLevel
	p.output.ForumInfo.LevelId = proto.Uint32(forumLevel)
	p.output.ForumInfo.LevelName = proto.String(p.ForumLevelInfo.GetForumForumLevelData().LevelName)
	p.output.ForumInfo.LevelNamePic = proto.String(p.ForumLevelInfo.GetForumForumLevelData().LevelNamePic)
	p.output.ForumInfo.CardPic = proto.String(p.ForumLevelInfo.GetForumForumLevelData().CardPic)
	p.output.ForumInfo.BackgroundPic = &getForumLevelDetail.PicConf{
		Defaultmode: proto.String(p.ForumLevelInfo.GetForumForumLevelData().BackgroundPic.Defaultmode),
		Darkmode:    proto.String(p.ForumLevelInfo.GetForumForumLevelData().BackgroundPic.Darkmode),
	}
	p.output.ForumInfo.BadgeUrl = proto.String(p.ForumLevelInfo.GetForumForumLevelData().BadgeURL)
	p.output.ForumInfo.ThemeColor = p.ForumLevelInfo.GetForumForumLevelData().ThemeColor

	details := p.ForumLevelDetail.GetForumForumLevelDetail()
	p.output.ForumInfo.AveragePostsUv = proto.Uint64(p.ForumLevelDetail.GetForumAveragePostUv())
	outputDetails := make([]*getForumLevelDetail.ForumLevelDetail, 0)
	for _, detail := range details {
		tempDetail := &getForumLevelDetail.ForumLevelDetail{}
		tempDetail.Datetime = proto.String(time.Unix(detail.GetUpdateTime(), 0).Format("06年1月"))
		tempDetail.LevelId = proto.Uint32(uint32(detail.GetLevel()))
		if value, exists := wordlistConf.ForumLevelConf[uint32(detail.GetLevel())]; exists {
			tempDetail.LevelName = proto.String(value.LevelName)
		}
		outputDetails = append(outputDetails, tempDetail)
	}
	p.output.Details = outputDetails

	return nil
}
