/**
 * @Author: gongruiyang
 * @Description:
 * @File:  operator
 * @Date: 2025/02/10 16:49
 */

package buildoutput

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/forumforumlevel"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/forumforumleveldetail"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/foruminfo"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getforumleveldetail/privateparams"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/getforumleveldetail/wordlist"
)

// OperatorBuildOutput 算子定义
type OperatorBuildOutput struct {
	PrivateParams    privateparams.PrivateParams                 `inject:"canLost=false,canNil=false"`
	ForumLevelDetail forumforumleveldetail.ForumForumLevelDetail `inject:"canLost=false,canNil=false"`
	ForumLevelInfo   forumforumlevel.ForumForumLevel             `inject:"canLost=false,canNil=false"`
	Foruminfo        foruminfo.ForumInfo                         `inject:"canLost=false,canNil=false"`
	WordList         wordlist.Wordlist                           `inject:"canLost=false,canNil=false"`
}

func (o *OperatorBuildOutput) DoImpl(ctx *engine.Context) error {
	p := NewProcessBuildOutput()
	p.PrivateParams = o.PrivateParams
	p.ForumLevelDetail = o.ForumLevelDetail
	p.ForumLevelInfo = o.ForumLevelInfo
	p.Foruminfo = o.Foruminfo
	p.WordList = o.WordList

	err := p.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	// 导出数据，供controller输出返回值使用
	ctx.MustRegisterInstance(p.output)
	return nil
}
