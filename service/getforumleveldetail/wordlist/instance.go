package wordlist

// Wordlist 私有参数算子 导出接口定义
type Wordlist interface {
	// GetWordlistConf 获取词表配置
	GetWordlistConf() *Config
}

type Config struct {
	ForumLevelConf         map[uint32]ForumLevelConfData `json:"forum_level_conf"`         //吧等级conf
	ForumLevelIntroduction []string                      `json:"forum_level_introduction"` //吧等级介绍文案
	ForumLevelButton       ForumLevelButtonConf          `json:"forum_button"`             //去升级按钮配置
}

type ForumLevelConfData struct {
	LevelName     string   `json:"level_name"`
	LevelNamePic  string   `json:"level_name_pic"`
	Icon          string   `json:"icon"`
	Schema        string   `json:"schema"`
	BackgroundPic PicConf  `json:"background_pic"`
	BadgeURL      string   `json:"badge_url"`
	CardPic       string   `json:"card_pic"`
	ThemeColor    []string `json:"theme_color"`
}

type ForumLevelButtonConf struct {
	ButtonText    string `json:"button_text"`
	ButtonJumpURL string `json:"button_jump_url"`
}

type PicConf struct {
	Defaultmode string `json:"defaultmode"`
	Darkmode    string `json:"darkmode"`
}
