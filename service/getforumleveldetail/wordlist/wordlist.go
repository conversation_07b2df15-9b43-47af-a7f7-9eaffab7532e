package wordlist

import (
	"context"
	"errors"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
)

// opName 算子名称
const opName = "wordlist"

const (
	wordlistTable             = "tb_wordlist_redis_bazhu_mgr_tool_conf"
	forumMgrLevelConfKey      = "forum_mgr_level_conf"
	forumLevelIntroductionKey = "forum_level_introduction"
	ForumLevelButtonConfKey   = "forum_level_button_conf"
)

type ProcessWordlist struct {
	WordlistConf *Config
}

func NewProcessWordlist() *ProcessWordlist {
	p := &ProcessWordlist{
		WordlistConf: &Config{
			ForumLevelConf:         make(map[uint32]ForumLevelConfData),
			ForumLevelIntroduction: []string{""},
			ForumLevelButton:       ForumLevelButtonConf{},
		},
	}
	return p
}

func (p *ProcessWordlist) Process(ctx context.Context) error {
	redisKeys := []string{forumMgrLevelConfKey, forumLevelIntroductionKey, ForumLevelButtonConfKey}
	redisRes, err := wordserver.QueryKeys(ctx, wordlistTable, redisKeys)
	if err != nil {
		tbcontext.WarningF(ctx, "get tb_wordlist_redis_bazhu_mgr_tool_conf fail: %v", err)
		return err
	} else if len(redisRes) != len(redisKeys) {
		tbcontext.WarningF(ctx, "the query out len is not right: %d", len(redisRes))
		return errors.New("out len not right")
	}

	forumLevelConf := make(map[uint32]ForumLevelConfData)
	if redisRes[0] != "" {
		err = jsoniter.Unmarshal([]byte(redisRes[0]), &forumLevelConf)
		if err != nil {
			tbcontext.WarningF(ctx, "fail to decode raw input, input = %s, err = %v", redisRes[0], err)
			return err
		}
	}
	p.WordlistConf.ForumLevelConf = forumLevelConf
	if redisRes[1] != "" {
		forumLevelIntroduction := []string{}
		err = jsoniter.UnmarshalFromString(redisRes[1], &forumLevelIntroduction)
		if err != nil {
			tbcontext.WarningF(ctx, "fail to decode raw input, input = %s, err = %v", redisRes[2], err)
			return err
		}
		p.WordlistConf.ForumLevelIntroduction = forumLevelIntroduction
	}

	forumLevelButtonConf := ForumLevelButtonConf{}
	if redisRes[2] != "" {
		err = jsoniter.Unmarshal([]byte(redisRes[2]), &forumLevelButtonConf)
		if err != nil {
			tbcontext.WarningF(ctx, "fail to decode raw input, input = %s, err = %v", redisRes[2], err)
			return err
		}
	}
	p.WordlistConf.ForumLevelButton = forumLevelButtonConf

	return nil
}

// GetWordlistConf 获取是否能创建智能体
func (p *ProcessWordlist) GetWordlistConf() *Config {
	return p.WordlistConf
}
