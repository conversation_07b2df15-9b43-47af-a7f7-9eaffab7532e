package dialog

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base32"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	uiclient "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/adcounter"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/ulink"

	"github.com/golang/protobuf/proto"
	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	commonProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/modata"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/usertask"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/ip"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/php2go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/dialog/showDialog"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/resource"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

// ShowDialog 弹窗展示
//
// 参数：
//
//	ctx: 上下文对象
//	baseData: 基础数据
//	response: 响应数据
//
// 返回值：
//
//	成功返回tiebaerror.ERR_SUCCESS，否则返回错误码
func ShowDialog(ctx context.Context, baseData *types.ShowDialogBaseData,
	response *showDialog.ShowDialogResIdl) int {
	dialogType := baseData.Request.GetType()

	showDialogInfo := &showDialog.ShowDialogRes{}
	var err int
	switch dialogType {
	case types.DialogTypeCommon:
		showDialogInfo, err = getCommonDialogInfo(ctx, baseData)
		if err != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "getCommonDialogInfo fail, input:[%v]", err)
			return err
		}
	case types.DialogTypePageSignPop:
		showDialogInfo, err = getPageSignPopInfo(ctx, baseData)
		if err != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "getPageSignPopInfo fail, input:[%v]", err)
			return err
		}
	case types.DialogTypePageSignAward:
		showDialogInfo, err = getPageSignAwardInfo(ctx, baseData)
		if err != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "getPageSignPopInfo fail, input:[%v]", err)
			return err
		}
	case types.DialogTypePageAppUninstall:
		showDialogInfo, err = getAppUninstallAwardInfo(ctx, baseData)
		if err != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "getAppUninstallAwardInfo fail, input:[%v]", err)
			return err
		}
	default:
	}

	response.Data = showDialogInfo

	return tiebaerror.ERR_SUCCESS
}

func getPageSignAwardInfo(ctx context.Context, baseData *types.ShowDialogBaseData) (*showDialog.ShowDialogRes, int) {
	dialogInfo := &showDialog.ShowDialogRes{}
	if baseData == nil || baseData.BaseObj == nil {
		tbcontext.WarningF(ctx, "pageSignPopPreInfo base data invalid")
		return dialogInfo, tiebaerror.ERR_PARAM_ERROR
	}
	awardName := baseData.Request.GetAwardName()
	taskId := baseData.Request.GetTaskId()
	if awardName == "" {
		tbcontext.WarningF(ctx, "pageSignPopPreInfo base data invalid")
		return dialogInfo, tiebaerror.ERR_PARAM_ERROR
	}
	// 商业化调起用户的签到弹窗，有更丰富的弹窗内容
	if baseData.Request.GetUserIdentify() == types.UsergrowthTaskIdentifyCommercialPush {
		pageSignTask, pageSignConfig, errno, err := pageSignPopPreInfo(ctx, baseData)
		if errno != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "pageSignPopPreInfo fail, input:[%v], err:[%v]", common.ToString(baseData), err)
			return dialogInfo, errno
		}
		if pageSignTask == nil || pageSignConfig == nil {
			tbcontext.WarningF(ctx, "pageSignPopPreInfo invalid, input:[%v]", common.ToString(baseData))
			return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		progress := pageSignTask.GetPageSignProgress().GetProgress()

		// 解析 commercial config
		var commercialConf map[string]map[string]interface{}
		if err := json.Unmarshal([]byte(pageSignConfig.ExtendAwardInfo), &commercialConf); err != nil {
			tbcontext.WarningF(ctx, "parse commercial config failed: %v", err)
			return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
		}

		pageSignAwards := make(map[string]string)
		var updateAwardInfo types.CommercialUpdateInfo

		progressKey := strconv.Itoa(int(progress))
		if targetConf, ok := commercialConf[progressKey]; ok {
			rawUpdateInfo, ok := targetConf["commercial_update_info"]
			if !ok {
				tbcontext.WarningF(ctx, "missing commercial_update_info at progress [%v]", progressKey)
				return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
			}

			updateInfoMap, ok := rawUpdateInfo.(map[string]interface{})
			if !ok {
				tbcontext.WarningF(ctx, "commercial_update_info type error: %T", rawUpdateInfo)
				return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
			}

			updateInfoBytes, err := json.Marshal(updateInfoMap)
			if err != nil {
				tbcontext.WarningF(ctx, "marshal commercial_update_info failed: %v", err)
				return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
			}

			if err := json.Unmarshal(updateInfoBytes, &updateAwardInfo); err != nil {
				tbcontext.WarningF(ctx, "unmarshal commercial_update_info failed: %v", err)
				return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
			}

			pageSignAwards["commercial_update_info"] = string(updateInfoBytes)
		}

		title := pageSignConfig.Title

		// sub_title文案包含升级前奖励替换项
		// 恭喜你，获得${$award_detail$}
		subtitle := strings.Replace(
			pageSignConfig.Subtitle,
			"$award_detail$",
			fmt.Sprintf("%d%s", updateAwardInfo.BeforeAwardInfo.Num, updateAwardInfo.BeforeAwardInfo.Text),
			-1,
		)

		// button文案包含升级后-升级前奖励差替换项
		buttonText := strings.Replace(
			pageSignConfig.ButtonText,
			"$award_detail$",
			fmt.Sprintf("%d%s", updateAwardInfo.AfterAwardInfo.Num-updateAwardInfo.BeforeAwardInfo.Num, updateAwardInfo.AfterAwardInfo.Text),
			-1,
		)

		dialogInfo = &showDialog.ShowDialogRes{
			Title:          proto.String(title),
			SubTitle:       proto.String(subtitle),
			PageSignAwards: pageSignAwards,
			ButtonText:     proto.String(buttonText),
		}

		return dialogInfo, tiebaerror.ERR_SUCCESS
	}

	// 获取词表数据
	awardConfKey := "task_award_" + awardName
	awardSepConfKey := "task_award_" + awardName + "_" + fmt.Sprintf("%d", taskId)
	wordKeys := []string{awardConfKey, awardSepConfKey}

	arrResults, err := wordserver.QueryItemsNoPHPSerialized(ctx, "tb_wordlist_redis_usergrowth_task", wordKeys)
	if err != nil || arrResults == nil {
		tbcontext.WarningF(ctx, "page_sign wordlist config err: %v", err)
		return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	strAwardConf := ""
	if awardConf, ok := arrResults[awardConfKey].(string); ok {
		strAwardConf = awardConf
	}
	if awardSepConf, ok := arrResults[awardSepConfKey].(string); ok {
		strAwardConf = awardSepConf
	}
	if strAwardConf == "" {
		tbcontext.WarningF(ctx, "page_sign wordlist config err: %v", err)
		return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	dialogConf := &types.PageSignAwardDialogConf{}
	err = json.Unmarshal([]byte(strAwardConf), &dialogConf)
	if err != nil {
		tbcontext.WarningF(ctx, "get Award Detail Unmarshal fail, err:[%v]", err)
		return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	dialogInfo = &showDialog.ShowDialogRes{
		Title:           &dialogConf.Title,
		BackgroundImage: &dialogConf.Pic,
		Type:            &dialogConf.Type,
		PropsId:         &awardName,
	}
	// dialogInfo = &showDialog.ShowDialogRes{
	// 	Title:           proto.String("签到"),
	// 	BackgroundImage: proto.String("https://tieba-ares.cdn.bcebos.com/mis/2024-4/1714376165556/17189c60a76b.png"),
	// 	Type:            proto.Int32(6),
	// 	PropsId:         proto.String("1310265"),
	// }

	return dialogInfo, tiebaerror.ERR_SUCCESS
}

func getPageSignPopInfo(ctx context.Context, baseData *types.ShowDialogBaseData) (*showDialog.ShowDialogRes, int) {
	dialogInfo := &showDialog.ShowDialogRes{}
	if baseData == nil || baseData.BaseObj == nil {
		tbcontext.WarningF(ctx, "pageSignPopPreInfo base data invalid")
		return dialogInfo, tiebaerror.ERR_PARAM_ERROR
	}
	// 校验token
	userID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	salt := fmt.Sprintf("%s_%d_%d", types.PageSignPopTokenSalt, baseData.Request.GetUserIdentify(), userID)
	realToken := php2go.Md5(salt)
	if realToken != baseData.Request.GetToken() {
		tbcontext.WarningF(ctx, "token not match real:%s actual: %s", realToken, baseData.Request.GetToken())
		return dialogInfo, tiebaerror.ERR_PARAM_ERROR
	}
	// 新用户和调起用户条件判断
	// 12.84引入商业化push调起用户
	pageSignTask, pageSignConfig, errno, err := pageSignPopPreInfo(ctx, baseData)
	if errno != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "pageSignPopPreInfo fail, input:[%v], err:[%v]", common.ToString(baseData), err)
		return dialogInfo, errno
	}
	if pageSignTask == nil || pageSignConfig == nil {
		tbcontext.WarningF(ctx, "pageSignPopPreInfo invalid, input:[%v]", common.ToString(baseData))
		return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	cUID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
	clientType := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", ""), common.TTT_INT).(int)
	clientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)

	// 任务奖励配置
	pageSignTaskExt := common.JSONToMap(pageSignTask.GetExt())
	pageSignTaskAward := pageSignTaskExt["award_info"].(map[string]interface{})

	// 商业化Push调起用户，不自动做任务，直接返回弹窗信息
	if baseData.Request.GetUserIdentify() == types.UsergrowthTaskIdentifyCommercialPush {
		dotaskStatus := 0
		progress := pageSignTask.GetPageSignProgress().GetProgress()

		// 解析当天签到奖励的升级信息，替换button_text3动态文案
		// 看视频，再领$award_detail$
		var commercialConf map[string]map[string]interface{} // 解析 commercial config
		if err := json.Unmarshal([]byte(pageSignConfig.ExtendAwardInfo), &commercialConf); err != nil {
			tbcontext.WarningF(ctx, "parse commercial config failed: %v", err)
			return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		var updateAwardInfo types.CommercialUpdateInfo
		progressKey := strconv.Itoa(int(progress + 1))
		if targetConf, ok := commercialConf[progressKey]; ok {
			rawUpdateInfo, ok := targetConf["commercial_update_info"]
			if !ok {
				tbcontext.WarningF(ctx, "missing commercial_update_info at progress [%v]", progressKey)
				return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
			}

			updateInfoMap, ok := rawUpdateInfo.(map[string]interface{})
			if !ok {
				tbcontext.WarningF(ctx, "commercial_update_info type error: %T", rawUpdateInfo)
				return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
			}

			updateInfoBytes, err := json.Marshal(updateInfoMap)
			if err != nil {
				tbcontext.WarningF(ctx, "marshal commercial_update_info failed: %v", err)
				return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
			}

			if err := json.Unmarshal(updateInfoBytes, &updateAwardInfo); err != nil {
				tbcontext.WarningF(ctx, "unmarshal commercial_update_info failed: %v", err)
				return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
			}
		}
		buttonText3 := strings.Replace(
			pageSignConfig.ButtonText3,
			"$award_detail$",
			fmt.Sprintf("%d%s", updateAwardInfo.AfterAwardInfo.Num-updateAwardInfo.BeforeAwardInfo.Num, updateAwardInfo.AfterAwardInfo.Text),
			-1,
		)

		dialogInfo = &showDialog.ShowDialogRes{
			Title:          proto.String(pageSignConfig.Title),
			SubTitle:       proto.String(pageSignConfig.Subtitle),
			Progress:       proto.Int32(int32(progress)),
			DotaskStatus:   proto.Int32(int32(dotaskStatus)),
			PageSignAwards: mergeAwardWithCommercialConf(ctx, pageSignTaskAward, pageSignConfig.ExtendAwardInfo),
			ButtonText:     proto.String(pageSignConfig.ButtonText),
			ButtonText2:    proto.String(pageSignConfig.ButtonText2),
			ButtonText3:    proto.String(buttonText3),
		}
		return dialogInfo, tiebaerror.ERR_SUCCESS
	}

	// 存储身份
	redisKey := fmt.Sprintf(types.UsergrowthTaskIdentifyKey, userID)
	_, err = resource.RedisTwLive.HMSet(ctx, redisKey, "identify", baseData.Request.GetUserIdentify(), "time", time.Now().Unix()).Result()
	if err != nil {
		tbcontext.WarningF(ctx, "set identify fail, input:[%v], err:[%v]", common.ToString(baseData), err)
		return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	awardDay := len(pageSignTaskAward)

	// 设置身份过期时间, 在配置的时间上加8天
	t2, _ := time.ParseInLocation("2006-01-02", time.Now().Format("2006-01-02"), time.Local)
	secTimeStamp := t2.AddDate(0, 0, awardDay+8).Unix()
	resource.RedisTwLive.Expire(ctx, redisKey, time.Second*time.Duration(secTimeStamp-time.Now().Unix()))

	// 做任务
	dotaskStatus := 1
	progress := pageSignTask.GetPageSignProgress().GetProgress()
	input := &usertask.DoUserTaskByActTypeReq{
		UserId:          proto.Uint64(userID),
		Cuid:            proto.String(cUID),
		ActType:         proto.String("page_sign"),
		CallFrom:        proto.String("client"),
		ClientType:      proto.Int64(int64(clientType)),
		ClientVersion:   proto.String(clientVersion),
		NeedCheckSwitch: proto.Int64(1),
	}

	output := &usertask.DoUserTaskByActTypeRes{}
	err = tbservice.Call(ctx, "common", "doUserGrowthTask", input, output, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "fail to call service common:doUserGrowthTask, input=[%v], output=[%v], err=[%v]",
			common.ToString(input), common.ToString(output), err)
	} else if len(output.GetData().GetSuccessTaskList()) > 0 {
		// 今日任务自动完成成功
		dotaskStatus = 2
		progress = progress + 1
	}
	pageSignTaskAwardFinal := make(map[string]string, len(pageSignTaskAward))
	for k, v := range pageSignTaskAward {
		pageSignTaskAwardFinal[k] = common.Tvttt(v, common.TTT_STRING).(string)
	}
	dialogInfo = &showDialog.ShowDialogRes{
		Title:          proto.String(pageSignConfig.Title),
		SubTitle:       proto.String(pageSignConfig.Subtitle),
		Progress:       proto.Int32(int32(progress)),
		DotaskStatus:   proto.Int32(int32(dotaskStatus)),
		PageSignAwards: pageSignTaskAwardFinal,
	}
	return dialogInfo, tiebaerror.ERR_SUCCESS
}

func pageSignPopPreInfo(ctx context.Context, baseData *types.ShowDialogBaseData) (*commonProto.UserGrowthTask, *types.PageSignPopConfig, int, error) {
	multi := tbservice.Multi()
	clientType := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", ""), common.TTT_INT).(int)
	clientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	userID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	cUID := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
	// 版控不符合，不出弹窗返回错误码
	if clientvers.CompareV2(clientVersion, "<", "12.78") {
		return nil, nil, tiebaerror.ERR_PARAM_ERROR, errors.New("client_version not match")
	}
	if !php2go.InArray(baseData.Request.GetUserIdentify(), []int32{types.UsergrowthTaskIdentifyNewUser, types.UsergrowthTaskIdentifyExternal,
		types.UsergrowthTaskIdentifyCommercialPush}) {
		return nil, nil, tiebaerror.ERR_PARAM_ERROR, errors.New("user identify invalid")
	}
	if userID <= 0 {
		return nil, nil, tiebaerror.ERR_PARAM_ERROR, errors.New("no user_id")
	}
	// 新用户
	if baseData.Request.GetUserIdentify() == types.UsergrowthTaskIdentifyNewUser {
		var brand string
		strBrand := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("brand", ""), common.TTT_STRING).(string)
		strModel := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("model", ""), common.TTT_STRING).(string)

		if clientType == clientvers.CLIENT_TYPE_IPHONE {
			brand = strBrand
		} else if clientType == clientvers.CLIENT_TYPE_ANDROID {
			brand = strModel
		}
		subappType := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("subapp_type", ""), common.TTT_STRING).(string)
		subappTypeInt := 0
		if types.SubappMap[subappType] != 0 {
			subappTypeInt = types.SubappMap[subappType]
		} else {
			subappTypeInt = types.SubappMap["tieba"]
		}
		arrParam := &tbservice.Parameter{
			Service: "modata",
			Method:  "getRecentClientInfo",
			Input: map[string]interface{}{
				"imei":        baseData.BaseObj.ObjRequest.GetCommonAttr("phone_imei", ""),
				"user_id":     common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", ""), common.TTT_INT64).(int64),
				"cuid":        common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string),
				"brand":       brand,
				"subapp_type": subappTypeInt,
			},
			Output: &modata.GetRecentClientInfoRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "modata:getRecentClientInfo", arrParam)
	}
	// 获取任务
	arrParam := &tbservice.Parameter{
		Service: "common",
		Method:  "getUserGrowthTaskList",
		Input: &commonProto.GetUserGrowthTaskListReq{
			Cuid:                 proto.String(cUID),
			UserId:               proto.Uint64(userID),
			CallFrom:             proto.String("client"),
			NeedPageSignProgress: proto.Int32(1),
			NeedAwardInfo:        proto.Int32(1),
			ActType:              proto.String("page_sign"),
			PageSignUserIdentify: proto.Int32(baseData.Request.GetUserIdentify()),
			ClientType:           proto.Int32(int32(clientType)),
			ClientVersion:        proto.String(clientVersion),
			NeedCheckPreValid:    proto.Int32(1),
		},
		Output: &commonProto.GetUserGrowthTaskListRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "common:getUserGrowthTaskList", arrParam)
	multi.Call(ctx)
	// 解析数据
	if baseData.Request.GetUserIdentify() == types.UsergrowthTaskIdentifyNewUser {
		resInter, err := multi.GetResult(ctx, "modata:getRecentClientInfo")
		if err != nil {
			tbcontext.WarningF(ctx, "call modata::getRecentClientInfo fail: %v", err)
			return nil, nil, tiebaerror.ERR_CALL_SERVICE_FAIL, err
		}
		res := resInter.(*modata.GetRecentClientInfoRes)
		if res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call modata::getRecentClientInfo not success: %v", common.ToString(res))
			return nil, nil, tiebaerror.ERR_CALL_SERVICE_FAIL, err
		}
		// 不符合新用户判定
		if time.Now().Unix()-int64(*res.Ext.LastTime) > 24*3600 {
			return nil, nil, tiebaerror.ERR_INVALID_SIGN, errors.New("not match new user")
		}
	}
	resInter, err := multi.GetResult(ctx, "common:getUserGrowthTaskList")
	if err != nil {
		tbcontext.WarningF(ctx, "call common::getUserGrowthTaskList fail: %v", err)
		return nil, nil, tiebaerror.ERR_CALL_SERVICE_FAIL, err
	}
	res := resInter.(*commonProto.GetUserGrowthTaskListRes)
	if res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call common::getUserGrowthTaskList not success: %v", common.ToString(res))
		return nil, nil, tiebaerror.ERR_CALL_SERVICE_FAIL, err
	}
	var userTask *commonProto.UserGrowthTask
	for _, v := range res.GetData().GetDailyTask() {
		if v.GetActType() == "page_sign" {
			userTask = v
		}
	}
	// 今日任务已完成的话不应该再有弹窗了
	if userTask == nil || userTask.GetDotaskStatus() == 2 && baseData.Request.GetType() == types.DialogTypePageSignPop {
		tbcontext.WarningF(ctx, "page_sign task has finished uid: %v", common.ToString(userID))
		return nil, nil, tiebaerror.ERR_INVALID_SIGN, errors.New("task has finished")
	}
	// 调起用户判定断签
	// 新用户理应无进度
	if userTask.GetPageSignProgress().GetProgress() != 0 &&
		(baseData.Request.GetUserIdentify() == types.UsergrowthTaskIdentifyNewUser ||
			baseData.Request.GetUserIdentify() == types.UsergrowthTaskIdentifyExternal) {
		tbcontext.WarningF(ctx, "page_sign task has progress uid: %v", common.ToString(userID))
		return nil, nil, tiebaerror.ERR_INVALID_SIGN, errors.New("task has progress")
	}
	// 获取词表数据
	var wordKeys []string
	if baseData.Request.GetUserIdentify() == types.UsergrowthTaskIdentifyNewUser {
		wordKeys = append(wordKeys, "newuser_sign_pop_title", "newuser_sign_pop_sub_title")
	} else if baseData.Request.GetUserIdentify() == types.UsergrowthTaskIdentifyExternal {
		wordKeys = append(wordKeys, "external_up_sign_pop_title", "external_up_sign_pop_sub_title")
	} else if baseData.Request.GetUserIdentify() == types.UsergrowthTaskIdentifyCommercialPush {
		if baseData.Request.GetType() == types.DialogTypePageSignPop {
			// 商业化用户引导弹窗
			wordKeys = append(wordKeys, "commercial_push_sign_pop_title", "commercial_push_sign_pop_sub_title", "commercial_push_sign_pop_button_text",
				"commercial_push_sign_pop_button_text2", "commercial_push_sign_pop_button_text3", "commercial_push_sign_pop_award_upgrade_info")
		} else if baseData.Request.GetType() == types.DialogTypePageSignAward {
			// 商业化用户获得奖励弹窗
			wordKeys = append(wordKeys, "commercial_push_award_pop_title", "commercial_push_award_pop_sub_title", "commercial_push_award_pop_button_text",
				"commercial_push_sign_pop_award_upgrade_info")
		}
	}
	wordRes, err := wordserver.QueryKeys(ctx, "tb_wordlist_redis_usergrowth_task", wordKeys)
	if nil != err {
		tbcontext.WarningF(ctx, "page_sign wordlist config err: %v", err)
		return nil, nil, tiebaerror.ERR_CALL_SERVICE_FAIL, err
	}
	if len(wordRes) < 2 {
		tbcontext.WarningF(ctx, "page_sign wordlist config err: %v", err)
		return nil, nil, tiebaerror.ERR_CALL_SERVICE_FAIL, errors.New("wordlist config err")
	}
	configInfo := &types.PageSignPopConfig{
		Title:    wordRes[0],
		Subtitle: wordRes[1],
	}
	if len(wordRes) >= 6 && baseData.Request.GetUserIdentify() == types.UsergrowthTaskIdentifyCommercialPush &&
		baseData.Request.GetType() == types.DialogTypePageSignPop {
		configInfo.ButtonText = wordRes[2]
		configInfo.ButtonText2 = wordRes[3]
		configInfo.ButtonText3 = wordRes[4]
		configInfo.ExtendAwardInfo = wordRes[5]
	} else if len(wordRes) >= 4 && baseData.Request.GetUserIdentify() == types.UsergrowthTaskIdentifyCommercialPush &&
		baseData.Request.GetType() == types.DialogTypePageSignAward {
		configInfo.ButtonText = wordRes[2]
		configInfo.ExtendAwardInfo = wordRes[3]
	}
	return userTask, configInfo, tiebaerror.ERR_SUCCESS, nil
}

func getAppUninstallAwardInfo(ctx context.Context, baseData *types.ShowDialogBaseData) (*showDialog.ShowDialogRes, int) {
	dialogInfo := &showDialog.ShowDialogRes{}
	if baseData == nil || baseData.BaseObj == nil || baseData.Request.GetGoodsId() <= 0 {
		tbcontext.WarningF(ctx, "getAppUninstallAwardInfo base data invalid")
		return dialogInfo, tiebaerror.ERR_PARAM_ERROR
	}
	// 校验token
	userID, ok := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_UINT64).(uint64)
	if !ok {
		tbcontext.WarningF(ctx, "getAppUninstallAwardInfo base data invalid")
		return dialogInfo, tiebaerror.ERR_PARAM_ERROR
	}

	salt := fmt.Sprintf("%s_%d_%d", types.AppUninstallPopTokenSalt, userID, baseData.Request.GetGoodsId())

	realToken := php2go.Md5(salt)
	if realToken != baseData.Request.GetToken() {
		tbcontext.WarningF(ctx, "getAppUninstallAwardInfo token not match real:%s actual: %s", realToken, baseData.Request.GetToken())
		return dialogInfo, tiebaerror.ERR_PARAM_ERROR
	}
	goodsID := baseData.Request.GetGoodsId()
	input := map[string][]int64{}
	input["goods_ids"] = []int64{int64(goodsID)}

	output := &commonProto.GetAllOnlineGoodsRes{}
	err := tbservice.Call(ctx, "common", "mgetGoodsInfoByGoodsId", input, output,
		tbservice.WithConverter(tbservice.JSONITER))

	if err != nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "fail to call service common:mgetGoodsInfoByGoodsId, input=[%s], output=[%s], err=[%v]",
			common.ToString(input), common.ToString(output), err)
		return dialogInfo, tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	if len(output.GetData()) == 0 {
		return dialogInfo, tiebaerror.ERR_SUCCESS
	}
	if len(output.GetData()) > 0 {
		if _, ok := output.GetData()[cast.ToString(goodsID)]; !ok {
			tbcontext.FatalF(ctx, "fail to get goods info, input=[%s], output=[%s]",
				common.ToString(input), common.ToString(output))
			return dialogInfo, tiebaerror.ERR_PARAM_ERROR
		}
		goodsInfo := output.GetData()[cast.ToString(goodsID)]
		if goodsInfo.GetExt() == nil {
			tbcontext.WarningF(ctx, "get goods info ext is nil, input=[%s], output=[%s]",
				common.ToString(input), common.ToString(output))
			return dialogInfo, tiebaerror.ERR_PARAM_ERROR
		}
		extInfo := cast.ToString(goodsInfo.GetExt())
		extMap := map[string]any{}
		err = json.Unmarshal([]byte(extInfo), &extMap)
		if err != nil {
			tbcontext.WarningF(ctx, "get goods info ext Unmarshal fail, err:[%v]", err)
			return dialogInfo, tiebaerror.ERR_PARAM_ERROR
		}
		if _, ok := extMap["night_icon"]; ok {
			nightIcon := extMap["night_icon"]
			dialogInfo.NightIcon = proto.String(cast.ToString(nightIcon))
		}
		dialogInfo.DayIcon = proto.String(goodsInfo.GetIcon())
		dialogInfo.Title = proto.String(goodsInfo.GetName())
		dialogInfo.SubTitle = proto.String(goodsInfo.GetBrief())
	}

	return dialogInfo, tiebaerror.ERR_SUCCESS
}

// getCommonDialogInfo 获取通用弹窗信息
//
// 参数：
//
//	ctx: 上下文对象
//	baseData: 基础数据
//
// 返回值：
//
//	*showDialog.ShowDialogRes: 弹窗信息结构体指针
//	int: 错误码
func getCommonDialogInfo(ctx context.Context, baseData *types.ShowDialogBaseData) (*showDialog.ShowDialogRes, int) {
	// 读词表
	dialogName := baseData.Request.GetDialogName()
	if dialogName == "" {
		tbcontext.WarningF(ctx, "dialog name is empty")
		return nil, tiebaerror.ERR_PARAM_ERROR
	}

	keys := []string{
		dialogName + types.DialogConfSuffix,
		"pop_up_dialog_trace_link_update",
	}

	values, err := wordserver.QueryKeys(ctx, types.DialogConfTable, keys)
	if err != nil {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], err=[%v]", types.DialogConfTable, keys, err)
		return nil, tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	if len(values) == 0 {
		tbcontext.WarningF(ctx, "dialog conf not found: %s", dialogName)
		return nil, tiebaerror.ERR_PARAM_ERROR
	}

	if values[0] == "" {
		tbcontext.WarningF(ctx, "dialog conf is empty, dialog name: %s", dialogName)
		return nil, tiebaerror.ERR_SUCCESS
	}

	dialogConf := types.DialogConf{}
	err = jsoniter.UnmarshalFromString(values[0], &dialogConf)
	if err != nil {
		tbcontext.WarningF(ctx, "unmarshal dialog conf fail: %v, res[%s]", err, values[0])
		return nil, tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	dialogTraceUrlConf := types.DialogTraceURLParamReplaceConf{
		Mode: types.StatisticsURLParamNotReplaceOff,
	}

	dialogTraceUrlConf = types.DialogTraceURLParamReplaceConf{}
	if len(values) > 1 && values[1] != "" {
		err = jsoniter.UnmarshalFromString(values[1], &dialogTraceUrlConf)
		if err != nil {
			tbcontext.WarningF(ctx, "unmarshal dialog trace conf fail: %v, res[%s]", err, values[1])
		}
	}

	dialogInfo := getDialogInfoByConf(ctx, baseData, dialogConf, dialogTraceUrlConf)

	return dialogInfo, tiebaerror.ERR_SUCCESS
}

// needReplaceStatisticsParam 判断是否需要替换trace url参数
// 判断是否需要替换trace url参数
//
// 参数：
//
//	ctx: 上下文对象
//	baseData: 基础数据
//	dialogTraceUrlConf: 弹窗trace url参数替换配置
//
// 返回值：
//
//	bool: 是否需要替换trace url参数 true需要 false不需要
func needReplaceStatisticsParam(ctx context.Context, baseData *types.ShowDialogBaseData,
	dialogTraceUrlConf types.DialogTraceURLParamReplaceConf, dialogConf types.DialogConf) (bool, bool) {

	if dialogConf.Advertiser != "JD" {
		return true, true
	}

	switch dialogTraceUrlConf.Mode {
	case types.StatisticsURLParamNotReplaceOff:
		return true, true
	case types.StatisticsURLParamNotReplaceAll:
		tbcontext.WarningF(ctx, "needReplaceStatisticsParam mod is all")
		return false, dialogTraceUrlConf.ReplaceULink
	case types.StatisticsURLParamNotReplaceWhitelist:
		if baseData == nil || baseData.BaseObj == nil || baseData.BaseObj.ObjRequest == nil {
			return true, true
		}
		cuid, ok := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string)
		if !ok {
			return true, true
		}
		if len(dialogTraceUrlConf.Whitelist) == 0 {
			return true, true
		}
		for _, c := range dialogTraceUrlConf.Whitelist {
			if cuid == c {
				tbcontext.WarningF(ctx, "needReplaceStatisticsParam cuid[%s] in whitelist", cuid)
				return false, dialogTraceUrlConf.ReplaceULink
			}
		}
		return true, true
	}
	return true, true
}

// getDialogInfoByConf 根据弹窗配置信息获取对话框信息
// ctx: 上下文对象
// baseData: 基础数据
// dialogConf: 词表配置信息
// 返回值：弹窗信息
func getDialogInfoByConf(ctx context.Context, baseData *types.ShowDialogBaseData,
	dialogConf types.DialogConf, dialogTraceUrlConf types.DialogTraceURLParamReplaceConf) *showDialog.ShowDialogRes {
	dialogInfo := &showDialog.ShowDialogRes{
		Type:            proto.Int32(dialogConf.Type),
		BackgroundImage: proto.String(dialogConf.BackgroundImage),
		ButtonText:      proto.String(dialogConf.ButtonText),
		Url:             proto.String(dialogConf.URL),
		ForumName:       proto.String(dialogConf.ForumName),
		ThreadId:        proto.Int64(dialogConf.ThreadID),
		RoomId:          proto.Int64(dialogConf.RoomID),
		Schema:          proto.String(dialogConf.Schema),
	}
	if dialogConf.ExtConf.Empty() {
		return dialogInfo
	}

	// 替换参数
	needReplaceParam := make(map[string]string)
	if dialogConf.Advertiser == "JD" {
		// if true {
		needReplaceParam = getReplaceParamJD(ctx, baseData)
	} else {
		needReplaceParam = getReplaceParam(ctx, baseData)
	}

	strScheme, strURL := getSchemeAndURL(ctx, dialogConf, needReplaceParam)
	dialogInfo.Schema = proto.String(strScheme)
	dialogInfo.Url = proto.String(strURL)

	pvUpload := getPvUploadInfo(ctx, baseData, dialogConf)
	if pvUpload != nil {
		dialogInfo.PvUpload = pvUpload
	}
	needReplaceTraceUrlParam, replaceUlink := needReplaceStatisticsParam(ctx, baseData, dialogTraceUrlConf, dialogConf)
	pvTrackData := getDataByReplaceParam(ctx, dialogConf.ExtConf.PvTrackData, needReplaceParam, needReplaceTraceUrlParam, baseData.BaseObj.ObjRequest)
	if len(pvTrackData) > 0 {
		dialogInfo.PvTrackData = pvTrackData
	}

	clickTrackData := getDataByReplaceParam(ctx, dialogConf.ExtConf.ClickTrackData, needReplaceParam, needReplaceTraceUrlParam, baseData.BaseObj.ObjRequest)
	if len(clickTrackData) > 0 {
		dialogInfo.ClickTrackData = clickTrackData
	}

	// logs
	strIP := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("ip_str", ""), common.TTT_STRING).(string)
	dialogLogs := &showDialog.DialogLogs{}
	if ip.GetIPVersion(strIP) == ip.IPV4 {
		dialogLogs.Ipv4 = proto.String(strIP)
	}
	if ip.GetIPVersion(strIP) == ip.IPV6 {
		dialogLogs.Ipv6 = proto.String(strIP)
	}
	dialogInfo.Logs = dialogLogs

	clientTypeInf := common.TransforValueToTargetType(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", 0), common.TTT_INT)
	clientVersionInf := common.TransforValueToTargetType(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING)

	clientType, ok := clientTypeInf.(int)
	if !ok {
		tbcontext.WarningF(ctx, "client_type is not int: %v", clientTypeInf)
		return dialogInfo
	}
	clientVersion, ok := clientVersionInf.(string)
	if !ok {
		tbcontext.WarningF(ctx, "client_version is not str: %v", clientVersion)
		return dialogInfo
	}
	if uLinkUrl, err := ulink.GetULinkWithTieba(ctx, baseData.BaseObj.ObjRequest,
		dialogInfo.GetSchema(), dialogInfo.GetUrl(), clientVersion, clientType, true); err == nil && replaceUlink {
		dialogInfo.Url = proto.String(uLinkUrl)
	}

	return dialogInfo
}

// getSchemeAndURL 函数用于获取Scheme和URL
//
// 参数：
// ctx：上下文对象
// baseData：ShowDialogBaseData类型指针，表示对话框基础数据
// dialogConf：DialogConf类型，表示对话框配置
//
// 返回值：
// string类型，表示App的URL
// string类型，表示H5的URL
func getSchemeAndURL(ctx context.Context, dialogConf types.DialogConf, needReplaceParam map[string]string) (string, string) {
	if dialogConf.ExtConf.SchemeH5URL == "" {
		return dialogConf.Schema, dialogConf.URL
	}

	h5URL := dialogConf.ExtConf.SchemeH5URL
	appURL := dialogConf.ExtConf.SchemeAppURL

	if len(needReplaceParam) == 0 {
		return appURL, h5URL
	}

	for k, v := range needReplaceParam {
		h5URL = strings.ReplaceAll(h5URL, k, v)
		appURL = strings.ReplaceAll(appURL, k, v)
	}

	return appURL, h5URL
}

// getPvUploadInfo 根据给定的上下文、基础数据和弹窗配置，获取曝光量上传信息
// 参数：
//
//	ctx：上下文
//	baseData：基础数据
//	dialogConf：词表弹窗配置
//
// 返回值：
//
//	返回曝光量上传信息，如果配置中的PvUpload为空，则返回nil
func getPvUploadInfo(ctx context.Context, baseData *types.ShowDialogBaseData, dialogConf types.DialogConf) *showDialog.PvUploadInfo {
	pvUploadInfo := &showDialog.PvUploadInfo{}
	if dialogConf.ExtConf.PvUpload.Empty() {
		return nil
	}

	// 曝光量key
	if dialogConf.ExtConf.PvUpload.UniqID != "" {
		pvUploadInfo.PvId = proto.String(dialogConf.ExtConf.PvUpload.UniqID)
	} else if dialogConf.ExtConf.PvUpload.UniqIDDaily != "" {
		t := time.Now()
		// 格式化日期
		date := t.Format("20060102")
		pvUploadInfo.PvId = proto.String(fmt.Sprintf("%s_%s", dialogConf.ExtConf.PvUpload.UniqIDDaily, date))
	}

	// 生成token
	if dialogConf.ExtConf.PvUpload.Token != "" {
		tokenParam := map[string]string{
			"uid":            common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("user_id", ""), common.TTT_STRING).(string),
			"cuid":           common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("cuid", ""), common.TTT_STRING).(string),
			"is_legal":       "1",
			"pop_name":       baseData.Request.GetDialogName(),
			"frequency_rock": dialogConf.ExtConf.PvUpload.FrequencyRock,
			"time":           common.Tvttt(time.Now().Unix(), common.TTT_STRING).(string),
			"pv_id":          *pvUploadInfo.PvId,
		}
		tokenJSON, _ := json.Marshal(tokenParam)
		encryptedToken, err := encrypt(tokenJSON, []byte(types.TiebaDialogSecurityKey))
		if err != nil {
			tbcontext.WarningF(ctx, "aes encrypt fail: err[%v], param[%s]", err, common.ToString(tokenParam))
			return nil
		}
		pvUploadInfo.Token = proto.String(encryptedToken)
	}

	// 粒度锁
	pvUploadInfo.FrequencyRock = proto.String(dialogConf.ExtConf.PvUpload.FrequencyRock)

	return pvUploadInfo
}

// MergeAwardWithCommercialConf 合并 page_sign_awards 和商业配置字段
func mergeAwardWithCommercialConf(ctx context.Context, pageSignTaskAward map[string]interface{}, rawCommercialConf string) map[string]string {
	// 原始转 map[string]string（用于 fallback）
	original := make(map[string]string, len(pageSignTaskAward))
	for k, v := range pageSignTaskAward {
		strVal, ok := v.(string)
		if !ok {
			tbcontext.WarningF(ctx, "pageSignTaskAward[%s] is not string, fallback to original", k)
			return original
		}
		original[k] = strVal
	}

	// 解析 commercial config
	var commercialConf map[string]map[string]interface{}
	if err := json.Unmarshal([]byte(rawCommercialConf), &commercialConf); err != nil {
		tbcontext.WarningF(ctx, "parse commercial config failed: %v", err)
		return original
	}

	// 构造合并结果
	final := make(map[string]string, len(pageSignTaskAward))
	for k, strVal := range original {
		var base map[string]interface{}
		if err := json.Unmarshal([]byte(strVal), &base); err != nil {
			tbcontext.WarningF(ctx, "unmarshal award[%s] failed: %v", k, err)
			return original
		}

		// 合并配置
		if patch, ok := commercialConf[k]; ok {
			for field, val := range patch {
				base[field] = val
			}
		}

		merged, err := json.Marshal(base)
		if err != nil {
			tbcontext.WarningF(ctx, "marshal merged award[%s] failed: %v", k, err)
			return original
		}
		final[k] = string(merged)
	}

	return final
}

// getDataByReplaceParam 接收一个上下文对象ctx、一个ShowDialogBaseData类型的指针baseData和一个字符串列表urlList，返回一个字符串列表
func getDataByReplaceParam(ctx context.Context, urlList []string,
	needReplaceParam map[string]string, needReplaceStatisticsParam bool, baseRequest *uiclient.UIRequest) []string {
	if len(urlList) == 0 {
		return urlList
	}

	if len(needReplaceParam) == 0 {
		return urlList
	}
	statisticsParam := adcounter.AddThirdMonitorLogic(ctx, baseRequest)
	// replace param
	for key, info := range urlList {
		if needReplaceStatisticsParam {
			for k, v := range needReplaceParam {
				info = strings.ReplaceAll(info, k, v)
			}
		} else {
			info = info + "&" + statisticsParam
		}

		urlList[key] = info
	}

	return urlList
}

// getReplaceParam 函数用于获取需要替换的参数
// 参数：
//
//	ctx：上下文对象
//	baseData：ShowDialogBaseData类型指针，表示对话框基础数据
//
// 返回值：
//
//	返回一个map[string]string类型的参数，表示需要替换的参数
func getReplaceParam(ctx context.Context, baseData *types.ShowDialogBaseData) map[string]string {
	objReq := baseData.BaseObj.ObjRequest
	strIP := common.Tvttt(objReq.GetCommonAttr("ip_str", ""), common.TTT_STRING).(string)
	clientType := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", ""), common.TTT_STRING).(string)

	aliAaid := ""
	strCaid := ""
	bootMark := ""
	updateMark := ""
	decodedDaic := ""
	daic := baseData.Request.GetCommon().GetDiac()
	var err error

	if daic != "" {
		decodedDaic, err = url.QueryUnescape(daic)
		if err != nil {
			tbcontext.WarningF(ctx, "url decode fail: err[%v], daic[%s]", err, daic)
		}
	}

	if decodedDaic != "" {
		caidInfo := types.CaidData{}
		err := json.Unmarshal([]byte(decodedDaic), &caidInfo)
		if err != nil {
			tbcontext.WarningF(ctx, "json unmarshal fail: err[%v], daic[%s]", err, daic)
		}
		for _, v := range caidInfo.Caid {
			if v.Vendor == types.CaidVenterAli {
				aliAaid = v.Caid[0].Caid
			}

			if v.Vendor == types.CaidVenterXTY {
				caids := make([]string, len(v.Caid))
				for i, caid := range v.Caid {
					if caid.Version == "" {
						caid.Version = "0"
					}
					caids[i] = caid.Version + "_" + caid.Caid
				}
				result := strings.Join(caids, ",")
				strCaid = url.QueryEscape(result)
			}
		}

		if caidInfo.FactorsData != "" {
			strFactors, err := base64.StdEncoding.DecodeString(caidInfo.FactorsData)
			if err != nil {
				tbcontext.WarningF(ctx, "base64 decode fail: err[%v], caidInfo.FactorsData[%s]", err, caidInfo.FactorsData)
			} else {
				factorsInfo := types.FactorsInfo{}
				err := json.Unmarshal(strFactors, &factorsInfo)
				if err != nil {
					tbcontext.WarningF(ctx, "json unmarshal fail: err[%v], daic[%s]", err, strFactors)
				} else {
					bootMark = factorsInfo.FactorsForCaid.BootSecTime
					updateMark = factorsInfo.FactorsForCaid.SysFileTime
				}
			}
		}
	}
	// 安卓：如果oaid是A10开头的，取中间部分，base32 decode，然后再替换
	strOaid := baseData.Request.GetCommon().GetOaid()
	// 形如 A10-GI3WCNDCHAYDOLJQHEYTALJUMFTDSLLBMIZTSLLFMFQTOYJQG5QWCNLGHA-T55E5TOZ   中间的值为加密后的oaid
	if clientType == "2" && strings.Index(strOaid, "A10-") == 0 {
		arrOaid := strings.Split(strOaid, "-")
		if len(arrOaid) > 1 {
			strOaidStrNew, err := base32.StdEncoding.WithPadding(base32.NoPadding).DecodeString(arrOaid[1])
			if err != nil {
				tbcontext.WarningF(ctx, "oaid base32.StdEncoding Decode err:%v strOaid;%s", err, arrOaid[1])
			}
			strOaid = string(strOaidStrNew)
		}
	}
	needReplaceParam := map[string]string{
		"__IP__":           strIP,
		"__OS__":           clientType,
		"__UA__":           url.QueryEscape(baseData.Request.GetCommon().GetUserAgent()),
		"__ALI_AAID__":     aliAaid,
		"__CAID__":         strCaid,
		"__IMEI__":         baseData.Request.GetCommon().GetXPhoneImei(),
		"__IMEI_MD5__":     php2go.Md5(baseData.Request.GetCommon().GetXPhoneImei()),
		"__MAC__":          baseData.Request.GetCommon().GetMac(),
		"__CUID__":         baseData.Request.GetCommon().GetCuid(),
		"__SHOUBAI_CUID__": baseData.Request.GetCommon().GetShoubaiCuid(),
		"__TS__":           common.Tvttt(time.Now().Unix(), common.TTT_STRING).(string),
		"__ANDROIDID__":    baseData.Request.GetCommon().GetAndroidId(),
		"__BOOT_MARK__":    bootMark,
		"__UPDATE_MARK__":  updateMark,
		"__IDFA__":         baseData.Request.GetCommon().GetIdfa(),
		"__OAID__":         strOaid,
	}
	// iOS：idfa =  "00000000-0000-0000-0000-000000000000"，不替换
	if clientType == "1" && baseData.Request.GetCommon().GetIdfa() == "00000000-0000-0000-0000-000000000000" {
		delete(needReplaceParam, "__IDFA__")
	}
	// oaid =  "00000000-0000-0000-0000-000000000000" ，不替换
	if clientType == "2" && strOaid == "00000000-0000-0000-0000-000000000000" {
		delete(needReplaceParam, "__OAID__")
	}
	// 空值不进行替换
	for k, v := range needReplaceParam {
		if v == "" {
			delete(needReplaceParam, k)
		}
	}
	return needReplaceParam
}

func getReplaceParamJD(ctx context.Context, baseData *types.ShowDialogBaseData) map[string]string {
	objReq := baseData.BaseObj.ObjRequest
	strIP := common.Tvttt(objReq.GetCommonAttr("ip_str", ""), common.TTT_STRING).(string)
	clientType := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_type", ""), common.TTT_STRING).(string)

	aliAaid := ""
	strCaid := ""
	strMD5Caid := ""
	decodedDaic := ""
	daic := baseData.Request.GetCommon().GetDiac()
	var err error

	if daic != "" {
		decodedDaic, err = url.QueryUnescape(daic)
		if err != nil {
			tbcontext.WarningF(ctx, "url decode fail: err[%v], daic[%s]", err, daic)
		}
	}

	if decodedDaic != "" {
		caidInfo := types.CaidData{}
		err := json.Unmarshal([]byte(decodedDaic), &caidInfo)
		if err != nil {
			tbcontext.WarningF(ctx, "json unmarshal fail: err[%v], daic[%s]", err, daic)
		}
		for _, v := range caidInfo.Caid {
			if v.Vendor == types.CaidVenterAli {
				aliAaid = v.Caid[0].Caid
			}

			if v.Vendor == types.CaidVenterXTY {
				caIDs := make([]string, len(v.Caid))
				MD5s := make([]string, len(v.Caid))
				for i, caid := range v.Caid {
					if caid.Version == "" {
						caid.Version = "0"
					}
					caIDs[i] = caid.Version + "_" + caid.Caid
					MD5s[i] = caid.Version + "_" + php2go.Md5(caid.Caid)
				}
				joinCaIDs := strings.Join(caIDs, ",")
				joinMD5s := strings.Join(MD5s, ",")
				strCaid = url.QueryEscape(joinCaIDs)
				strMD5Caid = url.QueryEscape(joinMD5s)
			}
		}
	}
	// 安卓：如果oaid是A10开头的，取中间部分，base32 decode，然后再替换
	strOaid := baseData.Request.GetCommon().GetOaid()
	// 形如 A10-GI3WCNDCHAYDOLJQHEYTALJUMFTDSLLBMIZTSLLFMFQTOYJQG5QWCNLGHA-T55E5TOZ   中间的值为加密后的oaid
	if clientType == "2" && strings.Index(strOaid, "A10-") == 0 {
		arrOaid := strings.Split(strOaid, "-")
		if len(arrOaid) > 1 {
			strOaidStrNew, err := base32.StdEncoding.WithPadding(base32.NoPadding).DecodeString(arrOaid[1])
			if err != nil {
				tbcontext.WarningF(ctx, "oaid base32.StdEncoding Decode err:%v strOaid;%s", err, arrOaid[1])
			}
			strOaid = string(strOaidStrNew)
		}
	}
	needReplaceParam := map[string]string{
		"__IP__":       strIP,
		"__OS__":       clientType,
		"__ALI_AAID__": aliAaid,
		"__CAID__":     strCaid,    // 用户 ios 设备的 caid 信息，格式为： ver_caid
		"__CAID1__":    strMD5Caid, // 用户 ios 设备的 caid 信息，格式为： ver_caidmd5
	}
	if strOaid != "" {
		needReplaceParam["__OAID1__"] = php2go.Md5(strOaid)
		needReplaceParam["__OAID__"] = strOaid
	}

	if baseData.Request.GetCommon().GetXPhoneImei() != "" {
		needReplaceParam["__IMEIIMEI__"] = baseData.Request.GetCommon().GetXPhoneImei()
		needReplaceParam["__IMEI__"] = php2go.Md5(baseData.Request.GetCommon().GetXPhoneImei())
	}

	if baseData.Request.GetCommon().GetIdfa() != "" {
		needReplaceParam["__IDFAIDFA__"] = baseData.Request.GetCommon().GetIdfa()
		needReplaceParam["__IDFA__"] = php2go.Md5(baseData.Request.GetCommon().GetIdfa())
	}

	// iOS：idfa =  "00000000-0000-0000-0000-000000000000"，不替换
	if clientType == "1" && baseData.Request.GetCommon().GetIdfa() == "00000000-0000-0000-0000-000000000000" {
		delete(needReplaceParam, "__IDFA__")
		delete(needReplaceParam, "__IDFAIDFA__")
	}
	// oaid =  "00000000-0000-0000-0000-000000000000" ，不替换
	if clientType == "2" && strOaid == "00000000-0000-0000-0000-000000000000" {
		delete(needReplaceParam, "__OAID__")
		delete(needReplaceParam, "__OAID1__")
	}
	// 空值不进行替换
	for k, v := range needReplaceParam {
		if v == "" {
			delete(needReplaceParam, k)
		}
	}
	return needReplaceParam
}

// encrypt 函数接收两个参数：plainText（明文）和key（密钥），返回加密后的密文字符串和可能发生的错误
func encrypt(plainText, key []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	if block.BlockSize() <= 0 {
		return "", errors.New("block size is zero")
	}

	plainText = PKCS7Padding(plainText, block.BlockSize())
	ciphertext := make([]byte, block.BlockSize()+len(plainText))
	iv := ciphertext[:block.BlockSize()]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext[block.BlockSize():], plainText)

	return base64.URLEncoding.EncodeToString(ciphertext), nil
}

// PKCS7Padding 对给定的密文进行PKCS7填充
//
// 参数：
// ciphertext: 需要进行填充的密文，类型为[]byte
// blockSize: 填充块大小，类型为int
//
// 返回值：
// 填充后的密文，类型为[]byte
//
// 注意：
// 如果blockSize小于等于0，则不进行填充，直接返回原密文
func PKCS7Padding(ciphertext []byte, blockSize int) []byte {
	if blockSize <= 0 {
		return ciphertext
	}
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}
