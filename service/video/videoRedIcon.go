package video

import (
	"context"
	"encoding/json"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/conf"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/threadlist"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/utilconst"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/forum"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/video"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/cmap"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/video/redIcon"
	"icode.baidu.com/baidu/tieba-server-go/golib2/errno"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func VideoRedIcon(ctx context.Context, baseData *types.CRedIconBaseData, response *redIcon.VideoRedIconResIdl) int {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest
	staticField.IntUID = common.Tvttt(objReq.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	if staticField.IntUID <= 0 {
		tbcontext.WarningF(ctx, "param invalid! uid <= 0")
		return tiebaerror.ERR_MO_PARAM_INVALID
	}

	errorNo := getRedIcon(ctx, baseData)
	if errorNo != tiebaerror.ERR_SUCCESS {
		return errorNo
	}

	if len(staticField.VideoTabRedIcon.GetRedIconList()) > 0 {
		response.Data = &redIcon.VideoRedIconRes{
			RedIconList: []*redIcon.RedIcon{
				{
					Type:       proto.Int32(staticField.VideoTabRedIcon.GetRedIconList()[0].GetType()),
					Text:       proto.String(staticField.VideoTabRedIcon.GetRedIconList()[0].GetText()),
					Image:      proto.String(staticField.VideoTabRedIcon.GetRedIconList()[0].GetImage()),
					ThreadList: make([]*client.ThreadInfo, 0),
				},
			},
		}

		for _, threadInfo := range staticField.ThreadInfo {
			response.Data.RedIconList[0].ThreadList = append(response.Data.RedIconList[0].ThreadList, threadInfo.ClientThreadInfo)
		}
	}

	return tiebaerror.ERR_SUCCESS
}

func getRedIcon(ctx context.Context, baseData *types.CRedIconBaseData) int {
	staticField := baseData.StaticField
	objReq := baseData.BaseObj.ObjRequest

	clientType := common.Tvttt(objReq.GetCommonAttr("client_type", 0), common.TTT_INT).(int)
	input := map[string]interface{}{
		"user_id":        staticField.IntUID,
		"cuid":           objReq.GetCommonAttr("cuid", ""),
		"client_type":    objReq.GetCommonAttr("client_type", 0),
		"client_version": objReq.GetCommonAttr("client_version", ""),
		"user_ip":        objReq.GetCommonAttr("ip_int", 0),
		"net_type":       objReq.GetCommonAttr("net_type", 0),
		"sample_id":      objReq.GetCommonAttr("sample_id", ""),
		"red_icon_type":  0,
	}
	res := new(video.GetVideoTabRedIconRes)
	err := tbservice.Call(ctx, "video", "getVideoTabRedIcon", input, res, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "Call video:getVideoTabRedIcon failed. input:%v, output:%v, err:%v", input, common.ToString(res), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	staticField.VideoTabRedIcon = res.GetData()

	threadIds := make([]uint64, 0)
	if len(res.GetData().GetRedIconList()) > 0 {
		for _, threadInfo := range res.GetData().GetRedIconList()[0].GetThreadList() {
			threadIds = append(threadIds, uint64(threadInfo.GetTid()))
		}
	}

	if len(threadIds) > 0 {
		threadListParam := threadlist.ThreadListParam{
			ArrTid:        threadIds,
			ClientType:    common.Tvttt(objReq.GetCommonAttr("client_type", 0), common.TTT_INT).(int),
			ClientVersion: common.Tvttt(objReq.GetCommonAttr("client_version", ""), common.TTT_STRING).(string),
			UserId:        staticField.IntUID,
			ArrStrategy:   cmap.New(),
		}
		threadInfos := threadListParam.GetThreadList(ctx, baseData.BaseObj)
		if len(threadInfos) == 0 {
			tbcontext.WarningF(ctx, "Molib_Util_ThreadList_GetThreadList::getThreadList empty!  thread_ids: %v", threadIds)
			return tiebaerror.ERR_PARAM_ERROR
		}

		staticField.ThreadInfo = threadInfos

		// 查询forum信息，并且拼接数据
		forumIds := make([]uint32, 0)
		for _, threadInfo := range staticField.ThreadInfo {
			if threadInfo.ClientThreadInfo.GetFid() != 0 {
				forumIds = append(forumIds, uint32(threadInfo.ClientThreadInfo.GetFid()))
			}
		}

		forumExtInput := map[string]interface{}{
			"forum_id": forumIds,
		}
		forumRes := &forum.MgetBtxInfoExRes{}
		err = tbservice.Call(ctx, "forum", "mgetBtxInfoEx", forumExtInput, forumRes, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || forumRes.Errno == nil || forumRes.GetErrno() != errno.CodeSuccess {
			tbcontext.WarningF(ctx, "call forum::mgetBtxInfoEx error: %v, res: %+v", err, forumRes)
		} else {
			for _, threadInfo := range staticField.ThreadInfo {
				if threadInfo.ClientThreadInfo.GetFid() != 0 && forumRes.GetOutput()[uint32(threadInfo.ClientThreadInfo.GetFid())] != nil {
					forumInfo := forumRes.GetOutput()[uint32(threadInfo.ClientThreadInfo.GetFid())]
					threadInfo.ClientThreadInfo.ForumInfo = &client.SimpleForum{
						Id:           proto.Int64(int64(forumInfo.GetForumName().GetForumId())),
						Name:         proto.String(forumInfo.GetForumName().GetForumName()),
						Avatar:       proto.String(forumInfo.GetCard().GetAvatar()),
						MemberNum:    proto.Int32(int32(forumInfo.GetStatistics().GetMemberCount())),
						PostNum:      proto.Int32(int32(forumInfo.GetStatistics().GetPostNum())),
						ThemeColor:   getThemeColor(ctx, forumInfo, clientType),
						RecommendTip: proto.String(""),
					}
					threadInfo.ClientThreadInfo.RecomTagIcon = proto.String(forumInfo.GetCard().GetAvatar())
				}
			}
		}
	}

	return tiebaerror.ERR_SUCCESS
}

func getThemeColor(ctx context.Context, forumInfo *forum.BtxInfo, clientType int) *client.ThemeColorInfo {
	//10.0返回吧主题色
	//如果没有返回默认色
	themeColor := &client.ThemeColorInfo{
		Day: &client.ThemeElement{
			CommonColor: proto.String(utilconst.DAY_THEME_COLOR_DEFAULT),
			DarkColor:   proto.String(utilconst.DAY_DARK_COLOR_DEFAULT),
			LightColor:  proto.String(utilconst.DAY_LIGHT_COLOR_DEFAULT),
			FontColor:   proto.String(utilconst.DAY_FONT_COLOR_DEFAULT),
		},
		Night: &client.ThemeElement{
			CommonColor: proto.String(utilconst.NIGHT_THEME_COLOR_DEFAULT),
			DarkColor:   proto.String(utilconst.NIGHT_DARK_COLOR_DEFAULT),
			LightColor:  proto.String(utilconst.NIGHT_LIGHT_COLOR_DEFAULT),
			FontColor:   proto.String(utilconst.NIGHT_FONT_COLOR_DEFAULT),
		},
		Dark: &client.ThemeElement{
			CommonColor: proto.String(utilconst.DARK_THEME_COLOR_DEFAULT),
			DarkColor:   proto.String(utilconst.DARK_DARK_COLOR_DEFAULT),
			LightColor:  proto.String(utilconst.DARK_LIGHT_COLOR_DEFAULT),
			FontColor:   proto.String(utilconst.DARK_FONT_COLOR_DEFAULT),
		},
	}

	themeColorStr := forumInfo.GetAttrs().GetThemeColor()
	if themeColorStr != "" {
		// 因主题色返回格式改变，主要为了兼容第一版
		themeColorAttr := new(client.ThemeColorInfo)
		err := json.Unmarshal([]byte(themeColorStr), themeColorAttr)
		if err != nil {
			tbcontext.WarningF(ctx, "change to ThemeColorInfo fail: %v", err)
		}

		if themeColorAttr.GetDay() != nil && themeColorAttr.GetNight() != nil {
			themeColor = themeColorAttr
		}
	}

	//10.0返回吧主题色
	//如果没有返回默认色
	clientStr := "android"
	if clientType == clientvers.CLIENT_TYPE_IPHONE {
		clientStr = "ios"
	}

	var pattern []string
	switch forumInfo.GetDir().GetLevel_1Name() {
	case utilconst.DIR_CATEGORY_YULE, utilconst.DIR_CATEGORY_ZONGYI, utilconst.DIR_CATEGORY_ZHUIJU, utilconst.DIR_CATEGORY_MOVIE, utilconst.DIR_CATEGORY_FUN:
		pattern = conf.ArrPatternImage[clientStr]["entertainment"]
	case utilconst.DIR_CATEGORY_SPORT:
		pattern = conf.ArrPatternImage[clientStr]["sport"]
	case utilconst.DIR_CATEGORY_NOVEL, utilconst.DIR_CATEGORY_AREA, utilconst.DIR_CATEGORY_NATURE:
		pattern = conf.ArrPatternImage[clientStr]["culture"]
	case utilconst.DIR_CATEGORY_LIFE:
		pattern = conf.ArrPatternImage[clientStr]["life"]
	case utilconst.DIR_CATEGORY_GAME, utilconst.DIR_CATEGORY_CARTOON, utilconst.DIR_CATEGORY_CAMPUS:
		pattern = conf.ArrPatternImage[clientStr]["young"]
	default:
		pattern = conf.ArrPatternImage[clientStr]["default"]
	}

	index := int(forumInfo.GetForumName().GetForumId()) % len(pattern)
	if themeColor.GetDay() != nil {
		themeColor.GetDay().PatternImage = proto.String(pattern[index])
	}
	if themeColor.GetNight() != nil {
		themeColor.GetNight().PatternImage = proto.String(pattern[index])
	}
	if themeColor.GetDark() != nil {
		themeColor.GetDark().PatternImage = proto.String(pattern[index])
	}

	return themeColor
}
