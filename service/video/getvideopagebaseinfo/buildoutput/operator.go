package buildoutput

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"

	getvideopagebaseinfoproto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/video/getVideoPageBaseInfo"

	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/forumperm"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/threadinfo"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/threadstore"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/useragreeinfo"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/userfollow"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/video/getvideopagebaseinfo/privateparams"
)

// OperatorBuildOutput 算子定义
type OperatorBuildOutput struct {
	CommonParams  commonparams.CommonParams   `inject:"canLost=false,canNil=false"`
	PrivateParams privateparams.PrivateParams `inject:"canLost=false,canNil=false"`
	ThreadInfo    threadinfo.ThreadInfo       `inject:"canLost=false,canNil=false"`
	ForumPerm     forumperm.ForumPerm         `inject:"canLost=false,canNil=false"`
	ThreadStore   threadstore.ThreadStore     `inject:"canLost=false,canNil=false"`
	UserAgreeInfo useragreeinfo.UserAgreeInfo `inject:"canLost=false,canNil=false"`
	UserFollow    userfollow.UserFollow       `inject:"canLost=false,canNil=false"`

	output *getvideopagebaseinfoproto.GetVideoPageBaseInfoRes
}

func (op *OperatorBuildOutput) DoImpl(ctx *engine.Context) error {
	p := NewProcessBuildOutput()
	p.CommonParams = op.CommonParams
	p.PrivateParams = op.PrivateParams
	p.ThreadInfo = op.ThreadInfo
	p.ForumPerm = op.ForumPerm
	p.ThreadStore = op.ThreadStore
	p.UserAgreeInfo = op.UserAgreeInfo
	p.UserFollow = op.UserFollow

	err := p.Process(ctx.CallerCtx())
	if err != nil {
		return err
	}

	// 导出数据，供controller输出使用
	ctx.MustRegisterInstance(p.output)
	return nil
}
