package buildoutput

import (
	"context"
	"fmt"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"

	postProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post"
	getvideopagebaseinfoproto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/video/getVideoPageBaseInfo"

	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/commonparams"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/forumperm"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/threadinfo"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/threadstore"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/useragreeinfo"
	"icode.baidu.com/baidu/tieba-server-go/operator-common/operators/userfollow"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/service/video/getvideopagebaseinfo/privateparams"
)

// opName 算子名称
const opName = "buildoutput"

type ProcessBuildOutput struct {
	CommonParams  commonparams.CommonParams
	PrivateParams privateparams.PrivateParams

	ThreadInfo    threadinfo.ThreadInfo
	ForumPerm     forumperm.ForumPerm
	ThreadStore   threadstore.ThreadStore
	UserAgreeInfo useragreeinfo.UserAgreeInfo
	UserFollow    userfollow.UserFollow

	output *getvideopagebaseinfoproto.GetVideoPageBaseInfoRes // 接口返回

	threadInfoData *postProto.ThreadInfo
}

func NewProcessBuildOutput() *ProcessBuildOutput {
	p := new(ProcessBuildOutput)
	p.output = &getvideopagebaseinfoproto.GetVideoPageBaseInfoRes{}
	return p
}

func (p *ProcessBuildOutput) Process(ctx context.Context) error {
	// 不可丢失核心数据校验
	if err := p.checkCoreData(); err != nil {
		return err
	}

	// 构建返回字段
	if err := p.buildOutput(); err != nil {
		return err
	}

	return nil
}

// checkCoreData 不可丢失核心数据校验
func (p *ProcessBuildOutput) checkCoreData() error {
	if p.PrivateParams == nil || p.PrivateParams.GetTID() == 0 {
		return fmt.Errorf("PrivateParams is nil or tid is empty")
	}

	if p.CommonParams == nil {
		return fmt.Errorf("CommonParams is nil")
	}

	if p.ThreadInfo == nil || p.ThreadInfo.GetRawThreads() == nil {
		return fmt.Errorf("ThreadInfo is nil")
	}
	threadInfo, ok := p.ThreadInfo.GetRawThreads()[p.PrivateParams.GetTID()]
	if !ok {
		return fmt.Errorf("threadInfo is nil")
	}
	p.threadInfoData = threadInfo
	return nil
}

// buildOutput 构建返回数据
func (p *ProcessBuildOutput) buildOutput() error {
	p.output.ThreadInfo = &getvideopagebaseinfoproto.GetVideoPageBaseInfoThreadInfo{
		PostNum:    proto.Int32(int32(p.threadInfoData.GetPostNum())),
		AgreeNum:   proto.Int32(int32(p.threadInfoData.GetAgreeNum())),
		ShareNum:   proto.Int64(p.threadInfoData.GetShareNum()),
		CollectNum: proto.Uint32(uint32(p.threadInfoData.GetCollectNum())),
	}
	// 是否关注吧
	if p.ForumPerm != nil {
		p.output.ForumInfo = &getvideopagebaseinfoproto.GetVideoPageBaseInfoForumInfo{
			IsLiked: proto.Uint32(p.ForumPerm.GetPerm().GetGrade().GetIsLike()),
		}
	}

	// 是否关注作者
	if p.UserFollow != nil {
		p.output.Author = &getvideopagebaseinfoproto.GetVideoPageBaseInfoAuthor{
			Focus: proto.Uint32(p.UserFollow.GetUserFollow(uint64(p.threadInfoData.GetUserId()))),
		}
	}
	// 贴子收藏状态
	if p.ThreadStore != nil {
		if threadStore, ok := p.ThreadStore.GetThreadStoreType()[p.PrivateParams.GetTID()]; ok {
			p.output.ThreadInfo.CollectStatus = proto.Int32(int32(threadStore.GetReply().GetKeptType()))
			p.output.ThreadInfo.CollectMarkPid = proto.String(cast.ToString(threadStore.GetReply().GetMarkPid()))
		}
	}

	// 主贴点赞状态
	if p.UserAgreeInfo != nil {
		if threadAgree := p.UserAgreeInfo.GetThreadAgreeInfoByUID(p.CommonParams.GetUserID()); threadAgree != nil {
			p.output.ThreadInfo.HasAgree = proto.Int32(threadAgree.GetHasAgree())
		}
	}
	return nil
}
