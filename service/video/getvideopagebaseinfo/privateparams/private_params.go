package privateparams

import (
	"context"

	"github.com/spf13/cast"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/client"
)

// opName 算子名称
const opName = "privateparams"

type ProcessPrivateParams struct {
	baseAction *client.UIBaseAction

	ForumID uint32 // 贴吧ID
	TID     uint64 // 贴子ID
}

func NewProcessPrivateParams() *ProcessPrivateParams {
	return &ProcessPrivateParams{}
}

func (p *ProcessPrivateParams) Process(ctx context.Context) error {
	p.ForumID = cast.ToUint32(p.baseAction.GetOriginalParam("forum_id"))
	p.TID = cast.ToUint64(p.baseAction.GetOriginalParam("tid"))
	if p.TID <= 0 {
		return errParamError
	}
	return nil
}

func (p *ProcessPrivateParams) GetTID() uint64 {
	return p.TID
}

func (p *ProcessPrivateParams) GetForumID() uint32 {
	return p.ForumID
}
