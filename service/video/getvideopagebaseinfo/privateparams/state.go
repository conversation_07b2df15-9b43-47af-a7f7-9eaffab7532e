package privateparams

import (
	"icode.baidu.com/baidu/ps-se-go/exgraph/engine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/base/stcdefine"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
)

var (
	ErrCodeScope = engine.NewStateScope("get_video_page_base_info" + opName)

	errParamError = ErrCodeScope.NewState(tiebaerror.ERR_PARAM_ERROR, stcdefine.GetErrMsg(tiebaerror.ERR_PARAM_ERROR))
)
