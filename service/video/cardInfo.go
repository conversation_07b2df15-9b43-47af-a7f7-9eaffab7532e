package video

import (
	"context"

	"google.golang.org/protobuf/proto"
	protocommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/video/cardInfo"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

func CardInfo(ctx context.Context, baseData *types.CCardInfoBaseData, response *cardInfo.VideoCardInfoResIdl) int {
	input := &protocommon.GetVideoSearchByURLReq{
		Url: proto.String(baseData.Request.GetUrl()),
	}
	output := &protocommon.GetVideoSearchByURLRes{}
	options := []tbservice.Option{
		tbservice.WithConverter(tbservice.JSONITER),
		tbservice.WithRalName("common_go"),
	}
	err := tbservice.Call(ctx, "common", "getVideoSearchByURL", input, output, options...)

	if err != nil || output == nil || output.Errno == nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call chat:getVideoSearchByURL fail, err:[%+v], input:[%s], output:[%s]", err, common.ToString(input), common.ToString(output))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	if output.Data == nil || output.Data.Info == nil {
		response.Data = &cardInfo.VideoCardInfoRes{}
		return tiebaerror.ERR_SUCCESS
	}

	info := output.Data.GetInfo()
	response.Data = &cardInfo.VideoCardInfoRes{
		ExternalLink: proto.String(baseData.Request.GetUrl()),
		Title:        proto.String(info.GetTitle()),
		VideoUrl:     proto.String(info.GetPlayUrl()),
		Tips:         proto.String("您已离开贴吧提供的页面，请注意甄别有害内容，保护个人信息。"),
		VideoHeight:  info.Height,
		VideoWidth:   info.Width,
	}
	if info.Duration != nil {
		response.Data.VideoDuration = proto.Int64(int64(info.GetDuration()))
	}

	return tiebaerror.ERR_SUCCESS
}
