package video

import (
	"context"
	"fmt"
	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/threadlist"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/anti"
	protocommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/feed"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/video"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/cmap"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/stlog"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/video/concernTab"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"

	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"strconv"
	"strings"
)

func ConcernTab(ctx context.Context, baseData *types.CConcernTabBaseData, response *concernTab.ConcernTabResIdl) int {
	objReq := baseData.BaseObj.ObjRequest
	staticField := baseData.StaticField
	staticField.UserId = common.Tvttt(objReq.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	//staticField.UserId = 155315065
	if staticField.UserId <= 0 {
		tbcontext.WarningF(ctx, "uid is invalid, uid=%d", staticField.UserId)
		return tiebaerror.ERR_MO_USER_NOT_LOGIN
	}

	if !checkFollowsByUid(ctx, staticField) {
		//没有关注的人
		staticField.StringShowWord = "嚯~还没人引起你的关注"

		//取吧友在看数据
		getFriendThreads(ctx, baseData)
		staticField.SourceType = 1
	} else {
		//有关注的人，则取关注列表数据
		getConcernThreads(ctx, baseData)

		//关注列表无数据时，取吧友在看数据
		if len(staticField.ThreadInfoList) == 0 {
			staticField.StringShowWord = "我的心在等待，怎么没人发动态"
			getFriendThreads(ctx, baseData)
			staticField.SourceType = 1
		}
	}

	response.GetData().ConcernList = &client.ConcernList{
		ThreadList: staticField.ConcernThreadInfoList,
		FeedId:     proto.Int64(common.Tvttt(staticField.IntLastFeedId, common.TTT_INT64).(int64)),
		ShowWord:   proto.String(staticField.StringShowWord),
		HasMore:    proto.Int32(int32(staticField.IntHasMore)),
	}
	response.GetData().ForumFriends = staticField.ArrForumFriends

	stlog.AddLog(ctx, "data_cnt", len(staticField.ThreadInfoList))
	stlog.AddLog(ctx, "source_type", staticField.SourceType)
	return tiebaerror.ERR_SUCCESS

}

func checkFollowsByUid(ctx context.Context, staticField *types.ConcernTabStaticField) bool {
	input := map[string]interface{}{
		"user_id": staticField.UserId,
		"offset":  0,
		"limit":   3,
		"type":    2,
	}
	followRes := new(user.GetFollowAndFollowedByUidRes)
	err := tbservice.Call(ctx, "user", "getFollowAndFollowedByUid", input, followRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || followRes.Errno == nil || followRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "Call user:getFollowAndFollowedByUid failed. input:%v, output:%v, err:%v", input, common.ToString(followRes), err)
		return false
	}

	if followRes.GetConcern().GetTotalCount() == 0 {
		tbcontext.WarningF(ctx, "Call user:getFollowAndFollowedByUid success,total_count=0. input:%v", input)
		return false
	}

	return true
}

func getFriendThreads(ctx context.Context, baseData *types.CConcernTabBaseData) {
	staticField := baseData.StaticField

	//从词表中取出有效uid
	arrUids := getFriendsUid(ctx)

	//根据uid取出视频作品tids
	getWorksByUid(ctx, staticField, arrUids)

	//组装threadInfo结构体数据
	getThreadInfoList(ctx, baseData)

	arrRetThread := make(map[int64][]*client.ThreadInfo)
	for _, threadInfo := range staticField.ThreadInfoList {
		if _, ok := arrRetThread[threadInfo.GetAuthor().GetId()]; !ok {
			arrRetThread[threadInfo.GetAuthor().GetId()] = make([]*client.ThreadInfo, 0)
		}
		arrRetThread[threadInfo.GetAuthor().GetId()] = append(arrRetThread[threadInfo.GetAuthor().GetId()], threadInfo)
	}

	for _, threadList := range arrRetThread {
		//视频沉浸态关注Tab打底数据话题富文本解析版本控制
		if staticField.IntClientType == clientvers.CLIENT_TYPE_ANDROID && clientvers.Compare("12.13.2.0", staticField.StrClientVersion) < 0 {
			for _, tmp := range threadList {
				tmp.RichTitle = nil
			}
		}
		tmpUser := new(client.ThreadUser)
		tmpUser.ThreadList = threadList
		tmpUser.UserInfo = threadList[0].GetAuthor()

		//粉丝数兜底。
		tmpUser.UserInfo.FansNum = proto.Int32(0)
		if _, ok := staticField.ArrFansNum[tmpUser.UserInfo.GetId()]; ok {
			tmpUser.UserInfo.FansNum = proto.Int32(int32(staticField.ArrFansNum[tmpUser.UserInfo.GetId()]))
		}
		staticField.ArrForumFriends = append(staticField.ArrForumFriends, tmpUser)
	}
}

func getThreadInfoList(ctx context.Context, baseData *types.CConcernTabBaseData) {
	staticField := baseData.StaticField
	threadListParam := threadlist.ThreadListParam{
		ArrTid:          staticField.ArrThreadTids,
		ClientType:      staticField.IntClientType,
		ClientVersion:   staticField.StrClientVersion,
		UserId:          staticField.UserId,
		ArrStrategy:     cmap.New(),
		NeedCollectData: true,
	}

	threadInfos := threadListParam.GetThreadList(ctx, baseData.BaseObj)
	//LogParam
	threadInfoMap := make(map[int64]*client.ThreadInfo)
	var albumIDs []*protocommon.ThreadAlbum

	for _, threadInfo := range threadInfos {
		albumID := threadInfo.ClientThreadInfo.GetAblumInfo().GetId()
		if albumID > 0 {
			albumIDs = append(albumIDs, &protocommon.ThreadAlbum{ThreadId: common.GetUint64Ptr(uint64(threadInfo.GetThreadId())),
				AlbumId: proto.Uint64(uint64(albumID))})
		}

		threadInfoMap[threadInfo.ClientThreadInfo.GetTid()] = threadInfo.ClientThreadInfo
		threadInfoMap[threadInfo.ClientThreadInfo.GetTid()].LogParam = []*client.FeedKV{
			{
				Key:   proto.String("fid"),
				Value: proto.String(strconv.FormatInt(threadInfo.ClientThreadInfo.GetFid(), 10)),
			},
			{
				Key:   proto.String("tid"),
				Value: proto.String(strconv.FormatInt(threadInfo.ClientThreadInfo.GetTid(), 10)),
			},
			{
				Key:   proto.String("card_type"),
				Value: proto.String("video"),
			},
		}
	}

	for _, tid := range staticField.ArrThreadTids {
		if _, ok := threadInfoMap[int64(tid)]; ok {
			staticField.ThreadInfoList = append(staticField.ThreadInfoList, threadInfoMap[int64(tid)])
		}
	}
	if len(albumIDs) > 0 {
		albumInput := &protocommon.MgetThreadAlbumInfoReq{
			ThreadAlbums: albumIDs,
		}

		ret := &protocommon.MgetThreadAlbumInfoRes{}
		err := tbservice.Call(ctx, "common", "mgetThreadAlbumInfoByTids", albumInput, ret, tbservice.WithConverter(tbservice.JSONITER))

		if err != nil {
			tbcontext.WarningF(ctx, "get mgetThreadAlbumInfoByTids failed: %s, input:%v", err.Error(), albumInput)
		} else if ret.GetErrno() != tiebaerror.ERR_SUCCESS || ret.Data == nil {
			tbcontext.WarningF(ctx, "get mgetThreadAlbumInfoByTids return error: input:%v output:%v", albumInput, ret)
		} else {
			buildAlbumInfo(ctx, ret.Data, staticField)
		}
	}

	if baseData.BaseObj.Req.HeaderDefault("Bfe_logid", "") == "11434792816684676606" {
		fmt.Println(1)
	}

}

func getWorksByUid(ctx context.Context, staticField *types.ConcernTabStaticField, arrUids []int64) {
	multi := tbservice.Multi()
	for i, uid := range arrUids {
		arrParam := &tbservice.Parameter{
			Service: "video",
			Method:  "getWorkListByUserId",
			Input: map[string]interface{}{
				"user_id":     uid,
				"rn":          6,
				"pn":          1,
				"work_status": 1,
			},
			Output: &video.GetWorkListByUserIdRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getWorkListByUserId"+strconv.Itoa(i), arrParam)
	}

	arrParam := &tbservice.Parameter{
		Service: "user",
		Method:  "getUserFollowedInfo",
		Input: map[string]interface{}{
			"user_id":     staticField.UserId,
			"req_user_id": arrUids,
		},
		Output: &user.GetUserFollowedInfoRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getUserFollowedInfo", arrParam)

	multi.Call(ctx)

	arrUidTids := make(map[int64][]uint64)
	for i, uid := range arrUids {
		workResInter, err := multi.GetResult(ctx, "getWorkListByUserId"+strconv.Itoa(i))
		workRes := workResInter.(*video.GetWorkListByUserIdRes)
		if err != nil || workRes.Errno == nil || workRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call video::getWorkListByUserId fail: %v, output: %v", err, common.ToString(workRes))
			continue
		}

		//每个用户下至少保证3个视频，故小于3个视频的用户丢弃
		if len(workRes.GetData()) < 3 {
			continue
		}

		if _, ok := arrUidTids[uid]; !ok {
			arrUidTids[uid] = make([]uint64, 0)
		}

		for _, tmp := range workRes.GetData() {
			arrUidTids[uid] = append(arrUidTids[uid], uint64(tmp.GetThreadId()))
		}
	}

	i := 0
	arrTids := make([]uint64, 0)
	for _, tids := range arrUidTids {
		if i >= 10 {
			break
		}
		arrTids = append(arrTids, tids...)
		i++
	}
	staticField.ArrThreadTids = arrTids

	followResInter, err := multi.GetResult(ctx, "getUserFollowedInfo")
	followRes := followResInter.(*user.GetUserFollowedInfoRes)
	if err != nil || followRes.Errno == nil || followRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call user::getUserFollowedInfo fail: %v, output: %v", err, common.ToString(followRes))
		return
	}

	for _, fansNum := range followRes.GetResUserInfos() {
		if fansNum.FollowedCount == nil {
			tbcontext.AddNotice(ctx, "a user has no followed_count", "1")
			continue
		}
		staticField.ArrFansNum[fansNum.GetUserId()] = fansNum.GetFollowedCount()
	}

}

func getFriendsUid(ctx context.Context) []int64 {
	wordRes, err := wordserver.QueryKey(ctx, "tb_wordlist_redis_video_concern_friends", "uid")
	if err != nil {
		tbcontext.WarningF(ctx, "call tb_wordlist_redis_video_concern_friends fail: %v", err)
		return nil
	}

	allUids := strings.Split(wordRes, ",")
	//虽然页面只展示10个用户，但考虑到后面有过滤，故取15个用户
	uidNum := 15
	if len(allUids) < 15 {
		uidNum = len(allUids)
	}
	allUids = allUids[:uidNum]

	arrWokerKey := make([]string, 0)
	for _, uid := range allUids {
		arrWokerKey = append(arrWokerKey, uid+"_creator")
	}

	worksRes, err := wordserver.QueryKeys(ctx, "tb_wordlist_redis_pc_works", arrWokerKey)
	if err != nil {
		tbcontext.WarningF(ctx, "call tb_wordlist_redis_pc_works fail: %v", err)
		return nil
	}

	arrUids := make([]int64, 0)
	for i, uid := range allUids {
		if worksRes[i] != "" {
			arrUids = append(arrUids, common.Tvttt(uid, common.TTT_INT64).(int64))
		}
	}

	multi := tbservice.Multi()
	for i, uid := range arrUids {
		arrParam := &tbservice.Parameter{
			Service: "anti",
			Method:  "antiUserBlockQuery",
			Input: map[string]interface{}{
				"req": map[string]interface{}{
					"user_id":  uid,
					"forum_id": 0,
				},
			},
			Output: &anti.AntiUserBlockQueryRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "antiUserBlockQuery"+strconv.Itoa(i), arrParam)
	}

	multi.Call(ctx)

	uids := make([]int64, 0)
	for i, uid := range arrUids {
		resInter, err := multi.GetResult(ctx, "antiUserBlockQuery"+strconv.Itoa(i))
		res := resInter.(*anti.AntiUserBlockQueryRes)
		if err != nil || res.Errno == nil || res.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "call anti::antiUserBlockQuery fail: %v, output: %v", err, common.ToString(res))
			continue
		}

		if res.GetIsBlock() != 0 {
			tbcontext.WarningF(ctx, "user is closed! uid: %d, blockInfo: %v", uid, common.ToString(res))
			continue
		}
		uids = append(uids, uid)
	}

	return uids
}

func getConcernThreads(ctx context.Context, baseData *types.CConcernTabBaseData) {
	staticField := baseData.StaticField

	//获取关注列表数据的tids
	getConcernTids(ctx, staticField, baseData)

	//组装threadInfo结构体数据
	getThreadInfoList(ctx, baseData)

	staticField.ConcernThreadInfoList = staticField.ThreadInfoList
}

func getConcernTids(ctx context.Context, staticField *types.ConcernTabStaticField, baseData *types.CConcernTabBaseData) {
	input := map[string]interface{}{
		"user_id":         staticField.UserId,
		"feed_id":         staticField.IntFeedId,
		"flip":            staticField.LoadType,
		"limit":           staticField.Limit,
		"bjh_thread_type": staticField.IntBjhThreadType,
	}
	listRes := new(feed.GetFeedNewsListRes)
	err := tbservice.Call(ctx, "post", "getFeedNewsList", input, listRes, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || listRes.Errno == nil || listRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "Call post:getFeedNewsList failed. input:%v, output:%v, err:%v", input, common.ToString(listRes), err)
		return
	}
	if len(listRes.GetData().GetFeedList()) == 0 {
		tbcontext.WarningF(ctx, "call post::getFeedNewsList success,but data is null, res: %s", common.ToString(listRes))
		return
	}

	staticField.IntLastFeedId = listRes.GetData().GetLastId()
	staticField.IntHasMore = int(listRes.GetData().GetHasMore())
	for _, feedInfo := range listRes.GetData().GetFeedList() {
		if feedInfo.GetFeedInfo().GetTid() != 0 {
			staticField.ArrThreadTids = append(staticField.ArrThreadTids, feedInfo.GetFeedInfo().GetTid())
		}
	}
}

func buildAlbumInfo(ctx context.Context, albumInfos map[int64]*protocommon.ThreadAlbumInfo, staticField *types.ConcernTabStaticField) {
	if staticField.ThreadInfoList != nil {
		for _, threadClient := range staticField.ThreadInfoList {
			if threadClient.AblumInfo.GetId() > 0 {
				albumInfo, ok := albumInfos[threadClient.GetTid()]
				if !ok || albumInfo == nil {
					continue
				}
				threadClient.AblumInfo = &client.ThreadAblum{
					Id:    common.GetInt32Ptr(int32(threadClient.AblumInfo.GetId())),
					TabId: common.GetInt64Ptr(507),
					Type:  albumInfo.Type,
					Url: common.GetStringPtr(fmt.Sprintf(
						"https://tieba.baidu.com/mo/q/hybrid-main-user/collectionCenter?customfullscreen=1&nonavigationbar=1&tid=%d&album_id=%d&album_type=%d",
						threadClient.GetTid(), threadClient.GetAblumInfo().GetId(), albumInfo.Type,
					)),
					Title:          albumInfo.Title,
					ThreadSortText: common.GetStringPtr(fmt.Sprintf("第%d贴", albumInfo.GetThreadSort())),
					Tid:            proto.Int64(threadClient.GetTid()),
				}
			}
		}
	}

}
