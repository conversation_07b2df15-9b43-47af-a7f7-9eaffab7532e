package task

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/usertask"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/task/commitTask"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
)

type CommitTaskIns struct {
	userID        int64
	clientType    int32
	clientVersion string
	cuid          string
}

func NewCommitTask(baseData *types.CommitTaskBaseData) *CommitTaskIns {
	obj := baseData.BaseObj
	cuidIos, _ := common.Tvttt(obj.ObjRequest.GetCommonAttr("shoubai_cuid", ""), common.TTT_STRING).(string)
	cuidAndroid, _ := common.Tvttt(obj.ObjRequest.GetCommonAttr("cuid_galaxy2", ""), common.TTT_STRING).(string)
	userID, _ := common.Tvttt(obj.ObjRequest.GetCommonAttr("user_id", 0), common.TTT_INT64).(int64)
	clientType := baseData.Request.GetCommon().GetXClientType()
	cuid := cuidAndroid
	if clientType == clientvers.CLIENT_TYPE_IPHONE {
		cuid = cuidIos
	}
	return &CommitTaskIns{
		userID:        userID,
		clientType:    clientType,
		clientVersion: baseData.Request.GetCommon().GetXClientVersion(),
		cuid:          cuid,
	}
}

// Execute 提交任务
func (c *CommitTaskIns) Execute(ctx context.Context, baseData *types.CommitTaskBaseData, response *commitTask.CommitTaskResIdl) int {
	if baseData == nil || baseData.Request == nil {
		tbcontext.FatalF(ctx, "invalid baseData")
		return tiebaerror.ERR_PARAM_ERROR
	}
	if c.userID <= 0 {
		tbcontext.WarningF(ctx, "user id empty")
		return tiebaerror.ERR_PARAM_ERROR
	}
	req := baseData.Request
	if c.clientType <= 0 || c.clientVersion == "" || c.cuid == "" || req.GetActType() == "" || req.GetCallFrom() == "" {
		tbcontext.WarningF(ctx, "invalid param, input:["+common.ToString(req)+"]")
		return tiebaerror.ERR_PARAM_ERROR
	}
	input := &usertask.DoUserTaskByActTypeReq{
		UserId:          proto.Uint64(uint64(c.userID)),
		Cuid:            proto.String(c.cuid),
		ActType:         proto.String(baseData.Request.GetActType()),
		CallFrom:        proto.String(baseData.Request.GetCallFrom()),
		ClientType:      proto.Int64(int64(c.clientType)),
		ClientVersion:   proto.String(c.clientVersion),
		NeedCheckSwitch: proto.Int64(1),
	}
	if data := baseData.BaseObj.GetOriginalParam("data"); data != nil {
		input.Data = &data
	}
	output := &usertask.DoUserTaskByActTypeRes{}
	err := tbservice.Call(ctx, "usertask", "doUserTaskByActType", input, output, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || output.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.FatalF(ctx, "fail to call service usertask:doUserTaskByActType, input=[%v], output=[%v], err=[%v]",
			common.ToString(input), common.ToString(output), err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	res := &commitTask.CommitTaskRes{
		SuccessTaskIds: output.GetData().GetSuccessTaskIds(),
	}

	response.Data = res
	return tiebaerror.ERR_SUCCESS
}
