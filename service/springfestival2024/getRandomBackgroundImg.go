package springfestival2024

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/image"
	"math/rand"
	"strconv"
	"time"

	"github.com/golang/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/springfestival2024/getRandomBackgroundImg"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
)

const (
	BackgroundImgTableName = "tb_wordlist_redis_springfestival2024_prayer_lamp_random_data" // 祝福语背景图词表名称
	BackgroundImgKeyTpl    = "springfestival2024_prayer_lamp_background_img_%d"             // 祝福语背景图词表key前缀
	BackgroundImgNumTql    = "springfestival2024_prayer_lamp_background_img_nums"           // 祝福语背景图词表中key的总个数
)

func GetRandomBackgroundImg(ctx context.Context, response *getRandomBackgroundImg.GetRandomBackgroundImgResIdl) int {

	// 先获取图片的总个数
	nums, err := wordserver.QueryKey(ctx, BackgroundImgTableName, BackgroundImgNumTql)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to query wordlist %v, key = %v, err = %v", BackgroundImgTableName, BackgroundImgNumTql, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	num, err := strconv.Atoi(nums)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to query wordlist want int get str %v, key = %v, err = %v", BackgroundImgTableName, BackgroundImgNumTql, err)

	}

	// 从词表中随机读一个key
	rand.Seed(time.Now().UnixNano())
	randomPage := rand.Int63n(int64(num))
	key := fmt.Sprintf(BackgroundImgKeyTpl, randomPage)
	pic, err := wordserver.QueryKey(ctx, BackgroundImgTableName, key)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to query wordlist %v, key = %v, err = %v", BackgroundImgTableName, key, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}
	picUrl, err := image.GenAuthUrl(pic)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to GenAuthUrl, url = %v, err = %v", picUrl, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	response.Data = &getRandomBackgroundImg.GetRandomBackgroundImgRes{
		ImgUrl: proto.String(picUrl),
	}
	return 0
}
