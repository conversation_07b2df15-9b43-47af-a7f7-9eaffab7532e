package springfestival2024

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"github.com/golang/protobuf/proto"
	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/springfestival2024/getRandomBlessings"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"
)

const (
	BlessingTableName   = "tb_wordlist_redis_springfestival2024_prayer_lamp_random_data" // 祝福语词表名称
	BlessingKeyTpl      = "springfestival2024_prayer_lamp_blessings_%d"                  // 祝福语词表key前缀
	BlesssingAllPageNum = 20                                                             // 祝福语词表中key的总个数
)

func GetRandomBlessings(ctx context.Context, response *getRandomBlessings.GetRandomBlessingsResIdl) int {
	// 从词表中随机读一个key
	rand.Seed(time.Now().UnixNano())
	randomPage := rand.Int63n(BlesssingAllPageNum)
	key := fmt.Sprintf(BlessingKeyTpl, randomPage)
	res, err := wordserver.QueryKey(ctx, BlessingTableName, key)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to query wordlist %v, key = %v, err = %v", BlessingKeyTpl, key, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	blessings := []string{}
	err = jsoniter.UnmarshalFromString(res, &blessings)
	if err != nil {
		tbcontext.WarningF(ctx, "fail to unmarshal blessings[%v], err = %v", res, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	if len(blessings) == 0 {
		tbcontext.WarningF(ctx, "blessings is empty! key=[%v]", key)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	randomNum := rand.Int63n(int64(len(blessings)))

	// 随机选一条祝福语返回
	response.Data = &getRandomBlessings.GetRandomBlessingsRes{
		BlessingContent: proto.String(blessings[randomNum]),
	}
	return 0
}
