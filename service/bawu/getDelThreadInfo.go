package bawu

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/usertool"
	protoCommon "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	permProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/frs"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/post/pb"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/user"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbportrait"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tbrichtext"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/threadtype"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/video"

	quizProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/quiz"
	scoreProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/score"

	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/ip"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/bawu/getDelThreadInfo"
	uiProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/bawu/getDelThreadInfo"
	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"
	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/util"
)

var mapIndexUrl = map[int]string{
	0: "https://tieba-ares.cdn.bcebos.com/mis/2024-6/1717415040580/64047ddc89c8.png",
	1: "https://tieba-ares.cdn.bcebos.com/mis/2024-6/1717415054078/b793d9ca6d6f.png",
	2: "https://tieba-ares.cdn.bcebos.com/mis/2024-6/1717415078643/c3af708b95d2.png",
}

func GetDelThreadInfo(ctx context.Context, baseData *types.GetDelThreadInfoBaseData, response *uiProto.GetDelThreadInfoResIdl) int {
	// 1 判断是否是本人或者吧务
	errno := checkIsBawuOrAuth(ctx, baseData)
	if errno != tiebaerror.ERR_SUCCESS {
		return errno
	}
	// 并行获取用户信息、特殊类型帖子信息
	eg := gtask.Group{
		// 不允许部分失败，核心数据方法会返回error，扩展数据方法不会返回error
		AllowSomeFail: false,
	}
	// 获取用户信息
	eg.Go(func() error {
		errno := getDelThreadAuthorInfo(ctx, baseData)
		if errno != tiebaerror.ERR_SUCCESS {
			return fmt.Errorf("getDelThreadAuthorInfo err: %d", errno)
		}
		return nil
	})

	// 获取帖子或者回复信息
	eg.Go(func() error {
		errno := getThreadDetail(ctx, baseData)
		if errno != tiebaerror.ERR_SUCCESS {
			return fmt.Errorf("getThreadDetail err: %d", errno)
		}
		return nil
	})

	// 核心数据失败阻塞执行
	_, err := eg.Wait()
	if err != nil {
		tbcontext.FatalF(ctx, "GetDelThreadInfo process fail, err=%v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 组装返回数据
	response.Data = &uiProto.GetDelThreadInfoData{
		PostInfo:    baseData.OutPostData,
		SubpostInfo: baseData.OutSubPostData,
		User:        baseData.OutUserData,
		Thread:      baseData.OutThreadData,
		ScoreInfo:   baseData.OutScoreThreadData,
		DrawInfo:    baseData.OutDrawThreadData,
	}
	return tiebaerror.ERR_SUCCESS
}

// 检查是否是本人或者吧务
func checkIsBawuOrAuth(ctx context.Context, baseData *types.GetDelThreadInfoBaseData) int {
	// 调用mgetThread或者getPostInfo获取帖子和回复的基础信息
	if baseData.Request.GetQueryType() == types.QueryTypeThread {
		threadID := baseData.Request.GetThreadId()
		postInput := map[string]any{
			"thread_ids":         []int64{threadID},
			"need_abstract":      1,
			"need_photo_pic":     1,
			"call_from":          "client_frs",
			"forum_id":           0, // 不传这个参数不返回pic_info
			"need_post_content":  1,
			"structured_content": 1,
		}
		threadRes := &frs.MgetThreadRes{}
		err := tbservice.Call(ctx, "post", "mgetThread", postInput, threadRes, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || threadRes.GetErrno() != tiebaerror.ERR_SUCCESS || len(threadRes.GetOutput().GetThreadList()) == 0 {
			tbcontext.WarningF(ctx, "checkIsBawuOrAuth call post:mgetThread err: %v,tid:%d, output:%s", err, threadID, common.ToString(threadRes))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		baseData.ThreadInfo = threadRes.GetOutput().GetThreadList()[uint64(threadID)]
		baseData.ForumID = int64(baseData.ThreadInfo.GetForumId())
		baseData.AuthorID = uint64(baseData.ThreadInfo.GetUserId())
	} else {
		postID := baseData.Request.GetPostId()
		postInput := map[string]any{
			"post_ids":           []int64{postID},
			"structured_content": 1,
		}
		postRes := &pb.GetPostInfoRes{}
		err := tbservice.Call(ctx, "post", "getPostInfo", postInput, postRes, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || postRes.GetErrno() != tiebaerror.ERR_SUCCESS || len(postRes.GetOutput()) == 0 {
			tbcontext.WarningF(ctx, "checkIsBawuOrAuth call post:getPostInfo err: %v,pid:%d, output:%s", err, postID, common.ToString(postRes))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		baseData.PostInfo = postRes.GetOutput()[0]
		baseData.ForumID = int64(baseData.PostInfo.GetForumId())
		baseData.AuthorID = uint64(baseData.PostInfo.GetUserId())
	}

	// 来源是回收站，校验是否作者本人访问
	if baseData.Request.GetCallFrom() == types.PmcCallFrom {
		if baseData.AuthorID != baseData.UserID {
			tbcontext.TraceF(ctx, "checkIsBawuOrAuth fail, login user:%d not equal author user:%d", baseData.UserID, baseData.AuthorID)
			return tiebaerror.ERR_USER_NO_PERM
		}
		return tiebaerror.ERR_SUCCESS
	}

	// 判断是否吧务
	permInput := map[string]any{
		"forum_id": baseData.ForumID,
		"user_id":  baseData.UserID,
	}
	ipInt := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("ip_int", 0), common.TTT_UINT32).(uint32)
	ipVersion := ip.GetIPVersion(ip.Long2IP(ipInt))
	if ipVersion == ip.IPV6 {
		permInput["user_ip6"] = ipInt
	} else {
		permInput["user_ip"] = ipInt
	}
	permOut := &permProto.GetPermRes{}
	err := tbservice.Call(ctx, "perm", "getPerm", permInput, permOut, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || permOut.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call perm getPerm fail, err:%v, permOut:%s", err, common.ToString(permOut))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	permStruct := perm.PermOut{
		Perm: permOut.GetOutput().GetPerm(),
	}

	isManager := permStruct.GetUserIsManager(ctx)
	if isManager == 0 {
		tbcontext.WarningF(ctx, "user is not manager, userid: %d, permOut:%s", baseData.UserID, common.ToString(permOut))
		return tiebaerror.ERR_USER_NO_PERM
	}

	return tiebaerror.ERR_SUCCESS
}

// 获取作者信息
func getDelThreadAuthorInfo(ctx context.Context, baseData *types.GetDelThreadInfoBaseData) int {
	multi := tbservice.Multi()
	userParam := &tbservice.Parameter{
		Service: "user",
		Method:  "getUserDataEx",
		Input: map[string]any{
			"user_id": baseData.AuthorID,
		},
		Output: &user.GetUserDataExRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getUserDataEx", userParam)

	commonParam := &tbservice.Parameter{
		Service: "common",
		Method:  "batchGetUidAddress",
		Input: map[string]any{
			"user_list": []uint64{baseData.AuthorID},
		},
		Output: &protoCommon.BatchGetUidAddressRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "batchGetUidAddress", commonParam)

	multi.Call(ctx)

	userResInter, err := multi.GetResult(ctx, "getUserDataEx")
	if err != nil {
		tbcontext.WarningF(ctx, "getDelThreadAuthorInfo get userDataEx fail, err:%v", err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	userRes, ok := userResInter.(*user.GetUserDataExRes)
	if !ok || userRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "getDelThreadAuthorInfo get userDataEx fail, output type error, output:%s", common.ToString(userResInter))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	commonResInter, err := multi.GetResult(ctx, "batchGetUidAddress")
	if err != nil {
		tbcontext.WarningF(ctx, "getDelThreadAuthorInfo batchGetUidAddress fail, err:%v", err)
	} else {
		commonRes, ok := commonResInter.(*protoCommon.BatchGetUidAddressRes)
		if !ok || commonRes.GetErrno() != tiebaerror.ERR_SUCCESS {
			tbcontext.WarningF(ctx, "getDelThreadAuthorInfo batchGetUidAddress fail, output type error, output:%s", common.ToString(commonResInter))
		} else if commonRes.GetData()[int64(baseData.AuthorID)] != nil {
			baseData.AuthorAddress = commonRes.GetData()[int64(baseData.AuthorID)].GetAddress()
		}
	}

	baseData.OutUserData = &client.User{
		NameShow:  proto.String(util.GetUserNameShow(userRes.GetUserInfo())),
		Portrait:  proto.String(tbportrait.Encode(userRes.GetUserInfo().GetUserId(), userRes.GetUserInfo().GetUserName(), 0)),
		IpAddress: proto.String(baseData.AuthorAddress),
	}
	return tiebaerror.ERR_SUCCESS
}

// 获取帖子或者回复的详细信息
func getThreadDetail(ctx context.Context, baseData *types.GetDelThreadInfoBaseData) int {
	// 回复信息直接用getPostInfo的返回值构造
	if baseData.Request.GetQueryType() != types.QueryTypeThread {
		pbContents := util.GetPostContentRichText(ctx, baseData.PostInfo.GetContent())
		addAtUserInfo(ctx, pbContents)
		if baseData.Request.GetQueryType() == types.QueryTypePost {
			baseData.OutPostData = &client.Post{
				Time:    baseData.PostInfo.NowTime,
				Content: pbContents,
			}
		} else {
			baseData.OutSubPostData = &client.SubPostList{
				Time:    baseData.PostInfo.NowTime,
				Content: pbContents,
			}
		}
		return tiebaerror.ERR_SUCCESS
	}

	// 处理帖子基础数据
	baseThread := baseData.ThreadInfo
	postContent, _ := baseThread.GetPostContentStruct()
	threadPbContent, _ := util.BuildRichAbstract(ctx, postContent)
	addAtUserInfo(ctx, threadPbContent)
	baseData.OutThreadData = &client.ThreadInfo{
		ThreadType:       proto.Int32(0), // 默认图文贴
		Title:            baseThread.Title,
		FirstPostContent: threadPbContent,
		Fid:              proto.Int64(int64(baseThread.GetForumId())),
		CreateTime:       proto.Int32(int32(baseThread.GetCreateTime())),
	}
	threadTypes := threadtype.GetThreadType(baseData.ThreadInfo.GetThreadTypes())
	// 视频贴
	if baseThread.GetVideoInfo() != nil && threadTypes["is_movideo"] {
		baseData.OutThreadData.ThreadType = proto.Int32(threadtype.VIDEO)
		videoInfo, err := baseThread.GetVideoInfoStruct(ctx)
		if err != nil {
			tbcontext.WarningF(ctx, "getThreadDetail getVideoInfo fail, err:%v, origin videoInfo:%s", err, common.ToString(baseThread.GetVideoInfo()))
		} else {
			videoExtParam := video.TbVideoExtParam{}
			video.CreateTbVideoProcessObj(ctx, videoExtParam).Execute(videoInfo, video.REQUEST_FROM_CLIENT)
			err := common.StructAToStructBCtx(ctx, videoInfo, &baseData.OutThreadData.VideoInfo)
			if err != nil {
				tbcontext.WarningF(ctx, "getThreadDetail trance vide_info structAToStructB fail, err:%v, origin videoInfo:%s", err, common.ToString(videoInfo))
			}
		}
	}

	// 根据帖子类型分别处理
	if threadTypes["is_draw"] {
		// 抽奖贴处理
		baseData.OutThreadData.ThreadType = proto.Int32(threadtype.DrawThread)
		infoStruct, _ := baseThread.GetDrawInfoStruct()
		quizReq := &quizProto.GetDrawInfoReq{
			DrawEntityId: proto.Uint64(infoStruct.GetDrawId()),
			CallFrom:     proto.String("pb"),
			Fid:          proto.Uint64(uint64(baseThread.GetForumId())),
			ThreadId:     proto.Uint64(baseThread.GetThreadId()),
			UserId:       proto.Uint64(baseData.UserID),
		}
		quizRes := &quizProto.GetDrawInfoRes{}
		err := tbservice.Call(ctx, "quiz", "getDrawInfo", quizReq, quizRes, tbservice.WithConverter(tbservice.JSONITER))
		if err != nil || quizRes.GetErrno() != tiebaerror.ERR_SUCCESS || quizRes.GetData() == nil {
			tbcontext.WarningF(ctx, "getThreadDetail getDrawInfo fail, err:%v, quizRes:%s", err, common.ToString(quizRes))
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		drawThreadInfo, err := structDrawInfo(ctx, quizRes.GetData(), baseData.ThreadInfo.GetThreadId())
		if err != nil {
			tbcontext.WarningF(ctx, "getThreadDetail structDrawInfo fail, err:%v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		baseData.OutDrawThreadData = drawThreadInfo
	} else if threadTypes["is_score"] {
		// 打分贴处理
		baseData.OutThreadData.ThreadType = proto.Int32(threadtype.SCORE_THREAD)
		scoreInfo, _ := baseThread.GetScoreInfoStruct()
		scoreListID := scoreInfo.GetScoreListId()
		outScoreInfo, err := structScoreInfo(ctx, baseData, scoreListID)
		if err != nil {
			tbcontext.WarningF(ctx, "getThreadDetail structScoreInfo fail, err:%v", err)
			return tiebaerror.ERR_CALL_SERVICE_FAIL
		}
		baseData.OutScoreThreadData = outScoreInfo
	} else if threadTypes["is_question"] {
		// 求助贴处理
		baseData.OutThreadData.ThreadType = proto.Int32(threadtype.THREAD_TYPE_QUESTION)
	}
	return tiebaerror.ERR_SUCCESS
}

// 构造打分贴信息
func structScoreInfo(ctx context.Context, baseData *types.GetDelThreadInfoBaseData, scoreListID uint64) (*getDelThreadInfo.ThreadScoreInfo, error) {
	if scoreListID <= 0 {
		tbcontext.WarningF(ctx, "scoreListID error, scoreListID = %d", scoreListID)
		return nil, nil
	}
	multi := tbservice.Multi()
	// 调用service
	getThreadScoreInfoParam := &tbservice.Parameter{
		Service: "score",
		Method:  "getThreadScoreInfo",
		Input: map[string]interface{}{
			"score_list_id":       scoreListID,
			"user_id":             baseData.UserID,
			"need_user_score":     1,
			"need_score_user_num": 1,
			"pn":                  1,
			"rn":                  30,
		},
		Output: &scoreProto.GetThreadScoreInfoRes{},
		Option: []tbservice.Option{
			tbservice.WithConverter(tbservice.JSONITER),
		},
	}
	multi.Register(ctx, "getThreadScoreInfoParam", getThreadScoreInfoParam)

	multi.Call(ctx)

	getThreadScoreInfoInter, err := multi.GetResult(ctx, "getThreadScoreInfoParam")
	if err != nil || getThreadScoreInfoInter == nil {
		tbcontext.WarningF(ctx, "structScoreInfo call score::getThreadScoreInfo fail, output: %v, err: %v", common.ToString(getThreadScoreInfoInter), err)
		return nil, errors.New("ThreadScore call service fail")
	}
	getThreadScoreInfoRes, ok := getThreadScoreInfoInter.(*scoreProto.GetThreadScoreInfoRes)
	if !ok || getThreadScoreInfoRes.Errno == nil || getThreadScoreInfoRes.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "structScoreInfo call score::getThreadScoreInfo, output: %v, err: %v", common.ToString(getThreadScoreInfoRes), err)
		return nil, errors.New("ThreadScore call service fail")
	}

	var items []*getDelThreadInfo.ThreadScoreItem
	getThreadScoreInfoData := getThreadScoreInfoRes.GetData()
	for idx, itemTmp := range getThreadScoreInfoData.GetItems() {
		item := &getDelThreadInfo.ThreadScoreItem{}
		err = common.StructAToStructBCtx(ctx, itemTmp, item)
		if err != nil {
			tbcontext.WarningF(ctx, "StructAToStructBCtx fail, err = %v, input = %s", err, common.ToString(itemTmp))
		}
		indexUrl := ""
		if _, ok := mapIndexUrl[idx]; ok {
			indexUrl = mapIndexUrl[idx]
		}
		item.IndexIcon = proto.String(indexUrl)
		items = append(items, item)
	}

	res := &getDelThreadInfo.ThreadScoreInfo{
		ScoreListId:      proto.Uint64(scoreListID),
		Items:            items,
		ShowIndex:        proto.Uint32(getThreadScoreInfoData.GetShowIndex()),
		FirstPageCount:   proto.Uint32(uint32(30)), // 固定写死
		TotalItemsCount:  proto.Uint64(getThreadScoreInfoData.GetTotalCount()),
		TotalUserCount:   proto.Uint64(getThreadScoreInfoData.GetTotalUserNum()),
		HasMore:          proto.Uint32(getThreadScoreInfoData.GetHasMore()),
		ShareIcon:        proto.String(getThreadScoreInfoData.GetShareIcon()),
		ShareMsg:         proto.String(getThreadScoreInfoData.GetShareMsg()),
		UserTotlaMsg:     proto.String(getThreadScoreInfoData.GetUserTotlaMsg()),
		UserFinishMsg:    proto.String(getThreadScoreInfoData.GetUserFinishMsg()),
		ValueMsg:         proto.String(getThreadScoreInfoData.GetValueMsg()),
		UserUnfinishMsg:  proto.String(getThreadScoreInfoData.GetUserUnfinishMsg()),
		ShareTitlePrefix: proto.String(getThreadScoreInfoData.GetShareTitlePrefix()),
		ShareSubtitle:    proto.String(getThreadScoreInfoData.GetShareSubtitle()),
		ShareUserSumMsg:  proto.String(getThreadScoreInfoData.GetShareUserSumMsg()),
		TabTitle:         proto.String(getThreadScoreInfoData.GetTabTitle()),
		ScoreItemIds:     getThreadScoreInfoData.GetScoreItemIds(),
	}
	return res, nil
}

// 构造抽奖贴的详细信息
func structDrawInfo(ctx context.Context, drawInfo *quizProto.DrawDataItem, threadID uint64) (*getDelThreadInfo.DrawThreadInfo, error) {
	keys := []string{
		"pb_marquee_intro",
		"pb_marquee_suffix",
		"pb_countdown_intro",
		"pb_countdown_btn",
		"pb_countdown_btn_url",
	}
	wordlistRes, err := wordserver.QueryItems(ctx, "tb_wordlist_redis_thread_draw_config", keys)
	if err != nil || wordlistRes == nil {
		tbcontext.WarningF(ctx, "structDrawInfo call wordlist fail, err:%v, wordlistRes:%s", err, common.ToString(wordlistRes))
		return nil, errors.New("query wordlist fail")
	}
	drawConfig := &types.ThreadDrawWordlistConfig{}
	// 跑马灯区配置
	tmp := strings.Split(wordlistRes["pb_marquee_intro"], "$$")
	if len(tmp) != 2 {
		return nil, errors.New("wordlist config invalid, key=pb_marquee_intro")
	}
	drawConfig.MarqueeIntroBefore = tmp[0]
	drawConfig.MarqueeIntroAfter = tmp[1]
	drawConfig.MarqueeSuffix = wordlistRes["pb_marquee_suffix"]
	// 倒计时区配置
	tmp = strings.Split(wordlistRes["pb_countdown_intro"], "$$")
	if len(tmp) != 5 {
		return nil, errors.New("wordlist config invalid, key=pb_countdown_intro")
	}
	drawConfig.CountdownIntroBefore1 = tmp[0]
	drawConfig.CountdownIntroBefore2 = tmp[1]
	drawConfig.CountdownIntroDuring = tmp[2]
	drawConfig.CountdownIntroAfter = tmp[3]
	drawConfig.CountdownIntroFinish = tmp[4]
	drawConfig.CountdownBtnText = wordlistRes["pb_countdown_btn"]
	drawConfig.CountdownBtnURL = wordlistRes["pb_countdown_btn_url"]

	drawThreadInfo := &getDelThreadInfo.DrawThreadInfo{}
	// 构造奖品列表
	prizeList := make([]*getDelThreadInfo.ThreadDrawPrizeInfo, 0, len(drawInfo.GetDrawPrize()))
	for _, info := range drawInfo.GetDrawPrize() {
		item := &getDelThreadInfo.ThreadDrawPrizeInfo{
			Name:  proto.String(info.GetName()),
			Pic:   proto.String(info.GetPic()),
			Count: proto.Uint64(info.GetCount()),
		}
		prizeList = append(prizeList, item)
	}

	// 构造倒计时信息
	countdownInfo := &getDelThreadInfo.ThreadDrawCountdownInfo{}
	openStatus := drawInfo.GetDrawEntityInfo().GetOpenStatus()

	// 前面展示的文案
	switch openStatus {
	case 0:
		// 开奖前
		if drawInfo.GetUserJoinInfo().GetUserJoinStatus() != 1 {
			// 未参与
			countdownInfo.Intro = proto.String(drawConfig.CountdownIntroBefore1)
		} else {
			// 已参与
			countdownInfo.Intro = proto.String(drawConfig.CountdownIntroBefore2)
		}
	case 1:
		countdownInfo.Intro = proto.String(drawConfig.CountdownIntroDuring)
	case 2:
		countdownInfo.Intro = proto.String(drawConfig.CountdownIntroAfter)
	case 3:
		countdownInfo.Intro = proto.String(drawConfig.CountdownIntroFinish)
	}

	// 开奖时间戳（开奖前下发）
	if uint64(time.Now().Unix()) < drawInfo.GetDrawEntityInfo().GetOpenTime() {
		countdownInfo.EndTime = proto.Uint64(drawInfo.GetDrawEntityInfo().GetOpenTime())
	}

	// 按钮信息（开奖后下发）
	if uint64(time.Now().Unix()) >= drawInfo.GetDrawEntityInfo().GetOpenTime() {
		countdownInfo.BtnInfo = &getDelThreadInfo.ThreadDrawCountdownBtnInfo{
			Text: proto.String(drawConfig.CountdownBtnText),
		}
		// 只有开奖后才展示跳转url
		if openStatus == 2 || openStatus == 3 {
			countdownInfo.BtnInfo.Url = proto.String(fmt.Sprintf(drawConfig.CountdownBtnURL, threadID))
		}
	}

	drawThreadInfo.PrizeList = prizeList
	drawThreadInfo.Countdown = countdownInfo
	drawThreadInfo.AuditStatus = proto.Uint64(drawInfo.GetDrawEntityInfo().GetAuditStatus())
	drawThreadInfo.OpenStatus = proto.Uint64(openStatus)
	return drawThreadInfo, nil
}

func addAtUserInfo(ctx context.Context, contents []*client.PbContent) {
	userIDMap := make(map[int64]bool)
	usernameMap := make(map[string]bool)
	for _, slot := range contents {
		if slot.GetType() == tbrichtext.SLOT_TYPE_AT {
			uid := slot.GetUid()
			if uid > 0 {
				// 如果有uid，用uid解析
				userIDMap[uid] = true
			}
			if slot.GetText() != "" {
				username := strings.Trim(slot.GetText(), "@")
				usernameMap[username] = true
			}
		}
	}

	if len(userIDMap) == 0 && len(usernameMap) == 0 {
		return
	}

	unameList := []string{}
	uidList := []int64{}
	for uname, _ := range usernameMap {
		unameList = append(unameList, uname)
	}
	for uid, _ := range userIDMap {
		uidList = append(uidList, uid)
	}

	multi := tbservice.Multi()

	if len(unameList) > 0 {
		getUidByNamesParam := &tbservice.Parameter{
			Service: "user",
			Method:  "getUidByNames",
			Input: map[string]interface{}{
				"names": unameList,
			},
			Output: &user.GetUidByNamesRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "getUidByNames", getUidByNamesParam)
	}

	if len(uidList) > 0 {
		mgetUserDataParam := &tbservice.Parameter{
			Service: "user",
			Method:  "mgetUserData",
			Input: map[string]interface{}{
				"user_id": uidList,
			},
			Output: &user.MgetUserDataRes{},
			Option: []tbservice.Option{
				tbservice.WithConverter(tbservice.JSONITER),
			},
		}
		multi.Register(ctx, "mgetUserData", mgetUserDataParam)
	}

	multi.Call(ctx)
	var getUidByNamesRes *user.GetUidByNamesRes
	if len(unameList) > 0 {
		getUidByNamesInter, err := multi.GetResult(ctx, "getUidByNames")
		getUidByNamesRes = getUidByNamesInter.(*user.GetUidByNamesRes)
		if err != nil {
			tbcontext.WarningF(ctx, "call user::getUidByNames fail: %v", err)
		}
	}

	var mgetUserDataRes *user.MgetUserDataRes
	if len(uidList) > 0 {
		mgetUserDataInter, err := multi.GetResult(ctx, "mgetUserData")
		mgetUserDataRes = mgetUserDataInter.(*user.MgetUserDataRes)
		if err != nil {
			tbcontext.WarningF(ctx, "call user::mgetUserData fail: %v", err)
		}

	}

	for _, slot := range contents {
		if slot.GetType() == tbrichtext.SLOT_TYPE_AT {
			strPrefix := "@"
			if len(slot.GetText()) > 0 && slot.GetText()[0] != '@' {
				strPrefix = ""
			}

			if slot.GetUid() != 0 {
				if userInfo, ok := mgetUserDataRes.GetUserInfo()[slot.GetUid()]; ok {
					showName := userInfo.GetUserName()
					if 0 < len(userInfo.GetUserNickname()) {
						showName = userInfo.GetUserNickname()
					}
					param := &usertool.NickNameByVersion{
						UserNickNameV2: userInfo.GetUserNicknameV2(),
						DisplayName:    userInfo.GetDisplayName(),
					}
					showName = usertool.GetUserNickNameByVersion(2, "12.76", param, showName)
					slot.Text = proto.String(strPrefix + showName)
				}
			} else {
				un := strings.TrimLeft(slot.GetText(), "@")
				if getUidByNamesRes != nil && len(getUidByNamesRes.Data[un]) != 0 {
					slot.Uid = proto.Int64(getUidByNamesRes.Data[un][0].GetUserId())
				}
			}
		}
	}
	return
}
