package bawu

import (
	"context"

	"google.golang.org/protobuf/proto"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/molib/utils/clientvers"
	bawuProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/bawu"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	permProto "icode.baidu.com/baidu/tieba-go-user-base/ub-golib/protos/service/perm"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/tiebaerror"
	uiProto "icode.baidu.com/baidu/tieba-go-user-base/ub-protobufidl/protos/client/forum/bawu/getDeleteReason"

	"icode.baidu.com/baidu/tieba-server-go/commonlib2/wordserver"
	"icode.baidu.com/baidu/tieba-server-go/golib2/tbcontext"

	"icode.baidu.com/baidu/tieba-server-user-base/go-client-forum/library/types"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/common"
	"icode.baidu.com/baidu/tieba-go-user-base/ub-golib/utillib/ip"
	tbservice "icode.baidu.com/baidu/tieba-server-go/golib2/service"
)

const (
	NEW_FORUM_RULE_CLIENT_VERSION = "*********" // 新版吧规的样式
	HAS_NOT_FORUM_RULE            = 0           // 没有吧规
	HAS_OLD_FORUM_RULE            = 1           // 旧版吧规
	HAS_NEW_FORUM_RULE            = 2           // 新版吧规
	FORUM_RULE_CONFIG_TABLE_NAME  = "tb_wordlist_redis_new_fourm_rule_conf"
)

func GetDeleteReason(ctx context.Context, baseData *types.GetDeleteReasonBaseData, response *uiProto.GetDeleteReasonResIdl) int {
	if baseData.Request.GetForumId() <= 0 {
		tbcontext.WarningF(ctx, "GetThreadScoreItemList params error")
		return tiebaerror.ERR_PARAM_ERROR
	}

	// 校验用户是否是吧务
	permInput := map[string]any{
		"forum_id": baseData.Request.GetForumId(),
		"user_id":  baseData.UserID,
	}
	ipInt := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("ip_int", 0), common.TTT_UINT32).(uint32)
	ipVersion := ip.GetIPVersion(ip.Long2IP(ipInt))
	if ipVersion == ip.IPV6 {
		permInput["user_ip6"] = ipInt
	} else {
		permInput["user_ip"] = ipInt
	}
	permOut := &permProto.GetPermRes{}
	err := tbservice.Call(ctx, "perm", "getPerm", permInput, permOut, tbservice.WithConverter(tbservice.JSONITER))
	if err != nil || permOut.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call perm getPerm fail, err:%v, permOut:%s", err, common.ToString(permOut))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	permStruct := perm.PermOut{
		Perm: permOut.GetOutput().GetPerm(),
	}

	isManager := permStruct.GetUserIsManager(ctx)
	if isManager == 0 {
		tbcontext.WarningF(ctx, "user is not manager, userid: %d, permOut:%s", baseData.UserID, common.ToString(permOut))
		return tiebaerror.ERR_USER_NO_PERM
	}

	// 查询当前吧规
	clientVersion := common.Tvttt(baseData.BaseObj.ObjRequest.GetCommonAttr("client_version", ""), common.TTT_STRING).(string)
	bawuInput := map[string]any{
		"forum_id":           baseData.Request.GetForumId(),
		"need_rule_detail":   1,
		"structured_content": 1,
		"client_version":     clientVersion,
		"is_edit":            1, // 传了这个参数才会固定返回默认吧规
	}
	bawuOut := &bawuProto.GetForumRuleDetailsStructuredRes{}
	err = tbservice.Call(ctx, "bawu", "getForumRuleDetails", bawuInput, bawuOut, tbservice.WithConverter(tbservice.JSONITER))

	if err != nil || bawuOut.GetErrno() != tiebaerror.ERR_SUCCESS {
		tbcontext.WarningF(ctx, "call bawu getForumRuleDetails fail, err:%v, bawuOut:%s", err, common.ToString(bawuOut))
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 查询配置信息
	keys := []string{
		"delete_reason_ui_window_conf",
	}
	values, err := wordserver.QueryKeys(ctx, FORUM_RULE_CONFIG_TABLE_NAME, keys)
	if err != nil || len(values) != len(keys) {
		tbcontext.WarningF(ctx, "call wordserver fail, table=[%s],key=[%v], err=[%v]", FORUM_RULE_CONFIG_TABLE_NAME, keys, err)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	configRes := make(map[string]string, 0)
	err = jsoniter.UnmarshalFromString(values[0], &configRes)
	if err != nil {
		tbcontext.WarningF(ctx, "unmarshal configRes fail, err:[%v], values:[%v]", err, values)
		return tiebaerror.ERR_CALL_SERVICE_FAIL
	}

	// 构建返回值
	firstIndex := bawuOut.GetData().GetForumRuleConf().GetFirstLevelIndexList()
	secondIndex := bawuOut.GetData().GetForumRuleConf().GetSecondLevelIndexList()
	response.Data = &uiProto.GetDeleteReasonData{}
	reasonList := make([]*uiProto.ReasonInfo, 0)
	forumRuleVersionInfo := &uiProto.RuleVersionInfo{
		ForumRuleStyle: proto.Int32(HAS_NOT_FORUM_RULE),
	}
	// 先使用默认吧规构建删帖理由
	for _, rule := range bawuOut.GetData().GetDefaultRules() {
		forumRuleVersionInfo.DefaultRuleVersion = proto.Int64(int64(bawuOut.GetData().GetDefaultRuleVersion()))

		firstTitle := rule.GetTitle()
		// 一级吧规拼上序号
		if len(reasonList) < len(firstIndex) {
			firstTitle = firstIndex[len(reasonList)] + firstTitle
		}
		reason := &uiProto.ReasonInfo{
			FirstTitle:    proto.String(firstTitle),
			IsDefaultRule: proto.Int32(1),
		}
		reasonList = append(reasonList, reason)
	}

	// 使用自定义吧规构建删帖理由，如果有新版吧规就用吧规构建，如果是旧版吧规就固定为"违反自设吧规"
	if bawuOut.GetData().GetHasForumRule() == 1 && clientvers.CompareV2(bawuOut.GetData().GetClientVersion(), ">=", NEW_FORUM_RULE_CLIENT_VERSION) {
		forumRuleVersionInfo.ForumRuleStyle = proto.Int32(HAS_NEW_FORUM_RULE)
		forumRuleVersionInfo.CustomizeRuleVersion = proto.Int64(int64(bawuOut.GetData().GetForumRuleVersion()))
		for _, rule := range bawuOut.GetData().GetNewRules() {
			firstTitle := rule.GetTitle()
			// 一级吧规拼上序号
			if len(reasonList) < len(firstIndex) {
				firstTitle = firstIndex[len(reasonList)] + firstTitle
			}
			secondTitleList := make([]string, 0)
			for index, secondRule := range rule.GetContentList() {
				secondTitle := ""
				// 将二级吧规的每行拼接起来
				for _, content := range secondRule.GetContent() {
					if content.GetTag() == "plainText" {
						contentValue := common.Tvttt(content.GetValue(), common.TTT_STRING).(string)
						secondTitle += contentValue
					}
				}
				// 二级吧规拼上序号
				if index < len(secondIndex) {
					secondTitle = secondIndex[index] + secondTitle
				}
				secondTitleList = append(secondTitleList, secondTitle)
			}
			reason := &uiProto.ReasonInfo{
				FirstTitle:      proto.String(firstTitle),
				IsDefaultRule:   proto.Int32(0),
				SecondTitleList: secondTitleList,
			}
			reasonList = append(reasonList, reason)
		}
	} else if bawuOut.GetData().GetHasForumRule() == 1 {
		forumRuleVersionInfo.ForumRuleStyle = proto.Int32(HAS_OLD_FORUM_RULE)
		strReason := "违反自设吧规"
		if len(reasonList) < len(firstIndex) {
			strReason = firstIndex[len(reasonList)] + strReason
		}
		reason := &uiProto.ReasonInfo{
			FirstTitle:    proto.String(strReason),
			IsDefaultRule: proto.Int32(0),
		}
		reasonList = append(reasonList, reason)
	}

	response.Data = &uiProto.GetDeleteReasonData{
		DeleteReasonList: reasonList,
		ForumRuleVersion: forumRuleVersionInfo,
		WindowTitle:      proto.String(configRes["window_title"]),
		TipsText:         proto.String(configRes["tips_text"]),
		ButtonText:       []string{configRes["left_button_text"], configRes["right_button_text"]},
		TipsIcon:         proto.String(configRes["tips_icon"]),
	}
	return tiebaerror.ERR_SUCCESS
}
